package com.adins.mss.foundation.sync.api.model;

import com.adins.mss.foundation.http.MssRequestType;
import com.google.gson.annotations.SerializedName;

public class JsonRequestSyncTableConstraint extends MssRequestType {

    @SerializedName("productOfferingCode")
    private String productOfferingCode;

    @SerializedName("branchCode")
    private String branchCode;

    public void setProductOfferingCode(String productOfferingCode) {
        this.productOfferingCode = productOfferingCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }
}
