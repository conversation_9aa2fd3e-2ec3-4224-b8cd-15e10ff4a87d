package com.adins.mss.dao;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "MS_SYNC".
 */
public class Sync {

    /** Not-null value. */
     @SerializedName("uuid_sync")
    private String uuid_sync;
     @SerializedName("tabel_name")
    private String tabel_name;
     @SerializedName("lov_group")
    private String lov_group;
     @SerializedName("path")
    private String path;
     @SerializedName("dtm_upd")
    private java.util.Date dtm_upd;
     @SerializedName("flag")
    private Integer flag;

    public Sync() {
    }

    public Sync(String uuid_sync) {
        this.uuid_sync = uuid_sync;
    }

    public Sync(String uuid_sync, String tabel_name, String lov_group, String path, java.util.Date dtm_upd, Integer flag) {
        this.uuid_sync = uuid_sync;
        this.tabel_name = tabel_name;
        this.lov_group = lov_group;
        this.path = path;
        this.dtm_upd = dtm_upd;
        this.flag = flag;
    }

    /** Not-null value. */
    public String getUuid_sync() {
        return uuid_sync;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_sync(String uuid_sync) {
        this.uuid_sync = uuid_sync;
    }

    public String getTabel_name() {
        return tabel_name;
    }

    public void setTabel_name(String tabel_name) {
        this.tabel_name = tabel_name;
    }

    public String getLov_group() {
        return lov_group;
    }

    public void setLov_group(String lov_group) {
        this.lov_group = lov_group;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public java.util.Date getDtm_upd() {
        return dtm_upd;
    }

    public void setDtm_upd(java.util.Date dtm_upd) {
        this.dtm_upd = dtm_upd;
    }

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

}
