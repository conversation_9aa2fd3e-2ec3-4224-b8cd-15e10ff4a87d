package com.adins.mss.base.dynamicform.form.models;

import com.adins.mss.foundation.http.MssResponseType;
import com.google.gson.annotations.SerializedName;

import java.util.HashMap;

public class LayerResponse extends MssResponseType {
    @SerializedName("map_result") private HashMap<String, Object> objectHashMap;
    @SerializedName("update_readonly") private HashMap<String, Object> objectHashMapReadOnly;

    public HashMap<String, Object> getObjectHashMap() {
        return objectHashMap;
    }

    public void setObjectHashMap(HashMap<String, Object> objectHashMap) {
        this.objectHashMap = objectHashMap;
    }

    public HashMap<String, Object> getObjectHashMapReadOnly() {
        return objectHashMapReadOnly;
    }

    public void setObjectHashMapReadOnly(HashMap<String, Object> objectHashMapReadOnly) {
        this.objectHashMapReadOnly = objectHashMapReadOnly;
    }
}
