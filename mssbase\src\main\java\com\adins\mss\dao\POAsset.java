package com.adins.mss.dao;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "MS_PO_ASSET".
 */
public class POAsset {

     @SerializedName("id")
    private long id;
     @SerializedName("isDeleted")
    private Integer is_deleted;
     @SerializedName("assetSchemeId")
    private int asset_scheme_id;
    /** Not-null value. */
     @SerializedName("brandCode")
    private String brand_code;
    /** Not-null value. */
     @SerializedName("brandName")
    private String brand_name;
    /** Not-null value. */
     @SerializedName("modelCode")
    private String model_code;
    /** Not-null value. */
     @SerializedName("modelName")
    private String model_name;
    /** Not-null value. */
     @SerializedName("groupType")
    private String group_type;
    /** Not-null value. */
     @SerializedName("masterCode")
    private String master_code;
    /** Not-null value. */
     @SerializedName("masterName")
    private String master_name;
     @SerializedName("dtmUpd")
    private java.util.Date dtm_upd;

    public POAsset() {
    }

    public POAsset(long id) {
        this.id = id;
    }

    public POAsset(long id, Integer is_deleted, int asset_scheme_id, String brand_code, String brand_name, String model_code, String model_name, String group_type, String master_code, String master_name, java.util.Date dtm_upd) {
        this.id = id;
        this.is_deleted = is_deleted;
        this.asset_scheme_id = asset_scheme_id;
        this.brand_code = brand_code;
        this.brand_name = brand_name;
        this.model_code = model_code;
        this.model_name = model_name;
        this.group_type = group_type;
        this.master_code = master_code;
        this.master_name = master_name;
        this.dtm_upd = dtm_upd;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public Integer getIs_deleted() {
        return is_deleted;
    }

    public void setIs_deleted(Integer is_deleted) {
        this.is_deleted = is_deleted;
    }

    public int getAsset_scheme_id() {
        return asset_scheme_id;
    }

    public void setAsset_scheme_id(int asset_scheme_id) {
        this.asset_scheme_id = asset_scheme_id;
    }

    /** Not-null value. */
    public String getBrand_code() {
        return brand_code;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setBrand_code(String brand_code) {
        this.brand_code = brand_code;
    }

    /** Not-null value. */
    public String getBrand_name() {
        return brand_name;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setBrand_name(String brand_name) {
        this.brand_name = brand_name;
    }

    /** Not-null value. */
    public String getModel_code() {
        return model_code;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setModel_code(String model_code) {
        this.model_code = model_code;
    }

    /** Not-null value. */
    public String getModel_name() {
        return model_name;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setModel_name(String model_name) {
        this.model_name = model_name;
    }

    /** Not-null value. */
    public String getGroup_type() {
        return group_type;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setGroup_type(String group_type) {
        this.group_type = group_type;
    }

    /** Not-null value. */
    public String getMaster_code() {
        return master_code;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setMaster_code(String master_code) {
        this.master_code = master_code;
    }

    /** Not-null value. */
    public String getMaster_name() {
        return master_name;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setMaster_name(String master_name) {
        this.master_name = master_name;
    }

    public java.util.Date getDtm_upd() {
        return dtm_upd;
    }

    public void setDtm_upd(java.util.Date dtm_upd) {
        this.dtm_upd = dtm_upd;
    }

}
