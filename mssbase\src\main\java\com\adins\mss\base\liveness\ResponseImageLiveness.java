package com.adins.mss.base.liveness;

import com.adins.mss.foundation.http.MssResponseType;
import com.google.gson.annotations.SerializedName;

public class ResponseImageLiveness extends MssResponseType {
    @SerializedName("liveness")
    private ImageLivenessBean liveness;

    public ImageLivenessBean getLiveness() {
        return liveness;
    }

    public void setLiveness(ImageLivenessBean liveness) {
        this.liveness = liveness;
    }
}
