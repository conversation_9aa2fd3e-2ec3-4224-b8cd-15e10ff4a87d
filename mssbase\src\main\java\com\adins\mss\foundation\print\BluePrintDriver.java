package com.adins.mss.foundation.print;


import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothSocket;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;

import java.io.DataInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.nio.ByteBuffer;
import java.util.Arrays;

public class BluePrintDriver {
    public static final int Code128_B = 732;
    private static final int DEFAULT_CMD_BUFFER_LEN = 1048576;
    public static String ErrorMessage = "No_Error_Message";
    public static boolean TextPosWinStyle = false;
    private static OutputStream myOutStream = null;
    private static InputStream myInStream = null;
    private static BluetoothSocket mySocket = null;
    private static BluetoothAdapter myBluetoothAdapter;
    private static BluetoothDevice myDevice;
    private static int mIndex = 0;
    private static byte[] mCmdBuffer = new byte[1048576];

    public BluePrintDriver() {
    }

    public static boolean open(BluetoothAdapter myBluetoothAdapter, BluetoothDevice btDevice) {
        return SPPOpen(myBluetoothAdapter, btDevice);
    }

    public static void close() {
        SPPClose();
    }

    public static boolean OpenPrinter(String BDAddr) {
        if (BDAddr == "") {
            ErrorMessage = "There is no available printer";
            return false;
        } else {
            myBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
            if (myBluetoothAdapter == null) {
                ErrorMessage = "Bluetooth system error";
                return false;
            } else {
                myDevice = myBluetoothAdapter.getRemoteDevice(BDAddr);
                if (myDevice == null) {
                    ErrorMessage = "Read Bluetooth device error";
                    return false;
                } else {
                    return SPPOpen(myBluetoothAdapter, myDevice);
                }
            }
        }
    }

    private static boolean SPPOpen(BluetoothAdapter BluetoothAdapter, BluetoothDevice btDevice) {
        boolean error = false;
        myBluetoothAdapter = BluetoothAdapter;
        myDevice = btDevice;
        if (!myBluetoothAdapter.isEnabled()) {
            ErrorMessage = "Bluetooth adapter is off";
            return false;
        } else {
            try {
                Method e3 = myDevice.getClass().getMethod("createRfcommSocket", Integer.TYPE);
                mySocket = (BluetoothSocket) e3.invoke(myDevice, Integer.valueOf(1));
            } catch (SecurityException var7) {
                mySocket = null;
                ErrorMessage = "Bluetooth port error";
                return false;
            } catch (NoSuchMethodException var8) {
                mySocket = null;
                ErrorMessage = "Bluetooth port error";
                return false;
            } catch (IllegalArgumentException var9) {
                mySocket = null;
                ErrorMessage = "Bluetooth port error";
                return false;
            } catch (IllegalAccessException var10) {
                mySocket = null;
                ErrorMessage = "Bluetooth port error";
                return false;
            } catch (InvocationTargetException var11) {
                mySocket = null;
                ErrorMessage = "Bluetooth port error";
                return false;
            }

            try {
                mySocket.connect();
            } catch (IOException var6) {
                ErrorMessage = var6.getLocalizedMessage();
                mySocket = null;
                return false;
            }

            try {
                myOutStream = mySocket.getOutputStream();
            } catch (IOException var5) {
                myOutStream = null;
                error = true;
            }

            try {
                myInStream = mySocket.getInputStream();
            } catch (IOException var4) {
                myInStream = null;
                error = true;
            }

            if (error) {
                SPPClose();
                return false;
            } else {
                return true;
            }
        }
    }

    private static boolean SPPClose() {
        try {
            Thread.sleep(1000L);
        } catch (InterruptedException var6) {
            var6.printStackTrace();
            // Restore interrupted state...
            Thread.currentThread().interrupt();
        }

        if (myOutStream != null) {
            try {
                myOutStream.flush();
            } catch (IOException var5) {
                var5.printStackTrace();
            }

            try {
                myOutStream.close();
            } catch (IOException var4) {
                var4.printStackTrace();
            }

            myOutStream = null;
        }

        if (myInStream != null) {
            try {
                myInStream.close();
            } catch (IOException var3) {
                var3.printStackTrace();
            }

            myInStream = null;
        }

        if (mySocket != null) {
            try {
                mySocket.close();
            } catch (IOException var2) {
                var2.printStackTrace();
            }

            mySocket = null;
        }

        try {
            Thread.sleep(200L);
        } catch (InterruptedException var1) {
            var1.printStackTrace();
            // Restore interrupted state...
            Thread.currentThread().interrupt();
        }

        return true;
    }

    public static boolean IsNoConnection() {
        return myOutStream == null;
    }

    public static boolean isPrinterConnected() {
        try {
            byte[] e = new byte[]{(byte) 27, (byte) 64};
            if (myOutStream == null) {
                return false;
            }

            myOutStream.write(e);
            Thread.sleep(1000);
            myOutStream.write(e);
        } catch (Exception var1) {
            var1.printStackTrace();
            // Restore interrupted state...
            Thread.currentThread().interrupt();
            return false;
        }

        return true;
    }

    public static boolean InitPrinter() {
        try {
            byte[] e = new byte[]{(byte) 27, (byte) 64};
            if (myOutStream == null) {
                return false;
            }

            myOutStream.write(e);
        } catch (IOException var1) {
            var1.printStackTrace();
            return false;
        }

        return true;
    }

    public static void ImportData(byte[] data, int dataLen) {
        int DataLength = dataLen;

        for (int i = 0; i < DataLength; ++i) {
            mCmdBuffer[mIndex++] = data[i];
        }

    }

    public static void ImportData(String dataString) {
        byte[] data = null;

        try {
            data = dataString.getBytes("GBK");
        } catch (UnsupportedEncodingException var4) {
            var4.printStackTrace();
        }

        int DataLength = data.length;

        for (int i = 0; i < DataLength; ++i) {
            mCmdBuffer[mIndex++] = data[i];
        }

    }

    public static void ImportData(String dataString, boolean bGBK) {
        byte[] data = null;
        if (bGBK) {
            try {
                data = dataString.getBytes("GBK");
            } catch (UnsupportedEncodingException var5) {
                var5.printStackTrace();
            }
        } else {
            data = dataString.getBytes();
        }

        int DataLength = data.length;

        for (int i = 0; i < DataLength; ++i) {
            mCmdBuffer[mIndex++] = data[i];
        }

    }

    public static void ImportData(String dataString, byte[] img) {
        byte[] data = null;
        int Totallen = 0;
        byte[] Buff = new byte[2048];
        int len = 0;
        int panjang = 3599;
        Totallen = dataString.getBytes().length + panjang + 1;
//        Totallen = panjang + 1;
        ByteBuffer sendBuffer = ByteBuffer.allocate(Totallen);
        byte[] SendBuffer = sendBuffer.array();
        try {
            data = dataString.getBytes("GBK");
            byte[] SendData = data;

            for (int i = 0; i < SendBuffer.length; i++)
                SendBuffer[i] = 0;
            for (int i = 0; i < len; i++)
                SendBuffer[i] = Buff[i];

            for (int i = 0; i < panjang; i++)
                SendBuffer[(i)] = img[i];
            for (int i = 0; i < SendData.length; i++)
                SendBuffer[(i + panjang)] = SendData[i];
            SendBuffer[(SendBuffer.length - 1)] = 0;

        } catch (UnsupportedEncodingException var4) {
            var4.printStackTrace();
        }

        int DataLength = SendBuffer.length;

        for (int i = 0; i < DataLength; ++i) {
            mCmdBuffer[mIndex++] = SendBuffer[i];
        }

    }

    public static void ClearData() {
        mIndex = 0;
    }

    public static void WakeUpPritner() {
        byte[] b = new byte[3];

        try {
            myOutStream.write(b);
            Thread.sleep(100L);
        } catch (Exception var2) {
            var2.printStackTrace();
            // Restore interrupted state...
            Thread.currentThread().interrupt();
        }

    }

    public static void Begin() {
        WakeUpPritner();
        InitPrinter();
        ClearData();
    }

    public static boolean excute() {
        if (mIndex > 0) {
            try {
                myOutStream.write(mCmdBuffer, 0, mIndex);
                myOutStream.flush();
                mIndex = 0;
                return true;
            } catch (IOException var1) {
                var1.printStackTrace();
                return false;
            }
        } else {
            //return false;
            return true;
        }
    }

    public static void LF() {
        mCmdBuffer[mIndex++] = 10;
    }

    public static void SetZoom(byte param) {
        mCmdBuffer[mIndex++] = 29;
        mCmdBuffer[mIndex++] = 33;
        mCmdBuffer[mIndex++] = param;
    }

    public static void SetCharacterFont(byte param) {
        mCmdBuffer[mIndex++] = 27;
        mCmdBuffer[mIndex++] = 77;
        mCmdBuffer[mIndex++] = param;
    }

    public static void SetUnderline(byte param) {
        mCmdBuffer[mIndex++] = 27;
        mCmdBuffer[mIndex++] = 45;
        mCmdBuffer[mIndex++] = param;
    }

    public static void AddBold(byte param) {
        mCmdBuffer[mIndex++] = 27;
        mCmdBuffer[mIndex++] = 69;
        mCmdBuffer[mIndex++] = param;
    }

    public static void AddInverse(byte param) {
        mCmdBuffer[mIndex++] = 29;
        mCmdBuffer[mIndex++] = 66;
        mCmdBuffer[mIndex++] = param;
    }

    public static void SetLineSpace(byte param) {
        mCmdBuffer[mIndex++] = 27;
        mCmdBuffer[mIndex++] = 51;
        mCmdBuffer[mIndex++] = 3;
    }

    public static void AddAlignMode(byte param) {
        mCmdBuffer[mIndex++] = 27;
        mCmdBuffer[mIndex++] = 97;
        mCmdBuffer[mIndex++] = param;
    }

    public static void AddCodePrint(int CodeType, String data) {
        switch (CodeType) {
            case 732:
                Code128_B(data);
            default:
        }
    }

    public static void Code128_B(String data) {
        byte m = 73;
        int num = data.length();
        int transNum = 0;
        mCmdBuffer[mIndex++] = 29;
        mCmdBuffer[mIndex++] = 107;
        mCmdBuffer[mIndex++] = m;
        int Code128C = mIndex++;
        mCmdBuffer[mIndex++] = 123;
        mCmdBuffer[mIndex++] = 66;

        int checkcodeID;
        for (checkcodeID = 0; checkcodeID < num; ++checkcodeID) {
            if (data.charAt(checkcodeID) > 127 || data.charAt(checkcodeID) < 32) {
                return;
            }
        }

        if (num <= 30) {
            for (checkcodeID = 0; checkcodeID < num; ++checkcodeID) {
                mCmdBuffer[mIndex++] = (byte) data.charAt(checkcodeID);
                if (data.charAt(checkcodeID) == 123) {
                    mCmdBuffer[mIndex++] = (byte) data.charAt(checkcodeID);
                    ++transNum;
                }
            }

            checkcodeID = 104;
            int n = 1;

            for (int i = 0; i < num; ++i) {
                checkcodeID += n++ * (data.charAt(i) - 32);
            }

            checkcodeID %= 103;
            if (checkcodeID >= 0 && checkcodeID <= 95) {
                mCmdBuffer[mIndex++] = (byte) (checkcodeID + 32);
                mCmdBuffer[Code128C] = (byte) (num + 3 + transNum);
            } else if (checkcodeID == 96) {
                mCmdBuffer[mIndex++] = 123;
                mCmdBuffer[mIndex++] = 51;
                mCmdBuffer[Code128C] = (byte) (num + 4 + transNum);
            } else if (checkcodeID == 97) {
                mCmdBuffer[mIndex++] = 123;
                mCmdBuffer[mIndex++] = 50;
                mCmdBuffer[Code128C] = (byte) (num + 4 + transNum);
            } else if (checkcodeID == 98) {
                mCmdBuffer[mIndex++] = 123;
                mCmdBuffer[mIndex++] = 83;
                mCmdBuffer[Code128C] = (byte) (num + 4 + transNum);
            } else if (checkcodeID == 99) {
                mCmdBuffer[mIndex++] = 123;
                mCmdBuffer[mIndex++] = 67;
                mCmdBuffer[Code128C] = (byte) (num + 4 + transNum);
            } else if (checkcodeID == 100) {
                mCmdBuffer[mIndex++] = 123;
                mCmdBuffer[mIndex++] = 52;
                mCmdBuffer[Code128C] = (byte) (num + 4 + transNum);
            } else if (checkcodeID == 101) {
                mCmdBuffer[mIndex++] = 123;
                mCmdBuffer[mIndex++] = 65;
                mCmdBuffer[Code128C] = (byte) (num + 4 + transNum);
            } else if (checkcodeID == 102) {
                mCmdBuffer[mIndex++] = 123;
                mCmdBuffer[mIndex++] = 49;
                mCmdBuffer[Code128C] = (byte) (num + 4 + transNum);
            }

        }
    }

    public static boolean SPPWrite(byte[] Data, int DataLen) {
        try {
            myOutStream.write(Data, 0, DataLen);
            return true;
        } catch (IOException var3) {
            ErrorMessage = "Failed to send Bluetooth data";
            return false;
        }
    }

    public static boolean SPPWrite(byte[] buffer) {
        try {
            myOutStream.write(buffer);
            return true;
        } catch (IOException var2) {
            ErrorMessage = "Failed to send Bluetooth data";
            return false;
        }
    }

    public static boolean SPPReadTimeout(byte[] Data, int DataLen, int Timeout) {
        for (int i = 0; i < Timeout / 50; ++i) {
            try {
                if (myInStream.available() >= DataLen) {
                    try {
                        myInStream.read(Data, 0, DataLen);
                        return true;
                    } catch (IOException var5) {
                        ErrorMessage = "Failed to read Bluetooth data";
                        return false;
                    }
                }
            } catch (IOException var7) {
                ErrorMessage = "Failed to read Bluetooth data";
                return false;
            }

            try {
                Thread.sleep(50L);
            } catch (InterruptedException var6) {
                ErrorMessage = "Failed to read Bluetooth data";
                // Restore interrupted state...
                Thread.currentThread().interrupt();
                return false;
            }
        }

        ErrorMessage = "The Bluetooth read data timeout";
        return false;
    }

    private static void SPPFlush() {
        int DataLen = 0;

        try {
            DataLen = myInStream.available();
        } catch (IOException var4) {
            var4.printStackTrace();
        }

        for (int var5 = 0; var5 < DataLen; ++var5) {
            try {
                myInStream.read();
            } catch (IOException var3) {
                var3.printStackTrace();
            }
        }

    }

    public static boolean zp_open(BluetoothAdapter myBluetoothAdapter, BluetoothDevice btDevice) {
        return SPPOpen(myBluetoothAdapter, btDevice);
    }

    public static void printString(String str) {
        try {
            SPPWrite(str.getBytes("GBK"));
            SPPWrite(new byte[]{(byte) 10});
        } catch (UnsupportedEncodingException var2) {
            var2.printStackTrace();
        }

    }

    public static void printParameterSet(byte[] buf) {
        SPPWrite(buf);
    }

    public static void printByteData(byte[] buf) {
        SPPWrite(buf);
        SPPWrite(new byte[]{(byte) 10});
    }

    public static void zeroBytes(byte[] zerobuff) {
        Arrays.fill(zerobuff, (byte) 0);
    }

    public static boolean woosimBitmap(String bmpPath) throws IOException {
        int BUFF_SIZE = 327680;
        byte[] dataBuff = new byte[BUFF_SIZE];
        byte[] workBuff = new byte[BUFF_SIZE];
        int buffPos = 0;

        byte[] lf = {0x0a};

        System.arraycopy(lf, 0, dataBuff, buffPos, lf.length);
        buffPos += lf.length;

        byte[] ESC_L = new byte[]{(byte) 27, (byte) 76};
        System.arraycopy(ESC_L, 0, dataBuff, buffPos, ESC_L.length);
        buffPos += ESC_L.length;

        byte[] ff = new byte[]{(byte) 27, (byte) 12};
        byte[] CAN = new byte[]{(byte) 24};
        File file = new File(bmpPath);

        if (file.exists()) {
            Bitmap originalImg = BitmapFactory.decodeFile(bmpPath);

            int width = originalImg.getWidth();
            int height = originalImg.getHeight();
            int byteperline = (width / 32 + (width % 32 == 0 ? 0 : 1)) * 4;
            DataInputStream dis = null;
            dis = new DataInputStream(new FileInputStream(file));
            int imgsize = byteperline * height;
            byte[] bmpbuff = new byte[imgsize];
            byte[] bmpdata = new byte[imgsize];
            zeroBytes(bmpbuff);
            zeroBytes(bmpdata);
            dis.skipBytes(58);
            boolean bmpBlack = false;
            byte[] bwData = new byte[3];

            int var22;
            for (var22 = 0; var22 < 3; ++var22) {
                bwData[var22] = (byte) dis.read();
                bmpBlack = bwData[0] == -1 && bwData[1] == -1 && bwData[2] == -1;
            }

            dis.skipBytes(1);

            for (var22 = 0; var22 < imgsize; ++var22) {
                if (bmpBlack) {
                    bmpbuff[var22] = (byte) (dis.read() ^ 255);
                } else {
                    bmpbuff[var22] = (byte) dis.read();
                }
            }

            int esc_w;
            int img_size_once;
            int repeat;
            if (width % 32 == 0) {
                esc_w = 0;

                for (img_size_once = 0; esc_w < height; ++esc_w) {
                    for (repeat = 0; repeat < byteperline; ++repeat) {
                        bmpdata[img_size_once++] = bmpbuff[imgsize - byteperline * (esc_w + 1) + repeat];
                    }
                }
            } else {
                esc_w = 0;

                for (img_size_once = 0; esc_w < height; ++esc_w) {
                    for (repeat = 0; repeat < byteperline; ++repeat) {
                        if (repeat < width / 8) {
                            bmpdata[img_size_once] = bmpbuff[imgsize - byteperline * (esc_w + 1) + repeat];
                            ++img_size_once;
                        } else if (repeat == width / 8) {
                            bmpdata[img_size_once] = (byte) ((bmpbuff[imgsize - byteperline * (esc_w + 1) + repeat] & 0xff) & 255 << 8 - width % 8);
                            ++img_size_once;
                        } else {
                            bmpdata[img_size_once] = (byte) (bmpbuff[imgsize - byteperline * (esc_w + 1) + repeat] << 8);
                            ++img_size_once;
                        }
                    }
                }
            }

            byte[] var23;
            if (height < 256) {
                var23 = new byte[]{(byte) 27, (byte) 87, (byte) 0, (byte) 0, (byte) 0, (byte) 0, (byte) 100, (byte) 3, (byte) height, (byte) 0};
                System.arraycopy(var23, 0, dataBuff, buffPos, var23.length);
                buffPos += var23.length;

                byte[] var24 = new byte[]{(byte) 27, (byte) 88, (byte) 52, (byte) byteperline, (byte) height};
                System.arraycopy(var24, 0, dataBuff, buffPos, var24.length);
                buffPos += var24.length;
                System.arraycopy(bmpdata, 0, dataBuff, buffPos, bmpdata.length);
                buffPos += bmpdata.length;
            } else {
                var23 = new byte[]{(byte) 27, (byte) 87, (byte) 0, (byte) 0, (byte) 0, (byte) 0, (byte) 100, (byte) 3, (byte) -1, (byte) 0};
                System.arraycopy(var23, 0, dataBuff, buffPos, var23.length);
                buffPos += var23.length;

                img_size_once = 1020 * (width / 32 + 1);
                if (width % 32 == 0) {
                    img_size_once = 1020 * (width / 32);
                }

                byte[] Esc_X_4_2_3;
                byte[] img_tmp2;
                for (repeat = 0; repeat < height / 255; ++repeat) {
                    System.arraycopy(CAN, 0, dataBuff, buffPos, CAN.length);
                    buffPos += CAN.length;

                    Esc_X_4_2_3 = new byte[img_size_once];
                    System.arraycopy(bmpdata, img_size_once * repeat, Esc_X_4_2_3, 0, img_size_once);
                    img_tmp2 = new byte[]{(byte) 27, (byte) 88, (byte) 52, (byte) byteperline, (byte) -1};
                    System.arraycopy(img_tmp2, 0, dataBuff, buffPos, img_tmp2.length);
                    buffPos += img_tmp2.length;

                    System.arraycopy(Esc_X_4_2_3, 0, dataBuff, buffPos, Esc_X_4_2_3.length);
                    buffPos += Esc_X_4_2_3.length;

                    System.arraycopy(ff, 0, dataBuff, buffPos, ff.length);
                    buffPos += ff.length;
                }

                System.arraycopy(CAN, 0, dataBuff, buffPos, CAN.length);
                buffPos += CAN.length;

                Esc_X_4_2_3 = new byte[]{(byte) 27, (byte) 88, (byte) 52, (byte) byteperline, (byte) (height - 255 * repeat)};
                System.arraycopy(Esc_X_4_2_3, 0, dataBuff, buffPos, Esc_X_4_2_3.length);
                buffPos += Esc_X_4_2_3.length;

                img_tmp2 = new byte[imgsize - img_size_once * repeat];
                System.arraycopy(bmpdata, img_size_once * repeat, img_tmp2, 0, imgsize - img_size_once * repeat);

                System.arraycopy(img_tmp2, 0, dataBuff, buffPos, img_tmp2.length);
                buffPos += Esc_X_4_2_3.length;
            }

            System.arraycopy(lf, 0, dataBuff, buffPos, lf.length);
            buffPos += lf.length;

            System.arraycopy(lf, 0, dataBuff, buffPos, lf.length);
            buffPos += lf.length;

            byte[] aa = {0x0c};
            System.arraycopy(aa, 0, dataBuff, buffPos, 1);
            buffPos += aa.length;

            byte transDataCnt = 0;
            int var8;
            int var9;

            for (var9 = 0; var9 < buffPos; ++var9) {
                workBuff[var9 + transDataCnt] = dataBuff[var9];
            }

            var8 = transDataCnt + var9;

            return printByteImage(workBuff, var8);
        }

        return false;
    }

    private static boolean printByteImage(byte[] buff, int nToWrite) {
        int quotient = nToWrite / 10000;
        int remainder = nToWrite % 10000;

        try {
            if (nToWrite > 10000) {
                for (int i = 0; i < quotient; ++i) {
                    myOutStream.write(buff, i * 10000, 10000);
                }

                myOutStream.write(buff, 10000 * quotient, remainder);
                myOutStream.flush();
            } else {
                myOutStream.write(buff, 0, nToWrite);
                myOutStream.flush();
            }
            return true;
        } catch (IOException var6) {
            var6.printStackTrace();
            return false;
        }
    }

    public static void printImage() {
        printParameterSet(new byte[]{(byte) 27, (byte) 64});
        printParameterSet(new byte[]{(byte) 27, (byte) 33, (byte) 0});
        byte[] bufTemp2 = new byte[]{(byte) 27, (byte) 64, (byte) 27, (byte) 74, (byte) 24, (byte) 29, (byte) 118, (byte) 48, (byte) 0, (byte) 16, (byte) 0, (byte) -128, (byte) 0, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -9, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -13, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -15, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 127, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 63, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 31, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 15, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 7, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 3, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 0, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 0, (byte) 127, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 0, (byte) 63, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 0, (byte) 31, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 0, (byte) 15, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 0, (byte) 7, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 0, (byte) 3, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 0, (byte) 1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 8, (byte) 0, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 12, (byte) 0, (byte) 127, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 14, (byte) 0, (byte) 63, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 15, (byte) 0, (byte) 31, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -13, (byte) -1, (byte) -16, (byte) 15, (byte) -128, (byte) 15, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -31, (byte) -1, (byte) -16, (byte) 15, (byte) -64, (byte) 7, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -64, (byte) -1, (byte) -16, (byte) 15, (byte) -32, (byte) 3, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -128, (byte) 127, (byte) -16, (byte) 15, (byte) -16, (byte) 1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) 0, (byte) 63, (byte) -16, (byte) 15, (byte) -8, (byte) 0, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) 0, (byte) 31, (byte) -16, (byte) 15, (byte) -8, (byte) 0, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -128, (byte) 15, (byte) -16, (byte) 15, (byte) -16, (byte) 1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -64, (byte) 7, (byte) -16, (byte) 15, (byte) -32, (byte) 3, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -32, (byte) 3, (byte) -16, (byte) 15, (byte) -64, (byte) 7, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 1, (byte) -16, (byte) 15, (byte) -128, (byte) 15, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -8, (byte) 0, (byte) -16, (byte) 15, (byte) 0, (byte) 31, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -4, (byte) 0, (byte) 112, (byte) 14, (byte) 0, (byte) 63, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -2, (byte) 0, (byte) 48, (byte) 12, (byte) 0, (byte) 127, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) 0, (byte) 16, (byte) 8, (byte) 0, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -128, (byte) 0, (byte) 0, (byte) 1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -64, (byte) 0, (byte) 0, (byte) 3, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -32, (byte) 0, (byte) 0, (byte) 7, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 0, (byte) 0, (byte) 15, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -8, (byte) 0, (byte) 0, (byte) 31, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -4, (byte) 0, (byte) 0, (byte) 63, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -2, (byte) 0, (byte) 0, (byte) 127, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) 0, (byte) 0, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -128, (byte) 1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -64, (byte) 3, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -64, (byte) 3, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -128, (byte) 1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) 0, (byte) 0, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -2, (byte) 0, (byte) 0, (byte) 127, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -4, (byte) 0, (byte) 0, (byte) 63, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -8, (byte) 0, (byte) 0, (byte) 31, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 0, (byte) 0, (byte) 15, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -32, (byte) 0, (byte) 0, (byte) 7, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -64, (byte) 0, (byte) 0, (byte) 3, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -128, (byte) 0, (byte) 0, (byte) 1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) 0, (byte) 16, (byte) 8, (byte) 0, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -2, (byte) 0, (byte) 48, (byte) 12, (byte) 0, (byte) 127, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -4, (byte) 0, (byte) 112, (byte) 14, (byte) 0, (byte) 63, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -8, (byte) 0, (byte) -16, (byte) 15, (byte) 0, (byte) 31, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 1, (byte) -16, (byte) 15, (byte) -128, (byte) 15, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -32, (byte) 3, (byte) -16, (byte) 15, (byte) -64, (byte) 7, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -64, (byte) 7, (byte) -16, (byte) 15, (byte) -32, (byte) 3, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -128, (byte) 15, (byte) -16, (byte) 15, (byte) -16, (byte) 1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) 0, (byte) 31, (byte) -16, (byte) 15, (byte) -8, (byte) 0, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) 0, (byte) 63, (byte) -16, (byte) 15, (byte) -4, (byte) 0, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -128, (byte) 127, (byte) -16, (byte) 15, (byte) -8, (byte) 1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -64, (byte) -1, (byte) -16, (byte) 15, (byte) -16, (byte) 3, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -31, (byte) -1, (byte) -16, (byte) 15, (byte) -32, (byte) 7, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -13, (byte) -1, (byte) -16, (byte) 15, (byte) -64, (byte) 15, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 15, (byte) -128, (byte) 31, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 15, (byte) 0, (byte) 63, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 14, (byte) 0, (byte) 127, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 12, (byte) 0, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 8, (byte) 1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 0, (byte) 3, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 0, (byte) 7, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 0, (byte) 15, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 0, (byte) 31, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 0, (byte) 63, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 0, (byte) 127, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 0, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 3, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 7, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 15, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 31, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 63, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) 127, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -16, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -15, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -13, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -9, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) -1, (byte) 10};
        printByteData(bufTemp2);
        printString("");
        printParameterSet(new byte[]{(byte) 27, (byte) 64});
        printParameterSet(new byte[]{(byte) 27, (byte) 97, (byte) 0});
    }
}

