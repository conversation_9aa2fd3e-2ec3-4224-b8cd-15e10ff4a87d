package com.adins.mss.foundation.location;

import android.view.Menu;
import android.view.MenuItem;

import com.adins.mss.base.R;

public class UpdateMenuIcon {
    public boolean updateGPSIcon(Menu mainMenu) {
        MenuItem existingItem = mainMenu.findItem(R.id.mnGPS);
        if (existingItem != null) {
            if (LocationTrackingManager.getLocationStatus() == 2) {
                existingItem.setIcon(
                        R.drawable.ic_gps_on);
            } else if (LocationTrackingManager.getLocationStatus() == 1) {
                existingItem.setIcon(
                        R.drawable.ic_gps_far);
            } else {
                existingItem.setIcon(
                        R.drawable.ic_gps_off);
            }
        }
        return true;
    }
}
