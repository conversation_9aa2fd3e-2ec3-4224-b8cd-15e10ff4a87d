package com.adins.mss.base.dynamicform.form.questions;

import com.adins.mss.foundation.questiongenerator.QuestionBean;

/**
 * Created by gigin.ginanjar on 01/09/2016.
 */
public interface OnQuestionClickListener {
    void onSetLocationClick(QuestionBean bean, int group, int position);

    void onUpdateLocationClick(QuestionBean bean, int group, int position);

    void onEditDrawingClick(QuestionBean bean, int group, int position);

    void onCapturePhotoClick(QuestionBean bean, int group, int position);

    void onLookupSelectedListener(QuestionBean bean, int group, int position);

    void onReviewClickListener(QuestionBean bean, int group, int position);

    void onAcceptedAgreement(QuestionBean bean, int group, int position);
}
