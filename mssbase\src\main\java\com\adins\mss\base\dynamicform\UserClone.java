package com.adins.mss.base.dynamicform;

import com.adins.mss.dao.User;

public class UserClone extends User {

    public UserClone(User user, boolean useImage) {
        this.setUuid_user(user.getUuid_user());
        this.setFlag_job(user.getFlag_job());
        this.setFullname(user.getFullname());
        this.setBranch_id(user.getBranch_id());
        this.setBranch_name(user.getBranch_name());
        this.setPassword(user.getPassword());
        this.setTask_seq(user.getTask_seq());
        this.setGoogle_id(user.getGoogle_id());
        this.setFacebook_id(user.getFacebook_id());
        this.setLogin_id(user.getLogin_id());
        this.setFail_count(user.getFail_count());
        this.setLast_sync(user.getLast_sync());
        this.setBranch_address(user.getBranch_address());
        this.setUsr_crt(user.getUsr_crt());
        this.setDtm_crt(user.getDtm_crt());
        this.setUsr_upd(user.getUsr_upd());
        this.setDtm_upd(user.getDtm_upd());
        this.setChg_pwd(user.getChg_pwd());
        this.setJob_description(user.getJob_description());
        this.setPwd_exp(user.getPwd_exp());
        this.setDealer_name(user.getDealer_name());
        if (useImage) {
            this.setImage_cover(user.getImage_cover());
            this.setImage_profile(user.getImage_profile());
        }
    }

}
