package com.adins.mss.base.dynamicform.form.questions;


import android.os.Build;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.view.animation.ScaleAnimation;

import androidx.annotation.RequiresApi;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.adins.mss.base.R;
import com.adins.mss.base.dynamicform.DynamicFormActivity;
import com.adins.mss.base.dynamicform.form.ScrollingLinearLayoutManager;
import com.adins.mss.base.dynamicform.form.models.SubmitLayerBean;
import com.adins.mss.base.dynamicform.form.questions.viewholder.CekReferantorViewHolder;
import com.adins.mss.base.dynamicform.form.questions.viewholder.ValidationCheckQuestionViewHolder;
import com.adins.mss.base.dynamicform.form.questions.viewholder.DSRQuestionViewHolder;
import com.adins.mss.base.dynamicform.form.questions.viewholder.DateTimeQuestionViewHolder;
import com.adins.mss.base.dynamicform.form.questions.viewholder.DrawingQuestionViewHolder;
import com.adins.mss.base.dynamicform.form.questions.viewholder.DropdownQuestionViewHolder;
import com.adins.mss.base.dynamicform.form.questions.viewholder.ExpandableRecyclerView;
import com.adins.mss.base.dynamicform.form.questions.viewholder.GetOTRViewHolder;
import com.adins.mss.base.dynamicform.form.questions.viewholder.ImageQuestionViewHolder;
import com.adins.mss.base.dynamicform.form.questions.viewholder.InvitationEsignViewHolder;
import com.adins.mss.base.dynamicform.form.questions.viewholder.LocationQuestionViewHolder;
import com.adins.mss.base.dynamicform.form.questions.viewholder.LookupQuestionViewHolder;
import com.adins.mss.base.dynamicform.form.questions.viewholder.MultipleQuestionViewHolder;
import com.adins.mss.base.dynamicform.form.questions.viewholder.QuestionGroupViewHolder;
import com.adins.mss.base.dynamicform.form.questions.viewholder.RadioQuestionViewHolder;
import com.adins.mss.base.dynamicform.form.questions.viewholder.RegistrationCheckViewHolder;
import com.adins.mss.base.dynamicform.form.questions.viewholder.ScoringQuestionViewHolder;
import com.adins.mss.base.dynamicform.form.questions.viewholder.SubmitLayerQuestionViewHolder;
import com.adins.mss.base.dynamicform.form.questions.viewholder.TelecheckQuestionViewHolder;
import com.adins.mss.base.dynamicform.form.questions.viewholder.TextMultilineSeparateViewHolder;
import com.adins.mss.base.dynamicform.form.questions.viewholder.TextQuestionViewHolder;
import com.adins.mss.base.dynamicform.form.questions.viewholder.TextWithSuggestionQuestionViewHolder;
import com.adins.mss.constant.Global;
import com.adins.mss.foundation.questiongenerator.QuestionBean;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * Created by gigin.ginanjar on 03/09/2016.
 */
public class QuestionViewAdapter extends ExpandableRecyclerView.Adapter<RecyclerView.ViewHolder, QuestionGroupViewHolder, QuestionBean, String> {
    private static final int FADE_DURATION = 750; // in milliseconds
    private final LinkedHashMap<String, List<QuestionBean>> mValues;
    private List<QuestionBean> list;
    private final List<String> mGroups;
    private final FragmentActivity mActivity;
    private final int VIEW_TYPE_LOADING = 999;
    public ScrollingLinearLayoutManager linearLayoutManager;
    public ExpandableRecyclerView mRecyclerView;
    private OnQuestionClickListener mListener;
    private OnIsOfflineDSRListener mDsrOfflineListener;
    private int lastPosition = -1;

    public QuestionViewAdapter(FragmentActivity activity, ExpandableRecyclerView recyclerView,
                               List<String> groups, LinkedHashMap<String, List<QuestionBean>> items,
                               OnQuestionClickListener listener,
                               OnIsOfflineDSRListener dsrOfflineListener,
                               List<QuestionBean> listOfQuestions) {
        mActivity = activity;
        mValues = items;
        mListener = listener;
        mDsrOfflineListener = dsrOfflineListener;
        mGroups = groups;
        mRecyclerView = recyclerView;
        list = listOfQuestions;
    }

    public static boolean IsScoringQuestion(int answerType){
        return answerType == Integer.valueOf(Global.AT_SCORING);
    }
    public static boolean IsTextQuestion(String answerType) {
        return answerType.equals(Global.AT_TEXT) ||
                answerType.equals(Global.AT_TEXT_MULTILINE) ||
                answerType.equals(Global.AT_CURRENCY) ||
                answerType.equals(Global.AT_NUMERIC) ||
                answerType.equals(Global.AT_DECIMAL) ||
                answerType.equals(Global.AT_INVITATION_ESIGN);
    }

    public static boolean IsDropdownQuestion(String answerType) {
        return answerType.equals(Global.AT_DROPDOWN) ||
                answerType.equals(Global.AT_DROPDOWN_W_DESCRIPTION);
    }

    public static boolean IsMultipleQuestion(String answerType) {
        return answerType.equals(Global.AT_MULTIPLE) ||
                answerType.equals(Global.AT_MULTIPLE_ONE_DESCRIPTION) ||
                answerType.equals(Global.AT_MULTIPLE_W_DESCRIPTION);
    }

    public static boolean IsRadioQuestion(String answerType) {
        return answerType.equals(Global.AT_RADIO) ||
                answerType.equals(Global.AT_RADIO_W_DESCRIPTION);
    }

    public static boolean IsImageQuestion(String answerType) {
        return answerType.equals(Global.AT_IMAGE) ||
                answerType.equals(Global.AT_IMAGE_W_GPS_ONLY) ||
                answerType.equals(Global.AT_IMAGE_W_LOCATION);
    }

    public static boolean IsLookupQuestion(String answerType) {
        return answerType.equals(Global.AT_LOV) ||
                answerType.equals(Global.AT_LOV_W_FILTER) ||
                answerType.equals(Global.AT_LOOKUP);
    }

    public static boolean IsTextQuestion(int answerType) {
        return answerType == Integer.valueOf(Global.AT_TEXT) ||
                answerType == Integer.valueOf(Global.AT_TEXT_MULTILINE) ||
                answerType == Integer.valueOf(Global.AT_CURRENCY) ||
                answerType == Integer.valueOf(Global.AT_NUMERIC) ||
                answerType == Integer.valueOf(Global.AT_DECIMAL);
    }

    public static boolean IsDropdownQuestion(int answerType) {
        return answerType == Integer.valueOf(Global.AT_DROPDOWN) ||
                answerType == Integer.valueOf(Global.AT_DROPDOWN_W_DESCRIPTION) ||
                answerType == Integer.valueOf(Global.AT_LOOKUP_TABLE);
    }

    public static boolean IsMultipleQuestion(int answerType) {
        return answerType == Integer.valueOf(Global.AT_MULTIPLE) ||
                answerType == Integer.valueOf(Global.AT_MULTIPLE_ONE_DESCRIPTION) ||
                answerType == Integer.valueOf(Global.AT_MULTIPLE_W_DESCRIPTION);
    }

    public static boolean IsRadioQuestion(int answerType) {
        return answerType == Integer.valueOf(Global.AT_RADIO) ||
                answerType == Integer.valueOf(Global.AT_RADIO_W_DESCRIPTION);
    }

    public static boolean IsImageQuestion(int answerType) {
        return answerType == Integer.valueOf(Global.AT_IMAGE) ||
                answerType == Integer.valueOf(Global.AT_IMAGE_W_GPS_ONLY) ||
                answerType == Integer.valueOf(Global.AT_IMAGE_W_LOCATION) ||
                answerType == Integer.valueOf(Global.AT_OCR_W_GALLERY) ||
                answerType == Integer.valueOf(Global.AT_IMAGE_LIVENESS) ||
                answerType == Integer.parseInt(Global.AT_OCR);
    }

    public static boolean IsDateTimeQuestion(int answerType) {
        return answerType == Integer.valueOf(Global.AT_DATE) ||
                answerType == Integer.valueOf(Global.AT_DATE_TIME) ||
                answerType == Integer.valueOf(Global.AT_TIME);
    }

    public static boolean IsLocationQuestion(int answerType) {
        return answerType == Integer.valueOf(Global.AT_LOCATION) ||
                answerType == Integer.valueOf(Global.AT_LOCATION_WITH_ADDRESS);
    }

    public static boolean IsDrawingQuestion(int answerType) {
        return answerType == Integer.valueOf(Global.AT_DRAWING);
    }

    public static boolean IsTextWithSuggestionQuestion(int answerType) {
        return answerType == Integer.valueOf(Global.AT_TEXT_WITH_SUGGESTION) ||
                answerType == Integer.valueOf(Global.AT_TEXT_WITH_SUGGESTION_NEW);
    }

    public static boolean IsLookupQuestion(int answerType) {
        return answerType == Integer.valueOf(Global.AT_LOV) ||
                answerType == Integer.valueOf(Global.AT_LOV_W_FILTER) ||
                answerType == Integer.valueOf(Global.AT_LOOKUP);
    }
    public static boolean isSubmitLayerQuestion(int answerType) {
        return answerType == Integer.parseInt(Global.AT_SUBMIT_LAYER);
    }
    public static boolean isTeleCheckQuestion(int answerType) {
        return answerType == Integer.parseInt(Global.AT_TELE_CHECK);
    }

    public static boolean isGetDSRQuestion(int answerType) {
        return answerType == Integer.parseInt(Global.AT_DSR);
    }

    public static boolean isTextMultilineSeparate(int answerType) {
        return answerType == Integer.parseInt(Global.AT_TEXT_MULTILINE_SEPARATE);
    }

    public static boolean IsInvitationEsignQuestion(int answerType) {
        return answerType == Integer.parseInt(Global.AT_INVITATION_ESIGN);
    }

    public static boolean isRegistrationCheckQuestion(int answerType) {
        return answerType == Integer.parseInt(Global.AT_REGISTRATION_CHECK);
    }

    public static boolean isGetOTRQuestion(int answerType) {
        return answerType == Integer.parseInt(Global.AT_GET_OTR);
    }

    public static boolean isCheckReferantorQuestion(int answerType) {
        return answerType == Integer.parseInt(Global.AT_CEK_REFERANTOR);
    }

    public static boolean isValidationCheckQuestion(int answerType) {
        return answerType == Integer.parseInt(Global.AT_VALIDATION_CHECK);
    }

    @Override
    public int getGroupItemCount() {
        return mGroups.size() - 1;
    }

    @Override
    public int getChildItemCount(int group) {
        return mValues.get(mGroups.get(group)).size();
    }

    @Override
    public String getGroupItem(int position) {
        return mGroups.get(position);
    }

    @Override
    public QuestionBean getChildItem(int group, int position) {
        return mValues.get(getGroupItem(group)).get(position);
    }

    @Override
    protected QuestionGroupViewHolder onCreateGroupViewHolder(ViewGroup parent) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.question_group_layout, parent, false);
        return new QuestionGroupViewHolder(view);
    }

    @Override
    public void onBindGroupViewHolder(QuestionGroupViewHolder holder, int group) {
        super.onBindGroupViewHolder(holder, group);
        String qGroup = "";
        try {
            qGroup = getGroupItem(group);
        } catch (Exception e) {
            e.printStackTrace();
        }

        holder.bind(qGroup);
        setFadeAnimation2(holder.itemView);
    }

    @Override
    protected RecyclerView.ViewHolder onCreateChildViewHolder(ViewGroup parent, int viewType) {
        if (IsTextQuestion(viewType)) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.question_text_layout, parent, false);
            return new TextQuestionViewHolder(mActivity, view, mRecyclerView, mListener);
        } else if (IsDropdownQuestion(viewType)) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.question_dropdown_layout, parent, false);
            return new DropdownQuestionViewHolder(view, mActivity);
        } else if (IsMultipleQuestion(viewType)) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.question_multiple_layout, parent, false);
            return new MultipleQuestionViewHolder(view, mActivity);
        } else if (IsImageQuestion(viewType)) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.question_image_layout, parent, false);
            return new ImageQuestionViewHolder(view, mActivity, mListener);
        } else if (IsRadioQuestion(viewType)) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.question_radio_layout, parent, false);
            return new RadioQuestionViewHolder(view, mActivity);
        } else if (IsLocationQuestion(viewType)) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.question_location_layout, parent, false);
            return new LocationQuestionViewHolder(view, mActivity, mListener);
        } else if (IsDrawingQuestion(viewType)) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.question_drawing_layout, parent, false);
            return new DrawingQuestionViewHolder(view, mActivity, mListener);
        } else if (IsDateTimeQuestion(viewType)) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.question_datetime_layout, parent, false);
            return new DateTimeQuestionViewHolder(mActivity, view);
        } else if (IsTextWithSuggestionQuestion(viewType)) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.question_textwithsuggestion_layout, parent, false);
            return new TextWithSuggestionQuestionViewHolder(view, mActivity);
        } else if (IsLookupQuestion(viewType)) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.question_lookup_layout, parent, false);
            return new LookupQuestionViewHolder(view, mActivity, mListener);
        } else if(IsScoringQuestion(viewType)){ //Nendi: 2017/10/27
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.question_scoring_layout, parent, false);
            return new ScoringQuestionViewHolder(view);
        } else if (isSubmitLayerQuestion(viewType)) {
            View view = LayoutInflater.from(parent.getContext()).inflate(
                    R.layout.question_submit_layer_layout, parent, false);
            SubmitLayerBean layerBean = new SubmitLayerBean();
            layerBean.setTaskH(DynamicFormActivity.header.getTaskH());
            layerBean.setQuestionBeanListOfGroup(mValues);
            layerBean.setQuestionBeans(list);
            return new SubmitLayerQuestionViewHolder(view, mActivity, layerBean);
        } else if (isTeleCheckQuestion(viewType)) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.question_telecheck_layout, parent, false);
            return new TelecheckQuestionViewHolder(view, mActivity);
        } else if (isGetDSRQuestion(viewType)) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.question_dsr_layout, parent, false);
            return new DSRQuestionViewHolder(view, mActivity, mDsrOfflineListener);
        } else if (isTextMultilineSeparate(viewType)) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.question_text_multiline_separate_layout, parent, false);
            return new TextMultilineSeparateViewHolder(view, mActivity);
        }
        // Jowoen 30 Agustus '24 : CR Esign
        else if (IsInvitationEsignQuestion(viewType)) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.question_invitation_esign_layout, parent, false);
            return new InvitationEsignViewHolder(view, mActivity);
        } else if (isRegistrationCheckQuestion(viewType)) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.question_registration_check_layout, parent, false);
            return new RegistrationCheckViewHolder(view, mActivity);
        }
        // Merge CR Maybank
        else if (isGetOTRQuestion(viewType)) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.question_get_otr_layout, parent, false);
            return new GetOTRViewHolder(view, mActivity);
        }
        // CR Thirdparty
        else if (isCheckReferantorQuestion(viewType)) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.question_cek_referantor_layout, parent, false);
            return new CekReferantorViewHolder(view, mActivity);
        }
        // CR ThirdParty
        else if (isValidationCheckQuestion(viewType)) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.question_validation_check_layout, parent, false);
            return new ValidationCheckQuestionViewHolder(view, mActivity, mValues, list);
        }
        return null;
    }

    @Override
    public int getChildItemViewType(int group, int position) {
        int viewType = Integer.valueOf(getChildItem(group, position).getAnswer_type());
        return getChildItem(group, position) == null ? VIEW_TYPE_LOADING : viewType;
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    @Override
    public void onBindChildViewHolder(RecyclerView.ViewHolder mHolder, int group, int position) {
        super.onBindChildViewHolder(mHolder, group, position);
        if (mHolder instanceof TextQuestionViewHolder) {
            final TextQuestionViewHolder holder = (TextQuestionViewHolder) mHolder;
            holder.bind(getChildItem(group, position), group, position + 1);
        } else if (mHolder instanceof DropdownQuestionViewHolder) {
            final DropdownQuestionViewHolder holder = (DropdownQuestionViewHolder) mHolder;
            holder.bind(getChildItem(group, position), position + 1);
        } else if (mHolder instanceof RadioQuestionViewHolder) {
            final RadioQuestionViewHolder holder = (RadioQuestionViewHolder) mHolder;
            holder.bind(getChildItem(group, position), position + 1);
        } else if (mHolder instanceof MultipleQuestionViewHolder) {
            final MultipleQuestionViewHolder holder = (MultipleQuestionViewHolder) mHolder;
            holder.bind(getChildItem(group, position), position + 1);
        } else if (mHolder instanceof LocationQuestionViewHolder) {
            final LocationQuestionViewHolder holder = (LocationQuestionViewHolder) mHolder;
            holder.bind(getChildItem(group, position), group, position + 1);
        } else if (mHolder instanceof ImageQuestionViewHolder) {
            final ImageQuestionViewHolder holder = (ImageQuestionViewHolder) mHolder;
            holder.bind(getChildItem(group, position), group, position + 1);
        } else if (mHolder instanceof DrawingQuestionViewHolder) {
            final DrawingQuestionViewHolder holder = (DrawingQuestionViewHolder) mHolder;
            holder.bind(getChildItem(group, position), group, position + 1);
        } else if (mHolder instanceof TextWithSuggestionQuestionViewHolder) {
            final TextWithSuggestionQuestionViewHolder holder = (TextWithSuggestionQuestionViewHolder) mHolder;
            holder.bind(getChildItem(group, position), position + 1);
        } else if (mHolder instanceof DateTimeQuestionViewHolder) {
            final DateTimeQuestionViewHolder holder = (DateTimeQuestionViewHolder) mHolder;
            holder.bind(getChildItem(group, position), position + 1);
        } else if (mHolder instanceof LookupQuestionViewHolder) {
            final LookupQuestionViewHolder holder = (LookupQuestionViewHolder) mHolder;
            holder.bind(getChildItem(group,position), group, position+1);
        } else if(mHolder instanceof ScoringQuestionViewHolder){ //Nendi: 2017/10/27
            final ScoringQuestionViewHolder holder = (ScoringQuestionViewHolder) mHolder;
            holder.bind(getChildItem(group, position), position + 1);
        } else if(mHolder instanceof SubmitLayerQuestionViewHolder) {
            final SubmitLayerQuestionViewHolder holder = (SubmitLayerQuestionViewHolder) mHolder;
            holder.bind(getChildItem(group, position),position + 1);
        } else if (mHolder instanceof TelecheckQuestionViewHolder) {
            final TelecheckQuestionViewHolder holder = (TelecheckQuestionViewHolder) mHolder;
            holder.bind(getChildItem(group, position),position + 1);
        }  else if (mHolder instanceof DSRQuestionViewHolder) {
            final DSRQuestionViewHolder holder = (DSRQuestionViewHolder) mHolder;
            holder.bind(getChildItem(group, position),position + 1);
        } else if (mHolder instanceof TextMultilineSeparateViewHolder) {
            final TextMultilineSeparateViewHolder holder = (TextMultilineSeparateViewHolder) mHolder;
            holder.bind(getChildItem(group, position), position + 1);
        }
        //
        else if(mHolder instanceof InvitationEsignViewHolder) {
            final InvitationEsignViewHolder holder = (InvitationEsignViewHolder) mHolder;
            holder.bind(getChildItem(group, position), position + 1);
        } else if (mHolder instanceof RegistrationCheckViewHolder) {
            final RegistrationCheckViewHolder holder = (RegistrationCheckViewHolder) mHolder;
            holder.bind(getChildItem(group, position), position + 1);
        } else if (mHolder instanceof GetOTRViewHolder) {
            final GetOTRViewHolder holder = (GetOTRViewHolder) mHolder;
            holder.bind(getChildItem(group, position), position + 1);
        } else if (mHolder instanceof CekReferantorViewHolder) {
            final CekReferantorViewHolder holder = (CekReferantorViewHolder) mHolder;
            holder.bind(getChildItem(group, position), position + 1);
        } else if (mHolder instanceof ValidationCheckQuestionViewHolder) {
            final ValidationCheckQuestionViewHolder holder = (ValidationCheckQuestionViewHolder) mHolder;
            holder.bind(getChildItem(group, position), position + 1);
        }
        setFadeAnimation(mHolder.itemView, position);
    }

    @Override
    public void onViewDetachedFromWindow(RecyclerView.ViewHolder holder) {
        super.onViewDetachedFromWindow(holder);
        holder.itemView.clearAnimation();
    }

    private void setFadeAnimation(View view, int position) {
        if (position > lastPosition) {
            lastPosition = position;
            AlphaAnimation anim = new AlphaAnimation(0.0f, 1.0f);
            anim.setDuration(FADE_DURATION);
            view.startAnimation(anim);
        } else if (lastPosition > getItemCount() - 1) {
            lastPosition = getItemCount() - 1;
        } else {
//            setScaleAnimation(view);
            setFadeAnimation2(view);
        }
    }

    private void setFadeAnimation2(View view) {
        AlphaAnimation anim = new AlphaAnimation(0.0f, 1.0f);
        anim.setDuration(FADE_DURATION / 2);
        view.startAnimation(anim);
    }

    private void setScaleAnimation(View view) {
        ScaleAnimation anim = new ScaleAnimation(0.0f, 1.0f, 0.0f, 1.0f, Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
        anim.setDuration(150);
        view.startAnimation(anim);
    }
}
