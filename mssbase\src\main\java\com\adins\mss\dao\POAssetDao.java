package com.adins.mss.dao;

import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;

import com.adins.mss.dao.POAsset;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "MS_PO_ASSET".
*/
public class POAssetDao extends AbstractDao<POAsset, Long> {

    public static final String TABLENAME = "MS_PO_ASSET";

    /**
     * Properties of entity POAsset.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Id = new Property(0, long.class, "id", true, "ID");
        public final static Property Is_deleted = new Property(1, Integer.class, "is_deleted", false, "IS_DELETED");
        public final static Property Asset_scheme_id = new Property(2, int.class, "asset_scheme_id", false, "ASSET_SCHEME_ID");
        public final static Property Brand_code = new Property(3, String.class, "brand_code", false, "BRAND_CODE");
        public final static Property Brand_name = new Property(4, String.class, "brand_name", false, "BRAND_NAME");
        public final static Property Model_code = new Property(5, String.class, "model_code", false, "MODEL_CODE");
        public final static Property Model_name = new Property(6, String.class, "model_name", false, "MODEL_NAME");
        public final static Property Group_type = new Property(7, String.class, "group_type", false, "GROUP_TYPE");
        public final static Property Master_code = new Property(8, String.class, "master_code", false, "MASTER_CODE");
        public final static Property Master_name = new Property(9, String.class, "master_name", false, "MASTER_NAME");
        public final static Property Dtm_upd = new Property(10, java.util.Date.class, "dtm_upd", false, "DTM_UPD");
    };


    public POAssetDao(DaoConfig config) {
        super(config);
    }
    
    public POAssetDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"MS_PO_ASSET\" (" + //
                "\"ID\" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL ," + // 0: id
                "\"IS_DELETED\" INTEGER," + // 1: is_deleted
                "\"ASSET_SCHEME_ID\" INTEGER NOT NULL ," + // 2: asset_scheme_id
                "\"BRAND_CODE\" TEXT NOT NULL ," + // 3: brand_code
                "\"BRAND_NAME\" TEXT NOT NULL ," + // 4: brand_name
                "\"MODEL_CODE\" TEXT NOT NULL ," + // 5: model_code
                "\"MODEL_NAME\" TEXT NOT NULL ," + // 6: model_name
                "\"GROUP_TYPE\" TEXT NOT NULL ," + // 7: group_type
                "\"MASTER_CODE\" TEXT NOT NULL ," + // 8: master_code
                "\"MASTER_NAME\" TEXT NOT NULL ," + // 9: master_name
                "\"DTM_UPD\" INTEGER);"); // 10: dtm_upd
        // Add Indexes
        db.execSQL("CREATE INDEX " + constraint + "IDX_MS_PO_ASSET_BRAND_NAME ON MS_PO_ASSET" +
                " (\"BRAND_NAME\");");
        db.execSQL("CREATE INDEX " + constraint + "IDX_MS_PO_ASSET_BRAND_NAME_MODEL_NAME ON MS_PO_ASSET" +
                " (\"BRAND_NAME\",\"MODEL_NAME\");");
        db.execSQL("CREATE INDEX " + constraint + "IDX_MS_PO_ASSET_BRAND_NAME_MODEL_NAME_GROUP_TYPE ON MS_PO_ASSET" +
                " (\"BRAND_NAME\",\"MODEL_NAME\",\"GROUP_TYPE\");");
        db.execSQL("CREATE INDEX " + constraint + "IDX_MS_PO_ASSET_BRAND_NAME_MODEL_NAME_GROUP_TYPE_MASTER_NAME ON MS_PO_ASSET" +
                " (\"BRAND_NAME\",\"MODEL_NAME\",\"GROUP_TYPE\",\"MASTER_NAME\");");
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"MS_PO_ASSET\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, POAsset entity) {
        stmt.clearBindings();
        stmt.bindLong(1, entity.getId());
 
        Integer is_deleted = entity.getIs_deleted();
        if (is_deleted != null) {
            stmt.bindLong(2, is_deleted);
        }
        stmt.bindLong(3, entity.getAsset_scheme_id());
        stmt.bindString(4, entity.getBrand_code());
        stmt.bindString(5, entity.getBrand_name());
        stmt.bindString(6, entity.getModel_code());
        stmt.bindString(7, entity.getModel_name());
        stmt.bindString(8, entity.getGroup_type());
        stmt.bindString(9, entity.getMaster_code());
        stmt.bindString(10, entity.getMaster_name());
 
        java.util.Date dtm_upd = entity.getDtm_upd();
        if (dtm_upd != null) {
            stmt.bindLong(11, dtm_upd.getTime());
        }
    }

    /** @inheritdoc */
    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.getLong(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public POAsset readEntity(Cursor cursor, int offset) {
        POAsset entity = new POAsset( //
            cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getInt(offset + 1), // is_deleted
            cursor.getInt(offset + 2), // asset_scheme_id
            cursor.getString(offset + 3), // brand_code
            cursor.getString(offset + 4), // brand_name
            cursor.getString(offset + 5), // model_code
            cursor.getString(offset + 6), // model_name
            cursor.getString(offset + 7), // group_type
            cursor.getString(offset + 8), // master_code
            cursor.getString(offset + 9), // master_name
            cursor.isNull(offset + 10) ? null : new java.util.Date(cursor.getLong(offset + 10)) // dtm_upd
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, POAsset entity, int offset) {
        entity.setId(cursor.getLong(offset + 0));
        entity.setIs_deleted(cursor.isNull(offset + 1) ? null : cursor.getInt(offset + 1));
        entity.setAsset_scheme_id(cursor.getInt(offset + 2));
        entity.setBrand_code(cursor.getString(offset + 3));
        entity.setBrand_name(cursor.getString(offset + 4));
        entity.setModel_code(cursor.getString(offset + 5));
        entity.setModel_name(cursor.getString(offset + 6));
        entity.setGroup_type(cursor.getString(offset + 7));
        entity.setMaster_code(cursor.getString(offset + 8));
        entity.setMaster_name(cursor.getString(offset + 9));
        entity.setDtm_upd(cursor.isNull(offset + 10) ? null : new java.util.Date(cursor.getLong(offset + 10)));
     }
    
    /** @inheritdoc */
    @Override
    protected Long updateKeyAfterInsert(POAsset entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    /** @inheritdoc */
    @Override
    public Long getKey(POAsset entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
}
