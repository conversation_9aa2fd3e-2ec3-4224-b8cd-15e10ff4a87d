package com.adins.mss.foundation.db.dataaccess;

import android.content.Context;
import android.database.Cursor;

import com.adins.mss.dao.DaoSession;
import com.adins.mss.dao.Decision;
import com.adins.mss.dao.DecisionDao;
import com.adins.mss.foundation.db.DaoOpenHelper;

import java.util.ArrayList;
import java.util.List;

import de.greenrobot.dao.query.QueryBuilder;

/**
 * Created by k<PERSON><PERSON><PERSON> on 4/18/18.
 */

public class DecisionDataAccess {
    /**
     * use to generate dao session that you can access modelDao
     *
     * @param context --> context from activity
     * @return
     */
    protected static DaoSession getDaoSession(Context context) {
        return DaoOpenHelper.getDaoSession(context);
    }

    /**
     * Get DecisionDao and you can access db
     * @param context
     * @return
     */
    protected static DecisionDao getDecisionDao(Context context) {
        return getDaoSession(context).getDecisionDao();
    }

    public static Decision getOne(Context context) {
        QueryBuilder<Decision> qb = getDecisionDao(context).queryBuilder();
        qb.limit(1).build();

        if (!qb.list().isEmpty()) return qb.list().get(0);
        return null;
    }

    public static List<Integer> getRow(Context context, String query) {
//        QueryBuilder<Integer> qb = getDecisionDao(context).queryBuilder().build();
//        query = "SELECT a.* FROM (SELECT DISTINCT ROW_NO FROM MS_TMP_RULE WHERE (CODE = 'VALUE' AND COLUMN_NO = 1 AND VALUE = '-') INTERSECT SELECT DISTINCT ROW_NO FROM MS_TMP_RULE WHERE (CODE = 'VALUE' AND COLUMN_NO = 2 AND VALUE = '-') OR (CODE = 'VALUE' AND COLUMN_NO = 2 AND VALUE = 'M21') INTERSECT SELECT DISTINCT ROW_NO FROM MS_TMP_RULE WHERE (CODE = 'VALUE' AND COLUMN_NO = 3 AND VALUE = '-') OR (CODE = 'VALUE' AND COLUMN_NO = 3 AND VALUE = 'COL') INTERSECT SELECT DISTINCT ROW_NO FROM MS_TMP_RULE WHERE (CODE = 'VALUE' AND COLUMN_NO = 4 AND VALUE = '-') OR (CODE = 'VALUE' AND COLUMN_NO = 4 AND VALUE = 'M21COL00093') INTERSECT SELECT DISTINCT ROW_NO FROM MS_TMP_RULE WHERE (CODE = 'VALUE' AND COLUMN_NO = 7 AND VALUE = '-') INTERSECT SELECT DISTINCT ROW_NO FROM MS_TMP_RULE WHERE (CODE = 'VALUE' AND COLUMN_NO = 8 AND VALUE = '-') INTERSECT SELECT DISTINCT ROW_NO FROM MS_TMP_RULE WHERE (CODE = 'VALUE' AND COLUMN_NO = 9 AND VALUE = '-')) a ORDER BY ROW_NO";

        List<Integer> rows = new ArrayList<>();
        Cursor cursor = getDaoSession(context).getDatabase().rawQuery(query, null);
        try {
            if (cursor.moveToFirst()) {
                do {
                    rows.add(cursor.getInt(cursor.getColumnIndex("ROW_NO")));
                } while (cursor.moveToNext());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            cursor.close();
        }

        return rows;
    }

    public static List<Decision> getDecision(Context context) {
        QueryBuilder<Decision> qb = getDecisionDao(context).queryBuilder();

        qb.where(DecisionDao.Properties.Code.eq("CONDITION"),
                DecisionDao.Properties.Value.like("%==%"));
//        qb.where(DecisionDao.Properties.Value.like("%==%"));
        qb.build();

        if (!qb.list().isEmpty()) return qb.list();
        else return null;
    }

    public static List<Decision> getByCodeIn(Context context, Object[] objects) {
        QueryBuilder<Decision> qb = getDecisionDao(context).queryBuilder();

        qb.where(DecisionDao.Properties.Code.in(objects));
        qb.orderAsc(DecisionDao.Properties.Column_no);
        qb.build();

        if (!qb.list().isEmpty()) return qb.list();
        else return new ArrayList<Decision>();
    }

    public static List<Decision> getByRowNo(Context context, Integer rowNO) {
        QueryBuilder<Decision> qb = getDecisionDao(context).queryBuilder();
        qb.where(DecisionDao.Properties.Row_no.eq(rowNO));
        qb.build();

        return qb.list();
    }
}
