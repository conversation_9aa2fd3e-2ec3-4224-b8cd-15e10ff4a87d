package com.adins.mss.dao;

import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;

import com.adins.mss.dao.PrintDate;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TR_PRINTDATE".
*/
public class PrintDateDao extends AbstractDao<PrintDate, java.util.Date> {

    public static final String TABLENAME = "TR_PRINTDATE";

    /**
     * Properties of entity PrintDate.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Dtm_print = new Property(0, java.util.Date.class, "dtm_print", true, "DTM_PRINT");
        public final static Property Uuid_task_h = new Property(1, String.class, "uuid_task_h", false, "UUID_TASK_H");
    };


    public PrintDateDao(DaoConfig config) {
        super(config);
    }
    
    public PrintDateDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TR_PRINTDATE\" (" + //
                "\"DTM_PRINT\" INTEGER PRIMARY KEY NOT NULL ," + // 0: dtm_print
                "\"UUID_TASK_H\" TEXT);"); // 1: uuid_task_h
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TR_PRINTDATE\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, PrintDate entity) {
        stmt.clearBindings();
        stmt.bindLong(1, entity.getDtm_print().getTime());
 
        String uuid_task_h = entity.getUuid_task_h();
        if (uuid_task_h != null) {
            stmt.bindString(2, uuid_task_h);
        }
    }

    /** @inheritdoc */
    @Override
    public java.util.Date readKey(Cursor cursor, int offset) {
        return new java.util.Date(cursor.getLong(offset + 0));
    }    

    /** @inheritdoc */
    @Override
    public PrintDate readEntity(Cursor cursor, int offset) {
        PrintDate entity = new PrintDate( //
            new java.util.Date(cursor.getLong(offset + 0)), // dtm_print
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1) // uuid_task_h
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, PrintDate entity, int offset) {
        entity.setDtm_print(new java.util.Date(cursor.getLong(offset + 0)));
        entity.setUuid_task_h(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
     }
    
    /** @inheritdoc */
    @Override
    protected java.util.Date updateKeyAfterInsert(PrintDate entity, long rowId) {
        return entity.getDtm_print();
    }
    
    /** @inheritdoc */
    @Override
    public java.util.Date getKey(PrintDate entity) {
        if(entity != null) {
            return entity.getDtm_print();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
}
