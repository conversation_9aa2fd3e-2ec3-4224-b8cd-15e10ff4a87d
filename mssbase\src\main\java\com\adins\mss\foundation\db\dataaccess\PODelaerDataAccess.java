package com.adins.mss.foundation.db.dataaccess;

import android.content.Context;
import android.database.Cursor;
import android.util.Log;

import com.adins.mss.base.commons.Query;
import com.adins.mss.dao.DaoSession;
import com.adins.mss.dao.Lookup;
import com.adins.mss.dao.PODealer;
import com.adins.mss.dao.PODealerDao;
import com.adins.mss.dao.ProductOfferingDao;
import com.adins.mss.foundation.db.DaoOpenHelper;
import com.adins.mss.foundation.questiongenerator.OptionAnswerBean;
import com.adins.mss.foundation.questiongenerator.QuestionBean;

import java.util.ArrayList;
import java.util.List;

import de.greenrobot.dao.query.QueryBuilder;

/**
 * Created by developer on 1/18/18.
 */

public class PODelaerDataAccess {

    /**
     * use to generate dao session that you can access modelDao
     *
     * @param context --> context from activity
     * @return
     */
    protected static DaoSession getDaoSession(Context context) {
        return DaoOpenHelper.getDaoSession(context);
    }

    /**
     * get PODao and you can access db
     */
    protected static PODealerDao getPoDealer(Context context) {
        return getDaoSession(context).getPODealerDao();
    }

    /**
     * add PO as entity
     */
    public static void add(Context context, PODealer entity) {
        getPoDealer(context).insert(entity);
        getDaoSession(context).clear();
    }

    /**
     * add PO as list
     */
    public static void add(Context context, List<PODealer> entities) {
        getPoDealer(context).insertInTx(entities);
        getDaoSession(context).clear();
    }

    public static void addOrReplace(Context context, PODealer entity) {
        getPoDealer(context).insertOrReplace(entity);
        getDaoSession(context).clear();
    }

    public static void addOrReplace(Context context, List<PODealer> entities) {
        getPoDealer(context).insertOrReplaceInTx(entities);
        getDaoSession(context).clear();
    }

    /**
     * Action for Delete
     */
    public static void delete(Context context, PODealer entity) {
        getPoDealer(context).delete(entity);
        getDaoSession(context).clear();
    }

    public static void clean(Context context) {
        getPoDealer(context).deleteAll();
        getDaoSession(context).clear();
    }

    /**
     * Get One Entity by Id
     * @param context
     * @param id
     * @return
     */
    public static PODealer getOne(Context context, int id) {
        QueryBuilder<PODealer> qb = getPoDealer(context).queryBuilder();
        qb.where(ProductOfferingDao.Properties.Prod_off_id.eq(id));
        qb.build();

        if (qb.list().isEmpty()) return null;
        else return qb.list().get(0);
    }

    /**
     * get All entities
     * @param context
     * @return
     */
    public static List<PODealer> all(Context context) {
        List<PODealer> data = getPoDealer(context).loadAll();

        if (!data.isEmpty()) return data;
        else return null;
    }

    public static List<OptionAnswerBean> lookup(Context context, Query query, String keyword) {
        List<OptionAnswerBean> answerBeans = new ArrayList<>();
        String statement = "SELECT MS_PO_DEALER.* FROM MS_PO_DEALER LEFT JOIN MS_PO ON MS_PO_DEALER.DEALER_SCHEME_ID = MS_PO.DEALER_SCHEME_ID WHERE %s";

        StringBuilder constraint = new StringBuilder("1");
        String groupBy  = " GROUP BY ";

        if (query.getConstraint() != null) {
            List<Query.Constraint> constraints = query.getConstraint();
            for (int i = 0; i < constraints.size(); i++) {
                constraint.append(" AND ")
                        .append(constraints.get(i).getColumn());

                if (constraints.get(i).getOperator() != null) {
                    constraint.append(" ").append(constraints.get(i).getOperator().trim()).append(" ");
                    if (constraints.get(i).getOperator().toLowerCase().contains("like")) {
                        constraint.append("'%"+constraints.get(i).getValue()+"%'");
                    }
                    else if (constraints.get(i).getOperator().toLowerCase().contains("in")) {
                        constraint.append("(");
                        constraint.append(constraints.get(i).getValue());
                        constraint.append(")");
                    }
                    else {
                        constraint.append("'"+constraints.get(i).getValue()+"'");
                    }
                } else {
                    constraint.append("=");
                    constraint.append("'"+constraints.get(i).getValue()+"'");
                }
            }
        }

        if (keyword != null || !keyword.equalsIgnoreCase(" ")) {
            constraint.append(" AND DEALER_NAME LIKE '%" + keyword.trim() + "%'");
        }

        if (query.getGroup() != null) constraint.append(groupBy)
                .append(query.getGroup());

        statement = String.format(statement, String.valueOf(constraint));
        Log.d(PODelaerDataAccess.class.getSimpleName(), statement);

        Cursor cursor = getDaoSession(context)
                .getDatabase()
                .rawQuery(statement, null);

        try {
            if (cursor.moveToFirst()) {
                do {
                    OptionAnswerBean optionAnswer = new OptionAnswerBean();
                    optionAnswer.setOption_id(String.valueOf(cursor.getInt(cursor.getColumnIndex("ID"))));
                    optionAnswer.setCode(String.valueOf(cursor.getString(cursor.getColumnIndex("DEALER_ID"))));
                    optionAnswer.setValue(String.valueOf(cursor.getString(cursor.getColumnIndex("DEALER_NAME"))));
                    optionAnswer.setLov_group("DEALER_NAME");
                    answerBeans.add(optionAnswer);
                } while (cursor.moveToNext());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            cursor.close();
        }

        return answerBeans;
    }

    public static Lookup getOneAsLookup(Context context, QuestionBean qBean, long id) {
        QueryBuilder<PODealer> qb = getPoDealer(context).queryBuilder();
        qb.where(PODealerDao.Properties.Id.eq(id));
        qb.limit(1).build();

        Lookup lookup = null;
        int size = qb.list().size();
        if (size > 0) {
            PODealer dealer = qb.list().get(0);
            lookup = new Lookup(String.valueOf(dealer.getId()));
            lookup.setCode(dealer.getDealer_id());
            lookup.setValue(dealer.getDealer_name());
            lookup.setOption_id(String.valueOf(dealer.getId()));
            lookup.setUuid_lookup(String.valueOf(dealer.getId()));
        }

        return lookup;
    }

    public static PODealer getLast(Context context) {
        QueryBuilder<PODealer> qb = getPoDealer(context).queryBuilder();
        qb.orderDesc(PODealerDao.Properties.Dtm_upd);
        qb.limit(1);
        qb.build();

        if (!qb.list().isEmpty()) return qb.list().get(0);
        return null;
    }

    public static void addOrUpdateAll(Context context, List<PODealer> transaction) {
        getPoDealer(context).insertOrReplaceInTx(transaction);
        getDaoSession(context).clear();
    }
}
