package com.adins.mss.base.liveness;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Context;
import android.os.AsyncTask;
import android.widget.Toast;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.base.util.Utility;
import com.adins.mss.constant.Global;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.questiongenerator.QuestionBean;
import com.google.firebase.perf.FirebasePerformance;
import com.google.firebase.perf.metrics.HttpMetric;

import java.lang.ref.WeakReference;

public class SubmitImageLiveness extends AsyncTask<String, Void, String> {

    private ProgressDialog progressDialog;
    private WeakReference<Activity> activity;
    private Context context;
    private String errMessage;
    private QuestionBean questionBean;

    public SubmitImageLiveness(Activity activity, Context context, QuestionBean questionBean) {
        this.activity = new WeakReference<>(activity);
        this.context = context;
        this.questionBean = questionBean;
    }

    @Override
    protected void onPreExecute() {
        super.onPreExecute();

        progressDialog = ProgressDialog.show(activity.get(), "", activity.get().getString(R.string.generate_image_data), true);
    }

    @Override
    protected String doInBackground(String... params) {
        String imgBase64 = params[0];
        if (Tool.isInternetconnected(activity.get())) {
            JsonRequestImageLiveness jsonRequestImageLiveness = new JsonRequestImageLiveness();
            jsonRequestImageLiveness.setAudit(GlobalData.getSharedGlobalData().getAuditData());
            jsonRequestImageLiveness.setImgIdentity(imgBase64);
            //TODO: complete request params

            String jsonRequest = GsonHelper.toJson(jsonRequestImageLiveness);

            //TODO: Change URL
            String url = "";
            boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
            boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
            HttpCryptedConnection httpConn = new HttpCryptedConnection(activity.get(), encrypt, decrypt);
            HttpConnectionResult serverResult = null;

            //Firebase Performance Trace HTTP Request
            HttpMetric networkMetric =
                    FirebasePerformance.getInstance().newHttpMetric(url, FirebasePerformance.HttpMethod.POST);
            Utility.metricStart(networkMetric, jsonRequest);

            try {
                serverResult = httpConn.requestToServer(url, jsonRequest, Global.DEFAULTCONNECTIONTIMEOUT);
            } catch (Exception e){
                FireCrash.log(e);
                questionBean.setImgAnswer(null);
                questionBean.setAnswer(null);
                errMessage = activity.get().getString(R.string.msgConnectionFailed);
                return null;
            }

            if (serverResult != null && serverResult.isOK()) {
                return serverResult.getResult();
            } else {
                questionBean.setImgAnswer(null);
                questionBean.setAnswer(null);
                if (serverResult != null) {
                    errMessage = serverResult.getResult();
                }
            }

        } else {
            questionBean.setImgAnswer(null);
            questionBean.setAnswer(null);
            errMessage = activity.get().getString(R.string.no_internet_connection);
        }

        return null;
    }

    @Override
    protected void onPostExecute(String s) {
        super.onPostExecute(s);

        if(errMessage==null) {
            //TODO: Cast s to response class
            ResponseImageLiveness reponse = GsonHelper.fromJson(s, ResponseImageLiveness.class);

            if(reponse.getStatus().getCode() == 0){
                //TODO: Do something with response
            }
        } else{
            Toast.makeText(context,errMessage, Toast.LENGTH_SHORT).show();
        }
        progressDialog.dismiss();
    }
}
