package com.adins.mss.base.tasklog;

import com.adins.mss.foundation.http.MssResponseType;
import com.google.gson.annotations.SerializedName;

import java.util.List;

public class JsonResponseApplicationStatus extends MssResponseType {

    @SerializedName("listTaskStatus")
    List<JsonRequestApplicationStatus.TaskStatus> listTaskStatus;

    public List<JsonRequestApplicationStatus.TaskStatus> getListTaskStatus() {
        return listTaskStatus;
    }

    public void setListTaskStatus(List<JsonRequestApplicationStatus.TaskStatus> listTaskStatus) {
        this.listTaskStatus = listTaskStatus;
    }
}
