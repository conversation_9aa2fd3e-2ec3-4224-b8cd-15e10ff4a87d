package com.adins.mss.foundation.http;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

public class KeyValue implements Serializable {
    @SerializedName("key")
    protected String key;
    @SerializedName("value")
    protected String value;

    //Glen add constructor
    public KeyValue(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
