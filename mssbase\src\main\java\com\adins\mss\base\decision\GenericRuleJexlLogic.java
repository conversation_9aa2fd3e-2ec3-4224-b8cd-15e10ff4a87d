package com.adins.mss.base.decision;

import android.content.Context;
import android.os.Environment;
import android.util.Log;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.commons.Helper;
import com.adins.mss.base.dynamicform.Constant;
import com.adins.mss.base.dynamicform.form.FragmentQuestion;
import com.adins.mss.base.dynamicform.form.questions.QuestionsValidator;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.Decision;
import com.adins.mss.dao.Rule;
import com.adins.mss.foundation.camerainapp.helper.Logger;
import com.adins.mss.foundation.db.dataaccess.DecisionDataAccess;
import com.adins.mss.foundation.db.dataaccess.RuleDataAccess;
import com.adins.mss.foundation.formatter.DateFormatter;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.questiongenerator.QuestionBean;
import com.google.gson.Gson;

import org.apache.commons.jexl2.Expression;
import org.apache.commons.jexl2.JexlContext;
import org.apache.commons.jexl2.JexlEngine;
import org.apache.commons.jexl2.JexlException;
import org.apache.commons.jexl2.MapContext;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.EncryptedDocumentException;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class GenericRuleJexlLogic implements RuleLogic {
	private static final Gson GSON = new Gson();
	private static final Helper helper= Helper.getInstance();
	public static String templateRule = "{\"table\":\"MS_PO\",\"reffIdPO\":\"[SVY_PO]\",\"file\":\"SCORING\",\"keyvalues\":[{\"id\":\"scId\",\"type\":\"java.lang.String\",\"value\":\"MS_PO.SC_ID\"},{\"id\":\"AgrmntAssetRegistration.MrOwnerRelationship\",\"type\":\"java.lang.String\",\"value\":\"{SVY_STTS_TMP_TINGGAL}\"},{\"id\":\"App.DSR\",\"type\":\"java.math.BigDecimal\",\"value\":\"{SVY_IIR}\"},{\"id\":\"AppX.Ltv\",\"type\":\"java.math.BigDecimal\",\"value\":\"{SVY_LTV}\"},{\"id\":\"Cust.RefCustModel.CustModelCode\",\"type\":\"java.lang.String\",\"value\":\"{ODR_NB_PC}\"},{\"id\":\"AssetHierarchyL1\",\"type\":\"java.lang.String\",\"value\":\"{SVY_BRAND}\"},{\"id\":\"AssetHierarchyL2\",\"type\":\"java.lang.String\",\"value\":\"{SVY_MODEL}\"},{\"id\":\"StayLength\",\"type\":\"java.math.BigDecimal\",\"value\":\"{SVY_LAMA_MENEMPATI}\"},{\"id\":\"LengthOfWork\",\"type\":\"java.math.BigDecimal\",\"value\":\"{SVY_PMHN_LAMA_BKRJA}\"},{\"id\":\"CustPersonal.MrEducation\",\"type\":\"java.lang.String\",\"value\":\"{SVY_PEND_PEMOHON}\"},{\"id\":\"CustPersonal.MrMaritalStat\",\"type\":\"java.lang.String\",\"value\":\"{SVY_STTS_PERNIKAHAN}\"},{\"id\":\"CustPersonal.MrGender\",\"type\":\"java.lang.String\",\"value\":\"{ref_id}\"},{\"id\":\"ResidenceStatus\",\"type\":\"java.lang.String\",\"value\":\"{SVY_STTS_TMP_TINGGAL}\"},{\"id\":\"RefProfession.ProfessionCode\",\"type\":\"java.lang.String\",\"value\":\"{SVY_JOB_KTGRY_PEMHON}\"},{\"id\":\"App.DownPaymentGrossAmt\",\"type\":\"java.math.BigDecimal\",\"value\":\"{SVY_DP_GROSS}\"},{\"id\":\"App.TotalAssetPrice\",\"type\":\"java.math.BigDecimal\",\"value\":\"{SVY_OTR}\"},{\"id\":\"App.Tenor\",\"type\":\"java.lang.Integer\",\"value\":\"{SVY_TENOR}\"},{\"id\":\"Age\",\"type\":\"java.lang.Integer\",\"value\":\"$AGE(SVY_TGL_LHR_PEMOHON)\"},{\"id\":\"CustPersonal.NoOfDependents\",\"type\":\"java.lang.Integer\",\"value\":\"{SVY_JMLH_TANGGUNGAN}\"},{\"id\":\"Income\",\"type\":\"java.math.BigDecimal\",\"value\":\"{SVY_PMHN_GAJI_POKOK}\"},{\"id\":\"RefOffice.OfficeCode\",\"type\":\"java.lang.String\",\"value\":\"$BRANCH_ID\"}]}";

	/*
	 * header0 => | CONDITION   | ACTION
	 * header1 => |             | objectName
	 * header2 => | var == @val | field/methodname
	 * data       | val1        | val2
	 */
	private static final int HEADER1_OFFSET = 1;
	private static final int HEADER2_OFFSET = 2;
	private static final int DATA_OFFSET 	= 3;

	public static final String SP_FUNC_LIKE			= "like";
	public static final String SP_OBJECT_METHOD		= "addSheetName()";

	private String skipEvaluationValue 	= "-";
	private String dataVarName			= "val";
	private String spObjectName			= "this";
	private boolean debug				= false;


	public String getSkipEvaluationValue() {
		return skipEvaluationValue;
	}

	public void setSkipEvaluationValue(String skipEvaluationValue) {
		this.skipEvaluationValue = skipEvaluationValue;
	}

	public String getDataVarName() {
		return dataVarName;
	}

	public void setDataVarName(String dataVarName) {
		this.dataVarName = dataVarName;
	}

	public String getSpObjectName() {
		return spObjectName;
	}

	public void setSpObjectName(String spObjectName) {
		this.spObjectName = spObjectName;
	}

	public boolean isDebug() {
		return debug;
	}

	public void setDebug(boolean debug) {
		this.debug = debug;
	}

	//-------------------------------------------------------------------------

	@Override
	public void executeRule(File file, Map<String, Object> dataObjects) {
		Workbook workbook = this.initWorkbook(file);
		this.doExecuteRule(workbook, dataObjects);
	}

	@Override
	public void executeRule(InputStream is, Map<String, Object> dataObjects){
		Workbook workbook = this.initWorkbook(is);
		this.doExecuteRule(workbook, dataObjects);
	}


	@Override
	public void executeRule(File file, String sheetName, Map<String, Object> dataObjects) {
		Workbook workbook = this.initWorkbook(file);
		this.doExecuteRule(workbook, sheetName, dataObjects);
	}

	@Override
	public void executeRule(InputStream is, String sheetName, Map<String, Object> dataObjects) {
		Workbook workbook = this.initWorkbook(is);
		this.doExecuteRule(workbook, sheetName, dataObjects);
	}

	@Override
	public void executeRule(String ruleCode, Map<String, Object> dataObjects) {
		//
	}

	@Override
	public void executeRule(String ruleCode, String sheetName, Map<String, Object> dataObjects) {
		//
	}

	@Override
	public float calculateScore(Context context, RuleParam ruleParam) {
		File file  = null;
		if (Constant.productOff != null) {
			Rule rule = RuleDataAccess.find(context, Constant.productOff.getRule_scoring());
			//TODO: Fix this
			String filePath = context.getFilesDir().getPath() + "/" + helper
					.getNameFromUrl(rule.getUrl().toLowerCase());
//			String filePath = context.getFilesDir().getPath() + "/sc_real.xls";

			if (helper.isFileExist(filePath)) {
				file = new File(filePath);
			}
		}

		if (file != null) {
			ResultScoring result = new ResultScoring();
			ScoreCard sc = new ScoreCard();

			Map<String, Object> dataObjects = mapObjects(ruleParam);
			dataObjects.put("sc", sc);
			dataObjects.put("resultD", result);
			executeRule(file, dataObjects);

			float finalScore = 0;
			int itr = 0;
			for (String sheetName : sc.getListSheet()) {
				dataObjects.put("resultD", new ResultScoring());
				Float weight = sc.getListWeight().get(itr);
				executeRule(file, sheetName, dataObjects);
				result = (ResultScoring) dataObjects.get("resultD");
				float score = (weight * result.getScore()) / 100;
				finalScore += score;
				Logger.i("INFO", "-----------------------------------");
				Logger.i("INFO", String.valueOf(weight));
				Logger.i("INFO", sheetName);
				Logger.i("INFO", String.valueOf(result.getScore()));
				Logger.i("INFO", String.valueOf(score));
				Logger.i("INFO", String.valueOf(finalScore));
				Logger.i("INFO", "-----------------------------------");
				itr += 1;
			}

			return finalScore;
//			try {
//
//			} catch (Exception ex) {
//				ex.printStackTrace();
//			}
		}

		return 0;
	}

	@Override
	public String scoreMatrix(Context context, Double score) {
		if (Constant.productOff != null) {
			Rule rule = RuleDataAccess.find(context, Constant.productOff.getRule_matrix());
			//TODO: Fix this
//			String filePath = context.getFilesDir().getPath() + "/" + helper
//					.getNameFromUrl(rule.getUrl().toLowerCase());

			if (rule != null) {
				String filePath = context.getFilesDir().getPath()
						.concat("/")
						.concat(helper.getNameFromUrl(rule.getUrl().toLowerCase()));

				if (helper.isFileExist(filePath)) {
					File ruleFile = new File(filePath);

					try {
						ResultScoring result = new ResultScoring();
						Map<String, Object> dataObjects = new HashMap<>();
						dataObjects.put("CreditScore", score);
						dataObjects.put("Result", result);

						executeRule(ruleFile, dataObjects);
						return result.getMatrix();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
		}

		return "N/A";
	}

	@Override
	public double basePrice(Context context, RuleParam ruleParam) {
		PriceResult listPrice = new PriceResult();

		File file  = null;
		if (Constant.productOff != null) {
			Rule rule = RuleDataAccess.find(context, Constant.productOff.getRule_asset_price());
			//Todo: Fix this
			String filePath = context.getFilesDir().getPath() + "/" + helper
					.getNameFromUrl(rule.getUrl().toLowerCase().replace(" ", "%20"));

			if (helper.isFileExist(filePath)) {
				file = new File(filePath);

				try {
					Map<String, Object> dataObjects = mapObjects(ruleParam);
					dataObjects.put("res", listPrice);
//				PriceResult listPrice = new PriceResult();
//				Map<String, Object> dataObjects = new HashMap<>();
//				dataObjects.put("SupplBranch.SupplBranchCode", "003 HONDA");
//				dataObjects.put("AssetHierarchyL1.AssetHierarchyL1Code", "M30");
//				dataObjects.put("AssetHierarchyL2.AssetHierarchyL2Code", "CAR");
//				dataObjects.put("AssetCode", "11");
//				dataObjects.put("ManufacturingYear", "2016");
//				dataObjects.put("AgrmntAsset.MrAssetCondition", "NEW");
//				dataObjects.put("GrpType", "GRP04300");
//				dataObjects.put("OfficeCode", "034");
//				dataObjects.put("res", listPrice);
//				GenericRuleJexlStreamLogic genericRule = new GenericRuleJexlStreamLogic(file, dataObjects);
//				genericRule.execute();
//
//				List<ActionBean> actions = genericRule.getActions();
//				List<String> conditions = genericRule.getConditions();
//				RuleMetadataBean meta = genericRule.getMeta();
					executeRule(file, rule.getRule_name(), dataObjects);
//					executeRuleOtr(context, dataObjects);

					FragmentQuestion.marketPrice 	= listPrice.getBasePrice();
					FragmentQuestion.tolerancePrctg = (double) listPrice.getTolerance();
					return listPrice.getBasePrice();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
//
//		if (file != null) {
//		}
		return 0;
	}

	public double getDepositAmount(Context context, RuleParam ruleParam) {
		RuleDpBean result 	= new RuleDpBean();

		if (Constant.productOff != null) {
			Rule rule = RuleDataAccess.find(context, Constant.productOff.getRule_min_dp());
			//Todo: Fix this
			String filePath = context.getFilesDir().getPath() + "/" + helper
					.getNameFromUrl(rule.getUrl().toLowerCase());

			if (helper.isFileExist(filePath)) {
				File file = new File(filePath);
				Map<String, Object> dataObjects = mapObjects(ruleParam);
				dataObjects.put("res", result);

				try {
					executeRule(file, dataObjects);
					if (dataObjects.get("DPType").equals("AMT")) {
						return result.getGrossDpAmt();
					} else {
						return result.getGrossDpPrctg();
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}

		return 0d;
	}

	public double getDepositPercent(Context context, RuleParam ruleParam, String type) {
		RuleDpBean result 	= new RuleDpBean();

		if (Constant.productOff != null) {
			Rule rule = null;
			if (type.equalsIgnoreCase("DP_PRCNT")) {
				if (Constant.productOff.getRule_min_dp() == null)
					return 0d;

				rule = RuleDataAccess.find(context, Constant.productOff.getRule_min_dp());
			} else {
				if (Constant.productOff.getRule_data_min_tdp() == null)
					return 0d;

				rule = RuleDataAccess.find(context, Constant.productOff.getRule_data_min_tdp());
			}

			if (rule != null) {
				String filePath = context.getFilesDir().getPath() + "/" + helper
						.getNameFromUrl(rule.getUrl().toLowerCase());

				if (helper.isFileExist(filePath)) {
					File file = new File(filePath);
					Map<String, Object> dataObjects = mapObjects(ruleParam);
					dataObjects.put("res", result);

					try {
						executeRule(file, dataObjects);
						if (type.equals("TDP")) {
							return result.getGrossDpPrctg() - result.getDevMin();
						}
						return result.getGrossDpPrctg();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
		}

		return 0d;
	}

	public Integer minManfYear(Context context, RuleParam ruleParam) {
		RuleManYearBean result = new RuleManYearBean();

		if (Constant.productOff != null && Constant.productOff.getRule_data_manf_year() != null) {
			Rule rule = RuleDataAccess.find(context, Constant.productOff.getRule_data_manf_year());

			if (rule != null) {
				//Todo: Fix this
				String filePath = context.getFilesDir().getPath() + "/" + helper.getNameFromUrl(rule.getUrl().toLowerCase());

				if (helper.isFileExist(filePath)) {
					File file = new File(filePath);
					Map<String, Object> dataObjects = mapObjects(ruleParam);
					dataObjects.put("res", result);

					try {
						executeRule(file, rule.getRule_name(), dataObjects);
						return result.getMinManufYear();
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
		}

		return 0;
	}

	@Override
	public boolean validateDP(Context context, QuestionBean bean, RuleParam ruleParam) {
//		RuleLogic logic = new GenericRuleJexlLogic();
		QuestionsValidator.Result result   = new QuestionsValidator.Result();

		File file  = new File(Environment.getExternalStorageDirectory() + "/" + "assetDP.xlsx");
		if (Constant.productOff != null) {
			Rule rule = RuleDataAccess.find(context, Constant.productOff.getRule_min_dp());
			//Todo: Fix this
			String filePath = context.getFilesDir().getPath() + "/" + helper
					.getNameFromUrl(rule.getUrl().toLowerCase());

			if (helper.isFileExist(filePath)) {
				file = new File(filePath);
			}
		}

		Map<String, Object> dataObjects = mapObjects(ruleParam);
//		dataObjects.put("AssetCondition", "NEW");
//		dataObjects.put("ManufacturingYear", 2015);
//		dataObjects.put("Tenor", 32);
//		dataObjects.put("AssetCategory.AssetCategoryCode", "3MBL");
		dataObjects.put("res", result);
		executeRule(file, dataObjects);

		int x = result.getMinDpPrcntg();
		int y = result.getDevMin();
		double r = Double.parseDouble(bean.getAnswer());
		return (r > (x-y));
	}

	@Override
	public List<String> validateTC(Context context, RuleParam ruleParam) {
		RuleLogic logic = new GenericRuleJexlLogic();

		File file  = null;
		if (Constant.productOff != null && Constant.productOff.getRule_app_tc() != null) {
			Rule rule = RuleDataAccess.find(context, Constant.productOff.getRule_app_tc());
			//Todo: Fix This
			if (rule != null) {
				String filePath = context.getFilesDir().getPath() + "/" + helper
						.getNameFromUrl(rule.getUrl().toLowerCase());

				if (helper.isFileExist(filePath)) {
					file = new File(filePath);
				}

				TcBean res = new TcBean();
				Map<String, Object> dataObjects = mapObjects(ruleParam);
				dataObjects.put("res", res);

				if (file != null) {
					logic.executeRule(file, dataObjects);
					return res.getListMandatory();
				}
			}
		}

		return null;
	}

	public HashMap<String, Object> mapObjects(RuleParam ruleParam) {
		HashMap<String, Object> dataObjects = new HashMap<>();

		/**
		 * Parsing Value for RuleParameter
		 * return RuleParam
		 */
		List<Keyvalue> keyvalues = ruleParam.getKeyvalues();
		for (Keyvalue kv : keyvalues) {
			String fmtValue 	= kv.getValue();

//			int idxOfOpenBrace 	= fmtValue.indexOf('{');
			int idxOfOpenBrace 	= fmtValue.indexOf('[');
			if (idxOfOpenBrace != -1)
			{
//				int idxOfCloseBrace 	= fmtValue.indexOf('}');
				int idxOfCloseBrace 	= fmtValue.indexOf(']');
				String tempIdentifier 	= fmtValue.substring(idxOfOpenBrace + 1, idxOfCloseBrace).toUpperCase();
				if (Constant.listOfQuestion.containsKey(tempIdentifier)) {
					QuestionBean bean 	= Constant.listOfQuestion.get(tempIdentifier);
					bean.setRelevanted(true);
//					if (bean.getSelectedOptionAnswers() != null && !QuestionViewAdapter.IsTextQuestion(bean.getAnswer_type())) {
					if (Tool.isOptions(bean.getAnswer_type()) || bean.getAnswer_type().equalsIgnoreCase(Global.AT_TEXT_WITH_SUGGESTION)) {
						kv.setValue(bean.getSelectedOptionAnswers().get(0)
								.getCode());
					} else {
						kv.setValue(QuestionBean.getAnswer(bean));
					}
				} else {
					if (kv.getType().contains("Integer") || kv.getType().contains("BigDecimal")) kv.setValue("0");
					else kv.setValue("-");
				}
			}
			else if (fmtValue.contains("MS_PO")) {
				String[] strings = Tool.split(fmtValue, ".");
				String idf = strings[1];

				switch (idf) {
					case "BRAND_CODE":
						kv.setValue(Constant.poAsset.getBrand_code());
						break;
					case "MODEL_CODE":
						kv.setValue(Constant.poAsset.getModel_code());
						break;
					case "MASTER_CODE":
						kv.setValue(Constant.poAsset.getMaster_code());
						break;
					case "SC_ID":
						kv.setValue(Constant.productOff.getSc_id());
						break;
					case "GROUP_TYPE":
						kv.setValue(Constant.poAsset.getGroup_type());
						break;
					default:
						kv.setValue("-");
						break;
				}
			}
			else {
				int idxOfOpenAbs = fmtValue.indexOf("$");
				String finalIdentifier = fmtValue.substring(idxOfOpenAbs + 1);

				switch (finalIdentifier) {
					case Global.IDF_LOGIN_ID:
						String loginId  = GlobalData.getSharedGlobalData().getUser().getLogin_id();
						int idxOfOpenAt = loginId.indexOf('@');
						if (idxOfOpenAt != -1) {
							loginId = loginId.substring(0, idxOfOpenAt);
						}
						kv.setValue(loginId);
						break;
					case Global.IDF_BRANCH_ID:
						String branchId = GlobalData.getSharedGlobalData().getUser().getBranch_id();
						kv.setValue(branchId);
						break;
					case Global.IDF_BRANCH_NAME:
						String branchName = GlobalData.getSharedGlobalData().getUser().getBranch_name();
						kv.setValue(branchName);
						break;
					case Global.IDF_UUID_USER:
						String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
						kv.setValue(uuidUser);
						break;
					case Global.IDF_JOB:
						String job = GlobalData.getSharedGlobalData().getUser().getFlag_job();
						kv.setValue(job);
						break;
					case Global.IDF_DEALER_NAME:
						String dealerName = GlobalData.getSharedGlobalData().getUser().getDealer_name();
						kv.setValue(dealerName);
						break;
					case Global.IDF_UUID_BRANCH:
						String uuidBranch = GlobalData.getSharedGlobalData().getUser().getUuid_branch();
						kv.setValue(uuidBranch);
						break;
					case Global.IDF_DEALER_ID:
						String dealerId = GlobalData.getSharedGlobalData().getUser().getUuid_dealer();
						kv.setValue(dealerId);
						break;
					case Global.IDF_BRANCH_TYPE:
						String branchType = GlobalData.getSharedGlobalData().getUser().getBranch_type();
						kv.setValue(branchType);
						break;
					default:
						if (finalIdentifier.contains("AGE")) {
							int idxOfOpen = finalIdentifier.indexOf('(');
							String value  = "";
							if (idxOfOpen != -1) {
								int idxofClose 	  = finalIdentifier.indexOf(')');
								String identifier = finalIdentifier.substring(idxOfOpen + 1, idxofClose);
								QuestionBean qBean= Constant.listOfQuestion.get(identifier);
								qBean.setRelevanted(true);
								value = QuestionBean.getAnswer(qBean);

								DateFormatter dateFormatter = new DateFormatter();
								int age = dateFormatter.age(value);
								kv.setValue(String.valueOf(age));
							}
						}
						break;
				}
			}
		}

		for (Keyvalue k : keyvalues) {
			if (k.getType().contains("Integer")) {
				dataObjects.put(k.getId(), 0);
				if (k.getValue() != null) {
					dataObjects.put(k.getId(), Integer.parseInt(k.getValue()));
				}
			} else if (k.getType().contains("BigDecimal")) {
				if (k.getValue() == null || k.getValue().equalsIgnoreCase("-")) {
					dataObjects.put(k.getId(), new BigDecimal(0));
				} else {
					Double value = Double.parseDouble(k.getValue());
					dataObjects.put(k.getId(), BigDecimal.valueOf(value));
				}
			} else {
				dataObjects.put(k.getId(), k.getValue());
			}
		}

		for (String key : dataObjects.keySet()) {
			Logger.i("INFO", key + ": " + dataObjects.get(key));
		}

		return dataObjects;
	}

	private Workbook initWorkbook(File file) {
		try {
			return WorkbookFactory.create(file);
		} catch (Exception e) {
			Log.e(e.getMessage(), e.getMessage());
			return initWorkbookUseInputstream(file);
		}
	}

	private Workbook initWorkbookUseInputstream(File file) {
		try {
			InputStream inputStream = new FileInputStream(file);
			return WorkbookFactory.create(inputStream);
		} catch (Exception e) {
			Log.e(e.getMessage(), e.getMessage());
		}
		throw new RuntimeException();
	}

	private Workbook initWorkbook(InputStream is) {
		try {
			return WorkbookFactory.create(is);
		} catch (EncryptedDocumentException | InvalidFormatException | IOException e) {
			Log.e(e.getMessage(), e.getMessage());

		}
		throw new RuntimeException();
	}

	public void doExecuteRule(Workbook workbook, Map<String, Object> dataObjects){
		Sheet sheet = workbook.getSheetAt(0);
		try {
			this.doExecuteRule(sheet, dataObjects);
		}
		finally {
			try {
				workbook.close();
			} catch (IOException e) {
				Log.e(e.getMessage(), e.getMessage());
			}
		}
	}

	public void doExecuteRule(Workbook workbook, String sheetName, Map<String, Object> dataObjects) {
		Sheet sheet = workbook.getSheet(sheetName);
		if (sheet == null) {
			Log.e("no file","no file");
		}
		this.doExecuteRule(sheet, dataObjects);
	}

	public void doExecuteRule(Sheet sheet, Map<String, Object> dataObjects){
		RuleMetadataBean metadata = this.initMetadata(sheet);
		List<String> conditions = this.initConditions(sheet, metadata.getRow(),
				metadata.getConditionStart(), metadata.getConditionEnd());
		List<ActionBean> actions = this.initActions(sheet, metadata.getRow(),
				metadata.getActionStart(), metadata.getActionEnd());

		this.evaluateExecute(sheet, metadata, conditions, actions, dataObjects);
	}

	private RuleMetadataBean initMetadata(Sheet sheet) {

		RuleMetadataBean meta = new RuleMetadataBean();
		for (Row row : sheet) {
			Cell cell0 = row.getCell(0, Row.RETURN_NULL_AND_BLANK);
			if (cell0 == null)
				continue;

			String cell0str = cell0.getStringCellValue();
			if (VAR_SKIP_ON_FIRST_APPLIED.equals(cell0str)) {
				Cell skipOnFirstCell = row.getCell(1);
				Object skipOnFirstVal = this.getCellValue(skipOnFirstCell);
				if (skipOnFirstVal instanceof Boolean) {
					meta.setSkipOnFirstAppliedRule(((Boolean) skipOnFirstVal).booleanValue());
				}
				else if (skipOnFirstVal instanceof Number) {
					int intVal = ((Number) skipOnFirstVal).intValue();
					boolean booVal = intVal == 1;
					meta.setSkipOnFirstAppliedRule(booVal);
				}
				else if (skipOnFirstVal instanceof String) {
					Boolean booVal = RuleUtils.toBooleanObject((String) skipOnFirstVal);
					if (booVal == null) {
						booVal = Boolean.FALSE;
					}
					meta.setSkipOnFirstAppliedRule(booVal.booleanValue());
				}
				else if (skipOnFirstVal == null) {
					meta.setSkipOnFirstAppliedRule(false);
				}
			}
			else if (CONDITION_STRING.equals(cell0str)) {
				meta.setRow(row.getRowNum());

				for (Cell cell : row) {
					String cellTxt = cell.getStringCellValue();
					if (CONDITION_STRING.equals(cellTxt)) {
						if (meta.getConditionStart() == -1) {
							meta.setConditionStart(cell.getColumnIndex());
						}
						meta.setConditionEnd(cell.getColumnIndex());
					}
					else if (ACTION_STRING.equals(cellTxt)) {
						if (meta.getActionStart() == -1) {
							meta.setActionStart(cell.getColumnIndex());
						}
						meta.setActionEnd(cell.getColumnIndex());
					}
				}

				if (meta.getActionStart() != -1) {
					return meta;

				}
			}
		}

		throw new RuntimeException();
	}

	private List<String> initConditions(Sheet sheet, int rowHeader0, int colStart, int colEnd) {
		Row header2 = sheet.getRow(rowHeader0 + HEADER2_OFFSET);

		List<String> result = new ArrayList<>(colEnd - colStart +1);

		for (int i = colStart; i <= colEnd; i++) {
			Cell cellH2 = header2.getCell(i);
			String eval = cellH2.getStringCellValue();
			eval = StringUtils.remove(eval, "#");
			eval = StringUtils.remove(eval, "@");

			if (StringUtils.contains(eval, "<>")) {
				eval = StringUtils.replace(eval, "<>", "!=");
			}
			result.add(eval);
		}

		return result;
	}

	private List<ActionBean> initActions(Sheet sheet, int rowHeader0, int colStart, int colEnd) {
		Row header1 = sheet.getRow(rowHeader0 + HEADER1_OFFSET);
		Row header2 = sheet.getRow(rowHeader0 + HEADER2_OFFSET);

		List<ActionBean> result = new ArrayList<>(colEnd - colStart + 1);

		for (int i = colStart; i <= colEnd; i++) {
			Cell cellH1 = header1.getCell(i);
			Cell cellH2 = header2.getCell(i);
			String objName = cellH1.getStringCellValue();
			objName = StringUtils.remove(objName, "#");
			String objFieldMethod = cellH2.getStringCellValue();
			ActionBean actionBean = new ActionBean(objName, objFieldMethod);
			result.add(actionBean);
		}

		return result;
	}

	public void evaluateExecute(Sheet sheet, RuleMetadataBean meta,
								List<String> conditions, List<ActionBean> actions, Map<String, Object> dataObjects){
		JexlEngine jexl = new JexlEngine();
		JexlContext jexlContext = new MapContext();

		//put all dataObjects variables to evaluationContext
		for (Iterator<String> iterator = dataObjects.keySet().iterator(); iterator.hasNext();) {
			String varName = iterator.next();
			jexlContext.set(varName, dataObjects.get(varName));
		}

		int numOfConditions = meta.getConditionEnd() - meta.getConditionStart() + 1;
		Object[] tempConditionVal = new Object[numOfConditions];
		loopData:
		for (int rowNum = (meta.getRow() + DATA_OFFSET); rowNum <= sheet.getLastRowNum(); rowNum++) {
			Row row = sheet.getRow(rowNum);
			if (row == null) continue loopData;

			loopConditionsEval:
			for (int colNum = meta.getConditionStart(), i=0; colNum <= meta.getConditionEnd(); colNum++, i++) {
				Cell conditionCell = row.getCell(colNum);
				Object val = null;

				//set previous row answer when row is empty
				if (conditionCell == null && tempConditionVal[i] != null) {
					val = tempConditionVal[i];
				}
				else {
					val = this.getCellValue(conditionCell);
					val = (val == null) ? "" : val;
					if (StringUtils.isBlank(val.toString())) {

						val = tempConditionVal[i];
					}
				}

				jexlContext.set(dataVarName, val);
				boolean evalResult = false;

				tempConditionVal[i] = val;
				if (StringUtils.equals(skipEvaluationValue, val.toString()))
					continue loopConditionsEval;

				String condition = conditions.get(i);
				Expression expression = null;
				if (condition.startsWith("like(") && condition.endsWith(")") && StringUtils.contains(condition, ",")) {
					condition = StringUtils.remove(condition, "like(");
					condition = StringUtils.remove(condition, ")");
					String[] varCondition = StringUtils.split(condition, ",");
					CharSequence comparison = (CharSequence) jexlContext.get(varCondition[0]);
					CharSequence criteria = (CharSequence) val;
					boolean boolLike = UserDefinedFunction.like(comparison, criteria);

					if (!boolLike)
						continue loopData;
				} else {
					expression = jexl.createExpression(conditions.get(i));

					try {
						evalResult = (boolean) expression.evaluate(jexlContext);
					} catch (JexlException e) {
						throw new RuntimeException();
					}

					if (!evalResult)
						continue loopData;
				}
			}

			loopActions:
			for (int colNum = meta.getActionStart(), i=0; colNum <= meta.getActionEnd(); colNum++, i++) {
				Cell actionCell = row.getCell(colNum);
				Object val = this.getCellValue(actionCell);
				val = (val == null) ? "" : val;
				ActionBean actionBean = actions.get(i);

				String expression = RuleUtils.createExpression(actionBean.getObjName(),
						actionBean.getObjFieldMethod(), val.toString());

				if (StringUtils.contains(val.toString(), "~")) {
					Object keyValue = StringUtils.remove(val.toString(), "~");
					keyValue = dataObjects.get(keyValue);
					keyValue = (keyValue == null) ? "" : keyValue;
					if (StringUtils.isNotBlank(keyValue.toString())) {
						expression = expression.replace(val.toString(), keyValue.toString());
					}
				}

				if (spObjectName.equals(actionBean.getObjName()) &&
						SP_OBJECT_METHOD.equalsIgnoreCase(actionBean.getObjFieldMethod())) {
					this.doExecuteRule(sheet.getWorkbook(), (String) val, dataObjects);

					continue loopActions;
				} else {
					try {
						Expression exp = jexl.createExpression(expression);
						exp.evaluate(jexlContext);
					} catch (JexlException e) {
						throw new RuntimeException();
					}
				}
			}

			if (meta.isSkipOnFirstAppliedRule()) {
				break loopData;
			}
		}
	}

	private Object getCellValue(Cell cell) {
		if (cell == null)
			return null;

		int cellType = cell.getCellType();
		switch (cellType) {
			case Cell.CELL_TYPE_BOOLEAN:
				return Boolean.valueOf(cell.getBooleanCellValue());
			case Cell.CELL_TYPE_ERROR:
				return Byte.valueOf(cell.getErrorCellValue());
			case Cell.CELL_TYPE_NUMERIC:
				Double cellVal = Double.valueOf(cell.getNumericCellValue());
				String strCellVal = String.valueOf(cellVal);
				if (strCellVal.endsWith(".0"))
					return (int) cell.getNumericCellValue();
				else
					return Double.valueOf(cell.getNumericCellValue());
			case Cell.CELL_TYPE_STRING:
				return cell.getStringCellValue();
			case Cell.CELL_TYPE_FORMULA:
				return cell.getCellFormula();
			case Cell.CELL_TYPE_BLANK:
			default:
				return "";
		}
	}

	public void executeRuleOtr(Context context, Map<String, Object> dataObjects) {
		//get row condition where contains ==
		// Query : select COLUMN_NO, VALUE from TMP_RULE where CODE = 'CONDITION' and VALUE like '%==%'
		List<Decision> listConditionEq = DecisionDataAccess.getDecision(context);
		//START DUMMY dummyListCondition
//		listConditionEq.add(new Decision(1, "SupplBranch.SupplBranchCode == @val"));
//		listConditionEq.add(new Decision(2, "AssetHierarchyL1.AssetHierarchyL1Code == @val"));
//		listConditionEq.add(new Decision(3, "AssetHierarchyL2.AssetHierarchyL2Code == @val"));
//		listConditionEq.add(new Decision(4, "AssetCode == @val"));
//		listConditionEq.add(new Decision(7, "AgrmntAsset.MrAssetCondition == @val"));
//		listConditionEq.add(new Decision(8, "GrpType == @val"));
//		listConditionEq.add(new Decision(9, "OfficeCode == @val"));
		//END DUMMY dummyListCondition

		if (listConditionEq != null) {
            for (Decision dec : listConditionEq){
                dec.setValue(StringUtils.remove(dec.getValue(), " "));
                dec.setValue(StringUtils.remove(dec.getValue(), "#"));
                dec.setValue(StringUtils.remove(dec.getValue(), "@"));
                dec.setValue(StringUtils.remove(dec.getValue(), "=="));
                dec.setValue(StringUtils.remove(dec.getValue(), dataVarName));
            }

            String strQuery = generateQueryOtr(listConditionEq, dataObjects);
            // QUERY - get list column from dynamic script from queryListValidColumn
            List<Integer> listValidColumnNo = DecisionDataAccess.getRow(context, strQuery);
//            List<Integer> ListValidColumnNo = new ArrayList<>();
////            //START DUMMY dummyListColumnNo
//            ListValidColumnNo.add(65536);
//            ListValidColumnNo.add(65535);
//            ListValidColumnNo.add(21);
            //END DUMMY dummyListColumnNo

            RuleMetadataBean metadata = new RuleMetadataBean();
            //Query Get list CONDITION : select * from TMP_RULE where CODE in ('CONDITION', 'ACTION') ORDER BY COLUMN_NO ASC
//            Object[] codes = {"CONDITION", "ACTION"};
//            List<Decision> listCondAct = DecisionDataAccess.getByCodeIn(context, codes);
            //START DUMMY dummyListColumnNo
			List<Decision> listCondAct = new ArrayList<>();
            listCondAct.add(new Decision(1, "CONDITION", "SupplBranch.SupplBranchCode == @val", 1, 19, null));
            listCondAct.add(new Decision(2, "CONDITION", "AssetHierarchyL1.AssetHierarchyL1Code == @val", 2, 19, null));
            listCondAct.add(new Decision(3, "CONDITION", "AssetHierarchyL2.AssetHierarchyL2Code == @val", 3, 19, null));
            listCondAct.add(new Decision(4, "CONDITION", "AssetCode == @val", 4, 19, null));
            listCondAct.add(new Decision(5, "CONDITION", "ManufacturingYear >= @val", 5, 19, null));
            listCondAct.add(new Decision(6, "CONDITION", "ManufacturingYear <= @val", 6, 19, null));
            listCondAct.add(new Decision(7, "CONDITION", "AgrmntAsset.MrAssetCondition == @val", 7, 19, null));
            listCondAct.add(new Decision(8, "CONDITION", "GrpType == @val", 8, 19, null));
            listCondAct.add(new Decision(9, "CONDITION", "OfficeCode == @val", 9, 19, null));
            listCondAct.add(new Decision(10, "ACTION", "AddListOTRAmt()", 10, 19, "res"));
//            listCondAct.add(new Decision(11, "ACTION", "AddListTolerancePrctg()", 11, 19, "res"));
//			listCondAct.add(new Decision(12, "ACTION", "AddListBehaviour()", 12, 19, "res"));
            //END DUMMY dummyListColumnNo

            List<String> conditions = new ArrayList<>();
            List<ActionBean> actions = new ArrayList<>();

            int itr = 0;
            String code = "";
            for (Decision dec : listCondAct) {
                if (StringUtils.isBlank(code)) {
                    code = dec.getCode();
                    metadata.setConditionStart(dec.getColumn_no());
                } else if (!code.equalsIgnoreCase(dec.getCode())) {
                    code = dec.getCode();
                    metadata.setConditionEnd(dec.getColumn_no()-1);
                    metadata.setActionStart(dec.getColumn_no());
                }

                if ("CONDITION".equals(code)) {
                    String eval = dec.getValue();
                    eval = StringUtils.remove(eval, "#");
                    eval = StringUtils.remove(eval, "@");
                    conditions.add(eval);
                } else if ("ACTION".equals(code)) {
                    ActionBean act = new ActionBean(dec.getObj_name(), dec.getValue());
                    actions.add(act);
                }

                itr += 1;
                if (listCondAct.size() > itr) {
                    metadata.setActionEnd(dec.getColumn_no()+1);
                }
            }

            metadata.setSkipOnFirstAppliedRule(true);
            try {
                evaluateExecuteDb(context, listValidColumnNo, metadata, conditions, actions, dataObjects);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
	}

	private String generateQueryOtr(List<Decision> listDecision, Map<String, Object> dataObjects) {
		StringBuilder sb = new StringBuilder();
		sb.append("select a.* from (");

		int itr = 0;
		for (Decision decision : listDecision) {
			String ans = (String) dataObjects.get(decision.getValue());
			sb.append(" select distinct ROW_NO from MS_TMP_RULE where (CODE = 'VALUE' and COLUMN_NO = '"+decision.getColumn_no()+"' and VALUE = '"+skipEvaluationValue+"') ");

			if (StringUtils.isNotBlank(ans)) {
				sb.append(" OR (CODE = 'VALUE' and COLUMN_NO = '"+decision.getColumn_no()+"' and VALUE = '"+dataObjects.get(decision.getValue())+"') ");
			}

			itr+=1;

			if (listDecision.size() > itr) {
				sb.append(" INTERSECT");
			}
		}

		sb.append(" ) a order by ROW_NO DESC");
		return sb.toString();
	}

	public void evaluateExecuteDb(Context context, List<Integer> listValidColumnNo, RuleMetadataBean meta,
								  List<String> conditions, List<ActionBean> actions, Map<String, Object> dataObjects) throws Exception {
		JexlEngine jexl = new JexlEngine();
		JexlContext jexlContext = new MapContext();

		//put all dataObjects variables to evaluationContext
		for (Iterator<String> iterator = dataObjects.keySet().iterator(); iterator.hasNext();) {
			String varName = iterator.next();
			jexlContext.set(varName, dataObjects.get(varName));
		}
		loopData:
		for (Integer rowNum : listValidColumnNo) {
			//QUERY - Get Model Data.
			//SCRIPT : select * from TMP_RULE where ROW_NO = rowNum
            List<Decision> listFromDB = DecisionDataAccess.getByRowNo(context, rowNum);
//			List<Decision> listFromDB = dummyReturnColumnDB(rowNum);

			int numOfConditions = conditions.size();
			Object[] tempConditionVal = new Object[numOfConditions];

			loopConditionsEval:
			for (int colNum = meta.getConditionStart(), i=0; colNum <= meta.getConditionEnd(); colNum++, i++) {
				Decision decCond = listFromDB.get(i);
				String conditionCell = decCond.getValue();
				Object val = null;


				//set previous row answer when row is empty
				if (conditionCell == null && tempConditionVal[i] != null) {
					val = tempConditionVal[i];
				}
				else {
					val = decCond.getValue();
					val = (val == null) ? "" : val;
					if (StringUtils.isBlank(val.toString())) {
						val = tempConditionVal[i];
					}
				}

				jexlContext.set(dataVarName, val);
				boolean evalResult = false;

				tempConditionVal[i] = val;
				if (StringUtils.equals(skipEvaluationValue, val.toString()))
					continue loopConditionsEval;

				String condition = conditions.get(i);
				Expression expression = null;
				if (condition.startsWith("like(") && condition.endsWith(")") && StringUtils.contains(condition, ",")) {
					condition = StringUtils.remove(condition, "like(");
					condition = StringUtils.remove(condition, ")");
					String[] varCondition = StringUtils.split(condition, ",");
					CharSequence comparison = (CharSequence) jexlContext.get(varCondition[0]);
					CharSequence criteria = (CharSequence) val;
					boolean boolLike = UserDefinedFunction.like(comparison, criteria);

					Logger.i("INFO", "Conditions=["+conditions.get(i)+"] with val=["+val+"] --> ["+evalResult+"]");

					if (!boolLike)
						continue loopData;
				} else {
					expression = jexl.createExpression(conditions.get(i));

					try {
						evalResult = (boolean) expression.evaluate(jexlContext);
//						if (debug) LOG.trace("Conditions=[{}] with val=[{}] --> [{}]",
//								conditions.get(i), val, evalResult);
					} catch (JexlException e) {
//						LOG.error("Error on firing rule, evaluating:[{}], val:[{}]",
//								conditions.get(i), val, e);
						e.printStackTrace();
//						throw new RulesException(e.getMessage(), e, Reason.RULE_EVALUATION_EXCEPTION);
					}

					if (!evalResult)
						continue loopData;
				}
			}

			loopActions:
			for (int colNum = meta.getActionStart(), i=0; colNum <= meta.getActionEnd(); colNum++, i++) {
				Decision decAction = listFromDB.get(colNum-1);
				Object val = decAction.getValue();
				val = (val == null) ? "" : val;
				ActionBean actionBean = actions.get(i);

				String expression = RuleUtils.createExpression(actionBean.getObjName(),
						actionBean.getObjFieldMethod(), val.toString());

				if (StringUtils.contains(val.toString(), "~")) {
					Object keyValue = StringUtils.remove(val.toString(), "~");
					keyValue = dataObjects.get(keyValue);
					keyValue = (keyValue == null) ? "" : keyValue;
					if (StringUtils.isNotBlank(keyValue.toString())) {
						expression = expression.replace(val.toString(), keyValue.toString());
					}
				}

				try {
					Expression exp = jexl.createExpression(expression);
					exp.evaluate(jexlContext);
				} catch (JexlException e) {
//					LOG.error("Error on firing rule, evaluating:[{}], val:[{}]",
//							conditions.get(i), val, e);
					e.printStackTrace();
				}
			}

			if (meta.isSkipOnFirstAppliedRule()) {
//				LOG.info("First rule applied, breaking looping data.");
				Logger.i("INFO", "First rule applied, breaking looping data.");
				break loopData;
			}
		}
	}

	private List<Decision> dummyReturnColumnDB (Integer row) {
		List<Decision> list = new ArrayList<>();
		if (row == 65536) {
			list.add(new Decision(786205, "VALUE", "-", 1, 65536, null));
			list.add(new Decision(786206, "VALUE", "-", 2, 65536, null));
			list.add(new Decision(786207, "VALUE", "-", 3, 65536, null));
			list.add(new Decision(786208, "VALUE", "SYHA01BEA00001", 4, 65536, null));
			list.add(new Decision(786209, "VALUE", "2000", 5, 65536, null));
			list.add(new Decision(786210, "VALUE", "2014", 6, 65536, null));
			list.add(new Decision(786211, "VALUE", "-", 7, 65536, null));
			list.add(new Decision(786212, "VALUE", "-", 8, 65536, null));
			list.add(new Decision(786213, "VALUE", "-", 9, 65536, null));
			list.add(new Decision(786214, "VALUE", "10000011", 10, 65536, null));
			list.add(new Decision(786215, "VALUE", "25", 11, 65536, null));
			list.add(new Decision(786216, "VALUE", "DEF", 12, 65536, null));
		} else if (row == 65535) {
			list.add(new Decision(786193, "VALUE", "-", 1, 65535, null));
			list.add(new Decision(786194, "VALUE", "-", 2, 65535, null));
			list.add(new Decision(786195, "VALUE", "-", 3, 65535, null));
			list.add(new Decision(786196, "VALUE", "SYHA01BEA00001", 4, 65535, null));
			list.add(new Decision(786197, "VALUE", "2015", 5, 65535, null));
			list.add(new Decision(786198, "VALUE", "2018", 6, 65535, null));
			list.add(new Decision(786199, "VALUE", "-", 7, 65535, null));
			list.add(new Decision(786200, "VALUE", "-", 8, 65535, null));
			list.add(new Decision(786201, "VALUE", "-", 9, 65535, null));
			list.add(new Decision(786202, "VALUE", "15000011", 10, 65535, null));
			list.add(new Decision(786203, "VALUE", "25", 11, 65535, null));
			list.add(new Decision(786204, "VALUE", "DEF", 12, 65535, null));
		} else if (row == 21) {
			list.add(new Decision(25, "VALUE", "-", 1, 21, null));
			list.add(new Decision(26, "VALUE", "-", 2, 21, null));
			list.add(new Decision(27, "VALUE", "-", 3, 21, null));
			list.add(new Decision(28, "VALUE", "-", 4, 21, null));
			list.add(new Decision(29, "VALUE", "2015", 5, 21, null));
			list.add(new Decision(30, "VALUE", "2018", 6, 21, null));
			list.add(new Decision(31, "VALUE", "-", 7, 21, null));
			list.add(new Decision(32, "VALUE", "-", 8, 21, null));
			list.add(new Decision(33, "VALUE", "-", 9, 21, null));
			list.add(new Decision(34, "VALUE", "15000000", 10, 21, null));
			list.add(new Decision(35, "VALUE", "20", 11, 21, null));
			list.add(new Decision(36, "VALUE", "DEF", 12, 21, null));
		}
		return list;
	}
}