package com.adins.example;

import android.app.Activity;
import android.os.Bundle;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.User;
import com.adins.mss.foundation.db.dataaccess.UserDataAccess;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.questiongenerator.NotEqualSymbol;
import com.gadberry.utility.expression.Expression;
import com.gadberry.utility.expression.OperatorSet;

import java.util.ArrayList;
import java.util.List;

public class MainActivity extends Activity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        copyDB();

        GlobalData.getSharedGlobalData();
        List<User> users = UserDataAccess.getAll(this);
        User user = new User(Tool.getUUID());
        user.setLogin_id("gigin@demo");
        user.setBranch_id("HO");
        user.setBranch_name("Head Office");
        user.setDealer_name("DLR NM");
        user.setFlag_job("JOB MH");
        GlobalData.getSharedGlobalData().setUser(user);
        boolean isrelevant = testingRelevant();
        testingFilter();

    }


    private boolean testingRelevant() {
        boolean result = false;
        String relevantExpression = "({$LOGIN_ID}==gigin) && ({$FLAG_JOB}==JOBMH)";
        String convertedExpression = new String(relevantExpression);        //make a copy of
        if (convertedExpression == null || convertedExpression.length() == 0) {
            return true;
        } else {

            //TODO, use extractIdentifierFromString next time to simplify
            boolean needReplacing = true;
            while (needReplacing) {

                int idxOfOpenBrace = convertedExpression.indexOf('{');
                if (idxOfOpenBrace != -1) {                    //there's {, prepare to replace what inside the {}
                    int idxOfCloseBrace = convertedExpression.indexOf('}');
                    String identifier = convertedExpression.substring(idxOfOpenBrace + 1, idxOfCloseBrace);
                    int idxOfOpenAbs = identifier.indexOf("$");
                    if (idxOfOpenAbs != -1) {
                        String finalIdentifier = identifier.substring(idxOfOpenAbs + 1);
                        String flatAnswer = "";

                        if (finalIdentifier.equals(Global.IDF_LOGIN_ID)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getLogin_id();
                            int idxOfOpenAt = flatAnswer.indexOf('@');
                            if (idxOfOpenAt != -1) {
                                flatAnswer = flatAnswer.substring(0, idxOfOpenAt);
                            }
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_ID)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_id();
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_NAME)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_name();
                        } else if (finalIdentifier.equals(Global.IDF_UUID_USER)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                        } else if (finalIdentifier.equals(Global.IDF_JOB)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getFlag_job();
                        } else if (finalIdentifier.equals(Global.IDF_DEALER_NAME)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getDealer_name();
                        }


                        if (flatAnswer != null && flatAnswer.length() > 0) {
                            convertedExpression = convertedExpression.replace("{" + identifier + "}", flatAnswer);
                        } else {            //if there's no answer, just hide the question
                            return false;
                        }

                    }
                } else {
                    needReplacing = false;
                }

            }
            try {
                OperatorSet opSet = OperatorSet.getStandardOperatorSet();
                opSet.addOperator("!=", NotEqualSymbol.class);
                Expression exp = new Expression(convertedExpression);
                exp.setOperatorSet(opSet);
                result = exp.evaluate().toBoolean();
                return result;
            } catch (Exception e) {
                FireCrash.log(e);
                e.printStackTrace();
                return false;
            }

        }
    }

    private void copyDB() {
        CopyDb copyDB = new CopyDb(getApplicationContext());
        copyDB.copyTable();
    }

    private void testingFilter() {
        List<String> filters = new ArrayList<String>();
        int constraintAmount = 0;
        String choiceFilter = "{$LOGIN_ID},{$DEALER_NAME}";
        String[] tempfilters = Tool.split(choiceFilter, Global.DELIMETER_DATA3);

        for (String newFilter : tempfilters) {
            int idxOfOpenBrace = newFilter.indexOf('{');
            if (idxOfOpenBrace != -1) {                    //there's {, prepare to replace what inside the {}
                int idxOfCloseBrace = newFilter.indexOf('}');
                String tempIdentifier = newFilter.substring(idxOfOpenBrace + 1, idxOfCloseBrace).toUpperCase();
                int idxOfOpenAbs = tempIdentifier.indexOf("$");
                if (idxOfOpenAbs != -1) {
                    String finalIdentifier = tempIdentifier.substring(idxOfOpenAbs + 1);
                    if (finalIdentifier.equals(Global.IDF_LOGIN_ID)) {
                        String loginId = GlobalData.getSharedGlobalData().getUser().getLogin_id();
                        int idxOfOpenAt = loginId.indexOf('@');
                        if (idxOfOpenAt != -1) {
                            loginId = loginId.substring(0, idxOfOpenAt);
                        }
                        filters.add(loginId);
                    } else if (finalIdentifier.equals(Global.IDF_BRANCH_ID)) {
                        String branchId = GlobalData.getSharedGlobalData().getUser().getBranch_id();
                        filters.add(branchId);
                    } else if (finalIdentifier.equals(Global.IDF_BRANCH_NAME)) {
                        String branchName = GlobalData.getSharedGlobalData().getUser().getBranch_name();
                        filters.add(branchName);
                    } else if (finalIdentifier.equals(Global.IDF_UUID_USER)) {
                        String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                        filters.add(uuidUser);
                    } else if (finalIdentifier.equals(Global.IDF_JOB)) {
                        String job = GlobalData.getSharedGlobalData().getUser().getFlag_job();
                        filters.add(job);
                    } else if (finalIdentifier.equals(Global.IDF_DEALER_NAME)) {
                        String dealerName = GlobalData.getSharedGlobalData().getUser().getDealer_name();
                        filters.add(dealerName);
                    }
                    constraintAmount++;
                }
            }
        }
    }
}
