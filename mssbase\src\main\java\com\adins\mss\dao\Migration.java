package com.adins.mss.dao;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "MS_MIGRATION".
 */
public class Migration {

     @SerializedName("id")
    private long id;
    /** Not-null value. */
     @SerializedName("version")
    private String version;
    /** Not-null value. */
     @SerializedName("description")
    private String description;

    public Migration() {
    }

    public Migration(long id) {
        this.id = id;
    }

    public Migration(long id, String version, String description) {
        this.id = id;
        this.version = version;
        this.description = description;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    /** Not-null value. */
    public String getVersion() {
        return version;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setVersion(String version) {
        this.version = version;
    }

    /** Not-null value. */
    public String getDescription() {
        return description;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setDescription(String description) {
        this.description = description;
    }

}
