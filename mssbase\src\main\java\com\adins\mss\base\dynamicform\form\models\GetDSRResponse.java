package com.adins.mss.base.dynamicform.form.models;

import com.adins.mss.foundation.http.MssResponseType;
import com.google.gson.annotations.SerializedName;

import java.util.HashMap;

public class GetDSRResponse extends MssResponseType {
    @SerializedName("value") private String value;
    @SerializedName("map_readonly") private HashMap<String, Object> mapReadOnly;

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public HashMap<String, Object> getMapReadOnly() {
        return mapReadOnly;
    }

    public void setMapReadOnly(HashMap<String, Object> mapReadOnly) {
        this.mapReadOnly = mapReadOnly;
    }
}

