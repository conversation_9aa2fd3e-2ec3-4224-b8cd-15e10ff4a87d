package com.adins.mss.base.dynamicform.form.models.validationcheck;

import com.adins.mss.foundation.http.MssResponseType;
import com.google.gson.annotations.SerializedName;

import java.util.HashMap;

public class ValidationCheckResponse extends MssResponseType {

    @SerializedName("map_result")
    private HashMap<String, Object> result;

    @SerializedName("is_mandatory")
    private HashMap<String, Object> listQuestionsIsMandatory;

    public HashMap<String, Object> getResult() {
        return result;
    }

    public void setResult(HashMap<String, Object> result) {
        this.result = result;
    }

    public HashMap<String, Object> getListQuestionsIsMandatory() {
        return listQuestionsIsMandatory;
    }

    public void setListQuestionsIsMandatory(HashMap<String, Object> listQuestionsIsMandatory) {
        this.listQuestionsIsMandatory = listQuestionsIsMandatory;
    }

}
