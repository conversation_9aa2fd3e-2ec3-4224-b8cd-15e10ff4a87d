package com.adins.mss.dao;

import java.util.List;
import java.util.ArrayList;
import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.SqlUtils;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;
import de.greenrobot.dao.query.Query;
import de.greenrobot.dao.query.QueryBuilder;

import com.adins.mss.dao.MobileContentH;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TR_MOBILECONTENT_H".
*/
public class MobileContentHDao extends AbstractDao<MobileContentH, String> {

    public static final String TABLENAME = "TR_MOBILECONTENT_H";

    /**
     * Properties of entity MobileContentH.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_mobile_content_h = new Property(0, String.class, "uuid_mobile_content_h", true, "UUID_MOBILE_CONTENT_H");
        public final static Property Content_name = new Property(1, String.class, "content_name", false, "CONTENT_NAME");
        public final static Property Last_update = new Property(2, java.util.Date.class, "last_update", false, "LAST_UPDATE");
        public final static Property Content_description = new Property(3, String.class, "content_description", false, "CONTENT_DESCRIPTION");
        public final static Property Usr_crt = new Property(4, String.class, "usr_crt", false, "USR_CRT");
        public final static Property Dtm_crt = new Property(5, java.util.Date.class, "dtm_crt", false, "DTM_CRT");
        public final static Property Usr_upd = new Property(6, String.class, "usr_upd", false, "USR_UPD");
        public final static Property Dtm_upd = new Property(7, java.util.Date.class, "dtm_upd", false, "DTM_UPD");
        public final static Property Uuid_user = new Property(8, String.class, "uuid_user", false, "UUID_USER");
        public final static Property Uuid_parent_content = new Property(9, String.class, "uuid_parent_content", false, "UUID_PARENT_CONTENT");
        public final static Property Start_date = new Property(10, java.util.Date.class, "start_date", false, "START_DATE");
        public final static Property End_date = new Property(11, java.util.Date.class, "end_date", false, "END_DATE");
    };

    private DaoSession daoSession;

    private Query<MobileContentH> mobileContentH_MobileContentHListQuery;
    private Query<MobileContentH> user_MobileContentHListQuery;

    public MobileContentHDao(DaoConfig config) {
        super(config);
    }
    
    public MobileContentHDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TR_MOBILECONTENT_H\" (" + //
                "\"UUID_MOBILE_CONTENT_H\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_mobile_content_h
                "\"CONTENT_NAME\" TEXT," + // 1: content_name
                "\"LAST_UPDATE\" INTEGER," + // 2: last_update
                "\"CONTENT_DESCRIPTION\" TEXT," + // 3: content_description
                "\"USR_CRT\" TEXT," + // 4: usr_crt
                "\"DTM_CRT\" INTEGER," + // 5: dtm_crt
                "\"USR_UPD\" TEXT," + // 6: usr_upd
                "\"DTM_UPD\" INTEGER," + // 7: dtm_upd
                "\"UUID_USER\" TEXT," + // 8: uuid_user
                "\"UUID_PARENT_CONTENT\" TEXT," + // 9: uuid_parent_content
                "\"START_DATE\" INTEGER," + // 10: start_date
                "\"END_DATE\" INTEGER);"); // 11: end_date
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TR_MOBILECONTENT_H\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, MobileContentH entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_mobile_content_h());
 
        String content_name = entity.getContent_name();
        if (content_name != null) {
            stmt.bindString(2, content_name);
        }
 
        java.util.Date last_update = entity.getLast_update();
        if (last_update != null) {
            stmt.bindLong(3, last_update.getTime());
        }
 
        String content_description = entity.getContent_description();
        if (content_description != null) {
            stmt.bindString(4, content_description);
        }
 
        String usr_crt = entity.getUsr_crt();
        if (usr_crt != null) {
            stmt.bindString(5, usr_crt);
        }
 
        java.util.Date dtm_crt = entity.getDtm_crt();
        if (dtm_crt != null) {
            stmt.bindLong(6, dtm_crt.getTime());
        }
 
        String usr_upd = entity.getUsr_upd();
        if (usr_upd != null) {
            stmt.bindString(7, usr_upd);
        }
 
        java.util.Date dtm_upd = entity.getDtm_upd();
        if (dtm_upd != null) {
            stmt.bindLong(8, dtm_upd.getTime());
        }
 
        String uuid_user = entity.getUuid_user();
        if (uuid_user != null) {
            stmt.bindString(9, uuid_user);
        }
 
        String uuid_parent_content = entity.getUuid_parent_content();
        if (uuid_parent_content != null) {
            stmt.bindString(10, uuid_parent_content);
        }
 
        java.util.Date start_date = entity.getStart_date();
        if (start_date != null) {
            stmt.bindLong(11, start_date.getTime());
        }
 
        java.util.Date end_date = entity.getEnd_date();
        if (end_date != null) {
            stmt.bindLong(12, end_date.getTime());
        }
    }

    @Override
    protected void attachEntity(MobileContentH entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public MobileContentH readEntity(Cursor cursor, int offset) {
        MobileContentH entity = new MobileContentH( //
            cursor.getString(offset + 0), // uuid_mobile_content_h
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // content_name
            cursor.isNull(offset + 2) ? null : new java.util.Date(cursor.getLong(offset + 2)), // last_update
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // content_description
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // usr_crt
            cursor.isNull(offset + 5) ? null : new java.util.Date(cursor.getLong(offset + 5)), // dtm_crt
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // usr_upd
            cursor.isNull(offset + 7) ? null : new java.util.Date(cursor.getLong(offset + 7)), // dtm_upd
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8), // uuid_user
            cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9), // uuid_parent_content
            cursor.isNull(offset + 10) ? null : new java.util.Date(cursor.getLong(offset + 10)), // start_date
            cursor.isNull(offset + 11) ? null : new java.util.Date(cursor.getLong(offset + 11)) // end_date
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, MobileContentH entity, int offset) {
        entity.setUuid_mobile_content_h(cursor.getString(offset + 0));
        entity.setContent_name(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setLast_update(cursor.isNull(offset + 2) ? null : new java.util.Date(cursor.getLong(offset + 2)));
        entity.setContent_description(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setUsr_crt(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setDtm_crt(cursor.isNull(offset + 5) ? null : new java.util.Date(cursor.getLong(offset + 5)));
        entity.setUsr_upd(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setDtm_upd(cursor.isNull(offset + 7) ? null : new java.util.Date(cursor.getLong(offset + 7)));
        entity.setUuid_user(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
        entity.setUuid_parent_content(cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9));
        entity.setStart_date(cursor.isNull(offset + 10) ? null : new java.util.Date(cursor.getLong(offset + 10)));
        entity.setEnd_date(cursor.isNull(offset + 11) ? null : new java.util.Date(cursor.getLong(offset + 11)));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(MobileContentH entity, long rowId) {
        return entity.getUuid_mobile_content_h();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(MobileContentH entity) {
        if(entity != null) {
            return entity.getUuid_mobile_content_h();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
    /** Internal query to resolve the "mobileContentHList" to-many relationship of MobileContentH. */
    public List<MobileContentH> _queryMobileContentH_MobileContentHList(String uuid_parent_content) {
        synchronized (this) {
            if (mobileContentH_MobileContentHListQuery == null) {
                QueryBuilder<MobileContentH> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_parent_content.eq(null));
                mobileContentH_MobileContentHListQuery = queryBuilder.build();
            }
        }
        Query<MobileContentH> query = mobileContentH_MobileContentHListQuery.forCurrentThread();
        query.setParameter(0, uuid_parent_content);
        return query.list();
    }

    /** Internal query to resolve the "mobileContentHList" to-many relationship of User. */
    public List<MobileContentH> _queryUser_MobileContentHList(String uuid_user) {
        synchronized (this) {
            if (user_MobileContentHListQuery == null) {
                QueryBuilder<MobileContentH> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_user.eq(null));
                user_MobileContentHListQuery = queryBuilder.build();
            }
        }
        Query<MobileContentH> query = user_MobileContentHListQuery.forCurrentThread();
        query.setParameter(0, uuid_user);
        return query.list();
    }

    private String selectDeep;

    protected String getSelectDeep() {
        if (selectDeep == null) {
            StringBuilder builder = new StringBuilder("SELECT ");
            SqlUtils.appendColumns(builder, "T", getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T0", daoSession.getMobileContentHDao().getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T1", daoSession.getUserDao().getAllColumns());
            builder.append(" FROM TR_MOBILECONTENT_H T");
            builder.append(" LEFT JOIN TR_MOBILECONTENT_H T0 ON T.\"UUID_PARENT_CONTENT\"=T0.\"UUID_MOBILE_CONTENT_H\"");
            builder.append(" LEFT JOIN MS_USER T1 ON T.\"UUID_USER\"=T1.\"UUID_USER\"");
            builder.append(' ');
            selectDeep = builder.toString();
        }
        return selectDeep;
    }
    
    protected MobileContentH loadCurrentDeep(Cursor cursor, boolean lock) {
        MobileContentH entity = loadCurrent(cursor, 0, lock);
        int offset = getAllColumns().length;

        MobileContentH mobileContentH = loadCurrentOther(daoSession.getMobileContentHDao(), cursor, offset);
        entity.setMobileContentH(mobileContentH);
        offset += daoSession.getMobileContentHDao().getAllColumns().length;

        User user = loadCurrentOther(daoSession.getUserDao(), cursor, offset);
        entity.setUser(user);

        return entity;    
    }

    public MobileContentH loadDeep(Long key) {
        assertSinglePk();
        if (key == null) {
            return null;
        }

        StringBuilder builder = new StringBuilder(getSelectDeep());
        builder.append("WHERE ");
        SqlUtils.appendColumnsEqValue(builder, "T", getPkColumns());
        String sql = builder.toString();
        
        String[] keyArray = new String[] { key.toString() };
        Cursor cursor = db.rawQuery(sql, keyArray);
        
        try {
            boolean available = cursor.moveToFirst();
            if (!available) {
                return null;
            } else if (!cursor.isLast()) {
                throw new IllegalStateException("Expected unique result, but count was " + cursor.getCount());
            }
            return loadCurrentDeep(cursor, true);
        } finally {
            cursor.close();
        }
    }
    
    /** Reads all available rows from the given cursor and returns a list of new ImageTO objects. */
    public List<MobileContentH> loadAllDeepFromCursor(Cursor cursor) {
        int count = cursor.getCount();
        List<MobileContentH> list = new ArrayList<MobileContentH>(count);
        
        if (cursor.moveToFirst()) {
            if (identityScope != null) {
                identityScope.lock();
                identityScope.reserveRoom(count);
            }
            try {
                do {
                    list.add(loadCurrentDeep(cursor, false));
                } while (cursor.moveToNext());
            } finally {
                if (identityScope != null) {
                    identityScope.unlock();
                }
            }
        }
        return list;
    }
    
    protected List<MobileContentH> loadDeepAllAndCloseCursor(Cursor cursor) {
        try {
            return loadAllDeepFromCursor(cursor);
        } finally {
            cursor.close();
        }
    }
    

    /** A raw-style query where you can pass any WHERE clause and arguments. */
    public List<MobileContentH> queryDeep(String where, String... selectionArg) {
        Cursor cursor = db.rawQuery(getSelectDeep() + where, selectionArg);
        return loadDeepAllAndCloseCursor(cursor);
    }
 
}
