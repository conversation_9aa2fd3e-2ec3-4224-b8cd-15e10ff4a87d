package com.adins.mss.base.dynamicform;

import com.google.gson.annotations.SerializedName;

import java.util.Date;

/**
 * Created by developer on 1/3/18.
 */

public class JsonRequestPromiseSurvey extends JsonRequestTaskD {
    @SerializedName("pts_date")
    Date pts_date;

    public Date getPts_date() {
        return pts_date;
    }

    public void setPts_date(Date ptsDate) {
        this.pts_date = ptsDate;
    }
}
