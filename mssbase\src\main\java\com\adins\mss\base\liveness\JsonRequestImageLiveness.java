package com.adins.mss.base.liveness;

import com.adins.mss.foundation.http.MssRequestType;
import com.google.gson.annotations.SerializedName;

public class JsonRequestImageLiveness extends MssRequestType {
    @SerializedName("imgIdentity")
    private String imgIdentity;

    public String getImgIdentity() {
        return imgIdentity;
    }

    public void setImgIdentity(String imgIdentity) {
        this.imgIdentity = imgIdentity;
    }
}
