package com.adins.mss.base.taskupdate.model;

import com.adins.mss.foundation.http.MssRequestType;
import com.google.gson.annotations.SerializedName;

public class SubmitTaskUpdateRequest extends MssRequestType {
    @SerializedName("uuid_task_update")
    String uuidTaskUpdate;
    @SerializedName("feedback_notes")
    String feedbackNotes;
    @SerializedName("upload_document")
    String uploadDocument;

    public String getUuidTaskUpdate() {
        return uuidTaskUpdate;
    }

    public void setUuidTaskUpdate(String uuidTaskUpdate) {
        this.uuidTaskUpdate = uuidTaskUpdate;
    }

    public String getFeedbackNotes() {
        return feedbackNotes;
    }

    public void setFeedbackNotes(String feedbackNotes) {
        this.feedbackNotes = feedbackNotes;
    }

    public String getUploadDocument() {
        return uploadDocument;
    }

    public void setUploadDocument(String uploadDocument) {
        this.uploadDocument = uploadDocument;
    }
}
