package com.adins.mss.base.dynamicform.form.models.validationcheck;

import com.adins.mss.dao.TaskD;
import com.google.gson.annotations.SerializedName;

import java.util.List;

public class ValidationCheckBean {

    @SerializedName("seqNo")
    private String seqNoQuestion;
    @SerializedName("taskD")
    private List<TaskD> listTaskD;
    @SerializedName("uuid_question_group")
    private String uuidQuestionGroup;

    public String getSeqNoQuestion() {
        return seqNoQuestion;
    }

    public void setSeqNoQuestion(String seqNoQuestion) {
        this.seqNoQuestion = seqNoQuestion;
    }

    public List<TaskD> getListTaskD() {
        return listTaskD;
    }

    public void setListTaskD(List<TaskD> listTaskD) {
        this.listTaskD = listTaskD;
    }

    public String getUuidQuestionGroup() {
        return uuidQuestionGroup;
    }

    public void setUuidQuestionGroup(String uuidQuestionGroup) {
        this.uuidQuestionGroup = uuidQuestionGroup;
    }

}
