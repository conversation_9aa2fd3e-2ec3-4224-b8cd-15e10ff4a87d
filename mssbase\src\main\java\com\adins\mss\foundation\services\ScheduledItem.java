package com.adins.mss.foundation.services;

import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.foundation.camerainapp.helper.Logger;

/**
 * <AUTHOR>
 *         <p>
 *         Object to store interval and event to trigger, used by AutoSendThread
 *         <p>
 *         example from:
 *         http://stackoverflow.com/questions/6776327/how-to-pause-resume-thread-in-android
 */
public class ScheduledItem extends Thread {


    String scheduleId;
    /**
     * Interval for which event will be triggered each time
     */
    int interval;
    ScheduledItemHandler handler;

//	/**
//	 * Used to count how much time left until event is triggered (in milliseconds)
//	 */
//	int intervalCounter;

    //	/**
//	 * The state of item, affecting if item should tick and trigger event
//	 */
//	State state;
    private Object mPauseLock = new Object();
    private boolean mPaused = false;

    public ScheduledItem(String id, int interval, ScheduledItemHandler handler) {
        super();
        this.scheduleId = id;
        this.interval = interval;
        this.handler = handler;
    }

    public ScheduledItem(String id, int interval) {
        super();
        this.scheduleId = id;
        this.interval = interval;
    }

    //=== Thread Method ===//
    public void run() {
//        while (!mFinished) {
        while (true) {

            try {
                handler.onEventTrigger(this);
            } catch (Exception e) {
                Logger.e("ScheduleItem " + scheduleId, "Exception occured");
                e.printStackTrace();
            }

            synchronized (mPauseLock) {

                //sleep by interval
                try {
                    mPauseLock.wait(interval);
                } catch (InterruptedException e1) {
                    e1.printStackTrace();
                    // Restore interrupted state...
                    Thread.currentThread().interrupt();
                }

                //sleep by pause, if paused
                while (mPaused) {
                    try {
                        mPauseLock.wait();
                    } catch (InterruptedException e) {
                        FireCrash.log(e);
                        // Restore interrupted state...
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }
    }

    /**
     * Call this on pause.
     */
    public void pauseSchedule() {
        synchronized (mPauseLock) {
            mPaused = true;
        }
    }

    /**
     * Call this on resume.
     */
    public void resumeSchedule() {
        synchronized (mPauseLock) {
            mPaused = false;
            mPauseLock.notifyAll();
        }
    }

    public int getInterval() {
        return interval;
    }

//	/**
//	 * 
//	 * Calculate count-down left before firing scheduled event. Calculation and event trigger will
//	 * be done only in active state.
//	 * 
//	 * @param interval interval of time has passed (in milliseconds) to put into calculation
//	 * @return flag whether the count-down has finished or still counting. Return true if count-down has finished.
//	 *  
//	 */
//	public boolean tick(int interval){
//		if (state == State.ACTIVE){
//			intervalCounter = intervalCounter - interval;
//			if (intervalCounter <= 0){
//				intervalCounter = this.interval - intervalCounter;
//				handler.onEventTrigger(this);
//				return true;
//			}
//		}
//		return false;
//	}

//	/**
//	 * Set state to paused (inactive) but retain last interval counter to be continued later
//	 */
//	public void pause(){
//		state = State.PAUSED;
//	}
//	
//	public void start(){
//		state = State.ACTIVE;
//	}
//	
//	
//	/**
//	 * Set state to inactive and restart interval counter should this object be started again 
//	 */
//	public void stop(){
//		state = State.INACTIVE;
//		intervalCounter = 0;
//	}

    public void setInterval(int interval) {
        this.interval = interval;
    }

    //=== Getter and Setter ===//

    public ScheduledItemHandler getHandler() {
        return handler;
    }

    public void setHandler(ScheduledItemHandler handler) {
        this.handler = handler;
    }

    public String getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(String scheduleId) {
        this.scheduleId = scheduleId;
    }

    enum State {
        PAUSED,
        ACTIVE,
        INACTIVE
    }

    /**
     * <AUTHOR>
     *         <p>
     *         A Interface for handler to handle event trigger when interval counter reaches zero or below
     */
    public interface ScheduledItemHandler {
        /**
         * Callback when ScheduledItem first started or after a set of interval.
         *
         * @param schItem
         * @return true if should ignore interval and trigger another event just after the previous is done,
         * or false if next trigger should wait until next interval
         */
        public boolean onEventTrigger(ScheduledItem schItem);
    }

}
