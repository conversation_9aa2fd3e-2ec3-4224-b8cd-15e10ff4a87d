package com.adins.mss.dao;

import java.util.List;
import java.util.ArrayList;
import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.SqlUtils;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;
import de.greenrobot.dao.query.Query;
import de.greenrobot.dao.query.QueryBuilder;

import com.adins.mss.dao.CollectionHistory;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TR_COLLECTIONHISTORY".
*/
public class CollectionHistoryDao extends AbstractDao<CollectionHistory, String> {

    public static final String TABLENAME = "TR_COLLECTIONHISTORY";

    /**
     * Properties of entity CollectionHistory.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_collection_history = new Property(0, String.class, "uuid_collection_history", true, "UUID_COLLECTION_HISTORY");
        public final static Property Last_update = new Property(1, java.util.Date.class, "last_update", false, "LAST_UPDATE");
        public final static Property Description = new Property(2, String.class, "description", false, "DESCRIPTION");
        public final static Property Usr_crt = new Property(3, String.class, "usr_crt", false, "USR_CRT");
        public final static Property Dtm_crt = new Property(4, java.util.Date.class, "dtm_crt", false, "DTM_CRT");
        public final static Property Uuid_user = new Property(5, String.class, "uuid_user", false, "UUID_USER");
    };

    private DaoSession daoSession;

    private Query<CollectionHistory> user_CollectionHistoryListQuery;

    public CollectionHistoryDao(DaoConfig config) {
        super(config);
    }
    
    public CollectionHistoryDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TR_COLLECTIONHISTORY\" (" + //
                "\"UUID_COLLECTION_HISTORY\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_collection_history
                "\"LAST_UPDATE\" INTEGER," + // 1: last_update
                "\"DESCRIPTION\" TEXT," + // 2: description
                "\"USR_CRT\" TEXT," + // 3: usr_crt
                "\"DTM_CRT\" INTEGER," + // 4: dtm_crt
                "\"UUID_USER\" TEXT);"); // 5: uuid_user
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TR_COLLECTIONHISTORY\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, CollectionHistory entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_collection_history());
 
        java.util.Date last_update = entity.getLast_update();
        if (last_update != null) {
            stmt.bindLong(2, last_update.getTime());
        }
 
        String description = entity.getDescription();
        if (description != null) {
            stmt.bindString(3, description);
        }
 
        String usr_crt = entity.getUsr_crt();
        if (usr_crt != null) {
            stmt.bindString(4, usr_crt);
        }
 
        java.util.Date dtm_crt = entity.getDtm_crt();
        if (dtm_crt != null) {
            stmt.bindLong(5, dtm_crt.getTime());
        }
 
        String uuid_user = entity.getUuid_user();
        if (uuid_user != null) {
            stmt.bindString(6, uuid_user);
        }
    }

    @Override
    protected void attachEntity(CollectionHistory entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public CollectionHistory readEntity(Cursor cursor, int offset) {
        CollectionHistory entity = new CollectionHistory( //
            cursor.getString(offset + 0), // uuid_collection_history
            cursor.isNull(offset + 1) ? null : new java.util.Date(cursor.getLong(offset + 1)), // last_update
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // description
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // usr_crt
            cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)), // dtm_crt
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5) // uuid_user
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, CollectionHistory entity, int offset) {
        entity.setUuid_collection_history(cursor.getString(offset + 0));
        entity.setLast_update(cursor.isNull(offset + 1) ? null : new java.util.Date(cursor.getLong(offset + 1)));
        entity.setDescription(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setUsr_crt(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setDtm_crt(cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)));
        entity.setUuid_user(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(CollectionHistory entity, long rowId) {
        return entity.getUuid_collection_history();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(CollectionHistory entity) {
        if(entity != null) {
            return entity.getUuid_collection_history();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
    /** Internal query to resolve the "collectionHistoryList" to-many relationship of User. */
    public List<CollectionHistory> _queryUser_CollectionHistoryList(String uuid_user) {
        synchronized (this) {
            if (user_CollectionHistoryListQuery == null) {
                QueryBuilder<CollectionHistory> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_user.eq(null));
                user_CollectionHistoryListQuery = queryBuilder.build();
            }
        }
        Query<CollectionHistory> query = user_CollectionHistoryListQuery.forCurrentThread();
        query.setParameter(0, uuid_user);
        return query.list();
    }

    private String selectDeep;

    protected String getSelectDeep() {
        if (selectDeep == null) {
            StringBuilder builder = new StringBuilder("SELECT ");
            SqlUtils.appendColumns(builder, "T", getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T0", daoSession.getUserDao().getAllColumns());
            builder.append(" FROM TR_COLLECTIONHISTORY T");
            builder.append(" LEFT JOIN MS_USER T0 ON T.\"UUID_USER\"=T0.\"UUID_USER\"");
            builder.append(' ');
            selectDeep = builder.toString();
        }
        return selectDeep;
    }
    
    protected CollectionHistory loadCurrentDeep(Cursor cursor, boolean lock) {
        CollectionHistory entity = loadCurrent(cursor, 0, lock);
        int offset = getAllColumns().length;

        User user = loadCurrentOther(daoSession.getUserDao(), cursor, offset);
        entity.setUser(user);

        return entity;    
    }

    public CollectionHistory loadDeep(Long key) {
        assertSinglePk();
        if (key == null) {
            return null;
        }

        StringBuilder builder = new StringBuilder(getSelectDeep());
        builder.append("WHERE ");
        SqlUtils.appendColumnsEqValue(builder, "T", getPkColumns());
        String sql = builder.toString();
        
        String[] keyArray = new String[] { key.toString() };
        Cursor cursor = db.rawQuery(sql, keyArray);
        
        try {
            boolean available = cursor.moveToFirst();
            if (!available) {
                return null;
            } else if (!cursor.isLast()) {
                throw new IllegalStateException("Expected unique result, but count was " + cursor.getCount());
            }
            return loadCurrentDeep(cursor, true);
        } finally {
            cursor.close();
        }
    }
    
    /** Reads all available rows from the given cursor and returns a list of new ImageTO objects. */
    public List<CollectionHistory> loadAllDeepFromCursor(Cursor cursor) {
        int count = cursor.getCount();
        List<CollectionHistory> list = new ArrayList<CollectionHistory>(count);
        
        if (cursor.moveToFirst()) {
            if (identityScope != null) {
                identityScope.lock();
                identityScope.reserveRoom(count);
            }
            try {
                do {
                    list.add(loadCurrentDeep(cursor, false));
                } while (cursor.moveToNext());
            } finally {
                if (identityScope != null) {
                    identityScope.unlock();
                }
            }
        }
        return list;
    }
    
    protected List<CollectionHistory> loadDeepAllAndCloseCursor(Cursor cursor) {
        try {
            return loadAllDeepFromCursor(cursor);
        } finally {
            cursor.close();
        }
    }
    

    /** A raw-style query where you can pass any WHERE clause and arguments. */
    public List<CollectionHistory> queryDeep(String where, String... selectionArg) {
        Cursor cursor = db.rawQuery(getSelectDeep() + where, selectionArg);
        return loadDeepAllAndCloseCursor(cursor);
    }
 
}
