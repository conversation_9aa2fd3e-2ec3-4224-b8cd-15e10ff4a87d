package com.adins.mss.dao;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "MS_PO".
 */
public class ProductOffering {

     @SerializedName("id")
    private long id;
     @SerializedName("assetSchemeId")
    private int asset_scheme_id;
     @SerializedName("dealerSchemeId")
    private int dealer_scheme_id;
     @SerializedName("branchId")
    private String branch_id;
    /** Not-null value. */
     @SerializedName("prodOffId")
    private String prod_off_id;
     @SerializedName("prodOffName")
    private String prod_off_name;
     @SerializedName("prodCatCode")
    private String prod_cat_code;
     @SerializedName("prodCatName")
    private String prod_cat_name;
     @SerializedName("jnsPmbiayaan")
    private String jns_pembiayaan;
     @SerializedName("minTenor")
    private Integer min_tenor;
     @SerializedName("maxTenor")
    private Integer max_tenor;
     @SerializedName("scId")
    private String sc_id;
     @SerializedName("component")
    private String component;
     @SerializedName("ruleDataMinTdp")
    private Integer rule_data_min_tdp;
     @SerializedName("ruleDataManfYear")
    private Integer rule_data_manf_year;
     @SerializedName("ruleDataAstPrice")
    private Integer rule_asset_price;
     @SerializedName("ruleDataMinDp")
    private Integer rule_min_dp;
     @SerializedName("ruleDataAppTc")
    private Integer rule_app_tc;
     @SerializedName("ruleDataScoring")
    private Integer rule_scoring;
     @SerializedName("ruleDataScoringMatrix")
    private Integer rule_matrix;
     @SerializedName("isDeleted")
    private Integer is_deleted;
     @SerializedName("dtmUpd")
    private java.util.Date dtm_upd;
     @SerializedName("isShow")
    private Integer show_pot;

    public ProductOffering() {
    }

    public ProductOffering(long id) {
        this.id = id;
    }

    public ProductOffering(long id, int asset_scheme_id, int dealer_scheme_id, String branch_id, String prod_off_id, String prod_off_name, String prod_cat_code, String prod_cat_name, String jns_pembiayaan, Integer min_tenor, Integer max_tenor, String sc_id, String component, Integer rule_data_min_tdp, Integer rule_data_manf_year, Integer rule_asset_price, Integer rule_min_dp, Integer rule_app_tc, Integer rule_scoring, Integer rule_matrix, Integer is_deleted, java.util.Date dtm_upd, Integer show_pot) {
        this.id = id;
        this.asset_scheme_id = asset_scheme_id;
        this.dealer_scheme_id = dealer_scheme_id;
        this.branch_id = branch_id;
        this.prod_off_id = prod_off_id;
        this.prod_off_name = prod_off_name;
        this.prod_cat_code = prod_cat_code;
        this.prod_cat_name = prod_cat_name;
        this.jns_pembiayaan = jns_pembiayaan;
        this.min_tenor = min_tenor;
        this.max_tenor = max_tenor;
        this.sc_id = sc_id;
        this.component = component;
        this.rule_data_min_tdp = rule_data_min_tdp;
        this.rule_data_manf_year = rule_data_manf_year;
        this.rule_asset_price = rule_asset_price;
        this.rule_min_dp = rule_min_dp;
        this.rule_app_tc = rule_app_tc;
        this.rule_scoring = rule_scoring;
        this.rule_matrix = rule_matrix;
        this.is_deleted = is_deleted;
        this.dtm_upd = dtm_upd;
        this.show_pot = show_pot;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getAsset_scheme_id() {
        return asset_scheme_id;
    }

    public void setAsset_scheme_id(int asset_scheme_id) {
        this.asset_scheme_id = asset_scheme_id;
    }

    public int getDealer_scheme_id() {
        return dealer_scheme_id;
    }

    public void setDealer_scheme_id(int dealer_scheme_id) {
        this.dealer_scheme_id = dealer_scheme_id;
    }

    public String getBranch_id() {
        return branch_id;
    }

    public void setBranch_id(String branch_id) {
        this.branch_id = branch_id;
    }

    /** Not-null value. */
    public String getProd_off_id() {
        return prod_off_id;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setProd_off_id(String prod_off_id) {
        this.prod_off_id = prod_off_id;
    }

    public String getProd_off_name() {
        return prod_off_name;
    }

    public void setProd_off_name(String prod_off_name) {
        this.prod_off_name = prod_off_name;
    }

    public String getProd_cat_code() {
        return prod_cat_code;
    }

    public void setProd_cat_code(String prod_cat_code) {
        this.prod_cat_code = prod_cat_code;
    }

    public String getProd_cat_name() {
        return prod_cat_name;
    }

    public void setProd_cat_name(String prod_cat_name) {
        this.prod_cat_name = prod_cat_name;
    }

    public String getJns_pembiayaan() {
        return jns_pembiayaan;
    }

    public void setJns_pembiayaan(String jns_pembiayaan) {
        this.jns_pembiayaan = jns_pembiayaan;
    }

    public Integer getMin_tenor() {
        return min_tenor;
    }

    public void setMin_tenor(Integer min_tenor) {
        this.min_tenor = min_tenor;
    }

    public Integer getMax_tenor() {
        return max_tenor;
    }

    public void setMax_tenor(Integer max_tenor) {
        this.max_tenor = max_tenor;
    }

    public String getSc_id() {
        return sc_id;
    }

    public void setSc_id(String sc_id) {
        this.sc_id = sc_id;
    }

    public String getComponent() {
        return component;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public Integer getRule_data_min_tdp() {
        return rule_data_min_tdp;
    }

    public void setRule_data_min_tdp(Integer rule_data_min_tdp) {
        this.rule_data_min_tdp = rule_data_min_tdp;
    }

    public Integer getRule_data_manf_year() {
        return rule_data_manf_year;
    }

    public void setRule_data_manf_year(Integer rule_data_manf_year) {
        this.rule_data_manf_year = rule_data_manf_year;
    }

    public Integer getRule_asset_price() {
        return rule_asset_price;
    }

    public void setRule_asset_price(Integer rule_asset_price) {
        this.rule_asset_price = rule_asset_price;
    }

    public Integer getRule_min_dp() {
        return rule_min_dp;
    }

    public void setRule_min_dp(Integer rule_min_dp) {
        this.rule_min_dp = rule_min_dp;
    }

    public Integer getRule_app_tc() {
        return rule_app_tc;
    }

    public void setRule_app_tc(Integer rule_app_tc) {
        this.rule_app_tc = rule_app_tc;
    }

    public Integer getRule_scoring() {
        return rule_scoring;
    }

    public void setRule_scoring(Integer rule_scoring) {
        this.rule_scoring = rule_scoring;
    }

    public Integer getRule_matrix() {
        return rule_matrix;
    }

    public void setRule_matrix(Integer rule_matrix) {
        this.rule_matrix = rule_matrix;
    }

    public Integer getIs_deleted() {
        return is_deleted;
    }

    public void setIs_deleted(Integer is_deleted) {
        this.is_deleted = is_deleted;
    }

    public java.util.Date getDtm_upd() {
        return dtm_upd;
    }

    public void setDtm_upd(java.util.Date dtm_upd) {
        this.dtm_upd = dtm_upd;
    }

    public Integer getShow_pot() {
        return show_pot;
    }

    public void setShow_pot(Integer show_pot) {
        this.show_pot = show_pot;
    }

}
