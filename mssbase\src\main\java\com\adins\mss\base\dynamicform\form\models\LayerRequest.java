package com.adins.mss.base.dynamicform.form.models;

import com.adins.mss.dao.TaskH;
import com.adins.mss.foundation.http.MssRequestType;
import com.google.gson.annotations.SerializedName;

import java.util.List;
import java.util.Map;

public class LayerRequest extends MssRequestType {
    @SerializedName("questionGroup")
    List<LayerBean> layerBeanList;
    @SerializedName("taskH")
    TaskH taskH;
    @SerializedName("uuidQuestionGroupProcess")
    String  uuidQuestionGroupProcess;
    @SerializedName("filter")
    Map<String, Map<String, String>> filter;
    @SerializedName("countRetry")
    String countRetry;

    public List<LayerBean> getLayerBeanList() {
        return layerBeanList;
    }

    public void setLayerBeanList(List<LayerBean> layerBeanList) {
        this.layerBeanList = layerBeanList;
    }

    public TaskH getTaskH() {
        return taskH;
    }

    public void setTaskH(TaskH taskH) {
        this.taskH = taskH;
    }

    public String getUuidQuestionGroupProcess() {
        return uuidQuestionGroupProcess;
    }

    public void setUuidQuestionGroupProcess(String uuidQuestionGroupProcess) {
        this.uuidQuestionGroupProcess = uuidQuestionGroupProcess;
    }

    public Map<String, Map<String, String>> getFilter() {
        return filter;
    }

    public void setFilter(Map<String, Map<String, String>> filter) {
        this.filter = filter;
    }

    public String getCountRetry() {
        return countRetry;
    }

    public void setCountRetry(String countRetry) {
        this.countRetry = countRetry;
    }

}
