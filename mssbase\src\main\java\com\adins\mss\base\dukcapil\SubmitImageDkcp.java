package com.adins.mss.base.dukcapil;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Context;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Message;
import android.widget.Toast;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.dynamicform.form.FragmentQuestion;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.base.util.Utility;
import com.adins.mss.constant.Global;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.questiongenerator.QuestionBean;
import com.google.firebase.perf.FirebasePerformance;
import com.google.firebase.perf.metrics.HttpMetric;

import org.apache.commons.lang3.StringUtils;

import java.lang.ref.WeakReference;

public class SubmitImageDkcp extends AsyncTask<String, Void, String> {

    private ProgressDialog progressDialog;
    private WeakReference<Activity> activity;
    private Context context;
    private String errMessage;
    private QuestionBean questionBean;

    public SubmitImageDkcp(Activity activity, Context context, QuestionBean questionBean){
        this.activity = new WeakReference<>(activity);
        this.questionBean = questionBean;
        this.context = context;
    }

    @Override
    protected void onPreExecute() {
        progressDialog = ProgressDialog.show(activity.get(), "", activity.get().getString(R.string.generate_image_data), true);
        super.onPreExecute();
    }

    @Override
    protected String doInBackground(String... params) {
        String imgBase64 = params[0];
        if (Tool.isInternetconnected(activity.get())) {
            JsonRequestImageDkcp jsonRequestImageIdentity = new JsonRequestImageDkcp();
            jsonRequestImageIdentity.setAudit(GlobalData.getSharedGlobalData().getAuditData());
            jsonRequestImageIdentity.setImgIdentity(imgBase64);
            jsonRequestImageIdentity.setAssetTag(questionBean.getTag());

            String jsonRequest = GsonHelper.toJson(jsonRequestImageIdentity);

            String url = GlobalData.getSharedGlobalData().getURL_SUBMIT_DKCP();
            boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
            boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
            HttpCryptedConnection httpConn = new HttpCryptedConnection(activity.get(), encrypt, decrypt);
            HttpConnectionResult serverResult = null;

            //Firebase Performance Trace HTTP Request
            HttpMetric networkMetric =
                    FirebasePerformance.getInstance().newHttpMetric(url, FirebasePerformance.HttpMethod.POST);
            Utility.metricStart(networkMetric, jsonRequest);

            try {
                serverResult = httpConn.requestToServer(url, jsonRequest, Global.DEFAULTCONNECTIONTIMEOUT);
            } catch (Exception e) {
                FireCrash.log(e);
                errMessage = activity.get().getString(R.string.msgConnectionFailed);
                return null;
            }
            if (serverResult != null && serverResult.isOK()) {
                return serverResult.getResult();
            } else {
                errMessage = activity.get().getString(R.string.connection_failed);
            }
        } else {
            errMessage = activity.get().getString(R.string.no_internet_connection);
        }
        return null;
    }

    @Override
    protected void onPostExecute(String response) {
        super.onPostExecute(response);
        if(errMessage==null) {
            ResponseImageDkcp responseImageDkcp = GsonHelper.fromJson(response, ResponseImageDkcp.class);
            if (responseImageDkcp.getStatus().getCode() == 0 && responseImageDkcp.getDataDkcp()!=null) {
                questionBean.setResponseImageDkcp(responseImageDkcp);
                String splitMsg = splitMessage(responseImageDkcp.getDataDkcp().getGeneratedStatus());
                if (!"".equalsIgnoreCase(splitMsg) && !splitMsg.equalsIgnoreCase("success")){
                    Toast.makeText(context,activity.get().getString(R.string.copy_value_dkcp_failed), Toast.LENGTH_SHORT).show();
                } else if (StringUtils.isNotBlank(responseImageDkcp.getErrorMessage())) {
                    Toast.makeText(context, responseImageDkcp.getErrorMessage(), Toast.LENGTH_SHORT).show();
                } else {
                    Message message = new Message();
                    Bundle bundle = new Bundle();
                    bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.RESULT_FROM_SUBMITDKCP);
                    bundle.putString("identifier", questionBean.getIdentifier_name());
                    message.setData(bundle);
                    FragmentQuestion.questionHandler.sendMessage(message);
                }
            } else {
                questionBean.setResponseImageDkcp(responseImageDkcp);
                Toast.makeText(context,activity.get().getString(R.string.copy_value_dkcp_failed), Toast.LENGTH_SHORT).show();
            }
        } else{
            Toast.makeText(context,errMessage, Toast.LENGTH_SHORT).show();
        }
        progressDialog.dismiss();
    }
    private String splitMessage(String errMessage) {
        String message = "";
        String[] splits = Tool.split(errMessage, "$");
        if (splits.length > 0) {
            message = splits[0];
        }
        return message;
    }
}
