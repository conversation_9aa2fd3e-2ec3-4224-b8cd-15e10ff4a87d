package com.adins.mss.base;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.EditText;

import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.util.LocaleHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.foundation.dialog.DialogManager;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.security.storepreferences.ObscuredSharedPreferences;

import org.acra.ACRA;

import java.util.Locale;

public class ServerLinkActivity extends Activity implements OnClickListener {


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (Tool.isProdFlavour(Global.FLAVORS)) {
            //To can't screenshoot
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
        }
        setContentView(R.layout.server_link);
        ACRA.getErrorReporter().putCustomData("LAST_CLASS_ACCESSED", getClass().getSimpleName());
        EditText txtServerLink = (EditText) findViewById(R.id.txtServerLink);
        txtServerLink.setText(GlobalData.getSharedGlobalData().getUrlMain());

        Button btnLogin = (Button) findViewById(R.id.btnSaveLink);
        btnLogin.setOnClickListener(this);
    }

    @Override
    protected void attachBaseContext(Context newBase) {
        Context context = newBase;
        Locale locale;
        try {
            locale = new Locale(GlobalData.getSharedGlobalData().getLocale());
            if (null == locale) {
                locale = new Locale(LocaleHelper.ENGLSIH);
            }
            context = LocaleHelper.wrap(newBase, locale);
        } catch (Exception e) {
            locale = new Locale(LocaleHelper.ENGLSIH);
            context = LocaleHelper.wrap(newBase, locale);
        } finally {
            super.attachBaseContext(context);
        }
    }

    @Override
    public void onBackPressed() {
        //startActivity(new Intent(ServerLinkActivity.this,  LoginActivity.class));
        ServerLinkActivity.this.finish();

    }

    @Override
    protected void onResume() {

        super.onResume();
        try {

            DialogManager.showGPSAlert(this);
        } catch (Exception e) {
            FireCrash.log(e);

        }
    }


    @Override
    public void onClick(View v) {

        Button btn = (Button) v;
        int id = btn.getId();
        if (R.id.btnSaveLink == id) {
            EditText txtServerLink = (EditText) findViewById(R.id.txtServerLink);
            String serverLink = txtServerLink.getText().toString().trim();


            GlobalData.getSharedGlobalData().setUrlMain(serverLink);
            GlobalData.getSharedGlobalData().reloadUrl(this.getApplicationContext());

            //Gigin ~ set URL Header Di Global.URL_HEADER tanpa "m/"
//			String nSVL = serverLink.substring(0, serverLink.length()-2);
//			Global.setUrlHeader(nSVL);
            ObscuredSharedPreferences sharedPref = ObscuredSharedPreferences.getPrefs(getApplicationContext(),
                    "GlobalData", Context.MODE_PRIVATE);

            ObscuredSharedPreferences.Editor sharedPrefEditor = sharedPref.edit();
            sharedPrefEditor.putString("URL_HEADER", serverLink);
            sharedPrefEditor.commit();


            //startActivity(new Intent(ServerLinkActivity.this,  LoginActivity.class));
            ServerLinkActivity.this.finish();
        }
    }
}
