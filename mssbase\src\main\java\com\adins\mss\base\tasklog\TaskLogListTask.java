package com.adins.mss.base.tasklog;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Context;
import android.os.AsyncTask;
import android.os.Handler;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentTransaction;
import android.text.format.DateFormat;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.mainmenu.MainMenuActivity;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.TaskH;
import com.adins.mss.foundation.dialog.NiftyDialogBuilder;

import org.acra.ACRA;

import java.util.Calendar;
import java.util.List;

public class TaskLogListTask extends AsyncTask<Void, Void, List<TaskH>> {
    public Fragment LogResultFragment;
    private ProgressDialog progressDialog;
    private Context context;
    private Activity activity;
    private String messageWait;
    private String messageEmpty;
    private String errMessage = null;
    private Class<?> taskLogResultActivity;
    private FragmentActivity fragmentActivity;
    private int contentFrame;

    public TaskLogListTask(Activity activity, String messageWait, String messageEmpty, Class<?> LogResultActivity) {
        this.activity = activity;
        this.messageWait = messageWait;
        this.messageEmpty = messageEmpty;
        this.taskLogResultActivity = taskLogResultActivity;
    }

    public TaskLogListTask(FragmentActivity fragmentActivity, String messageWait, String messageEmpty, int contentFrame, Fragment logResultFragment) {
        this.context = fragmentActivity;
        this.fragmentActivity = fragmentActivity;
        this.messageWait = messageWait;
        this.messageEmpty = messageEmpty;
        this.contentFrame = contentFrame;
        this.LogResultFragment = logResultFragment;
    }

    public TaskLogListTask(Context context, String messageWait, String messageEmpty) {
        this.context = context;
        this.messageWait = messageWait;
        this.messageEmpty = messageEmpty;
    }

    @Override
    protected void onPreExecute() {
        progressDialog = ProgressDialog.show(context, "", messageWait, true);
    }

    @Override
    protected List<TaskH> doInBackground(Void... params) {
        // TODO Auto-generated method stub
        List<TaskH> result = null;

        try {
            if (context != null) {
                TaskLog log = new TaskLog(context);
                result = log.getListTaskLog();
            } else if (activity != null) {
                TaskLog log = new TaskLog(activity);
                result = log.getListTaskLog();
            } else {
                TaskLog log = new TaskLog(fragmentActivity);
                result = log.getListTaskLog();
            }

			/*if (GlobalData.getSharedGlobalData().getApplication().equals(Global.APPLICATION_COLLECTION)) {
                List<TaskH> onlineLog = TaskLogHelper.getTaskLog(context);
				if (onlineLog != null) {
					if (result == null) result = new ArrayList<>();
					List<String> uuidListTaskH = new ArrayList<>();

					for (TaskH taskH : result) {
						uuidListTaskH.add(taskH.getUuid_task_h());
					}

					Iterator<TaskH> iterator = onlineLog.iterator();
					while(iterator.hasNext()) {
						TaskH taskH = iterator.next();

						if (uuidListTaskH.contains(taskH.getUuid_task_h())) {
							iterator.remove();
						}
					}

					if (onlineLog.size() > 0) {
						for (TaskH taskH : onlineLog) {
							taskH.setUuid_user(GlobalData.getSharedGlobalData().getUser().getUuid_user());
							taskH.setStatus(TaskHDataAccess.STATUS_SEND_SENT);
							TaskHDataAccess.addOrReplace(context, taskH);
							result.add(taskH);
						}
					}
				}
			}*/
        } catch (Exception ex) {
            FireCrash.log(ex);
            // TODO: handle exception
            ex.printStackTrace();
            try {
                progressDialog.dismiss();
            } catch (Exception e) {
                FireCrash.log(e);
            }
            errMessage = ex.getMessage();
        }
        return result;
    }

    @Override
    protected void onPostExecute(List<TaskH> result) {
        if (progressDialog.isShowing()) {
            try {
                progressDialog.dismiss();
            } catch (Exception e) {
                FireCrash.log(e);
            }
        }
        try {
            MainMenuActivity.setDrawerCounter();
        } catch (Exception e) {
            FireCrash.log(e);
            ACRA.getErrorReporter().putCustomData("ErrorMainMenuActivity", e.getMessage());
            ACRA.getErrorReporter().putCustomData("ErrorMainMenuActivity", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
            ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat set Drawer Counter"));
        }
        if (errMessage != null) {
            NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(context);
            dialogBuilder.withTitle(context.getString(R.string.error_capital))
                    .withMessage(errMessage)
                    .show();
        } else if (result == null || result.isEmpty()) {
            if (GlobalData.getSharedGlobalData().getApplication().equals(Global.APPLICATION_COLLECTION)) {
                gotoLog();
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(context);
                        dialogBuilder.withTitle(context.getString(R.string.info_capital))
                                .withMessage(messageEmpty)
                                .show();
                    }
                }, 1000);
            } else if (Global.APPLICATION_SURVEY.equals(GlobalData.getSharedGlobalData().getApplication())) {
                gotoLog();
            } else {
                NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(context);
                dialogBuilder.withTitle(context.getString(R.string.info_capital))
                        .withMessage(messageEmpty)
                        .show();
            }
        } else {
            //TODO: Buat Masuk ke Activity result nya
            if (result == null || result.isEmpty())
                Global.listOfSentTask = result;
            gotoLog();

        }
    }

    public void gotoLog() {
        Fragment fragment = new LogResultActivity();
        FragmentTransaction transaction = MainMenuActivity.fragmentManager.beginTransaction();
        transaction.setCustomAnimations(R.anim.activity_open_translate, R.anim.activity_close_scale, R.anim.activity_open_scale, R.anim.activity_close_translate);
        transaction.replace(contentFrame, fragment);
        transaction.addToBackStack(null);
        transaction.commitAllowingStateLoss();
        MainMenuActivity.tempPosition = 0;
    }
}
