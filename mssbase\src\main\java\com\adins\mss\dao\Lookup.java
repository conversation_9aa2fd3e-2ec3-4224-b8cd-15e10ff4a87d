package com.adins.mss.dao;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "MS_LOOKUP".
 */
public class Lookup {

    /** Not-null value. */
     @SerializedName("uuid_lookup")
    private String uuid_lookup;
     @SerializedName("option_id")
    private String option_id;
     @SerializedName("code")
    private String code;
     @SerializedName("value")
    private String value;
     @SerializedName("filter1")
    private String filter1;
     @SerializedName("filter2")
    private String filter2;
     @SerializedName("filter3")
    private String filter3;
     @SerializedName("filter4")
    private String filter4;
     @SerializedName("filter5")
    private String filter5;
     @SerializedName("sequence")
    private Integer sequence;
     @SerializedName("usr_crt")
    private String usr_crt;
     @SerializedName("dtm_crt")
    private java.util.Date dtm_crt;
     @SerializedName("usr_upd")
    private String usr_upd;
     @SerializedName("dtm_upd")
    private java.util.Date dtm_upd;
     @SerializedName("uuid_question_set")
    private String uuid_question_set;
     @SerializedName("lov_group")
    private String lov_group;
     @SerializedName("is_active")
    private String is_active;
     @SerializedName("is_deleted")
    private String is_deleted;

    public Lookup() {
    }

    public Lookup(String uuid_lookup) {
        this.uuid_lookup = uuid_lookup;
    }

    public Lookup(String uuid_lookup, String option_id, String code, String value, String filter1, String filter2, String filter3, String filter4, String filter5, Integer sequence, String usr_crt, java.util.Date dtm_crt, String usr_upd, java.util.Date dtm_upd, String uuid_question_set, String lov_group, String is_active, String is_deleted) {
        this.uuid_lookup = uuid_lookup;
        this.option_id = option_id;
        this.code = code;
        this.value = value;
        this.filter1 = filter1;
        this.filter2 = filter2;
        this.filter3 = filter3;
        this.filter4 = filter4;
        this.filter5 = filter5;
        this.sequence = sequence;
        this.usr_crt = usr_crt;
        this.dtm_crt = dtm_crt;
        this.usr_upd = usr_upd;
        this.dtm_upd = dtm_upd;
        this.uuid_question_set = uuid_question_set;
        this.lov_group = lov_group;
        this.is_active = is_active;
        this.is_deleted = is_deleted;
    }

    /** Not-null value. */
    public String getUuid_lookup() {
        return uuid_lookup;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_lookup(String uuid_lookup) {
        this.uuid_lookup = uuid_lookup;
    }

    public String getOption_id() {
        return option_id;
    }

    public void setOption_id(String option_id) {
        this.option_id = option_id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getFilter1() {
        return filter1;
    }

    public void setFilter1(String filter1) {
        this.filter1 = filter1;
    }

    public String getFilter2() {
        return filter2;
    }

    public void setFilter2(String filter2) {
        this.filter2 = filter2;
    }

    public String getFilter3() {
        return filter3;
    }

    public void setFilter3(String filter3) {
        this.filter3 = filter3;
    }

    public String getFilter4() {
        return filter4;
    }

    public void setFilter4(String filter4) {
        this.filter4 = filter4;
    }

    public String getFilter5() {
        return filter5;
    }

    public void setFilter5(String filter5) {
        this.filter5 = filter5;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public String getUsr_crt() {
        return usr_crt;
    }

    public void setUsr_crt(String usr_crt) {
        this.usr_crt = usr_crt;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public String getUsr_upd() {
        return usr_upd;
    }

    public void setUsr_upd(String usr_upd) {
        this.usr_upd = usr_upd;
    }

    public java.util.Date getDtm_upd() {
        return dtm_upd;
    }

    public void setDtm_upd(java.util.Date dtm_upd) {
        this.dtm_upd = dtm_upd;
    }

    public String getUuid_question_set() {
        return uuid_question_set;
    }

    public void setUuid_question_set(String uuid_question_set) {
        this.uuid_question_set = uuid_question_set;
    }

    public String getLov_group() {
        return lov_group;
    }

    public void setLov_group(String lov_group) {
        this.lov_group = lov_group;
    }

    public String getIs_active() {
        return is_active;
    }

    public void setIs_active(String is_active) {
        this.is_active = is_active;
    }

    public String getIs_deleted() {
        return is_deleted;
    }

    public void setIs_deleted(String is_deleted) {
        this.is_deleted = is_deleted;
    }

}
