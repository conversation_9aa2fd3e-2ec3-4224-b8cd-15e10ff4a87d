package com.adins.mss.base.dynamicform.form.questions.viewholder;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Context;
import android.os.AsyncTask;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.dynamicform.Constant;
import com.adins.mss.base.dynamicform.DynamicFormActivity;
import com.adins.mss.base.dynamicform.form.FragmentQuestion;
import com.adins.mss.base.dynamicform.form.models.GetDSRRequest;
import com.adins.mss.base.dynamicform.form.models.GetDSRResponse;
import com.adins.mss.base.dynamicform.form.questions.OnIsOfflineDSRListener;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.Scheme;
import com.adins.mss.foundation.db.dataaccess.SchemeDataAccess;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.questiongenerator.QuestionBean;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

public class DSRQuestionViewHolder extends RecyclerView.ViewHolder {
    private final Activity activity;
    private QuestionBean bean;
    private final Button btnGetDSR;
    private final TextView txtResultDSR;
    private final TextView mQuestionLabel;
    private final EditText txtDSR;
    private final OnIsOfflineDSRListener mListener;
    private String ansInstallment;
    private String ansSalary;
    private String ansSalarySpouse;
    private String ansInstallmentWOM;
    private String ansInstallmentOther;

    public DSRQuestionViewHolder(View view, Activity context, OnIsOfflineDSRListener onIsOfflineDSRListener) {
        super(view);
        mQuestionLabel = view.findViewById(R.id.lblQuestionDSR);
        txtResultDSR = view.findViewById(R.id.txtResult);
        btnGetDSR = view.findViewById(R.id.btnGetDSRResult);
        txtDSR= view.findViewById(R.id.txtDSR);
        activity = context;
        mListener = onIsOfflineDSRListener;
    }

    public void bind(final QuestionBean item, int number) {
        bean = item;
        String qLabel = number + ". " + bean.getQuestion_label();
        mQuestionLabel.setText(qLabel);
        mListener.viewTextValueDsr(txtDSR);
        mListener.viewButtonGetDsr(btnGetDSR);

        ansInstallment = answerData(Global.REF_PRE_ANGSURAN);
        ansSalary = answerData(Global.REF_PRE_PENGHASILAN);
        ansSalarySpouse = answerData(Global.REF_SVY_PGHSL_PSGN);
        ansInstallmentWOM = answerData(Global.REF_INSTALLMENT_WOM);
        ansInstallmentOther = answerData(Global.REF_INSTALLMENT_OTHER);

        String parameterNow = ansInstallment+"@"+ansSalary+"@"+ansSalarySpouse+"@"+ansInstallmentWOM+"@"+ansInstallmentOther;
        if (bean.getHistoryParamsCalculateDsr() != null) {
            if (parameterNow.equals(bean.getHistoryParamsCalculateDsr())) {

            } else {
                bean.setHistoryParamsCalculateDsr(parameterNow);
                bean.setAnswer("0");
                bean.setCountRetry(1);
            }
        } else {
            bean.setHistoryParamsCalculateDsr(parameterNow);
        }

        txtResultDSR.setVisibility(View.GONE);
        if (StringUtils.isNotBlank(bean.getAnswer())) {
            String[] answer = bean.getAnswer().split(Global.DELIMETER_DATA4);
            txtDSR.setText(answer[0]);
            if (answer.length > 1) {
                txtResultDSR.setText(answer[1]);
                txtResultDSR.setVisibility(View.VISIBLE);
            }
        } else {
            bean.setAnswer("0");
            txtDSR.setText("0");
            txtResultDSR.setText("");
        }
        if (bean.getCountRetry() < bean.getMaxRetry() + 1) {
            btnGetDSR.setEnabled(true);
        }

        // For conditional offline mode
        String msg = activity.getString(R.string.offline_mode);
        if (!Tool.isInternetconnected(activity)) { // Offline Mode
            if (StringUtils.isNotBlank(bean.getAnswer())) {
                String[] answer = bean.getAnswer().split(Global.DELIMETER_DATA4);
                if (answer.length > 1) {
                    if (answer[1].equalsIgnoreCase(activity.getString(R.string.offline_mode))) {
                        bean.setAnswer(answer[0] + Global.DELIMETER_DATA4 + msg);
                        mListener.showOfflineMode(txtResultDSR, msg);
                    }
                } else {
                    bean.setAnswer(bean.getAnswer() + Global.DELIMETER_DATA4 + msg);
                    mListener.showOfflineMode(txtResultDSR, msg);
                }
            }
        }

        btnGetDSR.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                txtResultDSR.setText("");
                bean.setCountRetry(bean.getCountRetry() + 1);

                if (!Tool.isInternetconnected(activity)) { // Offline Mode
                    String getDsrOffline = String.format(Locale.US, "%.2f", calculateDSRForOffline() * 100);
                    txtDSR.setText(getDsrOffline);
                    String message = activity.getString(R.string.message_offline_mode_dsr);
                    bean.setAnswer(getDsrOffline + Global.DELIMETER_DATA4 + message);
                    mListener.showOfflineMode(txtResultDSR, message);
                    if (bean.getCountRetry() >= bean.getMaxRetry()) {
                        String msg = message+"\n"+ activity.getString(R.string.message_max_retry_reached);
                        txtResultDSR.setText(msg);
                    } else {
                        txtResultDSR.setText(message);
                    }
                    txtResultDSR.setVisibility(View.VISIBLE);
                } else { // Online Mode
                    calculateDSRForOnline();
                }

                if (bean.getCountRetry() >= bean.getMaxRetry()) {
                    btnGetDSR.setEnabled(false);
                }
            }
        });

    }

    private String answerData(String refId) {
        QuestionBean qBean = Constant.listOfQuestion.get(refId);
        if (qBean != null) {
            if (qBean.getAnswer() == null || qBean.getAnswer().equals("")) {
                return "0";
            } else {
                return qBean.getAnswer();
            }
        } else {
            return "0";
        }
    }

    private void calculateDSRForOnline() {
        GetDSRTask task = new GetDSRTask(activity);
        task.execute();
    }

    private double calculateDSRForOffline() {
        long installments = 0;
        long salary = 0;
        long salary_spouse = 0;

        String choiceFilter = bean.getChoice_filter().replace("{", "").replace("}", "");
        String[] listChoiceFilter = choiceFilter.split(",");
        for (String s : listChoiceFilter) {
            QuestionBean qBean = Constant.listOfQuestion.get(s);
            assert qBean != null;
            if (qBean.getIdentifier_name().equalsIgnoreCase(Global.REF_PRE_PENGHASILAN)) {
                if (qBean.getAnswer() == null || qBean.getAnswer().equals("")) {
                    salary = 0;
                } else {
                    salary = Long.parseLong(qBean.getAnswer());
                }
            } else if (qBean.getIdentifier_name().equalsIgnoreCase(Global.REF_SVY_PGHSL_PSGN)) {
                if (qBean.getAnswer() == null || qBean.getAnswer().equals("")) {
                    salary_spouse = 0;
                } else {
                    salary_spouse = Long.parseLong(qBean.getAnswer());
                }
            } else if (qBean.getIdentifier_name().equalsIgnoreCase(Global.REF_PRE_ANGSURAN)) {
                if (qBean.getAnswer() == null || qBean.getAnswer().equals("")) {
                    installments = 0;
                } else {
                    installments = Long.parseLong(qBean.getAnswer());
                }
            }
        }

        QuestionBean beanInstallmentWOM = Constant.listOfQuestion.get(Global.REF_INSTALLMENT_WOM);
        long installmentWOM;
        if (beanInstallmentWOM != null) {
            if (beanInstallmentWOM.getAnswer() == null || beanInstallmentWOM.getAnswer().equals("")) {
                installmentWOM = 0;
            } else {
                installmentWOM = Long.parseLong(beanInstallmentWOM.getAnswer());
            }
        } else {
            installmentWOM = 0;
        }

        QuestionBean beanInstallmentOther = Constant.listOfQuestion.get(Global.REF_INSTALLMENT_OTHER);
        long installmentOther;
        if (beanInstallmentOther != null) {
            if (beanInstallmentOther.getAnswer() == null || beanInstallmentOther.getAnswer().equals("")) {
                installmentOther = 0;
            } else {
                installmentOther = Long.parseLong(beanInstallmentOther.getAnswer());
            }
        } else {
            installmentOther = 0;
        }

        String parameterNow = installments+"@"+salary+"@"+salary_spouse+"@"+installmentWOM+"@"+installmentOther;
        bean.setHistoryParamsCalculateDsr(parameterNow);

        return (double) (installments + installmentWOM + installmentOther)/(salary + salary_spouse);
    }

     /**
     * Async task Class for Get DSR
     */
    @SuppressLint("StaticFieldLeak")
    private class GetDSRTask extends AsyncTask<Void, Void, String> {
        Context context;
        private String message;
        private ProgressDialog dialog;

        public GetDSRTask (Activity activity) {
            context = activity;
            message = "";
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            dialog = ProgressDialog.show(activity, "",
                    activity.getString(R.string.progressWait), true, false);
        }

        @Override
        protected String doInBackground(Void... voids) {
            try {
                if (Tool.isInternetconnected(context)) {
                    GetDSRRequest request = new GetDSRRequest();
                    request.setAudit(GlobalData.getSharedGlobalData().getAuditData());
                    request.setUuidTaskH(DynamicFormActivity.header.getUuid_task_h());

                    Scheme form = SchemeDataAccess.getOne(context, bean.getUuid_scheme());
                    request.setFormName(form.getScheme_description());

                    String choiceFilter = bean.getChoice_filter();
                    choiceFilter = choiceFilter.replaceAll("\\{", "");
                    choiceFilter = choiceFilter.replaceAll("\\}", "");
                    Map<String, String> mapFilter = new HashMap<>();
                    if (choiceFilter != null && !"".equalsIgnoreCase(choiceFilter)) {
                        String [] identifierFilters = choiceFilter.split(",");
                        for (int i=0; i<identifierFilters.length; i++) {
                            String identifier = identifierFilters[i];
                            QuestionBean qBean = Constant.listOfQuestion.get(identifier);
                            if (qBean != null) {
                                String answer = QuestionBean.getAnswer(qBean);
                                if (answer == null) {
                                    answer = "";
                                }
                                if (StringUtils.isNotBlank(bean.getAnswer()) && StringUtils.contains(bean.getAnswer(), Global.DELIMETER_DATA4)) {
                                    answer = answer + Global.DELIMETER_DATA4 + qBean.getIs_readonly();
                                } else {
                                    answer = answer + Global.DELIMETER_DATA4 + "0";
                                }
                                mapFilter.put(identifier, answer);
                            }
                        }
                        request.setFilter(mapFilter);
                    }

                    String json = GsonHelper.toJson(request);
                    String url = GlobalData.getSharedGlobalData().getURL_GET_DSR();
                    boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                    boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                    HttpCryptedConnection httpConn = new HttpCryptedConnection(activity,
                            encrypt, decrypt);
                    HttpConnectionResult serverResult = null;
                    try {
                        serverResult = httpConn.requestToServer(url, json,
                                Global.DEFAULTCONNECTIONTIMEOUT);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    if (serverResult != null) {
                        if (serverResult.isOK()) {
                            return serverResult.getResult();
                        } else {
                            message = context.getString(R.string.message_online_mode_dsr_failed);
                            return message;
                        }
                    }

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            return null;
        }

        @Override
        protected void onPostExecute(String result) {
            super.onPostExecute(result);
            String answer = txtDSR.getText().toString();
            if (dialog!= null && dialog.isShowing()) {
                dialog.dismiss();
            }
            if (StringUtils.isBlank(message)) {
                GetDSRResponse response = GsonHelper.fromJson(result, GetDSRResponse.class);
                if (response != null) {
                    if (response.getStatus().getCode()==0) {
                        boolean isChange = false;
                        if (StringUtils.isNotBlank(response.getValue()) && Double.parseDouble(response.getValue()) > 0) {
                            answer = String.format(Locale.US, "%.2f", Double.parseDouble(response.getValue()));
                            txtDSR.setText(answer);
                        }

                        if (response.getMapReadOnly() != null && !response.getMapReadOnly().isEmpty()) {
                            for (Map.Entry<String, Object> mapElement : response.getMapReadOnly().entrySet()) {
                                String refId = mapElement.getKey();
                                String isReadonly = mapElement.getValue().toString();
                                if (StringUtils.isNotBlank(refId) && StringUtils.isNotBlank(isReadonly)) {
                                    QuestionBean questionBean = Constant.listOfQuestion.get(refId);
                                    if (questionBean != null && !isReadonly.equalsIgnoreCase(questionBean.getIs_readonly())) {
                                        questionBean.setIs_readonly(isReadonly);
                                        isChange = true;
                                    }
                                }
                            }

                            android.os.Message messageIsChange = new android.os.Message();
                            Bundle bundle = new Bundle();
                            bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.RESULT_FROM_GET_DSR);
                            bundle.putBoolean("isChange", isChange);
                            messageIsChange.setData(bundle);
                            FragmentQuestion.questionHandler.sendMessage(messageIsChange);
                        }
                        message = context.getString(R.string.message_online_mode_dsr_success);
                    } else {
                        answer = String.format(Locale.US, "%.2f", calculateDSRForOffline() * 100);
                        txtDSR.setText(answer);
                        bean.setAnswer(answer + Global.DELIMETER_DATA4 + message);
                        mListener.showOfflineMode(txtResultDSR, message);
                        txtResultDSR.setVisibility(View.VISIBLE);
                    }
                }
            } else {
                answer = String.format(Locale.US, "%.2f", calculateDSRForOffline() * 100);
                txtDSR.setText(answer);
                bean.setAnswer(answer + Global.DELIMETER_DATA4 + message);
            }

            if (bean.getCountRetry() >= bean.getMaxRetry()) {
                String msg = message+"\n"+ context.getString(R.string.message_max_retry_reached);
                txtResultDSR.setText(msg);
            } else {
                txtResultDSR.setText(message);
            }
            txtResultDSR.setVisibility(View.VISIBLE);

            if (StringUtils.isNotBlank(answer)) {
                answer = answer + Global.DELIMETER_DATA4 + message;
                bean.setAnswer(answer);
            }

            long installments = 0;
            long salary = 0;
            long salary_spouse = 0;
            String choiceFilter = bean.getChoice_filter().replace("{", "").replace("}", "");
            String[] listChoiceFilter = choiceFilter.split(",");
            for (String s : listChoiceFilter) {
                QuestionBean qBean = Constant.listOfQuestion.get(s);
                assert qBean != null;
                if (qBean.getIdentifier_name().equalsIgnoreCase(Global.REF_PRE_PENGHASILAN)) {
                    if (qBean.getAnswer() == null || qBean.getAnswer().equals("")) {
                        salary = 0;
                    } else {
                        salary = Long.parseLong(qBean.getAnswer());
                    }
                } else if (qBean.getIdentifier_name().equalsIgnoreCase(Global.REF_SVY_PGHSL_PSGN)) {
                    if (qBean.getAnswer() == null || qBean.getAnswer().equals("")) {
                        salary_spouse = 0;
                    } else {
                        salary_spouse = Long.parseLong(qBean.getAnswer());
                    }
                } else if (qBean.getIdentifier_name().equalsIgnoreCase(Global.REF_PRE_ANGSURAN)) {
                    if (qBean.getAnswer() == null || qBean.getAnswer().equals("")) {
                        installments = 0;
                    } else {
                        installments = Long.parseLong(qBean.getAnswer());
                    }
                }
            }

            QuestionBean beanInstallmentWOM = Constant.listOfQuestion.get(Global.REF_INSTALLMENT_WOM);
            long installmentWOM;
            if (beanInstallmentWOM != null) {
                if (beanInstallmentWOM.getAnswer() == null || beanInstallmentWOM.getAnswer().equals("")) {
                    installmentWOM = 0;
                } else {
                    installmentWOM = Long.parseLong(beanInstallmentWOM.getAnswer());
                }
            } else {
                installmentWOM = 0;
            }

            QuestionBean beanInstallmentOther = Constant.listOfQuestion.get(Global.REF_INSTALLMENT_OTHER);
            long installmentOther;
            if (beanInstallmentOther != null) {
                if (beanInstallmentOther.getAnswer() == null || beanInstallmentOther.getAnswer().equals("")) {
                    installmentOther = 0;
                } else {
                    installmentOther = Long.parseLong(beanInstallmentOther.getAnswer());
                }
            } else {
                installmentOther = 0;
            }

            String parameterNow = installments+"@"+salary+"@"+salary_spouse+"@"+installmentWOM+"@"+installmentOther;
            bean.setHistoryParamsCalculateDsr(parameterNow);
        }
    }

}
