package com.adins.mss.dao;

import java.util.List;
import java.util.ArrayList;
import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.SqlUtils;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;
import de.greenrobot.dao.query.Query;
import de.greenrobot.dao.query.QueryBuilder;

import com.adins.mss.dao.Timeline;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TR_TIMELINE".
*/
public class TimelineDao extends AbstractDao<Timeline, String> {

    public static final String TABLENAME = "TR_TIMELINE";

    /**
     * Properties of entity Timeline.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_timeline = new Property(0, String.class, "uuid_timeline", true, "UUID_TIMELINE");
        public final static Property Description = new Property(1, String.class, "description", false, "DESCRIPTION");
        public final static Property Latitude = new Property(2, String.class, "latitude", false, "LATITUDE");
        public final static Property Longitude = new Property(3, String.class, "longitude", false, "LONGITUDE");
        public final static Property Dtm_crt_server = new Property(4, java.util.Date.class, "dtm_crt_server", false, "DTM_CRT_SERVER");
        public final static Property Usr_crt = new Property(5, String.class, "usr_crt", false, "USR_CRT");
        public final static Property Dtm_crt = new Property(6, java.util.Date.class, "dtm_crt", false, "DTM_CRT");
        public final static Property Uuid_task_h = new Property(7, String.class, "uuid_task_h", false, "UUID_TASK_H");
        public final static Property Uuid_user = new Property(8, String.class, "uuid_user", false, "UUID_USER");
        public final static Property Uuid_timeline_type = new Property(9, String.class, "uuid_timeline_type", false, "UUID_TIMELINE_TYPE");
        public final static Property Uuid_message = new Property(10, String.class, "uuid_message", false, "UUID_MESSAGE");
        public final static Property Byte_image = new Property(11, byte[].class, "byte_image", false, "BYTE_IMAGE");
    };

    private DaoSession daoSession;

    private Query<Timeline> user_TimelineListQuery;
    private Query<Timeline> timelineType_TimelineListQuery;
    private Query<Timeline> message_TimelineListQuery;
    private Query<Timeline> taskH_TimelineListQuery;

    public TimelineDao(DaoConfig config) {
        super(config);
    }
    
    public TimelineDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TR_TIMELINE\" (" + //
                "\"UUID_TIMELINE\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_timeline
                "\"DESCRIPTION\" TEXT," + // 1: description
                "\"LATITUDE\" TEXT," + // 2: latitude
                "\"LONGITUDE\" TEXT," + // 3: longitude
                "\"DTM_CRT_SERVER\" INTEGER," + // 4: dtm_crt_server
                "\"USR_CRT\" TEXT," + // 5: usr_crt
                "\"DTM_CRT\" INTEGER," + // 6: dtm_crt
                "\"UUID_TASK_H\" TEXT," + // 7: uuid_task_h
                "\"UUID_USER\" TEXT," + // 8: uuid_user
                "\"UUID_TIMELINE_TYPE\" TEXT," + // 9: uuid_timeline_type
                "\"UUID_MESSAGE\" TEXT," + // 10: uuid_message
                "\"BYTE_IMAGE\" BLOB);"); // 11: byte_image
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TR_TIMELINE\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, Timeline entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_timeline());
 
        String description = entity.getDescription();
        if (description != null) {
            stmt.bindString(2, description);
        }
 
        String latitude = entity.getLatitude();
        if (latitude != null) {
            stmt.bindString(3, latitude);
        }
 
        String longitude = entity.getLongitude();
        if (longitude != null) {
            stmt.bindString(4, longitude);
        }
 
        java.util.Date dtm_crt_server = entity.getDtm_crt_server();
        if (dtm_crt_server != null) {
            stmt.bindLong(5, dtm_crt_server.getTime());
        }
 
        String usr_crt = entity.getUsr_crt();
        if (usr_crt != null) {
            stmt.bindString(6, usr_crt);
        }
 
        java.util.Date dtm_crt = entity.getDtm_crt();
        if (dtm_crt != null) {
            stmt.bindLong(7, dtm_crt.getTime());
        }
 
        String uuid_task_h = entity.getUuid_task_h();
        if (uuid_task_h != null) {
            stmt.bindString(8, uuid_task_h);
        }
 
        String uuid_user = entity.getUuid_user();
        if (uuid_user != null) {
            stmt.bindString(9, uuid_user);
        }
 
        String uuid_timeline_type = entity.getUuid_timeline_type();
        if (uuid_timeline_type != null) {
            stmt.bindString(10, uuid_timeline_type);
        }
 
        String uuid_message = entity.getUuid_message();
        if (uuid_message != null) {
            stmt.bindString(11, uuid_message);
        }
 
        byte[] byte_image = entity.getByte_image();
        if (byte_image != null) {
            stmt.bindBlob(12, byte_image);
        }
    }

    @Override
    protected void attachEntity(Timeline entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public Timeline readEntity(Cursor cursor, int offset) {
        Timeline entity = new Timeline( //
            cursor.getString(offset + 0), // uuid_timeline
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // description
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // latitude
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // longitude
            cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)), // dtm_crt_server
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // usr_crt
            cursor.isNull(offset + 6) ? null : new java.util.Date(cursor.getLong(offset + 6)), // dtm_crt
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7), // uuid_task_h
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8), // uuid_user
            cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9), // uuid_timeline_type
            cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10), // uuid_message
            cursor.isNull(offset + 11) ? null : cursor.getBlob(offset + 11) // byte_image
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, Timeline entity, int offset) {
        entity.setUuid_timeline(cursor.getString(offset + 0));
        entity.setDescription(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setLatitude(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setLongitude(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setDtm_crt_server(cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)));
        entity.setUsr_crt(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setDtm_crt(cursor.isNull(offset + 6) ? null : new java.util.Date(cursor.getLong(offset + 6)));
        entity.setUuid_task_h(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
        entity.setUuid_user(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
        entity.setUuid_timeline_type(cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9));
        entity.setUuid_message(cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10));
        entity.setByte_image(cursor.isNull(offset + 11) ? null : cursor.getBlob(offset + 11));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(Timeline entity, long rowId) {
        return entity.getUuid_timeline();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(Timeline entity) {
        if(entity != null) {
            return entity.getUuid_timeline();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
    /** Internal query to resolve the "timelineList" to-many relationship of User. */
    public List<Timeline> _queryUser_TimelineList(String uuid_user) {
        synchronized (this) {
            if (user_TimelineListQuery == null) {
                QueryBuilder<Timeline> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_user.eq(null));
                user_TimelineListQuery = queryBuilder.build();
            }
        }
        Query<Timeline> query = user_TimelineListQuery.forCurrentThread();
        query.setParameter(0, uuid_user);
        return query.list();
    }

    /** Internal query to resolve the "timelineList" to-many relationship of TimelineType. */
    public List<Timeline> _queryTimelineType_TimelineList(String uuid_timeline_type) {
        synchronized (this) {
            if (timelineType_TimelineListQuery == null) {
                QueryBuilder<Timeline> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_timeline_type.eq(null));
                timelineType_TimelineListQuery = queryBuilder.build();
            }
        }
        Query<Timeline> query = timelineType_TimelineListQuery.forCurrentThread();
        query.setParameter(0, uuid_timeline_type);
        return query.list();
    }

    /** Internal query to resolve the "timelineList" to-many relationship of Message. */
    public List<Timeline> _queryMessage_TimelineList(String uuid_message) {
        synchronized (this) {
            if (message_TimelineListQuery == null) {
                QueryBuilder<Timeline> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_message.eq(null));
                message_TimelineListQuery = queryBuilder.build();
            }
        }
        Query<Timeline> query = message_TimelineListQuery.forCurrentThread();
        query.setParameter(0, uuid_message);
        return query.list();
    }

    /** Internal query to resolve the "timelineList" to-many relationship of TaskH. */
    public List<Timeline> _queryTaskH_TimelineList(String uuid_task_h) {
        synchronized (this) {
            if (taskH_TimelineListQuery == null) {
                QueryBuilder<Timeline> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_task_h.eq(null));
                taskH_TimelineListQuery = queryBuilder.build();
            }
        }
        Query<Timeline> query = taskH_TimelineListQuery.forCurrentThread();
        query.setParameter(0, uuid_task_h);
        return query.list();
    }

    private String selectDeep;

    protected String getSelectDeep() {
        if (selectDeep == null) {
            StringBuilder builder = new StringBuilder("SELECT ");
            SqlUtils.appendColumns(builder, "T", getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T0", daoSession.getUserDao().getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T1", daoSession.getTimelineTypeDao().getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T2", daoSession.getMessageDao().getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T3", daoSession.getTaskHDao().getAllColumns());
            builder.append(" FROM TR_TIMELINE T");
            builder.append(" LEFT JOIN MS_USER T0 ON T.\"UUID_USER\"=T0.\"UUID_USER\"");
            builder.append(" LEFT JOIN MS_TIMELINETYPE T1 ON T.\"UUID_TIMELINE_TYPE\"=T1.\"UUID_TIMELINE_TYPE\"");
            builder.append(" LEFT JOIN TR_MESSAGE T2 ON T.\"UUID_MESSAGE\"=T2.\"UUID_MESSAGE\"");
            builder.append(" LEFT JOIN TR_TASK_H T3 ON T.\"UUID_TASK_H\"=T3.\"UUID_TASK_H\"");
            builder.append(' ');
            selectDeep = builder.toString();
        }
        return selectDeep;
    }
    
    protected Timeline loadCurrentDeep(Cursor cursor, boolean lock) {
        Timeline entity = loadCurrent(cursor, 0, lock);
        int offset = getAllColumns().length;

        User user = loadCurrentOther(daoSession.getUserDao(), cursor, offset);
        entity.setUser(user);
        offset += daoSession.getUserDao().getAllColumns().length;

        TimelineType timelineType = loadCurrentOther(daoSession.getTimelineTypeDao(), cursor, offset);
        entity.setTimelineType(timelineType);
        offset += daoSession.getTimelineTypeDao().getAllColumns().length;

        Message message = loadCurrentOther(daoSession.getMessageDao(), cursor, offset);
        entity.setMessage(message);
        offset += daoSession.getMessageDao().getAllColumns().length;

        TaskH taskH = loadCurrentOther(daoSession.getTaskHDao(), cursor, offset);
        entity.setTaskH(taskH);

        return entity;    
    }

    public Timeline loadDeep(Long key) {
        assertSinglePk();
        if (key == null) {
            return null;
        }

        StringBuilder builder = new StringBuilder(getSelectDeep());
        builder.append("WHERE ");
        SqlUtils.appendColumnsEqValue(builder, "T", getPkColumns());
        String sql = builder.toString();
        
        String[] keyArray = new String[] { key.toString() };
        Cursor cursor = db.rawQuery(sql, keyArray);
        
        try {
            boolean available = cursor.moveToFirst();
            if (!available) {
                return null;
            } else if (!cursor.isLast()) {
                throw new IllegalStateException("Expected unique result, but count was " + cursor.getCount());
            }
            return loadCurrentDeep(cursor, true);
        } finally {
            cursor.close();
        }
    }
    
    /** Reads all available rows from the given cursor and returns a list of new ImageTO objects. */
    public List<Timeline> loadAllDeepFromCursor(Cursor cursor) {
        int count = cursor.getCount();
        List<Timeline> list = new ArrayList<Timeline>(count);
        
        if (cursor.moveToFirst()) {
            if (identityScope != null) {
                identityScope.lock();
                identityScope.reserveRoom(count);
            }
            try {
                do {
                    list.add(loadCurrentDeep(cursor, false));
                } while (cursor.moveToNext());
            } finally {
                if (identityScope != null) {
                    identityScope.unlock();
                }
            }
        }
        return list;
    }
    
    protected List<Timeline> loadDeepAllAndCloseCursor(Cursor cursor) {
        try {
            return loadAllDeepFromCursor(cursor);
        } finally {
            cursor.close();
        }
    }
    

    /** A raw-style query where you can pass any WHERE clause and arguments. */
    public List<Timeline> queryDeep(String where, String... selectionArg) {
        Cursor cursor = db.rawQuery(getSelectDeep() + where, selectionArg);
        return loadDeepAllAndCloseCursor(cursor);
    }
 
}
