package com.adins.mss.foundation.db.dataaccess;

import android.content.Context;
import android.database.Cursor;
import android.util.Log;

import com.adins.mss.base.commons.Query;
import com.adins.mss.base.dynamicform.Constant;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.dao.DaoSession;
import com.adins.mss.dao.Lookup;
import com.adins.mss.dao.ProductOffering;
import com.adins.mss.dao.ProductOfferingDao;
import com.adins.mss.foundation.db.DaoOpenHelper;
import com.adins.mss.foundation.questiongenerator.OptionAnswerBean;
import com.adins.mss.foundation.questiongenerator.QuestionBean;

import java.util.ArrayList;
import java.util.List;

import de.greenrobot.dao.query.QueryBuilder;
import de.greenrobot.dao.query.WhereCondition;

/**
 * Created by developer on 1/18/18.
 */

public class PODataAccess {

    /**
     * use to generate dao session that you can access modelDao
     *
     * @param context --> context from activity
     * @return
     */
    protected static DaoSession getDaoSession(Context context) {
        return DaoOpenHelper.getDaoSession(context);
    }

    /**
     * get PODao and you can access db
     */
    protected static ProductOfferingDao getPoDao(Context context) {
        return getDaoSession(context).getProductOfferingDao();
    }

    /**
     * add PO as entity
     */
    public static void add(Context context, ProductOffering entity) {
        getPoDao(context).insert(entity);
        getDaoSession(context).clear();
    }

    /**
     * add PO as list
     */
    public static void add(Context context, List<ProductOffering> entities) {
        getPoDao(context).insertInTx(entities);
        getDaoSession(context).clear();
    }

    public static void addOrReplace(Context context, ProductOffering entity) {
        getPoDao(context).insertOrReplace(entity);
        getDaoSession(context).clear();
    }

    public static void addOrReplace(Context context, List<ProductOffering> entities) {
        getPoDao(context).insertOrReplaceInTx(entities);
        getDaoSession(context).clear();
    }

    /**
     * Action for Delete
     */
    public static void delete(Context context, ProductOffering entity) {
        getPoDao(context).delete(entity);
        getDaoSession(context).clear();
    }

    public static void clean(Context context) {
        getPoDao(context).deleteAll();
        getDaoSession(context).clear();
    }

    /**
     * Get One Entity by Id
     * @param context
     * @param id
     * @return
     */
    public static ProductOffering getOne(Context context, int id) {
        QueryBuilder<ProductOffering> qb = getPoDao(context).queryBuilder();
        qb.where(ProductOfferingDao.Properties.Id.eq(id));
        qb.build();

        if (qb.list().isEmpty()) return null;
        else return qb.list().get(0);
    }

    /**
     * Get Entity as Lookup
     */
    public static Lookup getOneAsLookup(Context context, QuestionBean qBean, long id) {
        QueryBuilder<ProductOffering> qb = getPoDao(context).queryBuilder();
        qb.where(ProductOfferingDao.Properties.Id.eq(id));
        qb.limit(1);
        qb.build();

        Lookup lookup = null;
        int size = qb.list().size();
        if (size != 0) {
            ProductOffering po = qb.list().get(0);
            lookup = new Lookup(String.valueOf(po.getId()));
            Query query = GsonHelper.fromJson(qBean.getChoice_filter(), Query.class);

            if (query.getCode().equalsIgnoreCase("PROD_OFF_ID"))
            {
                lookup.setCode(String.valueOf(po.getProd_off_id()));
                lookup.setValue(String.valueOf(po.getProd_off_name()));

                //Set Constant Product Offering
                Constant.productOff = po;
            }
            else if (query.getCode().equals("PROD_CAT_CODE"))
            {
                lookup.setCode(String.valueOf(po.getProd_cat_code()));
                lookup.setValue(String.valueOf(po.getProd_cat_name()));
            }
            else if (query.getCode().equals("COMPONENT"))
            {
                lookup.setCode(po.getComponent());
                lookup.setValue(po.getComponent());
            }

            lookup.setOption_id(String.valueOf(po.getId()));
            lookup.setUuid_lookup(String.valueOf(po.getId()));
        }

        return lookup;
    }

    public static Lookup getOneAsLookup(Context context, QuestionBean qBean, String keyword, String lov) {
        QueryBuilder<ProductOffering> qb = getPoDao(context).queryBuilder();
        qb.where(ProductOfferingDao.Properties.Prod_off_name.eq(keyword),
                ProductOfferingDao.Properties.Prod_off_id.eq(lov));
        qb.limit(1);
        qb.build();

        Lookup lookup = null;
        int size = qb.list().size();
        if (size != 0) {
            ProductOffering po = qb.list().get(0);
            lookup = new Lookup(String.valueOf(po.getId()));
            Query query = GsonHelper.fromJson(qBean.getChoice_filter(), Query.class);

            if (query.getCode().equalsIgnoreCase("PROD_OFF_ID"))
            {
                lookup.setCode(String.valueOf(po.getProd_off_id()));
                lookup.setValue(String.valueOf(po.getProd_off_name()));
            }

            lookup.setOption_id(String.valueOf(po.getId()));
            lookup.setUuid_lookup(String.valueOf(po.getId()));

            //Set Constant Product Offering
            Constant.productOff = po;
        }

        return lookup;
    }

    /**
     * get All entities
     * @param context
     * @return
     */
    public static List<ProductOffering> all(Context context) {
        List<ProductOffering> data = getPoDao(context).loadAll();

        if (!data.isEmpty()) return data;
        else return null;
    }

    public static List<OptionAnswerBean> lookup(Context context, Query ql) {
        List<OptionAnswerBean> answerBeans = new ArrayList<>();
//        String query  = "SELECT t1.ID, t1.PROD_OFF_ID, t1.PROD_OFF_NAME FROM MS_PO t1 LEFT JOIN MS_PO_ASSET t2 ON t1.PROD_OFF_ID = t2.PROD_OFF_ID LEFT JOIN MS_PO_DEALER t3 ON t2.PROD_OFF_ID = t3.PROD_OFF_ID WHERE %s";
        String query  = "SELECT MS_PO.ID, MS_PO.PROD_OFF_ID, MS_PO.PROD_OFF_NAME FROM MS_PO LEFT JOIN MS_ASSET_SCHEME ON MS_PO.ASSET_SCHEME_ID = MS_ASSET_SCHEME.ASSET_SCHEME_ID LEFT JOIN MS_PO_ASSET ON MS_PO.ASSET_SCHEME_ID = MS_PO_ASSET.ASSET_SCHEME_ID LEFT JOIN MS_PO_DEALER ON MS_PO.DEALER_SCHEME_ID = MS_PO_DEALER.DEALER_SCHEME_ID WHERE %s";

        if (ql.getCode().equals("PROD_CAT_CODE")) {
            query  = "SELECT MS_PO.ID, MS_PO.PROD_CAT_NAME, MS_PO.PROD_CAT_CODE FROM MS_PO WHERE %s";
        } else if (ql.getCode().equals("COMPONENT")) {
            query  = "SELECT MS_PO.ID, MS_PO.COMPONENT FROM MS_PO WHERE %s";
        }

        StringBuilder constraint = new StringBuilder("1");
//        String subQuery = "PROD_OFF_ID IN (SELECT PROD_OFF_ID FROM MS_PO_ASSET WHERE %s)";
        String groupBy  = " GROUP BY ";

        if (ql.getConstraint() != null) {
            List<Query.Constraint> constraints = ql.getConstraint();
            for (int i = 0; i < constraints.size(); i++) {
                constraint.append(" AND ")
                        .append(constraints.get(i).getColumn());

                //NENDI: 2018-04-10 | Add Operator on Query
                if (constraints.get(i).getOperator() != null) {
                    constraint.append(" ").append(constraints.get(i).getOperator().trim()).append(" ");
                    if (constraints.get(i).getOperator().toLowerCase().contains("like")) {
                        constraint.append("'%"+constraints.get(i).getValue()+"%'");
                    }
                    else if (constraints.get(i).getOperator().toLowerCase().contains("in")) {
                        constraint.append("(");
                        constraint.append(constraints.get(i).getValue());
                        constraint.append(")");
                    }
                    else {
                        constraint.append("'"+constraints.get(i).getValue()+"'");
                    }
                } else {
                    constraint.append("=");
                    constraint.append("'"+constraints.get(i).getValue()+"'");
                }
            }
        }

        if (ql.getGroup() != null) constraint.append(groupBy)
            .append(ql.getGroup());
//        else constraint.append(groupBy)
//            .append("MS_PO.PROD_OFF_ID");

//        subQuery = String.format(subQuery, String.valueOf(constraint));
        query = String.format(query, String.valueOf(constraint));
        Log.d(PODataAccess.class.getSimpleName(), query);

        Cursor cursor = getDaoSession(context)
                .getDatabase()
                .rawQuery(query, null);

        try {
            if (cursor.moveToFirst()) {
                if (ql.getCode().equals("PROD_CAT_CODE")) { //PROD CATEGORY
                    do {
                        OptionAnswerBean optionAnswer = new OptionAnswerBean();
                        optionAnswer.setOption_id(String.valueOf(cursor.getInt(cursor.getColumnIndex("ID"))));
                        optionAnswer.setUuid_lookup(optionAnswer.getOption_id());
                        optionAnswer.setCode(cursor.getString(cursor.getColumnIndex("PROD_CAT_CODE")));
                        optionAnswer.setValue(cursor.getString(cursor.getColumnIndex("PROD_CAT_NAME")));
                        optionAnswer.setLov_group("PROD_CAT");
                        answerBeans.add(optionAnswer);
                    } while (cursor.moveToNext());
                } else if (ql.getCode().equals("COMPONENT")) {
                    do {
                        OptionAnswerBean optionAnswer = new OptionAnswerBean();
                        optionAnswer.setOption_id(String.valueOf(cursor.getInt(cursor.getColumnIndex("ID"))));
                        optionAnswer.setUuid_lookup(optionAnswer.getOption_id());
                        optionAnswer.setCode(cursor.getString(cursor.getColumnIndex("COMPONENT")));
                        optionAnswer.setValue(cursor.getString(cursor.getColumnIndex("COMPONENT")));
                        optionAnswer.setLov_group("COMPONENT");
                        answerBeans.add(optionAnswer);
                    } while (cursor.moveToNext());
                }
                else {
                    do {
                        OptionAnswerBean optionAnswer = new OptionAnswerBean();
                        optionAnswer.setOption_id(String.valueOf(cursor.getInt(cursor.getColumnIndex("ID"))));
                        optionAnswer.setUuid_lookup(optionAnswer.getOption_id());
                        optionAnswer.setCode(cursor.getString(cursor.getColumnIndex("PROD_OFF_ID")));
                        optionAnswer.setValue(cursor.getString(cursor.getColumnIndex("PROD_OFF_NAME")));
                        optionAnswer.setLov_group("PRODUCT_OFFERING");
                        answerBeans.add(optionAnswer);
                    } while (cursor.moveToNext());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            cursor.close();
        }

        return answerBeans;
    }

//    public static List<OptionAnswerBean> lookup(Context context, Query ql, String keyword) {
//        List<OptionAnswerBean> answerBeans = new ArrayList<>();
//        StringBuilder constraint = new StringBuilder("1");
//        String subQuery = "PROD_OFF_ID IN (SELECT PROD_OFF_ID FROM MS_PO_ASSET WHERE %s)";
//        String groupBy  = " GROUP BY ";
//        String code     = ql.getCode();
//        String[] fields = ql.getFields();
//
//        if (ql.getConstraint() != null) {
//            List<Query.Constraint> constraints = ql.getConstraint();
//            for (int i = 0; i < constraints.size(); i++) {
//                constraint.append(" AND ")
//                        .append(constraints.get(i).getColumn());
//
//                //NENDI: 2018-04-10 | Add Operator on Query
//                if (constraints.get(i).getOperator() != null) {
//                    constraint.append(constraints.get(i).getOperator());
//
//                    if (constraints.get(i).getOperator().toLowerCase().contains("like")) {
//                        constraint.append("'%"+constraints.get(i).getValue()+"%'");
//                    }
//                    else if (constraints.get(i).getOperator().toLowerCase().contains("in")) {
//                        constraint.append("(");
//                        constraint.append(constraints.get(i).getValue());
//                        constraint.append(")");
//                    }
//                    else {
//                        constraint.append("'"+constraints.get(i).getValue()+"'");
//                    }
//                } else {
//                    constraint.append("=");
//                    constraint.append("'"+constraints.get(i).getValue()+"'");
//                }
////                        .append("=")
////                        .append("'"+constraints.get(i).getValue()+"'");
//            }
//        }
//
//        if (ql.getGroup() != null) constraint.append(groupBy)
//                .append(ql.getGroup());
//
//        subQuery = String.format(subQuery, String.valueOf(constraint));
//        QueryBuilder<ProductOffering> qb = getPoDao(context).queryBuilder();
//        qb.where(new WhereCondition.StringCondition(subQuery))
//                .build();
//
//        if (qb.list().size() > 0) {
//            for (ProductOffering po : qb.list()) {
//                OptionAnswerBean optionAnswer = new OptionAnswerBean();
//                if (code.equalsIgnoreCase("PROD_OFF_ID"))
//                {
//                    optionAnswer.setOption_id(String.valueOf(po.getId()));
//                    optionAnswer.setCode(String.valueOf(po.getProd_off_id()));
//                    optionAnswer.setValue(String.valueOf(po.getProd_off_name()));
//                }
//
//                optionAnswer.setLov_group("PRODUCT_OFFERING");
//                answerBeans.add(optionAnswer);
//            }
//        }
//
//        return answerBeans;
//    }

    public static List<OptionAnswerBean> lookup(Context context, Query ql, String keyword) {
        List<OptionAnswerBean> answerBeans = new ArrayList<>();
//        String query  = "SELECT t1.ID, t1.PROD_OFF_ID, t1.PROD_OFF_NAME FROM MS_PO t1 LEFT JOIN MS_PO_ASSET t2 ON t1.ASSET_SCHEME_ID = t2.ASSET_SCHEME_ID LEFT JOIN MS_PO_DEALER t3 ON t1.DEALER_SCHEME_ID = t3.DEALER_SCHEME_ID WHERE %s";
        String query  = "SELECT MS_PO.ID, MS_PO.PROD_OFF_ID, MS_PO.PROD_OFF_NAME FROM MS_PO LEFT JOIN MS_PO_ASSET ON MS_PO.ASSET_SCHEME_ID = MS_PO_ASSET.ASSET_SCHEME_ID LEFT JOIN MS_PO_DEALER ON MS_PO.DEALER_SCHEME_ID = MS_PO_DEALER.DEALER_SCHEME_ID WHERE %s";

        StringBuilder constraint = new StringBuilder("1");
        String groupBy  = " GROUP BY ";

        if (ql.getConstraint() != null) {
            List<Query.Constraint> constraints = ql.getConstraint();
            for (int i = 0; i < constraints.size(); i++) {
                constraint.append(" AND ")
                        .append(constraints.get(i).getColumn());

                //NENDI: 2018-04-10 | Add Operator on Query
                if (constraints.get(i).getOperator() != null) {
                    constraint.append(" ").append(constraints.get(i).getOperator().trim()).append(" ");
                    if (constraints.get(i).getOperator().toLowerCase().contains("like")) {
                        constraint.append("'%"+constraints.get(i).getValue()+"%'");
                    }
                    else if (constraints.get(i).getOperator().toLowerCase().contains("in")) {
                        constraint.append("(");
                        constraint.append(constraints.get(i).getValue());
                        constraint.append(")");
                    }
                    else {
                        constraint.append("'"+constraints.get(i).getValue()+"'");
                    }
                } else {
                    constraint.append("=");
                    constraint.append("'"+constraints.get(i).getValue()+"'");
                }
            }
        }

        if (keyword != null) {
            if (keyword.equalsIgnoreCase(" ") || !keyword.contains("%")) {
                constraint.append(" AND PROD_OFF_NAME LIKE '%" + keyword.trim() + "%'");
            }
        }

        if (ql.getGroup() != null) constraint.append(groupBy)
                .append(ql.getGroup());
        else constraint.append(groupBy)
                .append("MS_PO.PROD_OFF_ID");

        query = String.format(query, String.valueOf(constraint));
        Log.d(PODataAccess.class.getSimpleName(), query);

        Cursor cursor = getDaoSession(context)
                .getDatabase()
                .rawQuery(query, null);

        try {
            if (cursor.moveToFirst()) {
                do {
                    OptionAnswerBean optionAnswer = new OptionAnswerBean();
                    optionAnswer.setOption_id(String.valueOf(cursor.getInt(cursor.getColumnIndex("ID"))));
                    optionAnswer.setUuid_lookup(optionAnswer.getOption_id());
                    optionAnswer.setCode(cursor.getString(cursor.getColumnIndex("PROD_OFF_ID")));
                    optionAnswer.setValue(cursor.getString(cursor.getColumnIndex("PROD_OFF_NAME")));
                    optionAnswer.setLov_group("PRODUCT_OFFERING");
                    answerBeans.add(optionAnswer);
                } while (cursor.moveToNext());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            cursor.close();
        }

//        QueryBuilder<ProductOffering> qb = getPoDao(context).queryBuilder();
//        qb.where(new WhereCondition.StringCondition(subQuery))
//                .build();
//
//        if (qb.list().size() > 0) {
//            for (ProductOffering po : qb.list()) {
//                OptionAnswerBean optionAnswer = new OptionAnswerBean();
//                if (code.equalsIgnoreCase("PROD_OFF_ID"))
//                {
//                    optionAnswer.setOption_id(String.valueOf(po.getId()));
//                    optionAnswer.setCode(String.valueOf(po.getProd_off_id()));
//                    optionAnswer.setValue(String.valueOf(po.getProd_off_name()));
//                }
//
//                optionAnswer.setLov_group("PRODUCT_OFFERING");
//                answerBeans.add(optionAnswer);
//            }
//        }

        return answerBeans;
    }

    public static List<OptionAnswerBean> lookup(Context context, Query ql, String group, String keyword) {
        List<OptionAnswerBean> answerBeans = new ArrayList<>();
        StringBuilder constraint = new StringBuilder("1");
        String subQuery = "PROD_OFF_ID IN (SELECT PROD_OFF_ID FROM MS_PO_ASSET WHERE %s)";
        String groupBy  = " GROUP BY ";
        String code     = ql.getCode();

        if (ql.getConstraint() != null) {
            List<Query.Constraint> constraints = ql.getConstraint();
            for (int i = 0; i < constraints.size(); i++) {
                constraint.append(" AND ")
                        .append(constraints.get(i).getColumn());

                //NENDI: 2018-04-10 | Add Operator on Query
                if (constraints.get(i).getOperator() != null) {
                    constraint.append(constraints.get(i).getOperator());

                    if (constraints.get(i).getOperator().toLowerCase().contains("like")) {
                        constraint.append("'%"+constraints.get(i).getValue()+"%'");
                    } else {
                        constraint.append("'"+constraints.get(i).getValue()+"'");
                    }
                } else {
                    constraint.append("=");
                    constraint.append("'"+constraints.get(i).getValue()+"'");
                }
//                        .append("=")
//                        .append("'"+constraints.get(i).getValue()+"'");
            }
        }

        if (ql.getGroup() != null) constraint.append(groupBy)
                .append(ql.getGroup());

        subQuery = String.format(subQuery, String.valueOf(constraint));
        QueryBuilder<ProductOffering> qb = getPoDao(context).queryBuilder();
        qb.where(new WhereCondition.StringCondition(subQuery),
                ProductOfferingDao.Properties.Prod_off_name.like("%" + keyword + "%"))
                .build();

        if (!qb.list().isEmpty()) {
            for (ProductOffering po : qb.list()) {
                OptionAnswerBean optionAnswer = new OptionAnswerBean();
                if (code.equalsIgnoreCase("PROD_OFF_ID"))
                {
                    optionAnswer.setOption_id(String.valueOf(po.getId()));
                    optionAnswer.setUuid_lookup(optionAnswer.getOption_id());
                    optionAnswer.setCode(String.valueOf(po.getProd_off_id()));
                    optionAnswer.setValue(String.valueOf(po.getProd_off_name()));
                }

                optionAnswer.setLov_group("PRODUCT_OFFERING");
                answerBeans.add(optionAnswer);
            }
        }

        return answerBeans;
    }

    public static ProductOffering getLast(Context context) {
        QueryBuilder<ProductOffering> qb = getPoDao(context).queryBuilder();
        qb.orderDesc(ProductOfferingDao.Properties.Dtm_upd);
        qb.limit(1);
        qb.build();

        if (!qb.list().isEmpty()) return qb.list().get(0);
        return null;
    }

    public static void addOrUpdateAll(Context context, List<ProductOffering> transaction) {
        getPoDao(context).insertOrReplaceInTx(transaction);
        getDaoSession(context).clear();
    }
}
