package com.adins.mss.base.todolist.form;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import android.os.Handler;
import android.os.Message;
import android.text.format.DateFormat;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import com.adins.mss.base.AppContext;
import com.adins.mss.base.GlobalData;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.dynamicform.CustomerFragment;
import com.adins.mss.base.dynamicform.SurveyHeaderBean;
import com.adins.mss.base.mainmenu.MainMenuActivity;
import com.adins.mss.base.timeline.TimelineManager;
import com.adins.mss.base.todolist.ToDoList;

import com.adins.mss.base.todolist.form.helper.JsonRequestRetrieveVerificationForm;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.base.util.Utility;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.ReminderPo;
import com.adins.mss.dao.Scheme;
import com.adins.mss.dao.TaskH;
import com.adins.mss.dao.User;
import com.adins.mss.foundation.db.dataaccess.GeneralParameterDataAccess;
import com.adins.mss.foundation.db.dataaccess.ReminderPoDataAccess;
import com.adins.mss.foundation.db.dataaccess.SchemeDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskHDataAccess;
import com.adins.mss.foundation.db.dataaccess.TimelineDataAccess;
import com.adins.mss.foundation.db.dataaccess.TimelineTypeDataAccess;
import com.adins.mss.foundation.dialog.DialogManager;
import com.adins.mss.foundation.dialog.NiftyDialogBuilder;
import com.adins.mss.foundation.formatter.Formatter;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.base.R;
import com.adins.mss.base.todolist.form.JsonResponseTaskList.TaskReminderPo;
import com.adins.mss.foundation.notification.Notification;

import org.acra.ACRA;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static com.services.NotificationThread.LOG_NOTIFICATION_KEY;
import static com.services.NotificationThread.getNotificationIcon;
import static java.lang.Thread.NORM_PRIORITY;

/*
    Created by michael.wijaya 07 Apr 22
 */

public class TaskGuarantorFragment extends Fragment implements OnTaskListClickListener {
    // TODO: Customize parameter argument names
    public static ToDoList toDoList;
    public static List<TaskH> listTaskH;
    public static Spinner spinnerSearch;
    public static Handler mHandler;
    public static PriorityViewAdapter viewAdapter;
    public static TextView taskCounter;
    public static PriorityHandler handler;
    private static String param;
    private static Scheme selectedScheme;

    // TODO: Customize parameters
    private int mColumnCount = 3;

    private SwipeRefreshLayout mSwipeRefreshLayout;
    private String[] isiSearchBy;
    protected Spinner spinnerSortBy;
    private String[] listSortBy;
    protected Spinner spinnerOrderBy;
    private String[] listOrderBy;

    private RefreshBackgroundTask backgroundTask;
    private RecyclerView recyclerView;
    private boolean isPriorityOpen = false;
    //Nendi: 22/01/2018
    private String sortBy  = "UUID_TASK_H";
    private String orderBy = "ASC";

    /*Penambahan Task Reminder PO*/
    private boolean isShowNotifReminderPo = false;
    private int countNotifReminderPo = 0;

    public TaskGuarantorFragment() {
        // Required empty public constructor
    }

    @Override
    public void onPause() {
        super.onPause();
        isPriorityOpen = false;
    }

    @Override
    public void onResume() {
        super.onResume();
        isPriorityOpen = true;
        if (viewAdapter != null && listTaskH != null) {
            int pos = spinnerSearch.getSelectedItemPosition();
            getSelectedTaskH(pos);
            viewAdapter = new PriorityViewAdapter(getActivity(), listTaskH, this, param);
            recyclerView.setAdapter(viewAdapter);
            long counter = listTaskH.size();
            taskCounter.setText(getString(R.string.task_count) + String.valueOf(counter));
        }
        try {
            MainMenuActivity.setDrawerCounter();
        } catch (Exception e) {
            FireCrash.log(e);
            ACRA.getErrorReporter().putCustomData("ErrorMainMenuActivity", e.getMessage());
            ACRA.getErrorReporter().putCustomData("ErrorMainMenuActivity", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
            ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat set Drawer Counter"));
        }
        DialogManager.showTimeProviderAlert(getActivity());
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //To can't screenshoot
        if (Tool.isProdFlavour(Global.FLAVORS)) {
            getActivity().getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
        }

        try {
            param = GeneralParameterDataAccess.getOne(getActivity(), GlobalData.getSharedGlobalData().getUser().getUuid_user(), Global.GS_TASK_LAYOUT_MS).getGs_value();
        } catch (Exception e) {
            FireCrash.log(e);
            e.printStackTrace();
            ACRA.getErrorReporter().putCustomData("ErrorTaskGuarantorFragment", e.getMessage());
            ACRA.getErrorReporter().handleSilentException(new Exception("Error get param Task Guarantor Fragment" + e.getMessage()));
        }

        if (null == param) {
            param = "1";
        }

        //grab Form Task Guarantor Scheme
        selectedScheme = SchemeDataAccess.getOneSurveyTaskSchemeByFormName(getActivity().getApplicationContext(), Global.FORM_NAME_GUARANTOR);
        //show dialog in case user is not authorized for task guarantor form
        if(selectedScheme == null){
            NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(getActivity());
            if (!dialogBuilder.isShowing()) {
                dialogBuilder.withTitle("WARNING").
                        withIcon(android.R.drawable.ic_dialog_alert).
                        withMessage("You are not authorized to use Task Guarantor! Please contact your administrator!")
                        .isCancelable(true).show();
            }
        }

        mHandler = new Handler();

        toDoList = new ToDoList(getActivity().getApplicationContext());
        listTaskH = new ArrayList<>();
        try {
            listTaskH = toDoList.getListTaskTypes(getActivity().getApplicationContext(), Global.FORM_NAME_GUARANTOR);
            if(listTaskH == null){
                listTaskH = new ArrayList<>();
            }
        } catch (Exception e) {
            FireCrash.log(e);
            ACRA.getErrorReporter().putCustomData("ErrorOnCreateTaskGuarantorFragment", e.getMessage());
            ACRA.getErrorReporter().putCustomData("ErrorOnCreateTaskGuarantorFragment", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
            ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat set List TaskH"));
            if (Global.IS_DEV)
                e.printStackTrace();
        }

        viewAdapter = new PriorityViewAdapter(getActivity(), listTaskH, this, param);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_task_guarantor, container, false);

        // Set the adapter
        if (view instanceof RelativeLayout) {
            handler = new PriorityHandler();
            Context context = view.getContext();
            getActivity().getActionBar().setTitle(getString(R.string.title_mn_task_guarantor));
            MainMenuActivity.setDrawerPosition(getString(R.string.title_mn_task_guarantor));
            recyclerView = view.findViewById(R.id.listPriority);
            if (param.equalsIgnoreCase("3")) {
                recyclerView.setLayoutManager(new LinearLayoutManager(context));
            } else if (param.equalsIgnoreCase("1")) {
                recyclerView.setLayoutManager(new GridLayoutManager(context, mColumnCount));
            }
            recyclerView.setAdapter(viewAdapter);
            mSwipeRefreshLayout = view.findViewById(R.id.swiperefresh);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                mSwipeRefreshLayout.setColorSchemeColors(getResources().getColor(R.color.tv_light, getContext().getTheme()),
                        getResources().getColor(R.color.tv_normal, getContext().getTheme()),
                        getResources().getColor(R.color.tv_dark, getContext().getTheme()),
                        getResources().getColor(R.color.tv_darker, getContext().getTheme()));
            } else {
                mSwipeRefreshLayout.setColorSchemeColors(getResources().getColor(R.color.tv_light),
                        getResources().getColor(R.color.tv_normal),
                        getResources().getColor(R.color.tv_dark),
                        getResources().getColor(R.color.tv_darker));
            }
            mSwipeRefreshLayout
                    .setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
                        @Override
                        public void onRefresh() {
                            initiateRefresh(true);
                        }
                    });

            taskCounter = view.findViewById(R.id.taskCounter);
            spinnerSearch = view.findViewById(R.id.priorityViewBy);
            isiSearchBy = this.getResources().getStringArray(R.array.cbPriorityBy);
            PriorityAdapter priorityAdapter = new PriorityAdapter(getActivity(), R.layout.spinner_style, isiSearchBy);
            spinnerSearch.setAdapter(priorityAdapter);

            spinnerOrderBy = view.findViewById(R.id.orderBy);
            listOrderBy    = this.getResources().getStringArray(R.array.listOrderBy);
            SpinnerAdapter orderByAdapter = new SpinnerAdapter(getContext(), R.layout.spinner_style, listOrderBy, 1);
            spinnerOrderBy.setAdapter(orderByAdapter);
            spinnerOrderBy.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    cancelRefreshTask();
                    switch (position) {
                        case 0:
                            orderBy = Global.ORD_ASC;
                            break;
                        case 1:
                            orderBy = Global.ORD_DSC;
                            break;
                        default:
                            break;
                    }

                    fetchTaskH(spinnerSearch.getSelectedItemPosition());
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {
                    // TODO Auto-generated method stub
                }
            });

            spinnerSortBy  = view.findViewById(R.id.sortBy);
            listSortBy     = this.getResources().getStringArray(R.array.listSortByTaskCAE);
            SpinnerAdapter sortByAdapter  = new SpinnerAdapter(getContext(), R.layout.spinner_style, listSortBy, 2);
            spinnerSortBy.setAdapter(sortByAdapter);
            spinnerSortBy.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    cancelRefreshTask();
                    switch (position) {
                        case 0:
                            sortBy = Global.FIELD_TASK_ID;
                            break;
                        case 1:
                            sortBy = Global.FIELD_CUSTOMER_NAME;
                            break;
                        default:
                            break;
                    }

                    fetchTaskH(spinnerSearch.getSelectedItemPosition());
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {
                    // TODO Auto-generated method stub
                }
            });

            spinnerSearch
                    .setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {

                        @Override
                        public void onItemSelected(AdapterView<?> parent, View v,
                                                   int position, long id) {
                            fetchTaskH(position);
                        }

                        @Override
                        public void onNothingSelected(AdapterView<?> arg0) {
                            // TODO Auto-generated method stub

                        }

                    });
        }
        return view;
    }

    private void fetchTaskH(int position) {
        cancelRefreshTask();
        listTaskH = getSelectedTaskH(position);

        viewAdapter = new PriorityViewAdapter(getActivity(), listTaskH, this, param);
        recyclerView.setAdapter(viewAdapter);

        long counter = listTaskH.size();
        taskCounter.setText(getString(R.string.task_count) + String.valueOf(counter));
    }

    private List<TaskH> getSelectedTaskH(int position) {
        if (selectedScheme != null) {
            listTaskH.clear();
            try{
                switch(position){
                    case 0:
                        listTaskH.addAll(toDoList.getListTaskInPriority(ToDoList.SEARCH_BY_ALL, "", selectedScheme.getUuid_scheme(), sortBy, orderBy));
                        break;
                    case 1:
                        listTaskH.addAll(toDoList.getListTaskInHighPriority(selectedScheme.getUuid_scheme(), sortBy, orderBy));
                        break;
                    case 2:
                        listTaskH.addAll(toDoList.getListTaskInNormalPriority(selectedScheme.getUuid_scheme(), sortBy, orderBy));
                        break;
                    case 3:
                        listTaskH.addAll(toDoList.getListTaskInLowPriority(selectedScheme.getUuid_scheme(), sortBy, orderBy));
                        break;
                    default:
                        Toast.makeText(getActivity().getApplicationContext(), "Something is wrong with the spinner!", Toast.LENGTH_SHORT).show();
                        break;
                }
                if (listTaskH.isEmpty()) {
                    return listTaskH;
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        return listTaskH;
    }

    private void initiateRefresh(boolean getDataFromServer) {
        cancelRefreshTask();
        backgroundTask = new RefreshBackgroundTask(getDataFromServer);
        backgroundTask.execute();
    }

    private void cancelRefreshTask() {
        if (backgroundTask != null) {
            backgroundTask.cancel(true);
            backgroundTask = null;
        }
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
    }

    @Override
    public void onDetach() {
        super.onDetach();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        cancelRefreshTask();
        Utility.freeMemory();
    }

    @Override
    public void onItemClickListener(TaskH item) {
        try {
            Scheme scheme = null;
            scheme = item.getScheme();
            if (scheme == null && item.getUuid_scheme() != null) {
                scheme = SchemeDataAccess.getOne(getActivity(),
                        item.getUuid_scheme());
                if (scheme != null)
                    item.setScheme(scheme);
            }

            if (scheme == null) {
                Toast.makeText(getActivity(), getActivity().getString(com.adins.mss.base.R.string.task_cant_seen),
                        Toast.LENGTH_SHORT).show();
            } else {
                if (!GlobalData.getSharedGlobalData().getDoingTask()) {
                    SurveyHeaderBean header = new SurveyHeaderBean(item);
                    CustomerFragment fragment = CustomerFragment.create(header);
                    FragmentTransaction transaction = MainMenuActivity.fragmentManager.beginTransaction();
                    transaction.setCustomAnimations(com.adins.mss.base.R.anim.activity_open_translate, com.adins.mss.base.R.anim.activity_close_scale, com.adins.mss.base.R.anim.activity_open_scale, com.adins.mss.base.R.anim.activity_close_translate);
                    transaction.replace(com.adins.mss.base.R.id.content_frame, fragment);
                    transaction.addToBackStack(null);
                    transaction.commit();
                }
            }
        } catch (Exception e) {
            FireCrash.log(e);
            ACRA.getErrorReporter().putCustomData("errorClickListener", e.getMessage());
            ACRA.getErrorReporter().putCustomData("errorClickListener", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
            ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat click item"));
            String message = getActivity().getString(com.adins.mss.base.R.string.task_cant_seen2);
            Toast.makeText(getActivity(), message, Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onItemLongClickListener(TaskH item) {
        // TODO Auto-generated method stub
    }

    private void onRefreshComplete() {
        try {
            viewAdapter = new PriorityViewAdapter(getActivity(), listTaskH, this, param);
            recyclerView.setAdapter(viewAdapter);
            viewAdapter.notifyDataSetChanged();
            long counter = listTaskH.size();
            taskCounter.setText(getString(R.string.task_count) + String.valueOf(counter));
        } catch (UnsupportedOperationException e) {
            try {
                viewAdapter.notifyDataSetChanged();
            } catch (Exception e2) {
                FireCrash.log(e2);
                ACRA.getErrorReporter().putCustomData("ErrorOnRefreshCompleted", e.getMessage());
                ACRA.getErrorReporter().putCustomData("ErrorOnRefreshCompleted", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat set Notify Data Set Changed"));
            }
        } catch (Exception e) {
            FireCrash.log(e);
            try {
                viewAdapter.notifyDataSetChanged();
            } catch (Exception e2) {
                FireCrash.log(e2);
                ACRA.getErrorReporter().putCustomData("ErrorOnRefreshCompleted", e.getMessage());
                ACRA.getErrorReporter().putCustomData("ErrorOnRefreshCompleted", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat set Notify Data Set Changed"));
            }
        }
        // Stop the refreshing indicator
        if (mSwipeRefreshLayout != null && mSwipeRefreshLayout.isRefreshing())
            mSwipeRefreshLayout.setRefreshing(false);
    }

    private String getTaskListFromServer(Context activity) {
        String errMsg = "";
        if (Tool.isInternetconnected(activity)) {
            String result;
            User user = GlobalData.getSharedGlobalData().getUser();
            JsonRequestRetrieveVerificationForm requestType = new JsonRequestRetrieveVerificationForm();
            requestType.setAudit(GlobalData.getSharedGlobalData().getAuditData());
            requestType.addImeiAndroidIdToUnstructured();

            String url = GlobalData.getSharedGlobalData().getURL_GET_TASKLIST(); //task/retrievetasklist

            String json = GsonHelper.toJson(requestType);
            boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
            boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
            HttpCryptedConnection httpConn = new HttpCryptedConnection(activity, encrypt, decrypt);
            HttpConnectionResult serverResult = null;
            try {
                serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
            } catch (Exception e) {
                FireCrash.log(e);
                ACRA.getErrorReporter().putCustomData("errorRequestToServer", e.getMessage());
                ACRA.getErrorReporter().putCustomData("errorRequestToServer", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat request to server"));
                e.printStackTrace();
                errMsg = getActivity().getString(R.string.jsonParseFailed);
                return errMsg;
            }


            List<String> listUuidTaskH = new ArrayList<>();

            if (serverResult != null) {
                if (serverResult.isOK()) {
                    try {
                        result = serverResult.getResult();
                        JsonResponseTaskList taskList = GsonHelper.fromJson(result, JsonResponseTaskList.class);
                        if (taskList.getStatus().getCode() == 0) {
                            List<TaskH> listTaskH = taskList.getListTaskList();
                            if (listTaskH != null && !listTaskH.isEmpty()) {
                                String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                                TaskHDataAccess.deleteTaskHByStatus(activity, uuidUser, TaskHDataAccess.STATUS_SEND_INIT);
                                TaskHDataAccess.deleteTaskHByStatus(activity, uuidUser, TaskHDataAccess.STATUS_TASK_WAITING);

                                for (TaskH taskH : listTaskH) {
                                    taskH.setUser(user);
                                    taskH.setIs_verification(Global.TRUE_STRING);

                                    String uuid_scheme = taskH.getUuid_scheme();
                                    listUuidTaskH.add(taskH.getUuid_task_h());
                                    Scheme scheme = SchemeDataAccess.getOne(activity, uuid_scheme);
                                    if (scheme != null) {
                                        taskH.setScheme(scheme);
                                        TaskH h = TaskHDataAccess.getOneHeader(activity, taskH.getUuid_task_h());
                                        String uuid_timelineType = TimelineTypeDataAccess.getTimelineTypebyType(activity, Global.TIMELINE_TYPE_TASK).getUuid_timeline_type();
                                        boolean wasInTimeline = TimelineDataAccess.getOneTimelineByTaskH(activity, user.getUuid_user(), taskH.getUuid_task_h(), uuid_timelineType) != null;
                                        if (h != null && h.getStatus() != null) {
                                            if (!ToDoList.isOldTask(h)) {
                                                if (Global.STATUS_TASK_WEB_WAITING.equalsIgnoreCase(taskH.getStatus())) {
                                                    taskH.setStatus(TaskHDataAccess.STATUS_TASK_WAITING);
                                                } else {
                                                    taskH.setStatus(TaskHDataAccess.STATUS_SEND_INIT);
                                                    if (!wasInTimeline) {
                                                        TimelineManager.insertTimeline(activity, taskH);
                                                    }
                                                }
                                                TaskHDataAccess.addOrReplace(activity, taskH);
                                            } else {
                                                if (taskH.getPts_date() != null) {
                                                    h.setPts_date(taskH.getPts_date());
                                                    TaskHDataAccess.addOrReplace(activity, h);
                                                }
                                            }
                                        } else {
                                            if (Global.STATUS_TASK_WEB_WAITING.equalsIgnoreCase(taskH.getStatus())) {
                                                taskH.setStatus(TaskHDataAccess.STATUS_TASK_WAITING);
                                            } else {
                                                taskH.setStatus(TaskHDataAccess.STATUS_SEND_INIT);
                                                if (!wasInTimeline) {
                                                    TimelineManager.insertTimeline(activity, taskH);
                                                }
                                            }
                                            TaskHDataAccess.addOrReplace(activity, taskH);
                                        }
                                    }
                                }
                                List<TaskH> taskHs = TaskHDataAccess.getAllTaskByStatus(activity, GlobalData.getSharedGlobalData().getUser().getUuid_user(), TaskHDataAccess.STATUS_SEND_DOWNLOAD);

                                for (TaskH h : taskHs) {
                                    String uuid_task_h = h.getUuid_task_h();
                                    boolean isSame = false;
                                    for (String uuid_from_server : listUuidTaskH) {
                                        if (uuid_task_h.equals(uuid_from_server)) {
                                            isSame = true;
                                            break;
                                        }
                                    }
                                    if (!isSame) {
                                        TaskHDataAccess.deleteWithRelation(activity, h);
                                    }
                                }
                            }

                            // Adding code for get reminder po task from server (2022-08-12)
                            List<TaskReminderPo> listTaskReminderPo = taskList.getListTaskReminderPo();
                            processTaskReminderPo(listTaskReminderPo);

                            errMsg = "noError";
                            return errMsg;
                        } else {
                            errMsg = result;
                            return errMsg;
                        }
                    } catch (Exception e) {
                        FireCrash.log(e);
                        ACRA.getErrorReporter().putCustomData("errorGetMessageFromServer", e.getMessage());
                        ACRA.getErrorReporter().putCustomData("errorGetMessageFromServer", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                        ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat convert json dari Server"));
                        errMsg = getActivity().getString(R.string.jsonParseFailed);
                        return errMsg;
                    }
                } else {
                    errMsg = serverResult.getResult();
                    return errMsg;
                }
            }
            return errMsg;
        } else {
            return errMsg;
        }
    }

    public class PriorityHandler extends Handler {

        @Override
        public void handleMessage(Message msg) {
            try {
                if (isPriorityOpen) {
                    initiateRefresh(false);
                }
            } catch (Exception e) {
                FireCrash.log(e);
                ACRA.getErrorReporter().putCustomData("errorRefresh", e.getMessage());
                ACRA.getErrorReporter().putCustomData("errorRefresh", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat initiate Refresh"));
            }
        }
    }

    private class RefreshBackgroundTask extends AsyncTask<Void, Void, List<TaskH>> {
        static final int TASK_DURATION = 2 * 1000; // 2 seconds
        int pos = 0;
        String errMessage = "";
        boolean isGetFromServer;

        public RefreshBackgroundTask(boolean isGetFromServer) {
            try {
                this.isGetFromServer = isGetFromServer;
                pos = spinnerSearch.getSelectedItemPosition();
            } catch (Exception e) {
                FireCrash.log(e);

            }
        }

        @Override
        protected void onCancelled() {
            super.onCancelled();
            if (mSwipeRefreshLayout != null && mSwipeRefreshLayout.isRefreshing())
                mSwipeRefreshLayout.setRefreshing(false);
        }

        @Override
        protected List<TaskH> doInBackground(Void... params) {
            // Sleep for a small amount of time to simulate a background-task
            try {
                if (isGetFromServer)
                    errMessage = getTaskListFromServer(getActivity());
            } catch (Exception e) {
                FireCrash.log(e);
                ACRA.getErrorReporter().putCustomData("errorGetErrorMessage", e.getMessage());
                ACRA.getErrorReporter().putCustomData("errorGetErrorMessage", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat convert taskList in error message"));
                e.printStackTrace();
            }
            // Return a new random list of cheeses
            return getSelectedTaskH(pos);
        }

        @Override
        protected void onPostExecute(List<TaskH> result) {
            super.onPostExecute(result);
            if (!errMessage.isEmpty() && !errMessage.equals("noError")) {
                Toast.makeText(getActivity(), errMessage, Toast.LENGTH_SHORT).show();
            }
            try {
                MainMenuActivity.setDrawerCounter();
                if (isShowNotifReminderPo) {
                    showNotifTaskReminderPo();
                }
            } catch (Exception e) {
                FireCrash.log(e);
                ACRA.getErrorReporter().putCustomData("ErrorMainMenuActivity", e.getMessage());
                ACRA.getErrorReporter().putCustomData("ErrorMainMenuActivity", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat set Drawer Counter"));
            }
            onRefreshComplete();
        }

    }

    public class PriorityAdapter extends ArrayAdapter<String> {
        private String[] values;

        public PriorityAdapter(Context context, int resource, String[] objects) {
            super(context, resource, objects);
            this.values = objects;
        }

        public int getCount() {
            return values.length;
        }

        public String getItem(int position) {
            return values[position];
        }

        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            LayoutInflater inflater = getActivity().getLayoutInflater();
            View view = inflater.inflate(R.layout.spinner_style, parent, false);
            TextView label = view.findViewById(R.id.text_spin);
            label.setText("Priority : " + values[position]);
            return label;
        }

        @Override
        public View getDropDownView(int position, View convertView, ViewGroup parent) {
            LayoutInflater inflater = getActivity().getLayoutInflater();
            View view = inflater.inflate(R.layout.spinner_style, parent, false);
            TextView label = view.findViewById(R.id.text_spin);
            label.setText(values[position]);
            return label;
        }
    }

    //Nendi: 20171023 - Add SpinnerAdapter for sort/order By
    public class SpinnerAdapter extends ArrayAdapter<String> {
        private String[] lists;
        private int type;

        public SpinnerAdapter(Context context, int resource, String[] lists, int type) {
            super(context, resource, lists);
            this.lists   = lists;
            this.type    = type;
        }

        @Override
        public int getCount() {
            return lists.length;
        }

        @Nullable
        @Override
        public String getItem(int position) {
            return lists[position];
        }

        @Override
        public long getItemId(int position) {
            return super.getItemId(position);
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            LayoutInflater inflater = getActivity().getLayoutInflater();
            View view = inflater.inflate(R.layout.spinner_style, parent, false);
            TextView label = view.findViewById(R.id.text_spin);
            if (type == 1) label.setText("Order By : " + lists[position]);
            else label.setText("Sort By : " + lists[position]);
            return label;
        }

        @Override
        public View getDropDownView(int position, View convertView, ViewGroup parent) {
            LayoutInflater inflater = getActivity().getLayoutInflater();
            View view = inflater.inflate(R.layout.spinner_style, parent, false);
            TextView label = view.findViewById(R.id.text_spin);
            label.setText(lists[position]);
            return label;
        }
    }

    private void processTaskReminderPo(List<TaskReminderPo> listTaskReminderPo) {
        if (null != listTaskReminderPo && !listTaskReminderPo.isEmpty()) {
            List<String> listUuidTaskH = new ArrayList<>();
            for (TaskReminderPo taskPo : listTaskReminderPo) {
                String uuidTaskH = taskPo.getUuidTaskH();
                if (null != uuidTaskH && !"".equals(uuidTaskH)) {
                    listUuidTaskH.add(uuidTaskH);
                    ReminderPo dataReminderPo = ReminderPoDataAccess.getOneByUuidTaskH(getActivity(), uuidTaskH);
                    if (null != dataReminderPo) {
                        String taskCreateDate = Formatter.formatDate(dataReminderPo.getDtm_crt(), Global.DATE_STR_FORMAT2);
                        String currentDate = Formatter.formatDate(new Date(), Global.DATE_STR_FORMAT2);
                        boolean isOldTask = taskCreateDate.compareToIgnoreCase(currentDate) < 0;
                        if (isOldTask) {
                            dataReminderPo.setDtm_crt(new Date());
                            ReminderPoDataAccess.update(getActivity(), dataReminderPo);
                            isShowNotifReminderPo = true;
                        }

                        TaskH taskH = TaskHDataAccess.getOneHeader(getActivity(), uuidTaskH);
                        if (null == taskH) {
                            dataReminderPo.setIs_task_downloaded("0");
                            ReminderPoDataAccess.update(getActivity(), dataReminderPo);
                        }

                    } else {
                        ReminderPo reminderPo = new ReminderPo();
                        String newId = Tool.getUUID();
                        reminderPo.setId(newId);
                        reminderPo.setDtm_crt(new Date());
                        reminderPo.setUuid_task_h(uuidTaskH);
                        reminderPo.setExpired_date(taskPo.getExpireDate());
                        reminderPo.setStatus_po(taskPo.getStatusPo());
                        reminderPo.setUuid_user(GlobalData.getSharedGlobalData().getUser().getUuid_user());
                        TaskH taskH = TaskHDataAccess.getOneHeader(getActivity(), uuidTaskH);
                        if (null != taskH) {
                            reminderPo.setIs_task_downloaded("1");
                        } else {
                            reminderPo.setIs_task_downloaded("0");
                        }
                        ReminderPoDataAccess.add(getActivity(), reminderPo);
                        isShowNotifReminderPo = true;
                    }
                }
            }

            String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
            List<ReminderPo> listReminderPo = ReminderPoDataAccess.getAllTaskReminderPo(getActivity(), uuidUser);
            for (ReminderPo reminderPo : listReminderPo) {
                String uuidTaskH = reminderPo.getUuid_task_h();
                boolean isSame = false;
                for (String uuidFromServer : listUuidTaskH) {
                    if (uuidTaskH.equals(uuidFromServer)) {
                        isSame = true;
                        break;
                    }
                }
                if (!isSame) {
                    ReminderPoDataAccess.delete(getActivity(), reminderPo);
                }
            }

            if (isShowNotifReminderPo) {
                countNotifReminderPo = listTaskReminderPo.size();
            }
        } else {
            String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
            ReminderPoDataAccess.delete(getActivity(), uuidUser);
        }
    }

    private void showNotifTaskReminderPo() {
        String notifTitle = getActivity().getString(R.string.reminder_po_task);
        String message = getActivity().getString(R.string.reminder_po_notification, countNotifReminderPo);

        Intent intent = new Intent(getActivity(), AppContext.getInstance().getHomeClass());
        intent.setAction(LOG_NOTIFICATION_KEY);

        PendingIntent pendingIntent = PendingIntent.getActivity(getActivity(), 0, intent,
                PendingIntent.FLAG_CANCEL_CURRENT);
        Notification.getSharedNotification().setDefaultIcon(R.drawable.icon_notif);

        NotificationCompat.Builder builder = new NotificationCompat.Builder(getActivity());
        builder.setSmallIcon(getNotificationIcon());
        builder.setContentTitle(notifTitle).setNumber(countNotifReminderPo);
        builder.setContentText(message).setNumber(countNotifReminderPo);
        builder.setPriority(NORM_PRIORITY);
        NotificationCompat.BigTextStyle inboxStyle = new NotificationCompat.BigTextStyle();
        // Sets a title for the Inbox in expanded layout
        inboxStyle.setBigContentTitle(notifTitle);
        inboxStyle.bigText(message);
        inboxStyle.setSummaryText(getActivity().getString(R.string.click_to_open_log));

        builder.setDefaults(android.app.Notification.DEFAULT_ALL);
        builder.setStyle(inboxStyle);
        builder.setAutoCancel(true);
        builder.setContentIntent(pendingIntent);

        NotificationManager mNotificationManager = (NotificationManager) getActivity().getSystemService(
                Context.NOTIFICATION_SERVICE);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            String channelId = "channel_reminder_po_task";
            NotificationChannel channel = new NotificationChannel(
                    channelId,
                    "Reminder Po Task",
                    NotificationManager.IMPORTANCE_HIGH);
            mNotificationManager.createNotificationChannel(channel);
            builder.setChannelId(channelId);
        }
        mNotificationManager.notify(5, builder.build());
    }
}