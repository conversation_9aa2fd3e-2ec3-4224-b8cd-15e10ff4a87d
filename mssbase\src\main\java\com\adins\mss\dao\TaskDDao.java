package com.adins.mss.dao;

import java.util.List;
import java.util.ArrayList;
import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.SqlUtils;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;
import de.greenrobot.dao.query.Query;
import de.greenrobot.dao.query.QueryBuilder;

import com.adins.mss.dao.TaskD;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TR_TASK_D".
*/
public class TaskDDao extends AbstractDao<TaskD, String> {

    public static final String TABLENAME = "TR_TASK_D";

    /**
     * Properties of entity TaskD.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_task_d = new Property(0, String.class, "uuid_task_d", true, "UUID_TASK_D");
        public final static Property Question_group_id = new Property(1, String.class, "question_group_id", false, "QUESTION_GROUP_ID");
        public final static Property Question_id = new Property(2, String.class, "question_id", false, "QUESTION_ID");
        public final static Property Option_answer_id = new Property(3, String.class, "option_answer_id", false, "OPTION_ANSWER_ID");
        public final static Property Text_answer = new Property(4, String.class, "text_answer", false, "TEXT_ANSWER");
        public final static Property Image = new Property(5, byte[].class, "image", false, "IMAGE");
        public final static Property Is_final = new Property(6, String.class, "is_final", false, "IS_FINAL");
        public final static Property Is_sent = new Property(7, String.class, "is_sent", false, "IS_SENT");
        public final static Property Lov = new Property(8, String.class, "lov", false, "LOV");
        public final static Property Usr_crt = new Property(9, String.class, "usr_crt", false, "USR_CRT");
        public final static Property Dtm_crt = new Property(10, java.util.Date.class, "dtm_crt", false, "DTM_CRT");
        public final static Property Uuid_task_h = new Property(11, String.class, "uuid_task_h", false, "UUID_TASK_H");
        public final static Property Question_label = new Property(12, String.class, "question_label", false, "QUESTION_LABEL");
        public final static Property Latitude = new Property(13, String.class, "latitude", false, "LATITUDE");
        public final static Property Longitude = new Property(14, String.class, "longitude", false, "LONGITUDE");
        public final static Property Mcc = new Property(15, String.class, "mcc", false, "MCC");
        public final static Property Mnc = new Property(16, String.class, "mnc", false, "MNC");
        public final static Property Lac = new Property(17, String.class, "lac", false, "LAC");
        public final static Property Cid = new Property(18, String.class, "cid", false, "CID");
        public final static Property Gps_time = new Property(19, java.util.Date.class, "gps_time", false, "GPS_TIME");
        public final static Property Accuracy = new Property(20, Integer.class, "accuracy", false, "ACCURACY");
        public final static Property Regex = new Property(21, String.class, "regex", false, "REGEX");
        public final static Property Is_readonly = new Property(22, String.class, "is_readonly", false, "IS_READONLY");
        public final static Property Location_image = new Property(23, byte[].class, "location_image", false, "LOCATION_IMAGE");
        public final static Property Is_visible = new Property(24, String.class, "is_visible", false, "IS_VISIBLE");
        public final static Property Uuid_lookup = new Property(25, String.class, "uuid_lookup", false, "UUID_LOOKUP");
        public final static Property Tag = new Property(26, String.class, "tag", false, "TAG");
        public final static Property Count = new Property(27, String.class, "count", false, "COUNT");
        public final static Property Has_default_image = new Property(28, String.class, "has_default_image", false, "HAS_DEFAULT_IMAGE");
        public final static Property Is_resurvey = new Property(29, String.class, "is_resurvey", false, "IS_RESURVEY");
        public final static Property Uuid_question_mapping = new Property(30, String.class, "uuid_question_mapping", false, "UUID_QUESTION_MAPPING");
        public final static Property Is_readonly_mapping = new Property(31, String.class, "is_readonly_mapping", false, "IS_READONLY_MAPPING");
    };

    private DaoSession daoSession;

    private Query<TaskD> taskH_TaskDListQuery;

    public TaskDDao(DaoConfig config) {
        super(config);
    }
    
    public TaskDDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TR_TASK_D\" (" + //
                "\"UUID_TASK_D\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_task_d
                "\"QUESTION_GROUP_ID\" TEXT," + // 1: question_group_id
                "\"QUESTION_ID\" TEXT," + // 2: question_id
                "\"OPTION_ANSWER_ID\" TEXT," + // 3: option_answer_id
                "\"TEXT_ANSWER\" TEXT," + // 4: text_answer
                "\"IMAGE\" BLOB," + // 5: image
                "\"IS_FINAL\" TEXT," + // 6: is_final
                "\"IS_SENT\" TEXT," + // 7: is_sent
                "\"LOV\" TEXT," + // 8: lov
                "\"USR_CRT\" TEXT," + // 9: usr_crt
                "\"DTM_CRT\" INTEGER," + // 10: dtm_crt
                "\"UUID_TASK_H\" TEXT," + // 11: uuid_task_h
                "\"QUESTION_LABEL\" TEXT," + // 12: question_label
                "\"LATITUDE\" TEXT," + // 13: latitude
                "\"LONGITUDE\" TEXT," + // 14: longitude
                "\"MCC\" TEXT," + // 15: mcc
                "\"MNC\" TEXT," + // 16: mnc
                "\"LAC\" TEXT," + // 17: lac
                "\"CID\" TEXT," + // 18: cid
                "\"GPS_TIME\" INTEGER," + // 19: gps_time
                "\"ACCURACY\" INTEGER," + // 20: accuracy
                "\"REGEX\" TEXT," + // 21: regex
                "\"IS_READONLY\" TEXT," + // 22: is_readonly
                "\"LOCATION_IMAGE\" BLOB," + // 23: location_image
                "\"IS_VISIBLE\" TEXT," + // 24: is_visible
                "\"UUID_LOOKUP\" TEXT," + // 25: uuid_lookup
                "\"TAG\" TEXT," + // 26: tag
                "\"COUNT\" TEXT," + // 27: count
                "\"HAS_DEFAULT_IMAGE\" TEXT," + // 28: has_default_image
                "\"IS_RESURVEY\" TEXT," + // 29: is_resurvey
                "\"UUID_QUESTION_MAPPING\" TEXT," + // 30: uuid_question_mapping
                "\"IS_READONLY_MAPPING\" TEXT);"); // 31: is_readonly_mapping
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TR_TASK_D\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, TaskD entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_task_d());
 
        String question_group_id = entity.getQuestion_group_id();
        if (question_group_id != null) {
            stmt.bindString(2, question_group_id);
        }
 
        String question_id = entity.getQuestion_id();
        if (question_id != null) {
            stmt.bindString(3, question_id);
        }
 
        String option_answer_id = entity.getOption_answer_id();
        if (option_answer_id != null) {
            stmt.bindString(4, option_answer_id);
        }
 
        String text_answer = entity.getText_answer();
        if (text_answer != null) {
            stmt.bindString(5, text_answer);
        }
 
        byte[] image = entity.getImage();
        if (image != null) {
            stmt.bindBlob(6, image);
        }
 
        String is_final = entity.getIs_final();
        if (is_final != null) {
            stmt.bindString(7, is_final);
        }
 
        String is_sent = entity.getIs_sent();
        if (is_sent != null) {
            stmt.bindString(8, is_sent);
        }
 
        String lov = entity.getLov();
        if (lov != null) {
            stmt.bindString(9, lov);
        }
 
        String usr_crt = entity.getUsr_crt();
        if (usr_crt != null) {
            stmt.bindString(10, usr_crt);
        }
 
        java.util.Date dtm_crt = entity.getDtm_crt();
        if (dtm_crt != null) {
            stmt.bindLong(11, dtm_crt.getTime());
        }
 
        String uuid_task_h = entity.getUuid_task_h();
        if (uuid_task_h != null) {
            stmt.bindString(12, uuid_task_h);
        }
 
        String question_label = entity.getQuestion_label();
        if (question_label != null) {
            stmt.bindString(13, question_label);
        }
 
        String latitude = entity.getLatitude();
        if (latitude != null) {
            stmt.bindString(14, latitude);
        }
 
        String longitude = entity.getLongitude();
        if (longitude != null) {
            stmt.bindString(15, longitude);
        }
 
        String mcc = entity.getMcc();
        if (mcc != null) {
            stmt.bindString(16, mcc);
        }
 
        String mnc = entity.getMnc();
        if (mnc != null) {
            stmt.bindString(17, mnc);
        }
 
        String lac = entity.getLac();
        if (lac != null) {
            stmt.bindString(18, lac);
        }
 
        String cid = entity.getCid();
        if (cid != null) {
            stmt.bindString(19, cid);
        }
 
        java.util.Date gps_time = entity.getGps_time();
        if (gps_time != null) {
            stmt.bindLong(20, gps_time.getTime());
        }
 
        Integer accuracy = entity.getAccuracy();
        if (accuracy != null) {
            stmt.bindLong(21, accuracy);
        }
 
        String regex = entity.getRegex();
        if (regex != null) {
            stmt.bindString(22, regex);
        }
 
        String is_readonly = entity.getIs_readonly();
        if (is_readonly != null) {
            stmt.bindString(23, is_readonly);
        }
 
        byte[] location_image = entity.getLocation_image();
        if (location_image != null) {
            stmt.bindBlob(24, location_image);
        }
 
        String is_visible = entity.getIs_visible();
        if (is_visible != null) {
            stmt.bindString(25, is_visible);
        }
 
        String uuid_lookup = entity.getUuid_lookup();
        if (uuid_lookup != null) {
            stmt.bindString(26, uuid_lookup);
        }
 
        String tag = entity.getTag();
        if (tag != null) {
            stmt.bindString(27, tag);
        }
 
        String count = entity.getCount();
        if (count != null) {
            stmt.bindString(28, count);
        }
 
        String has_default_image = entity.getHas_default_image();
        if (has_default_image != null) {
            stmt.bindString(29, has_default_image);
        }
 
        String is_resurvey = entity.getIs_resurvey();
        if (is_resurvey != null) {
            stmt.bindString(30, is_resurvey);
        }
 
        String uuid_question_mapping = entity.getUuid_question_mapping();
        if (uuid_question_mapping != null) {
            stmt.bindString(31, uuid_question_mapping);
        }
 
        String is_readonly_mapping = entity.getIs_readonly_mapping();
        if (is_readonly_mapping != null) {
            stmt.bindString(32, is_readonly_mapping);
        }
    }

    @Override
    protected void attachEntity(TaskD entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public TaskD readEntity(Cursor cursor, int offset) {
        TaskD entity = new TaskD( //
            cursor.getString(offset + 0), // uuid_task_d
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // question_group_id
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // question_id
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // option_answer_id
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // text_answer
            cursor.isNull(offset + 5) ? null : cursor.getBlob(offset + 5), // image
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // is_final
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7), // is_sent
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8), // lov
            cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9), // usr_crt
            cursor.isNull(offset + 10) ? null : new java.util.Date(cursor.getLong(offset + 10)), // dtm_crt
            cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11), // uuid_task_h
            cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12), // question_label
            cursor.isNull(offset + 13) ? null : cursor.getString(offset + 13), // latitude
            cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14), // longitude
            cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15), // mcc
            cursor.isNull(offset + 16) ? null : cursor.getString(offset + 16), // mnc
            cursor.isNull(offset + 17) ? null : cursor.getString(offset + 17), // lac
            cursor.isNull(offset + 18) ? null : cursor.getString(offset + 18), // cid
            cursor.isNull(offset + 19) ? null : new java.util.Date(cursor.getLong(offset + 19)), // gps_time
            cursor.isNull(offset + 20) ? null : cursor.getInt(offset + 20), // accuracy
            cursor.isNull(offset + 21) ? null : cursor.getString(offset + 21), // regex
            cursor.isNull(offset + 22) ? null : cursor.getString(offset + 22), // is_readonly
            cursor.isNull(offset + 23) ? null : cursor.getBlob(offset + 23), // location_image
            cursor.isNull(offset + 24) ? null : cursor.getString(offset + 24), // is_visible
            cursor.isNull(offset + 25) ? null : cursor.getString(offset + 25), // uuid_lookup
            cursor.isNull(offset + 26) ? null : cursor.getString(offset + 26), // tag
            cursor.isNull(offset + 27) ? null : cursor.getString(offset + 27), // count
            cursor.isNull(offset + 28) ? null : cursor.getString(offset + 28), // has_default_image
            cursor.isNull(offset + 29) ? null : cursor.getString(offset + 29), // is_resurvey
            cursor.isNull(offset + 30) ? null : cursor.getString(offset + 30), // uuid_question_mapping
            cursor.isNull(offset + 31) ? null : cursor.getString(offset + 31) // is_readonly_mapping
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, TaskD entity, int offset) {
        entity.setUuid_task_d(cursor.getString(offset + 0));
        entity.setQuestion_group_id(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setQuestion_id(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setOption_answer_id(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setText_answer(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setImage(cursor.isNull(offset + 5) ? null : cursor.getBlob(offset + 5));
        entity.setIs_final(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setIs_sent(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
        entity.setLov(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
        entity.setUsr_crt(cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9));
        entity.setDtm_crt(cursor.isNull(offset + 10) ? null : new java.util.Date(cursor.getLong(offset + 10)));
        entity.setUuid_task_h(cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11));
        entity.setQuestion_label(cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12));
        entity.setLatitude(cursor.isNull(offset + 13) ? null : cursor.getString(offset + 13));
        entity.setLongitude(cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14));
        entity.setMcc(cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15));
        entity.setMnc(cursor.isNull(offset + 16) ? null : cursor.getString(offset + 16));
        entity.setLac(cursor.isNull(offset + 17) ? null : cursor.getString(offset + 17));
        entity.setCid(cursor.isNull(offset + 18) ? null : cursor.getString(offset + 18));
        entity.setGps_time(cursor.isNull(offset + 19) ? null : new java.util.Date(cursor.getLong(offset + 19)));
        entity.setAccuracy(cursor.isNull(offset + 20) ? null : cursor.getInt(offset + 20));
        entity.setRegex(cursor.isNull(offset + 21) ? null : cursor.getString(offset + 21));
        entity.setIs_readonly(cursor.isNull(offset + 22) ? null : cursor.getString(offset + 22));
        entity.setLocation_image(cursor.isNull(offset + 23) ? null : cursor.getBlob(offset + 23));
        entity.setIs_visible(cursor.isNull(offset + 24) ? null : cursor.getString(offset + 24));
        entity.setUuid_lookup(cursor.isNull(offset + 25) ? null : cursor.getString(offset + 25));
        entity.setTag(cursor.isNull(offset + 26) ? null : cursor.getString(offset + 26));
        entity.setCount(cursor.isNull(offset + 27) ? null : cursor.getString(offset + 27));
        entity.setHas_default_image(cursor.isNull(offset + 28) ? null : cursor.getString(offset + 28));
        entity.setIs_resurvey(cursor.isNull(offset + 29) ? null : cursor.getString(offset + 29));
        entity.setUuid_question_mapping(cursor.isNull(offset + 30) ? null : cursor.getString(offset + 30));
        entity.setIs_readonly_mapping(cursor.isNull(offset + 31) ? null : cursor.getString(offset + 31));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(TaskD entity, long rowId) {
        return entity.getUuid_task_d();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(TaskD entity) {
        if(entity != null) {
            return entity.getUuid_task_d();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
    /** Internal query to resolve the "taskDList" to-many relationship of TaskH. */
    public List<TaskD> _queryTaskH_TaskDList(String uuid_task_h) {
        synchronized (this) {
            if (taskH_TaskDListQuery == null) {
                QueryBuilder<TaskD> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_task_h.eq(null));
                taskH_TaskDListQuery = queryBuilder.build();
            }
        }
        Query<TaskD> query = taskH_TaskDListQuery.forCurrentThread();
        query.setParameter(0, uuid_task_h);
        return query.list();
    }

    private String selectDeep;

    protected String getSelectDeep() {
        if (selectDeep == null) {
            StringBuilder builder = new StringBuilder("SELECT ");
            SqlUtils.appendColumns(builder, "T", getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T0", daoSession.getTaskHDao().getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T1", daoSession.getLookupDao().getAllColumns());
            builder.append(" FROM TR_TASK_D T");
            builder.append(" LEFT JOIN TR_TASK_H T0 ON T.\"UUID_TASK_H\"=T0.\"UUID_TASK_H\"");
            builder.append(" LEFT JOIN MS_LOOKUP T1 ON T.\"UUID_LOOKUP\"=T1.\"UUID_LOOKUP\"");
            builder.append(' ');
            selectDeep = builder.toString();
        }
        return selectDeep;
    }
    
    protected TaskD loadCurrentDeep(Cursor cursor, boolean lock) {
        TaskD entity = loadCurrent(cursor, 0, lock);
        int offset = getAllColumns().length;

        TaskH taskH = loadCurrentOther(daoSession.getTaskHDao(), cursor, offset);
        entity.setTaskH(taskH);
        offset += daoSession.getTaskHDao().getAllColumns().length;

        Lookup lookup = loadCurrentOther(daoSession.getLookupDao(), cursor, offset);
        entity.setLookup(lookup);

        return entity;    
    }

    public TaskD loadDeep(Long key) {
        assertSinglePk();
        if (key == null) {
            return null;
        }

        StringBuilder builder = new StringBuilder(getSelectDeep());
        builder.append("WHERE ");
        SqlUtils.appendColumnsEqValue(builder, "T", getPkColumns());
        String sql = builder.toString();
        
        String[] keyArray = new String[] { key.toString() };
        Cursor cursor = db.rawQuery(sql, keyArray);
        
        try {
            boolean available = cursor.moveToFirst();
            if (!available) {
                return null;
            } else if (!cursor.isLast()) {
                throw new IllegalStateException("Expected unique result, but count was " + cursor.getCount());
            }
            return loadCurrentDeep(cursor, true);
        } finally {
            cursor.close();
        }
    }
    
    /** Reads all available rows from the given cursor and returns a list of new ImageTO objects. */
    public List<TaskD> loadAllDeepFromCursor(Cursor cursor) {
        int count = cursor.getCount();
        List<TaskD> list = new ArrayList<TaskD>(count);
        
        if (cursor.moveToFirst()) {
            if (identityScope != null) {
                identityScope.lock();
                identityScope.reserveRoom(count);
            }
            try {
                do {
                    list.add(loadCurrentDeep(cursor, false));
                } while (cursor.moveToNext());
            } finally {
                if (identityScope != null) {
                    identityScope.unlock();
                }
            }
        }
        return list;
    }
    
    protected List<TaskD> loadDeepAllAndCloseCursor(Cursor cursor) {
        try {
            return loadAllDeepFromCursor(cursor);
        } finally {
            cursor.close();
        }
    }
    

    /** A raw-style query where you can pass any WHERE clause and arguments. */
    public List<TaskD> queryDeep(String where, String... selectionArg) {
        Cursor cursor = db.rawQuery(getSelectDeep() + where, selectionArg);
        return loadDeepAllAndCloseCursor(cursor);
    }
 
}
