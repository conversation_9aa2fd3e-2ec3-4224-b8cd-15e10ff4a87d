package com.adins.mss.base.timeline;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.location.Address;
import android.location.Geocoder;
import android.os.AsyncTask;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.adins.mss.base.BaseActivity;
import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.util.LocaleHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.GeneralParameter;
import com.adins.mss.foundation.camerainapp.helper.Logger;
import com.adins.mss.foundation.db.dataaccess.GeneralParameterDataAccess;
import com.adins.mss.foundation.formatter.Tool;
import com.google.android.gms.maps.CameraUpdateFactory;
import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.OnMapReadyCallback;
import com.google.android.gms.maps.SupportMapFragment;
import com.google.android.gms.maps.model.BitmapDescriptorFactory;
import com.google.android.gms.maps.model.CircleOptions;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.Marker;
import com.google.android.gms.maps.model.MarkerOptions;

import java.io.IOException;
import java.util.List;
import java.util.Locale;

public class MapsViewer extends BaseActivity implements OnMapReadyCallback {
    SupportMapFragment mapFragment;
    private GoogleMap mGoogleMap;
    private LatLng locationPoint;
    boolean isMoveLocationEnabled = true;
    private boolean isViewOnly;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //To can't screenshoot
        if (Tool.isProdFlavour(Global.FLAVORS)) {
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
        }

        setContentView(R.layout.maps_layout);

        try{
            isViewOnly = getIntent().getBooleanExtra("isViewOnly", false);
        }catch(Exception e){
            isViewOnly = false;
        }

        Button btnSaveAddress = findViewById(R.id.btn_save_address);
        LinearLayout descLayoutIn = findViewById(R.id.DescLayoutIn);
        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) descLayoutIn.getLayoutParams();

        /*Riska 2022-11-08 add setting on/off move marker*/
        GeneralParameter gsMoveLocation = GeneralParameterDataAccess.getOne(this, GlobalData.getSharedGlobalData().getUser().getUuid_user(), Global.GS_MOVE_LOCATION);
        if (gsMoveLocation != null && "0".equalsIgnoreCase(gsMoveLocation.getGs_value())) {
            isMoveLocationEnabled = false;
        }

        //michael.wijaya 29/04/22: isViewOnly = true, menyembunyikan tombol Save dan mengunci posisi marker dan tidak bisa di-drag
        //isViewOnly = false, tombol Save tampil, dan posisi marker peta dapat diubah dengan drag & drop
        if(isViewOnly || !isMoveLocationEnabled){
            btnSaveAddress.setVisibility(View.GONE);
            params.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM, RelativeLayout.TRUE);
        }else{
            btnSaveAddress.setVisibility(View.VISIBLE);
            btnSaveAddress.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    String lat = getIntent().getStringExtra("latitude");
                    String lng = getIntent().getStringExtra("longitude");

                    //michael.wijaya 29/04/22: penjagaan kalau Save button ditekan tanpa mengubah posisi marker
                    if(locationPoint.latitude != Double.parseDouble(lat) && locationPoint.longitude != Double.parseDouble(lng)){
                        try{
                            //get the latitude longitude of the marker, and then save it as activity result
                            Intent intent = new Intent();
                            Bundle bundle = new Bundle();
                            bundle.putString("latitude", String.format("%.7f", locationPoint.latitude));
                            bundle.putString("longitude", String.format("%.7f", locationPoint.longitude));
                            intent.putExtras(bundle);
                            setResult(Activity.RESULT_OK, intent);
                            Toast.makeText(MapsViewer.this, "Location changed, marker is saved!", Toast.LENGTH_SHORT).show();
                            finish();
                        }catch (Exception e){
                            e.printStackTrace();
                            Toast.makeText(MapsViewer.this, "Failed to save marker position!", Toast.LENGTH_SHORT).show();
                        }
                    }else{
                        setResult(Activity.RESULT_CANCELED);
                        Toast.makeText(MapsViewer.this, "No changes on location's marker, save cancelled!", Toast.LENGTH_SHORT).show();
                        finish();
                    }
                }
            });
        }

        mapFragment = (SupportMapFragment) getSupportFragmentManager().findFragmentById(R.id.maps);
        if (savedInstanceState == null) {
            mapFragment.setRetainInstance(true);
        } else {
            mapFragment.getMapAsync(this);
        }
        initialize();
    }


    @Override
    protected void attachBaseContext(Context newBase) {
        Context context = newBase;
        Locale locale;
        try {
            locale = new Locale(GlobalData.getSharedGlobalData().getLocale());
            if (null == locale) {
                locale = new Locale(LocaleHelper.ENGLSIH);
            }
            context = LocaleHelper.wrap(newBase, locale);
        } catch (Exception e) {
            locale = new Locale(LocaleHelper.ENGLSIH);
            context = LocaleHelper.wrap(newBase, locale);
        } finally {
            super.attachBaseContext(context);
        }
    }

    private void initialize() {
        if (mGoogleMap == null) {
            ((SupportMapFragment) getSupportFragmentManager().findFragmentById(R.id.maps)).getMapAsync(this);
            if (mGoogleMap != null) {
                setupMaps();
            }
        }
    }

    private void setupMaps() {
        try {
            String lat = getIntent().getStringExtra("latitude");
            String lng = getIntent().getStringExtra("longitude");
            int accuracy = getIntent().getIntExtra("accuracy", 0);
            double mLatitude = Double.parseDouble(lat.replace(',', '.'));
            double mLongitude = Double.parseDouble(lng.replace(',', '.'));
            locationPoint = new LatLng(mLatitude, mLongitude);
            mGoogleMap.animateCamera(CameraUpdateFactory.newLatLngZoom(locationPoint, 16));
            MarkerOptions markerOptions = new MarkerOptions();
            markerOptions.position(locationPoint);
            markerOptions.icon(BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_GREEN));

            markerOptions.draggable(!isViewOnly && isMoveLocationEnabled);

            mGoogleMap.addMarker(markerOptions);

            //setting up drag event for marker
            if(!isViewOnly && isMoveLocationEnabled){
                mGoogleMap.setOnMarkerDragListener(new GoogleMap.OnMarkerDragListener() {
                    @Override
                    public void onMarkerDragStart(Marker marker) {
                        //michael.wijaya 29/04/22: toast untuk kasih tau user kalau marker nya ke ambil. Device harusnya juga vibrate kalau setting Vibration di device nya nyala
                        Toast.makeText(MapsViewer.this, "Marker picked up!", Toast.LENGTH_SHORT).show();
                    }

                    @Override
                    public void onMarkerDrag(Marker marker) {
                        //EMPTY
                    }

                    @Override
                    public void onMarkerDragEnd(Marker marker) {
                        //update the address box with newly generated address, update marker's latLng value
                        locationPoint = marker.getPosition();
                        mGoogleMap.animateCamera(CameraUpdateFactory.newLatLng(locationPoint));

                        new GetAddressTask(getApplicationContext(), locationPoint.latitude,
                                locationPoint.longitude).execute();
                    }
                });
            }

            if (accuracy != 0) {
                CircleOptions circleOptions = new CircleOptions()
                        .center(locationPoint)
                        .radius(accuracy)
                        .fillColor(0x402196F3)
                        .strokeColor(Color.TRANSPARENT)
                        .strokeWidth(2);

                mGoogleMap.addCircle(circleOptions);
            }

            new GetAddressTask(this, mLatitude,
                    mLongitude).execute();
        } catch (Exception e) {
            FireCrash.log(e);
        }
    }

    @Override
    public void onMapReady(GoogleMap googleMap) {
        mGoogleMap = googleMap;
        if (mGoogleMap != null) {
            setupMaps();
        }
    }

    private class GetAddressTask extends AsyncTask<Void, Void, String[]> {
        private Context mContext;
        private double mLatitude;
        private double mLongitude;

        public GetAddressTask(Context context, double latitude, double longitude) {
            mContext = context;
            mLatitude = latitude;
            mLongitude = longitude;
        }

        /**
         * Get a Geocoder instance, get the latitude and longitude look up the
         * address, and return it
         *
         * @return A string containing the address of the current location, or
         * an empty string if no address can be found, or an error
         * message
         * @params params One or more Location objects
         */
        @Override
        protected String[] doInBackground(Void... params) {
            Geocoder geocoder = new Geocoder(mContext, Locale.getDefault());
            // Get the current location from the input parameter list

            String[] addressString = new String[2];
            // Create a list to contain the result address
            List<Address> addresses = null;
            try {
                /*
                 * Return 1 address.
                 */
                addresses = geocoder.getFromLocation(mLatitude, mLongitude, 1);
            } catch (IOException e1) {
                if (Global.IS_DEV) {
                    Logger.e("LocationSampleActivity",
                            "IO Exception in getFromLocation()");
                    e1.printStackTrace();
                }
                addressString[0] = mContext.getResources().getString(R.string.address_not_found);
                addressString[1] = mContext.getResources().getString(R.string.address_not_found);
                return addressString;
            } catch (IllegalArgumentException e2) {
                // Error message to post in the log
                addressString[0] = mContext.getResources().getString(R.string.address_not_found);
                addressString[1] = mContext.getResources().getString(R.string.address_not_found);
                if (Global.IS_DEV) {
                    Logger.e("CheckIn Manager :", addressString[1]);
                    e2.printStackTrace();
                }
                return addressString;
            }
            // If the reverse geocode returned an address
            if (addresses != null && !addresses.isEmpty()) {
                // Get the first address
                Address address = addresses.get(0);
                addressString[0] = address.getLocality();
                // If address.getLocality() returns null or timed out
                if(addressString[0] == null ||
                        (addressString[0] != null && addressString[0].contains("Timed out"))){
                    addressString[0] = mContext.getResources().getString(R.string.address_not_found);
                }
                try {
                    addressString[1] = address.getAddressLine(0);
                    if (addressString[1] != null && addressString[1].contains("Timed out"))
                        addressString[1] = mContext.getResources().getString(R.string.address_not_found);
                } catch (Exception e) {
                    addressString[1] = mContext.getResources().getString(R.string.address_not_found);
                }
                // Return the text
                return addressString;
            } else {
                addressString[0] = mContext.getResources().getString(R.string.address_not_found);
                addressString[1] = mContext.getResources().getString(R.string.coordinat) + " : " + mLatitude + ", " + mLongitude;
                return addressString;
            }
        }

        @Override
        protected void onPostExecute(String[] addressResult) {
            // Set activity indicator visibility to "gone"
            // Display the results of the lookup.
            setAddress(addressResult);
        }

        private void setAddress(String[] addressResult) {
            TextView addressLocale = (TextView) findViewById(R.id.textLocalIn);
            TextView addressLine = (TextView) findViewById(R.id.textAddressIn);
            LinearLayout descLayout = (LinearLayout) findViewById(R.id.DescLayoutIn);
            if (addressResult[0]!=null){
                addressLocale.setText(addressResult[0]);
                addressLine.setText(addressResult[1]);
                descLayout.setVisibility(View.VISIBLE);
            }else {
                descLayout.setVisibility(View.GONE);
            }
        }
    }
}
