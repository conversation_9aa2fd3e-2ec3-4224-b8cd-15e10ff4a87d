package com.adins.mss.dao;

import com.adins.mss.dao.DaoSession;
import de.greenrobot.dao.DaoException;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "TR_REMINDER_PO".
 */
public class ReminderPo {

    /** Not-null value. */
     @SerializedName("id")
    private String id;
     @SerializedName("dtm_crt")
    private java.util.Date dtm_crt;
     @SerializedName("uuid_task_h")
    private String uuid_task_h;
     @SerializedName("is_task_downloaded")
    private String is_task_downloaded;
     @SerializedName("expired_date")
    private java.util.Date expired_date;
     @SerializedName("status_po")
    private String status_po;
     @SerializedName("uuid_user")
    private String uuid_user;

    /** Used to resolve relations */
    private transient DaoSession daoSession;

    /** Used for active entity operations. */
    private transient ReminderPoDao myDao;

    private TaskH taskH;
    private String taskH__resolvedKey;


    public ReminderPo() {
    }

    public ReminderPo(String id) {
        this.id = id;
    }

    public ReminderPo(String id, java.util.Date dtm_crt, String uuid_task_h, String is_task_downloaded, java.util.Date expired_date, String status_po, String uuid_user) {
        this.id = id;
        this.dtm_crt = dtm_crt;
        this.uuid_task_h = uuid_task_h;
        this.is_task_downloaded = is_task_downloaded;
        this.expired_date = expired_date;
        this.status_po = status_po;
        this.uuid_user = uuid_user;
    }

    /** called by internal mechanisms, do not call yourself. */
    public void __setDaoSession(DaoSession daoSession) {
        this.daoSession = daoSession;
        myDao = daoSession != null ? daoSession.getReminderPoDao() : null;
    }

    /** Not-null value. */
    public String getId() {
        return id;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setId(String id) {
        this.id = id;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public String getUuid_task_h() {
        return uuid_task_h;
    }

    public void setUuid_task_h(String uuid_task_h) {
        this.uuid_task_h = uuid_task_h;
    }

    public String getIs_task_downloaded() {
        return is_task_downloaded;
    }

    public void setIs_task_downloaded(String is_task_downloaded) {
        this.is_task_downloaded = is_task_downloaded;
    }

    public java.util.Date getExpired_date() {
        return expired_date;
    }

    public void setExpired_date(java.util.Date expired_date) {
        this.expired_date = expired_date;
    }

    public String getStatus_po() {
        return status_po;
    }

    public void setStatus_po(String status_po) {
        this.status_po = status_po;
    }

    public String getUuid_user() {
        return uuid_user;
    }

    public void setUuid_user(String uuid_user) {
        this.uuid_user = uuid_user;
    }

    /** To-one relationship, resolved on first access. */
    public TaskH getTaskH() {
        String __key = this.uuid_task_h;
        if (taskH__resolvedKey == null || taskH__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            TaskHDao targetDao = daoSession.getTaskHDao();
            TaskH taskHNew = targetDao.load(__key);
            synchronized (this) {
                taskH = taskHNew;
            	taskH__resolvedKey = __key;
            }
        }
        return taskH;
    }

    public void setTaskH(TaskH taskH) {
        synchronized (this) {
            this.taskH = taskH;
            uuid_task_h = taskH == null ? null : taskH.getUuid_task_h();
            taskH__resolvedKey = uuid_task_h;
        }
    }

    /** Convenient call for {@link AbstractDao#delete(Object)}. Entity must attached to an entity context. */
    public void delete() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.delete(this);
    }

    /** Convenient call for {@link AbstractDao#update(Object)}. Entity must attached to an entity context. */
    public void update() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.update(this);
    }

    /** Convenient call for {@link AbstractDao#refresh(Object)}. Entity must attached to an entity context. */
    public void refresh() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.refresh(this);
    }

}
