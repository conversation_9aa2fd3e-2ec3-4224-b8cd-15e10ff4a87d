package com.adins.mss.dao;

import java.util.List;
import java.util.ArrayList;
import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.SqlUtils;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;
import de.greenrobot.dao.query.Query;
import de.greenrobot.dao.query.QueryBuilder;

import com.adins.mss.dao.PrintResult;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TR_PRINTRESULT".
*/
public class PrintResultDao extends AbstractDao<PrintResult, String> {

    public static final String TABLENAME = "TR_PRINTRESULT";

    /**
     * Properties of entity PrintResult.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_print_result = new Property(0, String.class, "uuid_print_result", true, "UUID_PRINT_RESULT");
        public final static Property Dtm_crt_server = new Property(1, java.util.Date.class, "dtm_crt_server", false, "DTM_CRT_SERVER");
        public final static Property Label = new Property(2, String.class, "label", false, "LABEL");
        public final static Property Value = new Property(3, String.class, "value", false, "VALUE");
        public final static Property Print_type_id = new Property(4, String.class, "print_type_id", false, "PRINT_TYPE_ID");
        public final static Property Usr_crt = new Property(5, String.class, "usr_crt", false, "USR_CRT");
        public final static Property Dtm_crt = new Property(6, java.util.Date.class, "dtm_crt", false, "DTM_CRT");
        public final static Property Uuid_task_h = new Property(7, String.class, "uuid_task_h", false, "UUID_TASK_H");
    };

    private DaoSession daoSession;

    private Query<PrintResult> user_PrintResultListQuery;

    public PrintResultDao(DaoConfig config) {
        super(config);
    }
    
    public PrintResultDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TR_PRINTRESULT\" (" + //
                "\"UUID_PRINT_RESULT\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_print_result
                "\"DTM_CRT_SERVER\" INTEGER," + // 1: dtm_crt_server
                "\"LABEL\" TEXT," + // 2: label
                "\"VALUE\" TEXT," + // 3: value
                "\"PRINT_TYPE_ID\" TEXT," + // 4: print_type_id
                "\"USR_CRT\" TEXT," + // 5: usr_crt
                "\"DTM_CRT\" INTEGER," + // 6: dtm_crt
                "\"UUID_TASK_H\" TEXT);"); // 7: uuid_task_h
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TR_PRINTRESULT\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, PrintResult entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_print_result());
 
        java.util.Date dtm_crt_server = entity.getDtm_crt_server();
        if (dtm_crt_server != null) {
            stmt.bindLong(2, dtm_crt_server.getTime());
        }
 
        String label = entity.getLabel();
        if (label != null) {
            stmt.bindString(3, label);
        }
 
        String value = entity.getValue();
        if (value != null) {
            stmt.bindString(4, value);
        }
 
        String print_type_id = entity.getPrint_type_id();
        if (print_type_id != null) {
            stmt.bindString(5, print_type_id);
        }
 
        String usr_crt = entity.getUsr_crt();
        if (usr_crt != null) {
            stmt.bindString(6, usr_crt);
        }
 
        java.util.Date dtm_crt = entity.getDtm_crt();
        if (dtm_crt != null) {
            stmt.bindLong(7, dtm_crt.getTime());
        }
 
        String uuid_task_h = entity.getUuid_task_h();
        if (uuid_task_h != null) {
            stmt.bindString(8, uuid_task_h);
        }
    }

    @Override
    protected void attachEntity(PrintResult entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public PrintResult readEntity(Cursor cursor, int offset) {
        PrintResult entity = new PrintResult( //
            cursor.getString(offset + 0), // uuid_print_result
            cursor.isNull(offset + 1) ? null : new java.util.Date(cursor.getLong(offset + 1)), // dtm_crt_server
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // label
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // value
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // print_type_id
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // usr_crt
            cursor.isNull(offset + 6) ? null : new java.util.Date(cursor.getLong(offset + 6)), // dtm_crt
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7) // uuid_task_h
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, PrintResult entity, int offset) {
        entity.setUuid_print_result(cursor.getString(offset + 0));
        entity.setDtm_crt_server(cursor.isNull(offset + 1) ? null : new java.util.Date(cursor.getLong(offset + 1)));
        entity.setLabel(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setValue(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setPrint_type_id(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setUsr_crt(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setDtm_crt(cursor.isNull(offset + 6) ? null : new java.util.Date(cursor.getLong(offset + 6)));
        entity.setUuid_task_h(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(PrintResult entity, long rowId) {
        return entity.getUuid_print_result();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(PrintResult entity) {
        if(entity != null) {
            return entity.getUuid_print_result();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
    /** Internal query to resolve the "printResultList" to-many relationship of User. */
    public List<PrintResult> _queryUser_PrintResultList(String uuid_task_h) {
        synchronized (this) {
            if (user_PrintResultListQuery == null) {
                QueryBuilder<PrintResult> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_task_h.eq(null));
                user_PrintResultListQuery = queryBuilder.build();
            }
        }
        Query<PrintResult> query = user_PrintResultListQuery.forCurrentThread();
        query.setParameter(0, uuid_task_h);
        return query.list();
    }

    private String selectDeep;

    protected String getSelectDeep() {
        if (selectDeep == null) {
            StringBuilder builder = new StringBuilder("SELECT ");
            SqlUtils.appendColumns(builder, "T", getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T0", daoSession.getUserDao().getAllColumns());
            builder.append(" FROM TR_PRINTRESULT T");
            builder.append(" LEFT JOIN MS_USER T0 ON T.\"UUID_TASK_H\"=T0.\"UUID_USER\"");
            builder.append(' ');
            selectDeep = builder.toString();
        }
        return selectDeep;
    }
    
    protected PrintResult loadCurrentDeep(Cursor cursor, boolean lock) {
        PrintResult entity = loadCurrent(cursor, 0, lock);
        int offset = getAllColumns().length;

        User user = loadCurrentOther(daoSession.getUserDao(), cursor, offset);
        entity.setUser(user);

        return entity;    
    }

    public PrintResult loadDeep(Long key) {
        assertSinglePk();
        if (key == null) {
            return null;
        }

        StringBuilder builder = new StringBuilder(getSelectDeep());
        builder.append("WHERE ");
        SqlUtils.appendColumnsEqValue(builder, "T", getPkColumns());
        String sql = builder.toString();
        
        String[] keyArray = new String[] { key.toString() };
        Cursor cursor = db.rawQuery(sql, keyArray);
        
        try {
            boolean available = cursor.moveToFirst();
            if (!available) {
                return null;
            } else if (!cursor.isLast()) {
                throw new IllegalStateException("Expected unique result, but count was " + cursor.getCount());
            }
            return loadCurrentDeep(cursor, true);
        } finally {
            cursor.close();
        }
    }
    
    /** Reads all available rows from the given cursor and returns a list of new ImageTO objects. */
    public List<PrintResult> loadAllDeepFromCursor(Cursor cursor) {
        int count = cursor.getCount();
        List<PrintResult> list = new ArrayList<PrintResult>(count);
        
        if (cursor.moveToFirst()) {
            if (identityScope != null) {
                identityScope.lock();
                identityScope.reserveRoom(count);
            }
            try {
                do {
                    list.add(loadCurrentDeep(cursor, false));
                } while (cursor.moveToNext());
            } finally {
                if (identityScope != null) {
                    identityScope.unlock();
                }
            }
        }
        return list;
    }
    
    protected List<PrintResult> loadDeepAllAndCloseCursor(Cursor cursor) {
        try {
            return loadAllDeepFromCursor(cursor);
        } finally {
            cursor.close();
        }
    }
    

    /** A raw-style query where you can pass any WHERE clause and arguments. */
    public List<PrintResult> queryDeep(String where, String... selectionArg) {
        Cursor cursor = db.rawQuery(getSelectDeep() + where, selectionArg);
        return loadDeepAllAndCloseCursor(cursor);
    }
 
}
