package com.adins.mss.dao;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "MS_INDUSTRY".
 */
public class Industry {

     @SerializedName("id")
    private long id;
     @SerializedName("isDeleted")
    private Integer is_deleted;
     @SerializedName("indsTypeCode")
    private String type_code;
     @SerializedName("margin")
    private Double margin;
     @SerializedName("dtmUpd")
    private java.util.Date dtm_upd;

    public Industry() {
    }

    public Industry(long id) {
        this.id = id;
    }

    public Industry(long id, Integer is_deleted, String type_code, Double margin, java.util.Date dtm_upd) {
        this.id = id;
        this.is_deleted = is_deleted;
        this.type_code = type_code;
        this.margin = margin;
        this.dtm_upd = dtm_upd;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public Integer getIs_deleted() {
        return is_deleted;
    }

    public void setIs_deleted(Integer is_deleted) {
        this.is_deleted = is_deleted;
    }

    public String getType_code() {
        return type_code;
    }

    public void setType_code(String type_code) {
        this.type_code = type_code;
    }

    public Double getMargin() {
        return margin;
    }

    public void setMargin(Double margin) {
        this.margin = margin;
    }

    public java.util.Date getDtm_upd() {
        return dtm_upd;
    }

    public void setDtm_upd(java.util.Date dtm_upd) {
        this.dtm_upd = dtm_upd;
    }

}
