package com.adins.mss.foundation.image;

import com.adins.mss.dao.TaskD;
import com.adins.mss.foundation.http.MssResponseType;
import com.google.gson.annotations.SerializedName;

import java.util.List;

public class JsonResponseImage extends MssResponseType {
    @SerializedName("img")
    List<TaskD> img;

    public List<TaskD> getImg() {
        return this.img;
    }

    public void setImg(List<TaskD> value) {
        this.img = value;
    }
}
