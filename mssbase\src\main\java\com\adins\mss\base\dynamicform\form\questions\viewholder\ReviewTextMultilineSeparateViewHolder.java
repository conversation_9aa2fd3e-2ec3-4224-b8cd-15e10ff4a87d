package com.adins.mss.base.dynamicform.form.questions.viewholder;

import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.adins.mss.base.R;
import com.adins.mss.base.dynamicform.form.questions.OnQuestionClickListener;
import com.adins.mss.foundation.questiongenerator.QuestionBean;

public class ReviewTextMultilineSeparateViewHolder extends RecyclerView.ViewHolder {
    private final OnQuestionClickListener listener;
    private final RelativeLayout layout;
    private final TextView mLabelNo;
    private final TextView mQuestionLabel;
    private final TextView qTxtAnswer1;
    private final TextView qTxtAnswer2;
    private final TextView qTxtAnswer3;
    private final TextView qTxtAnswer4;
    private final TextView qTxtAnswer5;

    private QuestionBean bean;

    public ReviewTextMultilineSeparateViewHolder(View itemView, OnQuestionClickListener listener) {
        super(itemView);
        layout = itemView.findViewById(R.id.textReviewLayout);
        mLabelNo = itemView.findViewById(R.id.questionNoLabel);
        mQuestionLabel = itemView.findViewById(R.id.questionTextLabel);
        qTxtAnswer1 = itemView.findViewById(R.id.questionTextAnswer1);
        qTxtAnswer2 = itemView.findViewById(R.id.questionTextAnswer2);
        qTxtAnswer3 = itemView.findViewById(R.id.questionTextAnswer3);
        qTxtAnswer4 = itemView.findViewById(R.id.questionTextAnswer4);
        qTxtAnswer5 = itemView.findViewById(R.id.questionTextAnswer5);
        this.listener = listener;
    }

    public void bind(final QuestionBean item, final int group, final int number) {
        bean = item;

        mLabelNo.setText(number + ".");
        String qLabel = bean.getQuestion_label();
        mQuestionLabel.setText(qLabel);

        TextView[] listAnswer = {
                qTxtAnswer1, qTxtAnswer2, qTxtAnswer3, qTxtAnswer4, qTxtAnswer5
        };

        String answer = bean.getAnswer();
        if (answer != null) {
            String[] dataColl = answer.split("\n");
            for (int i = 0; i < dataColl.length; i++) {
                listAnswer[i].setText(dataColl[i]);
                listAnswer[i].setSingleLine(false);
                listAnswer[i].setCursorVisible(false);
                listAnswer[i].setEnabled(false);
            }
        } else {
            for (TextView textView : listAnswer) {
                textView.setText("");
                textView.setSingleLine(false);
                textView.setCursorVisible(false);
                textView.setEnabled(false);
            }
        }


        layout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                listener.onReviewClickListener(bean, group, number - 1);
            }
        });
    }
}
