package com.adins.mss.foundation.db.dataaccess;

import android.content.Context;

import com.adins.mss.dao.DaoSession;
import com.adins.mss.dao.PushSync;
import com.adins.mss.dao.PushSyncDao;
import com.adins.mss.foundation.db.DaoOpenHelper;

import java.util.List;

import de.greenrobot.dao.query.QueryBuilder;

public class PushSyncDataAccess {

//	private static DaoOpenHelper daoOpenHelper;

    /**
     * use to generate dao session that you can access modelDao
     *
     * @param context --> context from activity
     * @return
     */
    protected static DaoSession getDaoSession(Context context){
		/*if(daoOpenHelper==null){
//			if(daoOpenHelper.getDaoSession()==null)
				daoOpenHelper = new DaoOpenHelper(context);
		}
		DaoSession daoSeesion = daoOpenHelper.getDaoSession();
		return daoSeesion;*/
        return DaoOpenHelper.getDaoSession(context);
    }

    /**
     * get channel dao and you can access the DB
     *
     * @param context
     * @return
     */
    protected static PushSyncDao getPushSyncDao(Context context){
        return getDaoSession(context).getPushSyncDao();
    }

    /**
     * Clear session, close db and set daoOpenHelper to null
     *
     */
    public static void closeAll(){
		/*if(daoOpenHelper!=null){
			daoOpenHelper.closeAll();
			daoOpenHelper = null;
		}*/
        DaoOpenHelper.closeAll();
    }

    /**
     * add channel as entity
     *
     * @param context
     * @param channels
     */
    public static void add(Context context, PushSync channels){
        getPushSyncDao(context).insert(channels);
        getDaoSession(context).clear();
    }

    /**
     * add channel as list entity
     *
     * @param context
     * @param channels
     */
    public static void add(Context context, List<PushSync> channels){
        getPushSyncDao(context).insertInTx(channels);
        getDaoSession(context).clear();
    }

    /**
     *
     * delete all content in table.
     *
     * @param context
     */
    public static void clean(Context context){
        getPushSyncDao(context).deleteAll();
    }

    /**
     * @param context
     * @param channels
     */
    public static void delete(Context context, PushSync channels){
        getPushSyncDao(context).deleteInTx(channels);
        getDaoSession(context).clear();
    }

    /**
     * delete all record by user
     *
     * @param context
     * @param uuidUser
     */
//    public static void delete(Context context, String uuidUser){
//        QueryBuilder<PushSync> qb = getPushSyncDao(context).queryBuilder();
//        qb.where(PushSyncDao.Properties.Uuid_user.eq(uuidUser));
//        qb.build();
//        getPushSyncDao(context).deleteInTx(qb.list());
//        getDaoSession(context).clear();
//    }

    /**
     * @param context
     * @param pushSync
     */
    public static void update(Context context, PushSync pushSync){
        getPushSyncDao(context).update(pushSync);
        getDaoSession(context).clear();
    }

    /**
     * select * from table where uuid_user = param
     *
     * @param context
     * @param
     * @return
     */
    public static List<PushSync> getAll(Context context){
        QueryBuilder<PushSync> qb = getPushSyncDao(context).queryBuilder();
        qb.where(PushSyncDao.Properties.IsActive.eq("1"));
        qb.build();
        return qb.list();
    }



    public static void addOrUpdateAll(Context context, List<PushSync> entities) {
            getPushSyncDao(context).insertOrReplaceInTx(entities);
            getDaoSession(context).clear();

    }



    /**
     * select  per
     *
     * @param context
     * @return
     */

}
