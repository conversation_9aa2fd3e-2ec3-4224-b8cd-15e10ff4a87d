package com.adins.mss.dao;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "MS_MARKET_PRICE".
 */
public class MarketPrice {

     @SerializedName("id")
    private long price_id;
     @SerializedName("assetCode")
    private String asset_code;
     @SerializedName("manfYear")
    private String manufacturing_year;
     @SerializedName("officeCode")
    private String office_code;
     @SerializedName("tolerancePrctg")
    private Double tolerance_prctg;
     @SerializedName("marketPrice")
    private Double market_price;
     @SerializedName("effDate")
    private java.util.Date effective_date;
     @SerializedName("isDeleted")
    private Integer is_deleted;
     @SerializedName("dtmUpd")
    private java.util.Date dtm_upd;

    public MarketPrice() {
    }

    public MarketPrice(long price_id) {
        this.price_id = price_id;
    }

    public MarketPrice(long price_id, String asset_code, String manufacturing_year, String office_code, Double tolerance_prctg, Double market_price, java.util.Date effective_date, Integer is_deleted, java.util.Date dtm_upd) {
        this.price_id = price_id;
        this.asset_code = asset_code;
        this.manufacturing_year = manufacturing_year;
        this.office_code = office_code;
        this.tolerance_prctg = tolerance_prctg;
        this.market_price = market_price;
        this.effective_date = effective_date;
        this.is_deleted = is_deleted;
        this.dtm_upd = dtm_upd;
    }

    public long getPrice_id() {
        return price_id;
    }

    public void setPrice_id(long price_id) {
        this.price_id = price_id;
    }

    public String getAsset_code() {
        return asset_code;
    }

    public void setAsset_code(String asset_code) {
        this.asset_code = asset_code;
    }

    public String getManufacturing_year() {
        return manufacturing_year;
    }

    public void setManufacturing_year(String manufacturing_year) {
        this.manufacturing_year = manufacturing_year;
    }

    public String getOffice_code() {
        return office_code;
    }

    public void setOffice_code(String office_code) {
        this.office_code = office_code;
    }

    public Double getTolerance_prctg() {
        return tolerance_prctg;
    }

    public void setTolerance_prctg(Double tolerance_prctg) {
        this.tolerance_prctg = tolerance_prctg;
    }

    public Double getMarket_price() {
        return market_price;
    }

    public void setMarket_price(Double market_price) {
        this.market_price = market_price;
    }

    public java.util.Date getEffective_date() {
        return effective_date;
    }

    public void setEffective_date(java.util.Date effective_date) {
        this.effective_date = effective_date;
    }

    public Integer getIs_deleted() {
        return is_deleted;
    }

    public void setIs_deleted(Integer is_deleted) {
        this.is_deleted = is_deleted;
    }

    public java.util.Date getDtm_upd() {
        return dtm_upd;
    }

    public void setDtm_upd(java.util.Date dtm_upd) {
        this.dtm_upd = dtm_upd;
    }

}
