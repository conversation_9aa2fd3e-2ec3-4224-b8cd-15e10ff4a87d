apply plugin: 'com.android.library'

android {
    compileSdkVersion 28
    android.packageBuildConfig = false

    defaultConfig {
        minSdkVersion 16
        targetSdkVersion 28
        versionCode 3
        versionName "1.0"
        useLibrary 'org.apache.http.legacy'
        multiDexEnabled true
    }
    buildTypes {
        release {
            minifyEnabled false
            debuggable false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    dexOptions {
        javaMaxHeapSize "5g"
    }

}

dependencies {
    api fileTree(dir: 'libs', include: '*.jar')
    api files('libs/commons-net-3.6.jar')
    api 'com.google.android.gms:play-services-location:17.1.0'
    api 'com.google.android.gms:play-services-maps:17.0.0'
    api group: 'com.androidquery', name: 'androidquery', version: '0.26.8'
    api group: 'org.bouncycastle', name: 'bcprov-jdk16', version: '1.46'
    api group: 'com.bixolon', name: 'printer', version: '1.0'
    api group: 'com.github.invicnaper', name: 'btsdk', version: '1.0'
    api group: 'commons-io', name: 'commons-io', version: '2.5'
    api group: 'org.apache.commons', name: 'commons-jexl', version: '2.1.1'
    api group: 'com.google.code.gson', name: 'gson', version: '2.6.2'
    api group: 'com.fasterxml.uuid', name: 'java-uuid-generator', version: '3.1.3'
    api group: 'com.github.sin90lzc', name: 'jexel', version: '1.0.0', classifier: 'beta_13'
    api group: 'com.squareup.okhttp', name: 'okhttp', version: '2.2.0'
    api group: 'com.squareup.okio', name: 'okio', version: '1.7.0'
    api group: 'RT_Android_Bluetooth_SDK', name: 'RT_Android_Bluetooth_SDK', version: '1.0'
    api group: 'com.woosim', name: 'printer', version: '1.0', classifier: 'WoosimLib240'
    api group: 'com.google.zxing', name: 'zixng', version: '1.0'
    api group: 'com.zebra', name: 'android-sdk', version: '1.0'
    api 'com.google.firebase:firebase-messaging:20.2.4'
    api 'com.google.firebase:firebase-core:17.5.0'
    api 'com.google.firebase:firebase-storage:19.2.0'
    api 'com.google.firebase:firebase-database:19.4.0'
    api 'com.google.firebase:firebase-analytics:17.5.0'
    api 'com.google.firebase:firebase-crashlytics:17.2.1'
    api 'com.google.firebase:firebase-perf:19.0.8'
    api 'com.squareup.picasso:picasso:2.5.2'
    api 'org.apache.commons:commons-lang3:3.1'
    api 'com.squareup.retrofit2:retrofit:2.0.2'
    api 'com.squareup.retrofit2:converter-gson:2.0.2'
    api 'androidx.appcompat:appcompat:1.0.0'
    api 'com.google.android.material:material:1.0.0'
    api 'androidx.recyclerview:recyclerview:1.0.0'
    api 'androidx.cardview:cardview:1.0.0'
    api 'androidx.legacy:legacy-support-v4:1.0.0'
    api 'androidx.constraintlayout:constraintlayout:1.1.3'
    api 'jp.wasabeef:recyclerview-animators:2.2.7'
    api 'androidx.multidex:multidex:2.0.0'
    api 'de.greenrobot:eventbus:2.4.0'
    api 'org.greenrobot:greendao-encryption:2.2.2'
    api 'net.zetetic:android-database-sqlcipher:3.5.1'
    api group: 'com.fasterxml.jackson.core', name: 'jackson-core', version: '2.9.4'
    api group: 'com.fasterxml.jackson.core', name: 'jackson-annotations', version: '2.9.4'

    implementation 'androidx.viewpager:viewpager:1.0.0'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'
    implementation 'com.google.mlkit:face-detection:16.0.0'

    testImplementation 'junit:junit:4.12'

    configurations {
        all*.exclude group: 'commons-logging', module: 'commons-logging'
    }
}
apply plugin: 'com.google.gms.google-services'
apply from: "$project.rootDir/jacoco.gradle"

repositories {
    mavenCentral()
}