package com.adins.mss.dao;

import com.adins.mss.dao.DaoSession;
import de.greenrobot.dao.DaoException;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "MS_GENERALPARAMETER".
 */
public class GeneralParameter {

    /** Not-null value. */
     @SerializedName("uuid_general_parameter")
    private String uuid_general_parameter;
     @SerializedName("gs_value")
    private String gs_value;
     @SerializedName("gs_code")
    private String gs_code;
     @SerializedName("usr_crt")
    private String usr_crt;
     @SerializedName("dtm_crt")
    private java.util.Date dtm_crt;
     @SerializedName("usr_upd")
    private String usr_upd;
     @SerializedName("dtm_upd")
    private java.util.Date dtm_upd;
    /** Not-null value. */
     @SerializedName("uuid_user")
    private String uuid_user;

    /** Used to resolve relations */
    private transient DaoSession daoSession;

    /** Used for active entity operations. */
    private transient GeneralParameterDao myDao;

    private User user;
    private String user__resolvedKey;


    public GeneralParameter() {
    }

    public GeneralParameter(String uuid_general_parameter) {
        this.uuid_general_parameter = uuid_general_parameter;
    }

    public GeneralParameter(String uuid_general_parameter, String gs_value, String gs_code, String usr_crt, java.util.Date dtm_crt, String usr_upd, java.util.Date dtm_upd, String uuid_user) {
        this.uuid_general_parameter = uuid_general_parameter;
        this.gs_value = gs_value;
        this.gs_code = gs_code;
        this.usr_crt = usr_crt;
        this.dtm_crt = dtm_crt;
        this.usr_upd = usr_upd;
        this.dtm_upd = dtm_upd;
        this.uuid_user = uuid_user;
    }

    /** called by internal mechanisms, do not call yourself. */
    public void __setDaoSession(DaoSession daoSession) {
        this.daoSession = daoSession;
        myDao = daoSession != null ? daoSession.getGeneralParameterDao() : null;
    }

    /** Not-null value. */
    public String getUuid_general_parameter() {
        return uuid_general_parameter;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_general_parameter(String uuid_general_parameter) {
        this.uuid_general_parameter = uuid_general_parameter;
    }

    public String getGs_value() {
        return gs_value;
    }

    public void setGs_value(String gs_value) {
        this.gs_value = gs_value;
    }

    public String getGs_code() {
        return gs_code;
    }

    public void setGs_code(String gs_code) {
        this.gs_code = gs_code;
    }

    public String getUsr_crt() {
        return usr_crt;
    }

    public void setUsr_crt(String usr_crt) {
        this.usr_crt = usr_crt;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public String getUsr_upd() {
        return usr_upd;
    }

    public void setUsr_upd(String usr_upd) {
        this.usr_upd = usr_upd;
    }

    public java.util.Date getDtm_upd() {
        return dtm_upd;
    }

    public void setDtm_upd(java.util.Date dtm_upd) {
        this.dtm_upd = dtm_upd;
    }

    /** Not-null value. */
    public String getUuid_user() {
        return uuid_user;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_user(String uuid_user) {
        this.uuid_user = uuid_user;
    }

    /** To-one relationship, resolved on first access. */
    public User getUser() {
        String __key = this.uuid_user;
        if (user__resolvedKey == null || user__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            UserDao targetDao = daoSession.getUserDao();
            User userNew = targetDao.load(__key);
            synchronized (this) {
                user = userNew;
            	user__resolvedKey = __key;
            }
        }
        return user;
    }

    public void setUser(User user) {
        if (user == null) {
            throw new DaoException("To-one property 'uuid_user' has not-null constraint; cannot set to-one to null");
        }
        synchronized (this) {
            this.user = user;
            uuid_user = user.getUuid_user();
            user__resolvedKey = uuid_user;
        }
    }

    /** Convenient call for {@link AbstractDao#delete(Object)}. Entity must attached to an entity context. */
    public void delete() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.delete(this);
    }

    /** Convenient call for {@link AbstractDao#update(Object)}. Entity must attached to an entity context. */
    public void update() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.update(this);
    }

    /** Convenient call for {@link AbstractDao#refresh(Object)}. Entity must attached to an entity context. */
    public void refresh() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.refresh(this);
    }

}
