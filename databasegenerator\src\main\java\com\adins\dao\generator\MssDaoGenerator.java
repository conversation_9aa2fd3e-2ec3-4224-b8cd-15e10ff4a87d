package com.adins.dao.generator;

import de.greenrobot.daogenerator.DaoGenerator;
import de.greenrobot.daogenerator.Entity;
import de.greenrobot.daogenerator.Index;
import de.greenrobot.daogenerator.Property;
import de.greenrobot.daogenerator.Schema;

public class MssDaoGenerator {
    public static void main(String[] args) throws Exception {
        Schema schema = new Schema(26, "com.adins.mss.dao");
        general(schema);

        new DaoGenerator().generateAll(schema, "mssbase/src/main/java");
    }

    private static void general(Schema schema) {

        //TR_COLLECTIONACTIVITY
        Entity collectionActivity = schema.addEntity("CollectionActivity").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");

        collectionActivity.setTableName("TR_COLLECTIONACTIVITY");

        collectionActivity.addStringProperty("uuid_collection_activity").notNull().primaryKey().codeBeforeField("@SerializedName(\"uuid_collection_activity\")");
        collectionActivity.addStringProperty("uuid_task_h").codeBeforeField("@SerializedName(\"uuid_task_h\")");
        collectionActivity.addStringProperty("agreement_no").codeBeforeField("@SerializedName(\"agreement_no\")");
        collectionActivity.addStringProperty("branch_code").codeBeforeField("@SerializedName(\"branch_code\")");
        collectionActivity.addStringProperty("collector_name").codeBeforeField("@SerializedName(\"collector_name\")");
        collectionActivity.addStringProperty("activity").codeBeforeField("@SerializedName(\"activity\")");
        collectionActivity.addStringProperty("result").codeBeforeField("@SerializedName(\"result\")");
        collectionActivity.addStringProperty("notes").codeBeforeField("@SerializedName(\"notes\")");
        collectionActivity.addStringProperty("overdue_days").codeBeforeField("@SerializedName(\"overdue_days\")");
        collectionActivity.addDateProperty("activity_date").codeBeforeField("@SerializedName(\"activity_date\")");
        collectionActivity.addDateProperty("ptp_date").codeBeforeField("@SerializedName(\"ptp_date\")");
        collectionActivity.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        collectionActivity.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        collectionActivity.addStringProperty("usr_upd").codeBeforeField("@SerializedName(\"usr_upd\")");
        collectionActivity.addDateProperty("dtm_upd").codeBeforeField("@SerializedName(\"dtm_upd\")");
        collectionActivity.addDateProperty("next_plan_date").codeBeforeField("@SerializedName(\"next_plan_date\")");
        collectionActivity.addStringProperty("next_plan_action").codeBeforeField("@SerializedName(\"next_plan_action\")");

        //InstallmentSchedule TR_INSTALLMENTSCHEDULE
        Entity installmentSchedule = schema.addEntity("InstallmentSchedule").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        installmentSchedule.setTableName("TR_INSTALLMENTSCHEDULE");

        installmentSchedule.addStringProperty("uuid_installment_schedule").notNull().primaryKey().codeBeforeField("@SerializedName(\"uuid_installment_schedule\")");
        installmentSchedule.addStringProperty("uuid_task_h").codeBeforeField("@SerializedName(\"uuid_task_h\")");
        installmentSchedule.addStringProperty("agreement_no").codeBeforeField("@SerializedName(\"agreement_no\")");
        installmentSchedule.addStringProperty("branch_code").codeBeforeField("@SerializedName(\"branch_code\")");
        installmentSchedule.addStringProperty("installment_no").codeBeforeField("@SerializedName(\"installment_no\")");
        installmentSchedule.addStringProperty("installment_amount").codeBeforeField("@SerializedName(\"installment_amount\")");
        installmentSchedule.addStringProperty("instl_paid_amount").codeBeforeField("@SerializedName(\"instl_paid_amount\")");
        installmentSchedule.addStringProperty("lc_instl_amount").codeBeforeField("@SerializedName(\"lc_instl_amount\")");
        installmentSchedule.addStringProperty("lc_instl_paid").codeBeforeField("@SerializedName(\"lc_instl_paid\")");
        installmentSchedule.addStringProperty("lc_instl_waived").codeBeforeField("@SerializedName(\"lc_instl_waived\")");
        installmentSchedule.addStringProperty("principal_amount").codeBeforeField("@SerializedName(\"principal_amount\")");
        installmentSchedule.addStringProperty("interest_amount").codeBeforeField("@SerializedName(\"interest_amount\")");
        installmentSchedule.addStringProperty("os_principal_amount").codeBeforeField("@SerializedName(\"os_principal_amount\")");
        installmentSchedule.addStringProperty("os_interest_amount").codeBeforeField("@SerializedName(\"os_interest_amount\")");
        installmentSchedule.addStringProperty("lc_days").codeBeforeField("@SerializedName(\"lc_days\")");
        installmentSchedule.addStringProperty("lc_admin_fee").codeBeforeField("@SerializedName(\"lc_admin_fee\")");
        installmentSchedule.addStringProperty("lc_admin_fee_paid").codeBeforeField("@SerializedName(\"lc_admin_fee_paid\")");
        installmentSchedule.addStringProperty("lc_admin_fee_waive").codeBeforeField("@SerializedName(\"lc_admin_fee_waive\")");
        installmentSchedule.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        installmentSchedule.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        installmentSchedule.addDateProperty("due_date").codeBeforeField("@SerializedName(\"due_date\")");
        installmentSchedule.addDateProperty("instl_paid_date").codeBeforeField("@SerializedName(\"instl_paid_date\")");


        //MS_GENERALPARAMETER
        Entity generalParameter = schema.addEntity("GeneralParameter").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        generalParameter.setTableName("MS_GENERALPARAMETER");

        generalParameter.addStringProperty("uuid_general_parameter").notNull().primaryKey().codeBeforeField("@SerializedName(\"uuid_general_parameter\")");
        generalParameter.addStringProperty("gs_value").codeBeforeField("@SerializedName(\"gs_value\")");
        generalParameter.addStringProperty("gs_code").codeBeforeField("@SerializedName(\"gs_code\")");
        generalParameter.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        generalParameter.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        generalParameter.addStringProperty("usr_upd").codeBeforeField("@SerializedName(\"usr_upd\")");
        generalParameter.addDateProperty("dtm_upd").codeBeforeField("@SerializedName(\"dtm_upd\")");
        Property fkGeneralParameterUuidUser = generalParameter.addStringProperty("uuid_user").notNull()
                .codeBeforeField("@SerializedName(\"uuid_user\")").getProperty();


        //MS_GROUPUSER
        Entity groupUser = schema.addEntity("GroupUser").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        groupUser.setTableName("MS_GROUPUSER");

        groupUser.addStringProperty("uuid_group_user").notNull().primaryKey().codeBeforeField("@SerializedName(\"uuid_group_user\")");
        groupUser.addStringProperty("group_id").codeBeforeField("@SerializedName(\"group_id\")");
        groupUser.addStringProperty("is_admin").codeBeforeField("@SerializedName(\"is_admin\")");
        groupUser.addStringProperty("group_name").codeBeforeField("@SerializedName(\"group_name\")");
        groupUser.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        groupUser.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        groupUser.addStringProperty("usr_upd").codeBeforeField("@SerializedName(\"usr_upd\")");
        groupUser.addDateProperty("dtm_upd").codeBeforeField("@SerializedName(\"dtm_upd\")");
        Property fkGroupUserUuidUser = groupUser.addStringProperty("uuid_user")
                .codeBeforeField("@SerializedName(\"uuid_user\")").getProperty();


        //MS_LOOKUP
        Entity lookup = schema.addEntity("Lookup").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        lookup.setTableName("MS_LOOKUP");

        lookup.addStringProperty("uuid_lookup").notNull().primaryKey().codeBeforeField("@SerializedName(\"uuid_lookup\")").getProperty();
        lookup.addStringProperty("option_id").codeBeforeField("@SerializedName(\"option_id\")");
        Property code = lookup.addStringProperty("code").codeBeforeField("@SerializedName(\"code\")").getProperty();
        lookup.addStringProperty("value").codeBeforeField("@SerializedName(\"value\")");
        Property filter1 = lookup.addStringProperty("filter1").codeBeforeField("@SerializedName(\"filter1\")").getProperty();
        Property filter2 = lookup.addStringProperty("filter2").codeBeforeField("@SerializedName(\"filter2\")").getProperty();
        Property filter3 = lookup.addStringProperty("filter3").codeBeforeField("@SerializedName(\"filter3\")").getProperty();
        Property filter4 = lookup.addStringProperty("filter4").codeBeforeField("@SerializedName(\"filter4\")").getProperty();
        Property filter5 = lookup.addStringProperty("filter5").codeBeforeField("@SerializedName(\"filter5\")").getProperty();

        lookup.addIntProperty("sequence").codeBeforeField("@SerializedName(\"sequence\")").getProperty();
        lookup.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        lookup.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        lookup.addStringProperty("usr_upd").codeBeforeField("@SerializedName(\"usr_upd\")");
        lookup.addDateProperty("dtm_upd").codeBeforeField("@SerializedName(\"dtm_upd\")");
        lookup.addStringProperty("uuid_question_set").codeBeforeField("@SerializedName(\"uuid_question_set\")").getProperty();
        Property lovGroup =  lookup.addStringProperty("lov_group").codeBeforeField("@SerializedName(\"lov_group\")").getProperty();
        Property isActive = lookup.addStringProperty("is_active").codeBeforeField("@SerializedName(\"is_active\")").getProperty();
        Property isDeleted = lookup.addStringProperty("is_deleted").codeBeforeField("@SerializedName(\"is_deleted\")").getProperty();

        Index indexLookup = new Index();
        indexLookup.addProperty(lovGroup);
        indexLookup.addProperty(filter1);
        indexLookup.addProperty(filter2);
        indexLookup.addProperty(filter3);
        indexLookup.addProperty(filter4);
        indexLookup.addProperty(filter5);
        indexLookup.addProperty(isActive);
        indexLookup.addProperty(isDeleted);

        lookup.addIndex(indexLookup);

        Index indexLookup2 = new Index();
        indexLookup2.addProperty(code);
        indexLookup2.addProperty(lovGroup);
        indexLookup2.addProperty(isActive);
        indexLookup2.addProperty(isDeleted);

        lookup.addIndex(indexLookup2);

        Index indexLookup3 = new Index();
        indexLookup3.addProperty(lovGroup);
        indexLookup3.addProperty(filter1);
        indexLookup3.addProperty(filter2);
        indexLookup3.addProperty(filter3);
        indexLookup3.addProperty(filter4);
        indexLookup3.addProperty(isActive);
        indexLookup3.addProperty(isDeleted);

        lookup.addIndex(indexLookup3);

        Index indexLookup4 = new Index();
        indexLookup4.addProperty(lovGroup);
        indexLookup4.addProperty(filter1);
        indexLookup4.addProperty(filter2);
        indexLookup4.addProperty(filter3);
        indexLookup4.addProperty(isActive);
        indexLookup4.addProperty(isDeleted);

        lookup.addIndex(indexLookup4);

        Index indexLookup5 = new Index();
        indexLookup5.addProperty(lovGroup);
        indexLookup5.addProperty(filter1);
        indexLookup5.addProperty(filter2);
        indexLookup5.addProperty(isActive);
        indexLookup5.addProperty(isDeleted);

        lookup.addIndex(indexLookup5);

        Index indexLookup6 = new Index();
        indexLookup6.addProperty(lovGroup);
        indexLookup6.addProperty(filter1);
        indexLookup6.addProperty(isActive);
        indexLookup6.addProperty(isDeleted);

        lookup.addIndex(indexLookup6);

        Index indexLookup7 = new Index();
        indexLookup7.addProperty(lovGroup);
        indexLookup7.addProperty(isActive);
        indexLookup7.addProperty(isDeleted);

        lookup.addIndex(indexLookup7);


        //MS_SYNC
        Entity sync = schema.addEntity("Sync").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        sync.setTableName("MS_SYNC");

        sync.addStringProperty("uuid_sync").notNull().primaryKey().codeBeforeField("@SerializedName(\"uuid_sync\")");
        sync.addStringProperty("tabel_name").codeBeforeField("@SerializedName(\"tabel_name\")");
        sync.addStringProperty("lov_group").codeBeforeField("@SerializedName(\"lov_group\")");
        sync.addStringProperty("path").codeBeforeField("@SerializedName(\"path\")");
        sync.addDateProperty("dtm_upd").codeBeforeField("@SerializedName(\"dtm_upd\")");
        sync.addIntProperty("flag").codeBeforeField("@SerializedName(\"flag\")");

        /**
         * NEW UPDATE WOM PROJECT
         */

        Entity migration = schema.addEntity("Migration").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        migration.setTableName("MS_MIGRATION");

        migration.addLongProperty("id").primaryKey().notNull().codeBeforeField("@SerializedName(\"id\")");
        migration.addStringProperty("version").notNull().codeBeforeField("@SerializedName(\"version\")");
        migration.addStringProperty("description").notNull().codeBeforeField("@SerializedName(\"description\")");

        //MS_RULES
        Entity rules = schema.addEntity("Rule").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        rules.setTableName("MS_RULES");

        rules.addLongProperty("id").primaryKey().notNull().codeBeforeField("@SerializedName(\"id\")");
        rules.addIntProperty("is_deleted").notNull().codeBeforeField("@SerializedName(\"isDeleted\")");
        rules.addStringProperty("rule_name").notNull().codeBeforeField("@SerializedName(\"ruleName\")");
        rules.addStringProperty("url").notNull().codeBeforeField("@SerializedName(\"url\")");
        rules.addDateProperty("dtm_upd").codeBeforeField("@SerializedName(\"dtmUpd\")");

        //MS_BLACKLIST
        Entity blacklist = schema.addEntity("Blacklist").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        blacklist.setTableName("MS_BLACKLIST");

        blacklist.addLongProperty("id").primaryKey().notNull().codeBeforeField("@SerializedName(\"id\")");
        blacklist.addStringProperty("exclude_info1").codeBeforeField("@SerializedName(\"excludeInfo1\")");
        blacklist.addStringProperty("exclude_info2").codeBeforeField("@SerializedName(\"excludeInfo2\")");
        blacklist.addStringProperty("exclude_type_code").notNull().codeBeforeField("@SerializedName(\"excludeTypeCode\")");
        blacklist.addStringProperty("exclude_type_name").notNull().codeBeforeField("@SerializedName(\"excludeTypeName\")");
        blacklist.addIntProperty("is_deleted").codeBeforeField("@SerializedName(\"isDeleted\")");
        blacklist.addDateProperty("dtm_upd").codeBeforeField("@SerializedName(\"dtmUpd\")");

        //MS_PO
        Entity po = schema.addEntity("ProductOffering").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        po.setTableName("MS_PO");

        po.addLongProperty("id").primaryKey().notNull().codeBeforeField("@SerializedName(\"id\")");
        po.addIntProperty("asset_scheme_id").notNull().codeBeforeField("@SerializedName(\"assetSchemeId\")");
        po.addIntProperty("dealer_scheme_id").notNull().codeBeforeField("@SerializedName(\"dealerSchemeId\")");
        Property branchId = po.addStringProperty("branch_id").codeBeforeField("@SerializedName(\"branchId\")").getProperty();
        po.addStringProperty("prod_off_id").notNull().codeBeforeField("@SerializedName(\"prodOffId\")");
        po.addStringProperty("prod_off_name").codeBeforeField("@SerializedName(\"prodOffName\")");
        Property prodCatCode = po.addStringProperty("prod_cat_code").codeBeforeField("@SerializedName(\"prodCatCode\")").getProperty();
        po.addStringProperty("prod_cat_name").codeBeforeField("@SerializedName(\"prodCatName\")");
        Property jnsPembiayaan = po.addStringProperty("jns_pembiayaan").codeBeforeField("@SerializedName(\"jnsPmbiayaan\")").getProperty();
        po.addIntProperty("min_tenor").codeBeforeField("@SerializedName(\"minTenor\")");
        po.addIntProperty("max_tenor").codeBeforeField("@SerializedName(\"maxTenor\")");
        po.addStringProperty("sc_id").codeBeforeField("@SerializedName(\"scId\")");
        Property component = po.addStringProperty("component").codeBeforeField("@SerializedName(\"component\")").getProperty();
//        po.addStringProperty("rule_asset_price").codeBeforeField("@SerializedName(\"ruleDataAstPrice\")");
//        po.addStringProperty("rule_min_dp").codeBeforeField("@SerializedName(\"ruleDataMinDp\")");
//        po.addStringProperty("rule_app_tc").codeBeforeField("@SerializedName(\"ruleDataAppTc\")");
//        po.addStringProperty("rule_scoring").codeBeforeField("@SerializedName(\"ruleDataScoring\")");
//        po.addStringProperty("rule_matrix").codeBeforeField("@SerializedName(\"ruleDataScoringMatrix\")");
        po.addIntProperty("rule_data_min_tdp").codeBeforeField("@SerializedName(\"ruleDataMinTdp\")");
        po.addIntProperty("rule_data_manf_year").codeBeforeField("@SerializedName(\"ruleDataManfYear\")");
        po.addIntProperty("rule_asset_price").codeBeforeField("@SerializedName(\"ruleDataAstPrice\")");
        po.addIntProperty("rule_min_dp").codeBeforeField("@SerializedName(\"ruleDataMinDp\")");
        po.addIntProperty("rule_app_tc").codeBeforeField("@SerializedName(\"ruleDataAppTc\")");
        po.addIntProperty("rule_scoring").codeBeforeField("@SerializedName(\"ruleDataScoring\")");
        po.addIntProperty("rule_matrix").codeBeforeField("@SerializedName(\"ruleDataScoringMatrix\")");
        po.addIntProperty("is_deleted").codeBeforeField("@SerializedName(\"isDeleted\")");
        po.addDateProperty("dtm_upd").codeBeforeField("@SerializedName(\"dtmUpd\")");
        po.addIntProperty("show_pot").codeBeforeField("@SerializedName(\"isShow\")");

        Index poIndex = new Index();
        poIndex.addProperty(jnsPembiayaan);
        poIndex.addProperty(branchId);
        poIndex.addProperty(prodCatCode);
        poIndex.addProperty(component);
        po.addIndex(poIndex);

        //MS_PO_DEALER
        Entity poDealer = schema.addEntity("PODealer").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        poDealer.setTableName("MS_PO_DEALER");

        poDealer.addLongProperty("id").notNull().primaryKey().autoincrement().codeBeforeField("@SerializedName(\"id\")");
        Property dealerId = poDealer.addStringProperty("dealer_id").notNull().codeBeforeField("@SerializedName(\"dealerId\")").getProperty();
        poDealer.addIntProperty("dealer_scheme_id").notNull().codeBeforeField("@SerializedName(\"dealerSchemeId\")");
        poDealer.addStringProperty("dealer_name").codeBeforeField("@SerializedName(\"dealerName\")");
        poDealer.addDateProperty("dtm_upd").codeBeforeField("@SerializedName(\"dtmUpd\")");
        poDealer.addIntProperty("is_deleted").codeBeforeField("@SerializedName(\"isDeleted\")");
//        po_dealer.addStringProperty("prod_off_id").codeBeforeField("@SerializedName(\"prodOffId\")");
//        Property fk_po_dealer_pro_off_id = po_dealer.addStringProperty("prod_off_id")
//                .codeBeforeField("@SerializedName(\"prodOffId\")").getProperty();

        Index idxDealer = new Index();
        idxDealer.addProperty(dealerId);
        poDealer.addIndex(idxDealer);

        //MS_ASSET_SCHEME
        Entity assetScheme = schema.addEntity("AssetScheme").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        assetScheme.setTableName("MS_ASSET_SCHEME");

        assetScheme.addLongProperty("id").notNull().primaryKey().autoincrement().codeBeforeField("@SerializedName(\"id\")");
        assetScheme.addIntProperty("asset_scheme_id").notNull().codeBeforeField("@SerializedName(\"assetSchemeId\")");
        Property typeCode = assetScheme.addStringProperty("type_code").notNull().codeBeforeField("@SerializedName(\"assetTypeCode\")").getProperty();
        assetScheme.addStringProperty("type_name").notNull().codeBeforeField("@SerializedName(\"assetTypeName\")");
        assetScheme.addIntProperty("is_deleted").codeBeforeField("@SerializedName(\"isDeleted\")");
        assetScheme.addDateProperty("dtm_upd").codeBeforeField("@SerializedName(\"dtmUpd\")");

        Index idxAssetScheme = new Index();
        idxAssetScheme.addProperty(typeCode);
        assetScheme.addIndex(idxAssetScheme);

        //MS_PO_ASSET
        Entity poAsset = schema.addEntity("POAsset").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        poAsset.setTableName("MS_PO_ASSET");

        poAsset.addLongProperty("id").notNull().primaryKey().autoincrement().codeBeforeField("@SerializedName(\"id\")");
        poAsset.addIntProperty("is_deleted").codeBeforeField("@SerializedName(\"isDeleted\")");
        poAsset.addIntProperty("asset_scheme_id").notNull().codeBeforeField("@SerializedName(\"assetSchemeId\")");
        //Property typeCode = po_asset.addStringProperty("type_code").notNull().codeBeforeField("@SerializedName(\"assetTypeCode\")").getProperty();
        //po_asset.addStringProperty("type_name").notNull().codeBeforeField("@SerializedName(\"assetTypeName\")");
        poAsset.addStringProperty("brand_code").notNull().codeBeforeField("@SerializedName(\"brandCode\")");
        Property brandName= poAsset.addStringProperty("brand_name").notNull().codeBeforeField("@SerializedName(\"brandName\")").getProperty();
        poAsset.addStringProperty("model_code").notNull().codeBeforeField("@SerializedName(\"modelCode\")");
        Property modelName= poAsset.addStringProperty("model_name").notNull().codeBeforeField("@SerializedName(\"modelName\")").getProperty();
        Property groupType= poAsset.addStringProperty("group_type").notNull().codeBeforeField("@SerializedName(\"groupType\")").getProperty();
        poAsset.addStringProperty("master_code").notNull().codeBeforeField("@SerializedName(\"masterCode\")");
        Property mstName  = poAsset.addStringProperty("master_name").notNull().codeBeforeField("@SerializedName(\"masterName\")").getProperty();
        poAsset.addDateProperty("dtm_upd").codeBeforeField("@SerializedName(\"dtmUpd\")");
        //po_asset.addStringProperty("prod_off_id").codeBeforeField("@SerializedName(\"prodOffId\")");
//        Property fk_po_asset_pro_off_id = po_asset.addStringProperty("prod_off_id")
//                .codeBeforeField("@SerializedName(\"prodOffId\")").getProperty();

        Index assetIndex    = new Index();
        assetIndex.addProperty(brandName);
        poAsset.addIndex(assetIndex);
//
        Index assetIndex02 = new Index();
        assetIndex02.addProperty(brandName);
        assetIndex02.addProperty(modelName);
        poAsset.addIndex(assetIndex02);
//
        Index assetIndex03 = new Index();
        assetIndex03.addProperty(brandName);
        assetIndex03.addProperty(modelName);
        assetIndex03.addProperty(groupType);
        poAsset.addIndex(assetIndex03);
//
//        Index assetIndex_04 = new Index();
//        assetIndex_04.addProperty(brandName);
//        assetIndex_04.addProperty(modelName);
//        assetIndex_04.addProperty(groupType);
//        assetIndex_04.addProperty(mstName);
//        po_asset.addIndex(assetIndex_04);

        Index assetIndex05 = new Index();
        assetIndex05.addProperty(brandName);
        assetIndex05.addProperty(modelName);
        assetIndex05.addProperty(groupType);
        assetIndex05.addProperty(mstName);
        poAsset.addIndex(assetIndex05);

        //MS_INDUSTRY
        Entity industry = schema.addEntity("Industry").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        industry.setTableName("MS_INDUSTRY");

        industry.addLongProperty("id").notNull().primaryKey().autoincrement().codeBeforeField("@SerializedName(\"id\")");
        industry.addIntProperty("is_deleted").codeBeforeField("@SerializedName(\"isDeleted\")");
        industry.addStringProperty("type_code").codeBeforeField("@SerializedName(\"indsTypeCode\")");
        industry.addDoubleProperty("margin").codeBeforeField("@SerializedName(\"margin\")");
        industry.addDateProperty("dtm_upd").codeBeforeField("@SerializedName(\"dtmUpd\")");

        Entity tmpRule = schema.addEntity("Decision").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        tmpRule.setTableName("MS_TMP_RULE");
        tmpRule.addLongProperty("id").notNull().primaryKey().autoincrement().codeBeforeField("@SerializedName(\"id\")");
        Property tmpRuleCode = tmpRule.addStringProperty("code").codeBeforeField("@SerializedName(\"code\")").getProperty();
        Property tmpRuleValue= tmpRule.addStringProperty("value").codeBeforeField("@SerializedName(\"value\")").getProperty();
        Property tmpRuleColum=tmpRule.addIntProperty("column_no").codeBeforeField("@SerializedName(\"columnNo\")").getProperty();
        Property tmpRuleRow  = tmpRule.addIntProperty("row_no").codeBeforeField("@SerializedName(\"rowNo\")").getProperty();
        tmpRule.addStringProperty("obj_name").codeBeforeField("@SerializedName(\"objName\")");

        Index tmpRuleIndex = new Index();
        tmpRuleIndex.addProperty(tmpRuleCode);
        tmpRuleIndex.addProperty(tmpRuleColum);
        tmpRuleIndex.addProperty(tmpRuleValue);
        tmpRule.addIndex(tmpRuleIndex);

        Index tmpRuleIndex2= new Index();
        tmpRuleIndex2.addProperty(tmpRuleCode);
        tmpRuleIndex2.addProperty(tmpRuleValue);
        tmpRule.addIndex(tmpRuleIndex2);

        Index tmpRUleIndex3= new Index();
        tmpRUleIndex3.addProperty(tmpRuleRow);
        tmpRule.addIndex(tmpRUleIndex3);

        // MS_MARKET_PRICE

        Entity marketPrice = schema.addEntity("MarketPrice").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        marketPrice.setTableName("MS_MARKET_PRICE");
        marketPrice.addLongProperty("price_id").notNull().primaryKey().autoincrement().codeBeforeField("@SerializedName(\"id\")");
        Property mpAsset = marketPrice.addStringProperty("asset_code").codeBeforeField("@SerializedName(\"assetCode\")").getProperty();
        Property mpManufaktur = marketPrice.addStringProperty("manufacturing_year").codeBeforeField("@SerializedName(\"manfYear\")").getProperty();
        Property mpOfficeCode = marketPrice.addStringProperty("office_code").codeBeforeField("@SerializedName(\"officeCode\")").getProperty();
        marketPrice.addDoubleProperty("tolerance_prctg").codeBeforeField("@SerializedName(\"tolerancePrctg\")");
        marketPrice.addDoubleProperty("market_price").codeBeforeField("@SerializedName(\"marketPrice\")");
        marketPrice.addDateProperty("effective_date").codeBeforeField("@SerializedName(\"effDate\")");
        Property mpDelete = marketPrice.addIntProperty("is_deleted").codeBeforeField("@SerializedName(\"isDeleted\")").getProperty();
        marketPrice.addDateProperty("dtm_upd").codeBeforeField("@SerializedName(\"dtmUpd\")");

        Index mpIndex1 = new Index();
        mpIndex1.addProperty(mpAsset);
        mpIndex1.addProperty(mpManufaktur);
        mpIndex1.addProperty(mpOfficeCode);
        mpIndex1.addProperty(mpDelete);
        marketPrice.addIndex(mpIndex1);
        /**
         * END NEW UPDATE
         */

        //MS_MENU
        Entity menu = schema.addEntity("Menu").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        menu.setTableName("MS_MENU");

        menu.addStringProperty("uuid_menu").notNull().primaryKey().codeBeforeField("@SerializedName(\"uuid_menu\")");
        menu.addStringProperty("menu_id").codeBeforeField("@SerializedName(\"menu_id\")");
        menu.addStringProperty("flag_job").codeBeforeField("@SerializedName(\"flag_job\")");
        menu.addStringProperty("is_visible").codeBeforeField("@SerializedName(\"is_visible\")");
        menu.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        menu.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        menu.addStringProperty("usr_upd").codeBeforeField("@SerializedName(\"usr_upd\")");
        menu.addDateProperty("dtm_upd").codeBeforeField("@SerializedName(\"dtm_upd\")");
        Property fkMenuUuidUser = menu.addStringProperty("uuid_user")
                .codeBeforeField("@SerializedName(\"uuid_user\")").getProperty();

        //MS_PRINTITEM
        Entity printItem = schema.addEntity("PrintItem").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        printItem.setTableName("MS_PRINTITEM");

        printItem.addStringProperty("uuid_print_item").notNull().primaryKey().codeBeforeField("@SerializedName(\"uuid_print_item\")");
        printItem.addStringProperty("print_type_id").codeBeforeField("@SerializedName(\"print_type_id\")");
        printItem.addStringProperty("print_item_label").codeBeforeField("@SerializedName(\"print_item_label\")");
        printItem.addStringProperty("question_group_id").codeBeforeField("@SerializedName(\"question_group_id\")");
        printItem.addStringProperty("question_id").codeBeforeField("@SerializedName(\"question_id\")");
        printItem.addIntProperty("print_item_order").codeBeforeField("@SerializedName(\"print_item_order\")");
        printItem.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        printItem.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        printItem.addStringProperty("usr_upd").codeBeforeField("@SerializedName(\"usr_upd\")");
        printItem.addDateProperty("dtm_upd").codeBeforeField("@SerializedName(\"dtm_upd\")");
        Property fkPrintItemUuidScheme = printItem.addStringProperty("uuid_scheme")
                .codeBeforeField("@SerializedName(\"uuid_scheme\")").getProperty();


        //MS_QUESTIONSET
        Entity questionSet = schema.addEntity("QuestionSet").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        questionSet.setTableName("MS_QUESTIONSET");

        questionSet.addStringProperty("uuid_question_set").notNull().primaryKey().codeBeforeField("@SerializedName(\"uuid_question_set\")");
        questionSet.addStringProperty("question_group_id").codeBeforeField("@SerializedName(\"question_group_id\")");
        questionSet.addStringProperty("question_group_name").codeBeforeField("@SerializedName(\"question_group_name\")");
        questionSet.addIntProperty("question_group_order").codeBeforeField("@SerializedName(\"question_group_order\")");
        questionSet.addStringProperty("question_id").codeBeforeField("@SerializedName(\"question_id\")");
        questionSet.addStringProperty("question_label").codeBeforeField("@SerializedName(\"question_label\")");
        questionSet.addIntProperty("question_order").codeBeforeField("@SerializedName(\"question_order\")");
        questionSet.addStringProperty("answer_type").codeBeforeField("@SerializedName(\"answer_type\")");
        questionSet.addStringProperty("option_answers").codeBeforeField("@SerializedName(\"option_answers\")");
        questionSet.addStringProperty("choice_filter").codeBeforeField("@SerializedName(\"choice_filter\")");
        questionSet.addStringProperty("is_mandatory").codeBeforeField("@SerializedName(\"is_mandatory\")");
        questionSet.addIntProperty("max_length").codeBeforeField("@SerializedName(\"max_length\")");
        questionSet.addStringProperty("is_visible").codeBeforeField("@SerializedName(\"is_visible\")");
        questionSet.addStringProperty("is_readonly").codeBeforeField("@SerializedName(\"is_readonly\")");
        questionSet.addStringProperty("regex").codeBeforeField("@SerializedName(\"regex\")");
        questionSet.addStringProperty("relevant_question").codeBeforeField("@SerializedName(\"relevant_question\")");
        questionSet.addStringProperty("calculate").codeBeforeField("@SerializedName(\"calculate\")");
        questionSet.addStringProperty("constraint_message").codeBeforeField("@SerializedName(\"constraint_message\")");
        questionSet.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        questionSet.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        questionSet.addStringProperty("usr_upd").codeBeforeField("@SerializedName(\"usr_upd\")");
        questionSet.addDateProperty("dtm_upd").codeBeforeField("@SerializedName(\"dtm_upd\")");
        questionSet.addStringProperty("identifier_name").codeBeforeField("@SerializedName(\"identifier_name\")");
        Property fkQuestionSetUuidScheme = questionSet.addStringProperty("uuid_scheme")
                .codeBeforeField("@SerializedName(\"uuid_scheme\")").getProperty();
        questionSet.addStringProperty("lov_group").codeBeforeField("@SerializedName(\"lov_group\")");
        questionSet.addStringProperty("tag").codeBeforeField("@SerializedName(\"tag\")");
        questionSet.addStringProperty("is_holiday_allowed").codeBeforeField("@SerializedName(\"is_holiday_allowed\")");
        questionSet.addStringProperty("img_quality").codeBeforeField("@SerializedName(\"img_quality\")");
        questionSet.addStringProperty("question_validation").codeBeforeField("@SerializedName(\"question_validation\")");
        questionSet.addStringProperty("question_value").codeBeforeField("@SerializedName(\"question_value\")");
        questionSet.addStringProperty("validate_err_message").codeBeforeField("@SerializedName(\"validate_err_message\")");
        questionSet.addStringProperty("form_version").codeBeforeField("@SerializedName(\"form_version\")");
        questionSet.addStringProperty("relevant_mandatory").codeBeforeField("@SerializedName(\"relevant_mandatory\")");;


        //MS_SCHEME
        Entity scheme = schema.addEntity("Scheme").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        scheme.setTableName("MS_SCHEME");

        scheme.addStringProperty("uuid_scheme").notNull().primaryKey().codeBeforeField("@SerializedName(\"uuid_scheme\")");
        scheme.addStringProperty("scheme_description").codeBeforeField("@SerializedName(\"scheme_description\")");
        scheme.addDateProperty("scheme_last_update").codeBeforeField("@SerializedName(\"scheme_last_update\")");
        scheme.addStringProperty("is_printable").codeBeforeField("@SerializedName(\"is_printable\")");
        scheme.addStringProperty("form_id").codeBeforeField("@SerializedName(\"form_id\")");

        scheme.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        scheme.addStringProperty("is_preview_server").codeBeforeField("@SerializedName(\"is_preview_server\")");
        scheme.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        scheme.addStringProperty("usr_upd").codeBeforeField("@SerializedName(\"usr_upd\")");
        scheme.addDateProperty("dtm_upd").codeBeforeField("@SerializedName(\"dtm_upd\")");
        scheme.addStringProperty("form_type").codeBeforeField("@SerializedName(\"form_type\")");
        scheme.addStringProperty("is_active").codeBeforeField("@SerializedName(\"is_active\")");
        scheme.addStringProperty("form_version").codeBeforeField("@SerializedName(\"form_version\")");


        //MS_TIMELINETYPE
        Entity timelineType = schema.addEntity("TimelineType").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        timelineType.setTableName("MS_TIMELINETYPE");

        timelineType.addStringProperty("uuid_timeline_type").notNull().primaryKey().codeBeforeField("@SerializedName(\"uuid_timeline_type\")");
        timelineType.addStringProperty("timeline_description").codeBeforeField("@SerializedName(\"timeline_description\")");
        timelineType.addStringProperty("timeline_type").codeBeforeField("@SerializedName(\"timeline_type\")");
        timelineType.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        timelineType.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        timelineType.addStringProperty("usr_upd").codeBeforeField("@SerializedName(\"usr_upd\")");
        timelineType.addDateProperty("dtm_upd").codeBeforeField("@SerializedName(\"dtm_upd\")");


        //MS_USER
        Entity user = schema.addEntity("User").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        user.setTableName("MS_USER");

        user.addStringProperty("uuid_user").notNull().primaryKey().codeBeforeField("@SerializedName(\"uuid_user\")");
        user.addStringProperty("flag_job").codeBeforeField("@SerializedName(\"flag_job\")");
        user.addByteArrayProperty("image_profile").codeBeforeField("@ExcludeFromGson \n\t @SerializedName(\"image_profile\")");
        user.addStringProperty("fullname").codeBeforeField("@SerializedName(\"fullname\")");
        user.addStringProperty("branch_id").codeBeforeField("@SerializedName(\"branch_id\")");
        user.addStringProperty("branch_name").codeBeforeField("@SerializedName(\"branch_name\")");
        user.addStringProperty("is_branch").codeBeforeField("@SerializedName(\"is_branch\")");
        user.addStringProperty("password").codeBeforeField("@SerializedName(\"password\")");
        user.addIntProperty("task_seq").codeBeforeField("@SerializedName(\"task_seq\")");
        user.addStringProperty("google_id").codeBeforeField("@SerializedName(\"google_id\")");
        user.addStringProperty("facebook_id").codeBeforeField("@SerializedName(\"facebook_id\")");
        user.addStringProperty("login_id").codeBeforeField("@SerializedName(\"login_id\")");
        user.addIntProperty("fail_count").codeBeforeField("@SerializedName(\"fail_count\")");
        user.addDateProperty("last_sync").codeBeforeField("@SerializedName(\"last_sync\")");
        user.addStringProperty("branch_address").codeBeforeField("@SerializedName(\"branch_address\")");
        user.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        user.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        user.addStringProperty("usr_upd").codeBeforeField("@SerializedName(\"usr_upd\")");
        user.addDateProperty("dtm_upd").codeBeforeField("@SerializedName(\"dtm_upd\")");
        user.addByteArrayProperty("image_cover").codeBeforeField("@ExcludeFromGson \n" +
                "\t @SerializedName(\"image_cover\")");
        user.addStringProperty("chg_pwd").codeBeforeField("@SerializedName(\"chg_pwd\")");
        user.addStringProperty("job_description").codeBeforeField("@SerializedName(\"job_description\")");
        user.addStringProperty("pwd_exp").codeBeforeField("@SerializedName(\"pwd_exp\")");
        user.addStringProperty("dealer_name").codeBeforeField("@SerializedName(\"dealer_name\")");
        user.addStringProperty("cash_limit").codeBeforeField("@SerializedName(\"cash_limit\")");
        user.addStringProperty("cash_on_hand").codeBeforeField("@SerializedName(\"cash_on_hand\")");
        user.addStringProperty("uuid_branch").codeBeforeField("@SerializedName(\"uuid_branch\")");
        user.addStringProperty("uuid_group").codeBeforeField("@SerializedName(\"uuid_group\")");
        user.addStringProperty("uuid_dealer").codeBeforeField("@SerializedName(\"uuid_dealer\")");
        user.addStringProperty("start_time").codeBeforeField("@SerializedName(\"start_time\")");
        user.addStringProperty("end_time").codeBeforeField("@SerializedName(\"end_time\")");
        user.addStringProperty("is_tracking").codeBeforeField("@SerializedName(\"is_tracking\")");
        user.addStringProperty("tracking_days").codeBeforeField("@SerializedName(\"tracking_days\")");
        user.addStringProperty("token_id_fcm").codeBeforeField("@SerializedName(\"token_id_fcm\")");
        user.addStringProperty("is_piloting").codeBeforeField("@SerializedName(\"is_piloting\")");
        user.addStringProperty("pushsync_time").codeBeforeField("@SerializedName(\"pushsync_time\")");
        user.addStringProperty("is_piloting_cae").codeBeforeField("@SerializedName(\"is_piloting_cae\")");
        user.addStringProperty("branch_type").codeBeforeField("@SerializedName(\"branch_type\")");

        Entity pushsync = schema.addEntity("PushSync").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        pushsync.setTableName("TR_PUSHSYNC");

        pushsync.addStringProperty("idPushSync").notNull().primaryKey()
                .codeBeforeField("@SerializedName(\"idPushSync\")");
        pushsync.addStringProperty("isActive").codeBeforeField("@SerializedName(\"isActive\")");
        pushsync.addStringProperty("maxTimestamps").codeBeforeField("@SerializedName(\"maxTimestamps\")");
        pushsync.addStringProperty("tableName").codeBeforeField("@SerializedName(\"tableName\")");
        pushsync.addStringProperty("lovGroup").codeBeforeField("@SerializedName(\"lovGroup\")");
        pushsync.addStringProperty("totalRecord").codeBeforeField("@SerializedName(\"totalRecord\")");
        pushsync.addStringProperty("filename").codeBeforeField("@SerializedName(\"filename\")");
        pushsync.addStringProperty("fileUrl").codeBeforeField("@SerializedName(\"fileUrl\")");

        //TR_APPLICATIONLOG
        Entity logger = schema.addEntity("Logger").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        logger.setTableName("TR_APPLICATION_LOG");

        logger.addStringProperty("uuid_log").notNull().primaryKey().codeBeforeField("@SerializedName(\"uuid_log\")");
        logger.addStringProperty("screen").codeBeforeField("@SerializedName(\"screen\")");
        logger.addDateProperty("timestamp").codeBeforeField("@SerializedName(\"timestamp\")");
        logger.addStringProperty("detail").codeBeforeField("@SerializedName(\"detail\")");
        Property fkLoggerUuidUser = logger.addStringProperty("uuid_user")
                .codeBeforeField("@SerializedName(\"uuid_user\")").getProperty();


        //TR_COLLECTIONHISTORY
        Entity collectionHistory = schema.addEntity("CollectionHistory").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        collectionHistory.setTableName("TR_COLLECTIONHISTORY");

        collectionHistory.addStringProperty("uuid_collection_history").notNull().primaryKey()
                .codeBeforeField("@SerializedName(\"uuid_collection_history\")");
        collectionHistory.addDateProperty("last_update").codeBeforeField("@SerializedName(\"last_update\")");
        collectionHistory.addStringProperty("description").codeBeforeField("@SerializedName(\"description\")");
        collectionHistory.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        collectionHistory.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        Property fkCollectionHistoryUuidUser = collectionHistory.addStringProperty("uuid_user")
                .codeBeforeField("@SerializedName(\"uuid_user\")").getProperty();


        //TR_COMMENT
        Entity comment = schema.addEntity("Comment").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        comment.setTableName("TR_COMMENT");

        comment.addStringProperty("uuid_comment").notNull().primaryKey()
                .codeBeforeField("@SerializedName(\"uuid_comment\")");
        comment.addStringProperty("comment").codeBeforeField("@SerializedName(\"comment\")");
        comment.addDateProperty("dtm_crt_server").codeBeforeField("@SerializedName(\"dtm_crt_server\")");
        comment.addStringProperty("sender_id").codeBeforeField("@SerializedName(\"sender_id\")");
        comment.addStringProperty("sender_name").codeBeforeField("@SerializedName(\"sender_name\")");
        comment.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        comment.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        comment.addStringProperty("usr_upd").codeBeforeField("@SerializedName(\"usr_upd\")");
        comment.addDateProperty("dtm_upd").codeBeforeField("@SerializedName(\"dtm_upd\")");
        Property fkCommentUuidTimeline = comment.addStringProperty("uuid_timeline")
                .codeBeforeField("@SerializedName(\"uuid_timeline\")").getProperty();


        //TR_DEPOSITREPORT_D
        Entity depositReportD = schema.addEntity("DepositReportD").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        depositReportD.setTableName("TR_DEPOSITREPORT_D");

        depositReportD.addStringProperty("uuid_deposit_report_d").notNull().primaryKey()
                .codeBeforeField("@SerializedName(\"uuid_deposit_report_d\")");
        depositReportD.addStringProperty("uuid_task_h").codeBeforeField("@SerializedName(\"uuid_task_h\")");
        depositReportD.addStringProperty("deposit_amt").codeBeforeField("@SerializedName(\"deposit_amt\")");
        depositReportD.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        depositReportD.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        Property fkDepositReportDUuidDepositReportH = depositReportD.addStringProperty("uuid_deposit_report_h")
                .codeBeforeField("@SerializedName(\"uuid_deposit_report_h\")").getProperty();
        depositReportD.addStringProperty("is_sent").codeBeforeField("@SerializedName(\"is_sent\")");

        //TR_DEPOSITREPORT_H
        Entity depositReportH = schema.addEntity("DepositReportH").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        depositReportH.setTableName("TR_DEPOSITREPORT_H");

        depositReportH.addStringProperty("uuid_deposit_report_h").notNull().primaryKey()
                .codeBeforeField("@SerializedName(\"uuid_deposit_report_h\")");
        depositReportH.addDateProperty("last_update").codeBeforeField("@SerializedName(\"last_update\")");
        depositReportH.addStringProperty("batch_id").codeBeforeField("@SerializedName(\"batch_id\")");
        depositReportH.addStringProperty("bank_account").codeBeforeField("@SerializedName(\"bank_account\")");
        depositReportH.addStringProperty("bank_name").codeBeforeField("@SerializedName(\"bank_name\")");
        depositReportH.addStringProperty("cashier_name").codeBeforeField("@SerializedName(\"cashier_name\")");
        depositReportH.addDateProperty("transfered_date").codeBeforeField("@SerializedName(\"transfered_date\")");
        depositReportH.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        depositReportH.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        depositReportH.addByteArrayProperty("image").codeBeforeField("@SerializedName(\"image\")");
        Property fkDepositReportHUuidUser = depositReportH.addStringProperty("uuid_user")
                .codeBeforeField("@SerializedName(\"uuid_user\")").getProperty();


        //TR_IMAGERESULT
        Entity imageResult = schema.addEntity("ImageResult").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        imageResult.setTableName("TR_IMAGERESULT");

        imageResult.addStringProperty("uuid_image_result").notNull().primaryKey()
                .codeBeforeField("@SerializedName(\"uuid_image_result\")");
        imageResult.addStringProperty("question_id").codeBeforeField("@SerializedName(\"question_id\")");
        imageResult.addStringProperty("submit_duration").codeBeforeField("@SerializedName(\"submit_duration\")");
        imageResult.addStringProperty("submit_size").codeBeforeField("@SerializedName(\"submit_size\")");
        imageResult.addIntProperty("total_image").codeBeforeField("@SerializedName(\"total_image\")");
        imageResult.addIntProperty("count_image").codeBeforeField("@SerializedName(\"count_image\")");
        imageResult.addDateProperty("submit_date").codeBeforeField("@SerializedName(\"submit_date\")");
        imageResult.addStringProperty("submit_result").codeBeforeField("@SerializedName(\"submit_result\")");
        imageResult.addStringProperty("question_group_id").codeBeforeField("@SerializedName(\"question_group_id\")");
        imageResult.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        imageResult.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        Property fkImageResultUuidTaskH = imageResult.addStringProperty("uuid_task_h")
                .codeBeforeField("@SerializedName(\"uuid_task_h\")").getProperty();

        //TR_LOCATIONTRACKING
        Entity locationTracking = schema.addEntity("LocationInfo").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        locationTracking.setTableName("TR_LOCATION");

        locationTracking.addStringProperty("uuid_location_info").notNull().primaryKey()
                .codeBeforeField("@SerializedName(\"uuid_location_info\")");
        locationTracking.addStringProperty("latitude").codeBeforeField("@SerializedName(\"latitude\")");
        locationTracking.addStringProperty("longitude").codeBeforeField("@SerializedName(\"longitude\")");
        locationTracking.addStringProperty("mcc").codeBeforeField("@SerializedName(\"mcc\")");
        locationTracking.addStringProperty("mnc").codeBeforeField("@SerializedName(\"mnc\")");
        locationTracking.addStringProperty("lac").codeBeforeField("@SerializedName(\"lac\")");
        locationTracking.addStringProperty("cid").codeBeforeField("@SerializedName(\"cid\")");
        locationTracking.addDateProperty("handset_time").codeBeforeField("@SerializedName(\"handset_time\")");
        locationTracking.addStringProperty("mode").codeBeforeField("@SerializedName(\"mode\")");
        locationTracking.addIntProperty("accuracy").codeBeforeField("@SerializedName(\"accuracy\")");
        locationTracking.addDateProperty("gps_time").codeBeforeField("@SerializedName(\"gps_time\")");
        locationTracking.addStringProperty("is_gps_time").codeBeforeField("@SerializedName(\"is_gps_time\")");
        locationTracking.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        locationTracking.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        locationTracking.addStringProperty("location_type").codeBeforeField("@SerializedName(\"location_type\")");
        Property fkLocationTrackingUuidUser = locationTracking.addStringProperty("uuid_user")
                .codeBeforeField("@SerializedName(\"uuid_user\")").getProperty();


        //TR_MESSAGE
        Entity message = schema.addEntity("Message").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        message.setTableName("TR_MESSAGE");

        message.addStringProperty("uuid_message").notNull().primaryKey()
                .codeBeforeField("@SerializedName(\"uuid_message\")");
        message.addStringProperty("message").codeBeforeField("@SerializedName(\"message\")");
        message.addStringProperty("sender_id").codeBeforeField("@SerializedName(\"sender_id\")");
        message.addStringProperty("sender_name").codeBeforeField("@SerializedName(\"sender_name\")");
        message.addDateProperty("dtm_crt_server").codeBeforeField("@SerializedName(\"dtm_crt_server\")");
        message.addDateProperty("time_read").codeBeforeField("@SerializedName(\"time_read\")");
        message.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        message.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        Property fkMessageUuidUser = message.addStringProperty("uuid_user")
                .codeBeforeField("@SerializedName(\"uuid_user\")").getProperty();


        //TR_PAYMENTHISTORY_D
        Entity paymentHistoryD = schema.addEntity("PaymentHistoryD").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        paymentHistoryD.setTableName("TR_PAYMENTHISTORY_D");

        paymentHistoryD.addStringProperty("uuid_payment_history_d").notNull().primaryKey()
                .codeBeforeField("@SerializedName(\"uuid_payment_history_d\")");
        paymentHistoryD.addStringProperty("uuid_task_h").codeBeforeField("@SerializedName(\"uuid_task_h\")");

        paymentHistoryD.addStringProperty("transaction_type").codeBeforeField("@SerializedName(\"transactiontype\")");
        paymentHistoryD.addStringProperty("receipt_no").codeBeforeField("@SerializedName(\"receipt_no\")");
        paymentHistoryD.addDateProperty("value_date").codeBeforeField("@SerializedName(\"value_date\")");
        paymentHistoryD.addDateProperty("posting_date").codeBeforeField("@SerializedName(\"posting_date\")");
        paymentHistoryD.addStringProperty("payment_amount").codeBeforeField("@SerializedName(\"payment_amount\")");
        paymentHistoryD.addStringProperty("installment_amount").codeBeforeField("@SerializedName(\"installment_amount\")");
        paymentHistoryD.addStringProperty("installment_number").codeBeforeField("@SerializedName(\"installment_number\")");
        paymentHistoryD.addStringProperty("wop_code").codeBeforeField("@SerializedName(\"wop_code\")");

        paymentHistoryD.addStringProperty("payment_allocation_name").codeBeforeField("@SerializedName(\"payment_allocation_name\")");
        paymentHistoryD.addStringProperty("os_amount_od").codeBeforeField("@SerializedName(\"os_amount_od\")");
        paymentHistoryD.addStringProperty("receive_amount").codeBeforeField("@SerializedName(\"receive_amount\")");
        paymentHistoryD.addDateProperty("dtm_upd").codeBeforeField("@SerializedName(\"dtm_upd\")");
        paymentHistoryD.addStringProperty("usr_upd").codeBeforeField("@SerializedName(\"usr_upd\")");
        paymentHistoryD.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        paymentHistoryD.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        paymentHistoryD.addStringProperty("uuid_payment_history_h").codeBeforeField("@SerializedName(\"uuid_payment_history_h\")");

        //TR_PAYMENTHISTORY_H
        Entity paymentHistoryH = schema.addEntity("PaymentHistoryH").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        paymentHistoryH.setTableName("TR_PAYMENTHISTORY_H");

        paymentHistoryH.addStringProperty("uuid_payment_history_h").notNull().primaryKey()
                .codeBeforeField("@SerializedName(\"uuid_payment_history_h\")");
        paymentHistoryH.addStringProperty("uuid_task_h").codeBeforeField("@SerializedName(\"uuid_task_h\")");
        paymentHistoryH.addStringProperty("agreement_no").codeBeforeField("@SerializedName(\"agreement_no\")");
        paymentHistoryH.addStringProperty("branch_code").codeBeforeField("@SerializedName(\"branch_code\")");
        paymentHistoryH.addDateProperty("value_date").codeBeforeField("@SerializedName(\"value_date\")");
        paymentHistoryH.addStringProperty("payment_amount").codeBeforeField("@SerializedName(\"payment_amount\")");
        paymentHistoryH.addStringProperty("installment_amount").codeBeforeField("@SerializedName(\"installment_amount\")");
        paymentHistoryH.addStringProperty("installment_number").codeBeforeField("@SerializedName(\"installment_number\")");
        paymentHistoryH.addStringProperty("transaction_type").codeBeforeField("@SerializedName(\"transaction_type\")");
        paymentHistoryH.addStringProperty("wop_code").codeBeforeField("@SerializedName(\"wop_code\")");
        paymentHistoryH.addStringProperty("receipt_no").codeBeforeField("@SerializedName(\"receipt_no\")");

        paymentHistoryH.addDateProperty("posting_date").codeBeforeField("@SerializedName(\"posting_date\")");
        paymentHistoryH.addDateProperty("dtm_upd").codeBeforeField("@SerializedName(\"dtm_upd\")");
        paymentHistoryH.addStringProperty("usr_upd").codeBeforeField("@SerializedName(\"usr_upd\")");
        paymentHistoryH.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        paymentHistoryH.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");


        //TR_PRINTRESULT
        Entity printResult = schema.addEntity("PrintResult").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        printResult.setTableName("TR_PRINTRESULT");

        printResult.addStringProperty("uuid_print_result").notNull().primaryKey()
                .codeBeforeField("@SerializedName(\"uuid_print_result\")");
        printResult.addDateProperty("dtm_crt_server").codeBeforeField("@SerializedName(\"dtm_crt_server\")");
        printResult.addStringProperty("label").codeBeforeField("@SerializedName(\"label\")");
        printResult.addStringProperty("value").codeBeforeField("@SerializedName(\"value\")");
        printResult.addStringProperty("print_type_id").codeBeforeField("@SerializedName(\"print_type_id\")");
        printResult.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        printResult.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        Property fkPrintResultUser = printResult.addStringProperty("uuid_task_h")
                .codeBeforeField("@SerializedName(\"uuid_task_h\")").getProperty();


        // TR_RECEIPTVOUCHER
        Entity receiptVoucher = schema.addEntity("ReceiptVoucher").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        receiptVoucher.setTableName("TR_RECEIPTVOUCHER");

        receiptVoucher.addStringProperty("uuid_receipt_voucher").notNull().primaryKey()
                .codeBeforeField("@SerializedName(\"uuid_rv_number\")");
        receiptVoucher.addStringProperty("rv_status").codeBeforeField("@SerializedName(\"status_rv\")");
        receiptVoucher.addStringProperty("rv_number").codeBeforeField("@SerializedName(\"rv_number\")");
        receiptVoucher.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        receiptVoucher.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        receiptVoucher.addDateProperty("dtm_use").codeBeforeField("@SerializedName(\"dtm_use\")");
        Property fkReceiptVoucherUuidUser = receiptVoucher.addStringProperty("uuid_user")
                .codeBeforeField("@SerializedName(\"uuid_user\")").getProperty();

        Property fkReceiptVoucherUuidTaskH = receiptVoucher.addStringProperty("uuid_task_h")
                .codeBeforeField("@SerializedName(\"uuid_task_h\")").getProperty();


        // TR_TASK_D
        Entity taskD = schema.addEntity("TaskD").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        taskD.setTableName("TR_TASK_D");

        taskD.addStringProperty("uuid_task_d").notNull().primaryKey().codeBeforeField("@SerializedName(\"uuid_task_d\")");
        taskD.addStringProperty("question_group_id").codeBeforeField("@SerializedName(\"question_group_id\")");
        taskD.addStringProperty("question_id").codeBeforeField("@SerializedName(\"question_id\")");
        taskD.addStringProperty("option_answer_id").codeBeforeField("@SerializedName(\"option_answer_id\")");
        taskD.addStringProperty("text_answer").codeBeforeField("@SerializedName(\"text_answer\")");
        taskD.addByteArrayProperty("image").codeBeforeField("@SerializedName(\"image\")");
        taskD.addStringProperty("is_final").codeBeforeField("@SerializedName(\"is_final\")");
        taskD.addStringProperty("is_sent").codeBeforeField("@SerializedName(\"is_sent\")");
        taskD.addStringProperty("lov").codeBeforeField("@SerializedName(\"lov\")");
        taskD.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        taskD.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        Property fkTaskDUuidTaskH = taskD.addStringProperty("uuid_task_h")
                .codeBeforeField("@SerializedName(\"uuid_task_h\")").getProperty();
        //21 jan 2015 add new field
        taskD.addStringProperty("question_label").codeBeforeField("@SerializedName(\"question_label\")");
        taskD.addStringProperty("latitude").codeBeforeField("@SerializedName(\"latitude\")");
        taskD.addStringProperty("longitude").codeBeforeField("@SerializedName(\"longitude\")");
        taskD.addStringProperty("mcc").codeBeforeField("@SerializedName(\"mcc\")");
        taskD.addStringProperty("mnc").codeBeforeField("@SerializedName(\"mnc\")");
        taskD.addStringProperty("lac").codeBeforeField("@SerializedName(\"lac\")");
        taskD.addStringProperty("cid").codeBeforeField("@SerializedName(\"cid\")");
        taskD.addDateProperty("gps_time").codeBeforeField("@SerializedName(\"gps_time\")");
        taskD.addIntProperty("accuracy").codeBeforeField("@SerializedName(\"accuracy\")");
        taskD.addStringProperty("regex").codeBeforeField("@SerializedName(\"regex\")");
        taskD.addStringProperty("is_readonly").codeBeforeField("@SerializedName(\"is_readonly\")");
        taskD.addByteArrayProperty("location_image").codeBeforeField("@ExcludeFromGson \n\t @SerializedName(\"location_image\")");
        taskD.addStringProperty("is_visible").codeBeforeField("@SerializedName(\"is_visible\")");
        Property fkTaskDUuidLookup =taskD.addStringProperty("uuid_lookup").codeBeforeField("@SerializedName(\"uuid_lookup\")").getProperty();
        taskD.addStringProperty("tag").codeBeforeField("@SerializedName(\"tag\")");
        taskD.addStringProperty("count").codeBeforeField("@SerializedName(\"count\")");
        taskD.addStringProperty("has_default_image").codeBeforeField("@SerializedName(\"has_default_image\")");
        taskD.addStringProperty("is_resurvey").codeBeforeField("@SerializedName(\"is_resurvey\")");
        taskD.addStringProperty("uuid_question_mapping").codeBeforeField("@SerializedName(\"uuid_question_mapping\")");
        taskD.addStringProperty("is_readonly_mapping").codeBeforeField("@SerializedName(\"is_readonly_mapping\")");

        // TR_TASK_H
        Entity taskH = schema.addEntity("TaskH").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        taskH.setTableName("TR_TASK_H");

        taskH.addStringProperty("uuid_task_h").notNull().primaryKey().codeBeforeField("@SerializedName(\"uuid_task_h\")");
        taskH.addStringProperty("task_id").codeBeforeField("@SerializedName(\"task_id\")");
        taskH.addStringProperty("status").codeBeforeField("@SerializedName(\"status\")");
        taskH.addStringProperty("is_printable").codeBeforeField("@SerializedName(\"is_printable\")");
        taskH.addStringProperty("customer_name").codeBeforeField("@SerializedName(\"customer_name\")");
        taskH.addStringProperty("customer_phone").codeBeforeField("@SerializedName(\"customer_phone\")");
        taskH.addStringProperty("customer_address").codeBeforeField("@SerializedName(\"customer_address\")");
        taskH.addStringProperty("notes").codeBeforeField("@SerializedName(\"notes\")");
        taskH.addDateProperty("submit_date").codeBeforeField("@SerializedName(\"submit_date\")");
        taskH.addStringProperty("submit_duration").codeBeforeField("@SerializedName(\"submit_duration\")");
        taskH.addStringProperty("submit_size").codeBeforeField("@SerializedName(\"submit_size\")");
        taskH.addStringProperty("submit_result").codeBeforeField("@SerializedName(\"submit_result\")");
        taskH.addDateProperty("assignment_date").codeBeforeField("@SerializedName(\"assignment_date\")");
        taskH.addIntProperty("print_count").codeBeforeField("@SerializedName(\"print_count\")");
        taskH.addDateProperty("draft_date").codeBeforeField("@SerializedName(\"draft_date\")");
        taskH.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        taskH.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        taskH.addStringProperty("priority").codeBeforeField("@SerializedName(\"priority\")");
        taskH.addStringProperty("latitude").codeBeforeField("@SerializedName(\"latitude\")");
        taskH.addStringProperty("longitude").codeBeforeField("@SerializedName(\"longitude\")");
        taskH.addDateProperty("scheme_last_update").codeBeforeField("@SerializedName(\"scheme_last_update\")");
        taskH.addStringProperty("is_verification").codeBeforeField("@SerializedName(\"is_verification\")");
        taskH.addStringProperty("is_preview_server").codeBeforeField("@SerializedName(\"is_preview_server\")");
        Property fkTaskHUuidUser = taskH.addStringProperty("uuid_user")
                .codeBeforeField("@SerializedName(\"uuid_user\")").getProperty();
        taskH.addByteArrayProperty("voice_note").codeBeforeField("@SerializedName(\"voice_note\")");
        Property fkTaskHUuidScheme = taskH.addStringProperty("uuid_scheme")
                .codeBeforeField("@SerializedName(\"uuid_scheme\")").getProperty();
        taskH.addStringProperty("zip_code").codeBeforeField("@SerializedName(\"zip_code\")");
        taskH.addDateProperty("start_date").codeBeforeField("@SerializedName(\"start_date\")");
        taskH.addDateProperty("open_date").codeBeforeField("@SerializedName(\"open_date\")");
        taskH.addStringProperty("appl_no").codeBeforeField("@SerializedName(\"appl_no\")");
        taskH.addStringProperty("is_prepocessed").codeBeforeField("@ExcludeFromGson \n\t @SerializedName(\"is_prepocessed\")");
        taskH.addIntProperty("last_saved_question").codeBeforeField("@SerializedName(\"last_saved_question\")");
        taskH.addStringProperty("is_reconciled").codeBeforeField("@SerializedName(\"is_reconciled\")");
        taskH.addDateProperty("pts_date").codeBeforeField("@SerializedName(\"pts_date\")");
        taskH.addStringProperty("access_mode").codeBeforeField("@ExcludeFromGson \n\t @SerializedName(\"access_mode\")");
        taskH.addStringProperty("rv_number").codeBeforeField("@SerializedName(\"rv_number\")");
        taskH.addStringProperty("status_rv").codeBeforeField("@SerializedName(\"status_rv\")");
        taskH.addStringProperty("flag").codeBeforeField("@SerializedName(\"flag\")");
        taskH.addDateProperty("pms_date").codeBeforeField("@SerializedName(\"pms_date\")");
        taskH.addStringProperty("is_sent_pts").codeBeforeField("@SerializedName(\"is_sent_pts\")");
        taskH.addStringProperty("form_version").codeBeforeField("@SerializedName(\"form_version\")");
        taskH.addStringProperty("flag_survey").codeBeforeField("@SerializedName(\"flag_survey\")");
        taskH.addStringProperty("uuid_resurvey_user").codeBeforeField("@SerializedName(\"uuid_resurvey_user\")");
        taskH.addStringProperty("resurvey_suggested").codeBeforeField("@SerializedName(\"resurvey_suggested\")");
        taskH.addStringProperty("verification_notes").codeBeforeField("@SerializedName(\"verification_notes\")");
        taskH.addStringProperty("od").codeBeforeField("@SerializedName(\"od\")");
        taskH.addStringProperty("amt_due").codeBeforeField("@SerializedName(\"amt_due\")");
        taskH.addStringProperty("inst_no").codeBeforeField("@SerializedName(\"inst_no\")");
        taskH.addStringProperty("uuid_task_update").codeBeforeField("@SerializedName(\"uuid_task_update\")");
        taskH.addStringProperty("pending_notes").codeBeforeField("@SerializedName(\"pending_notes\")");
        taskH.addStringProperty("docupro_feedback").codeBeforeField("@SerializedName(\"docupro_feedback\")");
        taskH.addStringProperty("status_application").codeBeforeField("@SerializedName(\"status_application\")");
        taskH.addIntProperty("is_sent_confins").codeBeforeField("@SerializedName(\"is_sent_confins\")");
        taskH.addIntProperty("is_already_notified").codeBeforeField("@SerializedName(\"is_already_notified\")");
        taskH.addStringProperty("kelurahan").codeBeforeField("@SerializedName(\"kelurahan\")");
        taskH.addStringProperty("status_followup").codeBeforeField("@SerializedName(\"status_followup\")");
        taskH.addStringProperty("is_revisit").codeBeforeField("@SerializedName(\"is_revisit\")");
        taskH.addStringProperty("visit_type").codeBeforeField("@SerializedName(\"visit_type\")");
        taskH.addStringProperty("send_task_promise_to_survey").codeBeforeField("@SerializedName(\"sendTaskPromiseToSurvey\")");
        taskH.addStringProperty("send_task_presurvey").codeBeforeField("@SerializedName(\"sendTaskPreSurvey\")");
        taskH.addStringProperty("send_task_survey").codeBeforeField("@SerializedName(\"sendTaskSurvey\")");
        taskH.addStringProperty("is_piloting_cae").codeBeforeField("@SerializedName(\"is_piloting_cae\")");
        taskH.addStringProperty("category").codeBeforeField("@SerializedName(\"category\")");
        taskH.addStringProperty("sub_category").codeBeforeField("@SerializedName(\"sub_category\")");
        taskH.addStringProperty("reason_detail").codeBeforeField("@SerializedName(\"reason_detail\")");
        taskH.addStringProperty("validasi").codeBeforeField("@SerializedName(\"validasi\")");
        taskH.addStringProperty("notes_crm").codeBeforeField("@SerializedName(\"notes_crm\")");
        taskH.addIntProperty("is_pre_approval").codeBeforeField("@SerializedName(\"is_pre_approval\")");
        taskH.addStringProperty("source_data").codeBeforeField("@SerializedName(\"source_data\")");
        taskH.addStringProperty("is_already_download_task").codeBeforeField("@SerializedName(\"is_already_download_task\")");
        taskH.addStringProperty("product_name").codeBeforeField("@SerializedName(\"productName\")");
        taskH.addStringProperty("jenis_asset").codeBeforeField("@SerializedName(\"jenisAsset\")");

        // TR_TASK_H_SEQUENCE
        Entity taskHSequence = schema.addEntity("TaskHSequence").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        taskHSequence.setTableName("TR_TASK_H_SEQUENCE");

        taskHSequence.addIntProperty("sequence").codeBeforeField("@SerializedName(\"sequence\")").notNull();
        Property fkUuidTaskHSeq = taskHSequence.addStringProperty("uuid_task_h")
                .codeBeforeField("@SerializedName(\"uuid_task_h\")").getProperty();

        // TR_TASK_UPDATE
        Entity taskUpdate = schema.addEntity("TaskUpdate").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        taskUpdate.setTableName("TR_TASK_UPDATE");

        taskUpdate.addStringProperty("uuid_task_update").notNull().primaryKey().codeBeforeField("@SerializedName(\"uuid_task_update\")");
        taskUpdate.addStringProperty("customer_name").codeBeforeField("@SerializedName(\"customer_name\")");
        taskUpdate.addStringProperty("customer_phone").codeBeforeField("@SerializedName(\"customer_phone\")");
        taskUpdate.addStringProperty("customer_address").codeBeforeField("@SerializedName(\"customer_address\")");
        taskUpdate.addStringProperty("notes").codeBeforeField("@SerializedName(\"notes\")");
        taskUpdate.addStringProperty("assignment_date").codeBeforeField("@SerializedName(\"assignment_date\")");
        taskUpdate.addStringProperty("appl_no").codeBeforeField("@SerializedName(\"appl_no\")");
        taskUpdate.addStringProperty("form_name").codeBeforeField("@SerializedName(\"form_name\")");
        taskUpdate.addStringProperty("pending_notes").codeBeforeField("@SerializedName(\"pending_notes\")");
        taskUpdate.addStringProperty("docupro_feedback").codeBeforeField("@SerializedName(\"docupro_feedback\")");
        taskUpdate.addStringProperty("uuid_scheme").codeBeforeField("@SerializedName(\"uuid_scheme\")");
        taskUpdate.addStringProperty("uuid_user").codeBeforeField("@SerializedName(\"uuid_user\")");
        taskUpdate.addIntProperty("is_notified").codeBeforeField("@SerializedName(\"is_notified\")");
        taskUpdate.addStringProperty("category").codeBeforeField("@SerializedName(\"category\")");
        taskUpdate.addStringProperty("sub_category").codeBeforeField("@SerializedName(\"sub_category\")");
        taskUpdate.addStringProperty("reason_detail").codeBeforeField("@SerializedName(\"reason_detail\")");
        taskUpdate.addStringProperty("validasi").codeBeforeField("@SerializedName(\"validasi\")");
        taskUpdate.addIntProperty("is_pre_approval").codeBeforeField("@SerializedName(\"isPreApproval\")");

        // TR_TIMELINE
        Entity timeline = schema.addEntity("Timeline").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        timeline.setTableName("TR_TIMELINE");

        timeline.addStringProperty("uuid_timeline").notNull().primaryKey().codeBeforeField("@SerializedName(\"uuid_timeline\")");
        timeline.addStringProperty("description").codeBeforeField("@SerializedName(\"description\")");
        timeline.addStringProperty("latitude").codeBeforeField("@SerializedName(\"latitude\")");
        timeline.addStringProperty("longitude").codeBeforeField("@SerializedName(\"longitude\")");
        timeline.addDateProperty("dtm_crt_server").codeBeforeField("@SerializedName(\"dtm_crt_server\")");

        timeline.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        timeline.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        Property fkTimelineUuidTask = timeline.addStringProperty("uuid_task_h")
                .codeBeforeField("@SerializedName(\"uuid_task_h\")").getProperty();
        Property fkTimelineUuidUser = timeline.addStringProperty("uuid_user")
                .codeBeforeField("@SerializedName(\"uuid_user\")").getProperty();
        Property fkTimelineUuidTimelineType = timeline.addStringProperty("uuid_timeline_type")
                .codeBeforeField("@SerializedName(\"uuid_timeline_type\")").getProperty();
        Property fkTimelineUuidMessage = timeline.addStringProperty("uuid_message")
                .codeBeforeField("@SerializedName(\"uuid_message\")").getProperty();
        timeline.addByteArrayProperty("byte_image").codeBeforeField("@SerializedName(\"byte_image\")");

        // TR_MOBILECONTENT_D
        Entity mobileContentD = schema.addEntity("MobileContentD").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        mobileContentD.setTableName("TR_MOBILECONTENT_D");

        mobileContentD.addStringProperty("uuid_mobile_content_d").notNull().primaryKey()
                .codeBeforeField("@SerializedName(\"uuid_mobile_content_d\")");
        mobileContentD.addStringProperty("menu_id").codeBeforeField("@SerializedName(\"menu_id\")");
        mobileContentD.addByteArrayProperty("content").codeBeforeField("@SerializedName(\"content\")");
        mobileContentD.addStringProperty("content_type").codeBeforeField("@SerializedName(\"content_type\")");
        mobileContentD.addIntProperty("sequence").codeBeforeField("@SerializedName(\"sequence\")");
        mobileContentD.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        mobileContentD.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        mobileContentD.addStringProperty("usr_upd").codeBeforeField("@SerializedName(\"usr_upd\")");
        Property fkMobileContentDUuidMobileContentH = mobileContentD.addStringProperty("uuid_mobile_content_h")
                .codeBeforeField("@SerializedName(\"uuid_mobile_content_h\")").getProperty();
        mobileContentD.addDateProperty("start_date").codeBeforeField("@SerializedName(\"start_date\")");
        mobileContentD.addDateProperty("end_date").codeBeforeField("@SerializedName(\"end_date\")");

        // TR_MOBILECONTENT_H
        Entity mobileContentH = schema.addEntity("MobileContentH").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        mobileContentH.setTableName("TR_MOBILECONTENT_H");

        mobileContentH.addStringProperty("uuid_mobile_content_h").notNull().primaryKey()
                .codeBeforeField("@SerializedName(\"uuid_mobile_content_h\")");
        mobileContentH.addStringProperty("content_name").codeBeforeField("@SerializedName(\"content_name\")");
        mobileContentH.addDateProperty("last_update").codeBeforeField("@SerializedName(\"last_update\")");
        mobileContentH.addStringProperty("content_description").codeBeforeField("@SerializedName(\"content_description\")");
        mobileContentH.addStringProperty("usr_crt").codeBeforeField("@SerializedName(\"usr_crt\")");
        mobileContentH.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        mobileContentH.addStringProperty("usr_upd").codeBeforeField("@SerializedName(\"usr_upd\")");
        mobileContentH.addDateProperty("dtm_upd").codeBeforeField("@SerializedName(\"dtm_upd\")");
        Property fkMobileContentHUuidUser = mobileContentH.addStringProperty("uuid_user")
                .codeBeforeField("@SerializedName(\"uuid_user\")").getProperty();
        Property fkMobileContentHUuidMobileContentH = mobileContentH.addStringProperty("uuid_parent_content")
                .codeBeforeField("@SerializedName(\"uuid_parent_content\")").getProperty();
        mobileContentH.addDateProperty("start_date").codeBeforeField("@SerializedName(\"start_date\")");
        mobileContentH.addDateProperty("end_date").codeBeforeField("@SerializedName(\"end_date\")");


        // MS_HOLIDAY
        Entity holiday = schema.addEntity("Holiday").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        holiday.setTableName("MS_HOLIDAY");

        holiday.addStringProperty("uuid_holiday").notNull().primaryKey()
                .codeBeforeField("@SerializedName(\"uuid_holiday\")");
        holiday.addDateProperty("h_date").codeBeforeField("@SerializedName(\"h_date\")");
        holiday.addStringProperty("h_desc").codeBeforeField("@SerializedName(\"h_desc\")");
        holiday.addStringProperty("flag_holiday").codeBeforeField("@SerializedName(\"flag_holiday\")");
//					holiday.addStringProperty("usr_crt");
//					holiday.addDateProperty("dtm_crt");
//					holiday.addStringProperty("usr_upd");
        holiday.addDateProperty("dtm_upd").codeBeforeField("@SerializedName(\"dtm_upd\")");
        holiday.addStringProperty("flag_day").codeBeforeField("@SerializedName(\"flag_day\")");
        holiday.addStringProperty("branch").codeBeforeField("@SerializedName(\"branch\")");

        // TR_PRINTDATE
        Entity submitPrint = schema.addEntity("PrintDate").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        submitPrint.setTableName("TR_PRINTDATE");

        submitPrint.addDateProperty("dtm_print").notNull().primaryKey()
                .codeBeforeField("@SerializedName(\"dtm_print\")");
        submitPrint.addStringProperty("uuid_task_h").codeBeforeField("@SerializedName(\"uuid_task_h\")");

        // TR_ERROR_LOG
        Entity errorLog = schema.addEntity("ErrorLog").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        errorLog.setTableName("TR_ERROR_LOG");

        errorLog.addStringProperty("uuid_error_log").notNull().primaryKey().codeBeforeField("@SerializedName(\"uuid_error_log\")");
        errorLog.addStringProperty("error_description").codeBeforeField("@SerializedName(\"description\")");
        errorLog.addStringProperty("device_name").codeBeforeField("@SerializedName(\"device_name\")");
        errorLog.addDateProperty("dtm_activity").codeBeforeField("@SerializedName(\"dtm_activity\")");
        Property fkErrorLogUuidUser = errorLog.addStringProperty("uuid_user")
                .codeBeforeField("@SerializedName(\"uuid_user\")").getProperty();
        Property fkErrorLogTaskId = errorLog.addStringProperty("task_id")
                .codeBeforeField("@SerializedName(\"task_id\")").getProperty();

        //MS_MOBILEDATAFILES
        Entity mobileDataFiles = schema.addEntity("mobiledatafile").addImport("com.google.gson.annotations.Since");
        mobileDataFiles.setTableName("MS_MOBILEDATAFILES");
        mobileDataFiles.addLongProperty("id_datafile").notNull().primaryKey();
        mobileDataFiles.addStringProperty("is_active");
        mobileDataFiles.addDateProperty("max_timestamp");
        mobileDataFiles.addStringProperty("file_url");
        mobileDataFiles.addStringProperty("alternate_file_url");
        mobileDataFiles.addStringProperty("hash_sha1");
        mobileDataFiles.addStringProperty("usr_crt");
        mobileDataFiles.addDateProperty("dtm_crt");
        mobileDataFiles.addStringProperty("usr_upd");
        mobileDataFiles.addDateProperty("dtm_upd");
        mobileDataFiles.addStringProperty("downloaded_file_path");
        mobileDataFiles.addBooleanProperty("import_flag");

        // TR_REMINDER_PO
        Entity reminderPo = schema.addEntity("ReminderPo").addImport("com.adins.mss.base.util.ExcludeFromGson").addImport("com.google.gson.annotations.SerializedName");
        reminderPo.setTableName("TR_REMINDER_PO");

        reminderPo.addStringProperty("id").notNull().primaryKey().codeBeforeField("@SerializedName(\"id\")");
        reminderPo.addDateProperty("dtm_crt").codeBeforeField("@SerializedName(\"dtm_crt\")");
        Property fkReminderPoUuidTaskH = reminderPo.addStringProperty("uuid_task_h").codeBeforeField("@SerializedName(\"uuid_task_h\")").getProperty();
        reminderPo.addStringProperty("is_task_downloaded").codeBeforeField("@SerializedName(\"is_task_downloaded\")");
        reminderPo.addDateProperty("expired_date").codeBeforeField("@SerializedName(\"expired_date\")");
        reminderPo.addStringProperty("status_po").codeBeforeField("@SerializedName(\"status_po\")");
        reminderPo.addStringProperty("uuid_user").codeBeforeField("@SerializedName(\"uuid_user\")");

        // EMBEDDED_INFO
        Entity embeddedInfo = schema.addEntity("EmbeddedInfo").addImport("com.google.gson.annotations.Since");
        embeddedInfo.setTableName("MS_EMBEDDED_INFO");

        embeddedInfo.addLongProperty("id_embedded").notNull().primaryKey();
        embeddedInfo.addDateProperty("dtm_crt");
        embeddedInfo.addStringProperty("usr_crt");
        embeddedInfo.addStringProperty("embedded_info");
        embeddedInfo.addStringProperty("embedded_note");

        //SET RELATION  GENERAL PARAMETER
        generalParameter.addToOne(user, fkGeneralParameterUuidUser);
        user.addToMany(generalParameter, fkQuestionSetUuidScheme);


        //SET RELATION GROUP USER
        groupUser.addToOne(user, fkGroupUserUuidUser);
        user.addToMany(groupUser, fkGroupUserUuidUser);

        //SET RELATION for menu
        menu.addToOne(user, fkMenuUuidUser);
        user.addToMany(menu, fkMenuUuidUser);

        printItem.addToOne(scheme, fkPrintItemUuidScheme);
        scheme.addToMany(printItem, fkPrintItemUuidScheme);


        //SET RELATION ms_questionset
        questionSet.addToOne(scheme, fkQuestionSetUuidScheme);
        scheme.addToMany(questionSet, fkQuestionSetUuidScheme);

        //SET RELATION log
        logger.addToOne(user, fkLoggerUuidUser);
        user.addToMany(logger, fkLoggerUuidUser);


        //SET RELATION collection history
        collectionHistory.addToOne(user, fkCollectionHistoryUuidUser);
        user.addToMany(collectionHistory, fkCollectionHistoryUuidUser);


        //SET RELATION comment
        comment.addToOne(timeline, fkCommentUuidTimeline);
        timeline.addToMany(comment, fkCommentUuidTimeline);


        //SET RELATION deposit report d
        depositReportD.addToOne(depositReportH, fkDepositReportDUuidDepositReportH);
        depositReportH.addToMany(depositReportD, fkDepositReportDUuidDepositReportH);

        //SET RELATION deposit report h
        depositReportH.addToOne(user, fkDepositReportHUuidUser);
        user.addToMany(depositReportH, fkDepositReportHUuidUser);

        //SET RELATION image result
        imageResult.addToOne(taskH, fkImageResultUuidTaskH);
        taskH.addToMany(imageResult, fkImageResultUuidTaskH);

        //SET RELATION installmentSchedule
//					installmentSchedule.addToOne(user, fk_installmentSchedule_uuid_user);
//					user.addToMany(installmentSchedule, fk_installmentSchedule_uuid_user);


        //SET RELATION location tracking
        locationTracking.addToOne(user, fkLocationTrackingUuidUser);
        user.addToMany(locationTracking, fkLocationTrackingUuidUser);


        //SET RELATION message
        message.addToOne(user, fkMessageUuidUser);
        user.addToMany(message, fkMessageUuidUser);

        //SET RELATION mobileContentD
        mobileContentD.addToOne(mobileContentH, fkMobileContentDUuidMobileContentH);
        mobileContentH.addToMany(mobileContentD, fkMobileContentDUuidMobileContentH);


        //SET RELATION mobileContentH
        mobileContentH.addToOne(mobileContentH, fkMobileContentHUuidMobileContentH);
        mobileContentH.addToMany(mobileContentH, fkMobileContentHUuidMobileContentH);

        mobileContentH.addToOne(user, fkMobileContentHUuidUser);
        user.addToMany(mobileContentH, fkMobileContentHUuidUser);


        //SET RELATION payment history
//					paymentHistory.addToOne(user, fk_paymentHistory_user);
//					user.addToMany(paymentHistory, fk_paymentHistory_user);

        //SET RELATION print result
        printResult.addToOne(user, fkPrintResultUser);
        user.addToMany(printResult, fkPrintResultUser);

        //SET RELATION RV
        receiptVoucher.addToOne(user, fkReceiptVoucherUuidUser);
        user.addToMany(receiptVoucher, fkReceiptVoucherUuidUser);

        receiptVoucher.addToOne(taskH, fkReceiptVoucherUuidTaskH);
        taskH.addToMany(receiptVoucher, fkReceiptVoucherUuidTaskH);

        //SET RELATION task d
        taskD.addToOne(taskH, fkTaskDUuidTaskH);
        taskH.addToMany(taskD, fkTaskDUuidTaskH);
        taskD.addToOne(lookup, fkTaskDUuidLookup);

        //SET RELATION task h
        taskH.addToOne(user, fkTaskHUuidUser);
        user.addToMany(taskH, fkTaskHUuidUser);

        taskH.addToOne(scheme, fkTaskHUuidScheme);
        scheme.addToMany(taskH, fkTaskHUuidScheme);

        //SET RELATION error log
        errorLog.addToOne(user, fkErrorLogUuidUser);
        user.addToMany(errorLog, fkErrorLogUuidUser);

        errorLog.addToOne(taskH, fkErrorLogTaskId);
        taskH.addToMany(errorLog, fkErrorLogTaskId);

        //SET RELATION timeline
        timeline.addToOne(user, fkTimelineUuidUser);
        user.addToMany(timeline, fkTimelineUuidUser);

        timeline.addToOne(timelineType, fkTimelineUuidTimelineType);
        timelineType.addToMany(timeline, fkTimelineUuidTimelineType);

        timeline.addToOne(message, fkTimelineUuidMessage);
        message.addToMany(timeline, fkTimelineUuidMessage);

        timeline.addToOne(taskH, fkTimelineUuidTask);
        taskH.addToMany(timeline, fkTimelineUuidTask);

        taskHSequence.addToOne(taskH, fkUuidTaskHSeq);
        taskH.addToMany(taskHSequence, fkUuidTaskHSeq);

        // SET RELATION REMINDER PO
        reminderPo.addToOne(taskH, fkReminderPoUuidTaskH);
        taskH.addToMany(reminderPo, fkReminderPoUuidTaskH);

    }
}