package com.adins.mss.base.dynamicform;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.ProgressDialog;
import android.bluetooth.BluetoothAdapter;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.TrafficStats;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.dynamicform.form.questions.QuestionViewAdapter;
import com.adins.mss.base.mainmenu.MainMenuActivity;
import com.adins.mss.base.timeline.TimelineManager;
import com.adins.mss.base.timeline.activity.Timeline_Activity;
import com.adins.mss.base.todo.Task;
import com.adins.mss.base.todolist.ToDoList;
import com.adins.mss.base.todolist.form.StatusTabFragment;
import com.adins.mss.base.todolist.form.StatusViewAdapter;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.base.util.Utility;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.GeneralParameter;
import com.adins.mss.dao.LocationInfo;
import com.adins.mss.dao.Lookup;
import com.adins.mss.dao.PrintItem;
import com.adins.mss.dao.PrintResult;
import com.adins.mss.dao.QuestionSet;
import com.adins.mss.dao.Scheme;
import com.adins.mss.dao.TaskD;
import com.adins.mss.dao.TaskH;
import com.adins.mss.foundation.camerainapp.helper.Logger;
import com.adins.mss.foundation.db.dataaccess.GeneralParameterDataAccess;
import com.adins.mss.foundation.db.dataaccess.LookupDataAccess;
import com.adins.mss.foundation.db.dataaccess.PrintItemDataAccess;
import com.adins.mss.foundation.db.dataaccess.PrintResultDataAccess;
import com.adins.mss.foundation.db.dataaccess.QuestionSetDataAccess;
import com.adins.mss.foundation.db.dataaccess.ReceiptVoucherDataAccess;
import com.adins.mss.foundation.db.dataaccess.SchemeDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskDDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskHDataAccess;
import com.adins.mss.foundation.db.dataaccess.UserDataAccess;
import com.adins.mss.foundation.dialog.NiftyDialogBuilder;
import com.adins.mss.foundation.formatter.Formatter;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.http.MssResponseType;
import com.adins.mss.foundation.questiongenerator.OptionAnswerBean;
import com.adins.mss.foundation.questiongenerator.QuestionBean;
import com.services.AutoSendImageThread;
import com.services.MainServices;

import org.acra.ACRA;
import org.apache.commons.lang3.StringUtils;

import java.lang.ref.WeakReference;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class TaskManager implements Task {
    public static final String STATUS_TASK_NOT_MAPPING = "5002";
    public static final String STATUS_TASK_DELETED = "2051";
    public static final String STATUS_IMEI_FAILED = "8103";
    public static final String STATUS_TASKD_MISSING = "4040";
    public static final String STATUS_SAVE_FAILED = "7878";
    /*public void saveAndApprovalTask(final Activity activity, final int mode,
            final SurveyHeaderBean header}
//			, final List<QuestionBean> listOfQuestions) {

		new AsyncTask<Void, Void, String>(){
			protected ProgressDialog progressDialog;
			protected TaskH taskH;
			protected String errMessage = null;
			private String messageWait=activity.getString(R.string.progressSend);
			@Override
			protected void onPreExecute() {
		    	progressDialog = ProgressDialog.show(activity, "", this.messageWait, true);
			}
			@Override
			protected String doInBackground(Void... arg0) {
				// TODO Auto-generated method stub
				//bong 8 apr 15 - penjagaan jika autoSend null dari mainMenuActivity
				String result = null;
				saveTask(activity, mode, header, listOfQuestions,null,false);
				if(Tool.isInternetconnected(activity)){

				}
				return null;
			}
			@Override
			protected void onPostExecute(String result) {
				if (progressDialog.isShowing()){
					try {
						progressDialog.dismiss();
					} catch (Exception e) {
                    FireCrash.log(e);
					}
				}
				if(errMessage!=null&&errMessage.length()>0){
					Bundle extras = new Bundle();
					Intent intent = new Intent(activity.getApplicationContext(), SendResultActivity.class);
					extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
					if(flag.equals(Global.FLAG_FOR_APPROVALTASK)){
						extras.putString(Global.BUND_KEY_SEND_RESULT, "Approval Task Failed");
					}else if(flag.equals(Global.FLAG_FOR_REJECTEDTASK)){
						extras.putString(Global.BUND_KEY_SEND_RESULT, "Rejection Task Failed");
					}
					extras.putString(Global.BUND_KEY_TASK_ID, errMessage);
					intent.putExtras(extras);
					activity.startActivity(intent);
					activity.finish();
				}else{
					if(activity.getString(R.string.no_internet_connection).equals(result)){
						Bundle extras = new Bundle();
						Intent intent = new Intent(activity.getApplicationContext(), SendResultActivity.class);
						extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
						if(flag.equals(Global.FLAG_FOR_APPROVALTASK)){
							extras.putString(Global.BUND_KEY_SEND_RESULT, "Approval Task Failed");
						}else if(flag.equals(Global.FLAG_FOR_REJECTEDTASK)){
							extras.putString(Global.BUND_KEY_SEND_RESULT, "Rejection Task Failed");
						}
						extras.putString(Global.BUND_KEY_TASK_ID, result);
						intent.putExtras(extras);
						activity.startActivity(intent);
						activity.finish();
					}else{
						Bundle extras = new Bundle();
						Intent intent = new Intent(activity.getApplicationContext(), SendResultActivity.class);
						extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, false);
						if(flag.equals(Global.FLAG_FOR_APPROVALTASK)){
							extras.putString(Global.BUND_KEY_SEND_RESULT, "Task Approved");
						}else if(flag.equals(Global.FLAG_FOR_REJECTEDTASK)){
							extras.putString(Global.BUND_KEY_SEND_RESULT, "Task Rejected");
						}
						TaskH taskH = null;
						try {
							taskH = TaskHDataAccess.getOneHeader(activity, uuidTaskH);
							TaskHDataAccess.deleteWithRelation(activity, taskH);
						} catch (Exception e) {
                    FireCrash.log(e);	}
						intent.putExtras(extras);
						try {
							if(!isApprovalTask){
								if(taskH!=null){
									taskH.setSubmit_date(Tool.getSystemDateTime());
									TimelineManager.insertTimeline(activity, taskH, true, true);
								}
							}else{
								if(taskH!=null){
									taskH.setSubmit_date(Tool.getSystemDateTime());
									if(flag.equals(Global.FLAG_FOR_APPROVALTASK)){
										TimelineManager.insertTimeline(activity, taskH, false, false);
									}else if(flag.equals(Global.FLAG_FOR_REJECTEDTASK)){
										TimelineManager.insertTimeline(activity, taskH, false, true);
									}
								}
							}
						} catch (Exception e) {
                    FireCrash.log(e);
							// TODO: handle exception
						}
						activity.startActivity(intent);
						activity.finish();
					}
				}
			}
		}.execute();
	}*/
    public static NotificationManager mNotifyManager;
    public static Notification.Builder mBuilder;
    public static Notification.BigTextStyle inboxStyle;
    private static int totalPending = 0;
    String selectedRV, newTaskH, oldTaskH;
    private StatusViewAdapter viewAdapter;
    private static long previousBytes;

    //bong 8 may 15 - add method for generate printResult
    @SuppressLint("NewApi")
    public static void generatePrintResult(Context activity, TaskH taskH) {
        //bong 7 may 15 - make printItemBean in List and save to printResult
        List<PrintItem> printItemList = PrintItemDataAccess.getAll(activity, taskH.getUuid_scheme());
        List<PrintResult> printResultList = new ArrayList<PrintResult>();

        //delete dulu yang ada di database, karena generate printResult dengan jawaban yang baru
        List<PrintResult> printResultByTaskH = PrintResultDataAccess.getAll(activity, taskH.getUuid_task_h());
        if (!printResultByTaskH.isEmpty()) {
            PrintResultDataAccess.delete(activity, taskH.getUuid_task_h());
        }

        for (PrintItem bean : printItemList) {
            PrintResult printResult = new PrintResult(Tool.getUUID());
            printResult.setPrint_type_id(bean.getPrint_type_id());
            printResult.setUser(GlobalData.getSharedGlobalData().getUser());
            printResult.setUuid_task_h(taskH.getUuid_task_h());
            printResult.setDtm_crt_server(bean.getDtm_crt());
            if (bean.getPrint_type_id().equals(Global.PRINT_ANSWER)) {
                printResult.setLabel(bean.getPrint_item_label());
                TaskD taskD = TaskDDataAccess.getMatchDetailWithQuestion(activity, taskH.getUuid_task_h(), bean.getQuestion_id(), bean.getQuestion_group_id());
                if (taskD != null) {
                    String answer = taskD.getText_answer();
                    QuestionSet questionSet = QuestionSetDataAccess.getOne(activity, bean.getUuid_scheme(), bean.getQuestion_id(), bean.getQuestion_group_id());
                    if (questionSet.getAnswer_type().equals(Global.AT_DATE)) {
                        //convert String to date first
                        DateFormat format = new SimpleDateFormat("ddMMyyyyHHmmss");
                        Date date = null;
                        try {
                            date = format.parse(taskD.getText_answer());
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }

                        //convert date to string
                        SimpleDateFormat sdf = new SimpleDateFormat("dd-MMM-yyyy");
                        String stringDate = sdf.format(date);
                        answer = stringDate;
                    } else if (Global.AT_CURRENCY.equals(questionSet.getAnswer_type())) {
                        answer = Tool.separateThousand(answer);
                        answer = GlobalData.getSharedGlobalData().getCurrencyType() + answer;
                    } else if (Global.AT_DROPDOWN.equals(questionSet.getAnswer_type())) {
                        answer = taskD.getLov();
                    }
                    printResult.setValue(answer);
                }
            } else if (bean.getPrint_type_id().equals(Global.PRINT_BRANCH_ADDRESS)) {
                printResult.setLabel(GlobalData.getSharedGlobalData().getUser().getBranch_address());
                printResult.setValue("");
            } else if (bean.getPrint_type_id().equals(Global.PRINT_BRANCH_NAME)) {
                printResult.setLabel(GlobalData.getSharedGlobalData().getUser().getBranch_name());
                printResult.setValue("");
            } else if (bean.getPrint_type_id().equals(Global.PRINT_BT_ID)) {
//				BluetoothManager bm = (BluetoothManager) activity.getSystemService(Context.BLUETOOTH_SERVICE);
//				String address = bm.getAdapter().getAddress();
//				printResult.setLabel(address);
//				printResult.setValue("");
                String btAddr = "?";
                try {
                    btAddr = BluetoothAdapter.getDefaultAdapter().getAddress();
                } catch (Exception e) {
                    FireCrash.log(e);

                }
                // BluetoothManager bm = (BluetoothManager)
                // activity.getSystemService(Context.BLUETOOTH_SERVICE);
                // String address = bm.getAdapter().getAddress();
                printResult.setLabel(bean.getPrint_item_label());
                printResult.setValue(btAddr);
            } else if (bean.getPrint_type_id().equals(Global.PRINT_LABEL)) {
                printResult.setLabel(bean.getPrint_item_label());
                printResult.setValue("");
            } else if (bean.getPrint_type_id().equals(Global.PRINT_LABEL_BOLD)) {
                printResult.setLabel(bean.getPrint_item_label());
                printResult.setValue("");
            } else if (bean.getPrint_type_id().equals(Global.PRINT_LABEL_CENTER)) {
                printResult.setLabel(bean.getPrint_item_label());
                printResult.setValue("");
            } else if (bean.getPrint_type_id().equals(Global.PRINT_LABEL_CENTER_BOLD)) {
                printResult.setLabel(bean.getPrint_item_label());
                printResult.setValue("");
            } else if (bean.getPrint_type_id().equals(Global.PRINT_LOGO)) {
                printResult.setLabel("");
                printResult.setValue("");
            } else if (bean.getPrint_type_id().equals(Global.PRINT_NEW_LINE)) {
                printResult.setLabel("\n");
                printResult.setValue("\n");
            } else if (bean.getPrint_type_id().equals(Global.PRINT_TIMESTAMP)) {
/*				// Create an instance of SimpleDateFormat used for formatting
                // the string representation of date (month/day/year)
				DateFormat df = new SimpleDateFormat("MM/dd/yyyy HH:mm:ss");
				// Get the date today using Calendar object.
				Date today = Calendar.getInstance().getTime();
				// Using DateFormat format method we can create a string
				// representation of a date with the defined format.
				String reportDate = df.format(today);

				// Print what date is today!
				//if (Global.IS_DEV) System.out.println("Report Date: " + reportDate);

				printResult.setLabel(reportDate);
				printResult.setValue("");*/
                printResult.setLabel(bean.getPrint_item_label());
                printResult.setValue("");
            } else if (bean.getPrint_type_id().equals(Global.PRINT_USER_NAME)) {
                printResult.setLabel(bean.getPrint_item_label());
                printResult.setValue(GlobalData.getSharedGlobalData().getUser().getFullname());
            } else if (bean.getPrint_type_id().equals(Global.PRINT_LOGIN_ID)) {
                printResult.setLabel(bean.getPrint_item_label());
                printResult.setValue(GlobalData.getSharedGlobalData().getUser().getLogin_id());
            }
            printResult.setUuid_print_result(Tool.getUUID());
            printResultList.add(printResult);

            //TODO add addOrReplace to printResultDataAccess
            PrintResultDataAccess.add(activity, printResult);
        }
    }

    public static boolean isPartial(Context context) {

        GeneralParameter gp2 = GeneralParameterDataAccess.getOne(context, GlobalData.getSharedGlobalData().getUser().getUuid_user(), Global.GS_PARTIAL_SENT);
        boolean isPartial = true;
        if (gp2 != null) {
            if (gp2.getGs_value().equals("0")) {
                isPartial = false;
            } else {
                isPartial = true;
            }
//            isPartial = !gp2.getGs_value().equals("0");
        }
        GeneralParameter gp1 = GeneralParameterDataAccess.getOne(context, GlobalData.getSharedGlobalData().getUser().getUuid_user(), Global.GS_INTERVAL_AUTOSEND);
        if (gp1 != null && gp1.getGs_value().equals("0")) {
            return false;
        }
        return isPartial;
    }

    public static void ManualUploadImage(final Activity activity, final List<TaskD> taskDs, final TaskH taskH) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            mNotifyManager = (NotificationManager) activity.getSystemService(Context.NOTIFICATION_SERVICE);
            mBuilder = new Notification.Builder(activity);
            mBuilder.setContentTitle(activity.getString(R.string.uploading_image))
                    .setContentText(activity.getString(R.string.progress_uploading))
                    .setSmallIcon(AutoSendImageThread.getNotificationUploadingIcon());
            inboxStyle = new Notification.BigTextStyle();
            inboxStyle.setBigContentTitle(activity.getString(R.string.uploading_image));
            inboxStyle.bigText(activity.getString(R.string.progress_uploading));
            mBuilder.setStyle(inboxStyle);
        }
        new AsyncTask<Void, Void, String>() {
            private Integer errCode;
            private String errMessage = null;

            @Override
            protected String doInBackground(Void... params) {
                String message = null;
                String result = null;
                Global.isUploading = true;
                Global.isManualUploading = true;
                previousBytes = TrafficStats.getTotalRxBytes();

                try {
                    if (MainServices.autoSendTaskThread != null) {
                        MainServices.autoSendTaskThread.requestWait();
                        MainServices.autoSendImageThread.requestWait();
                        MainServices.taskListThread.requestWait();
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    // TODO: handle exception
                }
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                    Handler handler = new Handler(Looper.getMainLooper());
                    handler.post(new Runnable() {
                        public void run() {
                            mBuilder.setProgress(0, 0, true);
                            mBuilder.setOngoing(true);
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                                if (android.os.Build.VERSION.SDK_INT >=android.os.Build.VERSION_CODES.O) {
                                    String channelId = "channel_task_send";
                                    String channelName = "Task Send";
                                    int importance = NotificationManager.IMPORTANCE_HIGH;
                                    NotificationChannel notificationChannel = new NotificationChannel(channelId, channelName, importance);
                                    if (null == mNotifyManager) {
                                        throw new NullPointerException("mNotifyManager is null");
                                    }
                                    mBuilder.setChannelId(channelId);
                                    mNotifyManager.createNotificationChannel(notificationChannel);
                                }
                                mNotifyManager.notify(4, mBuilder.build());
                            }
                        }
                    });
                }
                String taskId = null;
                int count = taskDs.size();
                if (count > 0) {
                    Global.isUploading = true;
                    Global.isManualUploading = true;
                    for (TaskD taskD : taskDs) {
                        try {
                            String uuidTaskH = taskD.getTaskH().getUuid_task_h();
                            String taskNo = taskD.getTaskH().getTask_id();
                            totalPending = AutoSendImageThread.countPendingImageUpload(activity, uuidTaskH);
                            final int totalimage = AutoSendImageThread.countTotalImageTask(activity, uuidTaskH);
                            boolean finale = AutoSendImageThread.checkFinal(totalPending);
                            if (finale) {
                                taskD.setIs_final(Global.TRUE_STRING);
                            }
                            final TaskH taskH = taskD.getTaskH();
                            taskD.setTaskH(null);
                            taskD.setUuid_task_h(uuidTaskH);

                            if (taskD.getCount() == null) {
                                taskD.setCount(Global.FALSE_STRING);
                            }
                            JsonRequestSubmitTask task = new JsonRequestSubmitTask();
                            task.setAudit(GlobalData.getSharedGlobalData().getAuditData());
                            task.addImeiAndroidIdToUnstructured();
                            task.setTaskH(taskD.getTaskH());
                            List<TaskD> ds = new ArrayList<TaskD>();
                            ds.add(taskD);
                            task.setTaskD(ds);

                            if (finale) {
                                Map<String, QuestionSet> listSubmitLayerQuestion = new HashMap<>();
                                List<TaskD> listTaskD = TaskDDataAccess.getAll(activity, taskD.getTaskH().getUuid_task_h());
                                if (listTaskD != null && !listTaskD.isEmpty()) {
                                    for(TaskD d:listTaskD) {
                                        QuestionSet question = QuestionSetDataAccess.getOne(activity, taskD.getTaskH().getUuid_scheme(), d.getQuestion_id(), d.getQuestion_group_id(), taskD.getTaskH().getForm_version());
                                        if (Global.AT_SUBMIT_LAYER.equalsIgnoreCase(question.getAnswer_type())) {
                                            listSubmitLayerQuestion.put(question.getQuestion_group_id(), question);
                                        }
                                    }
                                    //2022-04-16 Riska - tambahan untuk submit per layer
                                    task = createFiltersForSubmitLayer(activity, task, listSubmitLayerQuestion);
                                    //end
                                }
                            }
                            String json = GsonHelper.toJson(task);
                            String url = GlobalData.getSharedGlobalData().getURL_SUBMITTASK();

                            if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                url = GlobalData.getSharedGlobalData().getURL_SUBMITVERIFICATIONTASK();
                            } else {
                                url = GlobalData.getSharedGlobalData().getURL_SUBMITTASK();
                            }

                            try {
                                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                                    Handler handler = new Handler(Looper.getMainLooper());
                                    handler.post(new Runnable() {
                                        public void run() {
                                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                                                int uplod = totalimage - totalPending + 1;
                                                String counter = activity.getString(R.string.uploading_image_counter, uplod, totalimage);
                                                String title = activity.getString(R.string.uploading_image_title, taskH.getCustomer_name());
                                                int progress = TaskDDataAccess.getProgressUploadImage(activity, taskH.getUuid_task_h());
                                                String speedKbps = getSpeedKbps();
                                                mBuilder.setContentTitle(title + speedKbps);
                                                mBuilder.setContentText(String.valueOf(progress)+"% "+counter)
                                                        // Removes the progress bar
                                                        .setProgress(0, 0, true);
                                                inboxStyle.setBigContentTitle(title + speedKbps);
                                                inboxStyle.bigText(String.valueOf(progress)+"% "+counter);
                                                mBuilder.setStyle(inboxStyle);
                                                mBuilder.setOngoing(true);

                                                if (android.os.Build.VERSION.SDK_INT >=android.os.Build.VERSION_CODES.O) {
                                                    String channelId = "channel_task_send";
                                                    String channelName = "Task Send";
                                                    int importance = NotificationManager.IMPORTANCE_HIGH;
                                                    NotificationChannel notificationChannel = new NotificationChannel(channelId, channelName, importance);
                                                    if (null == mNotifyManager) {
                                                        throw new NullPointerException("mNotifyManager is null");
                                                    }
                                                    mBuilder.setChannelId(channelId);
                                                    mNotifyManager.createNotificationChannel(notificationChannel);
                                                }
                                                mNotifyManager.notify(4, mBuilder.build());
                                            }
                                        }
                                    });
                                }
                            } catch (Exception e) {
                                FireCrash.log(e);
                            }

                            boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                            boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                            HttpCryptedConnection httpConn = new HttpCryptedConnection(activity, encrypt, decrypt);
                            HttpConnectionResult serverResult = null;
                            Date startSend = Tool.getSystemDateTime();
                            try {
                                serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
                            } catch (Exception ex) {
                                FireCrash.log(ex);
                                message = activity.getString(R.string.upload_queue);
                                break;
                            }

                            if (serverResult != null && serverResult.isOK()) {
                                String resultvalue = serverResult.getResult();
                                try {
                                    JsonResponseSubmitTask responseSubmitTask = GsonHelper.fromJson(resultvalue, JsonResponseSubmitTask.class);

                                    int statusCode = responseSubmitTask.getStatus().getCode();
                                    if (statusCode == 0) {
                                        Date finishSend = Tool.getSystemDateTime();
                                        if (Global.IS_DEV)
                                            Logger.i("INFO", "Berhasil terkirim");
                                        if (finale) {
                                            ToDoList.removeSurveyFromList(taskNo);
                                            if (responseSubmitTask.getTaskId() != null)
                                                taskId = responseSubmitTask.getTaskId();
                                            if (taskId != null && taskId.length() > 0) {
                                                if (responseSubmitTask.isDropTask()) {
                                                    taskH.setMessage(responseSubmitTask.getMessage());
                                                    taskH.setStatus(TaskHDataAccess.STATUS_SEND_DROP);
                                                    TaskHDataAccess.update(activity,taskH);
                                                    TimelineManager.insertTimeline(activity, taskH);
                                                } else {
                                                    taskH.setStatus(TaskHDataAccess.STATUS_SEND_UPLOADING);
                                                    TimelineManager.insertTimeline(activity, taskH);
                                                    doAfterFinish(activity, taskD, startSend, finishSend, taskId);
                                                    message = "";
                                                }

                                                if (StatusTabFragment.handler != null)
                                                    StatusTabFragment.handler.sendEmptyMessage(0);
                                                Utility.freeMemory();
                                            }
                                        } else {
                                            message = responseSubmitTask.getStatus().getMessage();
                                            totalPending--;
                                            ToDoList.updateStatusSurvey(taskNo, TaskHDataAccess.STATUS_SEND_UPLOADING, totalPending);
                                            taskD.setIs_sent(Global.TRUE_STRING);
                                            TaskDDataAccess.addOrReplace(activity, taskD);
                                            if (StatusTabFragment.handler != null)
                                                StatusTabFragment.handler.sendEmptyMessage(0);
                                        }
                                    } else {
                                        if (resultvalue != null) {
                                            if (Global.IS_DEV)
                                                Logger.i("INFO", "Status Auto Send Image : " + resultvalue);
                                            ACRA.getErrorReporter().handleSilentException(new Exception("Saat Upload image status code!= 0, " + resultvalue));
                                        }
                                        result = String.valueOf(responseSubmitTask.getStatus().getCode());
                                        errMessage = responseSubmitTask.getStatus().getMessage();
                                        errCode = responseSubmitTask.getStatus().getCode();
                                    }
                                } catch (Exception e) {
                                    FireCrash.log(e);
                                    message = activity.getString(R.string.upload_queue);
                                    taskH.setStatus(TaskHDataAccess.STATUS_SEND_UPLOADING);
                                    TimelineManager.insertTimeline(activity, taskH);
                                    break;
                                }
                            } else {
                                message = activity.getString(R.string.upload_queue);
                                taskH.setStatus(TaskHDataAccess.STATUS_SEND_UPLOADING);
                                TimelineManager.insertTimeline(activity, taskH);
                                if (serverResult != null) {
                                    String statusCode = String.valueOf(serverResult.getStatusCode()).trim();
                                    if (statusCode != null && statusCode.charAt(0) != '5') {
                                        //set counter for taskD
                                        taskD.setIs_sent(Global.FALSE_STRING);
                                        TaskDDataAccess.addOrReplace(activity, taskD);
                                    }
                                }
                                break;
                            }

                            if (errMessage != null) {
                                message = "failed";
                                mNotifyManager.cancelAll();
                                taskH.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);
                                if (result != null && result.equals(STATUS_TASK_NOT_MAPPING)) {
                                    taskH.setMessage(activity.getString(R.string.message_task_not_mapping) + " - Error (" + errCode + ")");
                                } else if (result != null && result.equals(STATUS_TASK_DELETED)) {
                                    taskH.setMessage(activity.getString(R.string.message_sending_deleted) + " - Error (" + errCode + ")");
                                } else if (result != null && result.equals(STATUS_IMEI_FAILED)) {
                                    taskH.setMessage(activity.getString(R.string.message_imei_not_registered) + " - Error (" + errCode + ")");
                                } else {
                                    taskH.setMessage(errMessage + " - Error (" + errCode + ")");
                                }
                                //
                                // TaskHDataAccess.addOrReplace(activity, taskH);
                                TimelineManager.insertTimeline(activity, taskH);
                            }
                        } catch (Exception e) {
                            FireCrash.log(e);
                            message = activity.getString(R.string.upload_failed);
                            e.printStackTrace();
                        }
                    }
                    Handler handler = new Handler(Looper.getMainLooper());
                    handler.post(new Runnable() {
                        public void run() {
                            try {
                                try {
                                    MainMenuActivity.setDrawerCounter();
                                } catch (Exception e) {
                                    FireCrash.log(e);
                                    // TODO: handle exception
                                }
                                if (Timeline_Activity.timelineHandler != null)
                                    Timeline_Activity.timelineHandler.sendEmptyMessage(0);
                            } catch (Exception e) {
                                FireCrash.log(e);
                                // TODO: handle exception
                            }
                        }
                    });
                } else {
                    message = activity.getString(R.string.no_internet_connection);
                }

                Global.isUploading = false;
                Global.isManualUploading = false;

                return message;
            }

            @Override
            protected void onPostExecute(final String message) {
                try {
                    if (MainServices.autoSendTaskThread != null) {
                        MainServices.autoSendTaskThread.stopWaiting();
                        MainServices.autoSendImageThread.stopWaiting();
                        MainServices.taskListThread.stopWaiting();
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    // TODO: handle exception
                }
                try {
                    if (message == null || message.length() == 0) {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                            Handler handler = new Handler(Looper.getMainLooper());
                            handler.post(new Runnable() {
                                public void run() {
                                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                                        String counter = activity.getString(R.string.upload_complete);
                                        String title = activity.getString(R.string.uploading_image_title, taskH.getCustomer_name());
                                        int progress = TaskDDataAccess.getProgressUploadImage(activity, taskH.getUuid_task_h());
                                        String speedKbps = getSpeedKbps();
                                        mBuilder.setContentTitle(title + speedKbps);
                                        mBuilder.setContentText(String.valueOf(progress)+"% "+counter)
                                                // Removes the progress bar
                                                .setProgress(0, 0, false);
                                        mBuilder.setOngoing(false);
                                        inboxStyle.setBigContentTitle(title + speedKbps);
                                        inboxStyle.bigText(String.valueOf(progress)+"% "+counter);
                                        mBuilder.setStyle(inboxStyle);

                                        if (android.os.Build.VERSION.SDK_INT >=android.os.Build.VERSION_CODES.O) {
                                            String channelId = "channel_task_send";
                                            String channelName = "Task Send";
                                            int importance = NotificationManager.IMPORTANCE_HIGH;
                                            NotificationChannel notificationChannel = new NotificationChannel(channelId, channelName, importance);
                                            if (null == mNotifyManager) {
                                                throw new NullPointerException("mNotifyManager is null");
                                            }
                                            mBuilder.setChannelId(channelId);
                                            mNotifyManager.createNotificationChannel(notificationChannel);
                                        }
                                        mNotifyManager.notify(4, mBuilder.build());
                                    }
                                }
                            });
                        }
                    } else if (message.equals("failed")) {
                        mNotifyManager.cancelAll();
                    } else {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                            Handler handler = new Handler(Looper.getMainLooper());
                            handler.post(new Runnable() {
                                public void run() {
                                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                                        String title = activity.getString(R.string.uploading_image_title, taskH.getCustomer_name());
                                        int progress = TaskDDataAccess.getProgressUploadImage(activity, taskH.getUuid_task_h());
                                        mBuilder.setContentTitle(title);
                                        mBuilder.setContentText(String.valueOf(progress)+"% "+message)
                                                // Removes the progress bar
                                                .setProgress(0, 0, false);
                                        mBuilder.setOngoing(false);
                                        inboxStyle.setBigContentTitle(title);
                                        inboxStyle.bigText(String.valueOf(progress)+"% "+message);
                                        mBuilder.setStyle(inboxStyle);
                                        if (android.os.Build.VERSION.SDK_INT >=android.os.Build.VERSION_CODES.O) {
                                            String channelId = "channel_task_send";
                                            String channelName = "Task Send";
                                            int importance = NotificationManager.IMPORTANCE_HIGH;
                                            NotificationChannel notificationChannel = new NotificationChannel(channelId, channelName, importance);
                                            if (null == mNotifyManager) {
                                                throw new NullPointerException("mNotifyManager is null");
                                            }
                                            mBuilder.setChannelId(channelId);
                                            mNotifyManager.createNotificationChannel(notificationChannel);
                                        }
                                        mNotifyManager.notify(4, mBuilder.build());
                                    }
                                    // To show message on list task
                                    if (errMessage != null) {
                                        taskH.setMessage(errMessage + " - Error (" + errCode + ")");
                                        TimelineManager.insertTimeline(activity, taskH);
                                    }
                                }
                            });
                        }
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    Global.isUploading = false;
                    Global.isManualUploading = false;
                }
            }
        }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR);
    }

    protected static String UploadImage(final Context context, List<TaskD> sendImage) {
        // TODO Auto-generated method stub
        String errMessage = "";
        Global.isUploading = true;
        Global.isManualUploading = true;
        if (Tool.isInternetconnected(context)) {
            previousBytes = TrafficStats.getTotalRxBytes();
            String taskId = null;
            int count = sendImage.size();
            if (count > 0) {
                Global.isUploading = true;
                Global.isManualUploading = true;
                for (TaskD taskd : sendImage) {
                    try {
                        if (Tool.isInternetconnected(context)) {
                            String uuidTaskH = taskd.getTaskH().getUuid_task_h();
                            String taskNo = taskd.getTaskH().getTask_id();
                            totalPending = AutoSendImageThread.countPendingImageUpload(context, uuidTaskH);
                            final int totalimage = AutoSendImageThread.countTotalImageTask(context, uuidTaskH);
                            boolean finale = AutoSendImageThread.checkFinal(totalPending);
                            if (finale) {
                                taskd.setIs_final(Global.TRUE_STRING);
                            }
//                            taskd.setTaskH(null);
                            taskd.getTaskH().getScheme();
                            taskd.setUuid_task_h(uuidTaskH);

                            if (taskd.getCount() == null) {
                                taskd.setCount(Global.FALSE_STRING);
                            }
                            JsonRequestSubmitTask task = new JsonRequestSubmitTask();
                            task.setAudit(GlobalData.getSharedGlobalData().getAuditData());
                            task.addImeiAndroidIdToUnstructured();
                            task.setTaskH(taskd.getTaskH());
                            List<TaskD> ds = new ArrayList<TaskD>();
                            ds.add(taskd);
                            task.setTaskD(ds);

                            if (finale) {
                                Map<String, QuestionSet> listSubmitLayerQuestion = new HashMap<>();
                                List<TaskD> listTaskD = TaskDDataAccess.getAll(context, taskd.getTaskH().getUuid_task_h());
                                if (listTaskD != null && !listTaskD.isEmpty()) {
                                    for(TaskD d:listTaskD) {
                                        QuestionSet question = QuestionSetDataAccess.getOne(context, taskd.getTaskH().getUuid_scheme(), d.getQuestion_id(), d.getQuestion_group_id(), taskd.getTaskH().getForm_version());
                                        if (Global.AT_SUBMIT_LAYER.equalsIgnoreCase(question.getAnswer_type())) {
                                            listSubmitLayerQuestion.put(question.getQuestion_group_id(), question);
                                        }
                                    }
                                    //2022-04-16 Riska - tambahan untuk submit per layer
                                    task = createFiltersForSubmitLayer(context, task, listSubmitLayerQuestion);
                                    //end
                                }
                            }

                            String json = GsonHelper.toJson(task);
                            String url = GlobalData.getSharedGlobalData().getURL_SUBMITTASK();
                            final TaskH taskH = taskd.getTaskH();
                            taskH.getUser();
                            if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                url = GlobalData.getSharedGlobalData().getURL_SUBMITVERIFICATIONTASK();
                            } else {
                                url = GlobalData.getSharedGlobalData().getURL_SUBMITTASK();
                            }
                            try {
                                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                                    Handler handler = new Handler(Looper.getMainLooper());
                                    handler.post(new Runnable() {
                                        public void run() {
                                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                                                int uplod = totalimage - totalPending + 1;
                                                String counter = context.getString(R.string.uploading_image_counter, uplod, totalimage);
                                                String title = context.getString(R.string.uploading_image_title, taskH.getCustomer_name());
                                                int progress = TaskDDataAccess.getProgressUploadImage(context, taskH.getUuid_task_h());
                                                String speedKbps = getSpeedKbps();
                                                mBuilder.setContentTitle(title + speedKbps);
                                                mBuilder.setContentText(String.valueOf(progress)+"% "+counter)
                                                        // Removes the progress bar
                                                        .setProgress(0, 0, true);
                                                inboxStyle.setBigContentTitle(title + speedKbps);
                                                inboxStyle.bigText(String.valueOf(progress)+"% "+counter);
                                                mBuilder.setStyle(inboxStyle);
                                                mBuilder.setOngoing(true);
                                                if (android.os.Build.VERSION.SDK_INT >=android.os.Build.VERSION_CODES.O) {
                                                    String channelId = "channel_task_send";
                                                    String channelName = "Task Send";
                                                    int importance = NotificationManager.IMPORTANCE_HIGH;
                                                    NotificationChannel notificationChannel = new NotificationChannel(channelId, channelName, importance);
                                                    if (null == mNotifyManager) {
                                                        throw new NullPointerException("mNotifyManager is null");
                                                    }
                                                    mBuilder.setChannelId(channelId);
                                                    mNotifyManager.createNotificationChannel(notificationChannel);
                                                }
                                                mNotifyManager.notify(4, mBuilder.build());
                                            }
                                        }
                                    });
                                }
                            } catch (Exception e) {
                                FireCrash.log(e);

                            }
                            boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                            boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                            HttpCryptedConnection httpConn = new HttpCryptedConnection(context, encrypt, decrypt);
                            HttpConnectionResult serverResult = null;
                            Date startSend = Tool.getSystemDateTime();
                            try {
                                serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
                            } catch (Exception ex) {
                                FireCrash.log(ex);
                                errMessage = context.getString(R.string.upload_queue);
                                break;
                            }
                            if (serverResult != null && serverResult.isOK()) {
                                String resultvalue = serverResult.getResult();
                                try {
                                    JsonResponseSubmitTask responseSubmitTask = GsonHelper.fromJson(resultvalue, JsonResponseSubmitTask.class);

                                    int statusCode = responseSubmitTask.getStatus().getCode();
                                    if (statusCode == 0) {
                                        Date finishSend = Tool.getSystemDateTime();
                                        if (Global.IS_DEV)
                                            Logger.i("INFO", "Berhasil terkirim");
                                        if (finale) {
                                            ToDoList.removeSurveyFromList(taskNo);
                                            if (responseSubmitTask.getTaskId() != null)
                                                taskId = responseSubmitTask.getTaskId();
                                            if (taskId != null && taskId.length() > 0) {

                                                if (responseSubmitTask.isDropTask()) {
                                                    taskH.setMessage(responseSubmitTask.getMessage());
                                                    taskH.setStatus(TaskHDataAccess.STATUS_SEND_DROP);
                                                    TaskHDataAccess.update(context,taskH);
                                                    TimelineManager.insertTimeline(context, taskH);
                                                } else {
                                                    taskd.setIs_sent(Global.TRUE_STRING);
                                                    TaskDDataAccess.addOrReplace(context, taskd);
                                                    doAfterFinish(context, taskd, startSend, finishSend, taskId);
                                                }
                                                errMessage = "";
//                                                if (StatusTabFragment.handler != null)
//                                                    StatusTabFragment.handler.sendEmptyMessage(0);
                                                Utility.freeMemory();
                                            }
                                        } else {
                                            errMessage = responseSubmitTask.getStatus().getMessage();
                                            totalPending--;
                                            ToDoList.updateStatusSurvey(taskNo, TaskHDataAccess.STATUS_SEND_UPLOADING, totalPending);
                                            taskd.setIs_sent(Global.TRUE_STRING);
                                            TaskDDataAccess.addOrReplace(context, taskd);
                                            if (StatusTabFragment.handler != null)
                                                StatusTabFragment.handler.sendEmptyMessage(0);
                                        }
                                    } else {
                                        if (resultvalue != null) {
                                            if (Global.IS_DEV)
                                                Logger.i("INFO", "Status Auto Send Image : " + resultvalue);
                                            ACRA.getErrorReporter().handleSilentException(new Exception("Saat Upload image status code!= 0, " + resultvalue));
                                        }
                                        errMessage = responseSubmitTask.getStatus().getMessage();
                                        String result = String.valueOf(responseSubmitTask.getStatus().getCode());
                                        Integer errCode = responseSubmitTask.getStatus().getCode();
                                        if (errMessage != null) {
                                            mNotifyManager.cancelAll();
                                            taskH.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);
                                            if (result != null && result.equals(STATUS_TASK_NOT_MAPPING)) {
                                                taskH.setMessage(context.getString(R.string.message_task_not_mapping) + " - Error (" + errCode + ")");
                                            } else if (result != null && result.equals(STATUS_TASK_DELETED)) {
                                                taskH.setMessage(context.getString(R.string.message_sending_deleted) + " - Error (" + errCode + ")");
                                            } else if (result != null && result.equals(STATUS_IMEI_FAILED)) {
                                                taskH.setMessage(context.getString(R.string.message_imei_not_registered) + " - Error (" + errCode + ")");
                                            } else {
                                                taskH.setMessage(errMessage + " - Error (" + errCode + ")");
                                            }
                                            //
                                            // TaskHDataAccess.addOrReplace(activity, taskH);
                                            TimelineManager.insertTimeline(context, taskH);
                                        }
                                    }
                                } catch (Exception e) {
                                    FireCrash.log(e);
                                    errMessage = context.getString(R.string.upload_queue);
                                    taskH.setStatus(TaskHDataAccess.STATUS_SEND_UPLOADING);
                                    TimelineManager.insertTimeline(context, taskH);
                                    break;
                                }
                            } else {
                                errMessage = context.getString(R.string.upload_queue);
                                taskH.setStatus(TaskHDataAccess.STATUS_SEND_UPLOADING);
                                TimelineManager.insertTimeline(context, taskH);
                                String statusCode = String.valueOf(serverResult.getStatusCode()).trim();
                                if (statusCode != null && statusCode.charAt(0) != '5') {
                                    //set counter for taskD
//                                    taskd.setCount(Global.TRUE_STRING);
                                    taskd.setIs_sent(Global.FALSE_STRING);
                                    TaskDDataAccess.addOrReplace(context, taskd);
                                }
                                break;
                            }
                        } else {
                            errMessage = context.getString(R.string.no_internet_connection);
                            break;
                        }
                    } catch (Exception ex) {
                        FireCrash.log(ex);
                        errMessage = context.getString(R.string.upload_failed);
                        ex.printStackTrace();
                        break;
                    }
                }

                Handler handler = new Handler(Looper.getMainLooper());
                handler.post(new Runnable() {
                    public void run() {
                        // UI code goes here
                        try {
                            try {
                                MainMenuActivity.setDrawerCounter();
                            } catch (Exception e) {
                                FireCrash.log(e);
                                // TODO: handle exception
                            }
//                            long taskListCounter = ToDoList.getAllCounter(context);
//                            Timeline_Activity.query.id(R.id.txtJumlahOutstanding).text(String.valueOf(taskListCounter));
                            if (Timeline_Activity.timelineHandler != null)
                                Timeline_Activity.timelineHandler.sendEmptyMessage(0);
                        } catch (Exception e) {
                            FireCrash.log(e);
                            // TODO: handle exception
                        }
                    }
                });
            }
        } else {
            errMessage = context.getString(R.string.no_internet_connection);
        }

        Global.isUploading = false;
        Global.isManualUploading = false;

        return errMessage;
    }

    private static void doAfterFinish(final Context context, TaskD taskd, Date startSend, Date finishSend, String taskId) {
        long time = finishSend.getTime() - startSend.getTime();
        int sec = (int) Math.ceil(time / 1000); // millisecond to second
        byte[] answer = taskd.getImage();
        int size = answer.length;

        TaskH taskH = taskd.getTaskH();
        taskH.setStatus(TaskHDataAccess.STATUS_SEND_SENT);
        taskH.setLast_saved_question(1);
        taskH.setTask_id(taskId);
        taskH.setSubmit_date(taskH.getSubmit_date());
        taskH.setSubmit_duration(String.valueOf(sec));
        taskH.setSubmit_size(String.valueOf(size));
        boolean isRVinFront = GeneralParameterDataAccess.isRvInFrontEnable(context, GlobalData.getSharedGlobalData().getUser().getUuid_user());
        if (isRVinFront && Global.APPLICATION_COLLECTION.equalsIgnoreCase(GlobalData.getSharedGlobalData().getApplication())) {
            taskH.setStatus_rv(TaskHDataAccess.STATUS_RV_SENT);
        }

        //update flagging send task cae
        if ( Global.FORM_NAME_PROMISE_TO_SURVEY.equalsIgnoreCase(taskH.getScheme().getScheme_description())
                && TaskHDataAccess.STATUS_SEND_FAILED.equalsIgnoreCase(taskH.getSend_task_promise_to_survey())) {
            TaskHDataAccess.updateFlaggingSendTask(taskH, TaskHDataAccess.STATUS_SEND_SENT, context);
        } else  if ( Global.FORM_NAME_PRE_SURVEY.equalsIgnoreCase(taskH.getScheme().getScheme_description())
                && TaskHDataAccess.STATUS_SEND_FAILED.equalsIgnoreCase(taskH.getSend_task_presurvey())) {
            TaskHDataAccess.updateFlaggingSendTask(taskH, TaskHDataAccess.STATUS_SEND_SENT, context);
        }

        TaskHDataAccess.addOrReplace(context, taskH);

        taskd.setIs_sent(Global.TRUE_STRING);
        TaskDDataAccess.addOrReplace(context, taskd);

//        long logCounter = TaskLog.getCounterLog(context);
//        long taskListCounter = ToDoList.getCounterTaskList(context);
        Handler handler = new Handler(Looper.getMainLooper());
        handler.post(new Runnable() {
            public void run() {
                // UI code goes here
                try {
                    try {
                        MainMenuActivity.setDrawerCounter();
                    } catch (Exception e) {
                        FireCrash.log(e);
                        // TODO: handle exception
                    }
                    if (StatusTabFragment.handler != null)
                        StatusTabFragment.handler.sendEmptyMessage(0);
//                    if(Timeline_Activity.getTimelineHandler() != null)
//                        Timeline_Activity.getTimelineHandler().sendEmptyMessage(0);
                } catch (Exception e) {
                    FireCrash.log(e);
                    // TODO: handle exception
                }
            }
        });

        TimelineManager.insertTimeline(context, taskH);
    }

    public static void sendRejectedTask(final Context activity,
                                        final SurveyHeaderBean header, final String flag, final String notes) {
        new AsyncTask<Void, Void, String>() {
            private String errMessage = null;

            @Override
            protected String doInBackground(Void... params) {
                String result = null;
                if (Tool.isInternetconnected(activity)) {
                    String uuidTaskH = header.getUuid_task_h();
                    JsonRequestRejectedVerificationTask request = new JsonRequestRejectedVerificationTask();
                    request.setUuid_task_h(uuidTaskH);
                    request.setFlag(flag);
                    request.setNotes(notes);
                    request.setAudit(GlobalData.getSharedGlobalData()
                            .getAuditData());
                    request.addImeiAndroidIdToUnstructured();

                    String json = GsonHelper.toJson(request);
                    String url = GlobalData.getSharedGlobalData()
                            .getURL_SUBMITVERIFICATIONTASK();

                    boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                    boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                    HttpCryptedConnection httpConn = new HttpCryptedConnection(activity, encrypt, decrypt);
                    HttpConnectionResult serverResult = null;

                    try {
                        serverResult = httpConn.requestToServer(url, json);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        errMessage = e.getMessage();
                    }

                    try {
                        result = serverResult.getResult();
                        MssResponseType response = GsonHelper.fromJson(result,
                                MssResponseType.class);
                        if (response.getStatus().getCode() == 0) {
                            result = "success";
                        } else {
                            errMessage = result;
                        }
                    } catch (Exception e) {
                        FireCrash.log(e);
                        errMessage = e.getMessage();
                    }
                } else {
                    result = activity
                            .getString(R.string.no_internet_connection);
                }
                return result;
            }

            @Override
            protected void onPostExecute(String result) {
                if (errMessage != null && errMessage.length() > 0) {
//					Bundle extras = new Bundle();
//					Intent intent = new Intent(
//							activity.getApplicationContext(),
//							SendResultActivity.class);
//					extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
//					if (flag.equals(Global.FLAG_FOR_APPROVALTASK)) {
//						extras.putString(Global.BUND_KEY_SEND_RESULT,
//								"Approval Task Failed");
//					} else if (flag.equals(Global.FLAG_FOR_REJECTEDTASK)) {
//						extras.putString(Global.BUND_KEY_SEND_RESULT,
//								"Rejection Task Failed");
//					}
//					extras.putString(Global.BUND_KEY_TASK_ID, errMessage);
//					intent.putExtras(extras);
//					activity.startActivity(intent);
//					activity.finish();

                    TaskH taskH = header.getTaskH();
                    taskH.setFlag_survey(flag);
                    taskH.setVerification_notes(notes);
                    taskH.setSubmit_date(Tool.getSystemDateTime());
                    taskH.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);

                    if (errMessage != null && errMessage.equals(STATUS_TASK_NOT_MAPPING)) {
                        taskH.setMessage(activity.getString(com.adins.mss.base.R.string.message_task_not_mapping) + " - Error (" + errMessage + ")");
                    } else if (errMessage != null && errMessage.equals(STATUS_TASK_DELETED)) {
                        taskH.setMessage(activity.getString(com.adins.mss.base.R.string.message_sending_deleted) + " - Error (" + errMessage + ")");
                    } else if (errMessage != null && errMessage.equals(STATUS_IMEI_FAILED)) {
                        taskH.setMessage(activity.getString(com.adins.mss.base.R.string.message_imei_not_registered) + " - Error (" + errMessage + ")");
                    } else {
                        taskH.setMessage(activity.getString(com.adins.mss.base.R.string.message_sending_failed) + " - Error (" + errMessage + ")");
                    }

                    TimelineManager.insertTimeline(activity, taskH);
                    TaskHDataAccess.addOrReplace(activity, taskH);
                } else {
                    if (activity.getString(R.string.no_internet_connection)
                            .equals(result)) {
//						Bundle extras = new Bundle();
//						Intent intent = new Intent(
//								activity.getApplicationContext(),
//								SendResultActivity.class);
//						extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
//						if (flag.equals(Global.FLAG_FOR_APPROVALTASK)) {
//							extras.putString(Global.BUND_KEY_SEND_RESULT,
//									"Approval Task Failed");
//						} else if (flag.equals(Global.FLAG_FOR_REJECTEDTASK)) {
//							extras.putString(Global.BUND_KEY_SEND_RESULT,
//									"Rejection Task Failed");
//						}
//						extras.putString(Global.BUND_KEY_TASK_ID, result);
//						intent.putExtras(extras);
//						activity.startActivity(intent);
//						activity.finish();
                        TaskH taskH = header.getTaskH();
                        taskH.setFlag_survey(flag);
                        taskH.setVerification_notes(notes);
                        taskH.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);
                        taskH.setMessage(activity.getString(com.adins.mss.base.R.string.no_internet_connection));
                        taskH.setSubmit_date(Tool.getSystemDateTime());
                        TimelineManager.insertTimeline(activity, taskH);
                        TaskHDataAccess.addOrReplace(activity, taskH);

                    } else {
                        TaskH taskH = header.getTaskH();
//						Bundle extras = new Bundle();
//						Intent intent = new Intent(
//								activity.getApplicationContext(),
//								SendResultActivity.class);
//						extras.putString(Global.BUND_KEY_TASK_ID,
//								taskH.getTask_id());
//						extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, false);
                        if (flag.equals(Global.FLAG_FOR_APPROVALTASK)) {
//							extras.putString(Global.BUND_KEY_SEND_RESULT,
//									"Task Approved");
                            taskH.setStatus(TaskHDataAccess.STATUS_SEND_SENT);
                            taskH.setIs_prepocessed(Global.FORM_TYPE_APPROVAL);
                        } else if (flag.equals(Global.FLAG_FOR_REJECTEDTASK)) {
//							extras.putString(Global.BUND_KEY_SEND_RESULT,
//									"Task Rejected");
                            taskH.setStatus(TaskHDataAccess.STATUS_SEND_REJECTED);
                        }

//						intent.putExtras(extras);
                        try {
                            if (taskH != null) {
                                taskH.setSubmit_date(Tool.getSystemDateTime());
                                TimelineManager.insertTimeline(activity, taskH,
                                        true, true);
                            }

                            TaskHDataAccess.addOrReplace(activity, taskH);
                        } catch (Exception e) {
                            FireCrash.log(e);
                        }
                    }
                }
            }
        }.execute();
    }

    public static void sendRejectedWithReSurveyTask(final Context context, final SurveyHeaderBean header,
                                                    final String flag, final String uuid_ms_user, final String is_suggested,
                                                    final String notes) {
        new AsyncTask<String, Void, String>() {
            private String errMessage = null;

            @Override
            protected String doInBackground(String... params) {
                String result = null;
                if (Tool.isInternetconnected(context)) {
                    String uuidTaskH = header.getUuid_task_h();
                    JsonRequestRejectedWithResurvey request = new JsonRequestRejectedWithResurvey();
                    request.setUuid_task_h(uuidTaskH);
                    request.setFlag(flag);
                    request.setUuid_user(GlobalData.getSharedGlobalData()
                            .getUser().getUuid_user());
                    request.setUuid_ms_user(params[0]);
                    request.setIs_suggested(params[1]);
                    request.setNotes(params[2]);
                    request.setAudit(GlobalData.getSharedGlobalData()
                            .getAuditData());
                    request.addImeiAndroidIdToUnstructured();

                    String json = GsonHelper.toJson(request);
                    String url = GlobalData.getSharedGlobalData()
                            .getURL_SUBMITVERIFICATIONTASK();
                    if (flag.equals(Global.APPROVAL_FLAG)) {
                        url = GlobalData.getSharedGlobalData()
                                .getURL_SUBMITAPPROVALTASK();
                    }
                    boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                    boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                    HttpCryptedConnection httpConn = new HttpCryptedConnection(context, encrypt, decrypt);
                    HttpConnectionResult serverResult = null;

                    try {
                        serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        errMessage = e.getMessage();
                    }

                    try {
                        result = serverResult.getResult();
                        MssResponseType response = GsonHelper.fromJson(result,
                                MssResponseType.class);
                        if (response.getStatus().getCode() == 0) {
                            result = "success";
                        } else {
                            errMessage = response.getStatus().getMessage();
                        }
                    } catch (Exception e) {
                        FireCrash.log(e);
                        errMessage = e.getMessage();
                    }
                } else {
                    result = context.getString(R.string.no_internet_connection);
                }
                return result;
            }

            @Override
            protected void onPostExecute(String result) {
                if (errMessage != null && errMessage.length() > 0) {
//					Bundle extras = new Bundle();
//					Intent intent = new Intent(getApplicationContext(),
//							SendResultActivity.class);
//					extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
//					extras.putString(Global.BUND_KEY_SEND_RESULT,
//							"Rejection Task Failed");
//					extras.putString(Global.BUND_KEY_TASK_ID, errMessage);
//					intent.putExtras(extras);
//					startActivity(intent);
//					finish();
                    TaskH taskH = header.getTaskH();
                    taskH.setFlag_survey(flag);
                    taskH.setUuid_resurvey_user(uuid_ms_user);
                    taskH.setResurvey_suggested(is_suggested);
                    taskH.setVerification_notes(notes);
                    taskH.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);

                    if (errMessage != null && errMessage.equals(STATUS_TASK_NOT_MAPPING)) {
                        taskH.setMessage(context.getString(com.adins.mss.base.R.string.message_task_not_mapping) + " - Error (" + errMessage + ")");
                    } else if (errMessage != null && errMessage.equals(STATUS_TASK_DELETED)) {
                        taskH.setMessage(context.getString(com.adins.mss.base.R.string.message_sending_deleted) + " - Error (" + errMessage + ")");
                    } else if (errMessage != null && errMessage.equals(STATUS_IMEI_FAILED)) {
                        taskH.setMessage(context.getString(com.adins.mss.base.R.string.message_imei_not_registered) + " - Error (" + errMessage + ")");
                    } else {
                        taskH.setMessage(context.getString(com.adins.mss.base.R.string.message_sending_failed) + " - Error (" + errMessage + ")");
                    }

                    TimelineManager.insertTimeline(context, taskH);
                    TaskHDataAccess.addOrReplace(context, taskH);

                } else {
                    if (context.getString(R.string.no_internet_connection).equals(
                            result)) {
//						Bundle extras = new Bundle();
//						Intent intent = new Intent(getApplicationContext(),
//								SendResultActivity.class);
//						extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
//						extras.putString(Global.BUND_KEY_SEND_RESULT,
//								"Rejection Task Failed");
//
//						extras.putString(Global.BUND_KEY_TASK_ID, result);
//						intent.putExtras(extras);
//						startActivity(intent);
//						finish();
                        TaskH taskH = header.getTaskH();
                        if (null != flag) {
                            taskH.setFlag_survey(flag);
                        }
                        taskH.setUuid_resurvey_user(uuid_ms_user);
                        taskH.setResurvey_suggested(is_suggested);
                        taskH.setVerification_notes(notes);
                        taskH.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);
                        taskH.setMessage(context.getString(com.adins.mss.base.R.string.no_internet_connection));
                        TimelineManager.insertTimeline(context, taskH);
                        TaskHDataAccess.addOrReplace(context, taskH);

                    } else {
                        Global.isVerifiedByUser = true;
                        TaskH taskH = header.getTaskH();
//						Bundle extras = new Bundle();
//						Intent intent = new Intent(getApplicationContext(),
//								SendResultActivity.class);
//						extras.putString(Global.BUND_KEY_TASK_ID,
//								taskH.getTask_id());
//						extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, false);
//						extras.putString(Global.BUND_KEY_SEND_RESULT,
//								"Task Rejected");
                        taskH.setStatus(TaskHDataAccess.STATUS_SEND_REJECTED);
//						intent.putExtras(extras);
                        try {
                            taskH.setSubmit_date(Tool.getSystemDateTime());
                            TimelineManager.insertTimeline(
                                    context,
                                    taskH, true, true);
                            TaskHDataAccess.addOrReplace(
                                    context,
                                    taskH);
                        } catch (Exception e) {
                            FireCrash.log(e);
                            errMessage = e.getMessage();
                        }
                    }
                }
            }
        }.execute(uuid_ms_user, is_suggested, notes);
    }

    @Override
    public boolean saveTask(Context activity, int mode, SurveyHeaderBean header,
                            List<QuestionBean> listOfQuestions, String uuidLastQuestion, boolean isSend) {
        String uuidRvSelected = "";
        String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        Date dtmCrt = new Date(System.currentTimeMillis());
        String taskId = null;
        SharedPreferences sharedPref = activity.getSharedPreferences(
                activity.getString(R.string.task_id), Context.MODE_PRIVATE);
        int saveTaskId = sharedPref.getInt(activity.getString(R.string.save_task_id), 1);
        if (mode == Global.MODE_NEW_SURVEY) {
            taskId = "N~" + saveTaskId;
            SharedPreferences.Editor sharedPrefEditor = sharedPref.edit();
            saveTaskId++;
            sharedPrefEditor.putInt(activity.getString(R.string.save_task_id), saveTaskId);
            sharedPrefEditor.commit();
        } else if (mode == Global.MODE_SURVEY_TASK) {
            taskId = header.getTask_id();
        } else if (mode == Global.MODE_VIEW_SENT_SURVEY) {
            taskId = header.getTask_id();
        }
        int lastSavedQuestion = 1;
        try {
            if (uuidLastQuestion != null || uuidLastQuestion.length() > 0) {
                int i = 1;
                for (QuestionBean bean : Constant.listOfQuestion.values()) {
                    if (uuidLastQuestion.equals(bean.getUuid_question_set())) {
                        lastSavedQuestion = i;
                    }
                    i++;
                }
            }
        } catch (Exception e) {
            FireCrash.log(e);
        }

        oldTaskH = header.getUuid_task_h();

        if (CustomerFragment.isEditable) {
            newTaskH = Tool.getUUID();
            header.setUuid_task_h(newTaskH);
            header.setRv_number(null);
            header.setStatus_rv(null);
        }

        Scheme scheme = SchemeDataAccess.getOne(activity, header.getUuid_scheme());

        TaskH taskH = header.getTaskH();
        taskH.setTask_id(taskId);
        taskH.setDraft_date(dtmCrt);
        taskH.setUser(GlobalData.getSharedGlobalData().getUser());
        taskH.setScheme(scheme);
        taskH.setLast_saved_question(lastSavedQuestion);
//        TaskHDataAccess.addOrReplace(activity, taskH);
        ToDoList.addSurveyToList(new SurveyHeaderBean(taskH), true);

        boolean isAnImage = false;
        for (QuestionBean bean : listOfQuestions) {
            if (bean.getImgAnswer() != null)
                isAnImage = true;
        }

        int i = 1;
//        TaskDDataAccess.delete(activity, taskH.getUuid_task_h());
//        SimpleDateFormat formatter = new SimpleDateFormat("E MMM dd hh:mm:ss yyyy");
        List<TaskD> listTaskD = new ArrayList<TaskD>();
        for (QuestionBean bean : listOfQuestions) {
            if (bean.isVisible()) {
//                if (Tool.isOptions(bean.getAnswer_type())
//                        || Global.AT_TEXT_WITH_SUGGESTION.equals(bean.getAnswer_type())) {
                //2018-07-04 | Nendi - Add Support AT_With_Suggestion from DB
                if (Tool.isOptions(bean.getAnswer_type())
                        || QuestionViewAdapter.IsTextWithSuggestionQuestion(Integer.valueOf(bean.getAnswer_type()))) {
                    try {
                        List<OptionAnswerBean> optAnsBean = bean.getSelectedOptionAnswers();
                        String answer = bean.getAnswer();//OptionAnswerBean.optionAnswerToString(optAnsBean);
                        String[] finalAnswer = new String[1];
                        if (answer != null && answer.length() > 0) {
                            finalAnswer = Tool.split(answer, Global.DELIMETER_DATA);
                        } else {
                            finalAnswer[0] = "";
                        }
                        int j = 0;
                        if (optAnsBean == null || optAnsBean.isEmpty()) {
                            OptionAnswerBean opt = new OptionAnswerBean("", "");
                            optAnsBean.add(opt);
                        }
                        for (OptionAnswerBean selectedOption : optAnsBean) {
                            TaskD taskD = new TaskD(Tool.getUUID());
                            taskD.setQuestion_group_id(bean.getQuestion_group_id());
                            taskD.setQuestion_id(bean.getQuestion_id());

                            taskD.setImage(bean.getImgAnswer());

                            if (isPartial(activity)) {
                                if (isAnImage)
                                    taskD.setIs_final(Global.FALSE_STRING);
                                else {
                                    if (i == listOfQuestions.size()) {
                                        taskD.setIs_final(Global.TRUE_STRING);
                                    } else {
                                        taskD.setIs_final(Global.FALSE_STRING);
                                    }
                                }
                            } else {
                                if (i == listOfQuestions.size()) {
                                    taskD.setIs_final(Global.TRUE_STRING);
                                } else {
                                    taskD.setIs_final(Global.FALSE_STRING);
                                }
                            }
//							taskD.setIs_sent(Global.FALSE_STRING);
                            taskD.setLov(bean.getLovId());
                            taskD.setUsr_crt(uuidUser);
                            taskD.setDtm_crt(dtmCrt);
                            taskD.setUuid_task_h(header.getUuid_task_h());
                            taskD.setQuestion_label(bean.getQuestion_label());
                            taskD.setIs_visible(Global.TRUE_STRING);
                            taskD.setRegex(bean.getRegex());
                            taskD.setIs_readonly(bean.getIs_readonly());
                            taskD.setTaskH(taskH);

                            String lookUpId = selectedOption.getUuid_lookup();
                            String lovCode = selectedOption.getCode();
                            String lovGroup = selectedOption.getLov_group();
//                            if (lookUpId != null && lovCode != null) {
                            //Nendi: Add AT_028

                            boolean isDbSource = false;
                            if (bean.getAnswer_type().equalsIgnoreCase(Global.AT_LOOKUP_TABLE) ||
                                    bean.getAnswer_type().equalsIgnoreCase(Global.AT_TEXT_WITH_SUGGESTION_NEW)) {
                                isDbSource = true;
                            }

                            if (lookUpId != null && lovCode != null && !isDbSource) {
                                if (Global.TAG_RV_NUMBER.equalsIgnoreCase(bean.getLov_group())) {
                                    uuidRvSelected = lookUpId;
                                }
                                Lookup lookup = LookupDataAccess.getOne(activity, lookUpId, lovGroup);
                                OptionAnswerBean selectedOption2 = new OptionAnswerBean(lookup);
                                selectedOption2.setSelected(true);
                                if (bean.getTag() != null && bean.getTag().equalsIgnoreCase("Job MH")) {
                                    taskD.setOption_answer_id(selectedOption2.getCode());
                                    taskD.setTag(bean.getTag());
                                } else {
                                    taskD.setOption_answer_id(selectedOption2.getUuid_lookup());
                                }
                                taskD.setUuid_lookup(selectedOption2.getUuid_lookup());
                            } else { //Nendi: 25/01/2018 | Add exception for AT_028
                                taskD.setText_answer(selectedOption.getValue());
                                taskD.setUuid_lookup(selectedOption.getOption_id());
                            }
                            taskD.setLov(lovCode);

                            if (Tool.isOptionsWithDescription(bean.getAnswer_type())) {
                                taskD.setText_answer(finalAnswer[j]);
                            } else if (Global.AT_MULTIPLE_ONE_DESCRIPTION.equals(bean.getAnswer_type()) ||
                                    Global.AT_DROPDOWN_W_DESCRIPTION.equals(bean.getAnswer_type()) ||
                                    Global.AT_TEXT_WITH_SUGGESTION.equals(bean.getAnswer_type()) ||
                                    Global.AT_TEXT_WITH_SUGGESTION_NEW.equals(bean.getAnswer_type()))
                                taskD.setText_answer(bean.getAnswer());
                            //TaskDDataAccess.addOrReplace(activity, taskD);
                            listTaskD.add(taskD);
                            j++;
                        }
                    } catch (Exception e) {
                        FireCrash.log(e);
                    }
                } else {
                    TaskD taskD = new TaskD(Tool.getUUID());
                    taskD.setQuestion_group_id(bean.getQuestion_group_id());
                    taskD.setQuestion_id(bean.getQuestion_id());
                    taskD.setText_answer(bean.getAnswer());
                    taskD.setImage(bean.getImgAnswer());
                    taskD.setIs_resurvey(bean.getIsResurvey());
                    taskD.setHas_default_image(bean.getHasDefaultImage());
                    if (isPartial(activity)) {
                        if (isAnImage)
                            taskD.setIs_final(Global.FALSE_STRING);
                        else {
                            if (i == listOfQuestions.size()) {
                                taskD.setIs_final(Global.TRUE_STRING);
                            } else {
                                taskD.setIs_final(Global.FALSE_STRING);
                            }
                        }
                    } else {
                        if (i == listOfQuestions.size()) {
                            taskD.setIs_final(Global.TRUE_STRING);
                        } else {
                            taskD.setIs_final(Global.FALSE_STRING);
                        }
                    }
//					taskD.setIs_sent(Global.FALSE_STRING);
                    taskD.setLov(bean.getLovId());
                    taskD.setUsr_crt(uuidUser);
                    taskD.setDtm_crt(dtmCrt);
                    taskD.setUuid_task_h(header.getUuid_task_h());
                    taskD.setQuestion_label(bean.getQuestion_label());
                    if (bean.isVisible()) {
                        taskD.setIs_visible(Global.TRUE_STRING);
                    } else {
                        taskD.setIs_visible(Global.FALSE_STRING);
                    }
                    if (bean.getAnswer_type().equals(Global.AT_GPS) ||
                            bean.getAnswer_type().equals(Global.AT_GPS_N_LBS) ||
                            bean.getAnswer_type().equals(Global.AT_LOCATION) ||
                            bean.getAnswer_type().equals(Global.AT_IMAGE_W_GPS_ONLY) ||
                            bean.getAnswer_type().equals(Global.AT_IMAGE_W_LOCATION) ||
                            bean.getAnswer_type().equals(Global.AT_LOCATION_WITH_ADDRESS)) {
                        try {
                            LocationInfo info = bean.getLocationInfo();
                            taskD.setLatitude(info.getLatitude());
                            taskD.setLongitude(info.getLongitude());
                            taskD.setCid(info.getCid());
                            taskD.setMcc(info.getMcc());
                            taskD.setMnc(info.getMnc());
                            taskD.setLac(info.getLac());
                            taskD.setAccuracy(info.getAccuracy());
                            taskD.setGps_time(info.getGps_time());
                            taskD.setLocation_image(bean.getImgLocation());
                        } catch (Exception e) {
                            FireCrash.log(e);

                        }
                    }
                    taskD.setRegex(bean.getRegex());
                    taskD.setIs_readonly(bean.getIs_readonly());
                    taskD.setTaskH(taskH);
//					TaskDDataAccess.addOrReplace(activity, taskD);
                    listTaskD.add(taskD);
                }
            } else {
                if (!isSend)
                    if (QuestionBean.isHaveAnswer(bean)) {
//                    if (bean.isReadOnly()) {
                        if (Tool.isOptions(bean.getAnswer_type())) {
                            try {
                                List<OptionAnswerBean> optAnsBean = bean.getSelectedOptionAnswers();
                                String answer = bean.getAnswer();//OptionAnswerBean.optionAnswerToString(optAnsBean);
                                String[] finalAnswer;
                                if (answer != null && answer.length() > 0) {
                                    finalAnswer = Tool.split(answer, Global.DELIMETER_DATA);
                                } else {
                                    finalAnswer = new String[0];
                                }
                                int j = 0;
                                for (OptionAnswerBean selectedOption : optAnsBean) {
                                    TaskD taskD = new TaskD(Tool.getUUID());
                                    taskD.setQuestion_group_id(bean.getQuestion_group_id());
                                    taskD.setQuestion_id(bean.getQuestion_id());

                                    taskD.setImage(bean.getImgAnswer());

                                    if (isPartial(activity)) {
                                        if (isAnImage)
                                            taskD.setIs_final(Global.FALSE_STRING);
                                        else {
                                            if (i == listOfQuestions.size()) {
                                                taskD.setIs_final(Global.TRUE_STRING);
                                            } else {
                                                taskD.setIs_final(Global.FALSE_STRING);
                                            }
                                        }
                                    } else {
                                        if (i == listOfQuestions.size()) {
                                            taskD.setIs_final(Global.TRUE_STRING);
                                        } else {
                                            taskD.setIs_final(Global.FALSE_STRING);
                                        }
                                    }
                                    taskD.setLov(bean.getLovId());
                                    taskD.setUsr_crt(uuidUser);
                                    taskD.setDtm_crt(dtmCrt);
                                    taskD.setUuid_task_h(header.getUuid_task_h());
                                    taskD.setQuestion_label(bean.getQuestion_label());
                                    taskD.setIs_visible(Global.TRUE_STRING);
                                    taskD.setRegex(bean.getRegex());
                                    taskD.setIs_readonly(bean.getIs_readonly());
                                    taskD.setTaskH(taskH);

                                    String lookUpId = selectedOption.getUuid_lookup();
                                    String lovCode = selectedOption.getCode();
                                    String lovGroup = selectedOption.getLov_group();
                                    //Nendi: Add AT_028
                                    if (lookUpId != null && lovCode != null && !bean.getAnswer_type().equalsIgnoreCase(Global.AT_LOOKUP_TABLE)) {
                                        if (Global.TAG_RV_NUMBER.equalsIgnoreCase(bean.getLov_group())) {
                                            uuidRvSelected = lookUpId;
                                        }
                                        Lookup lookup = LookupDataAccess.getOne(activity, lookUpId, lovGroup);
                                        OptionAnswerBean selectedOption2 = new OptionAnswerBean(lookup);
                                        selectedOption2.setSelected(true);
                                        if (bean.getTag() != null && bean.getTag().equalsIgnoreCase("Job MH")) {
                                            taskD.setOption_answer_id(selectedOption2.getCode());
                                            taskD.setTag(bean.getTag());
                                        } else {
                                            taskD.setOption_answer_id(selectedOption2.getUuid_lookup());
                                        }
                                        taskD.setUuid_lookup(selectedOption2.getUuid_lookup());
                                    } else { //Nendi: 25/01/2018 | Add exception for AT_028
                                        taskD.setText_answer(selectedOption.getValue());
                                        taskD.setUuid_lookup(selectedOption.getOption_id());
                                    }
                                    taskD.setLov(lovCode);

                                    if (Tool.isOptionsWithDescription(bean.getAnswer_type())) {
                                        taskD.setText_answer(finalAnswer[j]);
                                    } else if (Global.AT_MULTIPLE_ONE_DESCRIPTION.equals(bean.getAnswer_type()) ||
                                            Global.AT_DROPDOWN_W_DESCRIPTION.equals(bean.getAnswer_type()))
                                        taskD.setText_answer(bean.getAnswer());
                                    listTaskD.add(taskD);
                                    j++;
                                }
                            } catch (Exception e) {
                                FireCrash.log(e);
                            }
                        } else {
                            TaskD taskD = new TaskD(Tool.getUUID());
                            taskD.setQuestion_group_id(bean.getQuestion_group_id());
                            taskD.setQuestion_id(bean.getQuestion_id());
                            taskD.setText_answer(bean.getAnswer());
                            taskD.setImage(bean.getImgAnswer());
                            if (isPartial(activity)) {
                                if (isAnImage)
                                    taskD.setIs_final(Global.FALSE_STRING);
                                else {
                                    if (i == listOfQuestions.size()) {
                                        taskD.setIs_final(Global.TRUE_STRING);
                                    } else {
                                        taskD.setIs_final(Global.FALSE_STRING);
                                    }
                                }
                            } else {
                                if (i == listOfQuestions.size()) {
                                    taskD.setIs_final(Global.TRUE_STRING);
                                } else {
                                    taskD.setIs_final(Global.FALSE_STRING);
                                }
                            }
                            taskD.setLov(bean.getLovId());
                            taskD.setUsr_crt(uuidUser);
                            taskD.setDtm_crt(dtmCrt);
                            taskD.setUuid_task_h(header.getUuid_task_h());
                            taskD.setQuestion_label(bean.getQuestion_label());
                            if (bean.isVisible()) {
                                taskD.setIs_visible(Global.TRUE_STRING);
                            } else {
                                taskD.setIs_visible(Global.FALSE_STRING);
                            }
                            if (bean.getAnswer_type().equals(Global.AT_GPS) ||
                                    bean.getAnswer_type().equals(Global.AT_GPS_N_LBS) ||
                                    bean.getAnswer_type().equals(Global.AT_LOCATION) ||
                                    bean.getAnswer_type().equals(Global.AT_IMAGE_W_GPS_ONLY) ||
                                    bean.getAnswer_type().equals(Global.AT_IMAGE_W_LOCATION)) {
                                try {
                                    LocationInfo info = bean.getLocationInfo();
                                    taskD.setLatitude(info.getLatitude());
                                    taskD.setLongitude(info.getLongitude());
                                    taskD.setCid(info.getCid());
                                    taskD.setMcc(info.getMcc());
                                    taskD.setMnc(info.getMnc());
                                    taskD.setLac(info.getLac());
                                    taskD.setAccuracy(info.getAccuracy());
                                    taskD.setGps_time(info.getGps_time());
                                    taskD.setLocation_image(bean.getImgLocation());
                                } catch (Exception e) {
                                    FireCrash.log(e);

                                }
                            }
                            taskD.setRegex(bean.getRegex());
                            taskD.setIs_readonly(bean.getIs_readonly());
                            taskD.setTaskH(taskH);
                            listTaskD.add(taskD);
                        }
//                    }
                    }
            }
            i++;
        }
//        TaskDDataAccess.addOrReplace(activity, listTaskD);
        boolean success = TaskDDataAccess.reInsert(activity, taskH, listTaskD, isSend);
        if (success) {

            try {
                if (Global.APPLICATION_COLLECTION.equalsIgnoreCase(GlobalData.getSharedGlobalData().getApplication())) {
                    boolean isRVinFront = GeneralParameterDataAccess.isRvInFrontEnable(activity, uuidUser);
                    if (isRVinFront && !uuidRvSelected.isEmpty()) {
                        String lastRv = taskH.getRv_number();
                        if (lastRv != null && !lastRv.isEmpty()) {
                            if (!lastRv.equals(uuidRvSelected)) {
                                LookupDataAccess.updateToActive(activity, lastRv);
                                ReceiptVoucherDataAccess.updateToNew(activity, uuidUser, uuidRvSelected);
                            }
                        }
                        Lookup lookup = LookupDataAccess.getOne(activity, uuidRvSelected, Global.TAG_RV_NUMBER);
                        lookup.setIs_active(Global.FALSE_STRING);
                        LookupDataAccess.addOrReplace(activity, lookup);
                        ReceiptVoucherDataAccess.updateToUsed(activity, uuidUser, uuidRvSelected);
                        taskH.setRv_number(lookup.getUuid_lookup());
                        taskH.setStatus_rv(TaskHDataAccess.STATUS_RV_PENDING);
                    }
                }
            } catch (Exception e) {
                FireCrash.log(e);
                ACRA.getErrorReporter().handleSilentException(new Exception("Error saat save di update lookup/RV"));
            }
            TaskHDataAccess.addOrReplace(activity, taskH);
        }
        if (mode == Global.MODE_SURVEY_TASK) {
            if (StatusTabFragment.handler != null)
                StatusTabFragment.handler.sendEmptyMessage(0);
        }
        return success;
    }

    @Override
    public String saveTask(Activity activity, int mode,
                           SurveyHeaderBean header, List<QuestionBean> listOfQuestions) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public String[] doPreSubmitNewSurveyTask(Context context, String taskId,
                                             boolean isPartial) throws Exception {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public void saveAndSendTask(final Activity activity, final String taskId,
                                boolean isPrintable, boolean finishActivity) {
        // TODO Auto-generated method stub
        new AsyncTask<Void, Void, String>() {
            boolean isHaveImage = false;
            private ProgressDialog progressDialog;
            private List<TaskD> listTaskD;
            private TaskH taskH;
            private String errMessage = null;
            private String errCode = null;
            private String sec;
            private String size;
            private String nTaskId;
            private String messageWait = activity.getString(R.string.progressSend);
            private boolean isDropTask;
            private String messageTask;

            @Override
            protected void onPreExecute() {
                progressDialog = ProgressDialog.show(activity, "", this.messageWait, true);
            }

            @Override
            protected String doInBackground(Void... params) {
                // TODO Auto-generated method stub
                //bong 8 apr 15 - penjagaan jika autoSend null dari mainMenuActivity
                String result = null;
                Global.isUploading = true;
                Global.isManualSubmit = true;
                this.taskH = TaskHDataAccess.getOneTaskHeader(activity, taskId);
                try {
                    listTaskD = TaskDDataAccess.getAll(activity, taskH.getUuid_task_h(), TaskDDataAccess.ALL_TASK);
//                        tempListTaskD = TaskDDataAccess.getAll(activity, taskH.getUuid_task_h(), TaskDDataAccess.ALL_TASK);
                } catch (Exception e) {
                    FireCrash.log(e);
                }
                if (Tool.isInternetconnected(activity)) {
                    try {
//                        if (MainMenuActivity.autoSendTask != null) {
//                            MainMenuActivity.autoSendTask.requestWait();
//                            MainMenuActivity.autoSendImage.requestWait();
//                        }
                        if (MainServices.autoSendTaskThread != null) {
                            MainServices.autoSendTaskThread.requestWait();
                            MainServices.autoSendImageThread.requestWait();
                            MainServices.taskListThread.requestWait();
                        }
                    } catch (Exception e) {
                        FireCrash.log(e);
                        // TODO: handle exception
                    }


//					this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_UPLOADING);


//                    List<TaskD> tempListTaskD = null;

                    //minimalisir data
                    String uuidTaskH = taskH.getUuid_task_h();
//                    String uuidScheme = taskH.getUuid_scheme();
                    String uuidUser = taskH.getUuid_user();
                    /*TaskH h = TaskHDataAccess.getOneTaskHeader(activity, taskId);
                    h.setUser(null);
                    h.setScheme(null);
                    h.setUuid_user(uuidUser);
                    h.setUuid_scheme(uuidScheme);*/

                    List<TaskD> taskDs = new ArrayList<TaskD>();
                    int i = 1;

                    try {
                        List<TaskD> taskd = TaskDDataAccess.getUnsentImageByTaskH(activity, uuidUser, uuidTaskH);
                        if (taskd != null && !taskd.isEmpty())
                            isHaveImage = true;
                    } catch (Exception e) {
                        FireCrash.log(e);
                        e.printStackTrace();
                    }
                    if (!isHaveImage) {
                        for (TaskD d : listTaskD) {
                            if (d.getImage() != null) {
                                isHaveImage = true;
                                break;
                            }
                        }
                    }

                    Map<String, QuestionSet> listSubmitLayerQuestion = new HashMap<>();
                    boolean isFinal = false;
                    for (TaskD d : listTaskD) {
                        QuestionSet question = QuestionSetDataAccess.getOne(activity, taskH.getUuid_scheme(), d.getQuestion_id(), d.getQuestion_group_id(), taskH.getForm_version());
                        if (Global.AT_SUBMIT_LAYER.equalsIgnoreCase(question.getAnswer_type())) {
                            listSubmitLayerQuestion.put(question.getQuestion_group_id(), question);
                        }
                        if (Global.TRUE_STRING.equals(d.getIs_visible())) {
                            if (isPartial(activity)) {
                                if (d.getImage() == null) {
                                    taskDs.add(d);
                                }
                            } else {
                                if (i == taskDs.size()) {
                                    d.setIs_final(Global.TRUE_STRING);
                                    isFinal = true;
                                }
                                taskDs.add(d);
                            }
                        }
                        i++;
                    }

                    if (!isPartial(activity)) {
                        TaskD d = taskDs.get(taskDs.size() - 1);
                        if (!d.getIs_final().equals(Global.TRUE_STRING)) {
                            d.setIs_final(Global.TRUE_STRING);
                            isFinal = true;
                        }
                    } else {
                        if (!isHaveImage) {
                            if (!taskDs.isEmpty()) {
                                TaskD d = taskDs.get(taskDs.size() - 1);
                                if (!d.getIs_final().equals(Global.TRUE_STRING)) {
                                    d.setIs_final(Global.TRUE_STRING);
                                    isFinal = true;
                                }
                            }
                        }
                    }
                    //-------------------
                    taskH.setSubmit_date(taskH.getSubmit_date());

                    JsonRequestSubmitTask task = new JsonRequestSubmitTask();
                    task.setAudit(GlobalData.getSharedGlobalData().getAuditData());
                    task.addImeiAndroidIdToUnstructured();
                    task.setTaskH(taskH);
                    task.setTaskD(taskDs);

                    if ( isFinal ) {
                        //2022-04-16 Riska - tambahan untuk submit per layer
                        task = createFiltersForSubmitLayer(activity, task, listSubmitLayerQuestion);
                        //end
                    }

                    if (task.getTaskD() != null && !task.getTaskD().isEmpty()) {
                        String json = GsonHelper.toJson(task);
                        String url = GlobalData.getSharedGlobalData().getURL_SUBMITTASK();

                        if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                            url = GlobalData.getSharedGlobalData().getURL_SUBMITVERIFICATIONTASK();
                        } else {
                            url = GlobalData.getSharedGlobalData().getURL_SUBMITTASK();
                        }

                        size = String.valueOf(json.getBytes().length);

                        boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                        boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                        HttpCryptedConnection httpConn = new HttpCryptedConnection(activity, encrypt, decrypt);
                        HttpConnectionResult serverResult = null;
                        Date startTime = Tool.getSystemDateTime();

                        try {
                            serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
                        } catch (Exception e) {
                            FireCrash.log(e);
                            e.printStackTrace();
                            try {
                                progressDialog.dismiss();
                            } catch (Exception e1) {
                                e1.printStackTrace();
                            }
                            errMessage = e.getMessage();
                        }
                        if (serverResult.isOK()) {
                            String resultvalue = serverResult.getResult();
                            JsonResponseSubmitTask responseSubmitTask = GsonHelper.fromJson(resultvalue, JsonResponseSubmitTask.class);
                            if (responseSubmitTask.getStatus().getCode() == 0) {
                                String status = responseSubmitTask.getResult();
                                Date finishTime = Tool.getSystemDateTime();
                                long time = finishTime.getTime() - startTime.getTime();
                                sec = "0";
                                try {
                                    sec = String.valueOf((int) Math.ceil(time / 1000));
                                } catch (Exception e) {
                                    FireCrash.log(e);
                                }
                                if (status == null)
                                    status = "Success";
                                if (status.equalsIgnoreCase("Success")) {
                                    result = status;
                                    if (responseSubmitTask.getTaskId() != null) {
                                        nTaskId = responseSubmitTask.getTaskId();
                                        isDropTask = responseSubmitTask.isDropTask;
                                        messageTask = responseSubmitTask.getMessage();
                                        String application = GlobalData.getSharedGlobalData().getAuditData().getApplication();
                                        if (application == null)
                                            application = GlobalData.getSharedGlobalData().getApplication();
                                        if (Global.APPLICATION_COLLECTION.equalsIgnoreCase(application)) {
                                            if (responseSubmitTask.getCashOnHand() != null) {
                                                GlobalData.getSharedGlobalData().getUser().setCash_on_hand(
                                                        responseSubmitTask.getCashOnHand());
                                                UserDataAccess.addOrReplace(activity, GlobalData.getSharedGlobalData().getUser());
                                            }
                                        }
                                    } else
                                        nTaskId = activity.getString(R.string.message_no_task_id_from_server);
                                } else {
                                    result = status;
                                }

                                if (taskH.getScheme().getIs_printable().equals(Global.TRUE_STRING))
                                    generatePrintResult(activity, taskH);
                            } else {
                                errMessage = responseSubmitTask.getStatus().getMessage();
                                if (errMessage == null)
                                    errMessage = "Status Code:" + String.valueOf(responseSubmitTask.getStatus().getCode());
                                errCode = String.valueOf(responseSubmitTask.getStatus().getCode());
                            }
                        } else {
                            try {
                                result = serverResult.getResult();
                            } catch (Exception e) {
                                FireCrash.log(e);
                            }
                        }
                    } else {
                        errCode = STATUS_TASKD_MISSING;
                        result = activity.getString(R.string.taskd_was_gone, taskH.getCustomer_name());
                        errMessage = activity.getString(R.string.taskd_was_gone, taskH.getCustomer_name());
                        Global.isManualSubmit = false;
                    }

                } else {
                    if (listTaskD == null || listTaskD.isEmpty()) {
                        errCode = STATUS_TASKD_MISSING;
                        result = activity.getString(R.string.taskd_was_gone, taskH.getCustomer_name());
                        errMessage = activity.getString(R.string.taskd_was_gone, taskH.getCustomer_name());
                        Global.isManualSubmit = false;
                        return result;
                    }
                    result = activity.getString(R.string.no_internet_connection);
                    errMessage = activity.getString(R.string.no_internet_connection);
                    Global.isManualSubmit = false;
                }


                Global.isUploading = false;

                return result;
            }

            @Override
            protected void onPostExecute(String result) {
                if (progressDialog.isShowing()) {
                    try {
                        progressDialog.dismiss();
                    } catch (Exception e) {
                        FireCrash.log(e);
                    }
                }

                if (errMessage != null) {
                    if (errCode != null && errCode.equals(STATUS_TASK_NOT_MAPPING)) {
                        this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SAVEDRAFT);
                        ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_SAVEDRAFT, 0);
                        TaskHDataAccess.addOrReplace(activity, taskH);
                        if (StatusTabFragment.handler != null)
                            StatusTabFragment.handler.sendEmptyMessage(0);
                        String message = "";
                        if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                            message = activity.getString(R.string.message_verification_failed);
                        } else {
                            message = activity.getString(R.string.message_sending_failed);
                        }
                        NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(activity);
                        builder.withTitle(activity.getString(R.string.warning_capital))
                                .withMessage(message + errMessage)
                                .show();
                    } else if (errCode != null && errCode.equals(STATUS_TASK_DELETED)) {
                        if (StatusTabFragment.handler != null)
                            StatusTabFragment.handler.sendEmptyMessage(0);
                        String message = "";
                        if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                            message = activity.getString(R.string.message_verification_deleted);
                        } else {
                            message = activity.getString(R.string.message_sending_deleted);
                        }
                        NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(activity);
                        builder.withTitle(activity.getString(R.string.warning_capital))
                                .withMessage(message + errMessage)
                                .show();
                        TaskHDataAccess.deleteWithRelation(activity, this.taskH);
                    } else if (errCode != null && errCode.equals(STATUS_IMEI_FAILED)) {
                        this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SAVEDRAFT);
                        ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_SAVEDRAFT, 0);
                        TaskHDataAccess.addOrReplace(activity, taskH);
                        if (StatusTabFragment.handler != null)
                            StatusTabFragment.handler.sendEmptyMessage(0);
                        String message = "";
                        if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                            message = activity.getString(R.string.message_verification_failed);
                        } else {
                            message = activity.getString(R.string.message_sending_failed);
                        }
                        NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(activity);
                        builder.withTitle(activity.getString(R.string.warning_capital))
                                .withMessage(message + errMessage)
                                .show();
                    } else if (errCode != null && errCode.equals(STATUS_TASKD_MISSING)) {
                        try {
                            this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SAVEDRAFT);
                            TaskHDataAccess.addOrReplace(activity, this.taskH);
                        } catch (Exception e) {
                            FireCrash.log(e);
                        }
                        NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(activity);
                        builder.withTitle(activity.getString(R.string.warning_capital))
                                .withMessage(activity.getString(R.string.message_sending_failed) + "\n" + errMessage)
                                .show();
                    } else {
                        try {
                            int imageLeft = AutoSendImageThread.countPendingImageBeforeUpload(activity, this.taskH.getUuid_task_h());
                            ToDoList.updateStatusSurvey(taskId, this.taskH.getStatus(), imageLeft);
                        } catch (Exception e) {
                            FireCrash.log(e);

                        }
                        TaskHDataAccess.addOrReplace(activity, this.taskH);
                        NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(activity);
                        builder.withTitle(activity.getString(R.string.error_capital))
                                .withMessage(activity.getString(R.string.message_sending_failed) + "\n" + errMessage)
                                .show();
                    }
                } else {
                    if (result != null) {
                        if (result.equalsIgnoreCase("Success")) {
                            //List<TaskD> newlistTaskD = new ArrayList<TaskD>();
                            for (TaskD taskD : listTaskD) {
                                if (taskD.getImage() == null)
                                    taskD.setIs_sent(Global.TRUE_STRING);
                                else {
                                    if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                        taskD.setIs_sent(Global.TRUE_STRING);
                                        // newlistTaskD.add(taskD);
                                    } else {
                                        if (isPartial(activity)) {
                                            taskD.setIs_sent(Global.FALSE_STRING);
                                        } else {
                                            taskD.setIs_sent(Global.TRUE_STRING);
                                        }
                                    }
                                }
//                                TaskDDataAccess.addOrReplace(activity, taskD);
                            }
                            TaskDDataAccess.addOrReplace(activity, listTaskD);

                            if (isDropTask) {
                                taskH.setMessage(messageTask);
                                taskH.setStatus(TaskHDataAccess.STATUS_SEND_DROP);
                                TaskHDataAccess.update(activity,taskH);
                                TimelineManager.insertTimeline(activity, taskH);
                            } else {
                                int imageLeft = AutoSendImageThread.countPendingImageBeforeUpload(activity, this.taskH.getUuid_task_h());
                                if (imageLeft > 0) {
                                    this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_UPLOADING);
                                    ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_UPLOADING, imageLeft);
                                } else {
                                    try {
                                        List<TaskD> taskd = TaskDDataAccess.getUnsentImageByTaskH(activity, this.taskH.getUuid_user(), this.taskH.getUuid_task_h());
                                        if (taskd != null && !taskd.isEmpty()) {
                                            this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_UPLOADING);
                                            ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_UPLOADING, imageLeft);
                                        } else {
                                            this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SENT);
                                            ToDoList.removeSurveyFromList(taskId);
                                        }
                                    } catch (Exception e) {
                                        FireCrash.log(e);
                                        if (isHaveImage && isPartial(activity)) {
                                            this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_UPLOADING);
                                            ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_UPLOADING, imageLeft);
                                        } else {
                                            this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SENT);
                                            ToDoList.removeSurveyFromList(taskId);
                                        }
                                        ACRA.getErrorReporter().putCustomData("errorSubmit", e.getMessage());
                                        ACRA.getErrorReporter().handleSilentException(new Exception("Just for the stacktrace, kena exception saat submit"));
                                    }
                                }
                            }

                            if (nTaskId.contains(activity.getString(R.string.message_task_not_mapping))) {
                                this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SAVEDRAFT);
                                ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_SAVEDRAFT, 0);
                                TaskHDataAccess.addOrReplace(activity, taskH);
                                if (StatusTabFragment.handler != null)
                                    StatusTabFragment.handler.sendEmptyMessage(0);
                                String message = "";
                                if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                    message = activity.getString(R.string.message_verification_failed);
                                } else {
                                    message = activity.getString(R.string.message_sending_failed);
                                }
                                NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(activity);
                                builder.withTitle(activity.getString(R.string.warning_capital))
                                        .withMessage(message + nTaskId)
                                        .show();
                            } else if (nTaskId.contains(activity.getString(R.string.message_no_task_id_from_server))) {
                                this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SAVEDRAFT);
                                ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_SAVEDRAFT, 0);
                                TaskHDataAccess.addOrReplace(activity, taskH);
                                if (StatusTabFragment.handler != null)
                                    StatusTabFragment.handler.sendEmptyMessage(0);
                                String message = "";
                                if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                    message = activity.getString(R.string.message_verification_failed);
                                } else {
                                    message = activity.getString(R.string.message_sending_failed);
                                }
                                NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(activity);
                                builder.withTitle(activity.getString(R.string.warning_capital))
                                        .withMessage(message + nTaskId)
                                        .show();
                            } else if (nTaskId.contains("been deleted")) {
                                if (StatusTabFragment.handler != null)
                                    StatusTabFragment.handler.sendEmptyMessage(0);
                                String message = "";
                                if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                    message = activity.getString(R.string.message_verification_deleted);
                                } else {
                                    message = activity.getString(R.string.message_sending_deleted);
                                }
                                NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(activity);
                                builder.withTitle(activity.getString(R.string.warning_capital))
                                        .withMessage(message + nTaskId)
                                        .show();
                                TaskHDataAccess.deleteWithRelation(activity, this.taskH);
                            } else {
                                this.taskH.setSubmit_date(taskH.getSubmit_date());
                                this.taskH.setSubmit_duration(sec);
                                this.taskH.setSubmit_size(size);
                                this.taskH.setSubmit_result(result);
                                this.taskH.setTask_id(nTaskId);
                                this.taskH.setLast_saved_question(1);
                                TaskHDataAccess.addOrReplace(activity, this.taskH);
                                if (taskH.getStatus().equals(TaskHDataAccess.STATUS_SEND_SENT)) {
                                    String message = "";
                                    if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                        message = activity.getString(R.string.message_verification_success);
                                        if (MainMenuActivity.mnSVYApproval != null) {
                                            TaskHDataAccess.deleteWithRelation(activity, this.taskH);
                                            String newUUIDTaskH = Tool.getUUID();
                                            this.taskH.setUuid_task_h(newUUIDTaskH);
                                            TaskHDataAccess.addOrReplace(activity, this.taskH);
                                            for (TaskD d : listTaskD) {
                                                d.setTaskH(taskH);
                                                d.setUuid_task_d(Tool.getUUID());
//                                                TaskDDataAccess.addOrReplace(activity, d);
                                            }
                                            TaskDDataAccess.addOrReplace(activity, listTaskD);
                                        }
                                    } else {
                                        message = activity.getString(R.string.message_sending_success);
                                    }
                                    if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                        TimelineManager.insertTimeline(activity, this.taskH, true, false);
                                    } else {
                                        TimelineManager.insertTimeline(activity, this.taskH);
                                        boolean isRVinFront = GeneralParameterDataAccess.isRvInFrontEnable(activity, GlobalData.getSharedGlobalData().getUser().getUuid_user());
                                        if (isRVinFront && Global.APPLICATION_COLLECTION.equalsIgnoreCase(GlobalData.getSharedGlobalData().getApplication())) {
                                            taskH.setStatus_rv(TaskHDataAccess.STATUS_RV_SENT);
                                            TaskHDataAccess.addOrReplace(activity, this.taskH);
                                        }
                                    }
                                    NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(activity);
                                    builder.withTitle("Success")
                                            .withMessage(message)
                                            .show();
                                }
                            }
                        } else if (activity.getString(R.string.no_internet_connection).equals(result)) {
                            NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(activity);
                            builder.withTitle(activity.getString(R.string.info_capital))
                                    .withMessage(activity.getString(R.string.no_internet_connection))
                                    .show();
                        }
                    }
                }
                //bong 8 apr 15 - penjagaan jika autoSend null dari mainMenuActivity
                try {
                    Handler handler = new Handler(Looper.getMainLooper());
                    handler.post(new Runnable() {
                        public void run() {
                            // UI code goes here
                            try {
                                /*if (MainMenuActivity.mnTaskList != null)
                                    MainMenuActivity.mnTaskList.setCounter(String
                                            .valueOf(ToDoList
                                                    .getCounterTaskList(activity)));
                                if (MainMenuActivity.mnLog != null)
                                    MainMenuActivity.mnLog
                                            .setCounter(String.valueOf(TaskLog
                                                    .getCounterLog(activity)));
                                if (MainMenuActivity.menuAdapter != null)
                                    MainMenuActivity.menuAdapter.notifyDataSetChanged();*/
                                try {
                                    MainMenuActivity.setDrawerCounter();
                                } catch (Exception e) {
                                    FireCrash.log(e);
                                    // TODO: handle exception
                                }
                                if (StatusTabFragment.handler != null)
                                    StatusTabFragment.handler.sendEmptyMessage(0);
                            } catch (Exception e) {
                                FireCrash.log(e);
                                // TODO: handle exception
                            }
                        }
                    });
//                    if (MainMenuActivity.autoSendTask != null) {
//                        MainMenuActivity.autoSendTask.stopWaiting();
//                        MainMenuActivity.autoSendImage.stopWaiting();
//                    }
                    if (MainServices.autoSendTaskThread != null) {
                        MainServices.autoSendTaskThread.stopWaiting();
                        MainServices.autoSendImageThread.stopWaiting();
                        MainServices.taskListThread.stopWaiting();
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    // TODO: handle exception
                }
                Global.isManualSubmit = false;

            }

        }.execute();
    }

    @SuppressLint("StaticFieldLeak")
    @Override
    public void saveAndSendTask(final Activity activity, final int mode,
                                final SurveyHeaderBean header, final List<QuestionBean> listOfQuestions) {

        new AsyncTask<Void, Void, String>() {
            protected ProgressDialog progressDialog;
            protected List<TaskD> listTaskD;
            protected TaskH taskH;
            protected String errMessage = null;
            protected String errCode = null;
            protected String sec;
            protected String size;
            String taskId = null;
            boolean isHaveImage = false;
            private String messageWait = activity.getString(R.string.progressSend);
            private boolean isDropTask;
            private String messageTask;

            @Override
            protected void onPreExecute() {
                progressDialog = ProgressDialog.show(activity, "", this.messageWait, true);
            }

            @Override
            protected String doInBackground(Void... arg0) {
                // TODO Auto-generated method stub
                //bong 8 apr 15 - penjagaan jika autoSend null dari mainMenuActivity
                String result = null;
                Global.isManualSubmit = true;
                Global.isUploading = true;
                try {
//                    if (MainMenuActivity.autoSendTask != null) {
//                        MainMenuActivity.autoSendTask.requestWait();
//                        MainMenuActivity.autoSendImage.requestWait();
//                    }
                    if (MainServices.autoSendTaskThread != null) {
                        MainServices.autoSendTaskThread.requestWait();
                        MainServices.autoSendImageThread.requestWait();
                        MainServices.taskListThread.requestWait();
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    // TODO: handle exception
                }
                boolean saved = saveTask(activity, mode, header, listOfQuestions, null, true);
                if (saved) {
                    this.taskH = header.getTaskH();

                    //kamil 27/07/2017 permintaan mas raymond tambah lokasi untuk kirim taskH ke server
                    LocationInfo loc = Global.LTM.getCurrentLocation(Global.FLAG_LOCATION_TRACKING);
                    if ("null".equalsIgnoreCase(loc.getLatitude())) {
                        this.taskH.setLatitude("");
                    } else {
                        this.taskH.setLatitude(loc.getLatitude());
                    }

                    if ("null".equalsIgnoreCase(loc.getLongitude())) {
                        this.taskH.setLongitude("");
                    } else {
                        this.taskH.setLongitude(loc.getLongitude());
                    }
                    //2018-08-30 force update taskH setelah set latitude longitude
                    TaskHDataAccess.addOrReplace(activity, this.taskH);

                    try {
                        if (CustomerFragment.isEditable) {
                            try {
                                listTaskD = TaskDDataAccess.getAll(activity, header.getUuid_task_h(), TaskDDataAccess.ALL_TASK);
//                                header.setUuid_task_h(newTaskH);
                                header.setAssignment_date(null);
                                TaskHDataAccess.addOrReplace(activity, header);
//                                for (TaskD taskDetail : listTaskD) {
//                                    taskDetail.setUuid_task_d(Tool.getUUID());
//                                    taskDetail.setUuid_task_h(header.getUuid_task_h());
//                                }
//                                TaskDDataAccess.add(activity, listTaskD);
                                CustomerFragment.isEditable = false;

                            } catch (Exception e) {
                                FireCrash.log(e);
                            }
                        } else {
                            listTaskD = TaskDDataAccess.getAll(activity, header.getUuid_task_h(), TaskDDataAccess.ALL_TASK);
                        }
                    } catch (Exception e) {
                        FireCrash.log(e);
                    }
                    if (Tool.isInternetconnected(activity)) {
                        taskId = this.taskH.getTask_id();


                        //minimalisir data
                        String uuidTaskH = taskH.getUuid_task_h();
                        String uuidUser = taskH.getUuid_user();

                        List<TaskD> taskDs = new ArrayList<TaskD>();
                        int i = 1;

                        try {
                            List<TaskD> taskd = TaskDDataAccess.getUnsentImageByTaskH(activity, uuidUser, uuidTaskH);
                            if (taskd != null && !taskd.isEmpty())
                                isHaveImage = true;
                        } catch (Exception e) {
                            FireCrash.log(e);
                            e.printStackTrace();
                        }
                        if (!isHaveImage) {
                            for (TaskD d : listTaskD) {
                                if (d.getImage() != null) {
                                    isHaveImage = true;
                                    break;
                                }
                            }
                        }
                        Map<String, QuestionSet> listSubmitLayerQuestion = new HashMap<>();
                        boolean isFinal = false;
                        for (TaskD d : listTaskD) {
                            QuestionSet question = QuestionSetDataAccess.getOne(activity, taskH.getUuid_scheme(), d.getQuestion_id(), d.getQuestion_group_id(), taskH.getForm_version());
                            if (Global.AT_SUBMIT_LAYER.equalsIgnoreCase(question.getAnswer_type())) {
                                listSubmitLayerQuestion.put(question.getQuestion_group_id(), question);
                            }
                            if (d.getIs_visible().equals(Global.TRUE_STRING)) {
                                if (isPartial(activity)) {
                                    if (d.getImage() == null) {
                                        taskDs.add(d);
                                    }
                                } else {
                                    if (i == taskDs.size()) {
                                        d.setIs_final(Global.TRUE_STRING);
                                        isFinal = true;
                                    }
                                    taskDs.add(d);
                                }
                            }
                            i++;
                        }
                        if (!isPartial(activity)) {
                            TaskD d = taskDs.get(taskDs.size() - 1);
                            if (!d.getIs_final().equals(Global.TRUE_STRING)) {
                                d.setIs_final(Global.TRUE_STRING);
                                isFinal = true;
                            }
                        } else {
                            if (!isHaveImage) {
                                if (!taskDs.isEmpty()) {
                                    TaskD d = taskDs.get(taskDs.size() - 1);
                                    if (!d.getIs_final().equals(Global.TRUE_STRING)) {
                                        d.setIs_final(Global.TRUE_STRING);
                                        isFinal = true;
                                    }
                                }
                            }
                        }
                        this.taskH.setSubmit_date(taskH.getSubmit_date());
                        //-------------------

                        JsonRequestSubmitTask task = new JsonRequestSubmitTask();
                        task.setAudit(GlobalData.getSharedGlobalData().getAuditData());
                        task.addImeiAndroidIdToUnstructured();
                        task.setTaskH(taskH);
                        task.setTaskD(taskDs);

                        if ( isFinal ) {
                            //2022-04-16 Riska - tambahan untuk submit per layer
                            task = createFiltersForSubmitLayer(activity, task, listSubmitLayerQuestion);
                            //end
                        }

                        if (task.getTaskD() != null && !task.getTaskD().isEmpty()) {
                            String json = GsonHelper.toJson(task);
                            String url = GlobalData.getSharedGlobalData().getURL_SUBMITTASK();

                            if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                url = GlobalData.getSharedGlobalData().getURL_SUBMITVERIFICATIONTASK();
                            } else {
                                url = GlobalData.getSharedGlobalData().getURL_SUBMITTASK();
                            }

                            size = String.valueOf(json.getBytes().length);

                            boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                            boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                            HttpCryptedConnection httpConn = new HttpCryptedConnection(activity, encrypt, decrypt);
                            HttpConnectionResult serverResult = null;
                            Date startTime = Tool.getSystemDateTime();

                            try {
                                if (GlobalData.getSharedGlobalData().getURL_SUBMITVERIFICATIONTASK().equals(url)) {
                                    if (Global.IS_DEV)
                                        Logger.i("INFO", "VERIFY VERIFICATION TASK");
                                } else {
                                    if (Global.IS_DEV)
                                        Logger.i("INFO", "SUBMIT TASK");
                                }
                                serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
                            } catch (Exception e) {
                                FireCrash.log(e);
                                e.printStackTrace();
                                try {
                                    progressDialog.dismiss();
                                } catch (Exception e1) {
                                    e1.printStackTrace();
                                }
                                errMessage = e.getMessage();
                            }
                            if (serverResult.isOK()) {
                                String resultvalue = serverResult.getResult();
                                JsonResponseSubmitTask responseSubmitTask = GsonHelper.fromJson(resultvalue, JsonResponseSubmitTask.class);
                                if (responseSubmitTask.getStatus().getCode() == 0) {
                                    String status = responseSubmitTask.getResult();
                                    Date finishTime = Tool.getSystemDateTime();
                                    long time = finishTime.getTime() - startTime.getTime();
                                    sec = String.valueOf((int) Math.ceil(time / 1000));
                                    isDropTask = responseSubmitTask.isDropTask();
                                    messageTask = responseSubmitTask.getMessage();
                                    if (status == null)
                                        status = "Success";
                                    if (status.equalsIgnoreCase("Success")) {
                                        result = status;
                                        if (responseSubmitTask.getTaskId() != null) {
                                            taskId = responseSubmitTask.getTaskId();
                                            String application = GlobalData.getSharedGlobalData().getAuditData().getApplication();
                                            if (application == null)
                                                application = GlobalData.getSharedGlobalData().getApplication();
                                            if (Global.APPLICATION_COLLECTION.equalsIgnoreCase(application)) {
                                                if (responseSubmitTask.getCashOnHand() != null) {
                                                    GlobalData.getSharedGlobalData().getUser().setCash_on_hand(
                                                            responseSubmitTask.getCashOnHand()
                                                    );
                                                    UserDataAccess.addOrReplace(activity, GlobalData.getSharedGlobalData().getUser());
                                                }
                                            }
                                        } else
                                            taskId = activity.getString(R.string.message_no_task_id_from_server);
                                    } else {
                                        result = status;
                                    }


                                    // bong 8 may 15 - call generate print result
                                    //TODO generate printResult
                                    if (taskH.getScheme().getIs_printable() != null && taskH.getScheme().getIs_printable().equals(Global.TRUE_STRING))
                                        generatePrintResult(activity, taskH);

                                } else {
                                    result = String.valueOf(responseSubmitTask.getStatus().getCode());
                                    errMessage = responseSubmitTask.getStatus().getMessage();
                                    errCode = String.valueOf(responseSubmitTask.getStatus().getCode());
                                }
                            } else {
                                try {
                                    result = serverResult.getResult();
                                } catch (Exception e) {
                                    FireCrash.log(e);
                                }
                            }
                        } else {
                            result = activity.getString(R.string.request_error);
                            errMessage = activity.getString(R.string.request_error);
                            errCode = STATUS_TASKD_MISSING;
                            Global.isManualSubmit = false;
                        }

                    } else {
                        if (listTaskD == null || listTaskD.isEmpty()) {
                            result = activity.getString(R.string.request_error);
                            errMessage = activity.getString(R.string.request_error);
                            errCode = STATUS_TASKD_MISSING;
                            Global.isManualSubmit = false;
                            return result;
                        }

                        result = activity.getString(R.string.no_internet_connection);
                        Global.isManualSubmit = false;
                    }
                } else {
                    result = activity.getString(R.string.request_error);
                    errMessage = activity.getString(R.string.request_error);
                    errCode = STATUS_SAVE_FAILED;
                    Global.isManualSubmit = false;
                    return result;
                }

                Global.isUploading = false;
                return result;
            }

            @Override
            protected void onPostExecute(String result) {
                if (progressDialog.isShowing()) {
                    try {
                        progressDialog.dismiss();
                    } catch (Exception e) {
                        FireCrash.log(e);
                    }
                }

                if (errMessage != null) {
                    if (errCode != null && errCode.equals(STATUS_TASKD_MISSING)) {
                        try {
                            this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SAVEDRAFT);
                            TaskHDataAccess.addOrReplace(activity, this.taskH);
                        } catch (Exception e) {
                            FireCrash.log(e);
                        }
                        NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(activity);
                        builder.withTitle(activity.getString(R.string.warning_capital))
                                .withMessage(activity.getString(R.string.message_sending_failed) + "\n" + errMessage)
                                .show();
                    } else if (errCode != null && errCode.equals(STATUS_SAVE_FAILED)) {
                        TaskHDataAccess.addOrReplace(activity, taskH);
                        NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(activity);
                        builder.withTitle(activity.getString(R.string.warning_capital))
                                .withMessage(activity.getString(R.string.message_sending_failed) + "\n" + errMessage)
                                .show();
                    } else {
                        Bundle extras = new Bundle();
                        Intent intent = new Intent(activity.getApplicationContext(), SendResultActivity.class);
                        if (errCode != null && errCode.equals(STATUS_TASK_NOT_MAPPING)) {
                            this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SAVEDRAFT);
                            ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_SAVEDRAFT, 0);
                            TaskHDataAccess.addOrReplace(activity, taskH);
                            if (StatusTabFragment.handler != null)
                                StatusTabFragment.handler.sendEmptyMessage(0);
                            extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
                            if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_verification_failed));
                            } else {
                                extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_sending_failed));
                            }
                            extras.putString(Global.BUND_KEY_SEND_SIZE, this.size);
                            extras.putString(Global.BUND_KEY_SEND_TIME, this.sec);
                            extras.putString(Global.BUND_KEY_TASK_ID, this.errMessage);
                            extras.putBoolean(Global.BUND_KEY_TASK_IS_PRINTABLE, false);
                        } else if (errCode != null && errCode.equals(STATUS_TASK_DELETED)) {
                            if (StatusTabFragment.handler != null)
                                StatusTabFragment.handler.sendEmptyMessage(0);
                            extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
                            if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_verification_deleted));
                            } else {
                                extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_sending_deleted));
                            }
                            extras.putString(Global.BUND_KEY_SEND_SIZE, this.size);
                            extras.putString(Global.BUND_KEY_SEND_TIME, this.sec);
                            extras.putString(Global.BUND_KEY_TASK_ID, this.errMessage);
                            extras.putBoolean(Global.BUND_KEY_TASK_IS_PRINTABLE, false);
                            TaskHDataAccess.deleteWithRelation(activity, this.taskH);
                        } else if (errCode != null && errCode.equals(STATUS_IMEI_FAILED)) {
                            this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SAVEDRAFT);
                            ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_SAVEDRAFT, 0);
                            TaskHDataAccess.addOrReplace(activity, taskH);
                            if (StatusTabFragment.handler != null)
                                StatusTabFragment.handler.sendEmptyMessage(0);
                            extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
                            if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_verification_failed));
                            } else {
                                extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_sending_failed));
                            }
                            extras.putString(Global.BUND_KEY_SEND_SIZE, this.size);
                            extras.putString(Global.BUND_KEY_SEND_TIME, this.sec);
                            extras.putString(Global.BUND_KEY_TASK_ID, this.errMessage);
                            extras.putBoolean(Global.BUND_KEY_TASK_IS_PRINTABLE, false);
                        } else {
                            try {
                                int imageLeft = AutoSendImageThread.countPendingImageBeforeUpload(activity, this.taskH.getUuid_task_h());
                                ToDoList.updateStatusSurvey(taskId, this.taskH.getStatus(), imageLeft);
                            } catch (Exception e) {
                                FireCrash.log(e);

                            }
                            TaskHDataAccess.addOrReplace(activity, this.taskH);
                            extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
                            if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_verification_failed));
                            } else {
                                extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_sending_failed));
                            }
                            extras.putString(Global.BUND_KEY_SEND_SIZE, this.size);
                            extras.putString(Global.BUND_KEY_SEND_TIME, this.sec);
                            extras.putString(Global.BUND_KEY_TASK_ID, errMessage);
                        }
                        intent.putExtras(extras);
                        activity.startActivity(intent);
                        activity.finish();

                    }
                } else {
                    if (result != null) {
                        if (result.equalsIgnoreCase("Success")) {
                            Bundle extras = new Bundle();
                            Intent intent = new Intent(activity.getApplicationContext(), SendResultActivity.class);
                            //List<TaskD> newlistTaskD = new ArrayList<TaskD>();
                            for (TaskD taskD : listTaskD) {
                                if (taskD.getImage() == null)
                                    taskD.setIs_sent(Global.TRUE_STRING);
                                else {
                                    if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                        taskD.setIs_sent(Global.TRUE_STRING);
                                        //newlistTaskD.add(taskD);
                                    } else {
                                        if (isPartial(activity)) {
                                            taskD.setIs_sent(Global.FALSE_STRING);
                                        } else {
                                            taskD.setIs_sent(Global.TRUE_STRING);
                                        }
                                    }
                                }
                                //TaskDDataAccess.addOrReplace(activity, taskD);
                            }
                            TaskDDataAccess.addOrReplace(activity, listTaskD);

                            if (isDropTask) {
                                taskH.setMessage(messageTask);
                                taskH.setStatus(TaskHDataAccess.STATUS_SEND_DROP);
                                TaskHDataAccess.update(activity,taskH);
                                TimelineManager.insertTimeline(activity, taskH);
                            } else {
                                int imageLeft = AutoSendImageThread.countPendingImageBeforeUpload(activity, this.taskH.getUuid_task_h());
                                if (imageLeft > 0) {
                                    this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_UPLOADING);
                                    ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_UPLOADING, imageLeft);
                                } else {
                                    try {
                                        List<TaskD> taskd = TaskDDataAccess.getUnsentImageByTaskH(activity, this.taskH.getUuid_user(), this.taskH.getUuid_task_h());
                                        if (taskd != null && !taskd.isEmpty()) {
                                            this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_UPLOADING);
                                            ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_UPLOADING, imageLeft);
                                        } else {
                                            this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SENT);
                                            ToDoList.removeSurveyFromList(taskId);
                                        }
                                    } catch (Exception e) {
                                        FireCrash.log(e);
                                        if (isHaveImage && isPartial(activity)) {
                                            this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_UPLOADING);
                                            ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_UPLOADING, imageLeft);
                                        } else {
                                            this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SENT);
                                            ToDoList.removeSurveyFromList(taskId);
                                        }
                                        ACRA.getErrorReporter().putCustomData("errorSubmit", e.getMessage());
                                        ACRA.getErrorReporter().handleSilentException(new Exception("Just for the stacktrace, kena exception saat submit"));
                                    }
                                }
                            }

                            if (taskId.contains(activity.getString(R.string.message_task_not_mapping))) {
//								TaskHDataAccess.deleteWithRelation(activity, this.taskH);
                                this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SAVEDRAFT);
                                ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_SAVEDRAFT, 0);
                                TaskHDataAccess.addOrReplace(activity, taskH);
                                if (StatusTabFragment.handler != null)
                                    StatusTabFragment.handler.sendEmptyMessage(0);
                                extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
                                if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                    extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_verification_failed));
                                } else {
                                    extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_sending_failed));
                                }
                                extras.putString(Global.BUND_KEY_SEND_SIZE, this.size);
                                extras.putString(Global.BUND_KEY_SEND_TIME, this.sec);
                                extras.putString(Global.BUND_KEY_TASK_ID, this.taskId);
                                extras.putBoolean(Global.BUND_KEY_TASK_IS_PRINTABLE, false);

                            } else if (taskId.contains(activity.getString(R.string.message_no_task_id_from_server))) {
//								TaskHDataAccess.deleteWithRelation(activity, this.taskH);
                                this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SAVEDRAFT);
                                ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_SAVEDRAFT, 0);
                                TaskHDataAccess.addOrReplace(activity, taskH);
                                if (StatusTabFragment.handler != null)
                                    StatusTabFragment.handler.sendEmptyMessage(0);
                                extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
                                if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                    extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_verification_failed));
                                } else {
                                    extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_sending_failed));
                                }
                                extras.putString(Global.BUND_KEY_SEND_SIZE, this.size);
                                extras.putString(Global.BUND_KEY_SEND_TIME, this.sec);
                                extras.putString(Global.BUND_KEY_TASK_ID, this.taskId);
                                extras.putBoolean(Global.BUND_KEY_TASK_IS_PRINTABLE, false);
                            } else if (taskId.contains("been deleted")) {
                                if (StatusTabFragment.handler != null)
                                    StatusTabFragment.handler.sendEmptyMessage(0);
                                extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
                                if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                    extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_verification_deleted));
                                } else {
                                    extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_sending_deleted));
                                }
                                extras.putString(Global.BUND_KEY_SEND_SIZE, this.size);
                                extras.putString(Global.BUND_KEY_SEND_TIME, this.sec);
                                extras.putString(Global.BUND_KEY_TASK_ID, this.taskId);
                                extras.putBoolean(Global.BUND_KEY_TASK_IS_PRINTABLE, false);
                                TaskHDataAccess.deleteWithRelation(activity, this.taskH);
                            } else {
                                this.taskH.setSubmit_date(taskH.getSubmit_date());
                                this.taskH.setSubmit_duration(sec);
                                this.taskH.setSubmit_size(size);
                                this.taskH.setSubmit_result(result);
                                this.taskH.setTask_id(taskId);
                                this.taskH.setLast_saved_question(1);
                                TaskHDataAccess.addOrReplace(activity, this.taskH);

                                if (StatusTabFragment.handler != null)
                                    StatusTabFragment.handler.sendEmptyMessage(0);
                                extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, false);
                                if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                    extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_verification_success));
                                    if (MainMenuActivity.mnSVYApproval != null) {
                                        TaskHDataAccess.deleteWithRelation(activity, this.taskH);
                                        String newUUIDTaskH = Tool.getUUID();
                                        this.taskH.setUuid_task_h(newUUIDTaskH);
                                        TaskHDataAccess.addOrReplace(activity, this.taskH);
                                        for (TaskD d : listTaskD) {
                                            d.setTaskH(taskH);
                                            d.setUuid_task_d(Tool.getUUID());
//                                            TaskDDataAccess.addOrReplace(activity, d);
                                        }
                                        TaskDDataAccess.addOrReplace(activity, listTaskD);
                                    }
                                } else {
                                    extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_sending_success));
                                }
                                extras.putString(Global.BUND_KEY_SEND_SIZE, this.size);
                                extras.putString(Global.BUND_KEY_SEND_TIME, this.sec);
                                extras.putString(Global.BUND_KEY_TASK_ID, this.taskId);
                                try {
                                    extras.putBoolean(Global.BUND_KEY_TASK_IS_PRINTABLE, Formatter.stringToBoolean(header.getIs_printable()));
                                } catch (Exception e) {
                                    FireCrash.log(e);
                                    extras.putBoolean(Global.BUND_KEY_TASK_IS_PRINTABLE, false);
                                }

                                if (this.taskH.getStatus().equals(TaskHDataAccess.STATUS_SEND_SENT)) {
                                    if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                        TimelineManager.insertTimeline(activity, this.taskH, true, false);
                                    } else {
                                        TimelineManager.insertTimeline(activity, this.taskH);
                                        boolean isRVinFront = GeneralParameterDataAccess.isRvInFrontEnable(activity, GlobalData.getSharedGlobalData().getUser().getUuid_user());
                                        if (isRVinFront && Global.APPLICATION_COLLECTION.equalsIgnoreCase(GlobalData.getSharedGlobalData().getApplication())) {
                                            taskH.setStatus_rv(TaskHDataAccess.STATUS_RV_SENT);
                                            TaskHDataAccess.addOrReplace(activity, this.taskH);
                                        }
                                    }
                                }
//						    	try {
//						    		long logCounter = TaskLog.getCounterLog(activity);
//						        	long taskListCounter = ToDoList.getCounterTaskList(activity);
//						        	if(MainMenuActivity.mnLog!=null)
//						        		MainMenuActivity.mnLog.setCounter(String.valueOf(logCounter));
//						       		if(MainMenuActivity.mnTaskList!=null)
//						       			MainMenuActivity.mnTaskList.setCounter(String.valueOf(taskListCounter));
//						        	if(MainMenuActivity.menuAdapter!=null)
//						        		MainMenuActivity.menuAdapter.notifyDataSetChanged();
//						        	if(StatusSectionFragment.statusListAdapter!=null)
//						        		StatusSectionFragment.statusListAdapter.notifyDataSetChanged();
//								} catch (Exception e) {
//									// TODO: handle exception
//								}

                            }

                            intent.putExtras(extras);
                            activity.startActivity(intent);
                            activity.finish();


                        } else if (activity.getString(R.string.no_internet_connection).equals(result)) {
                            if (StatusTabFragment.handler != null)
                                StatusTabFragment.handler.sendEmptyMessage(0);

                            Bundle extras = new Bundle();
                            Intent intent = new Intent(activity.getApplicationContext(), SendResultActivity.class);
                            extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
                            extras.putString(Global.BUND_KEY_SEND_RESULT, "Failed");
                            extras.putString(Global.BUND_KEY_SEND_SIZE, this.size);
                            extras.putString(Global.BUND_KEY_SEND_TIME, this.sec);
                            extras.putString(Global.BUND_KEY_TASK_ID, result);
                            intent.putExtras(extras);
                            activity.startActivity(intent);
                            activity.finish();


                        } else {
                            TaskHDataAccess.addOrReplace(activity, this.taskH);

                            int imageLeft = AutoSendImageThread.countPendingImageBeforeUpload(activity, this.taskH.getUuid_task_h());
                            ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_PENDING, imageLeft);
                            if (StatusTabFragment.handler != null)
                                StatusTabFragment.handler.sendEmptyMessage(0);

                            Bundle extras = new Bundle();
                            Intent intent = new Intent(activity.getApplicationContext(), SendResultActivity.class);
                            extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
                            if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_verification_failed));
                            } else {
                                extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_sending_failed));
                            }
                            extras.putString(Global.BUND_KEY_SEND_SIZE, this.size);
                            extras.putString(Global.BUND_KEY_SEND_TIME, this.sec);
                            extras.putString(Global.BUND_KEY_TASK_ID, "error code: " + result);
                            intent.putExtras(extras);
                            activity.startActivity(intent);
                            activity.finish();


                        }
                    }

                }
                //bong 8 apr 15 - penjagaan jika autoSend null dari mainMenuActivity
                try {
//                    if (MainMenuActivity.autoSendTask != null) {
//                        MainMenuActivity.autoSendTask.stopWaiting();
//                        MainMenuActivity.autoSendImage.stopWaiting();
//                    }
                    if (MainServices.autoSendTaskThread != null) {
                        MainServices.autoSendTaskThread.stopWaiting();
                        MainServices.autoSendImageThread.stopWaiting();
                        MainServices.taskListThread.stopWaiting();
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    // TODO: handle exception
                }
                if (errMessage == null) {
                    if (result != null) {
                        if (result.equalsIgnoreCase("Success")) {
                            if (taskH != null) {
                                if (taskH.getStatus().equals(TaskHDataAccess.STATUS_SEND_UPLOADING)) {
                                    try {
                                        List<TaskD> taskd = TaskDDataAccess.getUnsentImageByTaskH(activity, taskH.getUuid_user(), taskH.getUuid_task_h());
                                        ManualUploadImage(activity, taskd, taskH);
                                    } catch (Exception e) {
                                        FireCrash.log(e);
                                        e.printStackTrace();
                                    }
                                }
                            }
                        }
                    }
                }
                Global.isManualSubmit = false;
            }
        }.execute();

    }

    @Override
    public void sendTaskManual(final Activity activity, final SurveyHeaderBean header,
                               boolean finishActivity) {
        new AsyncTask<Void, Void, String>() {
            String taskId = null;
            private ProgressDialog progressDialog;
            private List<TaskD> listTaskD;
            private TaskH taskH;
            private String errMessage = null;
            private String sec;
            private String size;

            @Override
            protected void onPreExecute() {
                progressDialog = ProgressDialog.show(activity, "", activity.getString(R.string.progressSave), true);
            }

            @Override
            protected String doInBackground(Void... params) {
                // TODO Auto-generated method stub
                //bong 8 apr 15 - penjagaan jika autoSend null dari mainMenuActivity
//                if (MainMenuActivity.autoSendTask != null) {
//                    MainMenuActivity.autoSendTask.requestWait();
//                    MainMenuActivity.autoSendImage.requestWait();
//                }
                Global.isManualSubmit = true;
                if (MainServices.autoSendTaskThread != null) {
                    MainServices.autoSendTaskThread.requestWait();
                    MainServices.autoSendImageThread.requestWait();
                    MainServices.taskListThread.requestWait();
                }
                this.taskH = header.getTaskH();

                taskId = this.taskH.getTask_id();
                String result = null;
                try {
                    listTaskD = TaskDDataAccess.getAll(activity, taskH.getUuid_task_h(), TaskDDataAccess.ALL_TASK);
                } catch (Exception e) {
                    FireCrash.log(e);
                }
                //minimalisir data
                String uuidTaskH = taskH.getUuid_task_h();
                String uuidUser = taskH.getUuid_user();
                boolean isHaveImage = false;
                List<TaskD> taskDs = new ArrayList<TaskD>();
                int i = 1;

                try {
                    List<TaskD> taskd = TaskDDataAccess.getUnsentImageByTaskH(activity, uuidUser, uuidTaskH);
                    if (taskd != null && !taskd.isEmpty())
                        isHaveImage = true;
                } catch (Exception e) {
                    FireCrash.log(e);
                    e.printStackTrace();
                }
                if (!isHaveImage) {
                    for (TaskD d : listTaskD) {
                        if (d.getImage() != null) {
                            isHaveImage = true;
                            break;
                        }
                    }
                }

                for (TaskD d : listTaskD) {
                    if (d.getIs_visible().equals(Global.TRUE_STRING)) {
                        if (isPartial(activity)) {
                            if (d.getImage() == null) {
                                taskDs.add(d);
                            }
                        } else {
                            if (i == taskDs.size())
                                d.setIs_final(Global.TRUE_STRING);
                            taskDs.add(d);
                        }
                    }
                    i++;
                }
                if (!isPartial(activity)) {
                    TaskD d = taskDs.get(taskDs.size() - 1);
                    if (!d.getIs_final().equals(Global.TRUE_STRING)) {
                        d.setIs_final(Global.TRUE_STRING);
                    }
                } else {
                    if (!isHaveImage) {
                        if (!taskDs.isEmpty()) {
                            TaskD d = taskDs.get(taskDs.size() - 1);
                            if (!d.getIs_final().equals(Global.TRUE_STRING)) {
                                d.setIs_final(Global.TRUE_STRING);
                            }
                        }
                    }
                }
                this.taskH.setSubmit_date(taskH.getSubmit_date());
                //-------------------

                JsonRequestSubmitTask task = new JsonRequestSubmitTask();
                task.setAudit(GlobalData.getSharedGlobalData().getAuditData());
                task.addImeiAndroidIdToUnstructured();
                task.setTaskH(taskH);
                task.setTaskD(taskDs);

                String json = GsonHelper.toJson(task);
                String url = GlobalData.getSharedGlobalData().getURL_SUBMITTASK();
                size = String.valueOf(json.getBytes().length);

                boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                HttpCryptedConnection httpConn = new HttpCryptedConnection(activity, encrypt, decrypt);
                HttpConnectionResult serverResult = null;
                Date startTime = Tool.getSystemDateTime();

                try {
                    serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
                } catch (Exception e) {
                    FireCrash.log(e);
                    e.printStackTrace();
                    try {
                        progressDialog.dismiss();
                    } catch (Exception e1) {
                        e1.printStackTrace();
                    }
                    errMessage = e.getMessage();
                }
                if (serverResult.isOK()) {
                    String resultvalue = serverResult.getResult();
                    JsonResponseSubmitTask responseSubmitTask = GsonHelper.fromJson(resultvalue, JsonResponseSubmitTask.class);
                    if (responseSubmitTask.getStatus().getCode() == 0) {
                        String status = responseSubmitTask.getResult();
                        Date finishTime = Tool.getSystemDateTime();
                        long time = finishTime.getTime() - startTime.getTime();
                        sec = String.valueOf((int) Math.ceil(time / 1000));
                        if (status.equalsIgnoreCase("Success")) {
                            result = status;
                            if (responseSubmitTask.getTaskId() != null) {
                                taskId = responseSubmitTask.getTaskId();
                                String application = GlobalData.getSharedGlobalData().getAuditData().getApplication();
                                if (application == null)
                                    application = GlobalData.getSharedGlobalData().getApplication();
                                if (Global.APPLICATION_COLLECTION.equalsIgnoreCase(application)) {
                                    if (responseSubmitTask.getCashOnHand() != null) {
                                        GlobalData.getSharedGlobalData().getUser().setCash_on_hand(
                                                responseSubmitTask.getCashOnHand()
                                        );
                                        UserDataAccess.addOrReplace(activity, GlobalData.getSharedGlobalData().getUser());
                                    }
                                }
                            }
                        } else {
                            result = status;
                        }

                    } else {
                        errMessage = responseSubmitTask.getStatus().getMessage();
                    }

                }
                return result;
            }

            @Override
            protected void onPostExecute(String result) {
                if (progressDialog.isShowing()) {
                    try {
                        progressDialog.dismiss();
                    } catch (Exception e) {
                        FireCrash.log(e);
                    }
                }

                if (errMessage != null) {
                    TaskHDataAccess.addOrReplace(activity, this.taskH);
                    NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(activity);
                    builder.withTitle(activity.getString(R.string.error_capital))
                            .withMessage(activity.getString(R.string.message_sending_failed) + "\n" + errMessage)
                            .show();
                } else {
                    if (result.equalsIgnoreCase("Success")) {
                        for (TaskD taskD : listTaskD) {
                            if (taskD.getImage() == null)
                                taskD.setIs_sent(Global.TRUE_STRING);
                            else {
                                if (isPartial(activity)) {
                                    taskD.setIs_sent(Global.FALSE_STRING);
                                } else {
                                    taskD.setIs_sent(Global.TRUE_STRING);
                                }
                            }
                        }
                        TaskDDataAccess.addOrReplace(activity, listTaskD);
                        if (AutoSendImageThread.countPendingImageBeforeUpload(activity, this.taskH.getUuid_task_h()) > 0) {
                            this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_UPLOADING);
                            int imageLeft = AutoSendImageThread.countPendingImageBeforeUpload(activity, this.taskH.getUuid_task_h());
                            TimelineManager.insertTimeline(activity, taskH);
                            ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_UPLOADING, imageLeft);
                        } else {
                            this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SENT);
                            ToDoList.removeSurveyFromList(taskId);
                        }
                        this.taskH.setSubmit_date(taskH.getSubmit_date());
                        this.taskH.setSubmit_duration(sec);
                        this.taskH.setSubmit_size(size);
                        this.taskH.setSubmit_result(result);
                        this.taskH.setTask_id(taskId);
                        this.taskH.setLast_saved_question(1);
                        TaskHDataAccess.addOrReplace(activity, this.taskH);
                        if (this.taskH.getStatus().equals(TaskHDataAccess.STATUS_SEND_SENT))
                            TimelineManager.insertTimeline(activity, this.taskH);
                    }
                }
                //bong 8 apr 15 - penjagaan jika autoSend null dari mainMenuActivity
//                if (MainMenuActivity.autoSendTask != null) {
//                    MainMenuActivity.autoSendTask.stopWaiting();
//                    MainMenuActivity.autoSendImage.stopWaiting();
//                }
                if (MainServices.autoSendTaskThread != null) {
                    MainServices.autoSendTaskThread.stopWaiting();
                    MainServices.autoSendImageThread.stopWaiting();
                    MainServices.taskListThread.stopWaiting();
                }
                Global.isManualSubmit = false;
            }

        }.execute();
    }

    public void sendApprovalTask(final Activity activity, final SurveyHeaderBean header, final String flag, final boolean isApprovalTask) {
        new AsyncTask<Void, Void, String>() {
            String taskId = null;
            private ProgressDialog progressDialog;
            private String errMessage = null;

            @Override
            protected void onPreExecute() {
                progressDialog = ProgressDialog.show(activity, "", activity.getString(R.string.progressSend), true);
            }

            @Override
            protected String doInBackground(Void... params) {
                // TODO Auto-generated method stub
                String result = null;
                if (Tool.isInternetconnected(activity)) {
                    String uuidTaskH = header.getUuid_task_h();
                    JsonRequestApprovalTask request = new JsonRequestApprovalTask();
                    request.setUuid_task_h(uuidTaskH);
                    request.setFlag(flag);
                    request.setAudit(GlobalData.getSharedGlobalData().getAuditData());
                    request.addImeiAndroidIdToUnstructured();

                    String json = GsonHelper.toJson(request);
                    String url = GlobalData.getSharedGlobalData().getURL_SUBMITAPPROVALTASK();
                    if (!isApprovalTask)
                        url = GlobalData.getSharedGlobalData().getURL_SUBMITVERIFICATIONTASK();

                    boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                    boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                    HttpCryptedConnection httpConn = new HttpCryptedConnection(activity, encrypt, decrypt);
                    HttpConnectionResult serverResult = null;

                    try {
                        serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        e.printStackTrace();
                        try {
                            progressDialog.dismiss();
                        } catch (Exception e1) {
                            e1.printStackTrace();
                        }
                        errMessage = e.getMessage();
                    }

                    try {
                        result = serverResult.getResult();
                        MssResponseType response = GsonHelper.fromJson(result, MssResponseType.class);
                        if (response.getStatus().getCode() == 0) {
                            result = "success";
                        } else {
                            errMessage = result;
                        }
                    } catch (Exception e) {
                        FireCrash.log(e);
                        // TODO: handle exception
                    }
                } else {
                    result = activity.getString(R.string.no_internet_connection);
                }
                return result;
            }

            @Override
            protected void onPostExecute(String result) {
                if (progressDialog.isShowing()) {
                    try {
                        progressDialog.dismiss();
                    } catch (Exception e) {
                        FireCrash.log(e);
                    }
                }
                if (errMessage != null && errMessage.length() > 0) {
                    Bundle extras = new Bundle();
                    Intent intent = new Intent(activity.getApplicationContext(), SendResultActivity.class);
                    extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
                    if (flag.equals(Global.FLAG_FOR_APPROVALTASK)) {
                        extras.putString(Global.BUND_KEY_SEND_RESULT, "Approval Task Failed");
                    } else if (flag.equals(Global.FLAG_FOR_REJECTEDTASK)) {
                        extras.putString(Global.BUND_KEY_SEND_RESULT, "Rejection Task Failed");
                    }
                    extras.putString(Global.BUND_KEY_TASK_ID, errMessage);
                    intent.putExtras(extras);
                    activity.startActivity(intent);
                    activity.finish();

                } else {
                    if (activity.getString(R.string.no_internet_connection).equals(result)) {
                        Bundle extras = new Bundle();
                        Intent intent = new Intent(activity.getApplicationContext(), SendResultActivity.class);
                        extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
                        if (flag.equals(Global.FLAG_FOR_APPROVALTASK)) {
                            extras.putString(Global.BUND_KEY_SEND_RESULT, "Approval Task Failed");
                        } else if (flag.equals(Global.FLAG_FOR_REJECTEDTASK)) {
                            extras.putString(Global.BUND_KEY_SEND_RESULT, "Rejection Task Failed");
                        }
                        extras.putString(Global.BUND_KEY_TASK_ID, result);
                        intent.putExtras(extras);
                        activity.startActivity(intent);
                        activity.finish();

                    } else {
                        TaskH taskH = header.getTaskH();
                        Bundle extras = new Bundle();
                        Intent intent = new Intent(activity.getApplicationContext(), SendResultActivity.class);
                        extras.putString(Global.BUND_KEY_TASK_ID, taskH.getTask_id());
                        extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, false);
                        if (flag.equals(Global.FLAG_FOR_APPROVALTASK)) {
                            extras.putString(Global.BUND_KEY_SEND_RESULT, "Task Approved");
                            taskH.setStatus(TaskHDataAccess.STATUS_SEND_SENT);
                            taskH.setIs_prepocessed(Global.FORM_TYPE_APPROVAL);
                        } else if (flag.equals(Global.FLAG_FOR_REJECTEDTASK)) {
                            extras.putString(Global.BUND_KEY_SEND_RESULT, "Task Rejected");
                            taskH.setStatus(TaskHDataAccess.STATUS_SEND_REJECTED);
                        }

						/*try {
                            taskH = TaskHDataAccess.getOneHeader(activity, uuidTaskH);
							TaskHDataAccess.deleteWithRelation(activity, taskH);
						} catch (Exception e) {
                    FireCrash.log(e);	}*/
                        intent.putExtras(extras);
                        try {
                            if (!isApprovalTask) {
                                if (taskH != null) {
                                    taskH.setSubmit_date(taskH.getSubmit_date());
                                    TimelineManager.insertTimeline(activity, taskH, true, true);
                                }
                            } else {
                                if (taskH != null) {
                                    taskH.setSubmit_date(taskH.getSubmit_date());
                                    if (flag.equals(Global.FLAG_FOR_APPROVALTASK)) {
                                        TimelineManager.insertTimeline(activity, taskH, false, false);
                                    } else if (flag.equals(Global.FLAG_FOR_REJECTEDTASK)) {
                                        TimelineManager.insertTimeline(activity, taskH, false, true);
                                    }
                                }
                            }
                            TaskHDataAccess.addOrReplace(activity, taskH);
                        } catch (Exception e) {
                            FireCrash.log(e);
                            // TODO: handle exception
                        }
                        activity.startActivity(intent);
                        activity.finish();

                    }
                }
            }
        }.execute();
    }

    public void sendApprovalTask(final Activity activity, final SurveyHeaderBean header, final String flag, final boolean isApprovalTask, final String notes) {
        new AsyncTask<Void, Void, String>() {
            String taskId = null;
            private ProgressDialog progressDialog;
            private String errMessage = null;

            @Override
            protected void onPreExecute() {
                progressDialog = ProgressDialog.show(activity, "", activity.getString(R.string.progressSend), true);
            }

            @Override
            protected String doInBackground(Void... params) {
                // TODO Auto-generated method stub
                String result = null;
                if (Tool.isInternetconnected(activity)) {
                    String uuidTaskH = header.getUuid_task_h();
                    JsonRequestApprovalTask request = new JsonRequestApprovalTask();
                    request.setUuid_task_h(uuidTaskH);
                    request.setFlag(flag);
                    request.setNotes(notes);
                    request.setAudit(GlobalData.getSharedGlobalData().getAuditData());
                    request.addImeiAndroidIdToUnstructured();

                    String json = GsonHelper.toJson(request);
                    String url = GlobalData.getSharedGlobalData().getURL_SUBMITAPPROVALTASK();
                    if (!isApprovalTask)
                        url = GlobalData.getSharedGlobalData().getURL_SUBMITVERIFICATIONTASK();

                    boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                    boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                    HttpCryptedConnection httpConn = new HttpCryptedConnection(activity, encrypt, decrypt);
                    HttpConnectionResult serverResult = null;

                    try {
                        serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        e.printStackTrace();
                        try {
                            progressDialog.dismiss();
                        } catch (Exception e1) {
                            e1.printStackTrace();
                        }
                        errMessage = e.getMessage();
                    }

                    try {
                        result = serverResult.getResult();
                        MssResponseType response = GsonHelper.fromJson(result, MssResponseType.class);
                        if (response.getStatus().getCode() == 0) {
                            result = "success";
                        } else {
                            errMessage = result;
                        }
                    } catch (Exception e) {
                        FireCrash.log(e);
                        // TODO: handle exception
                    }
                } else {
                    result = activity.getString(R.string.no_internet_connection);
                }
                return result;
            }

            @Override
            protected void onPostExecute(String result) {
                if (progressDialog.isShowing()) {
                    try {
                        progressDialog.dismiss();
                    } catch (Exception e) {
                        FireCrash.log(e);
                    }
                }
                if (errMessage != null && errMessage.length() > 0) {
                    Bundle extras = new Bundle();
                    Intent intent = new Intent(activity.getApplicationContext(), SendResultActivity.class);
                    extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
                    if (flag.equals(Global.FLAG_FOR_APPROVALTASK)) {
                        extras.putString(Global.BUND_KEY_SEND_RESULT, "Approval Task Failed");
                    } else if (flag.equals(Global.FLAG_FOR_REJECTEDTASK)) {
                        extras.putString(Global.BUND_KEY_SEND_RESULT, "Rejection Task Failed");
                    }
                    extras.putString(Global.BUND_KEY_TASK_ID, errMessage);
                    intent.putExtras(extras);
                    activity.startActivity(intent);
                    activity.finish();

                } else {
                    if (activity.getString(R.string.no_internet_connection).equals(result)) {
                        Bundle extras = new Bundle();
                        Intent intent = new Intent(activity.getApplicationContext(), SendResultActivity.class);
                        extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
                        if (flag.equals(Global.FLAG_FOR_APPROVALTASK)) {
                            extras.putString(Global.BUND_KEY_SEND_RESULT, "Approval Task Failed");
                        } else if (flag.equals(Global.FLAG_FOR_REJECTEDTASK)) {
                            extras.putString(Global.BUND_KEY_SEND_RESULT, "Rejection Task Failed");
                        }
                        extras.putString(Global.BUND_KEY_TASK_ID, result);
                        intent.putExtras(extras);
                        activity.startActivity(intent);
                        activity.finish();

                    } else {
                        TaskH taskH = header.getTaskH();
                        Bundle extras = new Bundle();
                        Intent intent = new Intent(activity.getApplicationContext(), SendResultActivity.class);
                        extras.putString(Global.BUND_KEY_TASK_ID, taskH.getTask_id());
                        extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, false);
                        if (flag.equals(Global.FLAG_FOR_APPROVALTASK)) {
                            extras.putString(Global.BUND_KEY_SEND_RESULT, "Task Approved");
                            taskH.setStatus(TaskHDataAccess.STATUS_SEND_SENT);
                            taskH.setIs_prepocessed(Global.FORM_TYPE_APPROVAL);
                        } else if (flag.equals(Global.FLAG_FOR_REJECTEDTASK)) {
                            extras.putString(Global.BUND_KEY_SEND_RESULT, "Task Rejected");
                            taskH.setStatus(TaskHDataAccess.STATUS_SEND_REJECTED);
                        }

						/*try {
                            taskH = TaskHDataAccess.getOneHeader(activity, uuidTaskH);
							TaskHDataAccess.deleteWithRelation(activity, taskH);
						} catch (Exception e) {
                    FireCrash.log(e);	}*/
                        intent.putExtras(extras);
                        try {
                            if (!isApprovalTask) {
                                if (taskH != null) {
                                    taskH.setSubmit_date(taskH.getSubmit_date());
                                    TimelineManager.insertTimeline(activity, taskH, true, true);
                                }
                            } else {
                                if (taskH != null) {
                                    taskH.setSubmit_date(taskH.getSubmit_date());
                                    if (flag.equals(Global.FLAG_FOR_APPROVALTASK)) {
                                        TimelineManager.insertTimeline(activity, taskH, false, false);
                                    } else if (flag.equals(Global.FLAG_FOR_REJECTEDTASK)) {
                                        TimelineManager.insertTimeline(activity, taskH, false, true);
                                    }
                                }
                            }
                            TaskHDataAccess.addOrReplace(activity, taskH);
                        } catch (Exception e) {
                            FireCrash.log(e);
                            // TODO: handle exception
                        }
                        activity.startActivity(intent);
                        activity.finish();

                    }
                }
            }
        }.execute();
    }

    public void saveAndVerificationTask(final Activity activity, final int mode,
                                        final SurveyHeaderBean header, final List<QuestionBean> listOfQuestions, final String notes) {

        new AsyncTask<Void, Void, String>() {
            protected ProgressDialog progressDialog;
            protected List<TaskD> listTaskD;
            protected TaskH taskH;
            protected String errMessage = null;
            protected String errCode = null;
            protected String sec;
            protected String size;
            String taskId = null;
            private String messageWait = activity.getString(R.string.progressSend);

            @Override
            protected void onPreExecute() {
                progressDialog = ProgressDialog.show(activity, "", this.messageWait, true);
            }

            @Override
            protected String doInBackground(Void... arg0) {
                // TODO Auto-generated method stub
                //bong 8 apr 15 - penjagaan jika autoSend null dari mainMenuActivity
                String result = null;
                Global.isManualSubmit = true;
                try {
//                    if (MainMenuActivity.autoSendTask != null) {
//                        MainMenuActivity.autoSendTask.requestWait();
//                        MainMenuActivity.autoSendImage.requestWait();
//                    }
                    if (MainServices.autoSendTaskThread != null) {
                        MainServices.autoSendTaskThread.requestWait();
                        MainServices.autoSendImageThread.requestWait();
                        MainServices.taskListThread.requestWait();
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    // TODO: handle exception
                }
                boolean saved = saveTask(activity, mode, header, listOfQuestions, null, true);
                if (saved) {
                    this.taskH = header.getTaskH();
                    try {
                        listTaskD = TaskDDataAccess.getAll(activity, header.getUuid_task_h(), TaskDDataAccess.ALL_TASK);
                    } catch (Exception e) {
                        FireCrash.log(e);
                    }
                    if (Tool.isInternetconnected(activity)) {

                        taskId = this.taskH.getTask_id();

                        //minimalisir data
                        String uuidTaskH = taskH.getUuid_task_h();
                        String uuidUser = taskH.getUuid_user();
                        boolean isHaveImage = false;
                        List<TaskD> taskDs = new ArrayList<TaskD>();
                        int i = 1;

                        try {
                            List<TaskD> taskd = TaskDDataAccess.getUnsentImageByTaskH(activity, uuidUser, uuidTaskH);
                            if (taskd != null && !taskd.isEmpty())
                                isHaveImage = true;
                        } catch (Exception e) {
                            FireCrash.log(e);
                            e.printStackTrace();
                        }
                        if (!isHaveImage) {
                            for (TaskD d : listTaskD) {
                                if (d.getImage() != null) {
                                    isHaveImage = true;
                                    break;
                                }
                            }
                        }

                        for (TaskD d : listTaskD) {
                            if (d.getIs_visible().equals(Global.TRUE_STRING)) {
                                if (isPartial(activity)) {
                                    if (d.getImage() == null) {
                                        taskDs.add(d);
                                    }
                                } else {
                                    if (i == taskDs.size())
                                        d.setIs_final(Global.TRUE_STRING);
                                    taskDs.add(d);
                                }
                            }
                            i++;
                        }
                        if (!isPartial(activity)) {
                            TaskD d = taskDs.get(taskDs.size() - 1);
                            if (!d.getIs_final().equals(Global.TRUE_STRING)) {
                                d.setIs_final(Global.TRUE_STRING);
                            }
                        } else {
                            if (!isHaveImage) {
                                if (!taskDs.isEmpty()) {
                                    TaskD d = taskDs.get(taskDs.size() - 1);
                                    if (!d.getIs_final().equals(Global.TRUE_STRING)) {
                                        d.setIs_final(Global.TRUE_STRING);
                                    }
                                }
                            }
                        }
                        this.taskH.setSubmit_date(taskH.getSubmit_date());
                        //-------------------

                        JsonRequestVerificationTask task = new JsonRequestVerificationTask();
                        task.setAudit(GlobalData.getSharedGlobalData().getAuditData());
                        task.addImeiAndroidIdToUnstructured();
                        task.setTaskH(taskH);
                        task.setTaskD(taskDs);
                        task.setNotes(notes);

                        if (task.getTaskD() != null && !task.getTaskD().isEmpty()) {
                            String json = GsonHelper.toJson(task);
                            String url = GlobalData.getSharedGlobalData().getURL_SUBMITTASK();

                            if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                url = GlobalData.getSharedGlobalData().getURL_SUBMITVERIFICATIONTASK();
                            } else {
                                url = GlobalData.getSharedGlobalData().getURL_SUBMITTASK();
                            }

                            size = String.valueOf(json.getBytes().length);

                            boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                            boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                            HttpCryptedConnection httpConn = new HttpCryptedConnection(activity, encrypt, decrypt);
                            HttpConnectionResult serverResult = null;
                            Date startTime = Tool.getSystemDateTime();

                            try {
                                if (Global.IS_DEV) {
                                    if (GlobalData.getSharedGlobalData().getURL_SUBMITVERIFICATIONTASK().equals(url)) {
                                        Logger.i("INFO", "VERIFY VERIFICATION TASK");
                                    } else {
                                        Logger.i("INFO", "SUBMIT TASK");
                                    }
                                }
                                serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
                            } catch (Exception e) {
                                FireCrash.log(e);
                                e.printStackTrace();
                                try {
                                    progressDialog.dismiss();
                                } catch (Exception e1) {
                                    e1.printStackTrace();
                                }
                                errMessage = e.getMessage();
                            }
                            if (serverResult.isOK()) {
                                String resultvalue = serverResult.getResult();
                                JsonResponseSubmitTask responseSubmitTask = GsonHelper.fromJson(resultvalue, JsonResponseSubmitTask.class);
                                if (responseSubmitTask.getStatus().getCode() == 0) {
                                    String status = responseSubmitTask.getResult();
                                    Date finishTime = Tool.getSystemDateTime();
                                    long time = finishTime.getTime() - startTime.getTime();
                                    sec = String.valueOf((int) Math.ceil(time / 1000));
                                    if (status == null)
                                        status = "Success";
                                    if (status.equalsIgnoreCase("Success")) {
                                        result = status;
                                        if (responseSubmitTask.getTaskId() != null)
                                            taskId = responseSubmitTask.getTaskId();
                                        else
                                            taskId = activity.getString(R.string.message_no_task_id_from_server);
                                    } else {
                                        result = status;
                                    }

                                    // bong 8 may 15 - call generate print result
                                    //TODO generate printResult
                                    if (taskH.getScheme().getIs_printable() != null && taskH.getScheme().getIs_printable().equals(Global.TRUE_STRING))
                                        generatePrintResult(activity, taskH);

                                } else {
                                    result = String.valueOf(responseSubmitTask.getStatus().getCode());
                                    errMessage = responseSubmitTask.getStatus().getMessage();
                                    errCode = String.valueOf(responseSubmitTask.getStatus().getCode());
                                }
                            } else {
                                try {
                                    result = serverResult.getResult();
                                } catch (Exception e) {
                                    FireCrash.log(e);
                                }
                            }
                        } else {
                            result = activity.getString(R.string.request_error);
                            errMessage = activity.getString(R.string.request_error);
                            errCode = STATUS_TASKD_MISSING;
                            Global.isManualSubmit = false;
                        }

                    } else {
                        if (listTaskD == null || listTaskD.isEmpty()) {
                            result = activity.getString(R.string.request_error);
                            errMessage = activity.getString(R.string.request_error);
                            errCode = STATUS_TASKD_MISSING;
                            Global.isManualSubmit = false;
                            return result;
                        }
                        result = activity.getString(R.string.no_internet_connection);
                        Global.isManualSubmit = false;
                    }
                } else {
                    result = activity.getString(R.string.request_error);
                    errMessage = activity.getString(R.string.request_error);
                    errCode = STATUS_SAVE_FAILED;
                    Global.isManualSubmit = false;
                    return result;
                }

                return result;
            }

            @Override
            protected void onPostExecute(String result) {
                if (progressDialog.isShowing()) {
                    try {
                        progressDialog.dismiss();
                    } catch (Exception e) {
                        FireCrash.log(e);
                    }
                }

                if (errMessage != null) {
                    if (errCode != null && errCode.equals(STATUS_TASKD_MISSING)) {
                        try {
                            this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SAVEDRAFT);
                            TaskHDataAccess.addOrReplace(activity, this.taskH);
                        } catch (Exception e) {
                            FireCrash.log(e);
                        }
                        NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(activity);
                        builder.withTitle(activity.getString(R.string.warning_capital))
                                .withMessage(activity.getString(R.string.message_sending_failed) + "\n" + errMessage)
                                .show();
                    } else if (errCode != null && errCode.equals(STATUS_SAVE_FAILED)) {
                        NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(activity);
                        builder.withTitle(activity.getString(R.string.warning_capital))
                                .withMessage(activity.getString(R.string.message_sending_failed) + "\n" + errMessage)
                                .show();
                    } else {
                        Bundle extras = new Bundle();
                        Intent intent = new Intent(activity.getApplicationContext(), SendResultActivity.class);
                        if (errCode != null && errCode.equals(STATUS_TASK_NOT_MAPPING)) {
                            this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SAVEDRAFT);
                            ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_SAVEDRAFT, 0);
                            TaskHDataAccess.addOrReplace(activity, taskH);
                            if (StatusTabFragment.handler != null)
                                StatusTabFragment.handler.sendEmptyMessage(0);
                            extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
                            if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_verification_failed));
                            } else {
                                extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_sending_failed));
                            }
                            extras.putString(Global.BUND_KEY_SEND_SIZE, this.size);
                            extras.putString(Global.BUND_KEY_SEND_TIME, this.sec);
                            extras.putString(Global.BUND_KEY_TASK_ID, this.errMessage);
                            extras.putBoolean(Global.BUND_KEY_TASK_IS_PRINTABLE, false);
                        } else if (errCode != null && errCode.equals(STATUS_TASK_DELETED)) {
                            if (StatusTabFragment.handler != null)
                                StatusTabFragment.handler.sendEmptyMessage(0);
                            extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
                            if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_verification_deleted));
                            } else {
                                extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_sending_deleted));
                            }
                            extras.putString(Global.BUND_KEY_SEND_SIZE, this.size);
                            extras.putString(Global.BUND_KEY_SEND_TIME, this.sec);
                            extras.putString(Global.BUND_KEY_TASK_ID, this.errMessage);
                            extras.putBoolean(Global.BUND_KEY_TASK_IS_PRINTABLE, false);
                            TaskHDataAccess.deleteWithRelation(activity, this.taskH);
                        } else if (errCode != null && errCode.equals(STATUS_IMEI_FAILED)) {
                            this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SAVEDRAFT);
                            ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_SAVEDRAFT, 0);
                            TaskHDataAccess.addOrReplace(activity, taskH);
                            if (StatusTabFragment.handler != null)
                                StatusTabFragment.handler.sendEmptyMessage(0);
                            extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
                            if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_verification_failed));
                            } else {
                                extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_sending_failed));
                            }
                            extras.putString(Global.BUND_KEY_SEND_SIZE, this.size);
                            extras.putString(Global.BUND_KEY_SEND_TIME, this.sec);
                            extras.putString(Global.BUND_KEY_TASK_ID, this.errMessage);
                            extras.putBoolean(Global.BUND_KEY_TASK_IS_PRINTABLE, false);
                        } else {
                            try {
                                int imageLeft = AutoSendImageThread.countPendingImageBeforeUpload(activity, this.taskH.getUuid_task_h());
                                ToDoList.updateStatusSurvey(taskId, this.taskH.getStatus(), imageLeft);
                            } catch (Exception e) {
                                FireCrash.log(e);

                            }
                            TaskHDataAccess.addOrReplace(activity, this.taskH);
                            extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
                            if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_verification_failed));
                            } else {
                                extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_sending_failed));
                            }
                            extras.putString(Global.BUND_KEY_SEND_SIZE, this.size);
                            extras.putString(Global.BUND_KEY_SEND_TIME, this.sec);
                            extras.putString(Global.BUND_KEY_TASK_ID, errMessage);
                        }
                        intent.putExtras(extras);
                        activity.startActivity(intent);
                        activity.finish();

                    }
                } else {
                    if (result != null) {
                        if (result.equalsIgnoreCase("Success")) {
                            Bundle extras = new Bundle();
                            Intent intent = new Intent(activity.getApplicationContext(), SendResultActivity.class);
                            //List<TaskD> newlistTaskD = new ArrayList<TaskD>();
                            for (TaskD taskD : listTaskD) {
                                if (taskD.getImage() == null)
                                    taskD.setIs_sent(Global.TRUE_STRING);
                                else {
                                    if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                        taskD.setIs_sent(Global.TRUE_STRING);
                                        //newlistTaskD.add(taskD);
                                    } else {
                                        if (isPartial(activity)) {
                                            taskD.setIs_sent(Global.FALSE_STRING);
                                        } else {
                                            taskD.setIs_sent(Global.TRUE_STRING);
                                        }
                                    }
                                }
//                                TaskDDataAccess.addOrReplace(activity, taskD);
                            }
                            TaskDDataAccess.addOrReplace(activity, listTaskD);
                            if (AutoSendImageThread.countPendingImageBeforeUpload(activity, this.taskH.getUuid_task_h()) > 0) {
                                this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_UPLOADING);
                                int imageLeft = AutoSendImageThread.countPendingImageBeforeUpload(activity, this.taskH.getUuid_task_h());
                                ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_UPLOADING, imageLeft);
                            } else {
                                this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SENT);
                                ToDoList.removeSurveyFromList(taskId);
                            }

                            if (taskId.contains(activity.getString(R.string.message_task_not_mapping))) {
//								TaskHDataAccess.deleteWithRelation(activity, this.taskH);
                                this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SAVEDRAFT);
                                ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_SAVEDRAFT, 0);
                                TaskHDataAccess.addOrReplace(activity, taskH);
                                if (StatusTabFragment.handler != null)
                                    StatusTabFragment.handler.sendEmptyMessage(0);
                                extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
                                if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                    extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_verification_failed));
                                } else {
                                    extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_sending_failed));
                                }
                                extras.putString(Global.BUND_KEY_SEND_SIZE, this.size);
                                extras.putString(Global.BUND_KEY_SEND_TIME, this.sec);
                                extras.putString(Global.BUND_KEY_TASK_ID, this.taskId);
                                extras.putBoolean(Global.BUND_KEY_TASK_IS_PRINTABLE, false);

                            } else if (taskId.contains(activity.getString(R.string.message_no_task_id_from_server))) {
//								TaskHDataAccess.deleteWithRelation(activity, this.taskH);
                                this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SAVEDRAFT);
                                ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_SAVEDRAFT, 0);
                                TaskHDataAccess.addOrReplace(activity, taskH);
                                if (StatusTabFragment.handler != null)
                                    StatusTabFragment.handler.sendEmptyMessage(0);
                                extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
                                if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                    extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_verification_failed));
                                } else {
                                    extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_sending_failed));
                                }
                                extras.putString(Global.BUND_KEY_SEND_SIZE, this.size);
                                extras.putString(Global.BUND_KEY_SEND_TIME, this.sec);
                                extras.putString(Global.BUND_KEY_TASK_ID, this.taskId);
                                extras.putBoolean(Global.BUND_KEY_TASK_IS_PRINTABLE, false);
                            } else if (taskId.contains("been deleted")) {
                                if (StatusTabFragment.handler != null)
                                    StatusTabFragment.handler.sendEmptyMessage(0);
                                extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
                                if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                    extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_verification_deleted));
                                } else {
                                    extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_sending_deleted));
                                }
                                extras.putString(Global.BUND_KEY_SEND_SIZE, this.size);
                                extras.putString(Global.BUND_KEY_SEND_TIME, this.sec);
                                extras.putString(Global.BUND_KEY_TASK_ID, this.taskId);
                                extras.putBoolean(Global.BUND_KEY_TASK_IS_PRINTABLE, false);
                                TaskHDataAccess.deleteWithRelation(activity, this.taskH);
                            } else {
                                this.taskH.setSubmit_date(taskH.getSubmit_date());
                                this.taskH.setSubmit_duration(sec);
                                this.taskH.setSubmit_size(size);
                                this.taskH.setSubmit_result(result);
                                this.taskH.setTask_id(taskId);
                                this.taskH.setLast_saved_question(1);
                                TaskHDataAccess.addOrReplace(activity, this.taskH);

                                if (StatusTabFragment.handler != null)
                                    StatusTabFragment.handler.sendEmptyMessage(0);
                                extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, false);
                                if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                    extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_verification_success));
                                    if (MainMenuActivity.mnSVYApproval != null) {
                                        TaskHDataAccess.deleteWithRelation(activity, this.taskH);
                                        String newUUIDTaskH = Tool.getUUID();
                                        this.taskH.setUuid_task_h(newUUIDTaskH);
                                        TaskHDataAccess.addOrReplace(activity, this.taskH);
                                        for (TaskD d : listTaskD) {
                                            d.setTaskH(taskH);
                                            d.setUuid_task_d(Tool.getUUID());
//                                            TaskDDataAccess.addOrReplace(activity, d);
                                        }
                                        TaskDDataAccess.addOrReplace(activity, listTaskD);
                                    }
                                } else {
                                    extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_sending_success));
                                }
                                extras.putString(Global.BUND_KEY_SEND_SIZE, this.size);
                                extras.putString(Global.BUND_KEY_SEND_TIME, this.sec);
                                extras.putString(Global.BUND_KEY_TASK_ID, this.taskId);
                                try {
                                    extras.putBoolean(Global.BUND_KEY_TASK_IS_PRINTABLE, Formatter.stringToBoolean(header.getIs_printable()));
                                } catch (Exception e) {
                                    FireCrash.log(e);
                                    extras.putBoolean(Global.BUND_KEY_TASK_IS_PRINTABLE, false);
                                }

                                if (this.taskH.getStatus().equals(TaskHDataAccess.STATUS_SEND_SENT)) {
                                    if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                        TimelineManager.insertTimeline(activity, this.taskH, true, false);
                                    } else {
                                        TimelineManager.insertTimeline(activity, this.taskH);
                                    }
                                }
//						    	try {
//						    		long logCounter = TaskLog.getCounterLog(activity);
//						        	long taskListCounter = ToDoList.getCounterTaskList(activity);
//						        	if(MainMenuActivity.mnLog!=null)
//						        		MainMenuActivity.mnLog.setCounter(String.valueOf(logCounter));
//						       		if(MainMenuActivity.mnTaskList!=null)
//						       			MainMenuActivity.mnTaskList.setCounter(String.valueOf(taskListCounter));
//						        	if(MainMenuActivity.menuAdapter!=null)
//						        		MainMenuActivity.menuAdapter.notifyDataSetChanged();
//						        	if(StatusSectionFragment.statusListAdapter!=null)
//						        		StatusSectionFragment.statusListAdapter.notifyDataSetChanged();
//								} catch (Exception e) {
//									// TODO: handle exception
//								}

                            }
                            intent.putExtras(extras);
                            activity.startActivity(intent);
                            activity.finish();
                        } else if (activity.getString(R.string.no_internet_connection).equals(result)) {
                            if (StatusTabFragment.handler != null)
                                StatusTabFragment.handler.sendEmptyMessage(0);

                            Bundle extras = new Bundle();
                            Intent intent = new Intent(activity.getApplicationContext(), SendResultActivity.class);
                            extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
                            extras.putString(Global.BUND_KEY_SEND_RESULT, "Failed");
                            extras.putString(Global.BUND_KEY_SEND_SIZE, this.size);
                            extras.putString(Global.BUND_KEY_SEND_TIME, this.sec);
                            extras.putString(Global.BUND_KEY_TASK_ID, result);
                            intent.putExtras(extras);
                            activity.startActivity(intent);
                            activity.finish();

                        } else {
                            TaskHDataAccess.addOrReplace(activity, this.taskH);

                            int imageLeft = AutoSendImageThread.countPendingImageBeforeUpload(activity, this.taskH.getUuid_task_h());
                            ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_PENDING, imageLeft);
                            if (StatusTabFragment.handler != null)
                                StatusTabFragment.handler.sendEmptyMessage(0);

                            Bundle extras = new Bundle();
                            Intent intent = new Intent(activity.getApplicationContext(), SendResultActivity.class);
                            extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
                            if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_verification_failed));
                            } else {
                                extras.putString(Global.BUND_KEY_SEND_RESULT, activity.getString(R.string.message_sending_failed));
                            }
                            extras.putString(Global.BUND_KEY_SEND_SIZE, this.size);
                            extras.putString(Global.BUND_KEY_SEND_TIME, this.sec);
                            extras.putString(Global.BUND_KEY_TASK_ID, "error code: " + result);
                            intent.putExtras(extras);
                            activity.startActivity(intent);
                            activity.finish();
                        }
                    }

                }
                //bong 8 apr 15 - penjagaan jika autoSend null dari mainMenuActivity
                try {
//                    if (MainMenuActivity.autoSendTask != null) {
//                        MainMenuActivity.autoSendTask.stopWaiting();
//                        MainMenuActivity.autoSendImage.stopWaiting();
//                    }
                    if (MainServices.autoSendTaskThread != null) {
                        MainServices.autoSendTaskThread.stopWaiting();
                        MainServices.autoSendImageThread.stopWaiting();
                        MainServices.taskListThread.stopWaiting();
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    // TODO: handle exception
                }
                Global.isManualSubmit = false;

            }
        }.execute();

    }

    @SuppressLint("StaticFieldLeak")
    @Override
    public void saveAndSendTaskOnBackground(final Context activity, final String taskId,
                                            boolean isPrintable, boolean finishActivity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            mNotifyManager = (NotificationManager) activity.getSystemService(Context.NOTIFICATION_SERVICE);
            mBuilder = new Notification.Builder(activity);
            mBuilder.setContentTitle(activity.getString(R.string.uploading_image))
                    .setContentText(activity.getString(R.string.progress_uploading))
                    .setSmallIcon(AutoSendImageThread.getNotificationUploadingIcon());
            inboxStyle = new Notification.BigTextStyle();
            inboxStyle.setBigContentTitle(activity.getString(R.string.uploading_image));
            inboxStyle.bigText(activity.getString(R.string.progress_uploading));
            mBuilder.setStyle(inboxStyle);
        }
        new AsyncTask<Void, Void, String>() {
            boolean isHaveImage = false;
            String uuidTaskFromServer = null;
            String oldUuidTask = null;
            private List<TaskD> listTaskD;
            private TaskH taskH;
            private String errMessage = null;
            private int errCode;
            private String sec;
            private String size;
            private String nTaskId;
            private boolean isDropTask;
            private String messageTask;

            @Override
            protected String doInBackground(Void... params) {
                String message = "";
                String result = null;
                Global.isManualSubmit = true;
                Global.isUploading = true;
                this.taskH = TaskHDataAccess.getOneTaskHeader(activity, taskId);

                this.taskH.getScheme();
                this.taskH.getUser();

                try {
                    listTaskD = TaskDDataAccess.getAll(activity, taskH.getUuid_task_h(), TaskDDataAccess.ALL_TASK);
                } catch (Exception e) {
                    FireCrash.log(e);
                }

                if (Tool.isInternetconnected(activity)) {
                    try {
                        if (MainServices.autoSendTaskThread != null) {
                            MainServices.autoSendTaskThread.requestWait();
                            MainServices.autoSendImageThread.requestWait();
                            MainServices.taskListThread.requestWait();
                        }
                    } catch (Exception e) {
                        FireCrash.log(e);
                        // TODO: handle exception
                    }

                    String uuidTaskH = taskH.getUuid_task_h();
                    String uuidUser = taskH.getUuid_user();

                    List<TaskD> taskDs = new ArrayList<TaskD>();
                    int i = 1;

                    try {
                        List<TaskD> taskd = TaskDDataAccess.getUnsentImageByTaskH(activity, uuidUser, uuidTaskH);
                        if (taskd != null && !taskd.isEmpty())
                            isHaveImage = true;
                    } catch (Exception e) {
                        FireCrash.log(e);
                        e.printStackTrace();
                    }
                    if (!isHaveImage) {
                        for (TaskD d : listTaskD) {
                            if (d.getImage() != null) {
                                isHaveImage = true;
                                break;
                            }
                        }
                    }

                    Map<String, QuestionSet> listSubmitLayerQuestion = new HashMap<>();
                    boolean isFinal = false;
                    for (TaskD d : listTaskD) {
                        QuestionSet question = QuestionSetDataAccess.getOne(activity, taskH.getUuid_scheme(), d.getQuestion_id(), d.getQuestion_group_id(), taskH.getForm_version());
                        if (Global.AT_SUBMIT_LAYER.equalsIgnoreCase(question.getAnswer_type())) {
                            listSubmitLayerQuestion.put(question.getQuestion_group_id(), question);
                        }
                        if (Global.TRUE_STRING.equals(d.getIs_visible())) {
                            if (isPartial(activity)) {
                                if (d.getImage() == null) {
                                    taskDs.add(d);
                                }
                            } else {
                                if (i == taskDs.size()) {
                                    d.setIs_final(Global.TRUE_STRING);
                                    isFinal = true;
                                }
                                taskDs.add(d);
                            }
                        }
                        i++;
                    }
                    if (!isPartial(activity)) {
                        TaskD d = taskDs.get(taskDs.size() - 1);
                        if (!d.getIs_final().equals(Global.TRUE_STRING)) {
                            d.setIs_final(Global.TRUE_STRING);
                            isFinal = true;
                        }
                    } else {
                        if (!isHaveImage) {
                            if (!taskDs.isEmpty()) {
                                TaskD d = taskDs.get(taskDs.size() - 1);
                                if (!d.getIs_final().equals(Global.TRUE_STRING)) {
                                    d.setIs_final(Global.TRUE_STRING);
                                    isFinal = true;
                                }
                            }
                        }
                    }

                    taskH.setSubmit_date(taskH.getSubmit_date());
                    taskH.getUser().setToken_id_fcm(null);

                    //14-08-2018: Nendi - Exclude Pilih Salah Satu
                    List<TaskD> excludeTaskDs = new ArrayList<>();
                    for (TaskD taskD2 : taskDs) {
                        if (taskD2.getText_answer() != null &&
                                taskD2.getText_answer().equalsIgnoreCase("Pilih Salah Satu")) {
                            excludeTaskDs.add(taskD2);
                        }
                    }

                    for (TaskD excludeTask : excludeTaskDs) {
                        taskDs.remove(excludeTask);
                    }
                    //-------------------

                    JsonRequestSubmitTask task = new JsonRequestSubmitTask();
                    task.setAudit(GlobalData.getSharedGlobalData().getAuditData());
                    task.addImeiAndroidIdToUnstructured();
                    task.setTaskH(taskH);
                    task.setTaskD(taskDs);

                    if(isFinal) {
                        //2022-04-16 Riska - tambahan untuk submit per layer
                        task = createFiltersForSubmitLayer(activity, task, listSubmitLayerQuestion);
                        //end
                    }

                    if (task.getTaskD() != null && !task.getTaskD().isEmpty()) {
                        String json = GsonHelper.toJson(task);
                        String url = GlobalData.getSharedGlobalData().getURL_SUBMITTASK();

                        if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                            url = GlobalData.getSharedGlobalData().getURL_SUBMITVERIFICATIONTASK();
                        } else {
                            url = GlobalData.getSharedGlobalData().getURL_SUBMITTASK();
                        }

                        if (!isHaveImage) {
                            message = "no image";
                        }

                        size = String.valueOf(json.getBytes().length);

                        boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                        boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                        HttpCryptedConnection httpConn = new HttpCryptedConnection(activity, encrypt, decrypt);
                        HttpConnectionResult serverResult = null;
                        Date startTime = Tool.getSystemDateTime();

                        try {
                            /*if(Global.IS_DEV) {
                                if (GlobalData.getSharedGlobalData().getURL_SUBMITVERIFICATIONTASK().equals(url)) {
                                    System.out.println("VERIFY VERIFICATION TASK");
                                } else {
                                    System.out.println("SUBMIT TASK");
                                }
                            }*/
                            serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
                            /*if(Global.IS_DEV)
                                System.out.println("Manual Send Task "+ taskH.getCustomer_name());*/
                        } catch (Exception e) {
                            FireCrash.log(e);
                            e.printStackTrace();
                            errMessage = e.getMessage();
                        }

                        if (serverResult != null && serverResult.isOK()) {
                            String resultvalue = serverResult.getResult();
                            JsonResponseSubmitTask responseSubmitTask = GsonHelper.fromJson(resultvalue, JsonResponseSubmitTask.class);
                            if (responseSubmitTask.getStatus().getCode() == 0) {
                                String status = responseSubmitTask.getResult();

                                uuidTaskFromServer = responseSubmitTask.getUuidTask();
                                oldUuidTask = this.taskH.getUuid_task_h();

                                //change to new uuid
                                if (!"".equals(uuidTaskFromServer) && uuidTaskFromServer != null) {
                                    TaskHDataAccess.deleteByUuid(activity, this.taskH.getUuid_user(), this.taskH.getUuid_task_h());
                                    this.taskH.setUuid_task_h(uuidTaskFromServer);
                                    TaskHDataAccess.addOrReplace(activity, this.taskH);
                                }

                                Date finishTime = Tool.getSystemDateTime();
                                long time = finishTime.getTime() - startTime.getTime();
                                sec = "0";
                                try {
                                    sec = String.valueOf((int) Math.ceil(time / 1000));
                                } catch (Exception e) {
                                    FireCrash.log(e);

                                }
                                if (status == null)
                                    status = "Success";
                                if (status.equalsIgnoreCase("Success")) {
                                    result = status;
                                    if (responseSubmitTask.getTaskId() != null) {
                                        nTaskId = responseSubmitTask.getTaskId();
                                        isDropTask = responseSubmitTask.isDropTask();
                                        messageTask = responseSubmitTask.getMessage();
                                        String appllication = GlobalData.getSharedGlobalData().getAuditData().getApplication();
                                        if (appllication == null)
                                            appllication = GlobalData.getSharedGlobalData().getApplication();
                                        if (Global.APPLICATION_COLLECTION.equalsIgnoreCase(appllication)) {
                                            if (responseSubmitTask.getCashOnHand() != null) {
                                                GlobalData.getSharedGlobalData().getUser().setCash_on_hand(responseSubmitTask.getCashOnHand());
                                                UserDataAccess.addOrReplace(activity, GlobalData.getSharedGlobalData().getUser());
                                            }
                                        }
                                    } else {
                                        nTaskId = activity.getString(R.string.message_no_task_id_from_server);
                                    }
                                } else {
                                    result = status;
                                }
                                if (taskH.getScheme().getIs_printable().equals(Global.TRUE_STRING)) {
                                    generatePrintResult(activity, taskH);
                                }
                            } else {
                                /*if (Global.IS_DEV) {
                                    if (responseSubmitTask.getStatus().getMessage() == null)
                                        System.out.println("AutoSendTaskThread server code :" + responseSubmitTask.getStatus().getCode());
                                    else
                                        System.out.println("AutoSendTaskThread server code :" + responseSubmitTask.getStatus().getCode() + ":" + responseSubmitTask.getStatus().getCode());
                                }*/
                                result = String.valueOf(responseSubmitTask.getStatus().getCode());
                                errMessage = responseSubmitTask.getStatus().getMessage();
                                errCode = responseSubmitTask.getStatus().getCode();
                            }
                        } else {
                            try {
                                errMessage = serverResult.getResult();
                            } catch (Exception e) {
                                FireCrash.log(e);
                            }
                        }

                        if (result != null && result.equalsIgnoreCase("Success")) {
                            if (nTaskId != null && nTaskId.length() > 0 && !nTaskId.contains("~")) {
                                List<TaskD> newlistTaskD = new ArrayList<TaskD>();
                                for (TaskD taskD : listTaskD) {
                                    if (taskD.getImage() == null)
                                        taskD.setIs_sent(Global.TRUE_STRING);
                                    else {
                                        if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                            taskD.setIs_sent(Global.TRUE_STRING);
                                            newlistTaskD.add(taskD);
                                        } else {
                                            if (TaskManager.isPartial(activity)) {
                                                taskD.setIs_sent(Global.FALSE_STRING);
                                            } else {
                                                taskD.setIs_sent(Global.TRUE_STRING);
                                            }
                                        }
                                    }

                                    //change to new uuid
                                    if (!"".equals(uuidTaskFromServer) && uuidTaskFromServer != null) {
                                        taskD.setUuid_task_h(uuidTaskFromServer);
                                    }
                                    TaskDDataAccess.addOrReplace(activity, taskD);
                                }

                                if (isDropTask) {
                                    taskH.setMessage(messageTask);
                                    taskH.setStatus(TaskHDataAccess.STATUS_SEND_DROP);
                                    TaskHDataAccess.update(activity,taskH);
                                    TimelineManager.insertTimeline(activity, taskH);
                                } else {
                                    int imageLeft = AutoSendImageThread.countPendingImageBeforeUpload(activity, taskH.getUuid_task_h());
                                    if (imageLeft > 0) {
                                        taskH.setStatus(TaskHDataAccess.STATUS_SEND_UPLOADING);
                                        ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_UPLOADING, imageLeft);
                                    } else {
                                        try {
                                            List<TaskD> taskd = TaskDDataAccess.getUnsentImageByTaskH(activity, taskH.getUuid_user(), taskH.getUuid_task_h());
                                            if (taskd != null && !taskd.isEmpty()) {
                                                taskH.setStatus(TaskHDataAccess.STATUS_SEND_UPLOADING);
                                                ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_UPLOADING, imageLeft);
                                            } else {
                                                taskH.setStatus(TaskHDataAccess.STATUS_SEND_SENT);

                                                //update flagging send task cae
                                                if ( Global.FORM_NAME_PROMISE_TO_SURVEY.equalsIgnoreCase(taskH.getScheme().getScheme_description())
                                                        && TaskHDataAccess.STATUS_SEND_FAILED.equalsIgnoreCase(taskH.getSend_task_promise_to_survey())) {
                                                    TaskHDataAccess.updateFlaggingSendTask(taskH, TaskHDataAccess.STATUS_SEND_SENT, activity);
                                                } else  if ( Global.FORM_NAME_PRE_SURVEY.equalsIgnoreCase(taskH.getScheme().getScheme_description())
                                                        && TaskHDataAccess.STATUS_SEND_FAILED.equalsIgnoreCase(taskH.getSend_task_presurvey())) {
                                                    TaskHDataAccess.updateFlaggingSendTask(taskH, TaskHDataAccess.STATUS_SEND_SENT, activity);
                                                }

                                                ToDoList.removeSurveyFromList(taskId);
                                            }
                                        } catch (Exception e) {
                                            FireCrash.log(e);
                                            taskH.setStatus(TaskHDataAccess.STATUS_SEND_SENT);
                                            //update flagging send task cae
                                            if ( Global.FORM_NAME_PROMISE_TO_SURVEY.equalsIgnoreCase(taskH.getScheme().getScheme_description())
                                                    && TaskHDataAccess.STATUS_SEND_FAILED.equalsIgnoreCase(taskH.getSend_task_promise_to_survey())) {
                                                TaskHDataAccess.updateFlaggingSendTask(taskH, TaskHDataAccess.STATUS_SEND_SENT, activity);
                                            } else  if ( Global.FORM_NAME_PRE_SURVEY.equalsIgnoreCase(taskH.getScheme().getScheme_description())
                                                    && TaskHDataAccess.STATUS_SEND_FAILED.equalsIgnoreCase(taskH.getSend_task_presurvey())) {
                                                TaskHDataAccess.updateFlaggingSendTask(taskH, TaskHDataAccess.STATUS_SEND_SENT, activity);
                                            }

                                            ToDoList.removeSurveyFromList(taskId);
                                            ACRA.getErrorReporter().putCustomData("errorSubmit", e.getMessage());
                                            ACRA.getErrorReporter().handleSilentException(new Exception("Just for the stacktrace, kena exception saat auto Send Task"));
                                        }
                                    }
                                }
                                taskH.setSubmit_date(taskH.getSubmit_date());
                                taskH.setSubmit_duration(sec);
                                taskH.setSubmit_size(size);
                                taskH.setSubmit_result(result);
                                taskH.setTask_id(nTaskId);
                                taskH.setLast_saved_question(1);
                                TaskHDataAccess.addOrReplace(activity, taskH);
                                if (taskH.getStatus().equals(TaskHDataAccess.STATUS_SEND_SENT)) {
                                    if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                        if (MainMenuActivity.mnSVYApproval != null) {
                                            TaskHDataAccess.deleteWithRelation(activity, taskH);
                                            String newUUIDTaskH = Tool.getUUID();
                                            taskH.setUuid_task_h(newUUIDTaskH);
                                            TaskHDataAccess.addOrReplace(activity, taskH);
                                            for (TaskD d : newlistTaskD) {
                                                d.setTaskH(taskH);
                                                d.setUuid_task_d(Tool.getUUID());
                                                TaskDDataAccess.addOrReplace(activity, d);
                                            }
                                        }
                                    }
                                    if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                        TimelineManager.insertTimeline(activity, taskH, true, false);
                                    } else {
                                        taskH.setMessage("");
                                        TaskHDataAccess.addOrReplace(activity, taskH);
                                        TimelineManager.insertTimeline(activity, taskH);
                                    }
                                } else if (taskH.getStatus().equals(TaskHDataAccess.STATUS_SEND_UPLOADING)) {
                                    taskH.setMessage("");
                                    TaskHDataAccess.addOrReplace(activity, taskH);
                                    TimelineManager.insertTimeline(activity, taskH);
                                }
                            }
                        } else {
                            mNotifyManager.cancelAll();
                            taskH.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);
                            if (result != null && result.equals(STATUS_TASK_NOT_MAPPING)) {
                                taskH.setMessage(activity.getString(R.string.message_task_not_mapping) + " - Error (" + errCode + ")");
                            } else if (result != null && result.equals(STATUS_TASK_DELETED)) {
                                taskH.setMessage(activity.getString(R.string.message_sending_deleted) + " - Error (" + errCode + ")");
                            } else if (result != null && result.equals(STATUS_IMEI_FAILED)) {
                                taskH.setMessage(activity.getString(R.string.message_imei_not_registered) + " - Error (" + errCode + ")");
                            } else {
                                taskH.setMessage(errMessage + " - Error (" + errCode + ")");
                            }
                            TaskHDataAccess.addOrReplace(activity, taskH);
                            TimelineManager.insertTimeline(activity, taskH);
                            Global.isManualSubmit = false;
                        }
                        Handler handler = new Handler(Looper.getMainLooper());
                        handler.post(new Runnable() {
                            public void run() {
                                // UI code goes here
                                try {
                                    try {
                                        MainMenuActivity.setDrawerCounter();
                                    } catch (Exception e) {
                                        FireCrash.log(e);
                                        // TODO: handle exception
                                    }
                                    if (Timeline_Activity.timelineHandler != null)
                                        Timeline_Activity.timelineHandler.sendEmptyMessage(0);
                                } catch (Exception e) {
                                    FireCrash.log(e);
                                    // TODO: handle exception
                                }
                            }
                        });
                        try {
                            if (taskH.getStatus().equals(TaskHDataAccess.STATUS_SEND_UPLOADING)) {
                                try {
                                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {

                                        Handler handler2 = new Handler(Looper.getMainLooper());
                                        handler2.post(new Runnable() {
                                            public void run() {
                                                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                                                    mNotifyManager = (NotificationManager) activity.getSystemService(Context.NOTIFICATION_SERVICE);
                                                    mBuilder = new Notification.Builder(activity);
                                                    mBuilder.setContentTitle(activity.getString(R.string.uploading_image))
                                                            .setContentText(activity.getString(R.string.progress_uploading))
                                                            .setSmallIcon(AutoSendImageThread.getNotificationUploadingIcon());
                                                    inboxStyle = new Notification.BigTextStyle();
                                                    inboxStyle.setBigContentTitle(activity.getString(R.string.uploading_image));
                                                    inboxStyle.bigText(activity.getString(R.string.progress_uploading));
                                                    mBuilder.setStyle(inboxStyle);
                                                    mBuilder.setProgress(0, 0, true);
                                                    mBuilder.setOngoing(true);
                                                    if (android.os.Build.VERSION.SDK_INT >=android.os.Build.VERSION_CODES.O) {
                                                        String channelId = "channel_task_send";
                                                        String channelName = "Task Send";
                                                        int importance = NotificationManager.IMPORTANCE_HIGH;
                                                        NotificationChannel notificationChannel = new NotificationChannel(channelId, channelName, importance);
                                                        if (null == mNotifyManager) {
                                                            throw new NullPointerException("mNotifyManager is null");
                                                        }
                                                        mBuilder.setChannelId(channelId);
                                                        mNotifyManager.createNotificationChannel(notificationChannel);
                                                    }
                                                    mNotifyManager.notify(4, mBuilder.build());
                                                }
                                            }
                                        });
                                    }
                                    try {
                                        List<TaskD> taskd = TaskDDataAccess.getUnsentImageByTaskH(activity, taskH.getUuid_user(), taskH.getUuid_task_h());
                                        message = UploadImage(activity, taskd);
                                    } catch (Exception e) {
                                        FireCrash.log(e);
                                        Global.isManualUploading = false;
                                        Global.isUploading = false;
                                    }
                                } catch (Exception e) {
                                    FireCrash.log(e);
                                    e.printStackTrace();
                                }
                            }
                        } catch (Exception e) {
                            FireCrash.log(e);
                            // TODO: handle exception
                        }
                    }
                } else {
                    TaskHDataAccess.updateFlaggingSendTask(taskH, TaskHDataAccess.STATUS_SEND_FAILED, activity);
                    TaskHDataAccess.processToOpenNextTask(taskH, activity);
                }
                try {
                    if (MainServices.autoSendTaskThread != null) {
                        MainServices.autoSendTaskThread.stopWaiting();
                        MainServices.autoSendImageThread.stopWaiting();
                        MainServices.taskListThread.stopWaiting();
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    // TODO: handle exception
                }

                Global.isUploading = false;
                return message;
            }

            @Override
            protected void onPostExecute(final String message) {
                try {
                    if (MainServices.autoSendTaskThread != null) {
                        MainServices.autoSendTaskThread.stopWaiting();
                        MainServices.autoSendImageThread.stopWaiting();
                        MainServices.taskListThread.stopWaiting();
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    // TODO: handle exception
                }
                try {
                    if (message == null || message.length() == 0) {
                        String status = taskH.getStatus();
                        if (status.equals(TaskHDataAccess.STATUS_SEND_PENDING)) {
                            mNotifyManager.cancelAll();
                        } else {
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                                Handler handler = new Handler(Looper.getMainLooper());
                                handler.post(new Runnable() {
                                    public void run() {
                                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                                            String counter = activity.getString(R.string.upload_complete);
                                            String title = activity.getString(R.string.uploading_image_title, taskH.getCustomer_name());
                                            int progress = TaskDDataAccess.getProgressUploadImage(activity, taskH.getUuid_task_h());
                                            String speedKbps = getSpeedKbps();
                                            mBuilder.setContentTitle(title + speedKbps);
                                            mBuilder.setContentText(String.valueOf(progress)+"% "+counter)
                                                    // Removes the progress bar
                                                    .setProgress(0, 0, false);
                                            mBuilder.setOngoing(false);
                                            inboxStyle.setBigContentTitle(title + speedKbps);
                                            inboxStyle.bigText(String.valueOf(progress)+"% "+counter);
                                            mBuilder.setStyle(inboxStyle);
                                            if (android.os.Build.VERSION.SDK_INT >=android.os.Build.VERSION_CODES.O) {
                                                String channelId = "channel_task_send";
                                                String channelName = "Task Send";
                                                int importance = NotificationManager.IMPORTANCE_HIGH;
                                                NotificationChannel notificationChannel = new NotificationChannel(channelId, channelName, importance);
                                                if (null == mNotifyManager) {
                                                    throw new NullPointerException("mNotifyManager is null");
                                                }
                                                mBuilder.setChannelId(channelId);
                                                mNotifyManager.createNotificationChannel(notificationChannel);
                                            }
                                            mNotifyManager.notify(4, mBuilder.build());
                                        }
                                    }
                                });
                            }
                        }
                    } else if (message.equals("no image")) {
                        mNotifyManager.cancelAll();
                    } else {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                            Handler handler = new Handler(Looper.getMainLooper());
                            handler.post(new Runnable() {
                                public void run() {
                                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                                        String title = activity.getString(R.string.uploading_image_title, taskH.getCustomer_name());
                                        int progress = TaskDDataAccess.getProgressUploadImage(activity, taskH.getUuid_task_h());
                                        mBuilder.setContentTitle(title);
                                        mBuilder.setContentText(message)
                                                // Removes the progress bar
                                                .setProgress(0, 0, false);
                                        mBuilder.setOngoing(false);
                                        inboxStyle.setBigContentTitle(title);
                                        inboxStyle.bigText(String.valueOf(progress)+"% "+message);
                                        mBuilder.setStyle(inboxStyle);
                                        if (android.os.Build.VERSION.SDK_INT >=android.os.Build.VERSION_CODES.O) {
                                            String channelId = "channel_task_send";
                                            String channelName = "Task Send";
                                            int importance = NotificationManager.IMPORTANCE_HIGH;
                                            NotificationChannel notificationChannel = new NotificationChannel(channelId, channelName, importance);
                                            if (null == mNotifyManager) {
                                                throw new NullPointerException("mNotifyManager is null");
                                            }
                                            mBuilder.setChannelId(channelId);
                                            mNotifyManager.createNotificationChannel(notificationChannel);
                                        }
                                        mNotifyManager.notify(4, mBuilder.build());
                                    }
                                    // To show message on list task
                                    if (errMessage != null) {
                                        taskH.setMessage(errMessage + " - Error (" + errCode + ")");
                                        TimelineManager.insertTimeline(activity, taskH);
                                    }
                                }
                            });
                        }
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    Global.isManualUploading = false;
                }
                Global.isManualSubmit = false;
            }
        }.execute();
    }

    @SuppressLint("StaticFieldLeak")
    @Override
    public void saveAndSendTaskOnBackground(final Activity activity, final int mode,
                                            final SurveyHeaderBean header, final List<QuestionBean> listOfQuestions,
                                            final LinkedHashMap<String, QuestionBean> listAllQuestions) {

        final WeakReference<Activity> activityRef = new WeakReference<Activity>(activity);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            mNotifyManager = (NotificationManager) activity.getSystemService(Context.NOTIFICATION_SERVICE);
            mBuilder = new Notification.Builder(activity);
            mBuilder.setContentTitle(activity.getString(R.string.uploading_image))
                    .setContentText(activity.getString(R.string.progress_uploading))
                    .setSmallIcon(AutoSendImageThread.getNotificationUploadingIcon());
            inboxStyle = new Notification.BigTextStyle();
            inboxStyle.setBigContentTitle(activity.getString(R.string.uploading_image));
            inboxStyle.bigText(activity.getString(R.string.progress_uploading));
            mBuilder.setStyle(inboxStyle);
        }
        new AsyncTask<Void, Void, String>() {
            protected ProgressDialog progressDialog;
            protected List<TaskD> listTaskD;
            protected TaskH taskH;
            protected String errMessage = null;
            protected int errCode;
            protected String sec;
            protected String size;
            String taskId = null;
            String uuidTaskFromServer = null;
            String oldUuidTask = null;
            private String messageWait = activity.getString(R.string.progressSend);
            private boolean isDropTask;
            private String messageTask;

            @Override
            protected void onPreExecute() {
//                progressDialog = ProgressDialog.show(activity, "", this.messageWait, true);
            }

            @Override
            protected String doInBackground(Void... arg0) {
                // TODO Auto-generated method stub
                //bong 8 apr 15 - penjagaan jika autoSend null dari mainMenuActivity
                String message = "";
                String result = null;
                Global.isManualSubmit = true;
                Global.isUploading = true;
                try {
//                    if(MainMenuActivity.autoSendTask!=null){
//                        MainMenuActivity.autoSendTask.requestWait();
//                        MainMenuActivity.autoSendImage.requestWait();
//                    }
                    if (MainServices.autoSendTaskThread != null) {
                        MainServices.autoSendTaskThread.requestWait();
                        MainServices.autoSendImageThread.requestWait();
                        MainServices.taskListThread.requestWait();
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    // TODO: handle exception
                }
                saveTask(activityRef.get(), mode, header, listOfQuestions, listOfQuestions.get(listOfQuestions.size()-1).getUuid_question_set(),  true);

                if (Tool.isInternetconnected(activityRef.get())) {
                    this.taskH = header.getTaskH();
                    this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);
                    taskId = this.taskH.getTask_id();
                    String nTaskId = taskId;
                    try {
                        listTaskD = TaskDDataAccess.getAll(activity, header.getUuid_task_h(), TaskDDataAccess.ALL_TASK);
                    } catch (Exception e) {
                        FireCrash.log(e);
                    }
                    //minimalisir data
                    String uuidTaskH = taskH.getUuid_task_h();
                    String uuidUser = taskH.getUuid_user();
                    boolean isHaveImage = false;
                    List<TaskD> taskDs = new ArrayList<TaskD>();
                    int i = 1;

                    try {
                        List<TaskD> taskd = TaskDDataAccess.getUnsentImageByTaskH(activity, uuidUser, uuidTaskH);
                        if (taskd != null && !taskd.isEmpty())
                            isHaveImage = true;
                    } catch (Exception e) {
                        FireCrash.log(e);
                        e.printStackTrace();
                    }
                    if (!isHaveImage) {
                        for (TaskD d : listTaskD) {
                            if (d.getImage() != null) {
                                isHaveImage = true;
                                break;
                            }
                        }
                    }
                    Map<String, QuestionSet> listSubmitLayerQuestion = new HashMap<>();
                    boolean isFinal = false;
                    for (TaskD d : listTaskD) {
                        QuestionSet question = QuestionSetDataAccess.getOne(activity, taskH.getUuid_scheme(), d.getQuestion_id(), d.getQuestion_group_id(), taskH.getForm_version());
                        if (Global.AT_SUBMIT_LAYER.equalsIgnoreCase(question.getAnswer_type())) {
                            listSubmitLayerQuestion.put(question.getQuestion_group_id(), question);
                        }
                        if (d.getIs_visible().equals(Global.TRUE_STRING)) {
                            if (isPartial(activity)) {
                                if (d.getImage() == null) {
                                    taskDs.add(d);
                                }
                            } else {
                                if (i == taskDs.size()) {
                                    d.setIs_final(Global.TRUE_STRING);
                                    isFinal = true;
                                }
                                taskDs.add(d);
                            }
                        }

                        i++;
                    }

                    if (!isPartial(activity)) {
                        TaskD d = taskDs.get(taskDs.size() - 1);
                        if (!d.getIs_final().equals(Global.TRUE_STRING)) {
                            d.setIs_final(Global.TRUE_STRING);
                            isFinal = true;
                        }
                    } else {
                        if (!isHaveImage) {
                            if (!taskDs.isEmpty()) {
                                TaskD d = taskDs.get(taskDs.size() - 1);
                                if (!d.getIs_final().equals(Global.TRUE_STRING)) {
                                    d.setIs_final(Global.TRUE_STRING);
                                    isFinal = true;
                                }
                            }
                        }
                    }

                    this.taskH.setSubmit_date(taskH.getSubmit_date());
                    this.taskH.getUser().setToken_id_fcm(null);
                    //kamil 27/07/2017 permintaan mas raymond tambah lokasi untuk kirim taskH ke server
                    LocationInfo loc = Global.LTM.getCurrentLocation(Global.FLAG_LOCATION_TRACKING);
                    if ("null".equalsIgnoreCase(loc.getLatitude())) {
                        this.taskH.setLatitude("");
                    } else {
                        this.taskH.setLatitude(loc.getLatitude());
                    }

                    if ("null".equalsIgnoreCase(loc.getLongitude())) {
                        this.taskH.setLongitude("");
                    } else {
                        this.taskH.setLongitude(loc.getLongitude());
                    }

                    //14-08-2018: Nendi - Exclude Pilih Salah Satu
                    List<TaskD> excludeTaskDs = new ArrayList<>();
                    for (TaskD taskD2 : taskDs) {
                        if (taskD2.getText_answer() != null &&
                                taskD2.getText_answer().equalsIgnoreCase("Pilih Salah Satu")) {
                            excludeTaskDs.add(taskD2);
                        }
                    }

                    for (TaskD excludeTask : excludeTaskDs) {
                        taskDs.remove(excludeTask);
                    }

                    JsonRequestSubmitTask task = new JsonRequestSubmitTask();
                    task.setAudit(GlobalData.getSharedGlobalData().getAuditData());
                    task.addImeiAndroidIdToUnstructured();
                    task.setTaskH(taskH);
                    task.setTaskD(taskDs);

                    if (isFinal) {
                        //2022-04-16 Riska - tambahan untuk submit per layer
                        task = createFiltersForSubmitLayer(activity, task, listSubmitLayerQuestion);
                        //end
                    }

                    if (task.getTaskD() != null && !task.getTaskD().isEmpty()) {
                        String json = GsonHelper.toJson(task);
                        String url = GlobalData.getSharedGlobalData().getURL_SUBMITTASK();

                        if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                            url = GlobalData.getSharedGlobalData().getURL_SUBMITVERIFICATIONTASK();
                        } else {
                            url = GlobalData.getSharedGlobalData().getURL_SUBMITTASK();
                        }

                        if (!isHaveImage) {
                            message = "no image";
                        }

                        size = String.valueOf(json.getBytes().length);

                        boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                        boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                        HttpCryptedConnection httpConn = new HttpCryptedConnection(activity, encrypt, decrypt);
                        HttpConnectionResult serverResult = null;
                        Date startTime = Tool.getSystemDateTime();

                        try {
                            if (Global.IS_DEV) {
                                if (GlobalData.getSharedGlobalData().getURL_SUBMITVERIFICATIONTASK().equals(url)) {
                                    Logger.i("INFO", "VERIFY VERIFICATION TASK");
                                } else {
                                    Logger.i("INFO", "SUBMIT TASK");
                                }
                            }
                            serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
                            if (Global.IS_DEV)
                                Logger.i("INFO", "Manual Send Task " + taskH.getCustomer_name());
                        } catch (Exception e) {
                            FireCrash.log(e);
                            e.printStackTrace();
                            errMessage = e.getMessage();
                        }

                        if (serverResult != null && serverResult.isOK()) {
                            String resultvalue = serverResult
                                    .getResult();
                            JsonResponseSubmitTask responseSubmitTask = GsonHelper.fromJson(resultvalue, JsonResponseSubmitTask.class);
                            if (responseSubmitTask.getStatus()
                                    .getCode() == 0) {
                                String status = responseSubmitTask.getResult();
                                uuidTaskFromServer = responseSubmitTask.getUuidTask();
                                oldUuidTask = this.taskH.getUuid_task_h();

                                //change to new uuid
                                if (!"".equals(uuidTaskFromServer) && uuidTaskFromServer != null) {
                                    TaskHDataAccess.deleteByUuid(activity, this.taskH.getUuid_user(), this.taskH.getUuid_task_h());
                                    this.taskH.setUuid_task_h(uuidTaskFromServer);
                                    TaskHDataAccess.addOrReplace(activity, this.taskH);
                                }

                                Date finishTime = Tool.getSystemDateTime();
                                long time = finishTime.getTime() - startTime.getTime();
                                sec = String.valueOf((int) Math.ceil(time / 1000));
                                if (status == null)
                                    status = "Success";
                                if (status.equalsIgnoreCase("Success")) {
                                    result = status;
                                    if (responseSubmitTask.getTaskId() != null) {
                                        nTaskId = responseSubmitTask.getTaskId();
                                        isDropTask = responseSubmitTask.isDropTask();
                                        messageTask = responseSubmitTask.getMessage();
                                        String appllication = GlobalData.getSharedGlobalData().getAuditData().getApplication();
                                        if (appllication == null)
                                            appllication = GlobalData.getSharedGlobalData().getApplication();
                                        if (Global.APPLICATION_COLLECTION.equalsIgnoreCase(appllication)) {
                                            if (responseSubmitTask.getCashOnHand() != null) {
                                                GlobalData.getSharedGlobalData().getUser().setCash_on_hand(responseSubmitTask.getCashOnHand());
                                                UserDataAccess.addOrReplace(activity, GlobalData.getSharedGlobalData().getUser());
                                            }
                                        }
                                    } else {
                                        nTaskId = activity.getString(R.string.message_no_task_id_from_server);
                                    }
                                } else {
                                    result = status;
                                }
                                if (taskH.getScheme().getIs_printable().equals(Global.TRUE_STRING)) {
                                    generatePrintResult(activity, taskH);
                                }
                            } else {
                                if (Global.IS_DEV) {
                                    if (responseSubmitTask.getStatus().getMessage() == null)
                                        Logger.i("INFO", "AutoSendTaskThread server code :" + responseSubmitTask.getStatus().getCode());
                                    else
                                        Logger.i("INFO", "AutoSendTaskThread server code :" + responseSubmitTask.getStatus().getCode() + ":" + responseSubmitTask.getStatus().getCode());
                                }
                                result = String.valueOf(responseSubmitTask.getStatus().getCode());
                                errMessage = responseSubmitTask.getStatus().getMessage();
                                errCode = responseSubmitTask.getStatus().getCode();
                                TaskHDataAccess.addOrReplace(activity, this.taskH);
                            }
                        }else{
                            TaskHDataAccess.addOrReplace(activity, this.taskH);
                        }

                        if (result != null && result.equalsIgnoreCase("Success")) {
                            if (nTaskId != null && nTaskId.length() > 0 && !nTaskId.contains("~")) {
                                List<TaskD> newlistTaskD = new ArrayList<TaskD>();
                                for (TaskD taskD : listTaskD) {
                                    if (taskD.getImage() == null)
                                        taskD.setIs_sent(Global.TRUE_STRING);
                                    else {
                                        if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                            taskD.setIs_sent(Global.TRUE_STRING);
                                            newlistTaskD.add(taskD);
                                        } else {
                                            if (TaskManager.isPartial(activity)) {
                                                taskD.setIs_sent(Global.FALSE_STRING);
                                            } else {
                                                taskD.setIs_sent(Global.TRUE_STRING);
                                            }
                                        }
                                    }

                                    //change to new uuid
                                    if (!"".equals(uuidTaskFromServer) && uuidTaskFromServer != null) {
                                        taskD.setUuid_task_h(uuidTaskFromServer);
                                    }
                                    TaskDDataAccess.addOrReplace(activity, taskD);
                                }
                                if (isDropTask) {
                                    taskH.setMessage(messageTask);
                                    taskH.setStatus(TaskHDataAccess.STATUS_SEND_DROP);
                                    TaskHDataAccess.update(activity,taskH);
                                    TimelineManager.insertTimeline(activity, taskH);
                                } else {
                                    int imageLeft = AutoSendImageThread.countPendingImageBeforeUpload(activity, taskH.getUuid_task_h());
                                    if (imageLeft > 0) {
                                        taskH.setStatus(TaskHDataAccess.STATUS_SEND_UPLOADING);
                                        ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_UPLOADING, imageLeft);
                                    } else {
                                        try {
                                            List<TaskD> taskd = TaskDDataAccess.getUnsentImageByTaskH(activity, taskH.getUuid_user(), taskH.getUuid_task_h());
                                            if (taskd != null && !taskd.isEmpty()) {
                                                taskH.setStatus(TaskHDataAccess.STATUS_SEND_UPLOADING);
                                                ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_UPLOADING, imageLeft);
                                            } else {
                                                taskH.setStatus(TaskHDataAccess.STATUS_SEND_SENT);

                                                //update flagging send task cae
                                                if ( Global.FORM_NAME_PROMISE_TO_SURVEY.equalsIgnoreCase(taskH.getScheme().getScheme_description())
                                                        && TaskHDataAccess.STATUS_SEND_FAILED.equalsIgnoreCase(taskH.getSend_task_promise_to_survey())) {
                                                    TaskHDataAccess.updateFlaggingSendTask(taskH, TaskHDataAccess.STATUS_SEND_SENT, activity);
                                                } else  if ( Global.FORM_NAME_PRE_SURVEY.equalsIgnoreCase(taskH.getScheme().getScheme_description())
                                                        && TaskHDataAccess.STATUS_SEND_FAILED.equalsIgnoreCase(taskH.getSend_task_presurvey())) {
                                                    TaskHDataAccess.updateFlaggingSendTask(taskH, TaskHDataAccess.STATUS_SEND_SENT, activity);
                                                }

                                                ToDoList.removeSurveyFromList(taskId);
                                            }
                                        } catch (Exception e) {
                                            FireCrash.log(e);
                                            taskH.setStatus(TaskHDataAccess.STATUS_SEND_SENT);
                                            //update flagging send task cae
                                            if ( Global.FORM_NAME_PROMISE_TO_SURVEY.equalsIgnoreCase(taskH.getScheme().getScheme_description())
                                                    && TaskHDataAccess.STATUS_SEND_FAILED.equalsIgnoreCase(taskH.getSend_task_promise_to_survey())) {
                                                TaskHDataAccess.updateFlaggingSendTask(taskH, TaskHDataAccess.STATUS_SEND_SENT, activity);
                                            } else  if ( Global.FORM_NAME_PRE_SURVEY.equalsIgnoreCase(taskH.getScheme().getScheme_description())
                                                    && TaskHDataAccess.STATUS_SEND_FAILED.equalsIgnoreCase(taskH.getSend_task_presurvey())) {
                                                TaskHDataAccess.updateFlaggingSendTask(taskH, TaskHDataAccess.STATUS_SEND_SENT, activity);
                                            }

                                            ToDoList.removeSurveyFromList(taskId);
                                            ACRA.getErrorReporter().putCustomData("errorSubmit", e.getMessage());
                                            ACRA.getErrorReporter().handleSilentException(new Exception("Just for the stacktrace, kena exception saat auto Send Task"));
                                        }
                                    }
                                }
                                taskH.setSubmit_date(taskH.getSubmit_date());
                                taskH.setSubmit_duration(sec);
                                taskH.setSubmit_size(size);
                                taskH.setSubmit_result(result);
                                taskH.setTask_id(nTaskId);
                                taskH.setLast_saved_question(1);
                                TaskHDataAccess.addOrReplace(activity, taskH);
                                if (taskH.getStatus().equals(TaskHDataAccess.STATUS_SEND_SENT)) {
                                    if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                        if (MainMenuActivity.mnSVYApproval != null) {
                                            TaskHDataAccess.deleteWithRelation(activity, taskH);
                                            String newUUIDTaskH = Tool.getUUID();
                                            taskH.setUuid_task_h(newUUIDTaskH);
                                            TaskHDataAccess.addOrReplace(activity, taskH);
                                            for (TaskD d : newlistTaskD) {
                                                d.setTaskH(taskH);
                                                d.setUuid_task_d(Tool.getUUID());
                                                TaskDDataAccess.addOrReplace(activity, d);
                                            }
                                        }
                                    }
                                    if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                        TimelineManager.insertTimeline(activity, taskH, true, false);
                                    } else {
                                        TimelineManager.insertTimeline(activity, taskH);
                                    }
                                } else if (taskH.getStatus().equals(TaskHDataAccess.STATUS_SEND_UPLOADING)) {
                                    TimelineManager.insertTimeline(activity, taskH);
                                }
                            }
                        }
                        Handler handler = new Handler(Looper.getMainLooper());
                        handler.post(new Runnable() {
                            public void run() {
                                // UI code goes here
                                try {
                                    /*if (MainMenuActivity.mnTaskList != null)
                                        MainMenuActivity.mnTaskList.setCounter(String
                                                .valueOf(ToDoList
                                                        .getCounterTaskList(activity)));
                                    if (MainMenuActivity.mnLog != null)
                                        MainMenuActivity.mnLog
                                                .setCounter(String.valueOf(TaskLog
                                                        .getCounterLog(activity)));
                                    if (MainMenuActivity.menuAdapter != null)
                                        MainMenuActivity.menuAdapter.notifyDataSetChanged();*/
                                    try {
                                        MainMenuActivity.setDrawerCounter();
                                    } catch (Exception e) {
                                        FireCrash.log(e);
                                        // TODO: handle exception
                                    }
                                    viewAdapter.notifyDataSetChanged();
//                                long taskListCounter = ToDoList.getAllCounter(activity);
//                                Timeline_Activity.query.id(R.id.txtJumlahOutstanding).text(String.valueOf(taskListCounter));
                                    if (Timeline_Activity.timelineHandler != null)
                                        Timeline_Activity.timelineHandler.sendEmptyMessage(0);
                                } catch (Exception e) {
                                    FireCrash.log(e);
                                    // TODO: handle exception
                                }
                            }
                        });
                        try {
                            if (taskH.getStatus().equals(TaskHDataAccess.STATUS_SEND_UPLOADING)) {
                                try {
                                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {

                                        Handler handler2 = new Handler(Looper.getMainLooper());
                                        handler2.post(new Runnable() {
                                            public void run() {
                                                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                                                    mNotifyManager = (NotificationManager) activity.getSystemService(Context.NOTIFICATION_SERVICE);
                                                    mBuilder = new Notification.Builder(activity);
                                                    mBuilder.setContentTitle(activity.getString(R.string.uploading_image))
                                                            .setContentText(activity.getString(R.string.progress_uploading))
                                                            .setSmallIcon(AutoSendImageThread.getNotificationUploadingIcon());
                                                    inboxStyle = new Notification.BigTextStyle();
                                                    inboxStyle.setBigContentTitle(activity.getString(R.string.uploading_image));
                                                    inboxStyle.bigText(activity.getString(R.string.progress_uploading));
                                                    mBuilder.setStyle(inboxStyle);
                                                    mBuilder.setProgress(0, 0, true);
                                                    mBuilder.setOngoing(true);

                                                    if (android.os.Build.VERSION.SDK_INT >=android.os.Build.VERSION_CODES.O) {
                                                        String channelId = "channel_task_send";
                                                        String channelName = "Task Send";
                                                        int importance = NotificationManager.IMPORTANCE_HIGH;
                                                        NotificationChannel notificationChannel = new NotificationChannel(channelId, channelName, importance);
                                                        if (null == mNotifyManager) {
                                                            throw new NullPointerException("mNotifyManager is null");
                                                        }
                                                        mBuilder.setChannelId(channelId);
                                                        mNotifyManager.createNotificationChannel(notificationChannel);
                                                    }

                                                    mNotifyManager.notify(4, mBuilder.build());
                                                }
                                            }
                                        });
                                    }
                                    try {
                                        List<TaskD> taskd = TaskDDataAccess.getUnsentImageByTaskH(activity, taskH.getUuid_user(), taskH.getUuid_task_h());
                                        message = UploadImage(activity, taskd);
                                    } catch (Exception e) {
                                        FireCrash.log(e);
                                        Global.isManualUploading = false;
                                        Global.isUploading = false;
                                    }
                                } catch (Exception e) {
                                    FireCrash.log(e);
                                    e.printStackTrace();
                                }
                            }
//                        Global.isManualSendingRunning = false;
                        } catch (Exception e) {
                            FireCrash.log(e);
                            // TODO: handle exception
                        }

                        if (errMessage != null) {
                            mNotifyManager.cancelAll();
                            this.taskH = header.getTaskH();
                            this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);
                            if (result != null && result.equals(STATUS_TASK_NOT_MAPPING)) {
                                //message = activity.getString(R.string.message_task_not_mapping);
                                this.taskH.setMessage(activity.getString(R.string.message_task_not_mapping) + " - Error (" + errCode + ")");
                            } else if (result != null && result.equals(STATUS_TASK_DELETED)) {
                                //message = activity.getString(R.string.message_sending_deleted);
                                this.taskH.setMessage(activity.getString(R.string.message_sending_deleted) + " - Error (" + errCode + ")");
                            } else if (result != null && result.equals(STATUS_IMEI_FAILED)) {
                                //message = activity.getString(R.string.message_imei_not_registered);
                                this.taskH.setMessage(activity.getString(R.string.message_imei_not_registered) + " - Error (" + errCode + ")");
                            } else {
                                this.taskH.setMessage(errMessage + " - Error (" + errCode + ")");
                            }
                            TaskHDataAccess.addOrReplace(activity, this.taskH);
                            TimelineManager.insertTimeline(activity, taskH);
                        }
                    } else {
                        mNotifyManager.cancelAll();
                        this.taskH = header.getTaskH();
                        this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);
                        //message = activity.getString(R.string.taskd_was_gone, taskH.getCustomer_name());
                        this.taskH.setMessage(activity.getString(R.string.taskd_was_gone, taskH.getCustomer_name()) + " - Error (" + errCode + ")");
                        TaskHDataAccess.addOrReplace(activity, taskH);
                        Global.isManualSubmit = false;

                        TimelineManager.insertTimeline(activity, taskH);
                    }
                } else if (!Tool.isInternetconnected(activity)) {
                    mNotifyManager.cancelAll();
                    this.taskH = header.getTaskH();
                    //message = activity.getString(R.string.no_internet_connection);
                    this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);
                    this.taskH.setMessage(activity.getString(R.string.no_internet_connection));
                    TaskHDataAccess.addOrReplace(activity, this.taskH);
                    TimelineManager.insertTimeline(activity, taskH);

                    // Adding offline tasks drop mechanism (2022-07-18)
                    boolean isDropTask = deletingTaskOffline(activity, this.taskH, listOfQuestions);

                    if(!isDropTask) {
                        // Adding code for offline tasks (2022-07-14)
                        try {
                            matchingAnswersTaskOffline(activity, this.taskH, listAllQuestions);
                            TaskHDataAccess.updateFlaggingSendTask(taskH, TaskHDataAccess.STATUS_SEND_FAILED, activity);
                            TaskHDataAccess.processToOpenNextTask(taskH, activity);
                        } catch (Exception ex) {
                            if(Global.IS_DEV) {
                                ex.printStackTrace();
                            }
                            this.taskH.setMessage(activity.getString(R.string.message_failed_mapping_task));
                            TimelineManager.insertTimeline(activity, taskH);
                            this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SAVEDRAFT);
                            TaskHDataAccess.addOrReplace(activity, this.taskH);
                        }
                    }

                } else {
                    Global.isManualSubmit = false;
                }
                try {
//                    Global.isManualSendingRunning = false;
//                    if(MainMenuActivity.autoSendTask!=null){
//                        MainMenuActivity.autoSendTask.stopWaiting();
//                        MainMenuActivity.autoSendImage.stopWaiting();
//                    }
                    if (MainServices.autoSendTaskThread != null) {
                        MainServices.autoSendTaskThread.stopWaiting();
                        MainServices.autoSendImageThread.stopWaiting();
                        MainServices.taskListThread.stopWaiting();
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    // TODO: handle exception
                }

                Global.isUploading = false;
                return message;
            }

            @Override
            protected void onPostExecute(final String message) {
//                progressDialog.dismiss();

                try {
//                    if (MainMenuActivity.autoSendTask != null) {
//                        MainMenuActivity.autoSendTask.stopWaiting();
//                        MainMenuActivity.autoSendImage.stopWaiting();
//                    }
                    if (MainServices.autoSendTaskThread != null) {
                        MainServices.autoSendTaskThread.stopWaiting();
                        MainServices.autoSendImageThread.stopWaiting();
                        MainServices.taskListThread.stopWaiting();
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    // TODO: handle exception
                }
                try {
                    if (message == null || message.length() == 0) {
                        String status = taskH.getStatus();
                        if (status.equals(TaskHDataAccess.STATUS_SEND_PENDING)) {
                            mNotifyManager.cancelAll();
                        } else {
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                                Handler handler = new Handler(Looper.getMainLooper());
                                handler.post(new Runnable() {
                                    public void run() {
                                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                                            String counter = activity.getString(R.string.upload_complete);
                                            String title = activity.getString(R.string.uploading_image_title, taskH.getCustomer_name());
                                            int progress = TaskDDataAccess.getProgressUploadImage(activity, taskH.getUuid_task_h());
                                            String speedKbps = getSpeedKbps();
                                            mBuilder.setContentTitle(title + speedKbps);
                                            mBuilder.setContentText(String.valueOf(progress)+"% "+counter)
                                                    // Removes the progress bar
                                                    .setProgress(0, 0, false);
                                            mBuilder.setOngoing(false);
                                            inboxStyle.setBigContentTitle(title + speedKbps);
                                            inboxStyle.bigText(String.valueOf(progress)+"% "+counter);
                                            mBuilder.setStyle(inboxStyle);

                                            if (android.os.Build.VERSION.SDK_INT >=android.os.Build.VERSION_CODES.O) {
                                                String channelId = "channel_task_send";
                                                String channelName = "Task Send";
                                                int importance = NotificationManager.IMPORTANCE_HIGH;
                                                NotificationChannel notificationChannel = new NotificationChannel(channelId, channelName, importance);
                                                if (null == mNotifyManager) {
                                                    throw new NullPointerException("mNotifyManager is null");
                                                }
                                                mBuilder.setChannelId(channelId);
                                                mNotifyManager.createNotificationChannel(notificationChannel);
                                            }
                                            mNotifyManager.notify(4, mBuilder.build());
                                        }
                                    }
                                });
                            }
                        }
                    } else if (message.equals("no image")) {
                        mNotifyManager.cancelAll();
                    } else {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                            Handler handler = new Handler(Looper.getMainLooper());
                            handler.post(new Runnable() {
                                public void run() {
                                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                                        String title = activity.getString(R.string.uploading_image_title, taskH.getCustomer_name());
                                        int progress = TaskDDataAccess.getProgressUploadImage(activity, taskH.getUuid_task_h());
                                        mBuilder.setContentTitle(title);
                                        mBuilder.setContentText(String.valueOf(progress)+"% "+message)
                                                // Removes the progress bar
                                                .setProgress(0, 0, false);
                                        mBuilder.setOngoing(false);
                                        inboxStyle.setBigContentTitle(title);
                                        inboxStyle.bigText(String.valueOf(progress)+"% "+message);
                                        mBuilder.setStyle(inboxStyle);

                                        if (android.os.Build.VERSION.SDK_INT >=android.os.Build.VERSION_CODES.O) {
                                            String channelId = "channel_task_send";
                                            String channelName = "Task Send";
                                            int importance = NotificationManager.IMPORTANCE_HIGH;
                                            NotificationChannel notificationChannel = new NotificationChannel(channelId, channelName, importance);
                                            if (null == mNotifyManager) {
                                                throw new NullPointerException("mNotifyManager is null");
                                            }
                                            mBuilder.setChannelId(channelId);
                                            mNotifyManager.createNotificationChannel(notificationChannel);
                                        }
                                        mNotifyManager.notify(4, mBuilder.build());
                                    }
                                    // To show message on list task
                                    if (errMessage != null) {
                                        taskH.setMessage(errMessage + " - Error (" + errCode + ")");
                                        TimelineManager.insertTimeline(activity, taskH);
                                    }
                                }
                            });
                        }
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    Global.isManualUploading = false;
                }
                Global.isManualSubmit = false;
            }
        }.execute();
    }

    public void sendApprovalTaskOnBackground(final Context activity, final TaskH taskH, final String flag, final boolean isApprovalTask, final String notes) {
        new AsyncTask<Void, Void, String>() {
            String taskId = null;
            private ProgressDialog progressDialog;
            private String errMessage = null;

            @Override
            protected void onPreExecute() {
//                progressDialog = ProgressDialog.show(activity, "", activity.getString(R.string.progressSend), true);
            }

            @Override
            protected String doInBackground(Void... params) {
                // TODO Auto-generated method stub
                String result = null;
                if (Tool.isInternetconnected(activity)) {
                    String uuidTaskH = taskH.getUuid_task_h();
                    JsonRequestApprovalTask request = new JsonRequestApprovalTask();
                    request.setUuid_task_h(uuidTaskH);
                    request.setFlag(flag);
                    request.setNotes(notes);
                    request.setAudit(GlobalData.getSharedGlobalData().getAuditData());
                    request.addImeiAndroidIdToUnstructured();

                    String json = GsonHelper.toJson(request);
                    String url = GlobalData.getSharedGlobalData().getURL_SUBMITAPPROVALTASK();
                    if (!isApprovalTask)
                        url = GlobalData.getSharedGlobalData().getURL_SUBMITVERIFICATIONTASK();

                    boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                    boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                    HttpCryptedConnection httpConn = new HttpCryptedConnection(activity, encrypt, decrypt);
                    HttpConnectionResult serverResult = null;

                    try {
                        serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        e.printStackTrace();
                        try {
                            progressDialog.dismiss();
                        } catch (Exception e1) {
                            e1.printStackTrace();
                        }
                        errMessage = e.getMessage();
                    }

                    try {
                        result = serverResult.getResult();
                        MssResponseType response = GsonHelper.fromJson(result, MssResponseType.class);
                        if (response.getStatus().getCode() == 0) {
                            result = "success";
                            taskH.setVerification_notes(notes);
                            if (flag.equals(Global.FLAG_FOR_APPROVALTASK)) {
                                taskH.setStatus(TaskHDataAccess.STATUS_SEND_SENT);
                                taskH.setIs_prepocessed(Global.FORM_TYPE_APPROVAL);
                            } else if (flag.equals(Global.FLAG_FOR_REJECTEDTASK)) {
                                taskH.setStatus(TaskHDataAccess.STATUS_SEND_REJECTED);
                            }
                            try {
                                if (!isApprovalTask) {
                                    if (taskH != null) {
                                        taskH.setSubmit_date(taskH.getSubmit_date());
                                        TimelineManager.insertTimeline(activity, taskH, true, true);
                                    }
                                } else {
                                    if (taskH != null) {
                                        taskH.setSubmit_date(taskH.getSubmit_date());
                                        if (flag.equals(Global.FLAG_FOR_APPROVALTASK)) {
                                            TimelineManager.insertTimeline(activity, taskH, false, false);
                                        } else if (flag.equals(Global.FLAG_FOR_REJECTEDTASK)) {
                                            TimelineManager.insertTimeline(activity, taskH, false, true);
                                        }
                                    }
                                }
                                TaskHDataAccess.addOrReplace(activity, taskH);
                            } catch (Exception e) {
                                FireCrash.log(e);
                                // TODO: handle exception
                            }
                        } else {
                            errMessage = String.valueOf(response.getStatus().getCode());
                            taskH.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);
                            taskH.setFlag_survey(flag);
                            taskH.setVerification_notes(notes);

                            if (errMessage != null && errMessage.equals(STATUS_TASK_NOT_MAPPING)) {
                                taskH.setMessage(activity.getString(com.adins.mss.base.R.string.message_task_not_mapping) + " - Error (" + errMessage + ")");
                            } else if (errMessage != null && errMessage.equals(STATUS_TASK_DELETED)) {
                                taskH.setMessage(activity.getString(com.adins.mss.base.R.string.message_sending_deleted) + " - Error (" + errMessage + ")");
                            } else if (errMessage != null && errMessage.equals(STATUS_IMEI_FAILED)) {
                                taskH.setMessage(activity.getString(com.adins.mss.base.R.string.message_imei_not_registered) + " - Error (" + errMessage + ")");
                            } else {
                                taskH.setMessage(activity.getString(com.adins.mss.base.R.string.message_sending_failed) + " - Error (" + errMessage + ")");
                            }

                            TimelineManager.insertTimeline(activity, taskH);
                            TaskHDataAccess.addOrReplace(activity, taskH);
                        }
                    } catch (Exception e) {
                        FireCrash.log(e);
                        // TODO: handle exception
                    }
                } else {
                    result = activity.getString(R.string.no_internet_connection);
                    taskH.setSubmit_date(taskH.getSubmit_date());
                    taskH.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);
                    if (null != flag) {
                        taskH.setFlag_survey(flag);
                    }
                    taskH.setVerification_notes(notes);
                    taskH.setMessage(activity.getString(R.string.no_internet_connection));
                    TimelineManager.insertTimeline(activity, taskH);
                    TaskHDataAccess.addOrReplace(activity, taskH);
                }

                Handler handler = new Handler(Looper.getMainLooper());
                handler.post(new Runnable() {
                    public void run() {
                        try {
                            try {
                                MainMenuActivity.setDrawerCounter();
                            } catch (Exception e) {
                                FireCrash.log(e);
                            }
                            if (Timeline_Activity.timelineHandler != null)
                                Timeline_Activity.timelineHandler.sendEmptyMessage(0);
                        } catch (Exception e) {
                            FireCrash.log(e);
                        }
                    }
                });
                return result;
            }

            @Override
            protected void onPostExecute(String result) {
//                if (progressDialog.isShowing()) {
//                    try {
//                        progressDialog.dismiss();
//                    } catch (Exception e) {
//                    }
//                }
            }
        }.execute();
    }

    public void sendVerificationTaskOnBackground(final Context activity, final int mode,
                                                 final SurveyHeaderBean header, final List<QuestionBean> listOfQuestions, final String notes, final boolean isProgress) {

        new AsyncTask<Void, Void, String>() {
            protected ProgressDialog progressDialog;
            protected List<TaskD> listTaskD;
            protected TaskH taskH;
            protected String errMessage = null;
            protected String errCode = null;
            String taskId = null;
            private String messageWait = activity.getString(R.string.progressSend);

            @Override
            protected void onPreExecute() {
                if (isProgress) {
                    progressDialog = ProgressDialog.show(activity, "", this.messageWait, true);
                }
            }

            @Override
            protected String doInBackground(Void... arg0) {
                String result = null;
                Global.isManualSubmit = true;
                try {
                    if (MainServices.autoSendTaskThread != null) {
                        MainServices.autoSendTaskThread.requestWait();
                        MainServices.autoSendImageThread.requestWait();
                        MainServices.taskListThread.requestWait();
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                }
                boolean saved = saveTask(activity, mode, header, listOfQuestions, null, true);
                if (saved) {
                    this.taskH = header.getTaskH();
                    try {
                        listTaskD = TaskDDataAccess.getAll(activity, header.getUuid_task_h(), TaskDDataAccess.ALL_TASK);
                    } catch (Exception e) {
                        FireCrash.log(e);
                    }
                    if (Tool.isInternetconnected(activity)) {

                        taskId = this.taskH.getTask_id();

                        String uuidTaskH = taskH.getUuid_task_h();
                        String uuidUser = taskH.getUuid_user();
                        boolean isHaveImage = false;
                        List<TaskD> taskDs = new ArrayList<TaskD>();
                        int i = 1;

                        try {
                            List<TaskD> taskd = TaskDDataAccess.getUnsentImageByTaskH(activity, uuidUser, uuidTaskH);
                            if (taskd != null && !taskd.isEmpty())
                                isHaveImage = true;
                        } catch (Exception e) {
                            FireCrash.log(e);
                            e.printStackTrace();
                        }
                        if (!isHaveImage) {
                            for (TaskD d : listTaskD) {
                                if (d.getImage() != null) {
                                    isHaveImage = true;
                                    break;
                                }
                            }
                        }

                        for (TaskD d : listTaskD) {
                            if (d.getIs_visible().equals(Global.TRUE_STRING)) {
                                if (isPartial(activity)) {
                                    if (d.getImage() == null) {
                                        taskDs.add(d);
                                    }
                                } else {
                                    if (i == taskDs.size())
                                        d.setIs_final(Global.TRUE_STRING);
                                    taskDs.add(d);
                                }
                            }
                            i++;
                        }
                        if (!isPartial(activity)) {
                            TaskD d = taskDs.get(taskDs.size() - 1);
                            if (!d.getIs_final().equals(Global.TRUE_STRING)) {
                                d.setIs_final(Global.TRUE_STRING);
                            }
                        } else {
                            if (!isHaveImage) {
                                if (!taskDs.isEmpty()) {
                                    TaskD d = taskDs.get(taskDs.size() - 1);
                                    if (!d.getIs_final().equals(Global.TRUE_STRING)) {
                                        d.setIs_final(Global.TRUE_STRING);
                                    }
                                }
                            }
                        }
                        this.taskH.setSubmit_date(taskH.getSubmit_date());
                        this.taskH.setVerification_notes(notes);
                        //-------------------

                        JsonRequestVerificationTask task = new JsonRequestVerificationTask();
                        task.setAudit(GlobalData.getSharedGlobalData().getAuditData());
                        task.addImeiAndroidIdToUnstructured();
                        task.setTaskH(taskH);
                        task.setTaskD(taskDs);
                        task.setNotes(notes);

                        if (task.getTaskD() != null && !task.getTaskD().isEmpty()) {
                            String json = GsonHelper.toJson(task);
                            String url = GlobalData.getSharedGlobalData().getURL_SUBMITVERIFICATIONTASK();

                            boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                            boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                            HttpCryptedConnection httpConn = new HttpCryptedConnection(activity, encrypt, decrypt);
                            HttpConnectionResult serverResult = null;

                            try {
                                serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
                            } catch (Exception e) {
                                FireCrash.log(e);
                                e.printStackTrace();
                                try {
                                    progressDialog.dismiss();
                                } catch (Exception e1) {
                                    e1.printStackTrace();
                                }
                                errMessage = e.getMessage();
                            }
                            if (serverResult.isOK()) {
                                String resultvalue = serverResult.getResult();
                                JsonResponseSubmitTask responseSubmitTask = GsonHelper.fromJson(resultvalue, JsonResponseSubmitTask.class);
                                if (responseSubmitTask.getStatus().getCode() == 0) {
                                    String status = responseSubmitTask.getResult();
                                    if (status == null)
                                        status = "Success";
                                    if (status.equalsIgnoreCase("Success")) {
                                        result = status;
                                        if (responseSubmitTask.getTaskId() != null)
                                            taskId = responseSubmitTask.getTaskId();
                                        else
                                            taskId = activity.getString(R.string.message_no_task_id_from_server);

                                        for (TaskD taskD : listTaskD) {
                                            taskD.setIs_sent(Global.TRUE_STRING);
                                        }
                                        TaskDDataAccess.addOrReplace(activity, listTaskD);
                                        if (AutoSendImageThread.countPendingImageBeforeUpload(activity, this.taskH.getUuid_task_h()) > 0) {
                                            this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_UPLOADING);
                                            int imageLeft = AutoSendImageThread.countPendingImageBeforeUpload(activity, this.taskH.getUuid_task_h());
                                            ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_UPLOADING, imageLeft);
                                        } else {
                                            this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SENT);
                                            ToDoList.removeSurveyFromList(taskId);
                                        }

                                        if (taskId.contains(activity.getString(R.string.message_task_not_mapping))) {
                                            this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SAVEDRAFT);
                                            ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_SAVEDRAFT, 0);
                                            TaskHDataAccess.addOrReplace(activity, taskH);
                                        } else if (taskId.contains(activity.getString(R.string.message_no_task_id_from_server))) {
                                            this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_SAVEDRAFT);
                                            ToDoList.updateStatusSurvey(taskId, TaskHDataAccess.STATUS_SEND_SAVEDRAFT, 0);
                                            TaskHDataAccess.addOrReplace(activity, taskH);
                                        } else if (taskId.contains("been deleted")) {
                                            TaskHDataAccess.deleteWithRelation(activity, this.taskH);
                                        } else {
                                            this.taskH.setSubmit_date(taskH.getSubmit_date());
                                            this.taskH.setSubmit_result(result);
                                            this.taskH.setTask_id(taskId);
                                            this.taskH.setLast_saved_question(1);
                                            TaskHDataAccess.addOrReplace(activity, this.taskH);

                                            if (taskH.getIs_prepocessed() != null && taskH.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                                                if (MainMenuActivity.mnSVYApproval != null) {
                                                    TaskHDataAccess.deleteWithRelation(activity, this.taskH);
                                                    String newUUIDTaskH = Tool.getUUID();
                                                    this.taskH.setUuid_task_h(newUUIDTaskH);
                                                    TaskHDataAccess.addOrReplace(activity, this.taskH);
                                                    for (TaskD d : listTaskD) {
                                                        d.setTaskH(taskH);
                                                        d.setUuid_task_d(Tool.getUUID());
                                                    }
                                                    TaskDDataAccess.addOrReplace(activity, listTaskD);
                                                }

                                                if (taskH.getStatus().equals(TaskHDataAccess.STATUS_SEND_SENT)) {
                                                    TimelineManager.insertTimeline(activity, this.taskH, true, false);
                                                }
                                            }
                                        }
                                    } else {
                                        result = status;
                                    }

                                } else {
                                    result = String.valueOf(responseSubmitTask.getStatus().getCode());
                                    errMessage = responseSubmitTask.getStatus().getMessage();
                                    errCode = String.valueOf(responseSubmitTask.getStatus().getCode());
                                }

                                if (errMessage != null) {
                                    this.taskH = header.getTaskH();
                                    this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);
                                    if (result != null && result.equals(STATUS_TASK_NOT_MAPPING)) {
                                        this.taskH.setMessage(activity.getString(R.string.message_task_not_mapping) + " - Error (" + errCode + ")");
                                    } else if (result != null && result.equals(STATUS_TASK_DELETED)) {
                                        this.taskH.setMessage(activity.getString(R.string.message_sending_deleted) + " - Error (" + errCode + ")");
                                    } else if (result != null && result.equals(STATUS_IMEI_FAILED)) {
                                        this.taskH.setMessage(activity.getString(R.string.message_imei_not_registered) + " - Error (" + errCode + ")");
                                    } else {
                                        this.taskH.setMessage(errMessage + " - Error (" + errCode + ")");
                                    }
                                    TaskHDataAccess.addOrReplace(activity, this.taskH);
                                    TimelineManager.insertTimeline(activity, taskH);
                                }
                            }
                        } else {
                            Global.isManualSubmit = false;
                            taskH = header.getTaskH();
                            this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);
                            this.taskH.setMessage(activity.getString(R.string.taskd_was_gone, taskH.getCustomer_name()) + " - Error (" + errCode + ")");
                            TaskHDataAccess.addOrReplace(activity, taskH);
                            TimelineManager.insertTimeline(activity, taskH);
                        }

                    } else {
                        taskH = header.getTaskH();
                        this.taskH.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);
                        this.taskH.setMessage(activity.getString(R.string.no_internet_connection));
                        TaskHDataAccess.addOrReplace(activity, taskH);
                        TimelineManager.insertTimeline(activity, taskH);
                    }
                }

                try {
                    if (MainServices.autoSendTaskThread != null) {
                        MainServices.autoSendTaskThread.stopWaiting();
                        MainServices.autoSendImageThread.stopWaiting();
                        MainServices.taskListThread.stopWaiting();
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                }
                Global.isManualSubmit = false;

                return result;
            }

            @Override
            protected void onPostExecute(String result) {
                if (isProgress) {
                    if (progressDialog.isShowing()) {
                        try {
                            progressDialog.dismiss();
                        } catch (Exception e) {
                            FireCrash.log(e);
                        }
                    }
                }
            }
        }.execute();
    }

    //2022-04-16 Riska - tambahan untuk submit per layer
    public static JsonRequestSubmitTask createFiltersForSubmitLayer(Context activity, JsonRequestSubmitTask request, Map<String, QuestionSet> listSubmitLayerQuestion) {
        if (listSubmitLayerQuestion!=null && listSubmitLayerQuestion.size()>0) {
            Map<String, Map<String, String>> filters = new HashMap<>();
            for (String uuidQuestionGroup : listSubmitLayerQuestion.keySet()) {
                QuestionSet btnSubmit = listSubmitLayerQuestion.get(uuidQuestionGroup);

                //create map values
                Map mapKeyValue = new HashMap();
                if(btnSubmit.getChoice_filter()!=null && btnSubmit.getChoice_filter().length()>0) {
                    String[] refIdList = btnSubmit.getChoice_filter().split(",");
                    if (refIdList!=null && refIdList.length>0) {
                        for(int j = 0; j < refIdList.length; j++) {
                            String identifier = refIdList[j];
                            identifier = identifier.replace("{", "");
                            identifier = identifier.replace("}", "");
                            QuestionSet bean = QuestionSetDataAccess.getOneByIdentifier(activity, request.getTaskH().getUuid_scheme(), identifier, request.getTaskH().getForm_version());
                            if(bean!=null) {
                                TaskD detail = TaskDDataAccess.getOneByQuestionAndTaskH(activity, bean.getQuestion_id(), request.getTaskH().getUuid_task_h());
                                if(detail != null) {
                                    mapKeyValue.put(bean.getIdentifier_name(), QuestionBean.getAnswerFromTaskD(activity, bean, detail));
                                }
                            }
                        }
                    }
                }
                filters.put(uuidQuestionGroup, mapKeyValue);
            }
            request.setFilter(filters);
        }
        return request;
    }

    // Adding code for offline tasks (2022-07-14)
    private void matchingAnswersTaskOffline(Activity activity, TaskH taskHeader, LinkedHashMap<String, QuestionBean> listAllQuestions) {
        List<TaskH> listTaskH = TaskHDataAccess.getTaskWaiting(activity, taskHeader.getAppl_no());
        if (null != listTaskH && !listTaskH.isEmpty()) {
            for (int i = 0; i < listTaskH.size(); i++) {
                TaskH taskH = listTaskH.get(i);
                List<TaskD> listTaskD = TaskDDataAccess.getAll(activity, taskH.getUuid_task_h(), TaskDDataAccess.ALL_TASK);
                if (null != listTaskD && !listTaskD.isEmpty()) {
                    for (int j = 0; j < listTaskD.size(); j++) {
                        TaskD taskDSurvey = listTaskD.get(j);
                        String uuidQuestion = taskDSurvey.getUuid_question_mapping();
                        if (null != uuidQuestion && !Global.FALSE_STRING.equals(uuidQuestion)) {
                            QuestionSet questionSet = QuestionSetDataAccess.getOneByQuestionId(activity, taskHeader.getUuid_scheme(),
                                    taskHeader.getForm_version(), uuidQuestion);
                            QuestionSet questionSetTaskSurvey = QuestionSetDataAccess.getOneByQuestionId(activity, taskH.getUuid_scheme(),
                                    taskH.getForm_version(), taskDSurvey.getQuestion_id());
                            if (null != questionSet) {
                                String identifier = questionSet.getIdentifier_name();
                                QuestionBean qBean = listAllQuestions.get(identifier);
                                if (null != qBean && QuestionBean.isHaveAnswer(qBean)) {
                                    String answerType = qBean.getAnswer_type();
                                    if (Tool.isOptions(answerType)) {
                                        try {
                                            List<OptionAnswerBean> optAnsBean = qBean.getSelectedOptionAnswers();
                                            String answer = qBean.getAnswer();
                                            String[] finalAnswer;
                                            if (null != answer && answer.length() > 0) {
                                                finalAnswer = Tool.split(answer, Global.DELIMETER_DATA);
                                            } else {
                                                finalAnswer = new String[0];
                                            }
                                            int x = 0;
                                            for (OptionAnswerBean selectedOption : optAnsBean) {
                                                taskDSurvey.setImage(qBean.getImgAnswer());
                                                taskDSurvey.setLov(qBean.getLovId());

                                                String lookUpId = selectedOption.getUuid_lookup();
                                                String lovCode = selectedOption.getCode();
                                                String lovGroup = selectedOption.getLov_group();

                                                if (null != lookUpId && null != lovCode &&
                                                        !Global.AT_LOOKUP_TABLE.equalsIgnoreCase(answerType)) {
                                                    Lookup lookup = LookupDataAccess.getOne(activity, lookUpId, lovGroup);
                                                    OptionAnswerBean selectedOption2 = new OptionAnswerBean(lookup);
                                                    selectedOption2.setSelected(true);
                                                    if (null != qBean.getTag() && "Job MH".equalsIgnoreCase(qBean.getTag())) {
                                                        taskDSurvey.setOption_answer_id(selectedOption2.getCode());
                                                        taskDSurvey.setTag(qBean.getTag());
                                                    } else {
                                                        taskDSurvey.setOption_answer_id(selectedOption2.getUuid_lookup());
                                                    }
                                                    taskDSurvey.setUuid_lookup(selectedOption2.getUuid_lookup());

                                                    if (Global.REF_PRE_INC_PSGN.equalsIgnoreCase(qBean.getIdentifier_name()) ||
                                                            Global.REF_PRE_NEED_GRNTR.equalsIgnoreCase(qBean.getIdentifier_name())) {
                                                        if ("YA".equalsIgnoreCase(optAnsBean.get(0).getValue())) {
                                                            taskDSurvey.setIs_readonly(Global.TRUE_STRING);
                                                            taskDSurvey.setIs_readonly_mapping(Global.TRUE_STRING);
                                                        } else {
                                                            taskDSurvey.setIs_readonly(Global.FALSE_STRING);
                                                            taskDSurvey.setIs_readonly_mapping(Global.FALSE_STRING);
                                                        }
                                                    }
                                                } else {
                                                    taskDSurvey.setText_answer(selectedOption.getValue());
                                                    taskDSurvey.setUuid_lookup(selectedOption.getOption_id());
                                                }
                                                taskDSurvey.setLov(lovCode);

                                                if (Tool.isOptionsWithDescription(answerType)) {
                                                    taskDSurvey.setText_answer(finalAnswer[x]);
                                                } else if (Global.AT_MULTIPLE_ONE_DESCRIPTION.equals(answerType) ||
                                                        Global.AT_DROPDOWN_W_DESCRIPTION.equals(answerType)) {
                                                    taskDSurvey.setText_answer(qBean.getAnswer());
                                                }
                                                TaskDDataAccess.addOrReplace(activity, taskDSurvey);
                                                x++;
                                            }
                                        } catch (Exception e) {
                                            FireCrash.log(e);
                                        }
                                    } else {
                                        String textAnswer = qBean.getAnswer();
                                        String refIdKodeProvider = null;
                                        if (Global.REF_PRESURVEY_PHONE.equalsIgnoreCase(qBean.getIdentifier_name())) {
                                            refIdKodeProvider = Global.REF_PMHN_KODE_PROVIDER;
                                        } else if (Global.REF_PRESURVEY_SPOUSE_PHONE.equalsIgnoreCase(qBean.getIdentifier_name())) {
                                            if (Global.PSGN_NO_TELEPON_1.equalsIgnoreCase(questionSetTaskSurvey.getIdentifier_name())) {
                                                refIdKodeProvider = Global.REF_PSGN_KODE_PROVIDER_1;
                                            } else if(Global.REF_PSGN_NO_TELEPON.equalsIgnoreCase(questionSetTaskSurvey.getIdentifier_name())) {
                                                refIdKodeProvider = Global.REF_PSGN_KODE_PROVIDER;
                                            }
                                        } else if (Global.REF_PRESURVEY_GRNTR_PHONE.equalsIgnoreCase(qBean.getIdentifier_name())) {
                                            refIdKodeProvider = Global.REF_GUA_PHONE_CODE;
                                        }

                                        if (StringUtils.isNotBlank(refIdKodeProvider) && StringUtils.isNotBlank(textAnswer) && textAnswer.length()>5) {
                                            String kodeArea = textAnswer.substring(0, 4);
                                            String phoneNumber = textAnswer.substring(4, textAnswer.length());
                                            QuestionSet questionKodeProvider = QuestionSetDataAccess.getOneByIdentifier(activity, taskH.getUuid_scheme(), refIdKodeProvider, taskH.getForm_version());
                                            TaskD detailKodeProvider  = TaskDDataAccess.getOneByQuestionAndTaskH(activity, questionKodeProvider.getQuestion_id(), taskH.getUuid_task_h());
                                            if (detailKodeProvider == null) {
                                                detailKodeProvider = new TaskD();
                                                detailKodeProvider.setUuid_task_d(Tool.getUUID());
                                                detailKodeProvider.setDtm_crt(new Date());
                                                detailKodeProvider.setQuestion_id(questionKodeProvider.getQuestion_id());
                                                detailKodeProvider.setQuestion_label(questionKodeProvider.getQuestion_label());
                                                detailKodeProvider.setQuestion_group_id(questionKodeProvider.getQuestion_group_id());
                                                detailKodeProvider.setRegex(questionKodeProvider.getRegex());
                                                detailKodeProvider.setTaskH(taskH);
                                                detailKodeProvider.setIs_readonly_mapping(taskDSurvey.getIs_readonly_mapping());
                                                detailKodeProvider.setIs_readonly(taskDSurvey.getIs_readonly());
                                                detailKodeProvider.setIs_final(Global.FALSE_STRING);
                                            }
                                            detailKodeProvider.setText_answer(kodeArea);
                                            TaskDDataAccess.addOrReplace(activity, detailKodeProvider);
                                            textAnswer = phoneNumber;
                                        }

                                        taskDSurvey.setText_answer(textAnswer);
                                        taskDSurvey.setImage(qBean.getImgAnswer());
                                        taskDSurvey.setLov(qBean.getLovId());

                                        if (Global.AT_GPS.equals(answerType) || Global.AT_GPS_N_LBS.equals(answerType) ||
                                                Global.AT_LOCATION.equals(answerType) || Global.AT_IMAGE_W_GPS_ONLY.equals(answerType) ||
                                                Global.AT_IMAGE_W_LOCATION.equals(answerType)) {
                                            try {
                                                LocationInfo info = qBean.getLocationInfo();
                                                taskDSurvey.setLatitude(info.getLatitude());
                                                taskDSurvey.setLongitude(info.getLongitude());
                                                taskDSurvey.setCid(info.getCid());
                                                taskDSurvey.setMcc(info.getMcc());
                                                taskDSurvey.setMnc(info.getMnc());
                                                taskDSurvey.setLac(info.getLac());
                                                taskDSurvey.setAccuracy(info.getAccuracy());
                                                taskDSurvey.setGps_time(info.getGps_time());
                                                taskDSurvey.setLocation_image(qBean.getImgLocation());
                                            } catch (Exception e) {
                                                FireCrash.log(e);
                                            }
                                        }
                                        TaskDDataAccess.addOrReplace(activity, taskDSurvey);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // Adding offline tasks drop mechanism (2022-07-18)
    private boolean deletingTaskOffline(Activity activity, TaskH taskH, List<QuestionBean> listOfQuestions) {
        boolean isDrop = false;
        for (int i = 0; i < listOfQuestions.size(); i++) {
            QuestionBean qBean = listOfQuestions.get(i);
            if (null != qBean && null != qBean.getTag()) {
                String lovCode = qBean.getLovCode();
                if (null != lovCode && ((Global.TAG_CANCEL_APP.equals(qBean.getTag()) && Global.TRUE_STRING.equals(lovCode)) ||
                            (Global.TAG_CMO_RECOMMENDATION.equals(qBean.getTag()) && Global.FALSE_STRING.equals(lovCode)))) {
                    List<TaskH> listTaskH = TaskHDataAccess.getTaskWaiting(activity, taskH.getAppl_no());
                    if (null != listTaskH && !listTaskH.isEmpty()) {
                        for (int j = 0; j < listTaskH.size(); j++) {
                            isDrop = true;
                            TaskH taskHDrop = listTaskH.get(j);
                            taskHDrop.setStatus(TaskHDataAccess.STATUS_SEND_DROP);
                            TaskHDataAccess.addOrReplace(activity, taskHDrop);
                        }
                    }
                }
            }
        }
        return isDrop;
    }

    public static String getSpeedKbps() {
        long currentBytes = TrafficStats.getTotalRxBytes();
        long bytesPerSecond = currentBytes - previousBytes;
        double speedKbps = bytesPerSecond / 1024.0;
        previousBytes = currentBytes;
        return String.format("(%.2f kbps)", speedKbps);
    }

}