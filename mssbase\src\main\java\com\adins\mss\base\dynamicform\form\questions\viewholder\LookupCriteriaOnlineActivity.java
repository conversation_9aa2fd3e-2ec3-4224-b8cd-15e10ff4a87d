package com.adins.mss.base.dynamicform.form.questions.viewholder;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.fragment.app.FragmentActivity;

import android.view.LayoutInflater;
import android.view.View;
import android.widget.TableLayout;
import android.widget.TableRow;
import android.widget.TextView;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.dynamicform.Constant;
import com.adins.mss.base.dynamicform.form.FragmentQuestion;
import com.adins.mss.base.dynamicform.form.models.LookupCriteriaBean;
import com.adins.mss.base.util.LocaleHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.questiongenerator.QuestionBean;

import java.util.List;
import java.util.Locale;

public class LookupCriteriaOnlineActivity extends FragmentActivity {
    public static final String KEY_SELECTED_CRITERIA = "KEY_SELECTED_CRITERIA";
    public static final String KEY_WITH_FILTER = "KEY_WITH_FILTER";
    public static List<LookupCriteriaBean> beanList;
    TableLayout criteriaTableLayout;
    private boolean withFilter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_lookup_online);
        criteriaTableLayout = (TableLayout) findViewById(R.id.tableCriteriaLayout);
        withFilter = getIntent().getBooleanExtra(KEY_WITH_FILTER, false);
        if (beanList != null) {
            int index = 1;
            for (final LookupCriteriaBean bean : beanList) {
                TableRow row = (TableRow) LayoutInflater.from(this).inflate(R.layout.lookup_criteria_row, criteriaTableLayout, false);
                if (!bean.getValue().equals("No Data Found")) {
                    row.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            String code = bean.getCode();
                            String value = bean.getValue();
                            String selected = code+"|"+value;
                            String refIdMaskapai = getIntent().getStringExtra(Global.GS_REF_ID_TTD_MASKAPAI);
                            if (refIdMaskapai != null)
                                makingResignData(selected, refIdMaskapai);

                            Intent intent = new Intent();
                            intent.putExtra(KEY_SELECTED_CRITERIA, bean);
                            setResult(RESULT_OK, intent);
                            finish();
                        }
                    });
                }
                row.setPadding(0, 16, 0, 16);

                String[] lookupCode = Tool.split(bean.getCode(), Global.DELIMETER_DATA_LOOKUP);
                String[] lookupValue = Tool.split(bean.getValue(), Global.DELIMETER_DATA_LOOKUP);
                StringBuilder lovCode = new StringBuilder();
                StringBuilder lovValue = new StringBuilder();
                for (String code : lookupCode) {
                    if (lovCode.length() != 0)
                        lovCode.append("\n");
                    lovCode.append(code);
                }
                for (String value : lookupValue) {
                    if (lovValue.length() != 0)
                        lovValue.append("\n");
                    lovValue.append(value);
                }
                TextView textCode = (TextView) row.findViewById(R.id.lookupCode);
                textCode.setText(lovCode.toString());

                TextView textDesc = (TextView) row.findViewById(R.id.fieldValue);
                textDesc.setText(lovValue.toString());

                if (index % 2 == 1) {
                    row.setBackgroundResource(R.color.tv_gray_light);
                } else if (index % 2 == 0) {
                    row.setBackgroundResource(R.color.tv_gray);
                }
                criteriaTableLayout.addView(row);
                index++;
            }
        }
    }

    @Override
    protected void attachBaseContext(Context newBase) {
        Context context = newBase;
        Locale locale;
        try {
            locale = new Locale(GlobalData.getSharedGlobalData().getLocale());
            if (null == locale) {
                locale = new Locale(LocaleHelper.ENGLSIH);
            }
            context = LocaleHelper.wrap(newBase, locale);
        } catch (Exception e) {
            locale = new Locale(LocaleHelper.ENGLSIH);
            context = LocaleHelper.wrap(newBase, locale);
        } finally {
            super.attachBaseContext(context);
        }
    }

    // kelvin.ht (12/04/2024) Making null for ttd data if the 'maskapai' is changed
    private void makingResignData(String selectedNow, String refId) {
        if (FragmentQuestion.refIdMaskapai != null) {
            if (refId.equalsIgnoreCase(FragmentQuestion.refIdMaskapai)) {
                QuestionBean qb = Constant.listOfQuestion.get(FragmentQuestion.refIdMaskapai);
                if (qb != null) {
                    if (qb.getHistoryAnsLuOnline() == null) {
                        qb.setHistoryAnsLuOnline(selectedNow);
                    } else {
                        String ansSelectedBefore = qb.getHistoryAnsLuOnline();
                        if (!selectedNow.equalsIgnoreCase(ansSelectedBefore)) {
                            String[] refIdTtdData = FragmentQuestion.refIdTtdQuestion;
                            for (String refIdTtdDatum : refIdTtdData) {
                                QuestionBean qbTtd = Constant.listOfQuestion.get(refIdTtdDatum);
                                if (qbTtd != null) {
                                    qbTtd.setRepeatTtd(true);
                                    qbTtd.setImgAnswer(null);
                                    if (FragmentQuestion.uiImgViewTtdCustomer != null && refIdTtdDatum.equalsIgnoreCase(Global.REF_PRE_TTD_CUSTOMER))
                                        FragmentQuestion.uiImgViewTtdCustomer.setImageResource(android.R.drawable.ic_menu_edit);
                                    else if (FragmentQuestion.uiImgViewTtdSpouse != null && refIdTtdDatum.equalsIgnoreCase(Global.REF_PRE_TTD_SPOUSE))
                                        FragmentQuestion.uiImgViewTtdSpouse.setImageResource(android.R.drawable.ic_menu_edit);
                                    else if (FragmentQuestion.uiImgViewTtdGuarantor != null && refIdTtdDatum.equalsIgnoreCase(Global.REF_PRE_TTD_GUARANTOR))
                                        FragmentQuestion.uiImgViewTtdGuarantor.setImageResource(android.R.drawable.ic_menu_edit);
                                }
                            }
                            qb.setHistoryAnsLuOnline(selectedNow);
                        }
                    }
                }
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        beanList = null;
    }

    @Override
    public void onBackPressed() {
        if (withFilter) {
            super.onBackPressed();
        } else {
            Intent intent = new Intent();
            setResult(RESULT_CANCELED, intent);
            finish();
        }
    }
}

