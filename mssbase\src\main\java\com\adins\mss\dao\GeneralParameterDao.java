package com.adins.mss.dao;

import java.util.List;
import java.util.ArrayList;
import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.SqlUtils;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;
import de.greenrobot.dao.query.Query;
import de.greenrobot.dao.query.QueryBuilder;

import com.adins.mss.dao.GeneralParameter;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "MS_GENERALPARAMETER".
*/
public class GeneralParameterDao extends AbstractDao<GeneralParameter, String> {

    public static final String TABLENAME = "MS_GENERALPARAMETER";

    /**
     * Properties of entity GeneralParameter.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_general_parameter = new Property(0, String.class, "uuid_general_parameter", true, "UUID_GENERAL_PARAMETER");
        public final static Property Gs_value = new Property(1, String.class, "gs_value", false, "GS_VALUE");
        public final static Property Gs_code = new Property(2, String.class, "gs_code", false, "GS_CODE");
        public final static Property Usr_crt = new Property(3, String.class, "usr_crt", false, "USR_CRT");
        public final static Property Dtm_crt = new Property(4, java.util.Date.class, "dtm_crt", false, "DTM_CRT");
        public final static Property Usr_upd = new Property(5, String.class, "usr_upd", false, "USR_UPD");
        public final static Property Dtm_upd = new Property(6, java.util.Date.class, "dtm_upd", false, "DTM_UPD");
        public final static Property Uuid_user = new Property(7, String.class, "uuid_user", false, "UUID_USER");
        public final static Property Uuid_scheme = new Property(8, String.class, "uuid_scheme", false, "UUID_SCHEME");
    };

    private DaoSession daoSession;

    private Query<GeneralParameter> user_GeneralParameterListQuery;

    public GeneralParameterDao(DaoConfig config) {
        super(config);
    }
    
    public GeneralParameterDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"MS_GENERALPARAMETER\" (" + //
                "\"UUID_GENERAL_PARAMETER\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_general_parameter
                "\"GS_VALUE\" TEXT," + // 1: gs_value
                "\"GS_CODE\" TEXT," + // 2: gs_code
                "\"USR_CRT\" TEXT," + // 3: usr_crt
                "\"DTM_CRT\" INTEGER," + // 4: dtm_crt
                "\"USR_UPD\" TEXT," + // 5: usr_upd
                "\"DTM_UPD\" INTEGER," + // 6: dtm_upd
                "\"UUID_USER\" TEXT NOT NULL ," + // 7: uuid_user
                "\"UUID_SCHEME\" TEXT);"); // 8: uuid_scheme
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"MS_GENERALPARAMETER\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, GeneralParameter entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_general_parameter());
 
        String gs_value = entity.getGs_value();
        if (gs_value != null) {
            stmt.bindString(2, gs_value);
        }
 
        String gs_code = entity.getGs_code();
        if (gs_code != null) {
            stmt.bindString(3, gs_code);
        }
 
        String usr_crt = entity.getUsr_crt();
        if (usr_crt != null) {
            stmt.bindString(4, usr_crt);
        }
 
        java.util.Date dtm_crt = entity.getDtm_crt();
        if (dtm_crt != null) {
            stmt.bindLong(5, dtm_crt.getTime());
        }
 
        String usr_upd = entity.getUsr_upd();
        if (usr_upd != null) {
            stmt.bindString(6, usr_upd);
        }
 
        java.util.Date dtm_upd = entity.getDtm_upd();
        if (dtm_upd != null) {
            stmt.bindLong(7, dtm_upd.getTime());
        }
        stmt.bindString(8, entity.getUuid_user());
    }

    @Override
    protected void attachEntity(GeneralParameter entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public GeneralParameter readEntity(Cursor cursor, int offset) {
        GeneralParameter entity = new GeneralParameter( //
            cursor.getString(offset + 0), // uuid_general_parameter
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // gs_value
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // gs_code
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // usr_crt
            cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)), // dtm_crt
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // usr_upd
            cursor.isNull(offset + 6) ? null : new java.util.Date(cursor.getLong(offset + 6)), // dtm_upd
            cursor.getString(offset + 7) // uuid_user
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, GeneralParameter entity, int offset) {
        entity.setUuid_general_parameter(cursor.getString(offset + 0));
        entity.setGs_value(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setGs_code(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setUsr_crt(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setDtm_crt(cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)));
        entity.setUsr_upd(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setDtm_upd(cursor.isNull(offset + 6) ? null : new java.util.Date(cursor.getLong(offset + 6)));
        entity.setUuid_user(cursor.getString(offset + 7));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(GeneralParameter entity, long rowId) {
        return entity.getUuid_general_parameter();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(GeneralParameter entity) {
        if(entity != null) {
            return entity.getUuid_general_parameter();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
    /** Internal query to resolve the "generalParameterList" to-many relationship of User. */
    public List<GeneralParameter> _queryUser_GeneralParameterList(String uuid_scheme) {
        synchronized (this) {
            if (user_GeneralParameterListQuery == null) {
                QueryBuilder<GeneralParameter> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_scheme.eq(null));
                user_GeneralParameterListQuery = queryBuilder.build();
            }
        }
        Query<GeneralParameter> query = user_GeneralParameterListQuery.forCurrentThread();
        query.setParameter(0, uuid_scheme);
        return query.list();
    }

    private String selectDeep;

    protected String getSelectDeep() {
        if (selectDeep == null) {
            StringBuilder builder = new StringBuilder("SELECT ");
            SqlUtils.appendColumns(builder, "T", getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T0", daoSession.getUserDao().getAllColumns());
            builder.append(" FROM MS_GENERALPARAMETER T");
            builder.append(" LEFT JOIN MS_USER T0 ON T.\"UUID_USER\"=T0.\"UUID_USER\"");
            builder.append(' ');
            selectDeep = builder.toString();
        }
        return selectDeep;
    }
    
    protected GeneralParameter loadCurrentDeep(Cursor cursor, boolean lock) {
        GeneralParameter entity = loadCurrent(cursor, 0, lock);
        int offset = getAllColumns().length;

        User user = loadCurrentOther(daoSession.getUserDao(), cursor, offset);
         if(user != null) {
            entity.setUser(user);
        }

        return entity;    
    }

    public GeneralParameter loadDeep(Long key) {
        assertSinglePk();
        if (key == null) {
            return null;
        }

        StringBuilder builder = new StringBuilder(getSelectDeep());
        builder.append("WHERE ");
        SqlUtils.appendColumnsEqValue(builder, "T", getPkColumns());
        String sql = builder.toString();
        
        String[] keyArray = new String[] { key.toString() };
        Cursor cursor = db.rawQuery(sql, keyArray);
        
        try {
            boolean available = cursor.moveToFirst();
            if (!available) {
                return null;
            } else if (!cursor.isLast()) {
                throw new IllegalStateException("Expected unique result, but count was " + cursor.getCount());
            }
            return loadCurrentDeep(cursor, true);
        } finally {
            cursor.close();
        }
    }
    
    /** Reads all available rows from the given cursor and returns a list of new ImageTO objects. */
    public List<GeneralParameter> loadAllDeepFromCursor(Cursor cursor) {
        int count = cursor.getCount();
        List<GeneralParameter> list = new ArrayList<GeneralParameter>(count);
        
        if (cursor.moveToFirst()) {
            if (identityScope != null) {
                identityScope.lock();
                identityScope.reserveRoom(count);
            }
            try {
                do {
                    list.add(loadCurrentDeep(cursor, false));
                } while (cursor.moveToNext());
            } finally {
                if (identityScope != null) {
                    identityScope.unlock();
                }
            }
        }
        return list;
    }
    
    protected List<GeneralParameter> loadDeepAllAndCloseCursor(Cursor cursor) {
        try {
            return loadAllDeepFromCursor(cursor);
        } finally {
            cursor.close();
        }
    }
    

    /** A raw-style query where you can pass any WHERE clause and arguments. */
    public List<GeneralParameter> queryDeep(String where, String... selectionArg) {
        Cursor cursor = db.rawQuery(getSelectDeep() + where, selectionArg);
        return loadDeepAllAndCloseCursor(cursor);
    }
 
}
