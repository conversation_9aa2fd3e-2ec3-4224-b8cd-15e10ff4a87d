package com.adins.mss.base.commons;

import android.content.Context;

/**
 * Created by <PERSON><PERSON><PERSON> on 1/6/2015.
 * Used to create a context based model.
 */
public abstract class ContextModel {
    private final Context context;

    /**
     * Initialize a new instance of context model.
     *
     * @param context The context for the model.
     */
    public ContextModel(Context context) {
        this.context = context;
    }

    /**
     * Get the model context.
     *
     * @return Model context.
     */
    public Context getContext() {
        return context;
    }
}
