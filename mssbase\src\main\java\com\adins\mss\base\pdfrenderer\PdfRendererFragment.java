package com.adins.mss.base.pdfrenderer;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Matrix;
import android.graphics.pdf.PdfRenderer;
import android.os.Build;
import android.os.Bundle;
import android.os.ParcelFileDescriptor;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.annotation.RequiresApi;

import com.adins.mss.base.BaseActivity;
import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.util.LocaleHelper;
import com.adins.mss.constant.Global;

import java.io.File;
import java.io.IOException;
import java.util.Locale;

/**
 * This fragment has a big {@ImageView} that shows PDF pages, and 2
 * {@link Button}s to move between pages. We use a
 * {@link PdfRenderer} to render PDF pages as
 * {@link Bitmap}s.
 */
@RequiresApi(Build.VERSION_CODES.LOLLIPOP)
public class PdfRendererFragment extends BaseActivity implements View.OnClickListener {

    /**
     * Key string for saving the state of current page index.
     */
    private static final String STATE_CURRENT_PAGE_INDEX = "current_page_index";
    private static final int MAX_BITMAP_SIZE = 100 * 1024 * 1024; // 100 MB

    /**
     * The filename of the PDF.
     */
    public String FILENAME;

    /**
     * File descriptor of the PDF.
     */
    private ParcelFileDescriptor mFileDescriptor;

    /**
     * {@link PdfRenderer} to render the PDF.
     */
    private PdfRenderer mPdfRenderer;

    /**
     * Page that is currently shown on the screen.
     */
    private PdfRenderer.Page mCurrentPage;

    /**
     * {@link ImageView} that shows a PDF page as a {@link Bitmap}
     */
    private ImageView mImageView;

    /**
     * {@link Button} to move to the previous page.
     */
    private Button mButtonPrevious;
    private Button mButtonZoomin;
    private Button mButtonZoomout;
    private Button mButtonNext;
    private static final float DEFAULT_ZOOM = 2;
    private float currentZoomLevel = DEFAULT_ZOOM;
    private String urlFileName;

    /**
     * PDF page index
     */
    private int mPageIndex;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //To can't screenshoot
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);

        setContentView(R.layout.pdf_renderer_basic_fragment);
        urlFileName = getIntent().getStringExtra("URL_FILE");
        // Retain view references.
        mImageView = (ImageView) findViewById(R.id.image);
        mButtonPrevious = (Button) findViewById(R.id.previous);
        mButtonNext = (Button) findViewById(R.id.next);
        mButtonZoomin = (Button) findViewById(R.id.zoomin);
        mButtonZoomout = (Button) findViewById(R.id.zoomout);
        // Bind events.
        mButtonPrevious.setOnClickListener(this);
        mButtonNext.setOnClickListener(this);
        mButtonZoomin.setOnClickListener(this);
        mButtonZoomout.setOnClickListener(this);

        mPageIndex = 0;
        // If there is a savedInstanceState (screen orientations, etc.), we restore the page index.
        if (null != savedInstanceState) {
            mPageIndex = savedInstanceState.getInt(STATE_CURRENT_PAGE_INDEX, 0);
        }
    }
    @Override
    protected void attachBaseContext(Context newBase) {
        Context context = newBase;
        Locale locale;
        try {
            if (null != GlobalData.getSharedGlobalData().getLocale()) {
                locale = new Locale(GlobalData.getSharedGlobalData().getLocale());
            } else {
                locale = new Locale(LocaleHelper.ENGLSIH);
            }
            context = LocaleHelper.wrap(newBase, locale);
        } catch (Exception e) {
            locale = new Locale(LocaleHelper.ENGLSIH);
            context = LocaleHelper.wrap(newBase, locale);
        } finally {
            super.attachBaseContext(context);
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        try {
            openRenderer(urlFileName);
            showPage(mPageIndex);
        } catch (IOException e) {
            if (Global.IS_DEV) {
                e.printStackTrace();
            }
            String message = "Error View PDF: " + e.getMessage();
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
            FireCrash.log(e);
            this.finish();
        } catch ( Exception ex) {
            if (Global.IS_DEV) {
                ex.printStackTrace();
            }
            String message = "Error View PDF: " + ex.getMessage();
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
            FireCrash.log(ex);
            this.finish();
        }
    }

    @Override
    public void onStop() {
        try {
            closeRenderer();
        } catch (IOException e) {
            e.printStackTrace();
        }
        super.onStop();
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        if (null != mCurrentPage) {
            outState.putInt(STATE_CURRENT_PAGE_INDEX, mCurrentPage.getIndex());
        }
    }

    /**
     * Sets up a {@link PdfRenderer} and related resources.
     */
    private void openRenderer(String urlFilename) throws IOException {
        File file = new File(urlFilename);
        mFileDescriptor = ParcelFileDescriptor.open(file, ParcelFileDescriptor.MODE_READ_ONLY);
        // This is the PdfRenderer we use to render the PDF.
        if (mFileDescriptor != null) {
            mPdfRenderer = new PdfRenderer(mFileDescriptor);
        }
    }

    /**
     * Closes the {@link PdfRenderer} and related resources.
     *
     * @throws IOException When the PDF file cannot be closed.
     */
    private void closeRenderer() throws IOException {
        if (null != mCurrentPage) {
            mCurrentPage.close();
            mCurrentPage = null;
        }
        if (null != mPdfRenderer) {
            mPdfRenderer.close();
        }
        if (null != mFileDescriptor) {
            mFileDescriptor.close();
        }
    }

    /**
     * Zoom level for zoom matrix depends on screen density (dpiAdjustedZoomLevel), but width and height of bitmap depends only on pixel size and don't depend on DPI
     * Shows the specified page of PDF to the screen.
     *
     * @param index The page index.
     */
    private void showPage(int index) {
        if (mPdfRenderer.getPageCount() <= index) {
            return;
        }
        // Make sure to close the current page before opening another one.
        if (null != mCurrentPage) {
            mCurrentPage.close();
        }
        // Use `openPage` to open a specific page in PDF.
        mCurrentPage = mPdfRenderer.openPage(index);
        // Important: the destination bitmap must be ARGB (not RGB).
        float nPercentW = ((float) getResources().getDisplayMetrics().widthPixels/ (float)  mCurrentPage.getWidth() * currentZoomLevel);

        int newWidth = Math.max(Math.round( mCurrentPage.getWidth() * nPercentW), 1);
        int newHeight = Math.max(Math.round( mCurrentPage.getHeight() * nPercentW), 1);
        Bitmap bitmap = Bitmap.createBitmap(
                newWidth,
                newHeight,
                Bitmap.Config.ARGB_8888);

        if (bitmap.getByteCount() <= MAX_BITMAP_SIZE) {
            Matrix matrix = new Matrix();
            float dpiAdjustedZoomLevel = currentZoomLevel * DisplayMetrics.DENSITY_DEVICE_STABLE / getResources().getDisplayMetrics().densityDpi;
            matrix.setScale(dpiAdjustedZoomLevel, dpiAdjustedZoomLevel);

            // Here, we render the page onto the Bitmap.
            // To render a portion of the page, use the second and third parameter. Pass nulls to get
            // the default result.
            // Pass either RENDER_MODE_FOR_DISPLAY or RENDER_MODE_FOR_PRINT for the last parameter.
            mCurrentPage.render(bitmap, null, matrix, PdfRenderer.Page.RENDER_MODE_FOR_DISPLAY);
            // We are ready to show the Bitmap to user.
            mImageView.setImageBitmap(bitmap);
            updateUi();
        } else {
            Toast.makeText(this, "Zoom has reached Max!", Toast.LENGTH_SHORT).show();
        }
}

    /**
     * Updates the state of 2 control buttons in response to the current page index.
     */
    private void updateUi() {
        int index = mCurrentPage.getIndex();
        int pageCount = mPdfRenderer.getPageCount();
        if (pageCount == 1) {
            mButtonPrevious.setVisibility(View.GONE);
            mButtonNext.setVisibility(View.GONE);
        } else {
            mButtonPrevious.setEnabled(0 != index);
            mButtonNext.setEnabled(index + 1 < pageCount);
        }
        if (currentZoomLevel == 2) {
            mButtonZoomout.setActivated(false);
        } else {
            mButtonZoomout.setActivated(true);
        }
    }

    /**
     * Gets the number of pages in the PDF. This method is marked as public for testing.
     *
     * @return The number of pages.
     */
    public int getPageCount() {
        return mPdfRenderer.getPageCount();
    }

    @Override
    public void onClick(View view) {
        int button = view.getId();
        if (button == R.id.previous) {
            // Move to the previous page
            currentZoomLevel = DEFAULT_ZOOM;
            showPage(mCurrentPage.getIndex() - 1);
        } else if (button == R.id.next) {
            currentZoomLevel = DEFAULT_ZOOM;
            showPage(mCurrentPage.getIndex() + 1);
        } else if (button == R.id.zoomout) {
            if (currentZoomLevel-1 > 0) {
                --currentZoomLevel;
                showPage(mCurrentPage.getIndex());
            }
        }  else if (button == R.id.zoomin) {
            ++currentZoomLevel;
            showPage(mCurrentPage.getIndex());
        }
    }
}