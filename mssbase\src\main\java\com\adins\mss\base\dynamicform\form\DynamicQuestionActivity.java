package com.adins.mss.base.dynamicform.form;

import android.Manifest;
import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.View;
import android.view.WindowManager;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.Toast;
import android.widget.ToggleButton;

import androidx.annotation.Keep;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.adins.mss.base.BaseActivity;
import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.commons.RealPathUtil;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.dynamicform.Constant;
import com.adins.mss.base.dynamicform.CustomerFragment;
import com.adins.mss.base.dynamicform.DynamicFormActivity;
import com.adins.mss.base.dynamicform.SurveyHeaderBean;
import com.adins.mss.base.dynamicform.form.questions.viewholder.ValidationCheckQuestionViewHolder;
import com.adins.mss.base.dynamicform.form.questions.viewholder.SubmitLayerQuestionViewHolder;
import com.adins.mss.base.mainmenu.MainMenuActivity;
import com.adins.mss.base.todo.Task;
import com.adins.mss.base.util.CustomAnimatorLayout;
import com.adins.mss.base.util.EventBusHelper;
import com.adins.mss.base.util.LocaleHelper;
import com.adins.mss.base.util.Utility;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.Scheme;
import com.adins.mss.foundation.camerainapp.helper.Logger;
import com.adins.mss.foundation.db.dataaccess.SchemeDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskHDataAccess;
import com.adins.mss.foundation.dialog.DialogManager;
import com.adins.mss.foundation.dialog.NiftyDialogBuilder;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.image.Utils;
import com.adins.mss.foundation.questiongenerator.QuestionBean;

import org.acra.ACRA;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;

import static com.adins.mss.base.dynamicform.form.questions.viewholder.ImageQuestionViewHolder.createImageFile;

public class DynamicQuestionActivity extends BaseActivity implements View.OnClickListener, LocationListener {
    public static FragmentManager fragmentManager;
    public static ArrayAdapter<String> adapter;
    public static ProgressDialog progressDialog;
    public static ArrayList<String> questionLabel = new ArrayList<String>();
    public SurveyHeaderBean header;
    LocationManager mLocation = null;
    private int mColumnCount = 1;
    private ImageButton btnBack;
    private ImageButton btnNext;
    private ImageButton btnSave;
    private ImageButton btnSend;
    private ImageButton btnVerified;
    private ImageButton btnReject;
    private ImageButton btnApprove;
    private ImageButton btnClose;
    private ImageButton btnSearch;
    private ToggleButton btnSearchBar;
    private AutoCompleteTextView txtSearch;
    //task abstract class
    private Task task;
    private boolean isSimulasi = false;
    private RelativeLayout searchContainer;
    private int mode;

    Bitmap bitmap = null;
    public static QuestionBean qBean;

    public static void dismissProgressBar() {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (DynamicQuestionActivity.progressDialog != null) {
                    if (DynamicQuestionActivity.progressDialog.isShowing()) {
                        try {
                            DynamicQuestionActivity.progressDialog.dismiss();
                        } catch (Exception e) {
                            FireCrash.log(e);
                            if (Global.IS_DEV)
                                e.printStackTrace();
                        }
                    }
                }
            }
        }, 1000);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (Global.isVerifiedByUser) {
            Global.isVerifiedByUser = false;
            this.finish();
        }
        DialogManager.showGPSAlert(this);
        DialogManager.showTimeProviderAlert(this);


    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        EventBusHelper.unregisterEventBus(this);
        Utility.freeMemory();
        Constant.listOfQuestion = null;
        CustomerFragment.header = null;
        DynamicFormActivity.listOfIdentifier = null;
        DynamicFormActivity.isApproval = false;
        DynamicFormActivity.isVerified = false;
        DynamicFormActivity.allowImageEdit = true;
        if (mLocation != null)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                    return;
                } else {
                    mLocation.removeUpdates(this);
                }
            } else {
                mLocation.removeUpdates(this);
            }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //To can't screenshoot
        if (Tool.isProdFlavour(Global.FLAVORS)) {
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
        }
        EventBusHelper.registerEventBus(this);
        setContentView(R.layout.activity_dynamic_question);
        fragmentManager = getSupportFragmentManager();
        bindLocationListener();
        initialize();
        initScreenLayout();
        questionLabel.clear();
        new AsyncTask<Void, Void, Void>() {

            @Override
            protected Void doInBackground(Void... params) {
                List<Scheme> schemes = SchemeDataAccess.getAll(getApplicationContext());
                Global.TempScheme = new HashMap<String, Date>();

                for (Scheme scheme : schemes) {
                    Global.TempScheme.put(scheme.getUuid_scheme(), scheme.getScheme_last_update());
                }

                Global.SchemeIsChange = true;
                return null;
            }
        }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR);
//        showProgressBar(getString(R.string.progressWait));
        new Handler().post(new Runnable() {
            @Override
            public void run() {
                gotoQuestionFragment();
            }
        });
        ACRA.getErrorReporter().putCustomData("LAST_CLASS_ACCESSED", getClass().getSimpleName());
    }

    @Override
    protected void attachBaseContext(Context newBase) {
        Context context = newBase;
        Locale locale;
        try {
            locale = new Locale(GlobalData.getSharedGlobalData().getLocale());
            if (null == locale) {
                locale = new Locale(LocaleHelper.ENGLSIH);
            }
            context = LocaleHelper.wrap(newBase, locale);
        } catch (Exception e) {
            locale = new Locale(LocaleHelper.ENGLSIH);
            context = LocaleHelper.wrap(newBase, locale);
        } finally {
            super.attachBaseContext(context);
        }
    }

    private void gotoQuestionFragment() {
        FragmentTransaction transaction = fragmentManager.beginTransaction();
        Fragment fragment1 = new FragmentQuestion();
        Bundle bundle = new Bundle();
        bundle.putInt(Global.BUND_KEY_MODE_SURVEY, mode);
        bundle.putBoolean(Global.BUND_KEY_MODE_SIMULASI, isSimulasi);
        fragment1.setArguments(bundle);
//        transaction.setCustomAnimations(R.anim.activity_open_translate, R.anim.activity_close_scale, R.anim.activity_open_scale, R.anim.activity_close_translate);
        transaction.replace(R.id.mainContainer, fragment1);
        transaction.commit();
//        transaction.commitAllowingStateLoss();
    }

    private void initialize() {
        Bundle extras = getIntent().getExtras();
        mode = extras.getInt(Global.BUND_KEY_MODE_SURVEY);
        DynamicFormActivity.header = null;
        DynamicFormActivity.header = CustomerFragment.header;
        header = DynamicFormActivity.header;
        task = (Task) extras.getSerializable(Global.BUND_KEY_TASK);
        isSimulasi = extras.getBoolean(Global.BUND_KEY_MODE_SIMULASI, false);

        try {
            if (header.getPriority() != null && header.getPriority().length() > 0) {
                if (header.getStart_date() == null) {
                    header.setStart_date(Tool.getSystemDateTime());
                    new CustomerFragment.SendOpenReadTaskH(this, header).executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR);
                }
            }
        } catch (Exception e) {
            FireCrash.log(e);
            if (Global.IS_DEV)
                e.printStackTrace();
//            String[] msg = {"Failed open questions,\nplease try again"};
//            String alert = Tool.implode(msg, "\n");
            Toast.makeText(this, getResources().getString(R.string.failed_open_question), Toast.LENGTH_SHORT).show();
        }
    }

    public void showProgressBar(final String message) {
//        new Handler().post(new Runnable() {
//            @Override
//            public void run() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                DynamicQuestionActivity.progressDialog = ProgressDialog.show(DynamicQuestionActivity.this, "", message, false);
            }
        });
//            }
//        });
    }

    @Override
    public void onBackPressed() {
        if (mode == Global.MODE_VIEW_SENT_SURVEY) {
            super.onBackPressed();
            GlobalData.getSharedGlobalData().setDoingTask(false);
        } else {
            DialogManager.showExitAlertQuestion(this, getString(R.string.alertExitSurvey));
        }
    }

    private void initScreenLayout() {
        btnBack = (ImageButton) findViewById(R.id.btnBack);
        btnNext = (ImageButton) findViewById(R.id.btnNext);
        btnSend = (ImageButton) findViewById(R.id.btnSend);
        btnSave = (ImageButton) findViewById(R.id.btnSave);
        btnSearch = (ImageButton) findViewById(R.id.btnSearch);
        btnVerified = (ImageButton) findViewById(R.id.btnVerified);
        btnReject = (ImageButton) findViewById(R.id.btnReject);
        btnApprove = (ImageButton) findViewById(R.id.btnApprove);
        btnClose = (ImageButton) findViewById(R.id.btnClose);
        btnSearchBar = (ToggleButton) findViewById(R.id.btnSearchBar);

        btnBack.setOnClickListener(this);
        btnNext.setOnClickListener(this);
        btnVerified.setOnClickListener(this);
        btnSend.setOnClickListener(this);
        btnSave.setOnClickListener(this);
        btnSearch.setOnClickListener(this);
        btnReject.setOnClickListener(this);
        btnApprove.setOnClickListener(this);
        btnSearchBar.setOnClickListener(this);
        btnClose.setOnClickListener(this);

        adapter = new ArrayAdapter<String>(this, R.layout.autotext_list, R.id.textauto, DynamicQuestionActivity.questionLabel);
        txtSearch = (AutoCompleteTextView) findViewById(R.id.autoCompleteSearch);
        txtSearch.setAdapter(adapter);
        txtSearch.setDropDownBackgroundDrawable(ContextCompat.getDrawable(this, R.drawable.actionbar_background));
        txtSearch.setOnFocusChangeListener(new View.OnFocusChangeListener() {

            @Override
            public void onFocusChange(View arg0, boolean hasFocused) {

                if (hasFocused) {
                    adapter.notifyDataSetChanged();
//                    txtSearch.setAdapter(adapter);
                }
            }
        });

        LinearLayout sendLayout = (LinearLayout) findViewById(R.id.btnSendLayout);
        LinearLayout verifyLayout = (LinearLayout) findViewById(R.id.btnVerifiedLayout);
        LinearLayout rejectLayout = (LinearLayout) findViewById(R.id.btnRejectLayout);
        LinearLayout approveLayout = (LinearLayout) findViewById(R.id.btnApproveLayout);
        LinearLayout nextLayout = (LinearLayout) findViewById(R.id.btnNextLayout);
        LinearLayout saveLayout = (LinearLayout) findViewById(R.id.btnSaveLayout);
        LinearLayout searchLayout = (LinearLayout) findViewById(R.id.btnSearchLayout);
        LinearLayout backLayout = (LinearLayout) findViewById(R.id.btnBackLayout);
        LinearLayout closeLayout = (LinearLayout) findViewById(R.id.btnCloseLayout);
        searchContainer = (RelativeLayout) findViewById(R.id.searchLayout);
        searchContainer.setVisibility(View.GONE);
        btnSend.setClickable(false);
        btnSend.setImageResource(R.drawable.ic_send_off);
        try {
            if (TaskHDataAccess.STATUS_TASK_VERIFICATION.equalsIgnoreCase(header.getStatus()) ||
                    TaskHDataAccess.STATUS_TASK_VERIFICATION_DOWNLOAD.equalsIgnoreCase(header.getStatus())) {
                backLayout.setVisibility(View.GONE);
                sendLayout.setVisibility(View.GONE);
                saveLayout.setVisibility(View.GONE);
                approveLayout.setVisibility(View.GONE);
                //ganti ke halaman baaru
                if (!Global.NEW_FEATURE) {
                    rejectLayout.setVisibility(View.VISIBLE);
                    verifyLayout.setVisibility(View.VISIBLE);
                }

            }
            if (TaskHDataAccess.STATUS_TASK_APPROVAL.equalsIgnoreCase(header.getStatus()) ||
                    TaskHDataAccess.STATUS_TASK_APPROVAL_DOWNLOAD.equalsIgnoreCase(header.getStatus())) {
                backLayout.setVisibility(View.GONE);
                sendLayout.setVisibility(View.GONE);
                searchLayout.setVisibility(View.GONE);
                verifyLayout.setVisibility(View.GONE);
                saveLayout.setVisibility(View.GONE);
                if (!Global.NEW_FEATURE) {
                    nextLayout.setVisibility(View.GONE);
                    rejectLayout.setVisibility(View.VISIBLE);
                    approveLayout.setVisibility(View.VISIBLE);
                }
                searchContainer.setVisibility(View.GONE);
            }
        } catch (Exception e) {
            FireCrash.log(e);

        }
        if (mode == Global.MODE_VIEW_SENT_SURVEY) {
            backLayout.setVisibility(View.GONE);
            nextLayout.setVisibility(View.GONE);
            sendLayout.setVisibility(View.GONE);
            searchLayout.setVisibility(View.GONE);
            verifyLayout.setVisibility(View.GONE);
            rejectLayout.setVisibility(View.GONE);
            approveLayout.setVisibility(View.GONE);
            saveLayout.setVisibility(View.GONE);
            closeLayout.setVisibility(View.VISIBLE);
            searchContainer.setVisibility(View.GONE);
        }
        if (isSimulasi) {
            saveLayout.setVisibility(View.GONE);
            sendLayout.setVisibility(View.GONE);
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == Global.REQUEST_DRAWING_QUESTION) {
                Bundle bundle = data.getExtras();
                bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.RESULT_FROM_DRAWING_QUESTION);
                Message message = new Message();
                message.setData(bundle);
                FragmentQuestion.questionHandler.sendMessage(message);
            } else if (requestCode == Utils.REQUEST_IN_APP_CAMERA) {
                Bundle bundle = data.getExtras();
                if (bundle != null) {
                    bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.RESULT_FROM_BUILT_IN_CAMERA);
                    Message message = new Message();
                    message.setData(bundle);
                    FragmentQuestion.questionHandler.sendMessage(message);
                }
            } else if (requestCode == Utils.REQUEST_CAMERA) {
                Bundle bundle = data.getExtras();
                if (bundle != null) {
                    bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.RESULT_FROM_ANDROID_CAMERA);
                    Message message = new Message();
                    message.setData(bundle);
                    FragmentQuestion.questionHandler.sendMessage(message);
                }
            } else if (requestCode == Utils.REQUEST_GALLERY) {
                Bundle bundle = new Bundle();
                if (data.getData() != null) {
                    String realPath;
                    if (Build.VERSION.SDK_INT < 19) {
                        realPath = RealPathUtil.getRealPathFromURI_API11to18(this, data.getData());
                    } else {
                        realPath = RealPathUtil.getRealPathFromURI_API19(this, data.getData());
                    }

                    Logger.i("INFO", "Image Path: " + realPath);
                    DynamicFormActivity.mCurrentPhotoPath = realPath;
                    bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.RESULT_FROM_ANDROID_GALLERY);
                    Message message = new Message();
                    message.setData(bundle);
                    FragmentQuestion.questionHandler.sendMessage(message);
                }
            } else if (requestCode == Global.REQUEST_LOCATIONTAGGING) {
                Bundle bundle = new Bundle();
                bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.RESULT_FROM_LOCATION_QUESTION);
                Message message = new Message();
                message.setData(bundle);
                FragmentQuestion.questionHandler.sendMessage(message);
            } else if (requestCode == Global.REQUEST_VOICE_NOTES) {
                byte[] voiceNotes = data.getByteArrayExtra(Global.BUND_KEY_DETAIL_DATA);
                if (voiceNotes != null && voiceNotes.length > 0) {
                    header.setVoice_note(voiceNotes);
                }
            } else if (requestCode == Global.REQUEST_EDIT_IMAGE) {
                Bundle bundle = data.getExtras();
                if (bundle != null) {
                    bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.RESULT_FROM_EDIT_IMAGE);
                    Message message = new Message();
                    message.setData(bundle);
                    FragmentQuestion.questionHandler.sendMessage(message);
                }
            } else if (requestCode == Global.REQUEST_LOOKUP_ANSWER) {
                Bundle bundle = data.getExtras();
                if (bundle != null) {
                    bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.RESULT_FROM_LOOKUP_CRITERIA);
                    Message message = new Message();
                    message.setData(bundle);
                    FragmentQuestion.questionHandler.sendMessage(message);
                }
            }
            else if (requestCode == Utils.REQUEST_PICK) {
                Bundle bundle = new Bundle();
                File photoFile = null;
                Uri sourceUri=Uri.parse(data.getData().toString());
                try {
                    photoFile = createImageFile();
                } catch (IOException ex) {
                    Toast.makeText(this, R.string.crop__pick_error, Toast.LENGTH_SHORT).show();
                }
                try {
                    if (photoFile != null) {
                        String path = photoFile.getPath();
                        InputStream inputStream = getContentResolver().openInputStream(sourceUri);
                        FileOutputStream oStream = new FileOutputStream(photoFile);
                        BitmapFactory.Options o = new BitmapFactory.Options();
                        o.inJustDecodeBounds = false;
                        o.inSampleSize = 1;
                        bitmap = BitmapFactory.decodeStream(inputStream, null, o);
                        boolean isHQ = false;
                        if (null != qBean.getImg_quality() && Global.IMAGE_HQ.equalsIgnoreCase(qBean.getImg_quality())) {
                            isHQ = true;
                        }
                        if (Global.AT_OCR_W_GALLERY.equals(qBean.getAnswer_type())) {
                            bitmap = Utils.resizeImageByPath(bitmap, isHQ);
                        }
                        if (isHQ) {
                            bitmap.compress(Bitmap.CompressFormat.JPEG, Utils.picHQQuality, oStream);
                        } else {
                            bitmap.compress(Bitmap.CompressFormat.JPEG, Utils.picQuality, oStream);
                        }
                        oStream.close();
                        Logger.i("INFO", "Image Path: " + photoFile.getPath());
                        DynamicFormActivity.mCurrentPhotoPath = photoFile.getPath();
                        bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.RESULT_FROM_PICK_IMAGE);
                        bundle.putString(FragmentQuestion.BUND_KEY_PICK_IMAGE, path);
                    } else {
                        Toast.makeText(this, R.string.crop__pick_error, Toast.LENGTH_SHORT).show();
                    }
                } catch (FileNotFoundException e) {
                    e.printStackTrace();
                } catch (IOException e) {
                    e.printStackTrace();
                }
                Message message = new Message();
                message.setData(bundle);
                FragmentQuestion.questionHandler.sendMessage(message);
            } else if (requestCode == Global.REQUEST_IMAGE_GPS_LOCATION_UPDATE) {
                Bundle bundle = data.getExtras();
                if(bundle != null){
                    bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.RESULT_FROM_IMAGE_LOC_UPDATE);
                    Message message = new Message();
                    message.setData(bundle);
                    FragmentQuestion.questionHandler.sendMessage(message);
                }
            } else if (requestCode == Global.REQUEST_LOCATION_UPDATE) {
                Bundle bundle = data.getExtras();
                if(bundle != null){
                    bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.RESULT_FROM_LOCATION_UPDATE);
                    Message message = new Message();
                    message.setData(bundle);
                    FragmentQuestion.questionHandler.sendMessage(message);
                }
            } else if (requestCode == Global.REQUEST_ACCEPTED_AGREEMENT) {
                Bundle bundle = data.getExtras();
                if (bundle != null) {
                    bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.RESULT_FROM_ACCEPTED_AGREEMENT);
                    Message message = new Message();
                    message.setData(bundle);
                    FragmentQuestion.questionHandler.sendMessage(message);
                }
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        switch (requestCode) {
            case Utility.REQUEST_CODE_ASK_MULTIPLE_PERMISSIONS: {
                if (Utility.checkPermissionResult(this, permissions, grantResults))
                    bindLocationListener();
            }
            break;
            default:
                super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
    }

    private void bindLocationListener() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                Utility.checkPermissionGranted(this);
                return;
            } else {
                mLocation = (LocationManager) getSystemService(LOCATION_SERVICE);
                try {
                    if (mLocation.getAllProviders().contains(LocationManager.GPS_PROVIDER))
                        mLocation.requestSingleUpdate(LocationManager.GPS_PROVIDER, this, null);
                } catch (IllegalArgumentException e) {
                    FireCrash.log(e);        // TODO: handle exception
                } catch (Exception e) {
                    FireCrash.log(e);

                }
            }
        } else {
            mLocation = (LocationManager) getSystemService(LOCATION_SERVICE);
            try {
                if (mLocation.getAllProviders().contains(LocationManager.GPS_PROVIDER))
                    mLocation.requestSingleUpdate(LocationManager.GPS_PROVIDER, this, null);
            } catch (IllegalArgumentException e) {
                FireCrash.log(e);    // TODO: handle exception
            } catch (Exception e) {
                FireCrash.log(e);

            }
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.btnNext) {
            Message message = new Message();
            Bundle bundle = new Bundle();
            bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.NEXT_QUESTION);
            message.setData(bundle);
            FragmentQuestion.questionHandler.sendMessage(message);
        } else if (id == R.id.btnSave) {
            Message message = new Message();
            Bundle bundle = new Bundle();
            bundle.putInt(Global.BUND_KEY_MODE_SURVEY, mode);
            bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.SAVE_QUESTION);
            message.setData(bundle);
            FragmentQuestion.questionHandler.sendMessage(message);
        } else if (id == R.id.btnSend) {
            Message message = new Message();
            Bundle bundle = new Bundle();
            bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.SEND_QUESTION);
            message.setData(bundle);
            FragmentQuestion.questionHandler.sendMessage(message);
        } else if (id == R.id.btnSearchBar) {
            adapter = new ArrayAdapter<String>(this, R.layout.autotext_list, R.id.textauto, DynamicQuestionActivity.questionLabel);
            txtSearch.setAdapter(adapter);
            adapter.notifyDataSetChanged();
            if (btnSearchBar.isChecked()) {
                CustomAnimatorLayout animatorLayout = new CustomAnimatorLayout(0, 1, 0, 1, 500, searchContainer, false);
                searchContainer.setVisibility(View.VISIBLE);
                searchContainer.startAnimation(animatorLayout);
            } else {
                CustomAnimatorLayout animatorLayout = new CustomAnimatorLayout(1, 0, 1, 0, 500, searchContainer, true);
                searchContainer.startAnimation(animatorLayout);
            }
        } else if (id == R.id.btnSearch) {
            btnSearchBar.setChecked(false);
            CustomAnimatorLayout animatorLayout = new CustomAnimatorLayout(1, 0, 1, 0, 500, searchContainer, true);
            searchContainer.startAnimation(animatorLayout);
            String searchKey = "";
            if (txtSearch.getText().length() > 0)
                searchKey = txtSearch.getText().toString().toLowerCase();
            Message message = new Message();
            Bundle bundle = new Bundle();
            bundle.putString(FragmentQuestion.BUND_KEY_SEARCH_ACTION, searchKey);
            bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.SEARCH_QUESTION);
            message.setData(bundle);
            FragmentQuestion.questionHandler.sendMessage(message);
        } else if (id == R.id.btnVerified) {
            Message message = new Message();
            Bundle bundle = new Bundle();
            bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.VERIFY_QUESTION);
            message.setData(bundle);
            FragmentQuestion.questionHandler.sendMessage(message);
        } else if (id == R.id.btnApprove) {
            Message message = new Message();
            Bundle bundle = new Bundle();
            bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.APPROVE_QUESTION);
            message.setData(bundle);
            FragmentQuestion.questionHandler.sendMessage(message);
        } else if (id == R.id.btnReject) {
            Message message = new Message();
            Bundle bundle = new Bundle();
            bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.REJECT_QUESTION);
            message.setData(bundle);
            FragmentQuestion.questionHandler.sendMessage(message);
        } else if (id == R.id.btnClose) {
            GlobalData.getSharedGlobalData().setDoingTask(false);
            this.finish();
        }
    }

    @Override
    public void onLocationChanged(Location location) {
        if (location != null)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (location.isFromMockProvider())
                    DialogManager.showMockDialog(DynamicQuestionActivity.this);
            }
    }

    @Override
    public void onStatusChanged(String provider, int status, Bundle extras) {

    }

    @Override
    public void onProviderEnabled(String provider) {
        DialogManager.closeGPSAlert();
    }

    @Override
    public void onProviderDisabled(String provider) {
        DialogManager.showGPSAlert(this);
    }

    @Keep // subcribe
    public void onEvent(SubmitLayerQuestionViewHolder holder) {
        final String errorMessage = holder.getErrorMessage();
        String title = "INFO";
        if (errorMessage.contains("Drop")) {
            title = "DROP";
        }
        final NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(this);
        dialogBuilder.withTitle(title)
                .withMessage(errorMessage)
                .withButton1Text("OK")
                .setButton1Click(new View.OnClickListener() {
                    @Override
                    public void onClick(View arg0) {
                        dialogBuilder.dismiss();
                        if (errorMessage.contains("Drop")) {
                            Bundle bundle = new Bundle();
                            bundle.putString("action", Global.MAINMENU_NOTIFICATION_KEY);
                            Message message = new Message();
                            message.setData(bundle);
                            MainMenuActivity.mStackHandler.sendMessage(message);
                            DynamicFormActivity.header = null;
                            DynamicQuestionActivity.this.finish();
                        }
                    }
                })
                .isCancelable(false)
                .show();
    }

    @Keep // Subcribe
    public void onEvent(ValidationCheckQuestionViewHolder holder) {
        final String errorMessage = holder.getErrorMessage();
        String title = "INFO";
        if (errorMessage.contains("Drop")) {
            title = "DROP";
        }

        final NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(this);
        dialogBuilder.withTitle(title)
                .withMessage(errorMessage)
                .withButton1Text("OK")
                .setButton1Click(new View.OnClickListener() {
                    @Override
                    public void onClick(View arg0) {
                        dialogBuilder.dismiss();
                        if (errorMessage.contains("Drop")) {
                            Bundle bundle = new Bundle();
                            bundle.putString("action", Global.MAINMENU_NOTIFICATION_KEY);
                            android.os.Message message = new android.os.Message();
                            message.setData(bundle);
                            MainMenuActivity.mStackHandler.sendMessage(message);
                            DynamicFormActivity.header = null;
                            DynamicQuestionActivity.this.finish();
                        }
                    }
                })
                .isCancelable(false)
                .show();
    }

}
