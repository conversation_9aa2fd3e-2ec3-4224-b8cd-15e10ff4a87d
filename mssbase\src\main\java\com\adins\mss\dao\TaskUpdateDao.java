package com.adins.mss.dao;

import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;

import com.adins.mss.dao.TaskUpdate;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TR_TASK_UPDATE".
*/
public class TaskUpdateDao extends AbstractDao<TaskUpdate, String> {

    public static final String TABLENAME = "TR_TASK_UPDATE";

    /**
     * Properties of entity TaskUpdate.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_task_update = new Property(0, String.class, "uuid_task_update", true, "UUID_TASK_UPDATE");
        public final static Property Customer_name = new Property(1, String.class, "customer_name", false, "CUSTOMER_NAME");
        public final static Property Customer_phone = new Property(2, String.class, "customer_phone", false, "CUSTOMER_PHONE");
        public final static Property Customer_address = new Property(3, String.class, "customer_address", false, "CUSTOMER_ADDRESS");
        public final static Property Notes = new Property(4, String.class, "notes", false, "NOTES");
        public final static Property Assignment_date = new Property(5, String.class, "assignment_date", false, "ASSIGNMENT_DATE");
        public final static Property Appl_no = new Property(6, String.class, "appl_no", false, "APPL_NO");
        public final static Property Form_name = new Property(7, String.class, "form_name", false, "FORM_NAME");
        public final static Property Pending_notes = new Property(8, String.class, "pending_notes", false, "PENDING_NOTES");
        public final static Property Docupro_feedback = new Property(9, String.class, "docupro_feedback", false, "DOCUPRO_FEEDBACK");
        public final static Property Uuid_scheme = new Property(10, String.class, "uuid_scheme", false, "UUID_SCHEME");
        public final static Property Uuid_user = new Property(11, String.class, "uuid_user", false, "UUID_USER");
        public final static Property Is_notified = new Property(12, Integer.class, "is_notified", false, "IS_NOTIFIED");
        public final static Property Category = new Property(13, String.class, "category", false, "CATEGORY");
        public final static Property Sub_category = new Property(14, String.class, "sub_category", false, "SUB_CATEGORY");
        public final static Property Reason_detail = new Property(15, String.class, "reason_detail", false, "REASON_DETAIL");
        public final static Property Validasi = new Property(16, String.class, "validasi", false, "VALIDASI");
        public final static Property Is_pre_approval = new Property(17, Integer.class, "is_pre_approval", false, "IS_PRE_APPROVAL");
    };


    public TaskUpdateDao(DaoConfig config) {
        super(config);
    }
    
    public TaskUpdateDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TR_TASK_UPDATE\" (" + //
                "\"UUID_TASK_UPDATE\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_task_update
                "\"CUSTOMER_NAME\" TEXT," + // 1: customer_name
                "\"CUSTOMER_PHONE\" TEXT," + // 2: customer_phone
                "\"CUSTOMER_ADDRESS\" TEXT," + // 3: customer_address
                "\"NOTES\" TEXT," + // 4: notes
                "\"ASSIGNMENT_DATE\" TEXT," + // 5: assignment_date
                "\"APPL_NO\" TEXT," + // 6: appl_no
                "\"FORM_NAME\" TEXT," + // 7: form_name
                "\"PENDING_NOTES\" TEXT," + // 8: pending_notes
                "\"DOCUPRO_FEEDBACK\" TEXT," + // 9: docupro_feedback
                "\"UUID_SCHEME\" TEXT," + // 10: uuid_scheme
                "\"UUID_USER\" TEXT," + // 11: uuid_user
                "\"IS_NOTIFIED\" INTEGER," + // 12: is_notified
                "\"CATEGORY\" TEXT," + // 13: category
                "\"SUB_CATEGORY\" TEXT," + // 14: sub_category
                "\"REASON_DETAIL\" TEXT," + // 15: reason_detail
                "\"VALIDASI\" TEXT," + // 16: validasi
                "\"IS_PRE_APPROVAL\" INTEGER);"); // 17: is_pre_approval
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TR_TASK_UPDATE\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, TaskUpdate entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_task_update());
 
        String customer_name = entity.getCustomer_name();
        if (customer_name != null) {
            stmt.bindString(2, customer_name);
        }
 
        String customer_phone = entity.getCustomer_phone();
        if (customer_phone != null) {
            stmt.bindString(3, customer_phone);
        }
 
        String customer_address = entity.getCustomer_address();
        if (customer_address != null) {
            stmt.bindString(4, customer_address);
        }
 
        String notes = entity.getNotes();
        if (notes != null) {
            stmt.bindString(5, notes);
        }
 
        String assignment_date = entity.getAssignment_date();
        if (assignment_date != null) {
            stmt.bindString(6, assignment_date);
        }
 
        String appl_no = entity.getAppl_no();
        if (appl_no != null) {
            stmt.bindString(7, appl_no);
        }
 
        String form_name = entity.getForm_name();
        if (form_name != null) {
            stmt.bindString(8, form_name);
        }
 
        String pending_notes = entity.getPending_notes();
        if (pending_notes != null) {
            stmt.bindString(9, pending_notes);
        }
 
        String docupro_feedback = entity.getDocupro_feedback();
        if (docupro_feedback != null) {
            stmt.bindString(10, docupro_feedback);
        }
 
        String uuid_scheme = entity.getUuid_scheme();
        if (uuid_scheme != null) {
            stmt.bindString(11, uuid_scheme);
        }
 
        String uuid_user = entity.getUuid_user();
        if (uuid_user != null) {
            stmt.bindString(12, uuid_user);
        }
 
        Integer is_notified = entity.getIs_notified();
        if (is_notified != null) {
            stmt.bindLong(13, is_notified);
        }
 
        String category = entity.getCategory();
        if (category != null) {
            stmt.bindString(14, category);
        }
 
        String sub_category = entity.getSub_category();
        if (sub_category != null) {
            stmt.bindString(15, sub_category);
        }
 
        String reason_detail = entity.getReason_detail();
        if (reason_detail != null) {
            stmt.bindString(16, reason_detail);
        }
 
        String validasi = entity.getValidasi();
        if (validasi != null) {
            stmt.bindString(17, validasi);
        }
 
        Integer is_pre_approval = entity.getIs_pre_approval();
        if (is_pre_approval != null) {
            stmt.bindLong(18, is_pre_approval);
        }
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public TaskUpdate readEntity(Cursor cursor, int offset) {
        TaskUpdate entity = new TaskUpdate( //
            cursor.getString(offset + 0), // uuid_task_update
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // customer_name
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // customer_phone
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // customer_address
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // notes
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // assignment_date
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // appl_no
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7), // form_name
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8), // pending_notes
            cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9), // docupro_feedback
            cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10), // uuid_scheme
            cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11), // uuid_user
            cursor.isNull(offset + 12) ? null : cursor.getInt(offset + 12), // is_notified
            cursor.isNull(offset + 13) ? null : cursor.getString(offset + 13), // category
            cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14), // sub_category
            cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15), // reason_detail
            cursor.isNull(offset + 16) ? null : cursor.getString(offset + 16), // validasi
            cursor.isNull(offset + 17) ? null : cursor.getInt(offset + 17) // is_pre_approval
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, TaskUpdate entity, int offset) {
        entity.setUuid_task_update(cursor.getString(offset + 0));
        entity.setCustomer_name(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setCustomer_phone(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setCustomer_address(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setNotes(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setAssignment_date(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setAppl_no(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setForm_name(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
        entity.setPending_notes(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
        entity.setDocupro_feedback(cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9));
        entity.setUuid_scheme(cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10));
        entity.setUuid_user(cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11));
        entity.setIs_notified(cursor.isNull(offset + 12) ? null : cursor.getInt(offset + 12));
        entity.setCategory(cursor.isNull(offset + 13) ? null : cursor.getString(offset + 13));
        entity.setSub_category(cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14));
        entity.setReason_detail(cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15));
        entity.setValidasi(cursor.isNull(offset + 16) ? null : cursor.getString(offset + 16));
        entity.setIs_pre_approval(cursor.isNull(offset + 17) ? null : cursor.getInt(offset + 17));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(TaskUpdate entity, long rowId) {
        return entity.getUuid_task_update();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(TaskUpdate entity) {
        if(entity != null) {
            return entity.getUuid_task_update();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
}
