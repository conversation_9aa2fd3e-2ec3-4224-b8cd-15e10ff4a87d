package com.adins.mss.dao;

import java.util.List;
import java.util.ArrayList;
import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.SqlUtils;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;
import de.greenrobot.dao.query.Query;
import de.greenrobot.dao.query.QueryBuilder;

import com.adins.mss.dao.Menu;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "MS_MENU".
*/
public class MenuDao extends AbstractDao<Menu, String> {

    public static final String TABLENAME = "MS_MENU";

    /**
     * Properties of entity Menu.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_menu = new Property(0, String.class, "uuid_menu", true, "UUID_MENU");
        public final static Property Menu_id = new Property(1, String.class, "menu_id", false, "MENU_ID");
        public final static Property Flag_job = new Property(2, String.class, "flag_job", false, "FLAG_JOB");
        public final static Property Is_visible = new Property(3, String.class, "is_visible", false, "IS_VISIBLE");
        public final static Property Usr_crt = new Property(4, String.class, "usr_crt", false, "USR_CRT");
        public final static Property Dtm_crt = new Property(5, java.util.Date.class, "dtm_crt", false, "DTM_CRT");
        public final static Property Usr_upd = new Property(6, String.class, "usr_upd", false, "USR_UPD");
        public final static Property Dtm_upd = new Property(7, java.util.Date.class, "dtm_upd", false, "DTM_UPD");
        public final static Property Uuid_user = new Property(8, String.class, "uuid_user", false, "UUID_USER");
    };

    private DaoSession daoSession;

    private Query<Menu> user_MenuListQuery;

    public MenuDao(DaoConfig config) {
        super(config);
    }
    
    public MenuDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"MS_MENU\" (" + //
                "\"UUID_MENU\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_menu
                "\"MENU_ID\" TEXT," + // 1: menu_id
                "\"FLAG_JOB\" TEXT," + // 2: flag_job
                "\"IS_VISIBLE\" TEXT," + // 3: is_visible
                "\"USR_CRT\" TEXT," + // 4: usr_crt
                "\"DTM_CRT\" INTEGER," + // 5: dtm_crt
                "\"USR_UPD\" TEXT," + // 6: usr_upd
                "\"DTM_UPD\" INTEGER," + // 7: dtm_upd
                "\"UUID_USER\" TEXT);"); // 8: uuid_user
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"MS_MENU\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, Menu entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_menu());
 
        String menu_id = entity.getMenu_id();
        if (menu_id != null) {
            stmt.bindString(2, menu_id);
        }
 
        String flag_job = entity.getFlag_job();
        if (flag_job != null) {
            stmt.bindString(3, flag_job);
        }
 
        String is_visible = entity.getIs_visible();
        if (is_visible != null) {
            stmt.bindString(4, is_visible);
        }
 
        String usr_crt = entity.getUsr_crt();
        if (usr_crt != null) {
            stmt.bindString(5, usr_crt);
        }
 
        java.util.Date dtm_crt = entity.getDtm_crt();
        if (dtm_crt != null) {
            stmt.bindLong(6, dtm_crt.getTime());
        }
 
        String usr_upd = entity.getUsr_upd();
        if (usr_upd != null) {
            stmt.bindString(7, usr_upd);
        }
 
        java.util.Date dtm_upd = entity.getDtm_upd();
        if (dtm_upd != null) {
            stmt.bindLong(8, dtm_upd.getTime());
        }
 
        String uuid_user = entity.getUuid_user();
        if (uuid_user != null) {
            stmt.bindString(9, uuid_user);
        }
    }

    @Override
    protected void attachEntity(Menu entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public Menu readEntity(Cursor cursor, int offset) {
        Menu entity = new Menu( //
            cursor.getString(offset + 0), // uuid_menu
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // menu_id
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // flag_job
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // is_visible
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // usr_crt
            cursor.isNull(offset + 5) ? null : new java.util.Date(cursor.getLong(offset + 5)), // dtm_crt
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // usr_upd
            cursor.isNull(offset + 7) ? null : new java.util.Date(cursor.getLong(offset + 7)), // dtm_upd
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8) // uuid_user
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, Menu entity, int offset) {
        entity.setUuid_menu(cursor.getString(offset + 0));
        entity.setMenu_id(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setFlag_job(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setIs_visible(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setUsr_crt(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setDtm_crt(cursor.isNull(offset + 5) ? null : new java.util.Date(cursor.getLong(offset + 5)));
        entity.setUsr_upd(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setDtm_upd(cursor.isNull(offset + 7) ? null : new java.util.Date(cursor.getLong(offset + 7)));
        entity.setUuid_user(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(Menu entity, long rowId) {
        return entity.getUuid_menu();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(Menu entity) {
        if(entity != null) {
            return entity.getUuid_menu();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
    /** Internal query to resolve the "menuList" to-many relationship of User. */
    public List<Menu> _queryUser_MenuList(String uuid_user) {
        synchronized (this) {
            if (user_MenuListQuery == null) {
                QueryBuilder<Menu> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_user.eq(null));
                user_MenuListQuery = queryBuilder.build();
            }
        }
        Query<Menu> query = user_MenuListQuery.forCurrentThread();
        query.setParameter(0, uuid_user);
        return query.list();
    }

    private String selectDeep;

    protected String getSelectDeep() {
        if (selectDeep == null) {
            StringBuilder builder = new StringBuilder("SELECT ");
            SqlUtils.appendColumns(builder, "T", getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T0", daoSession.getUserDao().getAllColumns());
            builder.append(" FROM MS_MENU T");
            builder.append(" LEFT JOIN MS_USER T0 ON T.\"UUID_USER\"=T0.\"UUID_USER\"");
            builder.append(' ');
            selectDeep = builder.toString();
        }
        return selectDeep;
    }
    
    protected Menu loadCurrentDeep(Cursor cursor, boolean lock) {
        Menu entity = loadCurrent(cursor, 0, lock);
        int offset = getAllColumns().length;

        User user = loadCurrentOther(daoSession.getUserDao(), cursor, offset);
        entity.setUser(user);

        return entity;    
    }

    public Menu loadDeep(Long key) {
        assertSinglePk();
        if (key == null) {
            return null;
        }

        StringBuilder builder = new StringBuilder(getSelectDeep());
        builder.append("WHERE ");
        SqlUtils.appendColumnsEqValue(builder, "T", getPkColumns());
        String sql = builder.toString();
        
        String[] keyArray = new String[] { key.toString() };
        Cursor cursor = db.rawQuery(sql, keyArray);
        
        try {
            boolean available = cursor.moveToFirst();
            if (!available) {
                return null;
            } else if (!cursor.isLast()) {
                throw new IllegalStateException("Expected unique result, but count was " + cursor.getCount());
            }
            return loadCurrentDeep(cursor, true);
        } finally {
            cursor.close();
        }
    }
    
    /** Reads all available rows from the given cursor and returns a list of new ImageTO objects. */
    public List<Menu> loadAllDeepFromCursor(Cursor cursor) {
        int count = cursor.getCount();
        List<Menu> list = new ArrayList<Menu>(count);
        
        if (cursor.moveToFirst()) {
            if (identityScope != null) {
                identityScope.lock();
                identityScope.reserveRoom(count);
            }
            try {
                do {
                    list.add(loadCurrentDeep(cursor, false));
                } while (cursor.moveToNext());
            } finally {
                if (identityScope != null) {
                    identityScope.unlock();
                }
            }
        }
        return list;
    }
    
    protected List<Menu> loadDeepAllAndCloseCursor(Cursor cursor) {
        try {
            return loadAllDeepFromCursor(cursor);
        } finally {
            cursor.close();
        }
    }
    

    /** A raw-style query where you can pass any WHERE clause and arguments. */
    public List<Menu> queryDeep(String where, String... selectionArg) {
        Cursor cursor = db.rawQuery(getSelectDeep() + where, selectionArg);
        return loadDeepAllAndCloseCursor(cursor);
    }
 
}
