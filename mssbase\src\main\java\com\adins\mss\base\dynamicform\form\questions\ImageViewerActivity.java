package com.adins.mss.base.dynamicform.form.questions;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.AsyncTask;
import android.os.Bundle;

import androidx.fragment.app.FragmentActivity;
import androidx.annotation.Nullable;

import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.LinearLayout;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.dynamicform.form.FragmentQuestion;
import com.adins.mss.base.util.LocaleHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.image.ImageManipulation;
import com.adins.mss.foundation.image.Utils;

import java.io.ByteArrayOutputStream;
import java.lang.ref.WeakReference;
import java.util.Locale;

/**
 * Created by gigin.ginanjar on 05/09/2016.
 */
public class ImageViewerActivity extends FragmentActivity implements View.OnClickListener {
    public static String BUND_KEY_IMAGE = "com.adins.mss.base.dynamicform.form.questions.BUND_KEY_IMAGE";
    public static String BUND_KEY_IMAGE_QUALITY = "com.adins.mss.base.dynamicform.form.questions.BUND_KEY_IMAGE_QUALITY";
    public static String BUND_KEY_IMAGE_ISVIEWER = "com.adins.mss.base.dynamicform.form.questions.BUND_KEY_IMAGE_ISVIEWER";
    TouchImageView imageView;
    LinearLayout btnLayout;
    Button btnSave;
    Bitmap tempBitmap;
    int imgQuality;
    boolean isPickImage = false;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //To can't screenshoot
        if (Tool.isProdFlavour(Global.FLAVORS)) {
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
        }

        setContentView(R.layout.image_viewer_layout);
        imageView = (TouchImageView) findViewById(R.id.imageViewer);
        imageView.setMaxZoom(8f);
        btnLayout = (LinearLayout) findViewById(R.id.btnLayout);
        byte[] imageByte = getIntent().getExtras().getByteArray(BUND_KEY_IMAGE);
        imgQuality = getIntent().getExtras().getInt(BUND_KEY_IMAGE_QUALITY);
        isPickImage = getIntent().getExtras().getBoolean(FragmentQuestion.BUND_KEY_PICK_IMAGE);
        boolean isViewer = getIntent().getExtras().getBoolean(BUND_KEY_IMAGE_ISVIEWER, false);
        new BitmapWorkerTask(this, imageView, imageByte, false, false).execute();
        if (isViewer) {
            btnLayout.setVisibility(View.GONE);
        } else {
            btnLayout.setVisibility(View.VISIBLE);
        }
        Button btnRotate = (Button) findViewById(R.id.btnRotate);
        btnSave = (Button) findViewById(R.id.btnSave);
        btnSave.setEnabled(false);
        btnRotate.setOnClickListener(this);
        btnSave.setOnClickListener(this);
    }


    @Override
    protected void attachBaseContext(Context newBase) {
        Context context = newBase;
        Locale locale;
        try {
            locale = new Locale(GlobalData.getSharedGlobalData().getLocale());
            if (null == locale) {
                locale = new Locale(LocaleHelper.ENGLSIH);
            }
            context = LocaleHelper.wrap(newBase, locale);
        } catch (Exception e) {
            locale = new Locale(LocaleHelper.ENGLSIH);
            context = LocaleHelper.wrap(newBase, locale);
        } finally {
            super.attachBaseContext(context);
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.btnSave) {
            new BitmapWorkerTask(this, imageView, null, false, true).execute();
        } else if (id == R.id.btnRotate) {
            btnSave.setEnabled(true);
            new BitmapWorkerTask(this, imageView, null, true, false).execute();
        }
    }

    public class BitmapWorkerTask extends AsyncTask<Void, Void, Bitmap> {
        private final WeakReference<TouchImageView> imageViewReference;
        private final byte[] data;
        private final FragmentActivity mContext;
        boolean isRotate;
        boolean isSave;
        private ProgressDialog progressDialog;
        private ByteArrayOutputStream stream;

        public BitmapWorkerTask(FragmentActivity context, TouchImageView imageView, byte[] data) {
            imageViewReference = new WeakReference<TouchImageView>(imageView);
            this.data = data;
            this.mContext = context;
            stream = new ByteArrayOutputStream();
        }

        public BitmapWorkerTask(FragmentActivity context, TouchImageView imageView, byte[] data, boolean isRotate, boolean isSave) {
            imageViewReference = new WeakReference<TouchImageView>(imageView);
            this.data = data;
            this.mContext = context;
            this.isRotate = isRotate;
            this.isSave = isSave;
            stream = new ByteArrayOutputStream();
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            progressDialog = ProgressDialog.show(mContext, "", mContext.getString(R.string.processing_image), true);
        }

        @Override
        protected Bitmap doInBackground(Void... params) {
            try {
                Bitmap bitmap = null;
                if (isRotate) {
                    bitmap = ImageManipulation.rotateImage(tempBitmap, 90);
                    tempBitmap = bitmap;
                } else if (isSave) {
                    tempBitmap.compress(Bitmap.CompressFormat.JPEG, imgQuality, stream);
                } else {
                    bitmap = Utils.byteToBitmap(data);
                    tempBitmap = bitmap;
                }
                return bitmap;
            } catch (Exception e) {
                FireCrash.log(e);
                e.printStackTrace();
            }
            return null;
        }

        @Override
        protected void onPostExecute(final Bitmap bitmap) {
            super.onPostExecute(bitmap);
            if (progressDialog != null && progressDialog.isShowing()) {
                progressDialog.dismiss();
            }
            if (imageViewReference != null && bitmap != null) {
                final TouchImageView imageView = imageViewReference.get();
                if (imageView != null) {
                    imageView.setImageBitmap(bitmap);
                    imageView.rescalingImage();
                }
            }
            if (isSave) {
                byte[] data = stream.toByteArray();
                Intent intent = new Intent();
                Bundle bundle = new Bundle();
                bundle.putByteArray(BUND_KEY_IMAGE, data);
                bundle.putBoolean(FragmentQuestion.BUND_KEY_PICK_IMAGE, isPickImage);
                intent.putExtras(bundle);
                setResult(Activity.RESULT_OK, intent);
                finish();
            }
        }
    }
}
