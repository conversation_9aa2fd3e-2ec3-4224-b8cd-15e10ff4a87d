package com.adins.mss.base;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.ProgressDialog;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import androidx.fragment.app.FragmentActivity;
import android.text.Editable;
import android.text.TextWatcher;
import android.text.method.PasswordTransformationMethod;
import android.view.ActionMode;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.adins.mss.base.authentication.LoginUserRequest;
import com.adins.mss.base.authentication.LoginUserResponse;
import com.adins.mss.base.commons.Helper;
import com.adins.mss.base.commons.ModeledActivity;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.login.DefaultLoginModel;
import com.adins.mss.base.login.LoginModel;
import com.adins.mss.base.syncfile.DownloadParams;
import com.adins.mss.base.syncfile.FileDownloader;
import com.adins.mss.base.syncfile.ImportDbFromCsv;
import com.adins.mss.base.syncfile.ImportDbParams;
import com.adins.mss.base.syncfile.JsonResponseSyncFile;
import com.adins.mss.base.syncfile.MobileDataFileDataAccess;
import com.adins.mss.base.syncfile.SyncFileRequest;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.base.util.LocaleHelper;
import com.adins.mss.base.util.Utility;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.EmbeddedInfo;
import com.adins.mss.dao.mobiledatafile;
import com.adins.mss.foundation.camerainapp.helper.Logger;
import com.adins.mss.foundation.config.ConfigFileReader;
import com.adins.mss.foundation.db.dataaccess.EmbeddedInfoDataAccess;
import com.adins.mss.foundation.dialog.DialogManager;
import com.adins.mss.foundation.dialog.NiftyDialogBuilder;
import com.adins.mss.foundation.formatter.Formatter;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.AuditDataType;
import com.adins.mss.foundation.http.AuditDataTypeGenerator;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.location.LocationTrackingManager;
import com.adins.mss.foundation.oauth2.OAuth2Client;
import com.adins.mss.foundation.oauth2.Token;
import com.adins.mss.foundation.oauth2.store.SharedPreferencesTokenStore;
import com.adins.mss.foundation.print.CopyBitmapLogo;
import com.adins.mss.foundation.security.storepreferences.ObscuredSharedPreferences;
import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.GoogleApiAvailability;
import com.services.RefreshToken;

import org.acra.ACRA;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Properties;


/**
 * Created by Aditya Purwa on 1/6/2015.
 * Activity for login.
 */
public abstract class LoginActivity extends FragmentActivity implements ModeledActivity<DefaultLoginModel>, LocationListener, BranchListDialogFragment.Listener {

    static final int REQUEST_CODE_RECOVER_PLAY_SERVICES = 1001;
    public static Context instance;
    public static List<mobiledatafile> data;
    public static List<mobiledatafile> activeData;
    public static int currentIdx = 0;
    public static mobiledatafile metadata;
    //variable penampung
    static String link, savePath, message;
    static ProgressDialog pDialog;
    private final String DevModeEnable = "$ADIMOBILEDEVMODEON$";
    private final String DevModeDisable = "$ADIMOBILEDEVMODEOFF$";
    private final String txtDevModeOn = "Enable Dev Mode";
    private final String txtDevModeOff = "Disable Dev Mode";
    public LocationTrackingManager manager;
    LocationManager mLocation = null;
    private LoginModel dataContext;
    private ObscuredSharedPreferences loginPreferences;
    //bong 1 apr 15 add menu to serverLinkActivity
    private Menu mainMenu;
    private ObscuredSharedPreferences sharedPref;
    private boolean isRooted;

    public static void downloadFiles() {
        currentIdx++;
        int i = currentIdx;
        metadata = data.get(i);
        message = "Downloading file " + (i + 1) + " out of " + data.size() + " files.";
        link = data.get(i).getFile_url();
        savePath = GlobalData.getSharedGlobalData().getSavePath();
        if (link == null || link.isEmpty()) {
            link = data.get(i).getAlternate_file_url();
            if (link == null || link.isEmpty()) {
                //popup message no link found
            }
        } else {
            //fileName = link.substring(link.lastIndexOf("/"), link.length());
            //savePath = savePath + fileName;
            DownloadParams parameters = new DownloadParams(savePath, instance, message, metadata);
            FileDownloader downloader = new FileDownloader();
            downloader.execute(parameters);
        }
    }

    public static void importFiles() {
        currentIdx++;
        int i = currentIdx;
        metadata = activeData.get(i);
        message = "Importing file " + (i + 1) + " out of " + activeData.size() + " files.";
        ImportDbParams importParams = new ImportDbParams(instance, message, metadata);
        ImportDbFromCsv importer = new ImportDbFromCsv();
        importer.execute(importParams);
    }

    private static boolean checkIsRooted() {
        return findBinary("su");
    }

    public static boolean findBinary(String binaryName) {
        boolean found = false;
        if (!found) {
            String[] places = {"/sbin/", "/system/bin/", "/system/xbin/",
                    "/data/local/xbin/", "/data/local/bin/",
                    "/system/sd/xbin/", "/system/bin/failsafe/", "/data/local/"};
            for (String where : places) {
                if (new File(where + binaryName).exists()) {
                    found = true;

                    break;
                }
            }
        }
        return found;
    }

    @Override
    public void onLocationChanged(Location location) {
        if (Global.IS_DEV)
            Logger.i("INFO", getString(R.string.masuk_on_change));
        if (location != null)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (location.isFromMockProvider())
                    DialogManager.showMockDialog(LoginActivity.this);
            }
    }

    @Override
    public void onStatusChanged(String provider, int status, Bundle extras) {
        if (Global.IS_DEV)
            Logger.i("INFO", getString(R.string.masuk_on_status_change));
    }

    @Override
    public void onProviderEnabled(String provider) {
        if (Global.IS_DEV)
            Logger.i("INFO", getString(R.string.masuk_on_provider_change));
        DialogManager.closeGPSAlert();
    }

    @Override
    public void onProviderDisabled(String provider) {
        DialogManager.showGPSAlert(this);
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // TODO Auto-generated method stub
        //return super.onCreateOptionsMenu(menu);
        MenuInflater inflater = getMenuInflater();
        inflater.inflate(R.menu.menu_item, menu);

        mainMenu = menu;
        return true;
    }

    private void bindLocationListener() {
        mLocation = (LocationManager) getSystemService(LOCATION_SERVICE);
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                    Utility.checkPermissionGranted(LoginActivity.this);
                    return;
                } else {
                    if (mLocation.getAllProviders().contains(LocationManager.GPS_PROVIDER))
                        mLocation.requestSingleUpdate(LocationManager.GPS_PROVIDER, this, null);
                }
            } else {
                if (mLocation.getAllProviders().contains(LocationManager.GPS_PROVIDER))
                    mLocation.requestSingleUpdate(LocationManager.GPS_PROVIDER, this, null);
            }
        } catch (IllegalArgumentException e) {
            FireCrash.log(e);// TODO: handle exception
        } catch (Exception e) {
            FireCrash.log(e);

        }
    }

    @Override
    public boolean onPrepareOptionsMenu(Menu menu) {
        // TODO Auto-generated method stub
        //return super.onPrepareOptionsMenu(menu);
        enableMenuItem(Global.IS_DEV);
        return true;
    }

    private void enableMenuItem(boolean enable) {
        if (enable) {
            mainMenu.findItem(R.id.menuItem).setVisible(true);
            mainMenu.findItem(R.id.serverLink).setVisible(true);
            mainMenu.findItem(R.id.devOption).setVisible(true);
        } else {
            mainMenu.findItem(R.id.menuItem).setVisible(false);
            mainMenu.findItem(R.id.serverLink).setVisible(false);
            mainMenu.findItem(R.id.devOption).setVisible(false);
        }
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        // TODO Auto-generated method stub
        //return super.onKeyUp(keyCode, event);
        if (event.getAction() == KeyEvent.ACTION_UP) {
            switch (keyCode) {

                case KeyEvent.KEYCODE_MENU:
                    if (Global.IS_DEV) {
                        mainMenu.performIdentifierAction(R.id.menuItem, 0);
                    }
                    return true;
            }
        }
        return super.onKeyUp(keyCode, event);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // TODO Auto-generated method stub
        //return super.onOptionsItemSelected(item);
        String title = item.getTitle().toString();
        if (getString(R.string.lblServerLinkId).equals(title)) {
            startActivity(new Intent(LoginActivity.this, ServerLinkActivity.class));
            //LoginActivity.this.finish();
        }
        if (getString(R.string.lblDevOption).equals(title)) {
            startActivity(new Intent(LoginActivity.this, DeveloperOptionActivity.class));
            //LoginActivity.this.finish();
        }
        // this.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //To can't screenshoot
        if (Tool.isProdFlavour(Global.FLAVORS)) {
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
        }
        LocaleHelper.onCreate(getApplicationContext(), LocaleHelper.ENGLSIH);
        String language = LocaleHelper.getLanguage(getApplicationContext());
        ACRA.getErrorReporter().putCustomData("LAST_CLASS_ACCESSED", getClass().getSimpleName());
//        Intent intent = new Intent(this, DefaultAutoSendService.class);
//        startService(intent);
        setContentView(R.layout.activity_login);

        sharedPref = ObscuredSharedPreferences.getPrefs(this,
                "GlobalData", Context.MODE_PRIVATE);

        GlobalData.getSharedGlobalData();
        GlobalData.getSharedGlobalData().loadFromProperties(getApplicationContext());

        GlobalData.getSharedGlobalData().setLocale(language);
        if (Global.IS_BYPASSROOT)
            isRooted = false;
        else
            isRooted = checkIsRooted();
        Properties prop = ConfigFileReader.propertiesFromFile(this, GlobalData.PROPERTY_FILENAME);
        boolean isDev = Boolean.parseBoolean(prop.getProperty(GlobalData.PROP_IS_DEVELOPER, "false"));
        Global.IS_DEV = sharedPref.getBoolean("IS_DEV", isDev);
        String urlHeader = sharedPref.getString("URL_HEADER", "");
        if (urlHeader != null && urlHeader.length() > 0) {
            GlobalData.getSharedGlobalData().setUrlMain(urlHeader);
            GlobalData.getSharedGlobalData().reloadUrl(this);
        }

        boolean accessTokenEnable = Boolean.parseBoolean(prop.getProperty(GlobalData.PROP_IS_REQUIRED_ACCESS_TOKEN, "false"));
        String propClientId = prop.getProperty(GlobalData.PROP_CLIENT_ID, "android");
        boolean encrypt = Boolean.parseBoolean(prop.getProperty(GlobalData.PROP_ENCRYPT, "false"));
        boolean decrypt = Boolean.parseBoolean(prop.getProperty(GlobalData.PROP_DECRYPT, "false"));
        boolean hasEncrypt = sharedPref.getBoolean("IS_ENCRYPT", encrypt);
        boolean hasDecrypt = sharedPref.getBoolean("IS_DECRYPT", decrypt);
        boolean isTokenEnable = sharedPref.getBoolean("IS_ACCESS_TOKEN_ENABLE", accessTokenEnable);
        String clientId = sharedPref.getString("CLIENT_ID", propClientId);

        GlobalData.getSharedGlobalData().setEncrypt(hasDecrypt);
        GlobalData.getSharedGlobalData().setDecrypt(hasEncrypt);
        GlobalData.getSharedGlobalData().setRequiresAccessToken(isTokenEnable);

        if (GlobalData.getSharedGlobalData().isRequiresAccessToken()) {
            GlobalData.getSharedGlobalData().setClientId(clientId);
        }

        if (isRooted) {
            DialogManager.showRootAlert(this, getApplicationContext());
        }
        instance = this;
        initialize();
        bindLocationListener();

        Global.printActivityClass = PrintActivity.class;
        CopyBitmapLogo copyBitmapLogo = new CopyBitmapLogo(this);
        copyBitmapLogo.copyLogoPrint();
    }

//    @Override
//    @TargetApi(Build.VERSION_CODES.N)
//    protected void attachBaseContext(Context newBase) {
//        Context context = newBase;
//        Locale locale;
//        try {
//            locale = new Locale(GlobalData.getSharedGlobalData().getLocale());
//            if (null == locale) {
//                locale = new Locale(LocaleHelper.ENGLSIH);
//            }
//            context = LocaleHelper.wrap(newBase, locale);
//        } catch (Exception e) {
//            locale = new Locale(LocaleHelper.ENGLSIH);
//            context = LocaleHelper.wrap(newBase, locale);
//        } finally {
//            super.attachBaseContext(context.getApplicationContext());
//        }
//    }

    @Override
    public void onResume() {
        super.onResume();
        Utility.checkPermissionGranted(LoginActivity.this);
        if (isRooted) {
            DialogManager.showRootAlert(this, getApplicationContext());
        }
        if (checkPlayServices()) {
            // Then we're good to go!
        }
        DialogManager.showGPSAlert(this);
        DialogManager.showTimeProviderAlert(this);
        if (Helper.isDevMode(this) && GlobalData.getSharedGlobalData().isDevMode()) {
            DialogManager.showTurnOffDevMode(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mLocation != null)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                    return;
                } else {
                    mLocation.removeUpdates(this);
                }
            } else {
                mLocation.removeUpdates(this);
            }
    }

    private boolean checkPlayServices() {
        int status = GoogleApiAvailability.getInstance().isGooglePlayServicesAvailable(this);
        if (status != ConnectionResult.SUCCESS) {
            if (GoogleApiAvailability.getInstance().isUserResolvableError(status)) {
                showErrorDialog(status);
            } else {
                Toast.makeText(this, getString(R.string.device_not_supported),
                        Toast.LENGTH_LONG).show();
                finish();
            }
            return false;
        }
        return true;
    }

    void showErrorDialog(int code) {
        GoogleApiAvailability.getInstance().getErrorDialog(this, code,
                REQUEST_CODE_RECOVER_PLAY_SERVICES).show();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
//      switch (requestCode) {
//        case REQUEST_CODE_RECOVER_PLAY_SERVICES:
//          if (resultCode == RESULT_CANCELED) {
//            Toast.makeText(this, "Google Play Services must be installed.",
//                Toast.LENGTH_SHORT).show();
//            finish();
//          }
//          return;
//      }
        super.onActivityResult(requestCode, resultCode, data);
    }

    private void initialize() {
        PackageInfo pInfo;
        try {
            new RefreshToken(getBaseContext()).onTokenRefresh();
        } catch (Exception e){
            e.printStackTrace();
        }
        try {
            pInfo = getPackageManager().getPackageInfo(getPackageName(), 0);
            Global.APP_VERSION = pInfo.versionName;
            Global.BUILD_VERSION = pInfo.versionCode;
        } catch (NameNotFoundException e) {
            FireCrash.log(e);
            if (Global.IS_DEV)
                e.printStackTrace();
        }


        TextView uniqeIds = (TextView) findViewById(R.id.uniqeIds);

        final String android_id;
        if(Build.VERSION.SDK_INT > 28){
            android_id = Settings.Secure.getString(this.getContentResolver(), Settings.Secure.ANDROID_ID);
            uniqeIds.setVisibility(View.VISIBLE);
        }else {
            android_id = "";
            uniqeIds.setVisibility(View.GONE);
        }

        uniqeIds.setText(android_id);


        TextView tvAppVersion = (TextView) findViewById(R.id.contentVersion);
        String versioning = getString(R.string.app_name) + " v." + Global.APP_VERSION;
        tvAppVersion.setText(versioning);

        uniqeIds.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                ClipboardManager clipboard = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
                ClipData clip = ClipData.newPlainText("Clip", android_id);
                clipboard.setPrimaryClip(clip);
            }
        });


        initializePreferences();
        // setModel(new DefaultLoginModel(this));
        setModel(getNewDefaultLoginModel(this));
        attachEventListener();
    }

    protected abstract DefaultLoginModel getNewDefaultLoginModel(Context context);

    private void initializePreferences() {
        loginPreferences = ObscuredSharedPreferences.getPrefs(this, DefaultLoginModel.LOGIN_PREFERENCES, MODE_PRIVATE);
    }

    @SuppressLint("StaticFieldLeak")
    private void getDownloadList() {
        new AsyncTask<Void, Void, Void>() {
            @Override
            protected void onPreExecute() {
                super.onPreExecute();
                pDialog = new ProgressDialog(instance);
                pDialog.setMessage(getResources().getString(R.string.getting_file_list));
                pDialog.setIndeterminate(true);
                pDialog.setProgressStyle(ProgressDialog.STYLE_SPINNER);
                pDialog.setCancelable(false);
                pDialog.show();
            }

            @Override
            protected Void doInBackground(Void... params) {
                AuditDataType audit = AuditDataTypeGenerator.generateActiveUserAuditData();
                GlobalData.getSharedGlobalData().loadFromProperties(instance);
                GlobalData.getSharedGlobalData().setAuditData(audit);
                SyncFileRequest request = new SyncFileRequest();
                request.setAudit(audit);
                request.setImei(audit.getDeviceId());
                Date maxdtm = MobileDataFileDataAccess.getMaxTimestamp(instance);
                request.setDtm_upd(maxdtm);
                String json = GsonHelper.toJson(request);
                String url = GlobalData.getSharedGlobalData().getUrlSyncFiles();
                boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                HttpCryptedConnection httpConn = new HttpCryptedConnection(instance, encrypt, decrypt);
                HttpConnectionResult serverResult = null;
                try {
                    serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
                    JsonResponseSyncFile response = GsonHelper.fromJson(serverResult.getResult(), JsonResponseSyncFile.class);
                    data = response.getListMobileDataFile();
                } catch (Exception e) {
                    FireCrash.log(e);
                    e.printStackTrace();
                }

                return null;
            }

            @Override
            protected void onPostExecute(Void aVoid) {
                super.onPostExecute(aVoid);
                if (pDialog.isShowing()) pDialog.dismiss();
                currentIdx = -1;
                if (data == null || data.isEmpty()) {
                    //toast no new data
                } else downloadFiles();
            }
        }.execute();
    }

    private void attachEventListener() {
        final Button loginButton = (Button) findViewById(R.id.buttonLogin);
        final Button exitButton  = (Button) findViewById(R.id.buttonExit);
        final Button resetButton = (Button) findViewById(R.id.buttonReset);
        final CheckBox checkShowPassword = (CheckBox) findViewById(R.id.checkShowPassword);
        final EditText editUserId = (EditText) findViewById(R.id.editUserId);
        final EditText editPassword = (EditText) findViewById(R.id.editPassword);

        editUserId.setCustomSelectionActionModeCallback(new ActionMode.Callback() {

            public boolean onPrepareActionMode(ActionMode mode, Menu menu) {
                return false;
            }

            public void onDestroyActionMode(ActionMode mode) {
            }

            public boolean onCreateActionMode(ActionMode mode, Menu menu) {
                return false;
            }

            public boolean onActionItemClicked(ActionMode mode, MenuItem item) {
                return false;
            }
        });

        editPassword.setCustomSelectionActionModeCallback(new ActionMode.Callback() {

            public boolean onPrepareActionMode(ActionMode mode, Menu menu) {
                return false;
            }

            public void onDestroyActionMode(ActionMode mode) {
            }

            public boolean onCreateActionMode(ActionMode mode, Menu menu) {
                return false;
            }

            public boolean onActionItemClicked(ActionMode mode, MenuItem item) {
                return false;
            }
        });

        editUserId.setText(loginPreferences.getString(DefaultLoginModel.LOGIN_PREFERENCES_USERNAME2, getString(R.string.text_empty)));
        editPassword.setText(loginPreferences.getString(DefaultLoginModel.LOGIN_PREFERENCES_PASSWORD, getString(R.string.text_empty)));
        getModel().setUsername(editUserId.getText().toString());
        getModel().setPassword(editPassword.getText().toString());
//        if(editUserId.getText().toString().length()>0&&editPassword.getText().toString().length()>0){
//        	checkRememberMe.setChecked(true);
//        }
        exitButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getModel().exit();
            }
        });
        resetButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                resetPassword();
            }
        });
        loginButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (loginButton.getText().equals(txtDevModeOn)) {
                    ObscuredSharedPreferences.Editor sharedPrefEditor = sharedPref.edit();
                    sharedPrefEditor.putBoolean("IS_DEV", true);
                    sharedPrefEditor.commit();
                    Global.IS_DEV = true;
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            enableMenuItem(true);
                            editPassword.setText("");
                        }
                    });
                } else if (loginButton.getText().equals(txtDevModeOff)) {
                    ObscuredSharedPreferences.Editor sharedPrefEditor = sharedPref.edit();
                    sharedPrefEditor.putBoolean("IS_DEV", false);
                    sharedPrefEditor.commit();
                    Global.IS_DEV = false;
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            enableMenuItem(false);
                            editPassword.setText("");
                        }
                    });
                } else {
//                    getModel().login();
                    String loginRequest = getStringForSending(editUserId.getText().toString(), getModel().getPassword(), true);
                    Context context = LoginActivity.this;

                    try {
                        if (Tool.isInternetconnected(context)) {
                            new MultiLoginTask(context, loginRequest).execute();
                        } else {
                            Toast.makeText(context, context.getString(R.string.connection_failed), Toast.LENGTH_SHORT).show();
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                }
            }
        });

        editUserId.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                getModel().setUsername(s.toString());
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        editPassword.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                getModel().setPassword(s.toString());
            }

            @Override
            public void afterTextChanged(Editable s) {
                String devMode = editPassword.getText().toString().trim();
                if (devMode != null && devMode.length() > 0 && devMode.equals(DevModeEnable)) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            loginButton.setText(txtDevModeOn);
                        }
                    });
                } else if (devMode != null && devMode.length() > 0 && devMode.equals(DevModeDisable)) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            loginButton.setText(txtDevModeOff);
                        }
                    });
                } else {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            loginButton.setText(getString(R.string.btnLogin));
                        }
                    });
                }
            }
        });

        checkShowPassword.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    editPassword.setTransformationMethod(null);
                } else {
                    editPassword.setTransformationMethod(new PasswordTransformationMethod());
                }
            }
        });


    }

    public void resetPassword() {}

    @Override
    public DefaultLoginModel getModel() {
        return (DefaultLoginModel) dataContext;
    }

    @Override
    public void setModel(DefaultLoginModel model) {
        dataContext = model;
    }

    private void showAskForDownloadDialog(int code) {
        final NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(this);
        builder.withTitle(getString(R.string.get_services))
                .withMessage(getString(R.string.get_services_message))
                .withButton1Text(getString(R.string.get_services))
                .setButton1Click(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        builder.dismiss();
//                        Intent download = new Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=com.google.android.gms"));
                        Intent download = new Intent(Intent.ACTION_VIEW, Uri.parse(getString(R.string.download_services)));
                        startActivity(download);
                        // exit();
                    }

                }).show();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        switch (requestCode) {
            case Utility.REQUEST_CODE_ASK_MULTIPLE_PERMISSIONS: {
                if (Utility.checkPermissionResult(LoginActivity.this, permissions, grantResults))
                    bindLocationListener();
            }
            break;
            default: {
                super.onRequestPermissionsResult(requestCode, permissions, grantResults);
            }
        }
    }

    @SuppressLint("StaticFieldLeak")
    private class MultiLoginTask extends AsyncTask<Void, Void, HttpConnectionResult> {
        private final Context context;
        private final String loginRequest;
        private String message;
        private ProgressDialog dialog;

        public MultiLoginTask(Context context, String loginRequest) {
            this.context = context;
            this.loginRequest = loginRequest;
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();

            dialog = new ProgressDialog(context);
            dialog.setCancelable(false);
            dialog.setMessage(context.getString(com.adins.mss.base.R.string.please_wait));
            dialog.show();
        }

        @Override
        protected void onPostExecute(HttpConnectionResult result) {
            super.onPostExecute(result);
            dialog.dismiss();

            if (result != null && result.isOK()) {
                String json = result.getResult();
                final LoginUserResponse loginUserResponse = GsonHelper.fromJson(json, LoginUserResponse.class);
                if (loginUserResponse.getStatus().getCode() == 0) {

                    //Update loginPreferences
                    SharedPreferences.Editor loginPrefsEditor = loginPreferences.edit();
                    loginPrefsEditor.putString(DefaultLoginModel.LOGIN_PREFERENCES_USERNAME2, getModel().getUsername());
                    loginPrefsEditor.putString(DefaultLoginModel.LOGIN_PREFERENCES_PASSWORD, getModel().getPassword());
                    loginPrefsEditor.apply();

                    if (loginUserResponse.getListLoginId().size() > 1) {
                        Bundle args = new Bundle();
                        args.putString("data", json);

                        BranchListDialogFragment branchDialog = BranchListDialogFragment.newInstance(args);
                        branchDialog.show(getSupportFragmentManager(), "Pick Branch");
                    } else {
                        getModel().setUsername(loginUserResponse.getListLoginId().get(0).getLoginId());
                        getModel().login();
                    }
                } else {
                    Toast.makeText(
                            context,
                            "Error Code: " + loginUserResponse.getStatus().getCode()
                                    + context.getString(com.adins.mss.base.R.string.divider_vertical_bar) + loginUserResponse.getStatus().getMessage(),
                            Toast.LENGTH_LONG
                    ).show();
                }
            } else {
                if (result == null) Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
                else Toast.makeText(context, result.getResult(), Toast.LENGTH_SHORT).show();
            }
        }

        @Override
        protected HttpConnectionResult doInBackground(Void... voids) {

            String result = "";
            if (GlobalData.getSharedGlobalData().isRequiresAccessToken()) {
                String urlService = GlobalData.getSharedGlobalData().getUrlMain();
                int idx = urlService.indexOf("/services");
                String urlMain = urlService.substring(0, idx);
                OAuth2Client client = new OAuth2Client(getModel().getUsername(), getModel().getPassword(), GlobalData.getSharedGlobalData().getClientId(), null, urlMain);
                GlobalData.getSharedGlobalData().setoAuth2Client(client);
//                if(GlobalData.getSharedGlobalData().getToken()==null) {
                try {
                    Token token = client.getAccessToken(context);
                    result = "success";
                    if (result.equals("success")) {
                        SharedPreferencesTokenStore tokenStore = new SharedPreferencesTokenStore(context);
                        tokenStore.store(GlobalData.getSharedGlobalData().getoAuth2Client().getUsername(), token);
                        GlobalData.getSharedGlobalData().setToken(token);
                    }
                } catch (RuntimeException ex) {
                    result = ex.getMessage();
                } catch (IOException e) {
                    FireCrash.log(e);
                    e.printStackTrace();
                    result = context.getResources().getString(R.string.token_failed_to_store);
                }

                message = result;
            }

            if (result.equalsIgnoreCase("") || result.contains("success")) {
                String json = loginRequest;
                String url  = GlobalData.getSharedGlobalData().getURL_MULTILOGIN();

                boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                HttpCryptedConnection httpConn = new HttpCryptedConnection(context, encrypt, decrypt);
                HttpConnectionResult serverResult = null;

                Logger.i("INFO", "Request: ".concat(json));
                try {
                    serverResult= httpConn.requestToServer(url, json);
                    return serverResult;
                } catch (Exception e) {
                    ACRA.getErrorReporter().putCustomData("errorRequestToServer", e.getMessage());
                    ACRA.getErrorReporter().putCustomData("errorRequestToServer", Tool.getSystemDateTime().toLocaleString());
                    ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat request to server"));
                    if (Global.IS_DEV)
                        e.printStackTrace();
                }
            }

            return null;
        }
    }

    protected static String getStringForSending(String username, String password, boolean isFreshInstall) {
        //Initiate audit data
        AuditDataType audit = AuditDataTypeGenerator.generateActiveUserAuditData();
        GlobalData.getSharedGlobalData().setAuditData(audit);

        String strFlagFreshInstall = isFreshInstall ? "1" : "0";

        //Create LoginUserRequest
        LoginUserRequest data = new LoginUserRequest();
        data.setAudit(audit);
        data.setUsername(username);
        data.setPassword(password);
        data.setFlagFreshInstall(strFlagFreshInstall);
        data.setFcmTokenId(Global.Token);

        //GlobalData.imei were set from method authenticate
        data.addImeiAndroidIdToUnstructured();

//		AuthenticationModel data = new AuthenticationModel(username, password, true, isFreshInstall);
        return Formatter.getJsonFromObject(data);
    }

    @Override
    public void onBranchClicked(LoginUserResponse.LoginId loginId, int position) {
        //Set Username
        getModel().setUsername(loginId.getLoginId());
        getModel().login();
    }
}