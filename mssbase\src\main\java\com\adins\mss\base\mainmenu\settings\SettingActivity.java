package com.adins.mss.base.mainmenu.settings;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.RadioButton;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.util.LocaleHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.AuditDataType;
import com.adins.mss.foundation.http.AuditDataTypeGenerator;

import java.util.Locale;

public class SettingActivity extends Activity {

    private Button btnSave;
    private RadioButton rdIndonesia;
    private RadioButton rdEnglish;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (Tool.isProdFlavour(Global.FLAVORS)) {
            //To can't screenshoot
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
        }

        setContentView(R.layout.activity_setting);
        btnSave = (Button) findViewById(R.id.btnSave);
        rdIndonesia = (RadioButton) findViewById(R.id.rdBahasa);
        rdEnglish = (RadioButton) findViewById(R.id.rdEnglish);

        String language = LocaleHelper.getLanguage(getApplicationContext());
        LocaleHelper.setLocale(getApplicationContext(), LocaleHelper.BAHASA_INDONESIA);
        GlobalData.getSharedGlobalData().setLocale(LocaleHelper.BAHASA_INDONESIA);
        if (language.equals(LocaleHelper.BAHASA_INDONESIA)) {
            rdIndonesia.setChecked(true);
        } else {
            rdEnglish.setChecked(true);
        }
        btnSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (rdEnglish.isChecked()) {
                    LocaleHelper.setLocale(getApplicationContext(), LocaleHelper.ENGLSIH);
                    GlobalData.getSharedGlobalData().setLocale(LocaleHelper.ENGLSIH);
                } else if (rdIndonesia.isChecked()) {
                    LocaleHelper.setLocale(getApplicationContext(), LocaleHelper.BAHASA_INDONESIA);
                    GlobalData.getSharedGlobalData().setLocale(LocaleHelper.BAHASA_INDONESIA);
                }
                AuditDataType tempAudit = AuditDataTypeGenerator.generateActiveUserAuditData();
                GlobalData.getSharedGlobalData().setAuditData(tempAudit);
                finish();
            }
        });
    }

    @Override
    protected void attachBaseContext(Context newBase) {
        Context context = newBase;
        Locale locale;
        try {
            locale = new Locale(GlobalData.getSharedGlobalData().getLocale());
            if (null == locale) {
                locale = new Locale(LocaleHelper.ENGLSIH);
            }
            context = LocaleHelper.wrap(newBase, locale);
        } catch (Exception e) {
            locale = new Locale(LocaleHelper.ENGLSIH);
            context = LocaleHelper.wrap(newBase, locale);
        } finally {
            super.attachBaseContext(context);
        }
    }
}
