package com.adins.mss.base.tasklog;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.os.Build;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.mainmenu.MainMenuActivity;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.ReminderPo;
import com.adins.mss.dao.TaskH;
import com.adins.mss.foundation.db.dataaccess.ReminderPoDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskHDataAccess;
import com.adins.mss.foundation.formatter.Formatter;

import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

public class TaskLogArrayAdapter extends ArrayAdapter<TaskH> {

    boolean isLog = false;
    private Context context;
    private List<TaskH> objects;
    private int color = Color.BLACK;
    private GradientDrawable gradient;
//	public TaskLogArrayAdapter(Context context,
//			List<TaskH> objects, int color) {
//		super(context, R.layout.log_item_layout, objects);
//		// TODO Auto-generated constructor stub
//		this.context = context;
//		this.objects = objects;
//		this.color=color;
//	}

    public TaskLogArrayAdapter(Context context,
                               List<TaskH> objects, GradientDrawable gradient) {
        super(context, R.layout.log_item_layout, objects);
        // TODO Auto-generated constructor stub
        this.context = context;
        this.objects = objects;
        this.gradient = gradient;
    }

    public TaskLogArrayAdapter(Context context, List<TaskH> objects, boolean isLog) {
        super(context, R.layout.log_item_layout, objects);
        // TODO Auto-generated constructor stub
        this.context = context;
        this.objects = objects;
        this.isLog = isLog;
    }

    @Override
    public int getCount() {
        return objects.size();
    }

    @Override
    public View getView(final int position, View convertView, ViewGroup parent) {

        if (null == convertView) {
            convertView = LayoutInflater.from(getContext()).inflate(R.layout.log_item_layout, parent, false);
        }

        LinearLayout layout = (LinearLayout) convertView.findViewById(R.id.bgGridLog);
        ImageView indicatorLamp = (ImageView) convertView.findViewById(R.id.indicatorLamp);
        indicatorLamp.setVisibility(View.GONE);
        TextView txtId = (TextView) convertView.findViewById(R.id.txtTaskId);
        TextView txtAppl = (TextView) convertView.findViewById(R.id.txtApplNo);
        TextView txtName = (TextView) convertView.findViewById(R.id.txtName);
        TextView txtTime = (TextView) convertView.findViewById(R.id.txtTimeSend);
        TextView txtScheme = (TextView) convertView.findViewById(R.id.txtScheme);
        TextView txtStatus = (TextView) convertView.findViewById(R.id.txtStatusApp);
        ImageView logIcon = (ImageView) convertView.findViewById(R.id.logIcon);

        /*Penambahan task reminder PO*/
        LinearLayout layoutExpDate = (LinearLayout) convertView.findViewById(R.id.layoutExpiredDate);
        TextView txtExpDate = (TextView) convertView.findViewById(R.id.txtExpDate);
        TextView lblExpDate = (TextView) convertView.findViewById(R.id.lblExpDate);

        TaskH task = objects.get(position);
        String taskId = task.getTask_id();
        String applNo = task.getAppl_no();
        String custName = task.getCustomer_name();

        String appStatus = task.getStatus_application();
        String formId = "";
        if (task.getScheme() != null)
            formId = task.getScheme().getForm_id();
        Date dTime = task.getSubmit_date();
        String sTime = "";
        try {
            sTime = Formatter.formatDate(dTime, Global.DATE_TIME_STR_FORMAT);
        } catch (Exception e) {
            FireCrash.log(e);
            try {
                sTime = Formatter.formatDate(task.getDtm_crt(), Global.DATE_TIME_STR_FORMAT);
            } catch (Exception e2) {
                e2.printStackTrace();
            }
        }

        try {
            if (gradient != null)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                    layout.setBackground(gradient);
                } else {
                    layout.setBackgroundDrawable(gradient);
                }
            String application = GlobalData.getSharedGlobalData().getAuditData().getApplication();
            if (isLog) {
                if (Global.APPLICATION_SURVEY.equalsIgnoreCase(application)) {
                    if (MainMenuActivity.mnSVYApproval != null || MainMenuActivity.mnSVYVerify != null) {
                        logIcon.setVisibility(View.VISIBLE);
                        if (task.getIs_prepocessed() != null && task.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)) {
                            if (task.getStatus().equals(TaskHDataAccess.STATUS_SEND_REJECTED)) {
                                logIcon.setImageResource(R.drawable.ic_verification_re_log);
                            } else {
                                logIcon.setImageResource(R.drawable.ic_verification_log);
                            }
                        } else if (task.getIs_prepocessed() != null && task.getIs_prepocessed().equals(Global.FORM_TYPE_APPROVAL)) {
                            if (task.getStatus().equals(TaskHDataAccess.STATUS_SEND_REJECTED)) {
                                logIcon.setImageResource(R.drawable.ic_approval_re_log);
                            } else {
                                logIcon.setImageResource(R.drawable.ic_approval_log);
                            }
                        } else {
                            logIcon.setImageResource(R.drawable.ic_log_white);
                        }
                    }

                    // Flagging on task log menu for reminder po task (2022-08-12)
                    String uuidTaskH = task.getUuid_task_h();
                    ReminderPo reminderPo = ReminderPoDataAccess.getOneByUuidTaskH(context, uuidTaskH);
                    if (null != reminderPo) {
                        String taskCreateDate = Formatter.formatDate(reminderPo.getDtm_crt(), Global.DATE_STR_FORMAT2);
                        String currentDate = Formatter.formatDate(new Date(), Global.DATE_STR_FORMAT2);
                        boolean isCurrentDateTask = taskCreateDate.compareToIgnoreCase(currentDate) == 0;
                        if (isCurrentDateTask) {
                            layoutExpDate.setVisibility(View.VISIBLE);
                            String expDate = "";
                            try {
                                expDate = Formatter.formatDate(reminderPo.getExpired_date(), Global.DATE_STR_FORMAT);
                            } catch (Exception e) {
                                if(Global.IS_DEV) {
                                    e.printStackTrace();
                                }
                            }

                            if(StringUtils.isNotBlank(expDate)) {
                               txtExpDate.setText(expDate);
                               lblExpDate.setText("Expired Date");
                            }

                            if ("Expired".equalsIgnoreCase(reminderPo.getStatus_po())) {
                                layout.setBackgroundResource(R.drawable.bg_task_expired_po);
                            } else if ("Pending".equalsIgnoreCase(reminderPo.getStatus_po())) {
                                layout.setBackgroundResource(R.drawable.bg_task_reminder_po);
                            }
                        } else {
                            layout.setBackgroundResource(R.drawable.spinner_background);
                            txtExpDate.setText("");
                            lblExpDate.setText("");
                        }
                    } else {
                        layout.setBackgroundResource(R.drawable.spinner_background);
                        txtExpDate.setText("");
                        lblExpDate.setText("");
                    }
                }
            }
//        	if(task.getIs_prepocessed()!=null&&task.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)){
//        		if(task.getStatus().equals(TaskHDataAccess.STATUS_SEND_REJECTED)){
//        			layout.setBackgroundResource(R.drawable.rejected_background);
//        		}else{
//        			layout.setBackgroundResource(R.drawable.verified_background);
//        		}
//        	}else if(task.getIs_prepocessed()!=null&&task.getIs_prepocessed().equals(Global.FORM_TYPE_APPROVAL)){
//        		if(task.getStatus().equals(TaskHDataAccess.STATUS_SEND_REJECTED)){
//        			layout.setBackgroundResource(R.drawable.rejected_background);
//        		}else{
//        			layout.setBackgroundResource(R.drawable.approved_background);
//        		}
//        	}    		
        } catch (Exception e) {
            FireCrash.log(e);
        }

        if (taskId != null) {
            if (taskId.contains("belum di-mapping")) {
                txtId.setText("");
                txtScheme.setText(taskId);
                layout.setBackgroundResource(R.drawable.mediumpriority_background);
                txtTime.setVisibility(View.GONE);
            } else {
                txtId.setText(taskId);
                txtScheme.setText(formId);
            }
        }


        txtAppl.setText(applNo);
        txtName.setText(custName);
        txtName.setSelected(true);
        if (appStatus != null) {
            txtStatus.setText(appStatus);
        }
        else {
            txtStatus.setText("-");
        }
        txtTime.setText(sTime);

        //TODO : buat masukkin icon ke indicator lamp
        //belum ditentuin parameternya
        boolean parameter = false;
        if (TaskHDataAccess.STATUS_TASK_APPROVAL.equals(task.getStatus()) ||
                TaskHDataAccess.STATUS_TASK_VERIFICATION.equals(task.getStatus())) {
            parameter = false;
            indicatorLamp.setVisibility(View.VISIBLE);
        }
        if (TaskHDataAccess.STATUS_TASK_APPROVAL_DOWNLOAD.equals(task.getStatus()) ||
                TaskHDataAccess.STATUS_TASK_VERIFICATION_DOWNLOAD.equals(task.getStatus())) {
            parameter = true;
            indicatorLamp.setVisibility(View.VISIBLE);
        }
        if (parameter) {
            indicatorLamp.setImageResource(R.drawable.ic_downloaded);
        } else {
            indicatorLamp.setImageResource(R.drawable.ic_undownload);
        }
        return convertView;
    }

    public List<TaskH> getObjects() {
        return objects;
    }

    public void setObjects(List<TaskH> objects) {
        this.objects.clear();
        if (!objects.isEmpty()) {
            for (TaskH task : objects) {
                this.objects.add(task);
            }
        }
    }

}
