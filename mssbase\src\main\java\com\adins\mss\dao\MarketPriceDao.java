package com.adins.mss.dao;

import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;

import com.adins.mss.dao.MarketPrice;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "MS_MARKET_PRICE".
*/
public class MarketPriceDao extends AbstractDao<MarketPrice, Long> {

    public static final String TABLENAME = "MS_MARKET_PRICE";

    /**
     * Properties of entity MarketPrice.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Price_id = new Property(0, long.class, "price_id", true, "PRICE_ID");
        public final static Property Asset_code = new Property(1, String.class, "asset_code", false, "ASSET_CODE");
        public final static Property Manufacturing_year = new Property(2, String.class, "manufacturing_year", false, "MANUFACTURING_YEAR");
        public final static Property Office_code = new Property(3, String.class, "office_code", false, "OFFICE_CODE");
        public final static Property Tolerance_prctg = new Property(4, Double.class, "tolerance_prctg", false, "TOLERANCE_PRCTG");
        public final static Property Market_price = new Property(5, Double.class, "market_price", false, "MARKET_PRICE");
        public final static Property Effective_date = new Property(6, java.util.Date.class, "effective_date", false, "EFFECTIVE_DATE");
        public final static Property Is_deleted = new Property(7, Integer.class, "is_deleted", false, "IS_DELETED");
        public final static Property Dtm_upd = new Property(8, java.util.Date.class, "dtm_upd", false, "DTM_UPD");
    };


    public MarketPriceDao(DaoConfig config) {
        super(config);
    }
    
    public MarketPriceDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"MS_MARKET_PRICE\" (" + //
                "\"PRICE_ID\" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL ," + // 0: price_id
                "\"ASSET_CODE\" TEXT," + // 1: asset_code
                "\"MANUFACTURING_YEAR\" TEXT," + // 2: manufacturing_year
                "\"OFFICE_CODE\" TEXT," + // 3: office_code
                "\"TOLERANCE_PRCTG\" REAL," + // 4: tolerance_prctg
                "\"MARKET_PRICE\" REAL," + // 5: market_price
                "\"EFFECTIVE_DATE\" INTEGER," + // 6: effective_date
                "\"IS_DELETED\" INTEGER," + // 7: is_deleted
                "\"DTM_UPD\" INTEGER);"); // 8: dtm_upd
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"MS_MARKET_PRICE\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, MarketPrice entity) {
        stmt.clearBindings();
        stmt.bindLong(1, entity.getPrice_id());
 
        String asset_code = entity.getAsset_code();
        if (asset_code != null) {
            stmt.bindString(2, asset_code);
        }
 
        String manufacturing_year = entity.getManufacturing_year();
        if (manufacturing_year != null) {
            stmt.bindString(3, manufacturing_year);
        }
 
        String office_code = entity.getOffice_code();
        if (office_code != null) {
            stmt.bindString(4, office_code);
        }
 
        Double tolerance_prctg = entity.getTolerance_prctg();
        if (tolerance_prctg != null) {
            stmt.bindDouble(5, tolerance_prctg);
        }
 
        Double market_price = entity.getMarket_price();
        if (market_price != null) {
            stmt.bindDouble(6, market_price);
        }
 
        java.util.Date effective_date = entity.getEffective_date();
        if (effective_date != null) {
            stmt.bindLong(7, effective_date.getTime());
        }
 
        Integer is_deleted = entity.getIs_deleted();
        if (is_deleted != null) {
            stmt.bindLong(8, is_deleted);
        }
 
        java.util.Date dtm_upd = entity.getDtm_upd();
        if (dtm_upd != null) {
            stmt.bindLong(9, dtm_upd.getTime());
        }
    }

    /** @inheritdoc */
    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.getLong(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public MarketPrice readEntity(Cursor cursor, int offset) {
        MarketPrice entity = new MarketPrice( //
            cursor.getLong(offset + 0), // price_id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // asset_code
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // manufacturing_year
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // office_code
            cursor.isNull(offset + 4) ? null : cursor.getDouble(offset + 4), // tolerance_prctg
            cursor.isNull(offset + 5) ? null : cursor.getDouble(offset + 5), // market_price
            cursor.isNull(offset + 6) ? null : new java.util.Date(cursor.getLong(offset + 6)), // effective_date
            cursor.isNull(offset + 7) ? null : cursor.getInt(offset + 7), // is_deleted
            cursor.isNull(offset + 8) ? null : new java.util.Date(cursor.getLong(offset + 8)) // dtm_upd
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, MarketPrice entity, int offset) {
        entity.setPrice_id(cursor.getLong(offset + 0));
        entity.setAsset_code(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setManufacturing_year(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setOffice_code(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setTolerance_prctg(cursor.isNull(offset + 4) ? null : cursor.getDouble(offset + 4));
        entity.setMarket_price(cursor.isNull(offset + 5) ? null : cursor.getDouble(offset + 5));
        entity.setEffective_date(cursor.isNull(offset + 6) ? null : new java.util.Date(cursor.getLong(offset + 6)));
        entity.setIs_deleted(cursor.isNull(offset + 7) ? null : cursor.getInt(offset + 7));
        entity.setDtm_upd(cursor.isNull(offset + 8) ? null : new java.util.Date(cursor.getLong(offset + 8)));
     }
    
    /** @inheritdoc */
    @Override
    protected Long updateKeyAfterInsert(MarketPrice entity, long rowId) {
        entity.setPrice_id(rowId);
        return rowId;
    }
    
    /** @inheritdoc */
    @Override
    public Long getKey(MarketPrice entity) {
        if(entity != null) {
            return entity.getPrice_id();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
}
