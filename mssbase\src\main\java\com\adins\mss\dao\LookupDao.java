package com.adins.mss.dao;

import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;

import com.adins.mss.dao.Lookup;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "MS_LOOKUP".
*/
public class LookupDao extends AbstractDao<Lookup, String> {

    public static final String TABLENAME = "MS_LOOKUP";

    /**
     * Properties of entity Lookup.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_lookup = new Property(0, String.class, "uuid_lookup", true, "UUID_LOOKUP");
        public final static Property Option_id = new Property(1, String.class, "option_id", false, "OPTION_ID");
        public final static Property Code = new Property(2, String.class, "code", false, "CODE");
        public final static Property Value = new Property(3, String.class, "value", false, "VALUE");
        public final static Property Filter1 = new Property(4, String.class, "filter1", false, "FILTER1");
        public final static Property Filter2 = new Property(5, String.class, "filter2", false, "FILTER2");
        public final static Property Filter3 = new Property(6, String.class, "filter3", false, "FILTER3");
        public final static Property Filter4 = new Property(7, String.class, "filter4", false, "FILTER4");
        public final static Property Filter5 = new Property(8, String.class, "filter5", false, "FILTER5");
        public final static Property Sequence = new Property(9, Integer.class, "sequence", false, "SEQUENCE");
        public final static Property Usr_crt = new Property(10, String.class, "usr_crt", false, "USR_CRT");
        public final static Property Dtm_crt = new Property(11, java.util.Date.class, "dtm_crt", false, "DTM_CRT");
        public final static Property Usr_upd = new Property(12, String.class, "usr_upd", false, "USR_UPD");
        public final static Property Dtm_upd = new Property(13, java.util.Date.class, "dtm_upd", false, "DTM_UPD");
        public final static Property Uuid_question_set = new Property(14, String.class, "uuid_question_set", false, "UUID_QUESTION_SET");
        public final static Property Lov_group = new Property(15, String.class, "lov_group", false, "LOV_GROUP");
        public final static Property Is_active = new Property(16, String.class, "is_active", false, "IS_ACTIVE");
        public final static Property Is_deleted = new Property(17, String.class, "is_deleted", false, "IS_DELETED");
    };


    public LookupDao(DaoConfig config) {
        super(config);
    }
    
    public LookupDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"MS_LOOKUP\" (" + //
                "\"UUID_LOOKUP\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_lookup
                "\"OPTION_ID\" TEXT," + // 1: option_id
                "\"CODE\" TEXT," + // 2: code
                "\"VALUE\" TEXT," + // 3: value
                "\"FILTER1\" TEXT," + // 4: filter1
                "\"FILTER2\" TEXT," + // 5: filter2
                "\"FILTER3\" TEXT," + // 6: filter3
                "\"FILTER4\" TEXT," + // 7: filter4
                "\"FILTER5\" TEXT," + // 8: filter5
                "\"SEQUENCE\" INTEGER," + // 9: sequence
                "\"USR_CRT\" TEXT," + // 10: usr_crt
                "\"DTM_CRT\" INTEGER," + // 11: dtm_crt
                "\"USR_UPD\" TEXT," + // 12: usr_upd
                "\"DTM_UPD\" INTEGER," + // 13: dtm_upd
                "\"UUID_QUESTION_SET\" TEXT," + // 14: uuid_question_set
                "\"LOV_GROUP\" TEXT," + // 15: lov_group
                "\"IS_ACTIVE\" TEXT," + // 16: is_active
                "\"IS_DELETED\" TEXT);"); // 17: is_deleted
        // Add Indexes
        db.execSQL("CREATE INDEX " + constraint + "IDX_MS_LOOKUP_LOV_GROUP_FILTER1_FILTER2_FILTER3_FILTER4_FILTER5_IS_ACTIVE_IS_DELETED ON MS_LOOKUP" +
                " (\"LOV_GROUP\",\"FILTER1\",\"FILTER2\",\"FILTER3\",\"FILTER4\",\"FILTER5\",\"IS_ACTIVE\",\"IS_DELETED\");");
        db.execSQL("CREATE INDEX " + constraint + "IDX_MS_LOOKUP_CODE_LOV_GROUP_IS_ACTIVE_IS_DELETED ON MS_LOOKUP" +
                " (\"CODE\",\"LOV_GROUP\",\"IS_ACTIVE\",\"IS_DELETED\");");
        db.execSQL("CREATE INDEX " + constraint + "IDX_MS_LOOKUP_LOV_GROUP_FILTER1_FILTER2_FILTER3_FILTER4_IS_ACTIVE_IS_DELETED ON MS_LOOKUP" +
                " (\"LOV_GROUP\",\"FILTER1\",\"FILTER2\",\"FILTER3\",\"FILTER4\",\"IS_ACTIVE\",\"IS_DELETED\");");
        db.execSQL("CREATE INDEX " + constraint + "IDX_MS_LOOKUP_LOV_GROUP_FILTER1_FILTER2_FILTER3_IS_ACTIVE_IS_DELETED ON MS_LOOKUP" +
                " (\"LOV_GROUP\",\"FILTER1\",\"FILTER2\",\"FILTER3\",\"IS_ACTIVE\",\"IS_DELETED\");");
        db.execSQL("CREATE INDEX " + constraint + "IDX_MS_LOOKUP_LOV_GROUP_FILTER1_FILTER2_IS_ACTIVE_IS_DELETED ON MS_LOOKUP" +
                " (\"LOV_GROUP\",\"FILTER1\",\"FILTER2\",\"IS_ACTIVE\",\"IS_DELETED\");");
        db.execSQL("CREATE INDEX " + constraint + "IDX_MS_LOOKUP_LOV_GROUP_FILTER1_IS_ACTIVE_IS_DELETED ON MS_LOOKUP" +
                " (\"LOV_GROUP\",\"FILTER1\",\"IS_ACTIVE\",\"IS_DELETED\");");
        db.execSQL("CREATE INDEX " + constraint + "IDX_MS_LOOKUP_LOV_GROUP_IS_ACTIVE_IS_DELETED ON MS_LOOKUP" +
                " (\"LOV_GROUP\",\"IS_ACTIVE\",\"IS_DELETED\");");
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"MS_LOOKUP\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, Lookup entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_lookup());
 
        String option_id = entity.getOption_id();
        if (option_id != null) {
            stmt.bindString(2, option_id);
        }
 
        String code = entity.getCode();
        if (code != null) {
            stmt.bindString(3, code);
        }
 
        String value = entity.getValue();
        if (value != null) {
            stmt.bindString(4, value);
        }
 
        String filter1 = entity.getFilter1();
        if (filter1 != null) {
            stmt.bindString(5, filter1);
        }
 
        String filter2 = entity.getFilter2();
        if (filter2 != null) {
            stmt.bindString(6, filter2);
        }
 
        String filter3 = entity.getFilter3();
        if (filter3 != null) {
            stmt.bindString(7, filter3);
        }
 
        String filter4 = entity.getFilter4();
        if (filter4 != null) {
            stmt.bindString(8, filter4);
        }
 
        String filter5 = entity.getFilter5();
        if (filter5 != null) {
            stmt.bindString(9, filter5);
        }
 
        Integer sequence = entity.getSequence();
        if (sequence != null) {
            stmt.bindLong(10, sequence);
        }
 
        String usr_crt = entity.getUsr_crt();
        if (usr_crt != null) {
            stmt.bindString(11, usr_crt);
        }
 
        java.util.Date dtm_crt = entity.getDtm_crt();
        if (dtm_crt != null) {
            stmt.bindLong(12, dtm_crt.getTime());
        }
 
        String usr_upd = entity.getUsr_upd();
        if (usr_upd != null) {
            stmt.bindString(13, usr_upd);
        }
 
        java.util.Date dtm_upd = entity.getDtm_upd();
        if (dtm_upd != null) {
            stmt.bindLong(14, dtm_upd.getTime());
        }
 
        String uuid_question_set = entity.getUuid_question_set();
        if (uuid_question_set != null) {
            stmt.bindString(15, uuid_question_set);
        }
 
        String lov_group = entity.getLov_group();
        if (lov_group != null) {
            stmt.bindString(16, lov_group);
        }
 
        String is_active = entity.getIs_active();
        if (is_active != null) {
            stmt.bindString(17, is_active);
        }
 
        String is_deleted = entity.getIs_deleted();
        if (is_deleted != null) {
            stmt.bindString(18, is_deleted);
        }
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public Lookup readEntity(Cursor cursor, int offset) {
        Lookup entity = new Lookup( //
            cursor.getString(offset + 0), // uuid_lookup
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // option_id
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // code
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // value
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // filter1
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // filter2
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // filter3
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7), // filter4
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8), // filter5
            cursor.isNull(offset + 9) ? null : cursor.getInt(offset + 9), // sequence
            cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10), // usr_crt
            cursor.isNull(offset + 11) ? null : new java.util.Date(cursor.getLong(offset + 11)), // dtm_crt
            cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12), // usr_upd
            cursor.isNull(offset + 13) ? null : new java.util.Date(cursor.getLong(offset + 13)), // dtm_upd
            cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14), // uuid_question_set
            cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15), // lov_group
            cursor.isNull(offset + 16) ? null : cursor.getString(offset + 16), // is_active
            cursor.isNull(offset + 17) ? null : cursor.getString(offset + 17) // is_deleted
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, Lookup entity, int offset) {
        entity.setUuid_lookup(cursor.getString(offset + 0));
        entity.setOption_id(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setCode(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setValue(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setFilter1(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setFilter2(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setFilter3(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setFilter4(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
        entity.setFilter5(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
        entity.setSequence(cursor.isNull(offset + 9) ? null : cursor.getInt(offset + 9));
        entity.setUsr_crt(cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10));
        entity.setDtm_crt(cursor.isNull(offset + 11) ? null : new java.util.Date(cursor.getLong(offset + 11)));
        entity.setUsr_upd(cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12));
        entity.setDtm_upd(cursor.isNull(offset + 13) ? null : new java.util.Date(cursor.getLong(offset + 13)));
        entity.setUuid_question_set(cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14));
        entity.setLov_group(cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15));
        entity.setIs_active(cursor.isNull(offset + 16) ? null : cursor.getString(offset + 16));
        entity.setIs_deleted(cursor.isNull(offset + 17) ? null : cursor.getString(offset + 17));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(Lookup entity, long rowId) {
        return entity.getUuid_lookup();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(Lookup entity) {
        if(entity != null) {
            return entity.getUuid_lookup();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
}
