package com.adins.mss.base.todo.form;

import android.app.ProgressDialog;
import android.os.AsyncTask;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentTransaction;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.mainmenu.MainMenuActivity;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.PrintItem;
import com.adins.mss.dao.Scheme;
import com.adins.mss.foundation.db.dataaccess.PrintItemDataAccess;
import com.adins.mss.foundation.db.dataaccess.SchemeDataAccess;
import com.adins.mss.foundation.dialog.NiftyDialogBuilder;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;

import java.util.List;

public class GetSchemeTask extends AsyncTask<Void, Void, Boolean> {
    FragmentActivity activity;
    Fragment fragment;
    boolean isNewTask = false;
    private ProgressDialog progressDialog;
    private String errMsg = null;

    public GetSchemeTask(FragmentActivity activity, Fragment newTaskActivity, boolean isNewTask) {
        this.activity = activity;
        fragment = newTaskActivity;
        this.isNewTask = isNewTask;
    }

    @Override
    protected void onPreExecute() {
        if (isNewTask) {
            this.progressDialog = ProgressDialog.show(activity, "", activity.getString(R.string.progressWait), true);
        }
    }

    @Override
    protected Boolean doInBackground(Void... arg0) {
        // TODO Auto-generated method stub

//		List<LocationInfo> infos = LocationInfoDataAccess.getAllbyType(activity, GlobalData.getSharedGlobalData().getUser().getUuid_user(), Global.LOCATION_TYPE_TRACKING);

        JsonRequestScheme requestScheme = new JsonRequestScheme();
        requestScheme.setAudit(GlobalData.getSharedGlobalData().getAuditData());
        requestScheme.setUuid_user(GlobalData.getSharedGlobalData().getUser().getUuid_user());
//		requestScheme.setUuid_scheme("value");
        requestScheme.setTask(Global.TASK_GETLIST);

        String json = GsonHelper.toJson(requestScheme);
        String url = GlobalData.getSharedGlobalData().getURL_GET_SCHEME();
//		url = "http://08-05-0006-0714.ad-ins.com:8080/com.adins.mss.webservices/services/m/task/retrievetasklist";
        boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
        boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
        HttpCryptedConnection httpConn = new HttpCryptedConnection(activity, encrypt, decrypt);
        HttpConnectionResult serverResult = null;
        try {
            serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
        } catch (Exception e) {
            FireCrash.log(e);
            e.printStackTrace();
            errMsg = e.getMessage();
        }
        if (serverResult.isOK()) {
            try {
                String result = serverResult.getResult();
//				result = "{\"listScheme\":[{\"uuid_scheme\":\"18ba5207-89bf-11e4-b68b-6f9ababe5e21\",\"scheme_description\":\"form1\",\"scheme_last_update\":\"22022015000000\",\"is_printable\":\"0\",\"form_id\":\"ODR01\",\"is_preview_server\":\"0\",\"form_type\":\"Order\"}],\"listPrintItems\":[{\"uuid_print_item\":\"18ba5207-89bf-11e4-b68b-6f9ababe5e22\",\"uuid_scheme\":\"18ba5207-89bf-11e4-b68b-6f9ababe5e21\"}],\"status\":{\"code\":0}}";
                JsonResponseScheme responseScheme = GsonHelper.fromJson(result, JsonResponseScheme.class);
                List<Scheme> schemes = responseScheme.getListScheme();
                List<PrintItem> printItems = responseScheme.getListPrintItems();

                //bong 19 may 15 - delete scheme dulu baru di add dari server
                if (!schemes.isEmpty()) {
                    SchemeDataAccess.clean(activity);
                }

                for (Scheme scheme : schemes) {
                    Scheme scheme2 = null;
                    try {
                        scheme2 = SchemeDataAccess.getOneByLastUpdate(activity, scheme.getUuid_scheme(), scheme.getScheme_last_update());
                    } catch (Exception e) {
                        FireCrash.log(e);
                    }

                    if (scheme2 == null) {
                        if (scheme.getUuid_scheme() != null) {
                            SchemeDataAccess.addOrReplace(activity, scheme);
                        }
                    } else {
                        if (scheme.getScheme_last_update().after(scheme2.getScheme_last_update())) {
                            if (scheme.getUuid_scheme() != null) {
                                SchemeDataAccess.addOrReplace(activity, scheme);
                            }
                        }
                    }
                }

                for (PrintItem printItem : printItems) {
                    Scheme scheme = SchemeDataAccess.getOne(activity, printItem.getUuid_scheme());
                    printItem.setScheme(scheme);
                    PrintItemDataAccess.addOrReplace(activity, printItem);
                }

//				SchemeDataAccess.addOrReplace(activity, schemes);
            } catch (Exception e) {
                FireCrash.log(e);
                errMsg = e.getMessage();
            }
        }

        return serverResult.isOK();
    }

    @Override
    protected void onPostExecute(Boolean result) {
        if (isNewTask) {
            if (progressDialog.isShowing()) {
                try {
                    progressDialog.dismiss();
                } catch (Exception e) {
                    FireCrash.log(e);
                }
            }
            NiftyDialogBuilder dialogBuilder;
            if (errMsg != null) {
                String application = GlobalData.getSharedGlobalData().getAuditData().getApplication();
                if (application.equalsIgnoreCase(Global.APPLICATION_SURVEY)) {
                    if (SchemeDataAccess.getAllSurveyScheme(activity) != null ||
                            !SchemeDataAccess.getAllSurveyScheme(activity).isEmpty()) {
                        FragmentTransaction transaction = MainMenuActivity.fragmentManager.beginTransaction();
                        transaction.setCustomAnimations(R.anim.activity_open_translate, R.anim.activity_close_scale, R.anim.activity_open_scale, R.anim.activity_close_translate);
                        transaction.replace(R.id.content_frame, fragment);
                        transaction.addToBackStack(null);
                        transaction.commit();
                    } else {
                        dialogBuilder = NiftyDialogBuilder.getInstance(activity);
                        dialogBuilder.withTitle(activity.getString(R.string.error_capital)).withMessage(errMsg).show();
                    }
                } else if (application.equalsIgnoreCase(Global.APPLICATION_ORDER)) {
                    if (SchemeDataAccess.getAllOrderScheme(activity) != null ||
                            !SchemeDataAccess.getAllOrderScheme(activity).isEmpty()) {
                        FragmentTransaction transaction = MainMenuActivity.fragmentManager.beginTransaction();
                        transaction.setCustomAnimations(R.anim.activity_open_translate, R.anim.activity_close_scale, R.anim.activity_open_scale, R.anim.activity_close_translate);
                        transaction.replace(R.id.content_frame, fragment);
                        transaction.addToBackStack(null);
                        transaction.commit();
                    } else {
                        dialogBuilder = NiftyDialogBuilder.getInstance(activity);
                        dialogBuilder.withTitle(activity.getString(R.string.error_capital)).withMessage(errMsg).show();
                    }
                }
            } else {
                FragmentTransaction transaction = MainMenuActivity.fragmentManager.beginTransaction();
                transaction.setCustomAnimations(R.anim.activity_open_translate, R.anim.activity_close_scale, R.anim.activity_open_scale, R.anim.activity_close_translate);
                transaction.replace(R.id.content_frame, fragment);
                transaction.addToBackStack(null);
                transaction.commitAllowingStateLoss();
            }
        }
    }
}
