package com.adins.mss.foundation.db.dataaccess;

import android.content.Context;
import android.database.Cursor;

import com.adins.mss.constant.Global;
import com.adins.mss.dao.DaoSession;
import com.adins.mss.dao.Scheme;
import com.adins.mss.dao.SchemeDao;
import com.adins.mss.foundation.db.DaoOpenHelper;

import java.util.ArrayList;
import java.util.List;

import de.greenrobot.dao.query.QueryBuilder;

public class SchemeDataAccess {

//	private static DaoOpenHelper daoOpenHelper;

    /**
     * use to generate dao session that you can access modelDao
     *
     * @param context --> context from activity
     * @return
     */
    protected static DaoSession getDaoSession(Context context) {
        /*if(daoOpenHelper==null){
//			if(daoOpenHelper.getDaoSession()==null)
				daoOpenHelper = new DaoOpenHelper(context);
		}
		DaoSession daoSeesion = daoOpenHelper.getDaoSession();
		return daoSeesion;*/
        return DaoOpenHelper.getDaoSession(context);
    }

    /**
     * get scheme dao and you can access the DB
     *
     * @param context
     * @return
     */
    protected static SchemeDao getSchemeDao(Context context) {
        return getDaoSession(context).getSchemeDao();
    }

    /**
     * Clear session, close db and set daoOpenHelper to null
     */
    public static void closeAll() {
        /*if(daoOpenHelper!=null){
            daoOpenHelper.closeAll();
			daoOpenHelper = null;
		}*/
        DaoOpenHelper.closeAll();
    }

    /**
     * add scheme as entity
     *
     * @param context
     * @param scheme
     */
    public static void add(Context context, Scheme scheme) {
        getSchemeDao(context).insertInTx(scheme);
        getDaoSession(context).clear();
    }

    /**
     * add scheme as list entity
     *
     * @param context
     * @param schemeList
     */
    public static void add(Context context, List<Scheme> schemeList) {
        getSchemeDao(context).insertInTx(schemeList);
        getDaoSession(context).clear();
    }

    /**
     * delete all content in table.
     *
     * @param context
     */
    public static void clean(Context context) {
        getSchemeDao(context).deleteAll();
        getDaoSession(context).clear();
    }

    /**
     * @param context
     * @param scheme
     */
    public static void delete(Context context, Scheme scheme) {
        getSchemeDao(context).delete(scheme);
        getDaoSession(context).clear();
    }

    /**
     * delete all record by keyScheme
     *
     * @param context
     */
    public static void delete(Context context, String keyScheme) {
        QueryBuilder<Scheme> qb = getSchemeDao(context).queryBuilder();
        qb.where(SchemeDao.Properties.Uuid_scheme.eq(keyScheme));
        qb.build();
        getSchemeDao(context).deleteInTx(qb.list());
        getDaoSession(context).clear();
    }

    /**
     * @param context
     * @param scheme
     */
    public static void update(Context context, Scheme scheme) {
        getSchemeDao(context).update(scheme);
        getDaoSession(context).clear();
    }

    /**
     * add scheme as entity
     *
     * @param context
     * @param scheme
     */
    public static void addOrReplace(Context context, Scheme scheme) {
        /*if(getOne(context, scheme.getUuid_scheme())!=null)
            update(context, scheme);
		else
			add(context, scheme);*/
        getSchemeDao(context).insertOrReplaceInTx(scheme);
        getDaoSession(context).clear();
    }

    /**
     * add scheme as list entity
     *
     * @param context
     * @param listScheme
     */
    public static void addOrReplace(Context context, List<Scheme> listScheme) {
        getSchemeDao(context).insertOrReplaceInTx(listScheme);
        getDaoSession(context).clear();
//		for (Scheme scheme : listScheme){
//			if(getOne(context, scheme.getUuid_scheme())!=null)
//				update(context, scheme);
//			else
//				add(context, scheme);
//		}
    }

    /**
     * select * from table where uuid_scheme = param
     *
     * @param context
     * @param keyScheme
     * @return
     */
    public static Scheme getOne(Context context, String keyScheme) {
        QueryBuilder<Scheme> qb = getSchemeDao(context).queryBuilder();
        qb.where(SchemeDao.Properties.Uuid_scheme.eq(keyScheme));
        qb.build().forCurrentThread();
        if (qb.list().isEmpty())
            return null;
        return qb.list().get(0);
    }

    /**
     * select * from table where uuid_scheme = param
     *
     * @param context
     * @return
     */
    public static List<Scheme> getAll(Context context) {
        QueryBuilder<Scheme> qb = getSchemeDao(context).queryBuilder();
//		qb.where(SchemeDao.Properties.Uuid_scheme.eq(keyScheme));
        qb.build();
        return qb.list();
    }

    public static List<Scheme> getAllActiveScheme(Context context) {
        QueryBuilder<Scheme> qb = getSchemeDao(context).queryBuilder();
        qb.where(SchemeDao.Properties.Is_active.eq(Global.TRUE_STRING));
        qb.build();
        return qb.list();
    }

    public static List<Scheme> getAllActivePriorityScheme(Context context) {
        QueryBuilder<Scheme> qb = getSchemeDao(context).queryBuilder();
        qb.where(SchemeDao.Properties.Is_active.eq(Global.TRUE_STRING));
        qb.where(SchemeDao.Properties.Form_type.notEq(Global.FORM_TYPE_SIMULASI));

        qb.where(SchemeDao.Properties.Scheme_description.notEq(Global.FORM_NAME_PROMISE_TO_SURVEY),
                SchemeDao.Properties.Scheme_description.notEq(Global.FORM_NAME_PRE_SURVEY),
                SchemeDao.Properties.Scheme_description.notEq(Global.FORM_NAME_OTS),
                SchemeDao.Properties.Scheme_description.notEq(Global.FORM_NAME_GUARANTOR));

        qb.build();
        return qb.list();
    }

    public static List<String> getAllSchemeName(Context context) {
        List<String> result = new ArrayList<>();
        String SQL_DISTINCT_ENAME = "SELECT " + SchemeDao.Properties.Scheme_description.columnName +
                " FROM " + SchemeDao.TABLENAME +
                " WHERE " + SchemeDao.Properties.Is_active.columnName + "='" + Global.TRUE_STRING + "' " +
                " ORDER BY " +
                SchemeDao.Properties.Scheme_description.columnName +
                " ASC";
        Cursor c = getSchemeDao(context).getDatabase().rawQuery(SQL_DISTINCT_ENAME, null);
        if (c.moveToFirst()) {
            do {
                result.add(c.getString(0));
            } while (c.moveToNext());
        }
        c.close();
        return result;
    }

    /**
     * get simulasi scheme
     *
     * @param context
     * @return
     */
    public static List<Scheme> getAllSimulateScheme(Context context) {
        QueryBuilder<Scheme> qb = getSchemeDao(context).queryBuilder();
        qb.where(SchemeDao.Properties.Form_type.eq(Global.FORM_TYPE_SIMULASI));
        qb.build();
        return qb.list();
    }

    /**
     * get Order Scheme
     *
     * @param context
     * @return
     */
    public static List<Scheme> getAllOrderScheme(Context context) {
        QueryBuilder<Scheme> qb = getSchemeDao(context).queryBuilder();
        qb.where(SchemeDao.Properties.Form_type.eq(Global.FORM_TYPE_ORDER),
                SchemeDao.Properties.Is_active.eq(Global.TRUE_STRING));
        qb.build();
        return qb.list();
    }
    public static Scheme getAllImageScheme(Context context, String uuid) {
        QueryBuilder<Scheme> qb = getSchemeDao(context).queryBuilder();
        qb.where(SchemeDao.Properties.Form_type.eq(Global.FORM_TYPE_IMAGE),
                SchemeDao.Properties.Is_active.eq(Global.TRUE_STRING),
                SchemeDao.Properties.Uuid_scheme.eq(uuid));
        qb.build();
        if(qb.list().isEmpty())
            return null;
        return qb.list().get(0);
    }
    /**
     * get Collection Scheme
     *
     * @param context
     * @return
     */
    public static List<Scheme> getAllCollectionScheme(Context context) {
        QueryBuilder<Scheme> qb = getSchemeDao(context).queryBuilder();
        qb.where(SchemeDao.Properties.Form_type.eq(Global.FORM_TYPE_COLL),
                SchemeDao.Properties.Is_active.eq(Global.TRUE_STRING));
        qb.build();
        return qb.list();
    }

    /**
     * get Survey scheme
     *
     * @param context
     * @return
     */
    public static List<Scheme> getAllSurveyScheme(Context context) {
        QueryBuilder<Scheme> qb = getSchemeDao(context).queryBuilder();
        qb.where(SchemeDao.Properties.Form_type.eq(Global.FORM_TYPE_SURVEY),
                SchemeDao.Properties.Is_active.eq(Global.TRUE_STRING));
        qb.build();
        return qb.list();
    }

    //michael.wijaya 08 Apr 22: used to get Scheme based on the schemeDescription
    public static Scheme getOneSurveyTaskSchemeByFormName(Context context, String schemeDescription){
        QueryBuilder<Scheme> qb = getSchemeDao(context).queryBuilder();
        qb.where(SchemeDao.Properties.Is_active.eq(Global.TRUE_STRING),
                SchemeDao.Properties.Scheme_description.like(schemeDescription));
        qb.build();
        if(qb.list().isEmpty()){
            return null;
        }
        return qb.list().get(0);
    }
    /**
     * get Approval Scheme
     * @param context
     * @return
     */
//	public static List<Scheme> getAllApprovalScheme(Context context){
//		QueryBuilder<Scheme> qb = getSchemeDao(context).queryBuilder();
//		qb.where(SchemeDao.Properties.Form_type.eq(Global.FORM_TYPE_APPROVAL));
//		qb.build();
//		return qb.list();
//	}
//	/**
//	 * get Verified Scheme
//	 * @param context
//	 * @return
//	 */
//	public static List<Scheme> getAllVerifiedScheme(Context context){
//		QueryBuilder<Scheme> qb = getSchemeDao(context).queryBuilder();
//		qb.where(SchemeDao.Properties.Form_type.eq(Global.FORM_TYPE_VERIFICATION));
//		qb.build();
//		return qb.list();
//	}

    /**
     * select scheme by last update
     *
     * @param context
     * @return
     */
    public static Scheme getOneByLastUpdate(Context context, String keyScheme, java.util.Date scheme_last_update) {
        QueryBuilder<Scheme> qb = getSchemeDao(context).queryBuilder();
        qb.where(SchemeDao.Properties.Uuid_scheme.eq(keyScheme),
                SchemeDao.Properties.Scheme_last_update.eq(scheme_last_update));
        qb.build().forCurrentThread();
        if (qb.list().isEmpty())
            return null;
        return qb.list().get(0);
    }
}
