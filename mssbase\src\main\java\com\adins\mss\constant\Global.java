/**
 *
 */
package com.adins.mss.constant;

import android.app.Activity;
import android.content.Intent;

import com.adins.mss.dao.TaskH;
import com.adins.mss.dao.TaskUpdate;
import com.adins.mss.dao.User;
import com.adins.mss.foundation.location.LocationTrackingManager;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Stack;


/**
 * <AUTHOR>
 */
public class Global {

    public static final boolean AUTOLOGIN_ENABLE = true;
    public static final boolean NEW_FEATURE = false;
    public static final String MSMDB = "msmdb";
    public static final int DB_VERSION = 2;
    //Format
    public static final int MAX_LOG = 30;
    public static final int DEFAULT_MAX_LENGTH = 500;
    public static final int ROW_PER_PAGE = 5;
    public static final String DATE_STR_FORMAT = "dd/MM/yyyy";
    public static final String DATE_STR_FORMAT1 = "dd-MM-yyyy";
    public static final String DATE_STR_FORMAT2 = "yyyyMMdd";
    public static final String DATE_STR_FORMAT3 = "dd MMM yyyy";
    public static final String DATE_STR_FORMAT4 = "yyyyMMddHHmm";
    public static final String DATE_STR_FORMAT5 = "ddMMyyyy";
    public static final String DATE_STR_FORMAT6 = "yyyy-MM-dd";
    public static final String DATE_STR_FORMAT_GSON = "ddMMyyyyHHmmss";
    public static final String TIME_STR_FORMAT = "HH:mm";
    public static final String TIME_STR_FORMAT2 = "HHmmss";
    public static final String DATE_TIME_STR_FORMAT = "dd/MM/yyyy HH:mm";
    public static final String DATE_TIME_STR_FORMAT2 = "yyyy-MM-dd'T'HH:mm";
    public static final String DATE_TIMESEC_STR_FORMAT = "dd-MM-yyyy HH:mm:ss";
    public static final String DATE_TIME_SEC_STR_FORMAT = "dd/MM/yyyy HH:mm:ss";
    public static final String DATE_TIMESEC_TIMELINE_FORMAT = "EE, HH:mm";
    public static final String DATE_TIMESEC_TIMELINE_FORMAT_OLD = "dd MMMM, HH:mm";
    public static final int NANOSECOND = 1000000;
    public static final int SECOND = 1000;
    public static final int MINUTE = 60 * SECOND;
    public static final int HOUR = 60 * MINUTE;
    public static final int DAY = 24 * HOUR;
    public static final int DAYS_KEEP_SENT_SURVEY = 1 * DAY;
    /* BUNDLE KEY */
    public static final String BUND_KEY_MODE_SURVEY = "ModeSurvey";
    public static final String BUND_KEY_MODE_SIMULASI = "ModeSimulasi";
    public static final String BUND_KEY_SURVEY_BEAN = "SurveyHeaderBean";
    public static final String BUND_KEY_FORM_BEAN = "BUND_KEY_FORM_BEAN";
    public static final String BUND_KEY_LOV_CRITERIA = "LovCriteria";
    public static final String BUND_KEY_CALL_MAIN_MENU = "CallMainMenu";
    public static final String BUND_KEY_REFRESHCOUNTER = "BUND_KEY_REFRESHCOUNTER";
    public static final String BUND_KEY_SURVEY_ERROR = "SurveyError";
    public static final String BUND_KEY_SURVEY_ERROR_MSG = "SurveyErrorMsg";
    public static final String BUND_KEY_SEND_TIME = "SendTime";
    public static final String BUND_KEY_SEND_SIZE = "SendSize";
    public static final String BUND_KEY_SEND_RESULT = "SendResult";
    public static final String BUND_KEY_TASK_ID = "TaskId";
    public static final String BUND_KEY_TASK_IS_PRINTABLE = "IsPrintable";
    public static final String BUND_KEY_IMAGE_BYTE = "Image";
    public static final String BUND_KEY_TASK = "Task";
    public static final String BUND_KEY_UUID_TASKH = "uuid_taskH";
    public static final String BUND_KEY_FORM_NAME = "formName";
    //Glen Iglesias, 2 July 2014
    public static final String BUND_KEY_TASK_TYPE = "tasktype";
    public static final String BUND_KEY_DETAIL_DATA = "detaildata";
    public static final String BUND_KEY_IMAGE_PATH = "imagePath";
    public static final String BUND_KEY_ASSIGNEE_VALUE = "asigneevalue";
    public static final String BUND_KEY_ASSIGNEE_JOB = "asigneejob";
    public static final String BUND_KEY_ASSIGNEE_ID = "asigneeid";
    public static final String MAP_KEY_LOOKUP_CRITERIA = "criteria";
    public static final String MAP_KEY_LOOKUP_FILTER = "filter";
    //bangkit 27 oct 2014
    public static final String BUND_KEY_TASK_STATUS_CODE = "statuscode";
    public static final String BUND_KEY_ISERROR = "isError";
    public static final String BUND_KEY_MESSAGE = "message";
    public static final String BUND_KEY_PAGE = "page";
    /* Order */
    public static final String BUND_KEY_ORDERNO = "nomor_order";
    public static final String BUND_KEY_QUESTIONID = "question_id";
    public static final int STATUS_CODE_APPL_CLEANSING = 1153;
    /* ACTIVITY FOR RESULT */
    public static final int REQUEST_CODE_LOOKUP = 1;
    public static final int REQUEST_VOICE_NOTES = 222;
    public static final int REQUEST_LOCATIONTAGGING = 123;
    public static final int REQUEST_DRAWING_QUESTION = 124;
    public static final int REQUEST_EDIT_IMAGE = 125;
    public static final int REQUEST_LOOKUP_ANSWER = 126;
    public static final int REQUEST_IMAGE_GPS_LOCATION_UPDATE = 127;
    public static final int REQUEST_LOCATION_UPDATE = 128;
    public static final int REQUEST_CAMERA = 888;
    public static final int REQUEST_ACCEPTED_AGREEMENT = 129;
    /* MODE */
    public static final int MODE_SURVEY_TASK = 1;
    public static final int MODE_NEW_SURVEY = 2;
    public static final int MODE_VIEW_SENT_SURVEY = 3;
    public static final int MODE_TASK_UPDATE = 4;
    /*	NOTIF_ID*/
    public static final int NOTIF_ID_OUTSTANDING = 1;
    //Glen Iglesias
    public static final int NOTIF_ID_ORDEROUTSTANDING = 2;
    //Glen Iglesias, 2 July 2014, new mode specific to MH
    /* MH MODE */
    public static final int TASK_ORDER_ASSIGNMENT = 1;
    public static final int TASK_ORDER_REASSIGNMENT = 2;
    public static final int TASK_CHECK_ORDER = 3;
    public static final int TASK_CANCEL_ORDER = 4;
    public static final String AT_TEXT = "001";
    public static final String AT_TEXT_MULTILINE = "002";
    public static final String AT_CURRENCY = "003";
    public static final String AT_NUMERIC = "004";
    public static final String AT_DECIMAL = "005";
    public static final String AT_MULTIPLE = "006";
    public static final String AT_MULTIPLE_ONE_DESCRIPTION = "007";
    public static final String AT_MULTIPLE_W_DESCRIPTION = "008";
    public static final String AT_RADIO = "009";
    public static final String AT_RADIO_W_DESCRIPTION = "010";
    public static final String AT_DROPDOWN = "011";
    public static final String AT_DROPDOWN_W_DESCRIPTION = "012";
    public static final String AT_DATE = "013";
    public static final String AT_TIME = "014";
    public static final String AT_DATE_TIME = "015";
    public static final String AT_IMAGE = "016";
    public static final String AT_IMAGE_W_LOCATION = "017";
    public static final String AT_IMAGE_W_GPS_ONLY = "018";
    public static final String AT_LOV = "019";
    public static final String AT_LOV_W_FILTER = "020";
    public static final String AT_DRAWING = "021";
    public static final String AT_RESPONDEN = "022";
    public static final String AT_DATALIST = "023";
    public static final String AT_LOCATION = "024";
    public static final String AT_TEXT_WITH_SUGGESTION = "025";
    public static final String AT_LOOKUP = "026";
	//Nendi: 2017-10-26 - Add Scoring Answer Type
	public static final String AT_SCORING = "027";
	public static final String AT_LOOKUP_TABLE = "028";
    public static final String AT_TEXT_WITH_SUGGESTION_NEW = "029";
    public static final String AT_LOCATION_WITH_ADDRESS = "030";
    public static final String AT_OCR = "038";
    public static final String AT_OCR_W_GALLERY = "038";
    public static final String AT_IMAGE_LIVENESS = "039";
    //-----
    public static final String AT_CALCULATION = "050";
    public static final String AT_LOV_IMAGE_SURVEY = "041";
    public static final String AT_DROPDOWN_RV = "042";
    public static final String AT_MULTIPLE_RADIO = "043";
    public static final String AT_GPS = "xxx";
    public static final String AT_GPS_N_LBS = "xxx";
    //
    public static final String AT_SUBMIT_LAYER = "041";
    public static final String AT_TELE_CHECK = "042";
    public static final String AT_DSR = "043";
    public static final String AT_TEXT_MULTILINE_SEPARATE = "046";
    // CR Esign
    public static final String AT_INVITATION_ESIGN = "037";
    public static final String AT_REGISTRATION_CHECK = "047";
    // CR Maybank
    public static final String AT_GET_OTR = "048";
    // CR Thirdparty
    public static final String AT_CEK_REFERANTOR = "049";
    public static final String AT_VALIDATION_CHECK = "050";
    //DELIMITER
    public static final String DELIMETER_DATA = ";";
    public static final String DELIMETER_DATA2 = "^^"; // print
    public static final String DELIMETER_DATA3 = ",";    //Choice Filter
	public static final String DELIMETER_DATA4 = "@";	//Validation
	public static final String DELIMETER_DATA5= ":";	//General setting
    public static final String DELIMETER_ROW = "|";
    public static final String DELIMETER_SUBDATA = "#";
    public static final String DELIMETER_DATA_LOOKUP = "@@@";
    public static final String DELIMETER_DKCP_AT = "@";
    public static final String DELIMETER_DKCP_DOT = ".";
    public static final int FLAG_LOCATION_TRACKING = 0;
    public static final int FLAG_LOCATION_CHECKIN = 1;
    public static final int FLAG_LOCATION_CHECKOUT = 2;
    public static final int FLAG_LOCATION_CAMERA = 3;
    public static final String LOCATION_TYPE_TRACKING = "TRACKING";
    public static final String LOCATION_TYPE_CHECKIN = "CHECK_IN";
    public static final String LOCATION_TYPE_CHECKOUT = "CHECK_OUT";
    public static final String LOCATION_TYPE_CAMERA = "CHECK_OUT";
    public static final String TIMELINE_TYPE_FAILED_SENT_TASK = "Failed Sent Task";
    public static final String TIMELINE_TYPE_PUSH_NOTIFICATION = "Push Notification";
    public static final String TIMELINE_TYPE_TASK = "Task Priority";
    public static final String TIMELINE_TYPE_TASK_UPDATE = "Task Update";
    public static final String TIMELINE_TYPE_PENDING = "Failed Submit Task";
    public static final String TIMELINE_TYPE_UPLOADING = "Upload Task";
    public static final String TIMELINE_TYPE_SUBMITTED = "Submitted Task";
    public static final String TIMELINE_TYPE_VERIFIED = "Verified Task";
    public static final String TIMELINE_TYPE_REJECTED = "Rejected Task";
    public static final String TIMELINE_TYPE_APPROVED = "Approved Task";
    public static final String TIMELINE_TYPE_VERIFICATION = "Verification Task";
    public static final String TIMELINE_TYPE_APPROVAL = "Approval Task";
    public static final String TIMELINE_TYPE_CHECKIN = "Attendance In";
    public static final String TIMELINE_TYPE_CHECKOUT = "Check Out";
    public static final String TIMELINE_TYPE_MESSAGE = "Messages";
    public static final String FORM_TYPE_SURVEY = "Survey";
    public static final String FORM_TYPE_COLL = "Collection";
    public static final String FORM_TYPE_ORDER = "Order";
    public static final String FORM_TYPE_IMAGE = "Survey Foto";
    public static final String FORM_TYPE_APPROVAL = "Approval";
    public static final String FORM_TYPE_SIMULASI = "Simulasi";
    public static final String FORM_TYPE_VERIFICATION = "Verification";

    //michael.wijaya 11 Apr 22: Scheme_Description for specific types of Task List
    public static final String FORM_NAME_PROMISE_TO_SURVEY = "Form Task Promise to Survey";
    public static final String FORM_NAME_PRE_SURVEY = "Form Pre Survey";
    public static final String FORM_NAME_OTS = "Form Task OTS";
    public static final String FORM_NAME_GUARANTOR = "Form Task Guarantor";

    //
    public static final String FORM_NAME_PROMISE_TO_VISIT = "Form Task Promise to Visit";

    // Form Name (2022-11-22)
    public static final String FORM_NAME_VISIT_POLO = "Task Visit Polo";

    //michael.wijaya 11 Apr 22: Task List Types to be shown in sidebar menu
    public static final int TASK_LIST_TYPE_DEFAULT = 0;
    public static final int TASK_LIST_TYPE_PROMISE_TO_SURVEY = 1;
    public static final int TASK_LIST_TYPE_PRE_SURVEY = 2;
    public static final int TASK_LIST_TYPE_OTS = 3;
    public static final int TASK_LIST_TYPE_GUARANTOR = 4;

    public static final String FLAG_BY_ORDERNUMBER = "ORDERNUMBER";
    public static final String FLAG_BY_DATE = "DATE";
    public static final String FLAG_BY_CUSTOMER_NAME = "CUSTOMERNAME";
    public static final String FLAG_BY_DAY = "DAY";
    public static final String FLAG_BY_MONTH = "MONTH";
    public static final String FLAG_FOR_CANCELORDER = "CANCELORDER";
    public static final String FLAG_FOR_ORDERASSIGNMENT = "assign";
    public static final String FLAG_FOR_ORDERREASSIGNMENT = "reassign";
    public static final String TASK_GETONE = "getOne";
    public static final String TASK_GETLIST = "getList";
    public static final String TRUE_STRING = "1";
    public static final String FALSE_STRING = "0";
    //TAG survey asset
    public static final String TAG_HOME = "HOME";
    public static final String TAG_IDENTITY = "IDENTITY";
    public static final String TAG_OFFICE = "OFFICE";
    public static final String TAG_STREET = "STREET";
    public static final String TAG_VEHICLE = "VEHICLE";
    public static final String TAG_URL_LINK = "LINK";
    public static final String TAG_OCR_W_GALLERY = "FOTO KTP";
    public static final String TAG_CANCEL_APP = "CANCEL APP";
    public static final String TAG_CMO_RECOMMENDATION = "CMO RECOMMENDATION";
    public static final String TAG_SURVEY_DATE = "TANGGAL SURVEY";
    public static final String TAG_VALIDATION_CHECK = "CHECK E-KYC";
    //TAG collection
    public static final String TAG_ANGSURAN = "ANGSURAN";
    public static final String TAG_DENDA = "DENDA";
    public static final String TAG_TITIPAN = "TITIPAN";
    public static final String TAG_TOTAL = "TOTAL BAYAR";
    public static final String TAG_PEMBAYARAN = "PEMBAYARAN";
    public static final String TAG_RV_NUMBER = "RV NUMBER";
    public static final String TAG_AGREEMENT_NO = "No Agreement";
    public static final String TAG_OS_AMOUNT = "TOTAL TAGIHAN";
    public static final String TAG_OD = "OVERDUE DAYS";
    public static final String TAG_INSTALLMENT_NO = "INSTALLMENT NO";
    //TAG Application
//	public static final String APPLICATION_COLLECTION = "MSSCOL";
//	public static final String APPLICATION_SURVEY= "MSSSVY";
//	public static final String APPLICATION_ORDER= "MSSODR";
    public static final String APPLICATION_COLLECTION = "MC";
    public static final String APPLICATION_SURVEY = "MS";
    public static final String APPLICATION_ORDER = "MO";
    public static final String FLAG_FOR_TEXT = "0";
    public static final String FLAG_FOR_IMAGE = "1";
    public static final String FLAG_FOR_LOCATION = "2";
    public static final String FLAG_FOR_IMAGE_WITH_GPS = "3";
    public static final String FLAG_FOR_REJECTEDTASK_WITHRESURVEY = "2";
    public static final String FLAG_FOR_APPROVALTASK = "1";
    public static final String FLAG_FOR_REJECTEDTASK = "0";
    // Esign
    public static final String TAG_RESULT = "RESULT";

    /**
     * Parameter Auto Logout
     */
    public static final String MS_TIMEOUT_MOBILE = "MS_TIMEOUT_MOBILE";

    /**
     * Rule Parameter TC validation
     */
    public static final String GS_TC_PARAM = "PRM24_REF";

    /**
     * Toleransi accuracy yang hendak disimpan (meter)
     */
    public static final String GS_TENANT_ID = "TENANT";
    /**
     * Toleransi accuracy yang hendak disimpan (meter)
     */
    public static final String GS_ACCURACY = "ACCURACY";
    /**
     * Location tracking enabled (1 = enabled)
     */
    public static final String GS_TRACKING = "PRM01_TRCK";
    /**
     * Location tracking capture interval
     */
    public static final String GS_INTERVAL_TRACKING = "PRM02_TRIN";
    /**
     * Task Result Autosend interval (seconds) (set 0 = OFF)
     */
    public static final String GS_INTERVAL_AUTOSEND = "PRM03_ASIN";
    /**
     * Task refresh interval (seconds)
     */
    public static final String GS_INTERVAL_TASKREFRESH = "PRM04_F5IN";
    /**
     * Camera capture image quality
     * encoding=jpeg&width=640&height=480&quality=normal&jpegquality=70
     */
    public static final String GS_IMG_QUALITY = "PRM06_IMGQ";
    /**
     * Camera capture image high quality
     * encoding=jpeg&width=640&height=480&quality=normal&jpegquality=70
     */
    public static final String GS_IMG_HIGH_QUALITY = "PRM06_IMGHQ";
    /**
     * application build number
     */
    public static final String GS_BUILD_NUMBER = "PRM07_VERS";
    /**
     * Enable login or not, if build version number not the latest
     */
    public static final String GS_VERS_LOGIN = "PRM08_LGIN";
    /**
     * OTA Download Link
     */
    public static final String GS_URL_DOWNLOAD = "PRM09_LINK";
    /**
     * Setting pengiriman image dipisah dengan hasil task text (1 = enabled)
     */
    public static final String GS_PARTIAL_SENT = "PRM12_PART";
    /**
     * Setting jarak perubahan lokasi yang akan dikirim (meter)
     */
    public static final String GS_DISTANCE_TRACKING = "PRM13_DIST";
    /**
     * Vibrate (OFF, SHORT 0.5S, TWICE 2S, LONG 2 Vibrations 0.2s)
     */
    public static final String GS_NOTIF_VIBRATE = "PRM14_VIB";
    /**
     * Tone (OFF, NORMAL inherit device, FORCE)
     */
    public static final String GS_NOTIF_TONE = "PRM15_TON";
    /**
     * Auto Clear Notif  (1=true, 0 false)
     */
    public static final String GS_NOTIF_AUTOCLEAR = "PRM16_ACN";
    /**
     * SLA Time for calculate SLA
     */
    public static final String GS_SLA_TIME = "SLA_TIME";
    /**
     * Timeline Keeping parameter on Day
     */
    public static final String GS_TIMELINE_TIME = "PRM17_TMLN";
    /**
     * Log Keeping parameter on counter Task
     */
    public static final String GS_LOG_COUNTER = "PRM18_LOG";
    /**
     * parameter on green accuracy
     */
    public static final String GS_ACCURACY_G = "PRM19_ACC_G";
    /**
     * parameter on yellow accuracy
     */
    public static final String GS_ACCURACY_Y = "PRM20_ACC_Y";
    /**
     * parameter on Cash on Hand
     */
    public static final String GS_CASHONHAND = "MC_LIMIT_COH";
    /**
     * parameter on Input RV
     * 1 = depan
     * 0 = belakang
     */
    public static final String GS_ENABLE_RV_IN_FRONT = "MC_FLAG_RV";
    /*
    * parameter on Currency Type
     */
    public static final String GS_CURRENCY_TYPE = "MC_CURRENCY_TYPE";
    public static final String GS_TASK_LAYOUT_MS = "PRM21_TASK_LIST";
    /**
     * parameter layout tasklist
     * 1 = grid
     * 3 = list
     */

    public static final String GS_TASK_LAYOUT_MO = "PRM21_TASK_LIST";
    /**
     * parameter layout tasklist
     * 1 = grid
     * 3 = list
     */

    public static final String GS_TASK_LAYOUT_MC = "PRM21_TASK_LIST";
    /**
     * parameter layout tasklist
     * 1 = grid
     * 3 = list
     */

    public static final String GS_ENABLE_NEW_ORDER_PILOTING = "MS_ENABLE_NEW_ORDER_PILOTING";
    /**
     * Setting Delete Menu New Order dynamic
     */
    public static final String GS_INTERVAL_PUSHSYNC = "MS_PUSHSYNC_INTERVAL";
    public static final String GS_PUSHSYNC_TIME = "MS_PUSHSYNC_TIME";
    /**
     * Setting Delete Log
     */
    public static final String MS_STATUS_LOG_DELETE = "MS_STATUS_LOG_DELETE";
    /**
     * Setting Image Gallery Ref Id
     */
    public static final String MS_IMAGE_GALLERY_REF_ID = "MS_IMAGE_GALLERY_REF_ID";

    public static final String GS_DB_VERS = "MS_DB_VERS";
    public static final String GS_URL_WEB = "MS_URL_WEB";

    /**
    * Setting Download Link PDF
    */
    public static final String GS_MS_PDF_LINK = "MS_PDF_LINK";

    /**Setting move location marker enable or not*/
    public static final String GS_MOVE_LOCATION = "MS_MOVE_LOCATION";
    /** Ref Id for Questions with Answer Type Date */
    public static final String GS_PLACEHOLDER_QUESTION = "PLACEHOLDER_QUESTION";
    public static final String GS_REF_ID_TTD_MASKAPAI = "REF_ID_TTD_MASKAPAI";
    public static final String GS_MAX_DURATION_VISIT_DATE = "MAX_DURATION_VISIT_DATE";

    public static final String _TRCK_DAYS = "_TRCK_DAYS";
    public static final String _TRCK_START_TIME = "_TRCK_START_TIME";
    public static final String _TRCK_END_TIME = "_TRCK_END_TIME";
    public static final String IMAGE_HQ = "HQ";
    public static final String IMAGE_NQ = "NQ";
    public static final String IDF_LOGIN_ID = "LOGIN_ID";
    public static final String IDF_BRANCH_ID = "BRANCH_ID";
    public static final String IDF_UUID_USER = "UUID_USER";
    public static final String IDF_BRANCH_NAME = "BRANCH_NAME";
    public static final String IDF_JOB = "FLAG_JOB";
    public static final String IDF_DEALER_NAME = "DEALER_NAME";
    public static final String IDF_UUID_BRANCH = "UUID_BRANCH";
    public static final String IDF_DEALER_ID = "UUID_DEALER";
    public static final String IDF_ANSWER_BEAN = "ANSWER";
    public static final String IDF_THIS_YEAR = "THISYEAR";
    public static final String IDF_NOWADAYS = "NOWADAYS";
    public static final String IDF_YESTERDAY = "YESTERDAY";
    public static final String IDF_BRANCH_TYPE = "BRANCH_TYPE";
    public static final String IDF_TASK_ID = "TASK_ID";
    public static final String IDF_SOURCE_DATA = "SOURCE_DATA";
    public static final String IDF_PRODUCT_NAME = "PRODUCT_NAME";
    public static final String IDF_JENIS_ASSET = "JENIS_ASSET";
    public static boolean ACRA_DISABLED = false;
    public static String Token = "";
    public static String FLAVORS = "";
    public static boolean IS_DEV = false;
    public static boolean IS_LOGIN = false;
    public static boolean VERIFICATION_BRANCH = true;
    public static boolean APPROVAL_BRANCH = true;
    public static boolean FEATURE_RESCHEDULE_SURVEY = true;
    public static boolean FEATURE_REVISIT_COLLECTION = true; //new
    public static boolean FEATURE_REJECT_WITH_RESURVEY = true;
    public static boolean IS_BYPASSROOT = false;
    public static boolean IS_DBENCRYPT = true;
    public static String MENU_RESCHEDULE_SURVEY = "Promise To Survey";
    public static String MENU_REVISIT_COLLECTION = "Re-Visit"; //new
    public static String MENU_VERIFICATION_BRANCH = "Verification by Branch";
    public static String MENU_APPROVAL_BRANCH = "Approval by Branch";
    //bong 9 apr 15 - for passing class
    public static Class printActivityClass;
    public static Class VerificationActivityClass;
    public static String APP_VERSION = "2.0";

    // general setting
    public static int BUILD_VERSION = 0;
    public static User user;
    public static int THUMBNAIL_WIDTH = 120;
    public static int THUMBNAIL_HEIGHT = 160;
    public static int TRIANGLE_SIZE = 24;
    public static Activity currentActivity = null;
    //bong 29 apr 15 - to set activity after force changePassword
    public static Intent syncIntent = null;
    public static Intent installmentSchIntent = null;
    public static Intent paymentHisIntent = null;
    public static Intent collectionActIntent = null;
    /* PRINT ITEM TYPE */
    //public static String PRINT_NO_ANSWER = "001";
    public static String PRINT_ANSWER = "001";
    public static String PRINT_TIMESTAMP = "002";
    public static String PRINT_LOGO = "003";
    public static String PRINT_USER_NAME = "004";
    public static String PRINT_LABEL_CENTER = "005";
    public static String PRINT_LABEL_CENTER_BOLD = "006";
    public static String PRINT_LABEL = "007";
    public static String PRINT_LABEL_BOLD = "008";
    public static String PRINT_BRANCH_NAME = "009";
    public static String PRINT_BRANCH_ADDRESS = "010";
    public static String PRINT_BT_ID = "011";
    public static String PRINT_NEW_LINE = "012";
    public static String PRINT_LOGIN_ID = "013";
    public static String PRINT_ANSWER_NO = "999";
    public static boolean isVerifiedByUser = false;
    public static int haveLogin = 0;
    public static String GS_MAX_TELECHECK = "MS_MAX_RETRY_REFRESH_TELECHECK";
    /*
     * Gigin, flag for terminate Collect data tracking
     */
    public static boolean TRACKING_ENDED = true;
    /**
     * Flag for location type
     * 0 for tracking
     * 1 for absent in
     * 2 for absent out
     */
    public static int FLAG_LOCATION_TYPE = 0;
    public static int FLAG_TIMELINE_TYPE = 0;
    public static LocationTrackingManager LTM;
    public static int DEFAULTCONNECTIONTIMEOUT = 120000;
    public static int SORTCONNECTIONTIMEOUT = 60000;
    public static int SIMULATED_REFRESH_LENGTH = 5000;
    public static double CASH_LIMIT = 0;
    public static double CASH_ON_HAND = 0;
    public static List<TaskH> listOfSentTask = new ArrayList<TaskH>();
    public static List<TaskUpdate> listOfTaskUpdate = new ArrayList<>();
    public static boolean isUploading = false;
    public static boolean isManualUploading = false;
    public static boolean isManualSubmit = false;
    public static boolean isGPS = false;
    public static boolean isViewer = false;
    public static Stack<Integer> positionStack = new Stack<Integer>();
    public static boolean isApproval = false;
    public static String LAST_SYNC = null;
    public static boolean isOfflineMode = false;
    public static HashMap<String, Date> TempScheme;
    public static boolean SchemeIsChange = true;
    public static Intent verifyNotivIntent;
    public static Intent approvalNotivIntent;
    public static String APPROVAL_FLAG = "APPROVAL_FLAG";
    public static String VERIFICATION_FLAG = "VERIFICATION_FLAG";
    public static String MAINMENU_NOTIFICATION_KEY  = "MAINMENU_NOTIFICATION_KEY";

    public static final String FIELD_UUID_TASK_H 	= "UUID_TASK_H";
    public static final String FIELD_CUSTOMER_NAME 	= "CUSTOMER_NAME";
    public static final String FIELD_TASK_ID 	= "ASSIGNMENT_DATE";
    public static final String FIELD_PTS_DATE 	= "PMS_DATE";

    public static final String ORD_ASC = "ASC";
    public static final String ORD_DSC = "DESC";
    public static String packageName;

    //CR CENTRALIZED DAN DOCUPRO
    public static boolean isForceLogout= false;
    public static boolean isLoggedIn= false;
    public static String flavourProduct = "product";
    public static String flavourProductNP = "productNonPilot";
    public static String flavourProductCloud = "productcloud";

    public static final String STATUS_TASK_WEB_WAITING = "Waiting on Pending";
    public static final String STATUS_TASK_WEB_PENDING = "Pending";


    //CR ENHANCEMENT REDAKSI
    public static final String ACCEPTED_AGREEMENT = "SAYA SETUJU";

    //CR CAE FASE 1
    public static final String REF_PRESURVEY_PHONE              = "PRE_NO_HP";
    public static final String REF_PRESURVEY_SPOUSE_PHONE       = "PRE_NO_HP_PSGN";
    public static final String REF_PRESURVEY_GRNTR_PHONE        = "PRE_NO_HP_GRTR";
    public static final String REF_PMHN_KODE_PROVIDER			= "SVY_KODE_PROVIDER";
    public static final String REF_PSGN_KODE_PROVIDER			= "SVY_KODE_PROVIDER_PA";
    public static final String REF_PSGN_NO_TELEPON				= "SVY_NO_TELEPON_PA";
    public static final String REF_PSGN_KODE_PROVIDER_1			= "SPOUSE_PROVIDERCODE";
    public static final String PSGN_NO_TELEPON_1			    = "SPOUSE_PHONE1";
    public static final String REF_GUA_PHONE_CODE				= "GRTR_PROVIDERCODE";
    public static final String REF_PRE_DSR = "PRE_DSR";
    public static final String REF_INSTALLMENT_WOM = "SVY_INSTL_WOM";
    public static final String REF_INSTALLMENT_OTHER = "SVY_INSTL_OTH";
    public static final String REF_PRE_PENGHASILAN = "PRE_PENGHASILAN";
    public static final String REF_SVY_PGHSL_PSGN = "SVY_PGHSL_PSGN";
    public static final String REF_PRE_ANGSURAN = "PRE_ANGSURAN";
    public static final String REF_PRE_TTD_CUSTOMER = "PRE_TTD_PEMOHON";
    public static final String REF_PRE_TTD_SPOUSE = "PRE_TTD_PASANGAN";
    public static final String REF_PRE_TTD_GUARANTOR = "PRE_TTD_PENJAMIN";

    public static final String REF_PRE_INC_PSGN = "PRE_INC_PSGN";
    public static final String REF_PRE_NEED_GRNTR = "PRE_NEED_GRNTR";

    public static final String REF_ID_STV_VISIT_DATE = "STV_VISIT_DATE";
    public static final String REF_ID_STV_INPUT_DATE = "STV_INPUT_DATE";

    public static final String REF_PRE_SOA = "PRE_SOA";
    public static final String PRE_PROD_OFF_TYPE = "PRE_PROD_OFF_TYPE";
}
