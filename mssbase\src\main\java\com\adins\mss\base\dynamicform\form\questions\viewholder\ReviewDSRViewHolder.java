package com.adins.mss.base.dynamicform.form.questions.viewholder;

import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.adins.mss.base.R;
import com.adins.mss.base.dynamicform.form.questions.OnQuestionClickListener;
import com.adins.mss.constant.Global;
import com.adins.mss.foundation.questiongenerator.QuestionBean;

import org.apache.commons.lang3.StringUtils;

public class ReviewDSRViewHolder extends RecyclerView.ViewHolder {
    private final RelativeLayout layout;
    private final TextView mLabelNo;
    private final TextView mQuestionLabel;
    private final TextView mQuestionAnswer;
    private final OnQuestionClickListener listener;
    public ImageView mCheckLocation;
    private QuestionBean bean;

    public ReviewDSRViewHolder(View itemView, OnQuestionClickListener listener) {
        super(itemView);
        layout = (RelativeLayout) itemView.findViewById(R.id.textReviewLayout);
        mLabelNo = (TextView) itemView.findViewById(R.id.questionNoLabel);
        mQuestionLabel = (TextView) itemView.findViewById(R.id.questionTextLabel);
        mQuestionAnswer = (TextView) itemView.findViewById(R.id.questionTextAnswer);
        mCheckLocation = (ImageView) itemView.findViewById(R.id.imgLocationAnswer);
        this.listener = listener;
    }
    public void bind(final QuestionBean item, final int group, final int number) {
        bean = item;
        mCheckLocation.setVisibility(View.GONE);

        mLabelNo.setText(number + ".");
        String qLabel = bean.getQuestion_label();
        mQuestionLabel.setText(qLabel);

        if (StringUtils.isNotBlank(item.getAnswer())) {
            mQuestionAnswer.setText(item.getAnswer().split(Global.DELIMETER_DATA4)[0]);
        }

        layout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                listener.onReviewClickListener(bean, group, number - 1);
            }
        });
    }
}
