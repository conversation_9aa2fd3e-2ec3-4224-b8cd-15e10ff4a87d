package com.adins.mss.foundation.location;

import android.Manifest;
import android.annotation.TargetApi;
import android.content.Context;
import android.content.pm.PackageManager;
import android.location.Location;
import android.location.LocationManager;
import android.os.Build;
import android.os.Bundle;
import android.os.SystemClock;
import android.telephony.CellIdentityGsm;
import android.telephony.CellIdentityLte;
import android.telephony.CellIdentityWcdma;
import android.telephony.CellInfo;
import android.telephony.CellInfoGsm;
import android.telephony.CellInfoLte;
import android.telephony.CellInfoWcdma;
import android.telephony.TelephonyManager;
import android.telephony.cdma.CdmaCellLocation;
import android.telephony.gsm.GsmCellLocation;

import androidx.core.app.ActivityCompat;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.mainmenu.UpdateMenuGPS;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.LocationInfo;
import com.adins.mss.foundation.db.dataaccess.LocationInfoDataAccess;
import com.adins.mss.foundation.formatter.Tool;
import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.api.GoogleApiClient;
import com.google.android.gms.location.LocationRequest;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.maps.model.LatLng;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 */
public class LocationTrackingManager implements GoogleApiClient.ConnectionCallbacks, GoogleApiClient.OnConnectionFailedListener {
    public static final int DEFAULT_MIN_TIME_CHANGE_LOCATION = 5 * 1000; // milliseconds
    public static final int DEFAULT_MIN_DISTANCE_CHANGE_LOCATION = 0; // meters
    public static final int MILLISECONDS_PER_SECOND = 1000;
    public static final int FAST_INTERVAL_CEILING_IN_MILLISECONDS = 1000;
    public static final int CONNECTION_FAILURE_RESOLUTION_REQUEST = 9000;
    public static Context context;
    public static int status = 0; //0=can't get location; 1= get location not accurate; 2 = get location accurate
    private static LocationManager locationManager;
    private static LocationInfo locationInfo = new LocationInfo();
    private static LocationListenerImpl locationListener = new LocationListenerImpl();
    private static GoogleApiClient mGoogleApiClient;
    public boolean isConnected = false;
    private TelephonyManager telephonyManager;
    private int minTime = -1;

    //bong 31 mar 15 - add gpsListener
    //private static GPSListener gpsListener = new GPSListener();
    private int minDistance = -1;
    // A request to connect to Location Services
    private LocationRequest mLocationRequest;
    // Stores the current instantiation of the location client in this object
//    public static LocationClient mLocationClient;

    /**
     * Inisialize LocationTrackingManager with Telephony Manager and Location Manager as parameter
     *
     * @param tm -
     *           Telephony Manager
     * @param lm -
     *           Location Manager
     */
    public LocationTrackingManager(TelephonyManager tm, LocationManager lm, Context context) {
        LocationTrackingManager.context = context;
        if (tm == null)
            throw new RuntimeException("Could not get telephony service!");
        if (lm == null)
            throw new RuntimeException("Could not get location service!");

        telephonyManager = tm;
        locationManager = lm;
        //23 Mei 17 add Sync GoogleAPIClient
        buildGoogleApiClient();
        //bong 31 mar 15 add gpsStatusListener
        //locationManager.addGpsStatusListener(gpsListener);

//		try {
//			Global.LTM = this;
//		} catch (Exception e) {
//			// TODO: handle exception
//		}
    }

    public static Boolean isGpsEnable(LocationTrackingManager manager) {
        return locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
    }

    public static GoogleApiClient getGoogleApiClient() {
        return mGoogleApiClient;
    }

    /**
     * Gets List of Locations Tracking from Database
     *
     * @param context
     * @return all Location
     */
    public static List<LocationInfo> getAllLocationInfoTrackingFromDB(Context context) {
        String uuid_user = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        return LocationInfoDataAccess.getAllbyType(context, uuid_user, Global.LOCATION_TYPE_TRACKING);
    }

    /**
     * flag for get Location status that use on GPS Indicator
     *
     * @return <b>0</b> if Location un-Available<br/>
     * <b>1</b> if Location Available but not accurate<br/>
     * <b>2</b> if Location Available with good accuracy
     */
    public static int getLocationStatus() {
        return status;
    }

    /**
     * Set Location Status Availability
     *
     * @param statusLocation <br/><br/><b>0</b> if Location un-Available<br/>
     *                       <b>1</b> if Location Available but not accurate<br/>
     *                       <b>2</b> if Location Available with good accuracy
     */
    public static void setLocationStatus(int statusLocation) {
        if (statusLocation == 2 || statusLocation == 1) {
            Global.isGPS = true;
        } else if (statusLocation == 0) {
            Global.isGPS = false;
        }

        status = statusLocation;
    }

    public static LatLng getLatLng(LocationInfo locationInfo) {
        double mLatitude = Double.parseDouble(locationInfo.getLatitude());
        double mLongitude = Double.parseDouble(locationInfo.getLongitude());
        LatLng latLng = new LatLng(mLatitude, mLongitude);

        return latLng;
    }

    /**
     * Insert a LocationInfo to Data Base
     *
     * @param locationInfo
     */
    public static void insertLocationInfoToDB(LocationInfo locationInfo) {
        LocationInfoDataAccess.add(context, locationInfo);
    }

    /**
     * Insert a LocationInfo to Data Base
     *
     * @param locationInfo
     */
    public static void insertLocationInfoListToDB(List<LocationInfo> locationInfo) {
        LocationInfoDataAccess.add(context, locationInfo);
    }

    public static String toAnswerString(LocationInfo locationInfo) {
        StringBuilder sb = new StringBuilder();
        sb.append(locationInfo.getLatitude()).append(",")
                .append(locationInfo.getLongitude()).append(",")
                .append(locationInfo.getCid()).append(",")
                .append(locationInfo.getMcc()).append(",")
                .append(locationInfo.getMnc()).append(",")
                .append(locationInfo.getLac()).append(",")
                .append(locationInfo.getAccuracy()).append(",")
                .append(locationInfo.getGps_time());
        return sb.toString();
    }
    public static String toAnswerString_short(LocationInfo locationInfo) {
        StringBuilder sb = new StringBuilder();
        sb.append("Coord : ")
                .append(locationInfo.getLatitude()).append(", ")
                .append(locationInfo.getLongitude()).append("\n")
                .append("Accuracy : ")
                .append(locationInfo.getAccuracy()).append(" m");
        return sb.toString();
    }

    public static String toAnswerString_short(LocationInfo locationInfo, String[] address) {
        StringBuilder sb = new StringBuilder();
        sb.append("Coord : ")
                .append(locationInfo.getLatitude()).append(", ")
                .append(locationInfo.getLongitude()).append("|")
                .append("Accuracy : ")
                .append(locationInfo.getAccuracy()==null?0:locationInfo.getAccuracy()).append(" m")
                .append("|").append(address[1]);
        return sb.toString();
    }

    public static int age_minutes(Location last) {
        return (int) (age_ms(last) / (60 * 1000));
    }

    private static long age_ms(Location last) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1)
            return age_ms_api_17(last);
        return age_ms_api_pre_17(last);
    }

    @TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR1)
    private static long age_ms_api_17(Location last) {
        return (SystemClock.elapsedRealtimeNanos() - last
                .getElapsedRealtimeNanos()) / 1000000;
    }

    private static long age_ms_api_pre_17(Location last) {
        return System.currentTimeMillis() - last.getTime();
    }

    /**
     * Creating google api client object
     */
    protected synchronized void buildGoogleApiClient() {
        mGoogleApiClient = new GoogleApiClient.Builder(context)
                .addConnectionCallbacks(this)
                .addOnConnectionFailedListener(this)
                .addApi(LocationServices.API)
                .build();
        mGoogleApiClient.connect();
    }

    /**
     * Begin Location Listener and Request Location Update
     *
     * @param context - Context
     *                context of the activity
     */
    public void applyLocationListener(Context context) {
        LocationTrackingManager.context = context;
        if (this.minTime == -1)
            this.minTime = DEFAULT_MIN_TIME_CHANGE_LOCATION;
        if (this.minDistance == -1)
            this.minDistance = DEFAULT_MIN_DISTANCE_CHANGE_LOCATION;
        // Create a new global location parameters object

        mLocationRequest = LocationRequest.create();
        mLocationRequest.setInterval(minTime);
        mLocationRequest.setFastestInterval(minTime);
        mLocationRequest.setPriority(LocationRequest.PRIORITY_HIGH_ACCURACY);
//        mLocationRequest.setSmallestDisplacement(0);
        mLocationRequest.setSmallestDisplacement(this.minDistance);//if u take this to 300 m so u will get location if u walk by 300 m or more
        // devide by 10 to make displacement right and get the right time to update
        mLocationRequest.setFastestInterval(FAST_INTERVAL_CEILING_IN_MILLISECONDS);

        connectLocationClient();
    }

    public void connectLocationClient() {
        if (!mGoogleApiClient.isConnected() || !mGoogleApiClient.isConnecting())
            mGoogleApiClient.connect();
    }

//    @Override
//    public void onDisconnected() {
//        // TODO Auto-generated method stub
//        Change to OnConnectionSuspend()
//    }

    public boolean isConnected() {
        return mGoogleApiClient.isConnected();
    }

    /**
     * Stop Periodic Update of Location Tracking
     */
    public void removeLocationListener() {
        stopPeriodicUpdates();
        if (mGoogleApiClient.isConnected())
            mGoogleApiClient.disconnect();
    }

    /**
     * Gets Local Area Code
     *
     * @return Local Area Code
     */
    public int getLac() {
        GsmCellLocation location = (GsmCellLocation) telephonyManager.getCellLocation();
        if (location == null)
            return 0;
        else
            return location.getLac();
    }

    /**
     * Gets Cell ID
     *
     * @return Cell ID from the Handset
     */
    public int getCid() {
        GsmCellLocation location = (GsmCellLocation) telephonyManager.getCellLocation();
        if (location == null)
            return 0;
        else
            return location.getCid();
    }

    /**
     * Gets Mobile Country Code
     *
     * @return Mobile Country Code
     */
    public int getMcc() { //Mobile Country Code
        String networkOperator = this.telephonyManager.getNetworkOperator();
        if (networkOperator == null || "".equals(networkOperator)) {
            return 0;
        } else {
            int val = 0;
            try {
                val = Integer.parseInt(networkOperator.substring(0, 3));
            } catch (NumberFormatException nfe) {
                val = 0;
            }
            return val;
        }
    }

    /**
     * Gets Mobile Network Code
     *
     * @return Mobile Network Code
     */
    public int getMnc() { //Mobile Network Code
        String networkOperator = this.telephonyManager.getNetworkOperator();
        if (networkOperator == null || "".equals(networkOperator)) {
            return 0;
        } else {
            int val = 0;
            try {
                val = Integer.parseInt(networkOperator.substring(3));
            } catch (NumberFormatException nfe) {
                val = 0;
            }
            return val;
        }
    }

    /**
     * <p>Set the desired interval for active location updates, in seconds.<br/>
     * Set to 5 seconds for mapping applications that are showing your location in real-time<br/>
     * By default this is 5 second</p>
     *
     * @param minTime Minimum time in second
     */
    public void setMinimalTimeChangeLocation(int minTime) {
        this.minTime = minTime * MILLISECONDS_PER_SECOND; // miliseconds
    }

    /**
     * <p>Set the minimum displacement between location updates in meters.<br/>
     * By default this is 0.</p>
     *
     * @param minDistance
     */
    public void setMinimalDistanceChangeLocation(int minDistance) {
        this.minDistance = minDistance;
    }

    @Override
    public void onConnectionFailed(ConnectionResult connectionResult) {
        // TODO Auto-generated method stub
        isConnected = false;
    }

    @Override
    public void onConnected(Bundle arg0) {
        startPeriodicUpdates();
        isConnected = true;
    }

    @Override
    public void onConnectionSuspended(int i) {
        isConnected = false;
    }

    private void startPeriodicUpdates() {
        try {
            if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED
                    && ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                return;
            }
            LocationServices.FusedLocationApi.requestLocationUpdates(mGoogleApiClient, mLocationRequest, locationListener);
        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: handle exception
        }
//        TODO FROM OLD LTM
//        try {
//            mLocationClient.requestLocationUpdates(mLocationRequest, locationListener);
//        } catch (Exception e) {
//            // TODO: handle exception
//        }
    }

    public void stopPeriodicUpdates() {
        if (mGoogleApiClient.isConnected()) {
            LocationServices.FusedLocationApi.removeLocationUpdates(mGoogleApiClient, locationListener);
        }
//        if (mLocationClient.isConnected())
//            mLocationClient.removeLocationUpdates(locationListener);
    }

    /**
     * Gets Current Location
     *
     * @return LocationTracking
     */
    public LocationInfo getCurrentLocation(int flag_location_type) {
        boolean isHasGps = Tool.locationEnabled(locationManager);
        if (isHasGps) {
            Location gps = null;
            Location locationSaved = null;
            try {
                if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED &&
                        ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                    return null;
                }
                gps = locationListener.getCurrentLocation();
                locationSaved = locationListener.getCurrentLocation();
            } catch (Exception e) {
                FireCrash.log(e);
            }

            if (gps != null) {
                setLocationStatus(2);
                if (gps.getAccuracy() < 50) {
                    locationInfo.setLatitude(String.valueOf(gps.getLatitude()));
                    locationInfo.setLongitude(String.valueOf(gps.getLongitude()));
                    Date date = new Date(gps.getTime());
                    locationInfo.setGps_time(date);
                    locationInfo.setIs_gps_time(Global.TRUE_STRING);
                    locationInfo.setAccuracy(Math.round(gps.getAccuracy()));
                }
                //if Last Location isn't Accurate then get Location from Last Location which good accuracy
                else {
                    if (locationSaved != null) {
                        if (locationSaved.getAccuracy() <= 50) {
                            LocationTrackingManager.setLocationStatus(2);
                        } else if (locationSaved.getAccuracy() > 50 && locationSaved.getAccuracy() <= 200) {
                            LocationTrackingManager.setLocationStatus(1);
                        } else {
                            LocationTrackingManager.setLocationStatus(0);
                        }
                        locationInfo.setLatitude(String.valueOf(locationSaved.getLatitude()));
                        locationInfo.setLongitude(String.valueOf(locationSaved.getLongitude()));
                        Date date = new Date(locationSaved.getTime());
                        locationInfo.setGps_time(date);
                        locationInfo.setIs_gps_time(Global.TRUE_STRING);
                        locationInfo.setAccuracy(Math.round(locationSaved.getAccuracy()));
                    } else {
                        setLocationStatus(0);
                        locationInfo.setLatitude(String.valueOf(0d));
                        locationInfo.setLongitude(String.valueOf(0d));
                        locationInfo.setGps_time(null);
                        locationInfo.setIs_gps_time(Global.FALSE_STRING);
                        locationInfo.setAccuracy(0);
                    }
                }
            } else {
                setLocationStatus(0); //
                locationInfo.setGps_time(null);
                locationInfo.setIs_gps_time(Global.FALSE_STRING);
                locationInfo.setAccuracy(0);
                locationInfo.setLatitude(String.valueOf(0d));
                locationInfo.setLongitude(String.valueOf(0d));
            }
        } else {
            setLocationStatus(0);
            locationInfo.setGps_time(null);
            locationInfo.setIs_gps_time(Global.FALSE_STRING);
            locationInfo.setAccuracy(0);
            locationInfo.setLatitude(String.valueOf(0d));
            locationInfo.setLongitude(String.valueOf(0d));
        }

        String networkOperator = telephonyManager.getNetworkOperator();

        if (networkOperator == null || "".equals(networkOperator)) {
            locationInfo.setMcc("0");
            locationInfo.setMnc("0");
        } else {
            String mcc = "0";
            String mnc = "0";
            try {
                if (networkOperator != null && networkOperator.length() > 0) {
                    mcc = networkOperator.substring(0, 3);
                    mnc = networkOperator.substring(3);
                }
                if (networkOperator.equalsIgnoreCase("null")) {
                    mcc = "0";
                    mnc = "0";
                }
                locationInfo.setMcc(mcc);
                locationInfo.setMnc(mnc);
            } catch (NumberFormatException nfe) {
                locationInfo.setMcc("0");
                locationInfo.setMnc("0");
            }
        }

        GsmCellLocation location = null;
        try {
            location = (GsmCellLocation) telephonyManager.getCellLocation();
        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: lemparan param untuk param cdma

            try {
                CdmaCellLocation cdmaLocation = (CdmaCellLocation) telephonyManager.getCellLocation();

                if (cdmaLocation == null) {
                    locationInfo.setCid("0");
                    locationInfo.setLac("0");
                } else {
                    //locationTracking.setCid(cdmaLocation.get);
                    //locationTracking.setLac(cdmaLocation.getLac());
                    locationInfo.setCid("0");
                    locationInfo.setLac("0");
                }
            } catch (Exception e2) {
                FireCrash.log(e2);// TODO: handle exception
                locationInfo.setCid("0");
                locationInfo.setLac("0");
            }
        }

        if (location == null) {
            locationInfo.setCid("0");
            locationInfo.setLac("0");
        } else {
            locationInfo.setCid(String.valueOf(location.getCid()));
            locationInfo.setLac(String.valueOf(location.getLac()));
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            try {
                //if (networkOperator == null || "".equals(networkOperator)) {
                List<CellInfo> cellInfoList = telephonyManager.getAllCellInfo();
                String mCid = null, mLac = null, mMnc = null, mMcc = null;
                if (cellInfoList != null) {
                    for (CellInfo cellInfo : cellInfoList) {
                        if (cellInfo.isRegistered()) {
                            if (cellInfo instanceof CellInfoGsm) {
                                CellInfoGsm infoGsm = (CellInfoGsm) cellInfo;
                                CellIdentityGsm gsmCellIdentity = infoGsm.getCellIdentity();
                                if (gsmCellIdentity != null) {
                                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                                        mCid = String.valueOf(gsmCellIdentity.getCid());
                                        mLac = String.valueOf(gsmCellIdentity.getLac());
                                        mMnc = String.valueOf(gsmCellIdentity.getMnc());
                                        mMcc = String.valueOf(gsmCellIdentity.getMcc());
                                    }
                                }
                            } else if (cellInfo instanceof CellInfoLte) {
                                CellInfoLte infoLte = (CellInfoLte) cellInfo;
                                CellIdentityLte lteCellIdentity = infoLte.getCellIdentity();
                                if (lteCellIdentity != null) {
                                    mMnc = String.valueOf(lteCellIdentity.getMnc());
                                    mMcc = String.valueOf(lteCellIdentity.getMcc());
                                    mLac = String.valueOf(lteCellIdentity.getTac());
                                    mCid = String.valueOf(lteCellIdentity.getCi());
                                }
                            } else if (cellInfo instanceof CellInfoWcdma) {
                                CellInfoWcdma cellInfoWcdma = (CellInfoWcdma) cellInfo;
                                CellIdentityWcdma identityWcdma = null;
                                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.JELLY_BEAN_MR2) {
                                    identityWcdma = cellInfoWcdma.getCellIdentity();
                                    if (identityWcdma != null) {
                                        mCid = String.valueOf(identityWcdma.getCid());
                                        mLac = String.valueOf(identityWcdma.getLac());
                                        mMnc = String.valueOf(identityWcdma.getMnc());
                                        mMcc = String.valueOf(identityWcdma.getMcc());
                                    }
                                }
                            }
                            if (mCid != null && !mCid.equals(String.valueOf(Integer.MAX_VALUE)))
                                locationInfo.setCid(mCid);
                            if (mLac != null && !mLac.equals(String.valueOf(Integer.MAX_VALUE)))
                                locationInfo.setLac(mLac);
                            if (mMnc != null && !mMnc.equals(String.valueOf(Integer.MAX_VALUE)))
                                locationInfo.setMnc(mMnc);
                            if (mMcc != null && !mMcc.equals(String.valueOf(Integer.MAX_VALUE)))
                                locationInfo.setMcc(mMcc);
                        }
                    }
                }
                //}
            } catch (Exception e) {
                FireCrash.log(e);
                e.printStackTrace();
            }
        }

        Date date = new Date(System.currentTimeMillis());
        locationInfo.setHandset_time(date);


        locationInfo.setUsr_crt(GlobalData.getSharedGlobalData().getUser().getUuid_user());
        locationInfo.setDtm_crt(date);
        locationInfo.setUser(GlobalData.getSharedGlobalData().getUser());

        switch (flag_location_type) {
            case Global.FLAG_LOCATION_TRACKING: // for tracking
                locationInfo.setLocation_type(Global.LOCATION_TYPE_TRACKING);
                break;
            case Global.FLAG_LOCATION_CHECKIN: // for check in
                locationInfo.setLocation_type(Global.LOCATION_TYPE_CHECKIN);
                break;
            case Global.FLAG_LOCATION_CHECKOUT: // for check out
                locationInfo.setLocation_type(Global.LOCATION_TYPE_CHECKOUT);
                break;
            case Global.FLAG_LOCATION_CAMERA: // for check out
                locationInfo.setLocation_type(Global.LOCATION_TYPE_CAMERA);
                break;

            default:
                break;
        }

        locationInfo.setUuid_location_info(Tool.getUUID());

        try {
            UpdateMenuGPS.SetMenuIcon();
        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: handle exception
        }

        return locationInfo;
    }
}
