package com.adins.mss.foundation.db.dataaccess;

import android.content.Context;
import android.database.Cursor;
import android.util.Log;

import com.adins.mss.base.commons.Query;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.dao.AssetScheme;
import com.adins.mss.dao.AssetSchemeDao;
import com.adins.mss.dao.DaoSession;
import com.adins.mss.dao.Lookup;
import com.adins.mss.foundation.db.DaoOpenHelper;
import com.adins.mss.foundation.questiongenerator.OptionAnswerBean;
import com.adins.mss.foundation.questiongenerator.QuestionBean;

import java.util.ArrayList;
import java.util.List;

import de.greenrobot.dao.query.QueryBuilder;

/**
 * Created by developer on 1/18/18.
 */

public class AssetSchemeDataAccess {

    /**
     * use to generate dao session that you can access modelDao
     *
     * @param context --> context from activity
     * @return
     */
    protected static DaoSession getDaoSession(Context context) {
        return DaoOpenHelper.getDaoSession(context);
    }

    /**
     * get assetSchemeDao and you can access DB
     */
    protected static AssetSchemeDao getAssetSchemeDao(Context context) {
        return getDaoSession(context).getAssetSchemeDao();
    }

    /**
     * add assetScheme as entity
     */
    public static void add(Context context, AssetScheme assetScheme) {
        getAssetSchemeDao(context).insert(assetScheme);
        getDaoSession(context).clear();
    }

    /**
     * add assetScheme as list entity
     */
    public static void add(Context context, List<AssetScheme> assetSchemes) {
        getAssetSchemeDao(context).insertInTx(assetSchemes);
        getDaoSession(context).clear();
    }

    /**
     * add or replace as entity
     */
    public static void addOrReplace(Context context, AssetScheme assetScheme) {
        getAssetSchemeDao(context).insertOrReplace(assetScheme);
        getDaoSession(context).clear();
    }

    /**
     * add or replace as list
     */
    public static void addOrReplace(Context context, List<AssetScheme> assetSchemes) {
        getAssetSchemeDao(context).insertOrReplaceInTx(assetSchemes);
        getDaoSession(context).clear();
    }

    /**
     * delete all assetScheme
     */
    public static void clean(Context context) {
        getAssetSchemeDao(context).deleteAll();
        getDaoSession(context).clear();
    }

    /**
     * delete one entity
     */
    public static void delete(Context context, AssetScheme assetScheme) {
        getAssetSchemeDao(context).delete(assetScheme);
        getDaoSession(context).clear();
    }

    /**
     * get all entity
     */
    public static List<AssetScheme> all(Context context) {
        List<AssetScheme> assetSchemes = getAssetSchemeDao(context).loadAll();
//        getDaoSession(context).clear();

        if (assetSchemes.isEmpty()) return null;
        else return assetSchemes;
    }

    public static List<OptionAnswerBean> lookup(Context context, Query ql) {
        List<OptionAnswerBean> answerBeans = new ArrayList<>();
        String query  = "SELECT MS_ASSET_SCHEME.ID, MS_ASSET_SCHEME.TYPE_NAME, MS_ASSET_SCHEME.TYPE_CODE FROM MS_ASSET_SCHEME LEFT JOIN MS_PO ON MS_ASSET_SCHEME.ASSET_SCHEME_ID = MS_PO.ASSET_SCHEME_ID WHERE %s";

        StringBuilder constraint = new StringBuilder("1");
        String groupBy  = " GROUP BY ";

        if (ql.getConstraint() != null) {
            List<Query.Constraint> constraints = ql.getConstraint();
            for (int i = 0; i < constraints.size(); i++) {
                constraint.append(" AND ")
                        .append(constraints.get(i).getColumn());

                //NENDI: 2018-04-10 | Add Operator on Query
                if (constraints.get(i).getOperator() != null) {
                    constraint.append(" ").append(constraints.get(i).getOperator().trim()).append(" ");
                    if (constraints.get(i).getOperator().toLowerCase().contains("like")) {
                        constraint.append("'%"+constraints.get(i).getValue()+"%'");
                    }
                    else if (constraints.get(i).getOperator().toLowerCase().contains("in")) {
                        constraint.append("(");
                        constraint.append(constraints.get(i).getValue());//                        }
                        constraint.append(")");
                    }
                    else {
                        constraint.append("'"+constraints.get(i).getValue()+"'");
                    }
                } else {
                    constraint.append("=");
                    constraint.append("'"+constraints.get(i).getValue()+"'");
                }
            }
        }

        if (ql.getGroup() != null) constraint.append(groupBy)
                .append(ql.getGroup());
        else constraint.append(groupBy)
                .append("MS_ASSET_SCHEME.TYPE_NAME");

        query = String.format(query, String.valueOf(constraint));
        Log.d(AssetScheme.class.getSimpleName(), query);

        Cursor cursor = getDaoSession(context)
                .getDatabase()
                .rawQuery(query, null);

        try {
            if (cursor.moveToFirst()) {
                do {
                    OptionAnswerBean optionAnswer = new OptionAnswerBean();
                    optionAnswer.setOption_id(String.valueOf(cursor.getInt(cursor.getColumnIndex("ID"))));
                    optionAnswer.setUuid_lookup(optionAnswer.getOption_id());
                    optionAnswer.setCode(cursor.getString(cursor.getColumnIndex("TYPE_CODE")));
                    optionAnswer.setValue(cursor.getString(cursor.getColumnIndex("TYPE_NAME")));
                    optionAnswer.setLov_group("ASSET_SCHEME");
                    answerBeans.add(optionAnswer);
                } while (cursor.moveToNext());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            cursor.close();
        }
        return answerBeans;
    }

    /**
     * Get Entity as Lookup
     */
    public static Lookup getById(Context context, QuestionBean qBean, long id) {
        QueryBuilder<AssetScheme> qb = getAssetSchemeDao(context).queryBuilder();
        qb.where(AssetSchemeDao.Properties.Id.eq(id));
        qb.limit(1);
        qb.build();

        Lookup lookup = null;
        int size = qb.list().size();
        if (size != 0) {
            AssetScheme assetScheme = qb.list().get(0);
            lookup = new Lookup(String.valueOf(assetScheme.getId()));
            Query query = GsonHelper.fromJson(qBean.getChoice_filter(), Query.class);

            if (query.getCode().equalsIgnoreCase("TYPE_CODE"))
            {
                lookup.setCode(String.valueOf(assetScheme.getType_code()));
                lookup.setValue(String.valueOf(assetScheme.getType_name()));
            }

            lookup.setOption_id(String.valueOf(assetScheme.getId()));
            lookup.setUuid_lookup(String.valueOf(assetScheme.getId()));
        }

        return lookup;
    }

    public static AssetScheme last(Context context) {
        QueryBuilder<AssetScheme> qb = getAssetSchemeDao(context).queryBuilder();
        qb.orderDesc(AssetSchemeDao.Properties.Dtm_upd);
        qb.limit(1);
        qb.build();

        if (!qb.list().isEmpty()) return qb.list().get(0);
        return null;
    }

    public static void addOrUpdateAll(Context context, List<AssetScheme> transaction) {
        getAssetSchemeDao(context).insertOrReplaceInTx(transaction);
        getDaoSession(context).clear();
    }
}
