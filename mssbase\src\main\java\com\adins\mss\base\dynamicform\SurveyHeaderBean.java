package com.adins.mss.base.dynamicform;

import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.util.ExcludeFromGson;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.TaskH;
import com.adins.mss.foundation.db.dataaccess.TaskHDataAccess;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

//import com.adins.util.Formatter;
//import com.adins.util.Tool;

public class SurveyHeaderBean extends TaskH implements Serializable, Cloneable {
    @ExcludeFromGson
    int lastQuestion;
    //	private boolean isVerification;
//	private Date assignmentDate;
//	private String latitude="0";
//	private String longitude="0";
//	private Date schemeLastUpdate;
//	private boolean previewNeedServer;
    //	private String id="";
//	private String customerName="";
//	private String customerPhone="";
//	private String customerAddress="";
//	private String notes="";
//	private int priority;
//	private String sentStatus="";
    @ExcludeFromGson
    private FormBean form;
    @ExcludeFromGson
    private int imageLeft;

    public SurveyHeaderBean() {
    }

    public SurveyHeaderBean(TaskH taskH) {
        if (taskH == null)
            throw new IllegalArgumentException("taskH is null!");

//		this.id =  setUuid_task_h(taskH.getUuid_task_h());
//		this.customerName = taskH.getCustomer_name();
//		this.customerAddress = taskH.getCustomer_address();
//		this.customerPhone = taskH.getCustomer_phone();
//		this.priority = (int) taskH.getPriority();
//		this.latitude = taskH.getLatitude();
//		this.longitude = taskH.getLongitude();
//		this.notes = taskH.getNotes();
//
//
//		this.schemeLastUpdate = taskH.getScheme_last_update();
//		this.isVerification = taskH.getIs_verification();
//		this.assignmentDate = taskH.getAssignment_date();

        setUuid_task_h(taskH.getUuid_task_h());
        setTask_id(taskH.getTask_id());
        setStatus(taskH.getStatus());
        setIs_printable(taskH.getIs_printable());
        setCustomer_name(taskH.getCustomer_name());
        setCustomer_phone(taskH.getCustomer_phone());
        setCustomer_address(taskH.getCustomer_address());
        setNotes(taskH.getNotes());
        setSubmit_date(taskH.getSubmit_date());
        setSubmit_duration(taskH.getSubmit_duration());
        setSubmit_size(taskH.getSubmit_size());
        setSubmit_result(taskH.getSubmit_result());
        setAssignment_date(taskH.getAssignment_date());
        setPrint_count(taskH.getPrint_count());
        setDraft_date(taskH.getDraft_date());
        setUsr_crt(taskH.getUsr_crt());
        setDtm_crt(taskH.getDtm_crt());
        setPriority(taskH.getPriority());
        setLatitude(taskH.getLatitude());
        setLongitude(taskH.getLongitude());
        setScheme_last_update(taskH.getScheme_last_update());
        setIs_verification(taskH.getIs_verification());
        setIs_preview_server(taskH.getIs_preview_server());
        setUuid_user(taskH.getUuid_user());
        setVoice_note(taskH.getVoice_note());
        setUuid_scheme(taskH.getUuid_scheme());
        setZip_code(taskH.getZip_code());
        setScheme(taskH.getScheme());
        setStart_date(taskH.getStart_date());
        setOpen_date(taskH.getOpen_date());
        setLast_saved_question(taskH.getLast_saved_question());
        setAppl_no(taskH.getAppl_no());
        setIs_prepocessed(taskH.getIs_prepocessed());
        setIs_reconciled(taskH.getIs_reconciled());
        setPts_date(taskH.getPts_date());
        setAccess_mode(taskH.getAccess_mode());
        setRv_number(taskH.getRv_number());
        setStatus_rv(taskH.getStatus_rv());
        setFlag(taskH.getFlag());
        setForm_version(taskH.getForm_version());
        setAmt_due(taskH.getAmt_due());
        setOd(taskH.getOd());
        setInst_no(taskH.getInst_no());
        setPms_date(taskH.getPms_date());
        setIs_revisit(taskH.getIs_revisit());

        /*Tambahan CAE*/
        setSend_task_promise_to_survey(taskH.getSend_task_promise_to_survey());
        setSend_task_presurvey(taskH.getSend_task_presurvey());
        setSend_task_survey(taskH.getSend_task_survey());
        setIs_piloting_cae(taskH.getIs_piloting_cae());
        setNotes_crm(taskH.getNotes_crm());
        setKelurahan(taskH.getKelurahan());
        setCategory(taskH.getCategory());
        setSub_category(taskH.getSub_category());
        setValidasi(taskH.getValidasi());
        setReason_detail(taskH.getReason_detail());

        // Add for Pre Approval
        setIs_pre_approval(taskH.getIs_pre_approval());

        // 3rd party
        setSource_data(taskH.getSource_data());

        String schemeIsPrintable = Global.FALSE_STRING;
        try {
            schemeIsPrintable = taskH.getIs_printable();
        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: handle exception
        }


        String schemeId = taskH.getUuid_scheme();
        this.form = new FormBean(schemeId, getScheme_last_update(), schemeIsPrintable);

        try {
            String previewServer = taskH.getIs_preview_server();
            if (getForm() != null) {
                getForm().setPreviewServer(previewServer);
            }
        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: handle exception
            String previewServer = Global.FALSE_STRING;
            if (getForm() != null) {
                getForm().setPreviewServer(previewServer);
            }
        }

        setProduct_name(taskH.getProduct_name());
        setJenis_asset(taskH.getJenis_asset());
    }

    public static List<SurveyHeaderBean> parseToDataList(List<TaskH> taskH) {
        if (taskH == null)
            return null;

        List<SurveyHeaderBean> result = new ArrayList<SurveyHeaderBean>();
        for (TaskH taskHBean : taskH) {
            SurveyHeaderBean bean = new SurveyHeaderBean(taskHBean);
            result.add(bean);
        }
        return result;
    }
//	private String applNo="";

    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }

    /**
     * Gets the lastQuestion
     */
    public int getLastQuestion() {
        return this.lastQuestion;
    }

    /**
     * Sets the lastQuestion
     */
    public void setLastQuestion(int value) {
        this.lastQuestion = value;
    }

//	public String getId() {
//		return id;
//	}
//
//	public void setId(String id) {
//		this.id = id;
//	}
//
//	public String getCustomerName() {
//		return customerName;
//	}
//
//	public void setCustomerName(String customerName) {
//		this.customerName = customerName;
//	}
//
//	public String getCustomerPhone() {
//		return customerPhone;
//	}
//
//	public void setCustomerPhone(String customerPhone) {
//		this.customerPhone = customerPhone;
//	}
//
//	public String getCustomerAddress() {
//		return customerAddress;
//	}
//
//	public void setCustomerAddress(String customerAddress) {
//		this.customerAddress = customerAddress;
//	}
//
//	public String getNotes() {
//		return notes;
//	}
//
//	public void setNotes(String notes) {
//		this.notes = notes;
//	}
//
//	public int getPriority() {
//		return  priority;
//	}
//
//	public void setPriority(int priority) {
//		this.priority = priority;
//	}
//
//	public String getSentStatus() {
//		return sentStatus;
//	}
//
//	public void setSentStatus(String sentStatus) {
//		this.sentStatus = sentStatus;
//	}

    public TaskH getTaskH() {
        return this;
    }

    public FormBean getForm() {
        return form;
    }

//	public boolean isVerification() {
//		return isVerification;
//	}
//
//	public void setVerification(boolean isVerification) {
//		this.isVerification = isVerification;
//	}
//
//	public Date getAssignmentDate() {
//		return assignmentDate;
//	}
//
//	public void setAssignmentDate(Date assignmentDate) {
//		this.assignmentDate = assignmentDate;
//	}

    public void setForm(FormBean form) {
        this.form = form;
    }
    // code dari project tower bersama msmtow
    /*public String toTitle() {
        String count = null;
		if(assignmentDate==null){
			assignmentDate = new Date();
			assignmentDate.setYear(0);
			assignmentDate.setMonth(0);
			assignmentDate.setDate(0);
		}

		String t = Formatter.formatDate(assignmentDate, Global.DATE_STR_FORMAT2);


		StringBuilder s = new StringBuilder()
			.append(form.getSchemeId()).append(" - ")
			.append(t).append(" - ")
			.append(getCustomerPhone()).append(" - ");

		if (Global.STATUS_SEND_UPLOADING.equals(sentStatus) && imageLeft > 0) {
			count = " / " + imageLeft;
		}
		else
			count = "";


			s.append(sentStatus).append(count);

		return s.toString();
	}*/

    public void setImageLeft(int imageLeft) {
        this.imageLeft = imageLeft;
    }

    public String toTitle() {
        StringBuilder s = new StringBuilder()
                .append(getScheme().getForm_id()).append(" - ")
                .append(getCustomer_name());
        return s.toString();
    }

    public String toDescription() {
        String count = null;
        //Glen 12/12/14 moved constant from Global to TaskHDataAccess
        if (TaskHDataAccess.STATUS_SEND_UPLOADING.equals(getStatus()) && imageLeft > 0) {
            count = " / " + imageLeft;
        } else
            count = "";

        StringBuilder s = new StringBuilder()
                .append(getStatus()).append(count).append(" - ")
                .append(form.getForm_id());
        return s.toString();
    }
    ////////////////
    /*public static SurveyHeaderBean parseFromDbString(String s) {
        if (s == null || "".equals(s.trim()))
			return null;

		SurveyHeaderBean bean = new SurveyHeaderBean();
		bean.setForm(new FormBean());

		String[] arrS = Tool.split(s, Global.DELIMETER_DATA);
		int idx = 0;
		bean.setId(arrS[idx++]);
		bean.setSentStatus(arrS[idx++]);
		bean.getForm().setSchemeId(arrS[idx++]);
		boolean isPrintable = "1".equals(arrS[idx++]);
		bean.getForm().setPrintable(isPrintable);

		//Glen 8 Aug 2014, add previewNeedServer
		boolean previewNeedServer = "1".equals(arrS[idx++]);
		bean.getForm().setPreviewNeedServer(previewNeedServer);

		bean.setCustomerName(arrS[idx++]);
		bean.setCustomerPhone(arrS[idx++]);
		bean.setCustomerAddress(arrS[idx++]);

		String notes = arrS[idx++];
		String notesArr[] = Tool.split(notes,Global.DELIMETER_SUBDATA);

		if(notesArr.length>1){
			bean.setLatitude(notesArr[0]);
			bean.setLongitude(notesArr[1]);
			bean.setNotes(notesArr[2]);

		}else{
			bean.setNotes(notesArr[0]);

		}

		try {
			bean.setAssignmentDate(new Date(Long.parseLong(arrS[idx++])));
		} catch (Exception e) {
                    FireCrash.log(e);
			if (Global.IS_DEV) System.out.println("setAssignmentDate error "+e);
		}

		// untuk tafs yg ditampilkan pada list adalah appl no,
		// namun komunikasi nya tertap menggunakan task id
		try {
			bean.setApplNo(arrS[idx++]);
		} catch (Exception e) {
                    FireCrash.log(e);
			if (Global.IS_DEV) System.out.println("setApplNo error "+e);
		}


		return bean;
	}*/

    public String toUploadingStatus() {
        String count = null;
        StringBuilder s = new StringBuilder();
        if (TaskHDataAccess.STATUS_SEND_UPLOADING.equals(getStatus()) && imageLeft > 0) {
            count = " " + imageLeft;
            s.append(count);
//			.append(" - ")
//			.append(getScheme().getForm_id());
        } else {
            count = "";
            s.append(getScheme().getForm_id());
        }
        return s.toString();
    }

//	public String getLatitude() {
//		return latitude;
//	}
//
//	public void setLatitude(String latitude) {
//		this.latitude = latitude;
//	}
//
//	public String getLongitude() {
//		return longitude;
//	}
//
//	public void setLongitude(String longitude) {
//		this.longitude = longitude;
//	}
//
//	public String getApplNo() {
//		return applNo;
//	}
//
//	public void setApplNo(String applNo) {
//		this.applNo = applNo;
//	}

//	public boolean previewNeedServer() {
//		return previewNeedServer;
//	}
//
//	public void setPreviewNeedServer(boolean previewNeedServer) {
//		this.previewNeedServer = previewNeedServer;
//	}
}
