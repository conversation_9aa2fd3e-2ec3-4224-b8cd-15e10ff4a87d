package com.adins.mss.base;

import android.content.Context;
import android.graphics.Color;

public class GlobalUI {

    public static int color01 = 0;
    public static int color02 = 0;
    public static int color03 = 0;
    public static int color04 = 0;
    public static int color05 = 0;
    public static int color06 = 0;
    public static int color07 = 0;
    public static int color08 = 0;

    public GlobalUI() {
        // TODO Auto-generated constructor stub
    }

    public static void setColor01(String hexaColor) {
        color01 = Color.parseColor(hexaColor);
    }

    public static void setColor02(String hexaColor) {
        color02 = Color.parseColor(hexaColor);
    }

    public static void setColor03(String hexaColor) {
        color03 = Color.parseColor(hexaColor);
    }

    public static void setColor04(String hexaColor) {
        color04 = Color.parseColor(hexaColor);
    }

    public static void setColor05(String hexaColor) {
        color05 = Color.parseColor(hexaColor);
    }

    public static void setColor06(String hexaColor) {
        color06 = Color.parseColor(hexaColor);
    }

    public static void setColor07(String hexaColor) {
        color07 = Color.parseColor(hexaColor);
    }

    public static void setColor08(String hexaColor) {
        color08 = Color.parseColor(hexaColor);
    }

    public static void setAllColor(String hexaColorArr[]) {
        color01 = Color.parseColor(hexaColorArr[0]);
        color02 = Color.parseColor(hexaColorArr[1]);
        color03 = Color.parseColor(hexaColorArr[2]);
        color04 = Color.parseColor(hexaColorArr[3]);
        color05 = Color.parseColor(hexaColorArr[4]);
        color06 = Color.parseColor(hexaColorArr[5]);
        color07 = Color.parseColor(hexaColorArr[6]);
        color08 = Color.parseColor(hexaColorArr[7]);
    }

    public static int getImage(Context context, String fileNamePath) {
//		int id = context.getResources().getIdentifier("yourpackagename:drawable/" + StringGenerated, null, null);
        int id = context.getResources().getIdentifier(fileNamePath, null, null);
        return id;
    }

}
