package com.adins.mss.dao;

import com.google.gson.annotations.Since;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "MS_MOBILEDATAFILES".
 */
public class mobiledatafile {

    private long id_datafile;
    private String is_active;
    private java.util.Date max_timestamp;
    private String file_url;
    private String alternate_file_url;
    private String hash_sha1;
    private String usr_crt;
    private java.util.Date dtm_crt;
    private String usr_upd;
    private java.util.Date dtm_upd;
    private String downloaded_file_path;
    private Boolean import_flag;

    public mobiledatafile() {
    }

    public mobiledatafile(long id_datafile) {
        this.id_datafile = id_datafile;
    }

    public mobiledatafile(long id_datafile, String is_active, java.util.Date max_timestamp, String file_url, String alternate_file_url, String hash_sha1, String usr_crt, java.util.Date dtm_crt, String usr_upd, java.util.Date dtm_upd, String downloaded_file_path, Boolean import_flag) {
        this.id_datafile = id_datafile;
        this.is_active = is_active;
        this.max_timestamp = max_timestamp;
        this.file_url = file_url;
        this.alternate_file_url = alternate_file_url;
        this.hash_sha1 = hash_sha1;
        this.usr_crt = usr_crt;
        this.dtm_crt = dtm_crt;
        this.usr_upd = usr_upd;
        this.dtm_upd = dtm_upd;
        this.downloaded_file_path = downloaded_file_path;
        this.import_flag = import_flag;
    }

    public long getId_datafile() {
        return id_datafile;
    }

    public void setId_datafile(long id_datafile) {
        this.id_datafile = id_datafile;
    }

    public String getIs_active() {
        return is_active;
    }

    public void setIs_active(String is_active) {
        this.is_active = is_active;
    }

    public java.util.Date getMax_timestamp() {
        return max_timestamp;
    }

    public void setMax_timestamp(java.util.Date max_timestamp) {
        this.max_timestamp = max_timestamp;
    }

    public String getFile_url() {
        return file_url;
    }

    public void setFile_url(String file_url) {
        this.file_url = file_url;
    }

    public String getAlternate_file_url() {
        return alternate_file_url;
    }

    public void setAlternate_file_url(String alternate_file_url) {
        this.alternate_file_url = alternate_file_url;
    }

    public String getHash_sha1() {
        return hash_sha1;
    }

    public void setHash_sha1(String hash_sha1) {
        this.hash_sha1 = hash_sha1;
    }

    public String getUsr_crt() {
        return usr_crt;
    }

    public void setUsr_crt(String usr_crt) {
        this.usr_crt = usr_crt;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public String getUsr_upd() {
        return usr_upd;
    }

    public void setUsr_upd(String usr_upd) {
        this.usr_upd = usr_upd;
    }

    public java.util.Date getDtm_upd() {
        return dtm_upd;
    }

    public void setDtm_upd(java.util.Date dtm_upd) {
        this.dtm_upd = dtm_upd;
    }

    public String getDownloaded_file_path() {
        return downloaded_file_path;
    }

    public void setDownloaded_file_path(String downloaded_file_path) {
        this.downloaded_file_path = downloaded_file_path;
    }

    public Boolean getImport_flag() {
        return import_flag;
    }

    public void setImport_flag(Boolean import_flag) {
        this.import_flag = import_flag;
    }

}
