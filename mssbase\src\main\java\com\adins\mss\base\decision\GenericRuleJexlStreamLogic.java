package com.adins.mss.base.decision;

import org.apache.commons.jexl2.Expression;
import org.apache.commons.jexl2.JexlContext;
import org.apache.commons.jexl2.JexlEngine;
import org.apache.commons.jexl2.JexlException;
import org.apache.commons.jexl2.MapContext;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.eventusermodel.HSSFEventFactory;
import org.apache.poi.hssf.eventusermodel.HSSFListener;
import org.apache.poi.hssf.eventusermodel.HSSFRequest;
import org.apache.poi.hssf.record.LabelSSTRecord;
import org.apache.poi.hssf.record.NumberRecord;
import org.apache.poi.hssf.record.Record;
import org.apache.poi.hssf.record.SSTRecord;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

//Based on Event API of APACHE POI

public class GenericRuleJexlStreamLogic implements HSSFListener {


	private static final String SKIP_EVALUATION = "-";
	private static final String SP_OBJECT_NAME = "this";
	private static final String DATA_VAR_NAME = "val";

	private Map<String, Object> dataObjects;
	private File excelFile;

	JexlEngine jexl = new JexlEngine();
	JexlContext jexlContext = new MapContext();

	private SSTRecord sstrec;
	private RuleMetadataBean meta = new RuleMetadataBean();
	private boolean headersRead = false;
//	private boolean conditionsRead = false;
//	private boolean actionsRead = false;
	private int numOfConditions = 0;
	private int numOfActions = 0;

	private List<String> actionObjects = new ArrayList<String>();
	private List<ActionBean> actions = new ArrayList<ActionBean>();
	private List<String> conditions = new ArrayList<String>();

	private int currRow = -1;
	private int rowToIgnore = -1;
	private boolean skipRead = false;


	public GenericRuleJexlStreamLogic(File file, Map<String, Object> dataObjects) {
		this.dataObjects = dataObjects;
		this.excelFile = file;
	}

	public RuleMetadataBean getMeta() {
		return meta;
	}

	public List<ActionBean> getActions() {
		return actions;
	}

	public List<String> getConditions() {
		return conditions;
	}

	public void execute() throws IOException {

		//put all dataObjects variables to evaluationContext
		for (Iterator<String> iterator = dataObjects.keySet().iterator(); iterator.hasNext();) {
			String varName = iterator.next();
			jexlContext.set(varName, dataObjects.get(varName));
		}

		FileInputStream fin = new FileInputStream(this.excelFile);
		POIFSFileSystem poifs = new POIFSFileSystem(fin);
		InputStream din = poifs.createDocumentInputStream("Workbook");		//Had no idea what this "Workbook" is
		HSSFRequest req = new HSSFRequest();
		req.addListenerForAllRecords(this);
		HSSFEventFactory factory = new HSSFEventFactory();

		//main execution, check processRecord()
		factory.processEvents(req, din);

		//closing
		fin.close();
		din.close();
//		logic.executeRule(din, dataObjects);
//		return 0.0;
	}

	@Override
	public void processRecord(Record record) {
		short currSid = record.getSid();

		//get the unique string cell table
		if (currSid == SSTRecord.sid ){
			sstrec = (SSTRecord) record;
		}

		//if it's string value and we haven't done reading headers yet, we process the cell read here
		if (!headersRead && currSid == LabelSSTRecord.sid ) {
			LabelSSTRecord lrec = (LabelSSTRecord) record;
			String cellValue = sstrec.getString(lrec.getSSTIndex()).getString();
			int columnIndex = lrec.getColumn();

			//we count how many CONDITION and ACTION on this .xls
			if (RuleLogic.CONDITION_STRING.equals(cellValue)) {
				if (meta.getConditionStart() == -1) {
					meta.setConditionStart(columnIndex);
				}
				meta.setConditionEnd(columnIndex);
			} else if (RuleLogic.ACTION_STRING.equals(cellValue)) {
				if (meta.getActionStart() == -1) {
					meta.setActionStart(columnIndex);
				}
				meta.setActionEnd(columnIndex);
			}
			//if a non CONDITION/ACTION is found after CONDITION is found, then it's the end of CONDITION/ACTION
			//Mark the end of CONDITION/ACTION
			else if (meta.getConditionStart() != -1) {

				//if we haven't count how many conditions/actions there are, do it
				//only check if numOfConditions 0, in case there might be no action
				if (numOfConditions == 0) {
					numOfConditions = meta.getConditionEnd() - meta.getConditionStart() + 1;
					numOfActions = meta.getActionEnd() - meta.getActionStart() + 1;
				}

				//get the object below ACTION, as much as the num of ACTION is, to fill up actionObjects
				if (actionObjects.size() < numOfActions) {
					String objName = cellValue;

					objName = StringUtils.remove(objName, "#");
					actionObjects.add(objName);
				}
				//get the next conditions and fill up conditions as much as the num of CONDITIONS
				else if (conditions.size() < numOfConditions){
					String eval = cellValue;
					eval = StringUtils.remove(eval, "#");
					eval = StringUtils.remove(eval, "@");
					conditions.add(eval);
				}
				//now actionObjects and CONDITIONS has been filled up, now get the method of ACTION to be appended to actionObjects
				else if (actions.size() < numOfActions){
					String objName = actionObjects.get(actions.size());		//get the corresponding object name of current iteration
					String objFieldMethod = cellValue;
					ActionBean actionBean = new ActionBean(objName, objFieldMethod);
					actions.add(actionBean);

					//when all three List has been filled up, we can conclude that headers has been read
					//we do checking on this iteration in order to start reading the content on next iteration
					if (actions.size() == numOfActions){
						headersRead = true;
					}
				}
			}

//					break;		//[TODO] Should we use break..? will check later
		}

		//start reading content when headers successfully read
		else if (headersRead){

			//skip if we don't really need to read anything
			if (skipRead) return;

			String cellValue = "";
			int row = -1;
			int column = -1;

			switch (record.getSid())
			{
				case NumberRecord.sid:
					NumberRecord numrec = (NumberRecord) record;
					cellValue = String.valueOf(numrec.getValue());
					row = numrec.getRow();
					column = numrec.getColumn();
					break;


				case LabelSSTRecord.sid:
					LabelSSTRecord lrec = (LabelSSTRecord) record;
					cellValue = sstrec.getString(lrec.getSSTIndex()).getString();
					row = lrec.getRow();
					column = lrec.getColumn();
					break;

				default:
					return;
			}

			// Comment this to increase performance
//			System.out.println("Cell found with value " + cellValue + " at row " + row + " and column " + column);

			currRow = row;		//TODO maybe we don't really need this 'currRow', and use just 'row' instead
			//ignore row, if current row is registered as ignored. e.g. when condition is this row is not met
			if (currRow == rowToIgnore) return;

			if (column >= meta.getConditionStart() && column <= meta.getConditionEnd()){
				//process CONDITION

				//read corresponding conditions of current column being read
				String condition = conditions.get(column - meta.getConditionStart());		//minus condition start in case of offset TODO check calculation
				Expression expression = null;

				boolean isConditionMet = false;
				jexlContext.set(DATA_VAR_NAME, cellValue);

				//If "-" or condition is met, continue to next row. if not, ignore this row
				if (SKIP_EVALUATION.equals(cellValue)){
					isConditionMet = true;
				}
				else if (condition.startsWith("like(") && condition.endsWith(")") && StringUtils.contains(condition, ",")) {
					condition = StringUtils.remove(condition, "like(");
					condition = StringUtils.remove(condition, ")");
					String[] varCondition = StringUtils.split(condition, ",");
					CharSequence comparison = (CharSequence) jexlContext.get(varCondition[0]);
					//Glen: use cellValue instead
//					CharSequence criteria = (CharSequence) val;
					CharSequence criteria = (CharSequence) cellValue;

					//Glen: let's put on more general var shall we?
//					boolean boolLike = UserDefinedFunction.like(comparison, criteria);
					isConditionMet = UserDefinedFunction.like(comparison, criteria);

				} else {
					//Glen: is this the same thing?
//					expression = jexl.createExpression(conditions.get(i));
					expression = jexl.createExpression(condition);
					try {
						//Glen: Let's put on more general var
//						evalResult = (boolean) expression.evaluate(jexlContext);
						isConditionMet = (boolean) expression.evaluate(jexlContext);
					} catch (JexlException e) {
						throw new RuntimeException();
					}
				}

//				System.out.println("Process as CONDITION, condition met as " + isConditionMet);
				//if condition is true, continue to next column, if not, skip to next row
				if (!isConditionMet){
					//register current row as 'ignored' so we can ignore it on next iteration
					rowToIgnore = currRow;
					return;
				}

			}
			else if (column >= meta.getActionStart() && column <= meta.getActionEnd()){
				//process ACTION

//				System.out.println("Process as ACTION");

				ActionBean actionBean = actions.get(column - meta.getActionStart());		//TODO check if calculation is correct

				String expression = RuleUtils.createExpression(actionBean.getObjName(),
						actionBean.getObjFieldMethod(), cellValue);

				if (StringUtils.contains(cellValue, "~")) {
					Object keyValue = StringUtils.remove(cellValue, "~");
					keyValue = dataObjects.get(keyValue);
					keyValue = (keyValue == null) ? "" : keyValue;
					if (StringUtils.isNotBlank(keyValue.toString())) {
						expression = expression.replace(cellValue, keyValue.toString());
					}
				}
//				if (SP_OBJECT_NAME.equals(actionBean.getObjName()) &&
//						SP_OBJECT_METHOD.equalsIgnoreCase(actionBean.getObjFieldMethod())) {
//					this.doExecuteRule(sheet.getWorkbook(), cellValue, dataObjects);
//
//					continue loopActions;
//				}
//				else {
					try {
						Expression exp = jexl.createExpression(expression);
						exp.evaluate(jexlContext);
					} catch (JexlException e) {
						throw new RuntimeException();
					}
//				}

				//this is not needed, as rule engine read until the bottom
				//when we reached the last ACTIONS, we conclude we found the needed data and skip the other rows
//				if (column == meta.getActionEnd()) {
//					System.out.println("Last ACTION met");
//					skipRead = true;
//				}

				//TODO process the meta.isSkipOnFirstAppliedRule()

			}
		}
	}


}