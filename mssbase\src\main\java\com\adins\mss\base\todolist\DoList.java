package com.adins.mss.base.todolist;

import android.content.Context;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.QuestionSet;
import com.adins.mss.dao.Scheme;
import com.adins.mss.dao.TaskD;
import com.adins.mss.dao.TaskH;
import com.adins.mss.dao.User;
import com.adins.mss.foundation.db.dataaccess.QuestionSetDataAccess;
import com.adins.mss.foundation.db.dataaccess.SchemeDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskDDataAccess;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.http.MssRequestType;

import java.util.List;

public class DoList {
    private static Context context;
    //	private SchemeDataAccess schemeDataAccess;
//	private TaskHDataAccess taskHDataAccess;
    private static List<Scheme> listScheme;
    private static List<QuestionSet> listQuestionSet;
    private TaskH taskH;
    private List<TaskD> listTaskD;
    private User user;

    public DoList(Context context) {
        DoList.context = context;
//		schemeDataAccess = new SchemeDataAccess();
//		taskHDataAccess = new TaskHDataAccess();
        user = GlobalData.getSharedGlobalData().getUser();
    }

    /**
     * For the first call, this method will download scheme list and all question set of each scheme from server and store them to local storage.
     * For the next call, this method will return a list from local storage.
     *
     * @return
     */
    public List<Scheme> getListScheme() {
        listScheme = SchemeDataAccess.getAll(context);
        if (listScheme == null)
            listScheme = getListSchemeFromServer();
        for (Scheme scheme : listScheme) {
            getQuestionSetFromServer(scheme);
        }
        return listScheme;
    }

    /**
     * it will return null if not be initialzed first
     *
     * @return
     */
    public TaskH getTaskH() {
        return taskH;
    }

    /**
     * This method will download list scheme from server and store it to local storage
     *
     * @return
     */
    public List<Scheme> getListSchemeFromServer() {
////		if (listScheme==null){
        // List<NameValuePair> param = new ArrayList<NameValuePair>();
        // param.add(new BasicNameValuePair("task", "getList"));
        // param.add(new BasicNameValuePair("userId", user.getUuid_user()));

        //TODO belum tentu fix pake objek ini
        // BaseCommunicationModel bcm = new BaseCommunicationModel(true);
        MssRequestType mrt = new MssRequestType();
        mrt.setAudit(GlobalData.getSharedGlobalData().getAuditData());
        mrt.addImeiAndroidIdToUnstructured();

        // to JSON
        String json = GsonHelper.toJson(mrt);

        boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
        boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
        HttpCryptedConnection httpConn = new HttpCryptedConnection(context, encrypt, decrypt);
        HttpConnectionResult result = null;
        try {
            result = httpConn.requestToServer(GlobalData.getSharedGlobalData().getURL_GET_SCHEME(), json, Global.DEFAULTCONNECTIONTIMEOUT);
        } catch (Exception e) {
            FireCrash.log(e);
            e.printStackTrace();
        }

        // result.getResult(); // -> balikan dari server
        // listScheme = gson.fromJson(result.getResult(), new TypeToken<List<Scheme>>(){}.getType());
        // TODO for checking scheme
        // for(Scheme scheme : listScheme) if (Global.IS_DEV) System.out.println("Scheme form Id = "+scheme.getForm_id());

        JsonGetScheme jgs = new JsonGetScheme();
        jgs = GsonHelper.fromJson(result.getResult(), JsonGetScheme.class);
        listScheme = jgs.getListScheme();

        for (Scheme scheme : listScheme) {
            if (scheme.getUuid_scheme() == null) {
                listScheme.remove(scheme);
            }
        }

//			String stringListScheme = null;
//			stringListScheme =  Tool.getServerResponse(Global.URL_GET_SCHEME, param);
//			if(stringListScheme==null||"".equals(stringListScheme))
//				return null;
//			else{
//				String[] arrS = Tool.split(stringListScheme, Global.DELIMETER_ROW);
//				if(arrS!=null && arrS.length>0){
//					listScheme = new ArrayList<Scheme>();
//					for(String schemeString : arrS){
//						Scheme scheme = new Scheme(schemeString);
//						listScheme.add(scheme);
//					}
//				}else return null;
//			}

        //bong 19 may 15 - delete scheme dulu baru di add dari server
        if (!listScheme.isEmpty()) {
            SchemeDataAccess.clean(context);
        }

        SchemeDataAccess.addOrReplace(context, listScheme);
////		}
        return listScheme;
    }

    /**
     * It will initiate a new object for TaskH data type
     *
     * @param scheme
     * @return
     */
    public TaskH getNewTaskH(Scheme scheme) {
        taskH = null;
        taskH = new TaskH();

        taskH.setUser(user);
        taskH.setScheme(scheme);
        return taskH;
    }

    /**
     * It will return all Task Detail for a taskId
     *
     * @param taskId
     * @param withImage
     * @return
     */
    public List<TaskD> getListTaskD(String taskId, int withImage) {
        listTaskD = null;
        listTaskD = TaskDDataAccess.getAllByTaskId(context, user.getUuid_user(), taskId, withImage);
        return listTaskD;
    }

    // need of getQuestionSet

    /**
     * It will return all question set for a scheme from local
     *
     * @param scheme
     * @return
     */
    public List<QuestionSet> getQuestionSet(Scheme scheme) {
        List<QuestionSet> listQuestionSet = QuestionSetDataAccess.getAll(context, scheme.getUuid_scheme());
        if (listQuestionSet.isEmpty())
            return getQuestionSetFromServer(scheme);
        else return listQuestionSet;
    }

    /**
     * It will return all question set for a scheme from server
     *
     * @param scheme
     * @return
     */
    public List<QuestionSet> getQuestionSetFromServer(Scheme scheme) {
//		if(QuestionSetDataAccess.getAll(context, uuidScheme)==null){

        //TODO belum tentu fix pake objek ini
//		BaseCommunicationModel bcm = new BaseCommunicationModel(true);

        RequestQuestionSetBean rqsb = new RequestQuestionSetBean();
        rqsb.setFormId(scheme.getForm_id());
        rqsb.setAudit(GlobalData.getSharedGlobalData().getAuditData());
        rqsb.addImeiAndroidIdToUnstructured();
        // to JSON
        String json = GsonHelper.toJson(rqsb);

        boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
        boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
        HttpCryptedConnection httpConn = new HttpCryptedConnection(context, encrypt, decrypt);
        HttpConnectionResult result = null;
        try {
            result = httpConn.requestToServer(GlobalData.getSharedGlobalData().getURL_GET_QUESTIONSET(), json, Global.DEFAULTCONNECTIONTIMEOUT);
        } catch (Exception e) {
            FireCrash.log(e);
            e.printStackTrace();
        }
        // listQuestionSet = gson.fromJson(result.getResult(), new TypeToken<List<QuestionSet>>(){}.getType());
        JsonQuestionSet jqs = new JsonQuestionSet();
        jqs = GsonHelper.fromJson(result.getResult(), JsonQuestionSet.class);
        listQuestionSet = jqs.getListQuestionSet();
        QuestionSetDataAccess.addOrReplace(context, scheme.getUuid_scheme(), listQuestionSet);
        return listQuestionSet;
//		}
//		else
//			return QuestionSetDataAccess.getAll(context, uuidScheme);
    }

    /**
     * This method will redownload scheme list and all question set of each scheme from server, then store them to local storage
     */
    public void doRefresh() {
        listScheme = getListSchemeFromServer();
        for (Scheme scheme : listScheme) {
            getQuestionSetFromServer(scheme);
        }
    }

    /**
     * Gets List Scheme for Mobile Order
     *
     * @return Scheme List
     * <AUTHOR>
     */
    public List<Scheme> getOrderListScheme() {
        return SchemeDataAccess.getAllOrderScheme(context);
    }

    /**
     * Gets List Scheme for Mobile Survey
     *
     * @return Scheme List
     * <AUTHOR>
     */
    public List<Scheme> getSurveyListScheme() {
        return SchemeDataAccess.getAllSurveyScheme(context);
    }

    /**
     * Gets List Scheme for Mobile Survey
     *
     * @return Scheme List
     * <AUTHOR>
     */
    public List<Scheme> getCollListScheme() {
        return SchemeDataAccess.getAllCollectionScheme(context);
    }
}
