package com.adins.mss.base.tasklog;

import android.content.Context;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.log.Log;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.TaskH;
import com.adins.mss.foundation.db.dataaccess.TaskHDataAccess;

import java.util.List;

public class TaskLog {
    private static Context context;
    private static List<TaskH> listTask;
    //private static String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();

    public TaskLog(Context context) {
        this.context = context;
    }

    public static long getCounterLog(Context context) {
        long counter = 0;
        try {
            Log log = new Log(context);
            List<TaskH> listTaskCounter = log.getAllSentTaskWithLimited();
            counter = listTaskCounter == null? 0:listTaskCounter.size();
        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: handle exception
        }
        return counter;
    }

    public List<TaskH> getListTaskLog() {
        Log log = new Log(this.context);
        //listTask = TaskHDataAccess.getAllSentTask(context, uuidUser);
        listTask = log.getAllSentTaskWithLimited();
        Global.listOfSentTask = listTask;
        return listTask;
    }

    public List<TaskH> doRefresh() {
        return getListTaskLog();
    }
}
