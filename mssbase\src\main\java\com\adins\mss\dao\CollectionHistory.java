package com.adins.mss.dao;

import com.adins.mss.dao.DaoSession;
import de.greenrobot.dao.DaoException;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "TR_COLLECTIONHISTORY".
 */
public class CollectionHistory {

    /** Not-null value. */
     @SerializedName("uuid_collection_history")
    private String uuid_collection_history;
     @SerializedName("last_update")
    private java.util.Date last_update;
     @SerializedName("description")
    private String description;
     @SerializedName("usr_crt")
    private String usr_crt;
     @SerializedName("dtm_crt")
    private java.util.Date dtm_crt;
     @SerializedName("uuid_user")
    private String uuid_user;

    /** Used to resolve relations */
    private transient DaoSession daoSession;

    /** Used for active entity operations. */
    private transient CollectionHistoryDao myDao;

    private User user;
    private String user__resolvedKey;


    public CollectionHistory() {
    }

    public CollectionHistory(String uuid_collection_history) {
        this.uuid_collection_history = uuid_collection_history;
    }

    public CollectionHistory(String uuid_collection_history, java.util.Date last_update, String description, String usr_crt, java.util.Date dtm_crt, String uuid_user) {
        this.uuid_collection_history = uuid_collection_history;
        this.last_update = last_update;
        this.description = description;
        this.usr_crt = usr_crt;
        this.dtm_crt = dtm_crt;
        this.uuid_user = uuid_user;
    }

    /** called by internal mechanisms, do not call yourself. */
    public void __setDaoSession(DaoSession daoSession) {
        this.daoSession = daoSession;
        myDao = daoSession != null ? daoSession.getCollectionHistoryDao() : null;
    }

    /** Not-null value. */
    public String getUuid_collection_history() {
        return uuid_collection_history;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_collection_history(String uuid_collection_history) {
        this.uuid_collection_history = uuid_collection_history;
    }

    public java.util.Date getLast_update() {
        return last_update;
    }

    public void setLast_update(java.util.Date last_update) {
        this.last_update = last_update;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getUsr_crt() {
        return usr_crt;
    }

    public void setUsr_crt(String usr_crt) {
        this.usr_crt = usr_crt;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public String getUuid_user() {
        return uuid_user;
    }

    public void setUuid_user(String uuid_user) {
        this.uuid_user = uuid_user;
    }

    /** To-one relationship, resolved on first access. */
    public User getUser() {
        String __key = this.uuid_user;
        if (user__resolvedKey == null || user__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            UserDao targetDao = daoSession.getUserDao();
            User userNew = targetDao.load(__key);
            synchronized (this) {
                user = userNew;
            	user__resolvedKey = __key;
            }
        }
        return user;
    }

    public void setUser(User user) {
        synchronized (this) {
            this.user = user;
            uuid_user = user == null ? null : user.getUuid_user();
            user__resolvedKey = uuid_user;
        }
    }

    /** Convenient call for {@link AbstractDao#delete(Object)}. Entity must attached to an entity context. */
    public void delete() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.delete(this);
    }

    /** Convenient call for {@link AbstractDao#update(Object)}. Entity must attached to an entity context. */
    public void update() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.update(this);
    }

    /** Convenient call for {@link AbstractDao#refresh(Object)}. Entity must attached to an entity context. */
    public void refresh() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.refresh(this);
    }

}
