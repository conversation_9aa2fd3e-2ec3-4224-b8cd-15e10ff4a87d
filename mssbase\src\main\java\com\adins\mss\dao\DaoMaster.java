package com.adins.mss.dao;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteDatabase.CursorFactory;
import android.util.Log;

import de.greenrobot.dao.AbstractDaoMaster;
import de.greenrobot.dao.database.StandardDatabase;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.EncryptedDatabaseOpenHelper;
import de.greenrobot.dao.database.DatabaseOpenHelper;
import de.greenrobot.dao.identityscope.IdentityScopeType;

import com.adins.mss.constant.Global;
import com.adins.mss.dao.CollectionActivityDao;
import com.adins.mss.dao.InstallmentScheduleDao;
import com.adins.mss.dao.GeneralParameterDao;
import com.adins.mss.dao.GroupUserDao;
import com.adins.mss.dao.LookupDao;
import com.adins.mss.dao.SyncDao;
import com.adins.mss.dao.MigrationDao;
import com.adins.mss.dao.RuleDao;
import com.adins.mss.dao.BlacklistDao;
import com.adins.mss.dao.ProductOfferingDao;
import com.adins.mss.dao.PODealerDao;
import com.adins.mss.dao.AssetSchemeDao;
import com.adins.mss.dao.POAssetDao;
import com.adins.mss.dao.IndustryDao;
import com.adins.mss.dao.DecisionDao;
import com.adins.mss.dao.MarketPriceDao;
import com.adins.mss.dao.MenuDao;
import com.adins.mss.dao.PrintItemDao;
import com.adins.mss.dao.QuestionSetDao;
import com.adins.mss.dao.SchemeDao;
import com.adins.mss.dao.TimelineTypeDao;
import com.adins.mss.dao.UserDao;
import com.adins.mss.dao.PushSyncDao;
import com.adins.mss.dao.LoggerDao;
import com.adins.mss.dao.CollectionHistoryDao;
import com.adins.mss.dao.CommentDao;
import com.adins.mss.dao.DepositReportDDao;
import com.adins.mss.dao.DepositReportHDao;
import com.adins.mss.dao.ImageResultDao;
import com.adins.mss.dao.LocationInfoDao;
import com.adins.mss.dao.MessageDao;
import com.adins.mss.dao.PaymentHistoryDDao;
import com.adins.mss.dao.PaymentHistoryHDao;
import com.adins.mss.dao.PrintResultDao;
import com.adins.mss.dao.ReceiptVoucherDao;
import com.adins.mss.dao.TaskDDao;
import com.adins.mss.dao.TaskHDao;
import com.adins.mss.dao.TaskHSequenceDao;
import com.adins.mss.dao.TaskUpdateDao;
import com.adins.mss.dao.TimelineDao;
import com.adins.mss.dao.MobileContentDDao;
import com.adins.mss.dao.MobileContentHDao;
import com.adins.mss.dao.HolidayDao;
import com.adins.mss.dao.PrintDateDao;
import com.adins.mss.dao.ErrorLogDao;
import com.adins.mss.dao.mobiledatafileDao;
import com.adins.mss.dao.ReminderPoDao;
import com.adins.mss.dao.EmbeddedInfoDao;
import com.adins.mss.migration.MigrationV18toV19;
import com.adins.mss.migration.MigrationV19toV20;
import com.adins.mss.migration.MigrationV20toV21;
import com.adins.mss.migration.MigrationV21toV22;
import com.adins.mss.migration.MigrationV22toV23;
import com.adins.mss.migration.MigrationV23toV24;
import com.adins.mss.migration.MigrationV24toV25;
import com.adins.mss.migration.MigrationV25toV26;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * Master of DAO (schema version 26): knows all DAOs.
*/
public class DaoMaster extends AbstractDaoMaster {
    public static final int SCHEMA_VERSION = 26;

    /** Creates underlying database table using DAOs. */
    public static void createAllTables(Database db, boolean ifNotExists) {
        CollectionActivityDao.createTable(db, ifNotExists);
        InstallmentScheduleDao.createTable(db, ifNotExists);
        GeneralParameterDao.createTable(db, ifNotExists);
        GroupUserDao.createTable(db, ifNotExists);
        LookupDao.createTable(db, ifNotExists);
        SyncDao.createTable(db, ifNotExists);
        MigrationDao.createTable(db, ifNotExists);
        RuleDao.createTable(db, ifNotExists);
        BlacklistDao.createTable(db, ifNotExists);
        ProductOfferingDao.createTable(db, ifNotExists);
        PODealerDao.createTable(db, ifNotExists);
        AssetSchemeDao.createTable(db, ifNotExists);
        POAssetDao.createTable(db, ifNotExists);
        IndustryDao.createTable(db, ifNotExists);
        DecisionDao.createTable(db, ifNotExists);
        MarketPriceDao.createTable(db, ifNotExists);
        MenuDao.createTable(db, ifNotExists);
        PrintItemDao.createTable(db, ifNotExists);
        QuestionSetDao.createTable(db, ifNotExists);
        SchemeDao.createTable(db, ifNotExists);
        TimelineTypeDao.createTable(db, ifNotExists);
        UserDao.createTable(db, ifNotExists);
        PushSyncDao.createTable(db, ifNotExists);
        LoggerDao.createTable(db, ifNotExists);
        CollectionHistoryDao.createTable(db, ifNotExists);
        CommentDao.createTable(db, ifNotExists);
        DepositReportDDao.createTable(db, ifNotExists);
        DepositReportHDao.createTable(db, ifNotExists);
        ImageResultDao.createTable(db, ifNotExists);
        LocationInfoDao.createTable(db, ifNotExists);
        MessageDao.createTable(db, ifNotExists);
        PaymentHistoryDDao.createTable(db, ifNotExists);
        PaymentHistoryHDao.createTable(db, ifNotExists);
        PrintResultDao.createTable(db, ifNotExists);
        ReceiptVoucherDao.createTable(db, ifNotExists);
        TaskDDao.createTable(db, ifNotExists);
        TaskHDao.createTable(db, ifNotExists);
        TaskHSequenceDao.createTable(db, ifNotExists);
        TaskUpdateDao.createTable(db, ifNotExists);
        TimelineDao.createTable(db, ifNotExists);
        MobileContentDDao.createTable(db, ifNotExists);
        MobileContentHDao.createTable(db, ifNotExists);
        HolidayDao.createTable(db, ifNotExists);
        PrintDateDao.createTable(db, ifNotExists);
        ErrorLogDao.createTable(db, ifNotExists);
        mobiledatafileDao.createTable(db, ifNotExists);
        ReminderPoDao.createTable(db, ifNotExists);
        EmbeddedInfoDao.createTable(db, ifNotExists);
    }
    
    /** Drops underlying database table using DAOs. */
    public static void dropAllTables(Database db, boolean ifExists) {
        CollectionActivityDao.dropTable(db, ifExists);
        InstallmentScheduleDao.dropTable(db, ifExists);
        GeneralParameterDao.dropTable(db, ifExists);
        GroupUserDao.dropTable(db, ifExists);
        LookupDao.dropTable(db, ifExists);
        SyncDao.dropTable(db, ifExists);
        MigrationDao.dropTable(db, ifExists);
        RuleDao.dropTable(db, ifExists);
        BlacklistDao.dropTable(db, ifExists);
        ProductOfferingDao.dropTable(db, ifExists);
        PODealerDao.dropTable(db, ifExists);
        AssetSchemeDao.dropTable(db, ifExists);
        POAssetDao.dropTable(db, ifExists);
        IndustryDao.dropTable(db, ifExists);
        DecisionDao.dropTable(db, ifExists);
        MarketPriceDao.dropTable(db, ifExists);
        MenuDao.dropTable(db, ifExists);
        PrintItemDao.dropTable(db, ifExists);
        QuestionSetDao.dropTable(db, ifExists);
        SchemeDao.dropTable(db, ifExists);
        TimelineTypeDao.dropTable(db, ifExists);
        UserDao.dropTable(db, ifExists);
        PushSyncDao.dropTable(db, ifExists);
        LoggerDao.dropTable(db, ifExists);
        CollectionHistoryDao.dropTable(db, ifExists);
        CommentDao.dropTable(db, ifExists);
        DepositReportDDao.dropTable(db, ifExists);
        DepositReportHDao.dropTable(db, ifExists);
        ImageResultDao.dropTable(db, ifExists);
        LocationInfoDao.dropTable(db, ifExists);
        MessageDao.dropTable(db, ifExists);
        PaymentHistoryDDao.dropTable(db, ifExists);
        PaymentHistoryHDao.dropTable(db, ifExists);
        PrintResultDao.dropTable(db, ifExists);
        ReceiptVoucherDao.dropTable(db, ifExists);
        TaskDDao.dropTable(db, ifExists);
        TaskHDao.dropTable(db, ifExists);
        TaskHSequenceDao.dropTable(db, ifExists);
        TaskUpdateDao.dropTable(db, ifExists);
        TimelineDao.dropTable(db, ifExists);
        MobileContentDDao.dropTable(db, ifExists);
        MobileContentHDao.dropTable(db, ifExists);
        HolidayDao.dropTable(db, ifExists);
        PrintDateDao.dropTable(db, ifExists);
        ErrorLogDao.dropTable(db, ifExists);
        mobiledatafileDao.dropTable(db, ifExists);
        ReminderPoDao.dropTable(db, ifExists);
        EmbeddedInfoDao.dropTable(db, ifExists);
    }
    
    public static abstract class OpenHelper extends DatabaseOpenHelper {
        public OpenHelper(Context context, String name) {
            super(context, name, SCHEMA_VERSION);
        }
        public OpenHelper(Context context, String name, CursorFactory factory) {
            super(context, name, factory, SCHEMA_VERSION);
        }

        @Override
        public void onCreate(Database db) {
            Log.i("greenDAO", "Creating tables for schema version " + SCHEMA_VERSION);
            createAllTables(db, false);
        }
    }
    
    /** WARNING: Drops all table on Upgrade! Use only during development. */
    public static class DevOpenHelper extends OpenHelper {
        public DevOpenHelper(Context context, String name) {
            super(context, name);
        }

        @Override
        public void onUpgrade(Database db, int oldVersion, int newVersion) {
            Log.i("greenDAO", "Upgrading schema from version " + oldVersion + " to " + newVersion + " by dropping all tables");

            if(oldVersion < 13){
                //am_msuser
                db.execSQL("ALTER TABLE MS_USER ADD IS_PILOTING TEXT");
            } else if (oldVersion < 14){
                PushSyncDao.createTable(db, true);
                db.execSQL("ALTER TABLE MS_USER ADD PUSHSYNC_TIME TEXT");
                db.execSQL("ALTER TABLE TR_TASK_D ADD HAS_DEFAULT_IMAGE TEXT");
            } else if (oldVersion < 15) {
                TaskUpdateDao.createTable(db, true);
                db.execSQL("ALTER TABLE TR_TASK_H ADD UUID_TASK_UPDATE TEXT");
                db.execSQL("ALTER TABLE TR_TASK_H ADD PENDING_NOTES TEXT");
                db.execSQL("ALTER TABLE TR_TASK_H ADD DOCUPRO_FEEDBACK TEXT");
                db.execSQL("ALTER TABLE TR_TASK_H ADD STATUS_APPLICATION TEXT");
                db.execSQL("ALTER TABLE TR_TASK_H ADD IS_SENT_CONFINS TEXT");
                db.execSQL("ALTER TABLE TR_TASK_H ADD IS_ALREADY_NOTIFIED TEXT");
                db.execSQL("ALTER TABLE TR_TASK_D ADD IS_RESURVEY TEXT");
            } else if (oldVersion < 16) {
                db.execSQL("ALTER TABLE MS_MARKET_PRICE ADD OFFICE_CODE TEXT");
            } else if (oldVersion < 17) {
                db.execSQL("ALTER TABLE MS_USER ADD IS_PILOTING_CAE TEXT");
                db.execSQL("ALTER TABLE TR_TASK_H ADD KELURAHAN TEXT");
                db.execSQL("ALTER TABLE TR_TASK_H ADD STATUS_FOLLOWUP TEXT");
                db.execSQL("ALTER TABLE MS_USER ADD BRANCH_TYPE TEXT");
            } else if (oldVersion < 18) {
                db.execSQL("ALTER TABLE TR_TASK_H ADD IS_REVISIT TEXT");
                db.execSQL("ALTER TABLE TR_TASK_H ADD VISIT_TYPE TEXT");
            }
            MigrationV18toV19.execute(db, oldVersion);
            MigrationV19toV20.execute(db, oldVersion);
            MigrationV20toV21.execute(db, oldVersion);
            MigrationV21toV22.execute(db, oldVersion);
            MigrationV22toV23.execute(db, oldVersion);
            MigrationV23toV24.execute(db, oldVersion);
            MigrationV24toV25.execute(db, oldVersion);
            MigrationV25toV26.execute(db, oldVersion);

            if(Global.isLoggedIn) {
                Global.isForceLogout = true;
            }
        }
    }

    public static abstract class EncryptedOpenHelper extends EncryptedDatabaseOpenHelper {
        public EncryptedOpenHelper(Context context, String name) {
            super(context, name, SCHEMA_VERSION);
        }

        public EncryptedOpenHelper(Context context, String name, Object cursorFactory, boolean loadNativeLibs) {
            super(context, name, cursorFactory, SCHEMA_VERSION, loadNativeLibs);
        }

        @Override
        public void onCreate(Database db) {
            Log.i("greenDAO", "Creating tables for schema version " + SCHEMA_VERSION);
            createAllTables(db, false);
        }
    }

    /** WARNING: Drops all table on Upgrade! Use only during development. */
    public static class EncryptedDevOpenHelper extends EncryptedOpenHelper {
        public EncryptedDevOpenHelper(Context context, String name) {
            super(context, name);
        }

        public EncryptedDevOpenHelper(Context context, String name, Object cursorFactory, boolean loadNativeLibs) {
            super(context, name, cursorFactory, loadNativeLibs);
        }

        @Override
        public void onUpgrade(Database db, int oldVersion, int newVersion) {
            Log.i("greenDAO", "Upgrading schema from version " + oldVersion + " to " + newVersion + " by dropping all tables");

            if(oldVersion < 13){
                //am_msuser
                db.execSQL("ALTER TABLE MS_USER ADD IS_PILOTING TEXT");
            } else if (oldVersion < 14){
                PushSyncDao.createTable(db, true);
                db.execSQL("ALTER TABLE MS_USER ADD PUSHSYNC_TIME TEXT");
                db.execSQL("ALTER TABLE TR_TASK_D ADD HAS_DEFAULT_IMAGE TEXT");
            } else if (oldVersion < 15) {
                TaskUpdateDao.createTable(db, true);
                db.execSQL("ALTER TABLE TR_TASK_H ADD UUID_TASK_UPDATE TEXT");
                db.execSQL("ALTER TABLE TR_TASK_H ADD PENDING_NOTES TEXT");
                db.execSQL("ALTER TABLE TR_TASK_H ADD DOCUPRO_FEEDBACK TEXT");
                db.execSQL("ALTER TABLE TR_TASK_H ADD STATUS_APPLICATION TEXT");
                db.execSQL("ALTER TABLE TR_TASK_H ADD IS_SENT_CONFINS TEXT");
                db.execSQL("ALTER TABLE TR_TASK_H ADD IS_ALREADY_NOTIFIED TEXT");
                db.execSQL("ALTER TABLE TR_TASK_D ADD IS_RESURVEY TEXT");
            } else if (oldVersion < 16) {
                db.execSQL("ALTER TABLE MS_MARKET_PRICE ADD OFFICE_CODE TEXT");
            } else if (oldVersion < 17) {
                db.execSQL("ALTER TABLE MS_USER ADD IS_PILOTING_CAE TEXT");
                db.execSQL("ALTER TABLE TR_TASK_H ADD KELURAHAN TEXT");
                db.execSQL("ALTER TABLE TR_TASK_H ADD STATUS_FOLLOWUP TEXT");
                db.execSQL("ALTER TABLE MS_USER ADD BRANCH_TYPE TEXT");
            } else if (oldVersion < 18) {
                db.execSQL("ALTER TABLE TR_TASK_H ADD IS_REVISIT TEXT");
                db.execSQL("ALTER TABLE TR_TASK_H ADD VISIT_TYPE TEXT");
            }
            MigrationV18toV19.execute(db, oldVersion);
            MigrationV19toV20.execute(db, oldVersion);
            MigrationV20toV21.execute(db, oldVersion);
            MigrationV21toV22.execute(db, oldVersion);
            MigrationV22toV23.execute(db, oldVersion);
            MigrationV23toV24.execute(db, oldVersion);
            MigrationV24toV25.execute(db, oldVersion);
            MigrationV25toV26.execute(db, oldVersion);

            if(Global.isLoggedIn) {
                Global.isForceLogout = true;
            }
        }
    }

    public DaoMaster(SQLiteDatabase db) {
        this(new StandardDatabase(db));
    }

    public DaoMaster(Database db) {
        super(db, SCHEMA_VERSION);
        registerDaoClass(CollectionActivityDao.class);
        registerDaoClass(InstallmentScheduleDao.class);
        registerDaoClass(GeneralParameterDao.class);
        registerDaoClass(GroupUserDao.class);
        registerDaoClass(LookupDao.class);
        registerDaoClass(SyncDao.class);
        registerDaoClass(MigrationDao.class);
        registerDaoClass(RuleDao.class);
        registerDaoClass(BlacklistDao.class);
        registerDaoClass(ProductOfferingDao.class);
        registerDaoClass(PODealerDao.class);
        registerDaoClass(AssetSchemeDao.class);
        registerDaoClass(POAssetDao.class);
        registerDaoClass(IndustryDao.class);
        registerDaoClass(DecisionDao.class);
        registerDaoClass(MarketPriceDao.class);
        registerDaoClass(MenuDao.class);
        registerDaoClass(PrintItemDao.class);
        registerDaoClass(QuestionSetDao.class);
        registerDaoClass(SchemeDao.class);
        registerDaoClass(TimelineTypeDao.class);
        registerDaoClass(UserDao.class);
        registerDaoClass(PushSyncDao.class);
        registerDaoClass(LoggerDao.class);
        registerDaoClass(CollectionHistoryDao.class);
        registerDaoClass(CommentDao.class);
        registerDaoClass(DepositReportDDao.class);
        registerDaoClass(DepositReportHDao.class);
        registerDaoClass(ImageResultDao.class);
        registerDaoClass(LocationInfoDao.class);
        registerDaoClass(MessageDao.class);
        registerDaoClass(PaymentHistoryDDao.class);
        registerDaoClass(PaymentHistoryHDao.class);
        registerDaoClass(PrintResultDao.class);
        registerDaoClass(ReceiptVoucherDao.class);
        registerDaoClass(TaskDDao.class);
        registerDaoClass(TaskHDao.class);
        registerDaoClass(TaskHSequenceDao.class);
        registerDaoClass(TaskUpdateDao.class);
        registerDaoClass(TimelineDao.class);
        registerDaoClass(MobileContentDDao.class);
        registerDaoClass(MobileContentHDao.class);
        registerDaoClass(HolidayDao.class);
        registerDaoClass(PrintDateDao.class);
        registerDaoClass(ErrorLogDao.class);
        registerDaoClass(mobiledatafileDao.class);
        registerDaoClass(ReminderPoDao.class);
        registerDaoClass(EmbeddedInfoDao.class);
    }
    
    public DaoSession newSession() {
        return new DaoSession(db, IdentityScopeType.Session, daoConfigMap);
    }
    
    public DaoSession newSession(IdentityScopeType type) {
        return new DaoSession(db, type, daoConfigMap);
    }
    
}
