package com.adins.mss.dao;

import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;

import com.adins.mss.dao.Rule;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "MS_RULES".
*/
public class RuleDao extends AbstractDao<Rule, Long> {

    public static final String TABLENAME = "MS_RULES";

    /**
     * Properties of entity Rule.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Id = new Property(0, long.class, "id", true, "ID");
        public final static Property Is_deleted = new Property(1, int.class, "is_deleted", false, "IS_DELETED");
        public final static Property Rule_name = new Property(2, String.class, "rule_name", false, "RULE_NAME");
        public final static Property Url = new Property(3, String.class, "url", false, "URL");
        public final static Property Dtm_upd = new Property(4, java.util.Date.class, "dtm_upd", false, "DTM_UPD");
    };


    public RuleDao(DaoConfig config) {
        super(config);
    }
    
    public RuleDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"MS_RULES\" (" + //
                "\"ID\" INTEGER PRIMARY KEY NOT NULL ," + // 0: id
                "\"IS_DELETED\" INTEGER NOT NULL ," + // 1: is_deleted
                "\"RULE_NAME\" TEXT NOT NULL ," + // 2: rule_name
                "\"URL\" TEXT NOT NULL ," + // 3: url
                "\"DTM_UPD\" INTEGER);"); // 4: dtm_upd
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"MS_RULES\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, Rule entity) {
        stmt.clearBindings();
        stmt.bindLong(1, entity.getId());
        stmt.bindLong(2, entity.getIs_deleted());
        stmt.bindString(3, entity.getRule_name());
        stmt.bindString(4, entity.getUrl());
 
        java.util.Date dtm_upd = entity.getDtm_upd();
        if (dtm_upd != null) {
            stmt.bindLong(5, dtm_upd.getTime());
        }
    }

    /** @inheritdoc */
    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.getLong(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public Rule readEntity(Cursor cursor, int offset) {
        Rule entity = new Rule( //
            cursor.getLong(offset + 0), // id
            cursor.getInt(offset + 1), // is_deleted
            cursor.getString(offset + 2), // rule_name
            cursor.getString(offset + 3), // url
            cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)) // dtm_upd
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, Rule entity, int offset) {
        entity.setId(cursor.getLong(offset + 0));
        entity.setIs_deleted(cursor.getInt(offset + 1));
        entity.setRule_name(cursor.getString(offset + 2));
        entity.setUrl(cursor.getString(offset + 3));
        entity.setDtm_upd(cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)));
     }
    
    /** @inheritdoc */
    @Override
    protected Long updateKeyAfterInsert(Rule entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    /** @inheritdoc */
    @Override
    public Long getKey(Rule entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
}
