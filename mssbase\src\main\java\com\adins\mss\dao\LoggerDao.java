package com.adins.mss.dao;

import java.util.List;
import java.util.ArrayList;
import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.SqlUtils;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;
import de.greenrobot.dao.query.Query;
import de.greenrobot.dao.query.QueryBuilder;

import com.adins.mss.dao.Logger;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TR_APPLICATION_LOG".
*/
public class LoggerDao extends AbstractDao<Logger, String> {

    public static final String TABLENAME = "TR_APPLICATION_LOG";

    /**
     * Properties of entity Logger.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_log = new Property(0, String.class, "uuid_log", true, "UUID_LOG");
        public final static Property Screen = new Property(1, String.class, "screen", false, "SCREEN");
        public final static Property Timestamp = new Property(2, java.util.Date.class, "timestamp", false, "TIMESTAMP");
        public final static Property Detail = new Property(3, String.class, "detail", false, "DETAIL");
        public final static Property Uuid_user = new Property(4, String.class, "uuid_user", false, "UUID_USER");
    };

    private DaoSession daoSession;

    private Query<Logger> user_LoggerListQuery;

    public LoggerDao(DaoConfig config) {
        super(config);
    }
    
    public LoggerDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TR_APPLICATION_LOG\" (" + //
                "\"UUID_LOG\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_log
                "\"SCREEN\" TEXT," + // 1: screen
                "\"TIMESTAMP\" INTEGER," + // 2: timestamp
                "\"DETAIL\" TEXT," + // 3: detail
                "\"UUID_USER\" TEXT);"); // 4: uuid_user
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TR_APPLICATION_LOG\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, Logger entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_log());
 
        String screen = entity.getScreen();
        if (screen != null) {
            stmt.bindString(2, screen);
        }
 
        java.util.Date timestamp = entity.getTimestamp();
        if (timestamp != null) {
            stmt.bindLong(3, timestamp.getTime());
        }
 
        String detail = entity.getDetail();
        if (detail != null) {
            stmt.bindString(4, detail);
        }
 
        String uuid_user = entity.getUuid_user();
        if (uuid_user != null) {
            stmt.bindString(5, uuid_user);
        }
    }

    @Override
    protected void attachEntity(Logger entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public Logger readEntity(Cursor cursor, int offset) {
        Logger entity = new Logger( //
            cursor.getString(offset + 0), // uuid_log
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // screen
            cursor.isNull(offset + 2) ? null : new java.util.Date(cursor.getLong(offset + 2)), // timestamp
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // detail
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4) // uuid_user
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, Logger entity, int offset) {
        entity.setUuid_log(cursor.getString(offset + 0));
        entity.setScreen(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setTimestamp(cursor.isNull(offset + 2) ? null : new java.util.Date(cursor.getLong(offset + 2)));
        entity.setDetail(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setUuid_user(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(Logger entity, long rowId) {
        return entity.getUuid_log();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(Logger entity) {
        if(entity != null) {
            return entity.getUuid_log();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
    /** Internal query to resolve the "loggerList" to-many relationship of User. */
    public List<Logger> _queryUser_LoggerList(String uuid_user) {
        synchronized (this) {
            if (user_LoggerListQuery == null) {
                QueryBuilder<Logger> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_user.eq(null));
                user_LoggerListQuery = queryBuilder.build();
            }
        }
        Query<Logger> query = user_LoggerListQuery.forCurrentThread();
        query.setParameter(0, uuid_user);
        return query.list();
    }

    private String selectDeep;

    protected String getSelectDeep() {
        if (selectDeep == null) {
            StringBuilder builder = new StringBuilder("SELECT ");
            SqlUtils.appendColumns(builder, "T", getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T0", daoSession.getUserDao().getAllColumns());
            builder.append(" FROM TR_APPLICATION_LOG T");
            builder.append(" LEFT JOIN MS_USER T0 ON T.\"UUID_USER\"=T0.\"UUID_USER\"");
            builder.append(' ');
            selectDeep = builder.toString();
        }
        return selectDeep;
    }
    
    protected Logger loadCurrentDeep(Cursor cursor, boolean lock) {
        Logger entity = loadCurrent(cursor, 0, lock);
        int offset = getAllColumns().length;

        User user = loadCurrentOther(daoSession.getUserDao(), cursor, offset);
        entity.setUser(user);

        return entity;    
    }

    public Logger loadDeep(Long key) {
        assertSinglePk();
        if (key == null) {
            return null;
        }

        StringBuilder builder = new StringBuilder(getSelectDeep());
        builder.append("WHERE ");
        SqlUtils.appendColumnsEqValue(builder, "T", getPkColumns());
        String sql = builder.toString();
        
        String[] keyArray = new String[] { key.toString() };
        Cursor cursor = db.rawQuery(sql, keyArray);
        
        try {
            boolean available = cursor.moveToFirst();
            if (!available) {
                return null;
            } else if (!cursor.isLast()) {
                throw new IllegalStateException("Expected unique result, but count was " + cursor.getCount());
            }
            return loadCurrentDeep(cursor, true);
        } finally {
            cursor.close();
        }
    }
    
    /** Reads all available rows from the given cursor and returns a list of new ImageTO objects. */
    public List<Logger> loadAllDeepFromCursor(Cursor cursor) {
        int count = cursor.getCount();
        List<Logger> list = new ArrayList<Logger>(count);
        
        if (cursor.moveToFirst()) {
            if (identityScope != null) {
                identityScope.lock();
                identityScope.reserveRoom(count);
            }
            try {
                do {
                    list.add(loadCurrentDeep(cursor, false));
                } while (cursor.moveToNext());
            } finally {
                if (identityScope != null) {
                    identityScope.unlock();
                }
            }
        }
        return list;
    }
    
    protected List<Logger> loadDeepAllAndCloseCursor(Cursor cursor) {
        try {
            return loadAllDeepFromCursor(cursor);
        } finally {
            cursor.close();
        }
    }
    

    /** A raw-style query where you can pass any WHERE clause and arguments. */
    public List<Logger> queryDeep(String where, String... selectionArg) {
        Cursor cursor = db.rawQuery(getSelectDeep() + where, selectionArg);
        return loadDeepAllAndCloseCursor(cursor);
    }
 
}
