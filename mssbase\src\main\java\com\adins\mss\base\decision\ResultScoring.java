package com.adins.mss.base.decision;

public class ResultScoring {
	private float Score;
	private String Flag;
	private String ComponentName;
	private String Value;
	private String matrix;
	
	public float getScore() {
		return Score;
	}
	public void setScore(float score) {
		Score = score;
	}
	public String getFlag() {
		return Flag;
	}
	public void setFlag(String flag) {
		Flag = flag;
	}
	public String getComponentName() {
		return ComponentName;
	}
	public void setComponentName(String componentName) {
		ComponentName = componentName;
	}
	public String getValue() {
		return Value;
	}
	public void setValue(String value) {
		Value = value;
	}

	public void AddScoreResult(String matrix) {
		this.matrix = matrix;
	}
	public String getMatrix() {
		return matrix;
	}

	public void reset() {
		this.Score = new Float("0.0");
		this.Flag = null;
		this.ComponentName = null;
		this.Value = null;
	}
}
