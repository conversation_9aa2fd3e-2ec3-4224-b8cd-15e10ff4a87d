package com.adins.mss.base.getotr;

import android.content.Context;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.http.MssResponseType;

public class GetOTRApi {

    private Context context;

    public GetOTRApi(Context context) {
        this.context = context;
    }

    public JsonResponseGetOTR request(JsonRequestGetOTR jsonRequestGetOTR) {
        String url = GlobalData.getSharedGlobalData().getURL_GET_OTR();
        String requestJson = GsonHelper.toJson(jsonRequestGetOTR);

        boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
        boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
        HttpCryptedConnection httpCryptedConnection = new HttpCryptedConnection(context, encrypt, decrypt);
        HttpConnectionResult serverResult = null;
        try {
            serverResult = httpCryptedConnection.requestToServer(url, requestJson, Global.SORTCONNECTIONTIMEOUT);
        } catch (Exception e) {
            e.printStackTrace();
        }

        String responseJson;
        JsonResponseGetOTR response = null;
        if (null != serverResult && serverResult.isOK()) {
            try {
                responseJson = serverResult.getResult();
                response = GsonHelper.fromJson(responseJson, JsonResponseGetOTR.class);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            response = new JsonResponseGetOTR();
            JsonResponseGetOTR.Status status = new JsonResponseGetOTR.Status();
            status.setMessage(serverResult.getResult());
            status.setCode(serverResult.getStatusCode());
            response.setStatus(status);
        }
        return response;
    }
}
