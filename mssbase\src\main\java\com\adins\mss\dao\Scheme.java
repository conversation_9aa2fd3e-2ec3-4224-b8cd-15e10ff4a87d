package com.adins.mss.dao;

import java.util.List;
import com.adins.mss.dao.DaoSession;
import de.greenrobot.dao.DaoException;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "MS_SCHEME".
 */
public class Scheme {

    /** Not-null value. */
     @SerializedName("uuid_scheme")
    private String uuid_scheme;
     @SerializedName("scheme_description")
    private String scheme_description;
     @SerializedName("scheme_last_update")
    private java.util.Date scheme_last_update;
     @SerializedName("is_printable")
    private String is_printable;
     @SerializedName("form_id")
    private String form_id;
     @SerializedName("usr_crt")
    private String usr_crt;
     @SerializedName("is_preview_server")
    private String is_preview_server;
     @SerializedName("dtm_crt")
    private java.util.Date dtm_crt;
     @SerializedName("usr_upd")
    private String usr_upd;
     @SerializedName("dtm_upd")
    private java.util.Date dtm_upd;
     @SerializedName("form_type")
    private String form_type;
     @SerializedName("is_active")
    private String is_active;
     @SerializedName("form_version")
    private String form_version;

    /** Used to resolve relations */
    private transient DaoSession daoSession;

    /** Used for active entity operations. */
    private transient SchemeDao myDao;

    private List<PrintItem> printItemList;
    private List<QuestionSet> questionSetList;
    private List<TaskH> taskHList;

    public Scheme() {
    }

    public Scheme(String uuid_scheme) {
        this.uuid_scheme = uuid_scheme;
    }

    public Scheme(String uuid_scheme, String scheme_description, java.util.Date scheme_last_update, String is_printable, String form_id, String usr_crt, String is_preview_server, java.util.Date dtm_crt, String usr_upd, java.util.Date dtm_upd, String form_type, String is_active, String form_version) {
        this.uuid_scheme = uuid_scheme;
        this.scheme_description = scheme_description;
        this.scheme_last_update = scheme_last_update;
        this.is_printable = is_printable;
        this.form_id = form_id;
        this.usr_crt = usr_crt;
        this.is_preview_server = is_preview_server;
        this.dtm_crt = dtm_crt;
        this.usr_upd = usr_upd;
        this.dtm_upd = dtm_upd;
        this.form_type = form_type;
        this.is_active = is_active;
        this.form_version = form_version;
    }

    /** called by internal mechanisms, do not call yourself. */
    public void __setDaoSession(DaoSession daoSession) {
        this.daoSession = daoSession;
        myDao = daoSession != null ? daoSession.getSchemeDao() : null;
    }

    /** Not-null value. */
    public String getUuid_scheme() {
        return uuid_scheme;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_scheme(String uuid_scheme) {
        this.uuid_scheme = uuid_scheme;
    }

    public String getScheme_description() {
        return scheme_description;
    }

    public void setScheme_description(String scheme_description) {
        this.scheme_description = scheme_description;
    }

    public java.util.Date getScheme_last_update() {
        return scheme_last_update;
    }

    public void setScheme_last_update(java.util.Date scheme_last_update) {
        this.scheme_last_update = scheme_last_update;
    }

    public String getIs_printable() {
        return is_printable;
    }

    public void setIs_printable(String is_printable) {
        this.is_printable = is_printable;
    }

    public String getForm_id() {
        return form_id;
    }

    public void setForm_id(String form_id) {
        this.form_id = form_id;
    }

    public String getUsr_crt() {
        return usr_crt;
    }

    public void setUsr_crt(String usr_crt) {
        this.usr_crt = usr_crt;
    }

    public String getIs_preview_server() {
        return is_preview_server;
    }

    public void setIs_preview_server(String is_preview_server) {
        this.is_preview_server = is_preview_server;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public String getUsr_upd() {
        return usr_upd;
    }

    public void setUsr_upd(String usr_upd) {
        this.usr_upd = usr_upd;
    }

    public java.util.Date getDtm_upd() {
        return dtm_upd;
    }

    public void setDtm_upd(java.util.Date dtm_upd) {
        this.dtm_upd = dtm_upd;
    }

    public String getForm_type() {
        return form_type;
    }

    public void setForm_type(String form_type) {
        this.form_type = form_type;
    }

    public String getIs_active() {
        return is_active;
    }

    public void setIs_active(String is_active) {
        this.is_active = is_active;
    }

    public String getForm_version() {
        return form_version;
    }

    public void setForm_version(String form_version) {
        this.form_version = form_version;
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<PrintItem> getPrintItemList() {
        if (printItemList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            PrintItemDao targetDao = daoSession.getPrintItemDao();
            List<PrintItem> printItemListNew = targetDao._queryScheme_PrintItemList(uuid_scheme);
            synchronized (this) {
                if(printItemList == null) {
                    printItemList = printItemListNew;
                }
            }
        }
        return printItemList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetPrintItemList() {
        printItemList = null;
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<QuestionSet> getQuestionSetList() {
        if (questionSetList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            QuestionSetDao targetDao = daoSession.getQuestionSetDao();
            List<QuestionSet> questionSetListNew = targetDao._queryScheme_QuestionSetList(uuid_scheme);
            synchronized (this) {
                if(questionSetList == null) {
                    questionSetList = questionSetListNew;
                }
            }
        }
        return questionSetList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetQuestionSetList() {
        questionSetList = null;
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<TaskH> getTaskHList() {
        if (taskHList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            TaskHDao targetDao = daoSession.getTaskHDao();
            List<TaskH> taskHListNew = targetDao._queryScheme_TaskHList(uuid_scheme);
            synchronized (this) {
                if(taskHList == null) {
                    taskHList = taskHListNew;
                }
            }
        }
        return taskHList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetTaskHList() {
        taskHList = null;
    }

    /** Convenient call for {@link AbstractDao#delete(Object)}. Entity must attached to an entity context. */
    public void delete() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.delete(this);
    }

    /** Convenient call for {@link AbstractDao#update(Object)}. Entity must attached to an entity context. */
    public void update() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.update(this);
    }

    /** Convenient call for {@link AbstractDao#refresh(Object)}. Entity must attached to an entity context. */
    public void refresh() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.refresh(this);
    }

}
