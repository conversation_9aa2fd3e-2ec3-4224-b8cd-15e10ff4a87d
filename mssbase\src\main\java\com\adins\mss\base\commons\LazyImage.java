package com.adins.mss.base.commons;

import android.content.Context;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.foundation.http.MssRequestType;
import com.squareup.okhttp.Cache;
import com.squareup.okhttp.Interceptor;
import com.squareup.okhttp.MediaType;
import com.squareup.okhttp.OkHttpClient;
import com.squareup.okhttp.Request;
import com.squareup.okhttp.RequestBody;
import com.squareup.okhttp.Response;
import com.squareup.picasso.OkHttpDownloader;
import com.squareup.picasso.Picasso;

import java.io.IOException;

/**
 * Created by developer on 1/9/18.
 */

public class LazyImage {
    private static String baseUrl  = GlobalData.getSharedGlobalData().getUrlMain().replace("/m/", "/p/");
    private static String viewImage= "task/view/image/";
    private static String imageUrl = null;
    private Context context;

    public static LazyImage newInstance = new LazyImage();

    public LazyImage() {
    }

    private static LazyImage newInstance(Context context, String taskD) {
        return new LazyImage(context, taskD);
    }

    private LazyImage(Context context, String taskD) {
        this.context = context;
        imageUrl = baseUrl.concat(viewImage).concat(taskD);
    }

    private static class PicassoInterceptor implements Interceptor {
        private String url;

        public PicassoInterceptor(String imageUrl) {
            this.url = imageUrl;
        }

        @Override
        public Response intercept(Chain chain) throws IOException {
            final MediaType mType   = MediaType.parse("application/json; charset=utf-8");
            MssRequestType request  = new MssRequestType();
            request.setAudit(GlobalData.getSharedGlobalData().getAuditData());

            String json = GsonHelper.toJson(request);
            RequestBody body = RequestBody.create(mType, json);
            Request original = chain.request();
            Request.Builder requestBuilder = original.newBuilder()
                    .url(url)
                    .post(body);

            return chain.proceed(requestBuilder.build());
        }
    }

    public Picasso picasso(Context context, String taskD) {
        String imageUrl = baseUrl.concat(viewImage).concat(taskD);

        OkHttpClient okHttpClient  = new OkHttpClient();
        okHttpClient.interceptors().add(new PicassoInterceptor(imageUrl));

        try {
            okHttpClient.setCache(new Cache(context.getCacheDir(), 25 * 1024 * 1024));
        } catch (IOException e) {
            e.printStackTrace();
        }

        OkHttpDownloader downloader= new OkHttpDownloader(okHttpClient);

        return new Picasso.Builder(context)
                .downloader(downloader)
                .build();
    }
}
