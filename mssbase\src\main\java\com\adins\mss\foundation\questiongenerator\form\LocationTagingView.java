package com.adins.mss.foundation.questiongenerator.form;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.location.Address;
import android.location.Geocoder;
import android.os.AsyncTask;
import android.os.Bundle;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.fragment.app.FragmentActivity;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.checkin.CheckInManager;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.util.LocaleHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.GeneralParameter;
import com.adins.mss.dao.LocationInfo;
import com.adins.mss.foundation.camerainapp.helper.Logger;
import com.adins.mss.foundation.db.dataaccess.GeneralParameterDataAccess;
import com.adins.mss.foundation.location.LocationTrackingManager;
import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.GoogleApiAvailability;
import com.google.android.gms.maps.CameraUpdateFactory;
import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.OnMapReadyCallback;
import com.google.android.gms.maps.SupportMapFragment;
import com.google.android.gms.maps.model.BitmapDescriptorFactory;
import com.google.android.gms.maps.model.CircleOptions;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.MarkerOptions;

import java.io.IOException;
import java.util.List;
import java.util.Locale;

public class LocationTagingView extends FragmentActivity implements OnClickListener, OnMapReadyCallback {

    public static LocationInfo locationInfo;
    static GoogleMap mGoogleMap;
    private static CheckInManager manager;
    private static LatLng latLng;
    int REQUEST_CODE_RECOVER_PLAY_SERVICES = 1001;
    private ImageButton btnRefresh;
    private Button btnOk;
    public static String[] address = new String[2];

    public static void setNewLocation() {
        locationInfo = manager.getNewLocation();
        latLng = manager.getLatLng(locationInfo);
        mGoogleMap.clear();
        mGoogleMap.animateCamera(CameraUpdateFactory.newLatLngZoom(latLng, 16));
        MarkerOptions markerOptions = new MarkerOptions();
        markerOptions.position(latLng);
        markerOptions.icon(BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_GREEN));
        mGoogleMap.addMarker(markerOptions);
        int accuracy = locationInfo.getAccuracy();
        if (accuracy != 0) {
            CircleOptions circleOptions = new CircleOptions()
                    .center(latLng)
                    .radius(accuracy)
                    .fillColor(0x402196F3)
                    .strokeColor(Color.TRANSPARENT)
                    .strokeWidth(2);

            mGoogleMap.addCircle(circleOptions);
        }
//	    CheckInManager.stopPeriodicUpdates();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.location_tagging_layout);
        initialize();
    }


    @Override
    protected void attachBaseContext(Context newBase) {
        Context context = newBase;
        Locale locale;
        try {
            locale = new Locale(GlobalData.getSharedGlobalData().getLocale());
            if (null == locale) {
                locale = new Locale(LocaleHelper.ENGLSIH);
            }
            context = LocaleHelper.wrap(newBase, locale);
        } catch (Exception e) {
            locale = new Locale(LocaleHelper.ENGLSIH);
            context = LocaleHelper.wrap(newBase, locale);
        } finally {
            super.attachBaseContext(context);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (checkPlayServices()) {
            // Then we're good to go!
        }
    }

    private void initialize() {
        // TODO Auto-generated method stub
        btnRefresh = (ImageButton) findViewById(R.id.btnRefresh);
        btnRefresh.setOnClickListener(this);

        btnOk = (Button) findViewById(R.id.btnOK);
        btnOk.setOnClickListener(this);

        latLng = null;

        manager = new CheckInManager(this);

        if (manager != null) {
            locationInfo = manager.getLocationInfoCheckIn();
            latLng = manager.getLatLng(locationInfo);
            if (latLng == null) {
                latLng = new LatLng(0, 0);
            }
            if (getIntent().getBooleanExtra("isAddress", false)){
                String intentLatitude = getIntent().getStringExtra("latitude");
                String intentLongitude = getIntent().getStringExtra("longitude");
                if (intentLongitude!=null&&intentLatitude!=null) {
                    LocationTagingView.latLng = new LatLng(Double.parseDouble(intentLatitude),
                            Double.parseDouble(intentLongitude));
                    LocationTagingView.locationInfo.setLatitude(intentLatitude);
                    LocationTagingView.locationInfo.setLongitude(intentLongitude);
                }
            }
            if (latLng.latitude == 0) {
                /*subtitute toast with auto update location,
                updateLocationCheckin() is feature of refresh button on mapsviewer
                this is couse mGoogleApiClient isn't connect immediately after do mGoogleApiClient.connect
                that cause "Location not available"
                */
                //manager.updateLocationCheckin();
                Toast.makeText(getApplicationContext(), getResources().getString(R.string.location_not_available), Toast.LENGTH_SHORT).show();
            }
            SupportMapFragment mapFragment =
                    (SupportMapFragment) getSupportFragmentManager().findFragmentById(R.id.mapTagging);

            // Setting Google Map
            mapFragment.getMapAsync(this);
        }

//		initializeMaps();
    }

    private void initializeMaps() {

        try {
            if (latLng.latitude != 0 && latLng.longitude != 0) {
                mGoogleMap.animateCamera(CameraUpdateFactory.newLatLngZoom(latLng, 16));
//				mGoogleMap.animateCamera(CameraUpdateFactory.newCameraPosition(new CameraPosition(latLng,16,0,0)));
                MarkerOptions markerOptions = new MarkerOptions();
                markerOptions.position(latLng);
                markerOptions.icon(BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_GREEN));
                mGoogleMap.addMarker(markerOptions);
                int accuracy = locationInfo.getAccuracy();
                if (accuracy != 0) {
                    CircleOptions circleOptions = new CircleOptions()
                            .center(latLng)
                            .radius(accuracy)
                            .fillColor(0x402196F3)
                            .strokeColor(Color.TRANSPARENT)
                            .strokeWidth(2);
                    mGoogleMap.addCircle(circleOptions);
                }
                if (getIntent().getBooleanExtra("isAddress", false)) {

                    new GetAddressTask(this, latLng.latitude, latLng.longitude).execute();
                }
//				manager.updateLocationCheckin();
            }

            final Context context = getApplicationContext();

            /*Riska 2022-11-08 add setting on/off move marker*/
            boolean isMoveLocationEnabled = true;
            GeneralParameter gsMoveLocation = GeneralParameterDataAccess.getOne(this, GlobalData.getSharedGlobalData().getUser().getUuid_user(), Global.GS_MOVE_LOCATION);
            if (gsMoveLocation != null && "0".equalsIgnoreCase(gsMoveLocation.getGs_value())) {
                isMoveLocationEnabled = false;
            }

            if (isMoveLocationEnabled) {
                mGoogleMap.setOnMapClickListener(new GoogleMap.OnMapClickListener() {
                    @Override
                    public void onMapClick(LatLng latLng) {
                        if (latLng.latitude != 0 && latLng.longitude != 0) {
                            mGoogleMap.clear();
                            mGoogleMap.addMarker(new MarkerOptions().position(latLng).icon(BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_GREEN)));
                            LocationTagingView.latLng = latLng;
                            LocationTagingView.locationInfo.setLatitude(String.valueOf(latLng.latitude));
                            LocationTagingView.locationInfo.setLongitude(String.valueOf(latLng.longitude));
                            if (getIntent().getBooleanExtra("isAddress", false)) {
                                new GetAddressTask(context, latLng.latitude, latLng.longitude).execute();
                            }
                        }
                    }
                });
            }
            String application = GlobalData.getSharedGlobalData().getAuditData().getApplication();
            if (Global.APPLICATION_ORDER.equals(application)) {
                mGoogleMap.setOnMapLongClickListener(new GoogleMap.OnMapLongClickListener() {
                    @Override
                    public void onMapLongClick(LatLng location) {
                        if (latLng.latitude != 0 && latLng.longitude != 0) {
                            mGoogleMap.clear();
                            latLng = location;
                            locationInfo = manager.getLocationInfoCheckIn();
                            locationInfo.setLatitude(String.valueOf(latLng.latitude));
                            locationInfo.setLongitude(String.valueOf(latLng.longitude));
                            locationInfo.setAccuracy(10);

                            String titleMarker = LocationTrackingManager.toAnswerString_short(locationInfo);
                            mGoogleMap.animateCamera(CameraUpdateFactory.newLatLngZoom(latLng, 17));
                            MarkerOptions markerOptions = new MarkerOptions();
                            markerOptions.position(latLng);
                            markerOptions.title("Selected Location");
                            markerOptions.snippet(titleMarker);
                            markerOptions.icon(BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_GREEN));
                            mGoogleMap.addMarker(markerOptions);
                        }
                    }
                });
            }

        } catch (Exception e) {
            FireCrash.log(e);
        }
    }

    private boolean checkPlayServices() {
        int status = GoogleApiAvailability.getInstance().isGooglePlayServicesAvailable(this);
        if (status != ConnectionResult.SUCCESS) {
            if (GoogleApiAvailability.getInstance().isUserResolvableError(status)) {
                showErrorDialog(status);
            } else {
                Toast.makeText(this, getResources().getString(R.string.device_not_supported),
                        Toast.LENGTH_LONG).show();
            }
            return false;
        }
        return true;
    }

    void showErrorDialog(int code) {
        GoogleApiAvailability.getInstance().getErrorDialog(this, code,
                REQUEST_CODE_RECOVER_PLAY_SERVICES).show();
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.btnRefresh) {
            manager.updateLocationCheckin();
        } else if (id == R.id.btnOK) {
            if (latLng.latitude == 0 || latLng.longitude == 0) {
                Toast.makeText(getApplicationContext(), getResources().getString(R.string.location_not_available), Toast.LENGTH_SHORT).show();
            } else {
                Intent intent = new Intent();
                setResult(Activity.RESULT_OK, intent);
                finish();
            }
        }
    }

    @Override
    public void onMapReady(GoogleMap googleMap) {
        mGoogleMap = googleMap;
        initializeMaps();
    }


    private class GetAddressTask extends AsyncTask<Void, Void, String[]> {
        private Context mContext;
        private double mLatitude;
        private double mLongitude;

        public GetAddressTask(Context context, double latitude, double longitude) {
            mLatitude = latitude;
            mLongitude = longitude;
            mContext = context;
        }

        /**
         * Get a Geocoder instance, get the latitude and longitude look up the
         * address, and return it
         *
         * @return A string containing the address of the current location, or
         * an empty string if no address can be found, or an error
         * message
         * @params params One or more Location objects
         */
        @Override
        protected String[] doInBackground(Void... params) {
            Geocoder geocoder = new Geocoder(mContext, Locale.getDefault());
            // Get the current location from the input parameter list

            String[] addressString = new String[2];
            // Create a list to contain the result address
            List<Address> addresses = null;
            try {
                /*
                 * Return 1 address.
                 */
                addresses = geocoder.getFromLocation(mLatitude, mLongitude, 1);
            } catch (IOException e1) {
                if (Global.IS_DEV) {
                    Logger.e("LocationSampleActivity",
                            "IO Exception in getFromLocation()");
                    e1.printStackTrace();
                }
                addressString[0] = mContext.getResources().getString(R.string.address_not_found);
                addressString[1] = mContext.getResources().getString(R.string.address_not_found);
                return addressString;
            } catch (IllegalArgumentException e2) {
                // Error message to post in the log
                addressString[0] = mContext.getResources().getString(R.string.address_not_found);
                addressString[1] = mContext.getResources().getString(R.string.address_not_found);
                if (Global.IS_DEV) {
                    Logger.e("CheckIn Manager :", addressString[1]);
                    e2.printStackTrace();
                }
                return addressString;
            }
            // If the reverse geocode returned an address
            if (addresses != null && !addresses.isEmpty()) {
                // Get the first address
                Address address = addresses.get(0);
                addressString[0] = address.getLocality();
                try {
                    addressString[1] = address.getAddressLine(0);
                    if (addressString[1] != null && addressString[1].contains("Timed out"))
                        addressString[1] = mContext.getResources().getString(R.string.address_not_found);
                } catch (Exception e) {
                    addressString[1] = mContext.getResources().getString(R.string.address_not_found);
                }
                // Return the text
                return addressString;
            } else {
                addressString[0] = mContext.getResources().getString(R.string.address_not_found);
                addressString[1] = mContext.getResources().getString(R.string.coordinat) + " : " + mLatitude + ", " + mLongitude;
                return addressString;
            }
        }

        @Override
        protected void onPostExecute(String[] addressResult) {
            // Set activity indicator visibility to "gone"
            // Display the results of the lookup.
            setAddress(addressResult);
            address = addressResult;
        }

        private void setAddress(String[] addressResult) {
            TextView addressLocale = (TextView) findViewById(R.id.textLocalIn);
            TextView addressLine = (TextView) findViewById(R.id.textAddressIn);
            LinearLayout descLayout = (LinearLayout) findViewById(R.id.DescLayoutIn);
            if (addressResult[0] != null) {
                addressLocale.setText(addressResult[0]);
                addressLine.setText(addressResult[1]);
                descLayout.setVisibility(View.VISIBLE);
            } else {
                descLayout.setVisibility(View.GONE);
            }
        }
    }

    static void setLocation(String[] address) {
        try {
            LocationTagingView.address = address;
        } catch (Exception e) {
            FireCrash.log(e);
        }
    }
}
