package com.adins.mss.base.getreferantor;

import com.adins.mss.foundation.http.MssRequestType;
import com.google.gson.annotations.SerializedName;

import java.util.HashMap;

public class JsonRequestCekReferantor extends MssRequestType {

    @SerializedName("formName")
    private String formName;

    @SerializedName("odrNoCae")
    private String odrNoCae;

    @SerializedName("mapValues")
    private HashMap<String, Object> mapValues;

    public String getFormName() {
        return formName;
    }

    public void setFormName(String formName) {
        this.formName = formName;
    }

    public String getOdrNoCae() {
        return odrNoCae;
    }

    public void setOdrNoCae(String odrNoCae) {
        this.odrNoCae = odrNoCae;
    }

    public HashMap<String, Object> getMapValues() {
        return mapValues;
    }

    public void setMapValues(HashMap<String, Object> mapValues) {
        this.mapValues = mapValues;
    }
}
