package com.adins.mss.base.getreferantor;

import com.adins.mss.foundation.http.MssResponseType;
import com.google.gson.annotations.SerializedName;

import java.util.HashMap;

public class JsonResponseCekReferantor extends MssResponseType {

    @SerializedName("map_result") private HashMap<String, Object> objectHashMap;

    public HashMap<String, Object> getObjectHashMap() {
        return objectHashMap;
    }

    public void setObjectHashMap(HashMap<String, Object> objectHashMap) {
        this.objectHashMap = objectHashMap;
    }
}
