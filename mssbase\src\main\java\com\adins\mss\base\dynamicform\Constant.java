package com.adins.mss.base.dynamicform;

import com.adins.mss.dao.PrintResult;
import com.adins.mss.foundation.questiongenerator.QuestionBean;

import java.util.LinkedHashMap;
import java.util.List;

import com.adins.mss.dao.POAsset;
import com.adins.mss.dao.ProductOffering;



public class Constant {

	public static FormBean selectedForm;
	public static LinkedHashMap<String, QuestionBean> listOfQuestion=null;
	public static ProductOffering productOff = null;
	public static POAsset poAsset = null;
	//public static List<PrintItemBean> listOfPrintItem=null;
	public static List<PrintResult> listOfPrintItem=null;
	public static int notifCount;
	//public static Task task;


	public static FormBean getSelectedForm() {
		return selectedForm;
	}

	public static void setSelectedForm(FormBean selectedForm) {
		Constant.selectedForm = selectedForm;
	}

	public static LinkedHashMap<String, QuestionBean> getListOfQuestion() {
		return listOfQuestion;
	}

	public static void setListOfQuestion(LinkedHashMap<String, QuestionBean> listOfQuestion) {
		Constant.listOfQuestion = listOfQuestion;
	}

	public static List<PrintResult> getListOfPrintItem() {
		return listOfPrintItem;
	}

	public static void setListOfPrintItem(List<PrintResult> listOfPrintItem) {
		Constant.listOfPrintItem = listOfPrintItem;
	}
}
