package com.adins.mss.base.timeline;

import android.content.Context;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import android.widget.ArrayAdapter;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.LocationInfo;
import com.adins.mss.dao.Message;
import com.adins.mss.dao.TaskH;
import com.adins.mss.dao.Timeline;
import com.adins.mss.dao.TimelineType;
import com.adins.mss.dao.User;
import com.adins.mss.foundation.db.dataaccess.TaskHDataAccess;
import com.adins.mss.foundation.db.dataaccess.TimelineDataAccess;
import com.adins.mss.foundation.db.dataaccess.TimelineTypeDataAccess;
import com.adins.mss.foundation.formatter.Tool;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class TimelineManager {
    public static final String DESC_TM_CHECKIN  = "Timeline Type for Check In";
    public static final String DESC_TM_SUBMIT   = "Timeline Type for Submitted Task";
    public static final String DESC_TM_PRIORITY = "Timeline Type for Server Task";
    public static final String DESC_TM_VERIFIED = "Timeline Type for Verified Task";
    public static final String DESC_TM_APPROVED = "Timeline Type for Approved Task";
    public static final String DESC_TM_VERIFICATION = "Timeline Type for Verification Task";
    public static final String DESC_TM_APPROVAL = "Timeline Type for Approval Task";
    public static final String DESC_TM_REJECT   = "Timeline Type for Rejected Task";
    public static final String DESC_TM_MESSAGE  = "Timeline Type for Message";
    //	private Timeline timeline;
    ArrayAdapter<Timeline> listAdapter;

    //	static User user = GlobalData.getSharedGlobalData().getUser();
//	static String uuidUser  = GlobalData.getSharedGlobalData().getUser().getUuid_user();
//	static String userFullName  = GlobalData.getSharedGlobalData().getUser().getFullname();
//	static String userJob  = GlobalData.getSharedGlobalData().getUser().getJob_description();
//	static String userBranch  = GlobalData.getSharedGlobalData().getUser().getBranch_name();
    ArrayAdapter<Timeline> listAdapterbyType;
    TimelineArrayAdapter arrayAdapter;
    TimelineArrayAdapter arrayAdapterbyType;
    private Context context;

    public TimelineManager(Context context) {
        this.context = context;
    }

    /**
     * Gets List of Timeline from Database
     *
     * @param context
     * @return all Timeline
     */
    public static List<Timeline> getAllTimeline(Context context) {
        return TimelineDataAccess.getAll(context, GlobalData.getSharedGlobalData().getUser().getUuid_user());
    }

    public static List<Timeline> getAllTimelineWithLimitedDay(Context context, int range) {
        int minRange = 0 - range;
        try {
            if (minRange != 0) {
                List<Timeline> alltimeline = TimelineDataAccess.getAllDeletedTimeline(context, GlobalData.getSharedGlobalData().getUser().getUuid_user(), minRange);
                for (Timeline timeline : alltimeline) {
                    if(Global.TIMELINE_TYPE_REJECTED.equalsIgnoreCase(timeline.getTimelineType().getTimeline_type()) &&
                            timeline.getTaskH() != null) {
                        TaskHDataAccess.delete(context, timeline.getTaskH());
                    }
                    TimelineDataAccess.delete(context, timeline);
                }
            }
        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: handle exception
        }
        if (minRange != 0) {
            return TimelineDataAccess.getAllWithLimitedDay(context, GlobalData.getSharedGlobalData().getUser().getUuid_user(), minRange);
        } else {
            return getAllTimeline(context);
        }

    }

    public static Timeline getTimeline(Context context, String uuidUser, String uuidTimeline) {
        return TimelineDataAccess.getOneTimeline(context, uuidUser, uuidTimeline);
    }

    public static List<Timeline> getAllTimeline(Context context, String uuidUser) {
        return TimelineDataAccess.getAll(context, uuidUser);
    }

    public static List<Timeline> getAllTimelineByType(Context context, String timelineType, String uuidUser) {
        TimelineType type = TimelineTypeDataAccess.getTimelineTypebyType(context, timelineType);
        String uuidTimelineType = type.getUuid_timeline_type();
        return TimelineDataAccess.getAll(context, uuidUser, uuidTimelineType);
    }

    public static Timeline getTimelineByDescription(Context context, String description) {
        List<Timeline> list = getAllTimeline(context, GlobalData.getSharedGlobalData().getUser().getUuid_user());
        Timeline timeline = null;
        for (Timeline tempTimeline : list) {
            if (tempTimeline.getDescription().equals(description)) {
                timeline = tempTimeline;
            }
        }
        return timeline;
    }

    /**
     * Method for insert Timeline to Database by timeline
     *
     * @param context  - Context
     * @param timeline - Timeline
     */
    public static void insertTimeline(Context context, Timeline timeline) {
        TimelineDataAccess.add(context, timeline);
    }

    /**
     * Method for insert Timeline to Database, use by Checkin and Checkout
     *
     * @param context       - context
     * @param description   - String description
     * @param latitude      - String latitude
     * @param longitude     - String longitude
     * @param usrCrt       - String User Create
     * @param dtmCrt       - String date time create
     * @param timeLineType - String timeline type
     * @param bitmapArray   - byte[] byte image
     */
    public static void insertTimeline(Context context, String description, String latitude, String longitude, String usrCrt, Date dtmCrt, String timeLineType, byte[] bitmapArray) {
        Context mContext;
        mContext = context;
        String uuidTimeline = Tool.getUUID();
        Date dtmCrtServer = null;
        String uuidTaskH = "";
        String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        String uuidMessage = "";

//		String uuidTimelineType = Tool.getUUID();
//		String usr_upd = GlobalData.getSharedGlobalData().getUser().getUuid_user();
//		Date dtm_upd = dtmCrt;
//
//		TimelineType timelineType = new TimelineType(uuidTimelineType, description, timeLineType, usrCrt, dtmCrt, usr_upd, dtm_upd);
//		TimelineTypeDataAccess.add(mContext, timelineType);
        TimelineType timelineType = TimelineTypeDataAccess.getTimelineTypebyType(mContext, timeLineType);

        Timeline temptimeline = new Timeline(uuidTimeline, description, latitude, longitude, dtmCrtServer, usrCrt, dtmCrt, uuidTaskH, uuidUser, null, uuidMessage, bitmapArray);
        temptimeline.setTimelineType(timelineType);
        User nUser = null;
        try {
//			nUser = UserDataAccess.getOne(mContext, uuidUser);
            nUser = GlobalData.getSharedGlobalData().getUser();
        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: handle exception
        }
        if (nUser != null)
            temptimeline.setUser(nUser);
        else
            temptimeline.setUser(GlobalData.getSharedGlobalData().getUser());
        TimelineDataAccess.add(mContext, temptimeline);
    }

    /**
     * Method for insert Timeline to Database, use by Submitted task and Server Task
     *
     * @param context       - Context
     * @param description   - String description
     * @param usrCrt       - String user create
     * @param dtmCrt       - Date date time create
     * @param timeLineType - String timeline type
     * @param uuidTaskH   - String uuid  task H
     */
    public static void insertTimeline(Context context, String uuidTaskH, String description, String usrCrt, Date dtmCrt, String timeLineType) {
        Context mContext;
        mContext = context;
        String uuidTimeline = Tool.getUUID();
        Date dtmCrtServer = null;
        String latitude = "0.0";
        String longitude = "0.0";
        String uuidMessage = "";
        String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();

//		String uuidTimelineType = Tool.getUUID();
//		String usr_upd = GlobalData.getSharedGlobalData().getUser().getUuid_user();
//		Date dtm_upd = dtmCrt;
//
//		TimelineType timelineType = new TimelineType(uuidTimelineType, description, timeLineType, usrCrt, dtmCrt, usr_upd, dtm_upd);
//		TimelineTypeDataAccess.add(mContext, timelineType);
        TimelineType timelineType = TimelineTypeDataAccess.getTimelineTypebyType(mContext, timeLineType);

        Timeline temptimeline = new Timeline(uuidTimeline, description, latitude, longitude, dtmCrtServer, usrCrt, dtmCrt, uuidTaskH, uuidUser, null, uuidMessage, null);
        temptimeline.setTimelineType(timelineType);
        User nUser = null;
        try {
//			nUser = UserDataAccess.getOne(mContext, uuidUser);
            nUser = GlobalData.getSharedGlobalData().getUser();
        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: handle exception
        }
        if (nUser != null)
            temptimeline.setUser(nUser);
        else
            temptimeline.setUser(GlobalData.getSharedGlobalData().getUser());
        TimelineDataAccess.add(mContext, temptimeline);
    }

    /**
     * Method for insert Timeline to Database by Message
     *
     * @param context - Context
     * @param message
     */
    public static void insertTimeline(Context context, Message message) {
        Context mContext;
        mContext = context;
        String uuidTimeline = Tool.getUUID();
        Date dtmCrtServer = message.getDtm_crt_server();
        String uuidTaskH = "";
        String description = message.getMessage();
        String latitude = "0.0";
        String longitude = "0.0";
        String uuidMessage = message.getUuid_message();
        String usrCrt = message.getUsr_crt();
        Date dtmCrt = message.getDtm_crt_server();
        String timeLineType = Global.TIMELINE_TYPE_MESSAGE;
//		String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        String uuidUser = message.getUuid_user();


        TimelineType timelineType = TimelineTypeDataAccess.getTimelineTypebyType(mContext, timeLineType);

//		MessageDataAccess.add(mContext, message);

        Timeline temptimeline = new Timeline(uuidTimeline, description, latitude, longitude, dtmCrtServer, usrCrt, dtmCrt, uuidTaskH, uuidUser, null, uuidMessage, null);
        temptimeline.setTimelineType(timelineType);
        User nUser = null;
        try {
//			nUser = UserDataAccess.getOne(mContext, uuidUser);
            nUser = GlobalData.getSharedGlobalData().getUser();
        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: handle exception
        }
        if (nUser != null)
            temptimeline.setUser(nUser);
        else
            temptimeline.setUser(GlobalData.getSharedGlobalData().getUser());
        temptimeline.setMessage(message);
        TimelineDataAccess.add(mContext, temptimeline);
    }

    /**
     * Method for insert Timeline to Database by Message
     *
     * @param context - Context
     */
    public static void insertTimelinePushNotification(Context context, String description) {
        Context mContext;
        mContext = context;
        String uuidTimeline = Tool.getUUID();
        Date dtmCrtServer = new Date();
        String uuidTaskH = "";
        String latitude = "0.0";
        String longitude = "0.0";
        String uuidMessage = Tool.getUUID();
        String usrCrt = new Date().toString();
        Date dtmCrt = new Date();
        String timeLineType = Global.TIMELINE_TYPE_PUSH_NOTIFICATION;
        String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();

        TimelineType timelineType = TimelineTypeDataAccess.getTimelineTypebyType(mContext, timeLineType);

        Timeline temptimeline = new Timeline(uuidTimeline, description, latitude, longitude, dtmCrtServer, usrCrt, dtmCrt, uuidTaskH, uuidUser, null, uuidMessage, null);
        temptimeline.setTimelineType(timelineType);
        User nUser = null;
        try {
            nUser = GlobalData.getSharedGlobalData().getUser();
        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: handle exception
        }
        if (nUser != null)
            temptimeline.setUser(nUser);
        else
            temptimeline.setUser(GlobalData.getSharedGlobalData().getUser());

        TimelineDataAccess.add(mContext, temptimeline);
    }

    /**
     * @param context
     * @param taskH
     */
    public static void insertTimeline(Context context, TaskH taskH) {
        Context mContext;
        mContext = context;
        String uuidTimeline = Tool.getUUID();
        Date dtmCrtServer = taskH.getAssignment_date();
        String uuidTaskH = taskH.getUuid_task_h();
        String description;
        if (taskH.getMessage() != null) {
            if (taskH.getNotes() != null && taskH.getNotes().length() > 0) {
                description = taskH.getMessage() + "\n" + taskH.getCustomer_name() + " - " + taskH.getTask_id() + "\n" + taskH.getNotes();
            } else {
                if (taskH.getTask_id() != null && taskH.getTask_id().length() > 0)
                    description = taskH.getMessage() + "\n" + taskH.getCustomer_name() + " - " + taskH.getTask_id() + "\n" + context.getString(R.string.notes_not_available);
                else
                    description = taskH.getMessage() + "\n" + taskH.getCustomer_name() + "\n" + context.getString(R.string.notes_not_available);
            }
        } else {
            if (taskH.getNotes() != null && taskH.getNotes().length() > 0) {
                description = taskH.getCustomer_name() + " - " + taskH.getTask_id() + "\n" + taskH.getNotes();
            } else {
                if (taskH.getTask_id() != null && taskH.getTask_id().length() > 0)
                    description = taskH.getCustomer_name() + " - " + taskH.getTask_id() + "\n" + context.getString(R.string.notes_not_available);
                else
                    description = taskH.getCustomer_name() + "\n" + context.getString(R.string.notes_not_available);
            }
        }

        String latitude = taskH.getLatitude();
        String longitude = taskH.getLongitude();
        String uuidMessage = "";
        String usrCrt = taskH.getUsr_crt();

        Date dtmCrt = null;

        String timeLineType = null;
        if (TaskHDataAccess.STATUS_SEND_DROP.equalsIgnoreCase(taskH.getStatus())) {
            dtmCrt = Tool.getSystemDateTime();
            timeLineType = Global.TIMELINE_TYPE_REJECTED;
        } else if (TaskHDataAccess.STATUS_SEND_SENT.equals(taskH.getStatus())) {
//			dtmCrt= taskH.getSubmit_date();
            dtmCrt = Tool.getSystemDateTime();
            timeLineType = Global.TIMELINE_TYPE_SUBMITTED;
//			List<Timeline> tempTimeline = TimelineDataAccess.getTimelineByTask(context, GlobalData.getSharedGlobalData().getUser().getUuid_user(), uuidTaskH);
//			if(tempTimeline!=null && tempTimeline.size()>0){
//				for(Timeline timeline : tempTimeline){
//					TimelineType tt = timeline.getTimelineType();
//					if(tt.getTimeline_type().equals(Global.TIMELINE_TYPE_PENDING)){
//						TimelineDataAccess.delete(context, timeline);
//					}else if(tt.getTimeline_type().equals(Global.TIMELINE_TYPE_UPLOADING)){
//						TimelineDataAccess.delete(context, timeline);
//					}
//				}
//			}
        } else if (TaskHDataAccess.STATUS_SEND_INIT.equals(taskH.getStatus())) {
            timeLineType = Global.TIMELINE_TYPE_TASK;
            dtmCrt = taskH.getAssignment_date();
            if (dtmCrt == null)
                dtmCrt = Tool.getSystemDateTime();
        } else if (TaskHDataAccess.STATUS_TASK_VERIFICATION.equals(taskH.getStatus()) ||
                TaskHDataAccess.STATUS_TASK_VERIFICATION_DOWNLOAD.equals(taskH.getStatus())) {
            timeLineType = Global.TIMELINE_TYPE_VERIFICATION;
            dtmCrt = taskH.getAssignment_date();
            if (dtmCrt == null) {
                dtmCrt = taskH.getDtm_crt();
            }
            if (dtmCrt == null) {
                dtmCrt = Tool.getSystemDateTime();
            }
        } else if (TaskHDataAccess.STATUS_TASK_APPROVAL.equals(taskH.getStatus()) ||
                TaskHDataAccess.STATUS_TASK_APPROVAL_DOWNLOAD.equals(taskH.getStatus())) {
            timeLineType = Global.TIMELINE_TYPE_APPROVAL;
            dtmCrt = taskH.getAssignment_date();
            if (dtmCrt == null) {
                dtmCrt = taskH.getDtm_crt();
            }
            if (dtmCrt == null) {
                dtmCrt = Tool.getSystemDateTime();
            }
        } else if (TaskHDataAccess.STATUS_SEND_DOWNLOAD.equals(taskH.getStatus())) {
            timeLineType = Global.TIMELINE_TYPE_TASK;
            dtmCrt = taskH.getAssignment_date();
        }
        //TODO: Aktifin kalo mau pake timeline untuk task pending dan uploading
        else if (TaskHDataAccess.STATUS_SEND_PENDING.equals(taskH.getStatus())) {
            dtmCrt = Tool.getSystemDateTime();
            timeLineType = Global.TIMELINE_TYPE_PENDING;
        } else if (TaskHDataAccess.STATUS_SEND_UPLOADING.equals(taskH.getStatus())) {
            dtmCrt = Tool.getSystemDateTime();
            timeLineType = Global.TIMELINE_TYPE_UPLOADING;
//			List<Timeline> tempTimeline = TimelineDataAccess.getTimelineByTask(context, GlobalData.getSharedGlobalData().getUser().getUuid_user(), uuidTaskH);
//			if(tempTimeline!=null && tempTimeline.size()>0){
//				for(Timeline timeline : tempTimeline){
//					if(timeline.getTimelineType().getTimeline_type().equals(Global.TIMELINE_TYPE_PENDING)){
//						TimelineDataAccess.delete(context, timeline);
//					}
//				}
//			}
        }
        //TODO: ----------------------------------------------------------------
        String uuidUser = taskH.getUuid_user();

//		String uuidTimelineType = Tool.getUUID();
//		String usr_upd = usrCrt;
//		Date dtm_upd = dtmCrt;
//
//		TimelineType timelineType = new TimelineType(uuidTimelineType, description, timeLineType, usrCrt, dtmCrt, usr_upd, dtm_upd);
//		TimelineTypeDataAccess.add(mContext, timelineType);
        TimelineType timelineType = TimelineTypeDataAccess.getTimelineTypebyType(mContext, timeLineType);

        //if(timeline==null){
        Timeline temptimeline = new Timeline(uuidTimeline, description, latitude, longitude, dtmCrtServer, usrCrt, dtmCrt, uuidTaskH, uuidUser, null, uuidMessage, null);
        temptimeline.setTimelineType(timelineType);
        User nUser = null;
        try {
//				nUser = UserDataAccess.getOne(mContext, uuidUser);
            nUser = GlobalData.getSharedGlobalData().getUser();
        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: handle exception
        }
        if (nUser != null)
            temptimeline.setUser(nUser);
        else
            temptimeline.setUser(GlobalData.getSharedGlobalData().getUser());
        temptimeline.setTaskH(taskH);
        TimelineDataAccess.add(mContext, temptimeline);
        //}
    }

    public static void insertTimeline(Context context, TaskH taskH, boolean isVerified, boolean isReject) {
        Context mContext;
        mContext = context;
        String uuidTimeline = Tool.getUUID();
        Date dtmCrtServer = taskH.getAssignment_date();
        String uuidTaskH = taskH.getUuid_task_h();
        String description;
        if (taskH.getNotes() != null && taskH.getNotes().length() > 0) {
            description = taskH.getCustomer_name() + " - " + taskH.getTask_id() + "\n" + taskH.getNotes();
        } else {
            if (taskH.getTask_id() != null && taskH.getTask_id().length() > 0)
                description = taskH.getCustomer_name() + " - " + taskH.getTask_id() + "\n" + context.getString(R.string.notes_not_available);
            else
                description = taskH.getCustomer_name() + "\n" + context.getString(R.string.notes_not_available);
        }

        String latitude = taskH.getLatitude();
        String longitude = taskH.getLongitude();
        String uuidMessage = "";
        String usrCrt = taskH.getUsr_crt();

        Date dtmCrt = null;

        String timeLineType = null;
        if (isVerified) {
            if (isReject) {
                dtmCrt = taskH.getSubmit_date();
                timeLineType = Global.TIMELINE_TYPE_REJECTED;
            } else {
                dtmCrt = taskH.getSubmit_date();
                timeLineType = Global.TIMELINE_TYPE_VERIFIED;
            }
        } else {
            if (isReject) {
                dtmCrt = taskH.getSubmit_date();
                timeLineType = Global.TIMELINE_TYPE_REJECTED;
            } else {
                dtmCrt = taskH.getSubmit_date();
                timeLineType = Global.TIMELINE_TYPE_APPROVED;
            }
        }
        String uuidUser = taskH.getUuid_user();

        TimelineType timelineType = TimelineTypeDataAccess.getTimelineTypebyType(mContext, timeLineType);

        Timeline timeline = TimelineDataAccess.getOneTimelineByTaskH(context, GlobalData.getSharedGlobalData().getUser().getUuid_user(), uuidTaskH, timelineType.getUuid_timeline_type());
        if (timeline == null) {
            Timeline temptimeline = new Timeline(uuidTimeline, description, latitude, longitude, dtmCrtServer, usrCrt, dtmCrt, uuidTaskH, uuidUser, null, uuidMessage, null);
            temptimeline.setTimelineType(timelineType);
            User nUser = null;
            try {
//				nUser = UserDataAccess.getOne(mContext, uuidUser);
                nUser = GlobalData.getSharedGlobalData().getUser();
            } catch (Exception e) {
                FireCrash.log(e);
                // TODO: handle exception
            }
            if (nUser != null)
                temptimeline.setUser(nUser);
            else
                temptimeline.setUser(GlobalData.getSharedGlobalData().getUser());
            temptimeline.setTaskH(taskH);
            TimelineDataAccess.add(mContext, temptimeline);
        }
    }

    public static void insertTimeline(Context context, LocationInfo locationInfo, String description, byte[] bitmapArray) {
        Context mContext;
        mContext = context;
        String uuidTimeline = Tool.getUUID();
        Date dtmCrtServer = null;
        String uuidTaskH = "";
        String latitude = locationInfo.getLatitude();
        String longitude = locationInfo.getLongitude();
        String uuidMessage = "";
        String usrCrt = locationInfo.getUsr_crt();
        Date dtmCrt = locationInfo.getDtm_crt();
        String timeLineType = null;
        if (locationInfo.getLocation_type() == Global.LOCATION_TYPE_CHECKIN)
            timeLineType = Global.TIMELINE_TYPE_CHECKIN;
        else if (locationInfo.getLocation_type() == Global.LOCATION_TYPE_CHECKOUT)
            timeLineType = Global.TIMELINE_TYPE_CHECKOUT;
//		String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        String uuidUser = locationInfo.getUuid_user();

//		String uuidTimelineType = Tool.getUUID();
//		String usr_upd = usrCrt;
//		Date dtm_upd = dtmCrt;
//
//		TimelineType timelineType = new TimelineType(uuidTimelineType, description, timeLineType, usrCrt, dtmCrt, usr_upd, dtm_upd);
//		TimelineTypeDataAccess.add(mContext, timelineType);
        TimelineType timelineType = TimelineTypeDataAccess.getTimelineTypebyType(mContext, timeLineType);

        Timeline temptimeline = new Timeline(uuidTimeline, description, latitude, longitude, dtmCrtServer, usrCrt, dtmCrt, uuidTaskH, uuidUser, null, uuidMessage, bitmapArray);
        temptimeline.setTimelineType(timelineType);
        User nUser = null;
        try {
//			nUser = UserDataAccess.getOne(mContext, uuidUser);
            nUser = GlobalData.getSharedGlobalData().getUser();
        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: handle exception
        }
        if (nUser != null)
            temptimeline.setUser(nUser);
        else
            temptimeline.setUser(GlobalData.getSharedGlobalData().getUser());
        TimelineDataAccess.add(mContext, temptimeline);
    }

    /**
     * Method for insert Timeline List to Database
     *
     * @param context  - Context
     * @param timeline - Timeline
     */
    public static void insertTimeline(Context context, List<Timeline> timeline) {
        TimelineDataAccess.add(context, timeline);
    }

    public TimelineManager setColor(int color) {

        return this;
    }

    /**
     * Gets List of Timeline from Database
     *
     * @param context
     * @param timelineType
     * @return all timeline by type
     */
    public List<Timeline> getAllTimelineByType(Context context, String timelineType) {
        TimelineType type = TimelineTypeDataAccess.getTimelineTypebyType(context, timelineType);
        String uuidTimelineType = type.getUuid_timeline_type();
        return TimelineDataAccess.getAll(context, GlobalData.getSharedGlobalData().getUser().getUuid_user(), uuidTimelineType);
    }

    public ArrayAdapter<Timeline> doRefreshListAdapter() {
        return getTimelineAdapter();
    }

    public TimelineArrayAdapter doRefreshArrayAdapter() {
        return getTimelineArrayAdapter();
    }

    public ArrayAdapter<Timeline> getTimelineAdapter() {
        listAdapter = new TimelineArrayAdapter(context, getAllTimeline(context));
        return listAdapter;
    }

    public ArrayAdapter<Timeline> getTimelineAdapterByType(String timelineType) {
        listAdapterbyType = new TimelineArrayAdapter(context, getAllTimelineByType(context, timelineType));
        return listAdapterbyType;
    }

    public TimelineArrayAdapter getTimelineArrayAdapter() {
        arrayAdapter = new TimelineArrayAdapter(context, getAllTimeline(context));
        return arrayAdapter;
    }

    public TimelineArrayAdapter getTimelineArrayAdapterFragmentWithColor(FragmentActivity mainActivity, Fragment endFragment, int contentFrame, int color) {
        arrayAdapter = new TimelineArrayAdapter(mainActivity, endFragment, contentFrame, getAllTimeline(context), color);
        return arrayAdapter;
    }

    public TimelineArrayAdapter getTimelineArrayAdapterByType(String timelineType) {
        arrayAdapterbyType = new TimelineArrayAdapter(context, getAllTimelineByType(context, timelineType));
        return arrayAdapterbyType;
    }

//	public String getUserFullName(){
//		return userFullName;
//	}
//	
//	public String getUserJob(){
//		return userJob;		
//	}
//	
//	public String getUserBranch(){
//		return userBranch;
//	}
}


