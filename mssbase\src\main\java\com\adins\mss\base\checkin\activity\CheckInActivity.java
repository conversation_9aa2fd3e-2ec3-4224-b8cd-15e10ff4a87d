package com.adins.mss.base.checkin.activity;

import android.app.ActionBar;
import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import androidx.fragment.app.Fragment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.adins.mss.base.R;
import com.adins.mss.base.checkin.CheckInLocationTask;
import com.adins.mss.base.checkin.CheckInManager;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.timeline.Constants;
import com.adins.mss.base.util.Utility;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.LocationInfo;
import com.adins.mss.foundation.dialog.DialogManager;
import com.adins.mss.foundation.dialog.NiftyDialogBuilder;
import com.adins.mss.foundation.formatter.Tool;
import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.GoogleApiAvailability;
import com.google.android.gms.maps.CameraUpdateFactory;
import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.OnMapReadyCallback;
import com.google.android.gms.maps.SupportMapFragment;
import com.google.android.gms.maps.model.BitmapDescriptorFactory;
import com.google.android.gms.maps.model.CircleOptions;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.MarkerOptions;

import org.acra.ACRA;

public class CheckInActivity extends Fragment implements
        OnClickListener, OnMapReadyCallback {

    public static TextView AddressLocale;
    public static TextView AddressLine;
    public static LinearLayout descLayout;
    static CheckInManager manager;
    static LocationInfo info;
    static LatLng latLng;
    static GoogleMap mGoogleMap;
    private static Context context;
    private static View view;
    private static String[] address;
    int REQUEST_CODE_RECOVER_PLAY_SERVICES = 1001;
    ImageButton btnRefresh;
    double mLatitude = 0;
    double mLongitude = 0;

    public static void setLocation(String[] address) {
        try {
            descLayout.setVisibility(View.VISIBLE);
            AddressLocale.setText(address[0]);
            AddressLine.setText(address[1]);
            CheckInActivity.address = address;
        } catch (Exception e) {
            FireCrash.log(e);
        }

    }

    public static void setNewLocation() {
        info = manager.getNewLocation();
        manager.getAddressLocation(context, info);
        latLng = manager.getLatLng(info);
        int accuracy = info.getAccuracy();
        mGoogleMap.clear();
        mGoogleMap.animateCamera(CameraUpdateFactory.newLatLngZoom(latLng, 15));
        MarkerOptions markerOptions = new MarkerOptions();
        markerOptions.position(latLng);
        markerOptions.icon(BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_GREEN));
        mGoogleMap.addMarker(markerOptions);

        if (accuracy != 0) {
            CircleOptions circleOptions = new CircleOptions()
                    .center(latLng)
                    .radius(accuracy)
                    .fillColor(0x402196F3)
                    .strokeColor(Color.TRANSPARENT)
                    .strokeWidth(2);

            mGoogleMap.addCircle(circleOptions);
        }
    }

    @Override
    public void onAttach(Context activity) {
        super.onAttach(activity);
        ACRA.getErrorReporter().putCustomData("LAST_CLASS_ACCESSED", getClass().getSimpleName());
        setHasOptionsMenu(true);
        getActivity().getActionBar().setTitle(getString(R.string.title_mn_absentin));
        context = activity;
        try {
            manager = new CheckInManager(activity);
            info = manager.getLocationInfoCheckIn();
            manager.getAddressLocation(context, info);
            latLng = manager.getLatLng(info);
        } catch (Exception e) {
            FireCrash.log(e);
        }

    }

    private boolean checkPlayServices() {
        int status = GoogleApiAvailability.getInstance().isGooglePlayServicesAvailable(getActivity());
        if (status != ConnectionResult.SUCCESS) {
            if (GoogleApiAvailability.getInstance().isUserResolvableError(status)) {
                showErrorDialog(status);
            } else {
                Toast.makeText(getActivity(), getActivity().getString(R.string.device_not_supported),
                        Toast.LENGTH_LONG).show();
            }
            return false;
        }
        return true;
    }

    void showErrorDialog(int code) {
        GoogleApiAvailability.getInstance().getErrorDialog(getActivity(), code,
                REQUEST_CODE_RECOVER_PLAY_SERVICES).show();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (checkPlayServices()) {
            // Then we're good to go!
        }
        try {
            DialogManager.showGPSAlert(getActivity());
        } catch (Exception e) {
            FireCrash.log(e);
            e.printStackTrace();
        }
        getActivity().getActionBar().removeAllTabs();
        getActivity().getActionBar().setTitle(getString(R.string.title_mn_absentin));
        getActivity().getActionBar().setNavigationMode(ActionBar.NAVIGATION_MODE_STANDARD);

    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        //super.onCreateView(inflater, container, savedInstanceState);
        if (Tool.isProdFlavour(Global.FLAVORS)) {
            //To can't screenshoot
            getActivity().getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
        }

        if (view != null) {
            ViewGroup parent = (ViewGroup) view.getParent();
            if (parent != null)
                parent.removeView(view);
        }
        try {
            view = inflater.inflate(R.layout.checkin_layout, container, false);

            AddressLocale = (TextView) view.findViewById(R.id.textLocalIn);
            AddressLine = (TextView) view.findViewById(R.id.textAddressIn);
            descLayout = (LinearLayout) view.findViewById(R.id.DescLayoutIn);
            btnRefresh = (ImageButton) view.findViewById(R.id.btnRefreshCIn);

            btnRefresh.setOnClickListener(this);
            descLayout.setOnClickListener(this);
            SupportMapFragment fragment = (SupportMapFragment)
                    getChildFragmentManager().findFragmentById(R.id.mapIn);

            // Setting Google Map
            fragment.getMapAsync(this);
        } catch (Exception e) {
            FireCrash.log(e);
//			initializeMaps();
        }

        manager.updateLocationCheckin();
        Constants.inAbsent = true;

        return view;
    }

    private void initializeMaps() {
        try {
            try {
                mGoogleMap.clear();
            } catch (Exception e) {
                FireCrash.log(e);
            }
            mGoogleMap.animateCamera(CameraUpdateFactory.newLatLngZoom(latLng, 15));
            MarkerOptions markerOptions = new MarkerOptions();
            markerOptions.position(latLng);
            markerOptions.icon(BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_GREEN));
            mGoogleMap.addMarker(markerOptions);

            int accuracy = info.getAccuracy();
            if (accuracy != 0) {
                CircleOptions circleOptions = new CircleOptions()
                        .center(latLng)
                        .radius(accuracy)
                        .fillColor(0x402196F3)
                        .strokeColor(Color.TRANSPARENT)
                        .strokeWidth(2);

                mGoogleMap.addCircle(circleOptions);
            }
        } catch (Exception e) {
            FireCrash.log(e);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Utility.freeMemory();
    }

    //	@Override
//	public void onDestroyView(){
//		super.onDestroyView();
//
//		FragmentTransaction ft2 = getActivity().getSupportFragmentManager()
//                .beginTransaction();
//
//        ft2.remove(getActivity().getSupportFragmentManager()
//                .findFragmentById(R.id.mapIn));
//        ft2.commit();
//	}
//
//	@Override
//	public void onPause(){
//		FragmentTransaction ft2 = getActivity().getSupportFragmentManager()
//                .beginTransaction();
//
//        ft2.remove( getActivity().getSupportFragmentManager()
//                .findFragmentByTag("mapin"));
//        ft2.commit();	
//        super.onPause();
//	}
    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.btnRefreshCIn) {
            manager.updateLocationCheckin();
        } else if (id == R.id.DescLayoutIn) {
            if (AddressLocale.getText().equals(context.getString(R.string.address_not_found)) || AddressLocale.getText().equals("")) {
                NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(context);
                dialogBuilder.withTitle(context.getString(R.string.error_capital))
                        .withMessage(context.getString(R.string.msgUnavailableAddress))
                        .isCancelable(true)
                        .show();
            } else {
                try {
                    CheckInLocationTask task = new CheckInLocationTask(context, context.getString(R.string.progressWait),
                            context.getString(R.string.msgUnavaibleLocationCheckIn), info, address);
                    task.execute();
                } catch (Exception e) {
                    FireCrash.log(e);
                    if (context != null) {
                        NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(context);
                        dialogBuilder.withTitle(context.getString(R.string.error_capital))
                                .withMessage(context.getString(R.string.msgUnavaibleLocationCheckIn))
                                .isCancelable(true)
                                .show();

                    }
                }
            }
        }
    }

    @Override
    public void onMapReady(GoogleMap googleMap) {
        mGoogleMap = googleMap;
        initializeMaps();
    }
}