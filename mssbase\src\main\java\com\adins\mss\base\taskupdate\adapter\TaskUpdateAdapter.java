package com.adins.mss.base.taskupdate.adapter;

import android.content.Context;
import android.graphics.drawable.GradientDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.TextView;

import com.adins.mss.base.R;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.TaskUpdate;
import com.adins.mss.foundation.formatter.Formatter;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

public class TaskUpdateAdapter extends ArrayAdapter<TaskUpdate>  {

    private Context context;
    private List<TaskUpdate> objects;
    private GradientDrawable gradient;

    public TaskUpdateAdapter(Context context, List<TaskUpdate> objects) {
        super(context, R.layout.task_update_item_layout, objects);
        this.context = context;
        this.objects = objects;
    }

    @Override
    public int getCount() {
        return objects.size();
    }

    @Override
    public View getView(final int position, View convertView, ViewGroup parent) {
        if (null == convertView) {
            convertView = LayoutInflater.from(context).inflate(R.layout.task_update_item_layout, parent, false);
        }

        TextView txtId = (TextView) convertView.findViewById(R.id.taskUpdateId);
        TextView txtName = (TextView) convertView.findViewById(R.id.custName);
        TextView txtApp = (TextView) convertView.findViewById(R.id.taskUpAppName);
        TextView txtTime = (TextView) convertView.findViewById(R.id.timeSendTaskUp);
        TextView txtScheme = (TextView) convertView.findViewById(R.id.taskUpScheme);

        TaskUpdate task = objects.get(position);
        String taskUpdateId = task.getUuid_task_update();
        String taskUpdateApp = task.getAppl_no();
        String customerName = task.getCustomer_name();
        String formId = "";
        if (task.getForm_name() != null) {
            formId = task.getForm_name();
        }
        String dTime = task.getAssignment_date();
        String sTime = "";
        try {
            Date date = new SimpleDateFormat("ddMMyyyyHHmmss").parse(dTime);
            sTime = Formatter.formatDate(date, Global.DATE_TIME_STR_FORMAT);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        txtId.setText(taskUpdateId);
        txtApp.setText(taskUpdateApp);
        txtName.setSelected(true);
        txtName.setText(customerName);
        txtScheme.setText(formId);
        txtTime.setText(sTime);

        return convertView;
    }

}
