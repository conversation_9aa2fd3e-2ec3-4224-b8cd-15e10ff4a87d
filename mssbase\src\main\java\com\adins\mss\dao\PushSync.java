package com.adins.mss.dao;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "TR_PUSHSYNC".
 */
public class PushSync {

    /** Not-null value. */
     @SerializedName("idPushSync")
    private String idPushSync;
     @SerializedName("isActive")
    private String isActive;
     @SerializedName("maxTimestamps")
    private String maxTimestamps;
     @SerializedName("tableName")
    private String tableName;
     @SerializedName("lovGroup")
    private String lovGroup;
     @SerializedName("totalRecord")
    private String totalRecord;
     @SerializedName("filename")
    private String filename;
     @SerializedName("fileUrl")
    private String fileUrl;

    public PushSync() {
    }

    public PushSync(String idPushSync) {
        this.idPushSync = idPushSync;
    }

    public PushSync(String idPushSync, String isActive, String maxTimestamps, String tableName, String lovGroup, String totalRecord, String filename, String fileUrl) {
        this.idPushSync = idPushSync;
        this.isActive = isActive;
        this.maxTimestamps = maxTimestamps;
        this.tableName = tableName;
        this.lovGroup = lovGroup;
        this.totalRecord = totalRecord;
        this.filename = filename;
        this.fileUrl = fileUrl;
    }

    /** Not-null value. */
    public String getIdPushSync() {
        return idPushSync;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setIdPushSync(String idPushSync) {
        this.idPushSync = idPushSync;
    }

    public String getIsActive() {
        return isActive;
    }

    public void setIsActive(String isActive) {
        this.isActive = isActive;
    }

    public String getMaxTimestamps() {
        return maxTimestamps;
    }

    public void setMaxTimestamps(String maxTimestamps) {
        this.maxTimestamps = maxTimestamps;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getLovGroup() {
        return lovGroup;
    }

    public void setLovGroup(String lovGroup) {
        this.lovGroup = lovGroup;
    }

    public String getTotalRecord() {
        return totalRecord;
    }

    public void setTotalRecord(String totalRecord) {
        this.totalRecord = totalRecord;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

}
