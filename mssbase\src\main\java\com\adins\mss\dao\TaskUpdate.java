package com.adins.mss.dao;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "TR_TASK_UPDATE".
 */
public class TaskUpdate {

    /** Not-null value. */
     @SerializedName("uuid_task_update")
    private String uuid_task_update;
     @SerializedName("customer_name")
    private String customer_name;
     @SerializedName("customer_phone")
    private String customer_phone;
     @SerializedName("customer_address")
    private String customer_address;
     @SerializedName("notes")
    private String notes;
     @SerializedName("assignment_date")
    private String assignment_date;
     @SerializedName("appl_no")
    private String appl_no;
     @SerializedName("form_name")
    private String form_name;
     @SerializedName("pending_notes")
    private String pending_notes;
     @SerializedName("docupro_feedback")
    private String docupro_feedback;
     @SerializedName("uuid_scheme")
    private String uuid_scheme;
     @SerializedName("uuid_user")
    private String uuid_user;
     @SerializedName("is_notified")
    private Integer is_notified;
     @SerializedName("category")
    private String category;
     @SerializedName("sub_category")
    private String sub_category;
     @SerializedName("reason_detail")
    private String reason_detail;
     @SerializedName("validasi")
    private String validasi;
     @SerializedName("isPreApproval")
    private Integer is_pre_approval;

    public TaskUpdate() {
    }

    public TaskUpdate(String uuid_task_update) {
        this.uuid_task_update = uuid_task_update;
    }

    public TaskUpdate(String uuid_task_update, String customer_name, String customer_phone, String customer_address, String notes, String assignment_date, String appl_no, String form_name, String pending_notes, String docupro_feedback, String uuid_scheme, String uuid_user, Integer is_notified, String category, String sub_category, String reason_detail, String validasi, Integer is_pre_approval) {
        this.uuid_task_update = uuid_task_update;
        this.customer_name = customer_name;
        this.customer_phone = customer_phone;
        this.customer_address = customer_address;
        this.notes = notes;
        this.assignment_date = assignment_date;
        this.appl_no = appl_no;
        this.form_name = form_name;
        this.pending_notes = pending_notes;
        this.docupro_feedback = docupro_feedback;
        this.uuid_scheme = uuid_scheme;
        this.uuid_user = uuid_user;
        this.is_notified = is_notified;
        this.category = category;
        this.sub_category = sub_category;
        this.reason_detail = reason_detail;
        this.validasi = validasi;
        this.is_pre_approval = is_pre_approval;
    }

    /** Not-null value. */
    public String getUuid_task_update() {
        return uuid_task_update;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_task_update(String uuid_task_update) {
        this.uuid_task_update = uuid_task_update;
    }

    public String getCustomer_name() {
        return customer_name;
    }

    public void setCustomer_name(String customer_name) {
        this.customer_name = customer_name;
    }

    public String getCustomer_phone() {
        return customer_phone;
    }

    public void setCustomer_phone(String customer_phone) {
        this.customer_phone = customer_phone;
    }

    public String getCustomer_address() {
        return customer_address;
    }

    public void setCustomer_address(String customer_address) {
        this.customer_address = customer_address;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getAssignment_date() {
        return assignment_date;
    }

    public void setAssignment_date(String assignment_date) {
        this.assignment_date = assignment_date;
    }

    public String getAppl_no() {
        return appl_no;
    }

    public void setAppl_no(String appl_no) {
        this.appl_no = appl_no;
    }

    public String getForm_name() {
        return form_name;
    }

    public void setForm_name(String form_name) {
        this.form_name = form_name;
    }

    public String getPending_notes() {
        return pending_notes;
    }

    public void setPending_notes(String pending_notes) {
        this.pending_notes = pending_notes;
    }

    public String getDocupro_feedback() {
        return docupro_feedback;
    }

    public void setDocupro_feedback(String docupro_feedback) {
        this.docupro_feedback = docupro_feedback;
    }

    public String getUuid_scheme() {
        return uuid_scheme;
    }

    public void setUuid_scheme(String uuid_scheme) {
        this.uuid_scheme = uuid_scheme;
    }

    public String getUuid_user() {
        return uuid_user;
    }

    public void setUuid_user(String uuid_user) {
        this.uuid_user = uuid_user;
    }

    public Integer getIs_notified() {
        return is_notified;
    }

    public void setIs_notified(Integer is_notified) {
        this.is_notified = is_notified;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getSub_category() {
        return sub_category;
    }

    public void setSub_category(String sub_category) {
        this.sub_category = sub_category;
    }

    public String getReason_detail() {
        return reason_detail;
    }

    public void setReason_detail(String reason_detail) {
        this.reason_detail = reason_detail;
    }

    public String getValidasi() {
        return validasi;
    }

    public void setValidasi(String validasi) {
        this.validasi = validasi;
    }

    public Integer getIs_pre_approval() {
        return is_pre_approval;
    }

    public void setIs_pre_approval(Integer is_pre_approval) {
        this.is_pre_approval = is_pre_approval;
    }

}
