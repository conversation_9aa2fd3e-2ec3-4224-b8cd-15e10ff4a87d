package com.adins.mss.dao;

import com.adins.mss.dao.DaoSession;
import de.greenrobot.dao.DaoException;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "TR_IMAGERESULT".
 */
public class ImageResult {

    /** Not-null value. */
     @SerializedName("uuid_image_result")
    private String uuid_image_result;
     @SerializedName("question_id")
    private String question_id;
     @SerializedName("submit_duration")
    private String submit_duration;
     @SerializedName("submit_size")
    private String submit_size;
     @SerializedName("total_image")
    private Integer total_image;
     @SerializedName("count_image")
    private Integer count_image;
     @SerializedName("submit_date")
    private java.util.Date submit_date;
     @SerializedName("submit_result")
    private String submit_result;
     @SerializedName("question_group_id")
    private String question_group_id;
     @SerializedName("usr_crt")
    private String usr_crt;
     @SerializedName("dtm_crt")
    private java.util.Date dtm_crt;
     @SerializedName("uuid_task_h")
    private String uuid_task_h;

    /** Used to resolve relations */
    private transient DaoSession daoSession;

    /** Used for active entity operations. */
    private transient ImageResultDao myDao;

    private TaskH taskH;
    private String taskH__resolvedKey;


    public ImageResult() {
    }

    public ImageResult(String uuid_image_result) {
        this.uuid_image_result = uuid_image_result;
    }

    public ImageResult(String uuid_image_result, String question_id, String submit_duration, String submit_size, Integer total_image, Integer count_image, java.util.Date submit_date, String submit_result, String question_group_id, String usr_crt, java.util.Date dtm_crt, String uuid_task_h) {
        this.uuid_image_result = uuid_image_result;
        this.question_id = question_id;
        this.submit_duration = submit_duration;
        this.submit_size = submit_size;
        this.total_image = total_image;
        this.count_image = count_image;
        this.submit_date = submit_date;
        this.submit_result = submit_result;
        this.question_group_id = question_group_id;
        this.usr_crt = usr_crt;
        this.dtm_crt = dtm_crt;
        this.uuid_task_h = uuid_task_h;
    }

    /** called by internal mechanisms, do not call yourself. */
    public void __setDaoSession(DaoSession daoSession) {
        this.daoSession = daoSession;
        myDao = daoSession != null ? daoSession.getImageResultDao() : null;
    }

    /** Not-null value. */
    public String getUuid_image_result() {
        return uuid_image_result;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_image_result(String uuid_image_result) {
        this.uuid_image_result = uuid_image_result;
    }

    public String getQuestion_id() {
        return question_id;
    }

    public void setQuestion_id(String question_id) {
        this.question_id = question_id;
    }

    public String getSubmit_duration() {
        return submit_duration;
    }

    public void setSubmit_duration(String submit_duration) {
        this.submit_duration = submit_duration;
    }

    public String getSubmit_size() {
        return submit_size;
    }

    public void setSubmit_size(String submit_size) {
        this.submit_size = submit_size;
    }

    public Integer getTotal_image() {
        return total_image;
    }

    public void setTotal_image(Integer total_image) {
        this.total_image = total_image;
    }

    public Integer getCount_image() {
        return count_image;
    }

    public void setCount_image(Integer count_image) {
        this.count_image = count_image;
    }

    public java.util.Date getSubmit_date() {
        return submit_date;
    }

    public void setSubmit_date(java.util.Date submit_date) {
        this.submit_date = submit_date;
    }

    public String getSubmit_result() {
        return submit_result;
    }

    public void setSubmit_result(String submit_result) {
        this.submit_result = submit_result;
    }

    public String getQuestion_group_id() {
        return question_group_id;
    }

    public void setQuestion_group_id(String question_group_id) {
        this.question_group_id = question_group_id;
    }

    public String getUsr_crt() {
        return usr_crt;
    }

    public void setUsr_crt(String usr_crt) {
        this.usr_crt = usr_crt;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public String getUuid_task_h() {
        return uuid_task_h;
    }

    public void setUuid_task_h(String uuid_task_h) {
        this.uuid_task_h = uuid_task_h;
    }

    /** To-one relationship, resolved on first access. */
    public TaskH getTaskH() {
        String __key = this.uuid_task_h;
        if (taskH__resolvedKey == null || taskH__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            TaskHDao targetDao = daoSession.getTaskHDao();
            TaskH taskHNew = targetDao.load(__key);
            synchronized (this) {
                taskH = taskHNew;
            	taskH__resolvedKey = __key;
            }
        }
        return taskH;
    }

    public void setTaskH(TaskH taskH) {
        synchronized (this) {
            this.taskH = taskH;
            uuid_task_h = taskH == null ? null : taskH.getUuid_task_h();
            taskH__resolvedKey = uuid_task_h;
        }
    }

    /** Convenient call for {@link AbstractDao#delete(Object)}. Entity must attached to an entity context. */
    public void delete() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.delete(this);
    }

    /** Convenient call for {@link AbstractDao#update(Object)}. Entity must attached to an entity context. */
    public void update() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.update(this);
    }

    /** Convenient call for {@link AbstractDao#refresh(Object)}. Entity must attached to an entity context. */
    public void refresh() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.refresh(this);
    }

}
