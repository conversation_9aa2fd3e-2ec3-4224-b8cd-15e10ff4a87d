package com.adins.mss.base;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import androidx.fragment.app.FragmentActivity;
import android.view.View;
import android.view.WindowManager;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.dynamicform.JsonRequestQuestionSet;
import com.adins.mss.base.dynamicform.JsonResponseQuestionSet;
import com.adins.mss.base.mainmenu.MainMenuActivity;
import com.adins.mss.base.todo.form.JsonRequestScheme;
import com.adins.mss.base.todo.form.JsonResponseScheme;
import com.adins.mss.base.util.CustomizeDatabase;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.base.util.LocaleHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.Holiday;
import com.adins.mss.dao.Lookup;
import com.adins.mss.dao.PrintItem;
import com.adins.mss.dao.QuestionSet;
import com.adins.mss.dao.Scheme;
import com.adins.mss.dao.Sync;
import com.adins.mss.dao.User;
import com.adins.mss.foundation.camerainapp.helper.Logger;
import com.adins.mss.foundation.db.dataaccess.HolidayDataAccess;
import com.adins.mss.foundation.db.dataaccess.LookupDataAccess;
import com.adins.mss.foundation.db.dataaccess.PrintItemDataAccess;
import com.adins.mss.foundation.db.dataaccess.QuestionSetDataAccess;
import com.adins.mss.foundation.db.dataaccess.SchemeDataAccess;
import com.adins.mss.foundation.db.dataaccess.SyncDataAccess;
import com.adins.mss.foundation.db.dataaccess.UserDataAccess;
import com.adins.mss.foundation.dialog.DialogManager;
import com.adins.mss.foundation.dialog.NiftyDialogBuilder;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.HttpConnection;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.security.storepreferences.ObscuredSharedPreferences;
import com.adins.mss.foundation.sync.DefaultSynchronizeScheme;
import com.adins.mss.foundation.sync.Synchronize;
import com.adins.mss.foundation.sync.SynchronizeItem;
import com.adins.mss.foundation.sync.api.DataSynchronizer;
import com.adins.mss.foundation.sync.api.FakeSynchronizationCallback;
import com.google.gson.JsonParseException;

import org.acra.ACRA;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;

/**
 * Created by winy.firdasari on 05/01/2015.
 */

public abstract class SynchronizeActivity extends FragmentActivity implements
        Synchronize.SynchronizeListener, Runnable {

    private static final String SYNCHRONIZATION_PREFERENCE = "com.adins.mss.base.SynchronizationPreference";
    public Activity activity = this;
    protected ProgressBar progressBar;
    protected TextView progressLabel, syncScheme, syncSchemeDone, syncQuestion,
            syncQuestionDone, syncLookup, syncLookupDone, syncholidayDone;
    Synchronize sync;
    private int syncCounter = 0;
    private int syncMax = 0;
    private FakeSynchronizationCallback<Sync> syncFakeCallback;
    private boolean init;
    private DataSynchronizer datasync;
    private FakeSynchronizationCallback<Lookup> lookupFakeCallback;
    private FakeSynchronizationCallback<Holiday> holidayFakeCallback;
    private FakeSynchronizationCallback<User> cashFakeCallBack;
    private Sync finalSavedSync;
    private boolean forceStop;
    private boolean isFinish = false, isSyncScheme = false,
            isSyncQuestionSet = false, isSyncLookup = false,
            isSyncHoliday = false, isHoliday = false;
    private SynchronizeAll synchronizeAll;

    public SynchronizeActivity() {

    }

    @Override
    public void onBackPressed() {
    }

    protected void synchronize() {
        datasync = new DataSynchronizer(activity);
        if (synchronizeAll != null)
            synchronizeAll.cancel(true);
        synchronizeAll = new SynchronizeAll();
        synchronizeAll.execute();
        // datasync.reflect("MS_HOLIDAY_D", Holiday.class,
        // HolidayDataAccess.class, null,null);
        /*new AsyncTask<Void, Void, Void>() {

            @Override
            protected Void doInBackground(Void... params) {
                try {
                    final String res1 = getScheme();
                    if (res1.equals(Global.TRUE_STRING)) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                isSyncScheme = true;
                                progressUpdated(1);
                            }
                        });
                        getQuestionSet();
                        syncLookup();
                    } else {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                if (Global.FALSE_STRING.equals(res1)) {
                                    showErrorDialog();
                                } else if ("Data has gone".equals(res1)) {
                                    String message = "data has been corrupted. Please exit the application and relogin.";
                                    DialogManager.showForceExitAlert(activity, message);
                                } else if (HttpConnection.ERROR_STATUSCODE_FROM_SERVER.equals(res1)) {
                                    if(Global.IS_DEV)
                                        System.out.print(HttpConnection.ERROR_STATUSCODE_FROM_SERVER);
                                } else {
                                    showErrorDialog(res1);
                                }
                            }
                        });
                    }
                } catch (IOException e) {
                  FireCrash.log(e);
                    if(Global.IS_DEV)
                        e.printStackTrace();
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            showErrorDialog();
                        }
                    });
                }
                return null;
            }
        }.execute();*/

    }

    protected void downloadFiles(boolean sync) {
        //
    }

    private String getScheme() {
        String resultScheme = Global.FALSE_STRING;
        List<Scheme> listSchemes = SchemeDataAccess.getAll(getApplicationContext());
        if (Global.TempScheme != null)
            Global.TempScheme = null;
        Global.TempScheme = new HashMap<String, Date>();

        for (Scheme scheme : listSchemes) {
            Global.TempScheme.put(scheme.getUuid_scheme(), scheme.getScheme_last_update());
        }

        try {
            if (GlobalData.getSharedGlobalData().getUser() == null)
                MainMenuActivity.InitializeGlobalDataIfError(getApplicationContext());
        } catch (Exception e) {
            FireCrash.log(e);
            resultScheme = "Data has gone";
            return resultScheme;
        }

        JsonRequestScheme requestScheme = new JsonRequestScheme();
        if (GlobalData.getSharedGlobalData().getUser() != null && GlobalData.getSharedGlobalData().getUser()
                .getUuid_user() != null) {
            requestScheme.setUuid_user(GlobalData.getSharedGlobalData().getUser()
                    .getUuid_user());
        } else {
            ObscuredSharedPreferences sharedPref = ObscuredSharedPreferences.getPrefs(activity,
                    "GlobalData", Context.MODE_PRIVATE);
            String uuidUser = sharedPref.getString("UUID_USER", "");
            if (uuidUser.length() > 0) {
                requestScheme.setUuid_user(uuidUser);
            } else {
                resultScheme = "Data has gone";
                return resultScheme;
            }
        }
        requestScheme.setAudit(GlobalData.getSharedGlobalData().getAuditData());
        requestScheme.setTask(Global.TASK_GETLIST);

        String json = GsonHelper.toJson(requestScheme);
        String url = GlobalData.getSharedGlobalData().getURL_GET_SCHEME();
        boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
        boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
        HttpCryptedConnection httpConn = new HttpCryptedConnection(activity, encrypt, decrypt);
        HttpConnectionResult serverResult = null;
        try {
            serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
        } catch (Exception e) {
            FireCrash.log(e);
            if (Global.IS_DEV)
                e.printStackTrace();
        }
        if (serverResult != null && serverResult.isOK()) {
            try {
                String result = serverResult.getResult();
//				String result =
//				 "{\"listScheme\":[{\"uuid_scheme\":\"18ba5207-89bf-11e4-b68b-6f9ababe5e21\",\"scheme_description\":\"form1\",\"scheme_last_update\":\"22022015000000\",\"is_printable\":\"0\",\"form_id\":\"ODR01\",\"is_preview_server\":\"0\",\"form_type\":\"Order\"}],\"listPrintItems\":[{\"uuid_print_item\":\"18ba5207-89bf-11e4-b68b-6f9ababe5e22\",\"uuid_scheme\":\"18ba5207-89bf-11e4-b68b-6f9ababe5e21\"}],\"status\":{\"code\":0}}";
                JsonResponseScheme responseScheme = GsonHelper.fromJson(result,
                        JsonResponseScheme.class);
                List<Scheme> schemes = responseScheme.getListScheme();
                List<PrintItem> printItems = responseScheme.getListPrintItems();
                if (schemes != null) {
                    resultScheme = Global.TRUE_STRING;
                    syncMax = schemes.size() + printItems.size();
                    // bong 19 may 15 - delete scheme dulu baru di add dari server
//                    if (schemes.size() > 0) {
                    SchemeDataAccess.clean(activity);
//                    }

                    for (Scheme scheme : schemes) {
                        syncCounter++;
                        publishCounter();
                        Scheme scheme2 = null;
                        try {
                            scheme2 = SchemeDataAccess.getOneByLastUpdate(activity,
                                    scheme.getUuid_scheme(),
                                    scheme.getScheme_last_update());
                        } catch (Exception e) {
                            FireCrash.log(e);
                        }
                        try {
                            PrintItemDataAccess.delete(activity, scheme.getUuid_scheme());
                        } catch (Exception e) {
                            FireCrash.log(e);
                        }

                        if (scheme2 == null) {
                            if (scheme.getUuid_scheme() != null) {
                                SchemeDataAccess.addOrReplace(activity, scheme);
                            }
                        } else {
                            if (scheme.getScheme_last_update().after(
                                    scheme2.getScheme_last_update())) {
                                if (scheme.getUuid_scheme() != null) {
                                    SchemeDataAccess.addOrReplace(activity, scheme);
                                }
                            }
                        }
                    }


                    for (PrintItem printItem : printItems) {
                        syncCounter++;
                        publishCounter();
                        Scheme scheme = SchemeDataAccess.getOne(activity,
                                printItem.getUuid_scheme());
                        printItem.setScheme(scheme);
                        PrintItemDataAccess.addOrReplace(activity, printItem);
                    }

                    if (schemes.isEmpty() || printItems.isEmpty()) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                progressUpdated(100);
                                isSyncScheme = true;
                            }
                        });
                    }
                } else {
                    resultScheme = responseScheme.getStatus().getMessage();
                }

                // SchemeDataAccess.addOrReplace(activity, schemes);
            } catch (JsonParseException e) {
                FireCrash.log(e);
                if (Global.IS_DEV)
                    e.printStackTrace();
                resultScheme = getString(R.string.jsonParseFailed);
            } catch (IllegalStateException e) {
                FireCrash.log(e);

                if (Global.IS_DEV)
                    e.printStackTrace();
                resultScheme = getString(R.string.jsonParseFailed);
            } catch (Exception e) {
                FireCrash.log(e);
                if (Global.IS_DEV)
                    e.printStackTrace();
                resultScheme = getString(R.string.jsonParseFailed);
            }
        } else {
            try {
                resultScheme = serverResult.getResult();
            } catch (NullPointerException e) {
                FireCrash.log(e);

                resultScheme = getString(R.string.jsonParseFailed);
            } catch (Exception e) {
                FireCrash.log(e);
                resultScheme = getString(R.string.jsonParseFailed);
            }
        }

//		runOnUiThread(new Runnable() {
//			@Override
//			public void run() {
//				isSyncScheme = true;
//				progressUpdated(1);
//			}
//		});
        return resultScheme;
    }

    protected void getQuestionSet() {
        List<Scheme> schemeList = SchemeDataAccess.getAll(activity);
        syncMax = schemeList.size();
        syncCounter = 0;
        for (Scheme scheme : schemeList) {
            List<QuestionSet> qs = new ArrayList<>();
            try {
                qs = QuestionSetDataAccess.getAllByFormVersion(activity, scheme.getUuid_scheme(), scheme.getForm_version());
            } catch (Exception e) {
                Logger.e("SynchronizeActivity", e);
            }
            boolean isChange = true;

            if (isChange || qs.isEmpty()) {
                syncCounter++;
                publishCounter();
                JsonRequestQuestionSet request = new JsonRequestQuestionSet();
                request.setAudit(GlobalData.getSharedGlobalData().getAuditData());
                request.setForm_version(scheme.getForm_version());
                request.setUuid_scheme(scheme.getUuid_scheme());

                String json = GsonHelper.toJson(request);
                String url = GlobalData.getSharedGlobalData().getURL_GET_QUESTIONSET();
                boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                HttpCryptedConnection httpConn = new HttpCryptedConnection(getApplicationContext(), encrypt, decrypt);
                HttpConnectionResult serverResult = null;
                if (Tool.isInternetconnected(activity)) {
                    try {
                        serverResult = httpConn.requestToServer(url, json,
                                Global.DEFAULTCONNECTIONTIMEOUT);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        if (Global.IS_DEV)
                            e.printStackTrace();
//                    System.out.println(e.getMessage());
                    }
                    if (serverResult != null)
                        if (serverResult.isOK()) {
                            try {
                                String result = serverResult.getResult();
                                JsonResponseQuestionSet response = GsonHelper.fromJson(
                                        result, JsonResponseQuestionSet.class);
                                if (response.getStatus().getCode() == 0) {
                                    List<QuestionSet> questionSets = response
                                            .getListQuestionSet();
                                    QuestionSetDataAccess.delete(activity,
                                            scheme.getUuid_scheme());
                                    List<QuestionSet> newquestionSets = new ArrayList<QuestionSet>();
                                    for (QuestionSet questionSet : questionSets) {
                                        questionSet
                                                .setUuid_question_set(Tool.getUUID());
                                        questionSet.setScheme(scheme);
                                        newquestionSets.add(questionSet);
                                    }
                                    QuestionSetDataAccess.addOrReplace(activity,
                                            scheme.getUuid_scheme(), newquestionSets);
                                } else {
                                    if (Global.IS_DEV)
                                        Logger.i("INFO", result);
                                }
                            } catch (Exception e) {
                                FireCrash.log(e);
                                if (Global.IS_DEV)
                                    Logger.i("INFO", e.getMessage());
                            }
                        }
                }
            } else {
                syncMax = 2;
                syncCounter = 1;
                publishCounter();
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
//    					isSyncQuestionSet = true;
                        progressUpdated(100);
                    }
                });
            }
        }
        if (schemeList.isEmpty()) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
//					isSyncQuestionSet = true;
                    progressUpdated(100);
                    isSyncQuestionSet = true;
                }
            });
        }
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                isSyncQuestionSet = true;
                progressUpdated(1);
            }
        });
//		isSyncQuestionSet = true;
    }

    public void syncMaster() {}

    public void syncFile() {}

    private void syncLookup() throws IOException {
        List<Scheme> schemeList = SchemeDataAccess.getAll(activity);
//        List<Sync> syncs = SyncDataAccess.getAll(activity);
        //TODO : Pake yang dibawah ini kalo pake embedded DB untuk table Lookup, jgn lupa comment yg atas
        List<Sync> syncs = CustomizeDatabase.getAll(activity);
        init = syncs == null || syncs.isEmpty();
        List<HashMap<String, String>> lookupArgs = new ArrayList<HashMap<String, String>>();
        for (Scheme scheme : schemeList) {
            HashMap<String, String> forms = new HashMap<String, String>();
            forms.put("formId", scheme.getForm_id());
            lookupArgs.add(forms);
        }
        if (!schemeList.isEmpty()) {
            datasync.fakeReflect("MS_LOV", Sync.class, lookupArgs,
                    syncFakeCallback, init ? 1 : 0, true, DataSynchronizer.IS_SYNCHRONIZE_SYNC);
        } else {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    progressUpdated(100);
                }
            });
            Thread thread = new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        String application = GlobalData.getSharedGlobalData().getAuditData().getApplication();
                        if (Global.APPLICATION_COLLECTION.equalsIgnoreCase(application)) {
                            datasync.fakeReflect("USER_CASH", User.class,
                                    null, cashFakeCallBack, 1,
                                    false, DataSynchronizer.IS_SYNCHRONIZE_COH);
                        }
                        //Your code goes here
                        datasync.fakeReflect("MS_HOLIDAY_D", Holiday.class,
                                null, holidayFakeCallback, 1,
                                false, DataSynchronizer.IS_SYNCHRONIZE_HOLIDAY);

                        if (forceStop) {
                            return;
                        }
                    } catch (Exception e) {
                        if (Global.IS_DEV)
                            e.printStackTrace();
                        Handler handler = new Handler(Looper.getMainLooper());
                        handler.post(new Runnable() {
                            public void run() {
                                progressUpdated(100);
                                isSyncHoliday = true;
                                publishCounter();
                            }

                        });
                    }
                }
            });

            thread.start();
        }
    }

    private void publishCounter() {
        float progress = (float) (syncCounter) / (syncMax - 1) * 100;
        final float finalProgress = progress > 100 ? 100 : progress;
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                progressUpdated(finalProgress);
            }
        });
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (Tool.isProdFlavour(Global.FLAVORS)) {
            //To can't screenshoot
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
        }

        setContentView(R.layout.synchronize_layout);
        ACRA.getErrorReporter().putCustomData("LAST_CLASS_ACCESSED", getClass().getSimpleName());
        progressBar = (ProgressBar) findViewById(R.id.progressBar);
        progressLabel = (TextView) findViewById(R.id.progressLabel);
        syncScheme = (TextView) findViewById(R.id.textViewSyncScheme);
        syncSchemeDone = (TextView) findViewById(R.id.textViewSyncSchemeDone);
        syncQuestion = (TextView) findViewById(R.id.textViewSyncQuestionSet);
        syncQuestionDone = (TextView) findViewById(R.id.textViewSyncQuestionSetDone);
        syncLookup = (TextView) findViewById(R.id.textViewSyncLookup);
        syncLookupDone = (TextView) findViewById(R.id.textViewSyncLookupDone);
        syncholidayDone = (TextView) findViewById(R.id.textViewSyncHolidayDone);
        syncSchemeDone.setVisibility(View.GONE);
        syncQuestionDone.setVisibility(View.GONE);
        syncLookupDone.setVisibility(View.GONE);
        syncholidayDone.setVisibility(View.GONE);
        TextView tvAppVersion = (TextView) findViewById(R.id.contentVersion);
        String versioning = getString(R.string.app_name) + " v." + Global.APP_VERSION;
        tvAppVersion.setText(versioning);

        syncScheme.setText("Check Database version...");

        // Intent timelineIntent = new Intent(this,TimelineActivity.class);
        // Intent timelineIntent = getIntentMainMenu();
        // this.startActivity(timelineIntent);
        syncFakeCallback = new FakeSynchronizationCallback<Sync>() {
            @Override
            public void onSuccess(List<Sync> entities) {
                syncMax = entities.size() * 2;
                syncCounter = 0;
                try {
                    if (!entities.isEmpty()) {
                        for (Sync sync : entities) {
                            syncCounter++;
                            publishCounter();
                            if (init) {
                                /**
                                 * Cara Embed Table lookup
                                 * 1. Ambil file msmdb menggunakan Android Device Manager (ADM)
                                 * 2. pilih file explorer -> data -> data -> nama package -> database -> msmdb -> save ke drive
                                 * 3. buka Sqlite Manager open file msmdb kemudian Import file .csv ke table MS_LOOKUP (jika belum ada datanya)
                                 * 4. isi table sync (jika belum ada isinya) sesuai lov_group yang mau di embed
                                 *    jangan lupa tambahkan 1 second untuk dtm_upd masing2 item yang mau di embed, untuk dtm_upd bisa minta ke bagian web
                                 *    edit table sync (jika pernah melewati langkah 7), kemudian hapus setiap record untuk lov_group yang TIDAK di embedd
                                 * 5. (jika kesulitan untuk mengisi tabel sync, lewati langkah 4 langsung ke langkah 6)
                                 * 6. Copy-paste file msmdb yg telah diubah tadi ke folder assets di masing2 project nya.
                                 * 7. (jika langkah 4 di lewati, jalankan aplikasi kemudian login sampai masuk ke halaman timeline, kemudian ulangi dari langkah 1)
                                 * 8. //TODO : cek tiap tag TODO di file ini
                                 **/
                                // TODO : UncOmment jika Pake Embedded DB untuk table Lookup
                                //untuk LOV_GROUP yang di embed (sample :KOTA, KELURAHAN, KECAMATAN, ASSETMASTER, KODE POS
                            if (sync.getLov_group().equals("KOTA") ||
                                    sync.getLov_group().equals("KELURAHAN") ||
									sync.getLov_group().equals("KECAMATAN") ||
									sync.getLov_group().equals("PROVINSI") ||
									sync.getLov_group().equals("KODE POS")) {
								finalSavedSync = SyncDataAccess.getOneByLovGroupName(activity, sync.getLov_group());
								if (finalSavedSync != null) {
									if (finalSavedSync.getDtm_upd().getTime() >= sync.getDtm_upd().getTime()) {
										syncCounter++;
										publishCounter();
										List<HashMap<String, Object>> lookupArgs = new ArrayList<HashMap<String, Object>>();
										HashMap<String, Object> forms = new HashMap<String, Object>();
										forms.put("lov_group", sync.getLov_group());
										forms.put("dtm_upd", finalSavedSync.getDtm_upd());

										lookupArgs.add(forms);

										try {

											datasync.fakeReflect("MS_LOV", Lookup.class,
													lookupArgs, lookupFakeCallback, 1,
													false, DataSynchronizer.IS_SYNCHRONIZE_LOOKUP);
											if (forceStop) {
												return;
											}
										} catch (IOException e) {
                  FireCrash.log(e);
											e.printStackTrace();
											runOnUiThread(new Runnable() {
												@Override
												public void run() {
													showErrorDialog();
												}
											});
											return;
										}
									}
								} else {
									finalSavedSync = sync;
									Lookup lookups = LookupDataAccess.getOneLastUpdByLovGroup(activity, sync.getLov_group());
                                    Date dtmUpd = lookups.getDtm_upd();
									finalSavedSync.setDtm_upd(dtmUpd);
									List<HashMap<String, Object>> lookupArgs = new ArrayList<HashMap<String, Object>>();
									HashMap<String, Object> forms = new HashMap<String, Object>();
									forms.put("lov_group", sync.getLov_group());
									forms.put("dtm_upd", finalSavedSync.getDtm_upd());
									lookupArgs.add(forms);

									try {

										datasync.fakeReflect("MS_LOV", Lookup.class,
												lookupArgs, lookupFakeCallback, 1,
												false, DataSynchronizer.IS_SYNCHRONIZE_LOOKUP);
										if (forceStop) {
											return;
										}
									} catch (IOException e) {
                  FireCrash.log(e);
										e.printStackTrace();
										runOnUiThread(new Runnable() {
											@Override
											public void run() {
												showErrorDialog();
											}
										});
										return;
									}

								}

							}
							//untuk LOV_GROUP yang tidak di embed
							else {
								List<HashMap<String, String>> lookupArgs = new ArrayList<HashMap<String, String>>();
								HashMap<String, String> forms = new HashMap<String, String>();
								forms.put("lov_group", sync.getLov_group());
								lookupArgs.add(forms);

								try {
                                    finalSavedSync = sync;
                                    datasync.fakeReflect("MS_LOV", Lookup.class,
                                            lookupArgs, lookupFakeCallback, 1,
                                            false, DataSynchronizer.IS_SYNCHRONIZE_LOOKUP);
									if (forceStop) {
										return;
									}
								} catch (IOException e) {
                  FireCrash.log(e);
									e.printStackTrace();
									runOnUiThread(new Runnable() {
										@Override
										public void run() {
											showErrorDialog();
										}
									});
									return;
								}
							}
                                // TODO : UnComment sampe sini
                                /**-----------------------------------------------------------*/
                                // TODO : Comment code dibawah jika Pake Embedded DB untuk table Lookup
//                                List<HashMap<String, String>> lookupArgs = new ArrayList<HashMap<String, String>>();
//                                HashMap<String, String> forms = new HashMap<String, String>();
//                                forms.put("lov_group", sync.getLov_group());
//                                lookupArgs.add(forms);
//
//                                try {
//                                    finalSavedSync = sync;
//                                    datasync.fakeReflect("MS_LOV", Lookup.class,
//                                            lookupArgs, lookupFakeCallback, 1,
//                                            false, DataSynchronizer.IS_SYNCHRONIZE_LOOKUP);
//                                    if (forceStop) {
//                                        return;
//                                    }
//
//                                } catch (IOException e) {
//                                    FireCrash.log(e);
//                                    sendErrorDataToACRA(e.getMessage());
//                                    if (Global.IS_DEV)
//                                        e.printStackTrace();
//                                    runOnUiThread(new Runnable() {
//                                        @Override
//                                        public void run() {
//                                            showErrorDialog();
//                                        }
//                                    });
//                                    return;
//                                }
                                // TODO : Comment sampe sini
                            } else {
                                finalSavedSync = SyncDataAccess
                                        .getOneByLovGroupName(
                                                activity,
                                                sync.getLov_group());
                                Lookup lookups = LookupDataAccess.getOneByLovGroup(activity, sync.getLov_group());
                                boolean isHaveLookupData;
                                isHaveLookupData = lookups != null;
                                if (finalSavedSync != null && isHaveLookupData) {
                                    if (finalSavedSync.getDtm_upd().getTime() >= sync
                                            .getDtm_upd().getTime()) {
                                        syncCounter++;
                                        publishCounter();
                                        continue;
                                    }
                                } else {
                                    finalSavedSync = sync;
                                }

                                List<HashMap<String, Object>> lookupArgs = new ArrayList<HashMap<String, Object>>();
                                HashMap<String, Object> forms = new HashMap<String, Object>();
                                forms.put("lov_group", sync.getLov_group());

                                if (SyncDataAccess.getOneByLovGroupName(activity, finalSavedSync.getLov_group()) != null) {/*SyncDataAccess.getOne(activity, finalSavedSync.getUuid_sync()) != null*/
                                    forms.put("dtm_upd", finalSavedSync.getDtm_upd());
                                }
                                //FIXME: uncomment & Ganti lov_group dibawah ini jika menggunakan embeded lookup table
                            /*else if (finalSavedSync.getLov_group().equals("KOTA") ||
                                        finalSavedSync.getLov_group().equals("KELURAHAN") ||
                                        finalSavedSync.getLov_group().equals("KECAMATAN") ||
                                        finalSavedSync.getLov_group().equals("ASSETMASTER") ||
                                        finalSavedSync.getLov_group().equals("KODE POS")) {
                                    forms.put("dtm_upd", finalSavedSync.getDtm_upd());
                            }*/
                                /**-----------------------------------------------------------*/

                                lookupArgs.add(forms);

                                try {
                                    datasync.fakeReflect("MS_LOV", Lookup.class,
                                            lookupArgs, lookupFakeCallback, 0,
                                            false, DataSynchronizer.IS_SYNCHRONIZE_LOOKUP);
                                    if (forceStop) {
                                        return;
                                    }
                                } catch (IOException e) {
                                    FireCrash.log(e);
                                    if (Global.IS_DEV)
                                        e.printStackTrace();
                                    sendErrorDataToACRA(e.getMessage());
                                    runOnUiThread(new Runnable() {
                                        @Override
                                        public void run() {
                                            showErrorDialog();
                                        }
                                    });
                                    return;
                                }
                            }
                            syncCounter++;
                            publishCounter();
                        }

                        // Nendi: 2018.03.08 | Add Embeded LOV Branch & Region
                        // TODO : Comment code dibawah jika Pake Embedded DB untuk table Lookup
                        List<String> groups = new ArrayList<>();
                        groups.add("BRANCH");
                        groups.add("REGION");

                        for (String lovGroup : groups) {
                            List<HashMap<String, String>> lookupArgs = new ArrayList<HashMap<String, String>>();
                            HashMap<String, String> forms = new HashMap<String, String>();
                            forms.put("lov_group", lovGroup);
                            lookupArgs.add(forms);

                            try {
                                datasync.fakeReflect("MS_LOV", Lookup.class,
                                        lookupArgs, lookupFakeCallback, 1,
                                        false, DataSynchronizer.IS_SYNCHRONIZE_LOOKUP);
                                if (forceStop) {
                                    return;
                                }

                            } catch (IOException e) {
                                FireCrash.log(e);
                                sendErrorDataToACRA(e.getMessage());
                                if (Global.IS_DEV)
                                    e.printStackTrace();
                                runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        showErrorDialog();
                                    }
                                });
                                return;
                            }
                        }

                    } else {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                progressUpdated(100);
                                isSyncLookup = true;
                            }
                        });
                        //isSyncLookup = true;
                        //syncCounter++;
                        //publishCounter();
                    }

                    publishCounter();
                    String application = GlobalData.getSharedGlobalData().getAuditData().getApplication();
                    if (Global.APPLICATION_COLLECTION.equalsIgnoreCase(application)) {
                        try {
                            datasync.fakeReflect("USER_CASH", User.class,
                                    null, cashFakeCallBack, 1,
                                    false, DataSynchronizer.IS_SYNCHRONIZE_COH);
                            if (forceStop) {
                                return;
                            }
                        } catch (Exception e) {
                            FireCrash.log(e);
                            isSyncHoliday = true;
                            publishCounter();
                        }
                    }
                    //sync Holiday
                    try {
                        datasync.fakeReflect("MS_HOLIDAY_D", Holiday.class,
                                null, holidayFakeCallback, 1,
                                false, DataSynchronizer.IS_SYNCHRONIZE_HOLIDAY);
                        if (forceStop) {
                            return;
                        }
                    } catch (Exception e) {
                        FireCrash.log(e);
                        isSyncHoliday = true;
                        publishCounter();
                    }

                } catch (Exception ex) {
                    FireCrash.log(ex);
                    if (Global.IS_DEV)
                        ex.printStackTrace();
                    sendErrorDataToACRA(ex.getMessage());
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            showErrorDialog();
                        }
                    });
                    return;
                }
            }

            @Override
            public void onFailed(final String errMessage) {
                sendErrorDataToACRA(errMessage);
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (Global.IS_DEV && errMessage != null && !errMessage.isEmpty())
                            showErrorDialog(errMessage);
                        else
                            showErrorDialog();
                    }
                });
            }
        };

        holidayFakeCallback = new FakeSynchronizationCallback<Holiday>() {

            @Override
            public void onSuccess(List<Holiday> entities) {
                try {
                    syncMax = entities.size() * 2;
                    syncCounter = 0;
                    isHoliday = true;
                    for (Holiday holiday : entities) {
                        syncCounter++;
                        publishCounter();
                        HolidayDataAccess.addOrReplace(getApplicationContext(), holiday);
                        syncCounter++;
                        publishCounter();
                    }
                    publishCounter();
                } catch (Exception ex) {
                    FireCrash.log(ex);
                    sendErrorDataToACRA(ex.getMessage());
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            showErrorDialog();
                        }
                    });
                    forceStop = true;
                    if (Global.IS_DEV)
                        ex.printStackTrace();
                }
            }

            @Override
            public void onFailed(final String errMessage) {
                sendErrorDataToACRA(errMessage);
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (Global.IS_DEV && errMessage != null && !errMessage.isEmpty())
                            showErrorDialog(errMessage);
                        else
                            showErrorDialog();
                        forceStop = true;
                    }
                });
            }
        };

        lookupFakeCallback = new FakeSynchronizationCallback<Lookup>() {
            @Override
            public void onSuccess(List<Lookup> entities) {
                try {
                    LookupDataAccess.addOrUpdateAll(activity, entities);
                    SyncDataAccess.addOrReplace(activity, finalSavedSync);
                } catch (Exception ex) {
                    FireCrash.log(ex);
                    sendErrorDataToACRA(ex.getMessage());
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            showErrorDialog();
                        }
                    });
                    forceStop = true;
                    if (Global.IS_DEV)
                        ex.printStackTrace();
                }
            }

            @Override
            public void onFailed(final String errMessage) {
                sendErrorDataToACRA(errMessage);
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (Global.IS_DEV && errMessage != null && !errMessage.isEmpty())
                            showErrorDialog(errMessage);
                        else
                            showErrorDialog();
                        forceStop = true;
                    }
                });
            }
        };

        cashFakeCallBack = new FakeSynchronizationCallback<User>() {
            @Override
            public void onSuccess(List<User> entities) {
                if (!entities.isEmpty()) {
                    User ucash = GlobalData.getSharedGlobalData().getUser();
                    ucash.setCash_limit(entities.get(0).getCash_limit());
                    ucash.setCash_on_hand(entities.get(0).getCash_on_hand());
                    UserDataAccess.addOrReplace(getApplicationContext(), ucash);
                }
            }

            @Override
            public void onFailed(final String errMessage) {
                sendErrorDataToACRA(errMessage);
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (Global.IS_DEV && errMessage != null && !errMessage.isEmpty())
                            showErrorDialog(errMessage);
                        else
                            showErrorDialog();
                        forceStop = true;
                    }
                });
            }
        };

        syncMaster();
//        synchronize();
//        checkDatabaseVersion();
    }


    @Override
    protected void attachBaseContext(Context newBase) {
        Context context = newBase;
        Locale locale;
        try {
            locale = new Locale(GlobalData.getSharedGlobalData().getLocale());
            if (null == locale) {
                locale = new Locale(LocaleHelper.ENGLSIH);
            }
            context = LocaleHelper.wrap(newBase, locale);
        } catch (Exception e) {
            locale = new Locale(LocaleHelper.ENGLSIH);
            context = LocaleHelper.wrap(newBase, locale);
        } finally {
            super.attachBaseContext(context);
        }
    }

    protected abstract Intent getIntentMainMenu();

    public void startSync() {
        sync = new Synchronize();
        sync.setSynchronizeScheme(new DefaultSynchronizeScheme());
        sync.setListener(this);
        Thread backThread = new Thread(this);
        backThread.start();
    }

    @Override
    public void run() {
        sync.startSynchronize();
    }

    @Override
    public void progressUpdated(float progress) {
        if (!isSyncScheme && !isSyncQuestionSet && !isSyncLookup) {
            if (progress == 100) {
                downloadFiles(false);
                syncSchemeDone.setVisibility(View.VISIBLE);
                syncSchemeDone.setText("done");
            } else {
                syncSchemeDone.setVisibility(View.VISIBLE);
                syncSchemeDone.setText("on progress");
            }
        } else if (isSyncScheme && !isSyncQuestionSet && !isSyncLookup) {
//            if (progress == 100) {
//                syncQuestionDone.setVisibility(View.VISIBLE);
//                syncQuestionDone.setText("done");
//            } else {
//                syncQuestionDone.setVisibility(View.VISIBLE);
//                syncQuestionDone.setText("on progress");
//            }
            syncScheme.setText("Sync Question Set");
            if (progress == 100) {
                syncSchemeDone.setVisibility(View.VISIBLE);
                syncSchemeDone.setText("done");
            } else {
                syncSchemeDone.setVisibility(View.VISIBLE);
                syncSchemeDone.setText("on progress");
            }
        } else if (isSyncScheme && isSyncQuestionSet && !isSyncLookup) {
            syncScheme.setText("Sync Lookup");
            if (progress >= 100) {
                isSyncLookup = true;
                syncSchemeDone.setVisibility(View.VISIBLE);
                syncSchemeDone.setText("done");
            } else {
                syncSchemeDone.setVisibility(View.VISIBLE);
                syncSchemeDone.setText("on progress");
            }
        } else if (isSyncScheme && isSyncQuestionSet && isSyncLookup && !isSyncHoliday) {
            syncScheme.setText("Sync Holiday");
            if (progress >= 100) {
                if (isHoliday) {
                    isSyncHoliday = true;
                    syncSchemeDone.setVisibility(View.VISIBLE);
                    syncSchemeDone.setText("done");
                }
            } else {
                if (isHoliday) {
                    syncSchemeDone.setVisibility(View.VISIBLE);
                    syncSchemeDone.setText("on progress");
                }
            }
            if (progress == -0.0) {
                if (isHoliday) {
                    isSyncHoliday = true;
                    syncSchemeDone.setVisibility(View.VISIBLE);
                    syncSchemeDone.setText("done");
                }
            }
        }

        if (progress > 100)
            progress = 100;
        if (progress != 0.0) {
            progressBar.setProgress(((int) progress) > 100 ? 100
                    : (int) progress);
            progressLabel.setText("Progress : " + (int) progress + "%");
        } else {
            progressBar.setProgress(100);
            progressLabel.setText("Progress : 100 %");
        }

        if (syncMax == 0 || progress >= 100) {
            // Intent timelineIntent = new Intent(this,TimelineActivity.class);
            if (!isFinish && isSyncLookup && isSyncHoliday) {
                Intent timelineIntent = getIntentMainMenu();
                activity.startActivity(timelineIntent);
                finish();
                isFinish = true;

                String application = GlobalData.getSharedGlobalData().getAuditData().getApplication();

                String day = String.valueOf(Calendar.getInstance().get(Calendar.DATE));
                String month = String.valueOf(Calendar.getInstance().get(Calendar.MONTH) + 1);
                String year = String.valueOf(Calendar.getInstance().get(Calendar.YEAR));

                ObscuredSharedPreferences synchronizationPreference = ObscuredSharedPreferences.getPrefs(this, SYNCHRONIZATION_PREFERENCE, Context.MODE_PRIVATE);
                if (Global.APPLICATION_ORDER.equalsIgnoreCase(application)) {
                    synchronizationPreference.edit().putString("MOSyncDate", day + month + year).commit();
                } else if (Global.APPLICATION_SURVEY.equalsIgnoreCase(application)) {
                    synchronizationPreference.edit().putString("MSSyncDate", day + month + year).commit();
                } else if (Global.APPLICATION_COLLECTION.equalsIgnoreCase(application)) {
                    synchronizationPreference.edit().putString("MCSyncDate", day + month + year).commit();
                }
            }
        }
    }

    @Override
    public void synchronizeFailed(SynchronizeItem syncItem,
                                  HttpConnectionResult errorResult, int numOfRetries) {
        sync.resumeSync();
    }

    private void showErrorDialog() {
        if (synchronizeAll != null)
            synchronizeAll.cancel(true);
        if (!isFinishing()) {
            final NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(activity);
            builder.withTitle("Error").withMessage("Synchronize error")
                    .withButton1Text("Try Again").withButton2Text("Later")
                    .isCancelable(false)
                    .setButton1Click(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            builder.dismiss();
                            if (Build.VERSION.SDK_INT >= 11) {
                                recreate();
                            } else {
                                Intent intent = getIntent();
                                intent.addFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION);
                                finish();
                                overridePendingTransition(0, 0);

                                startActivity(intent);
                                overridePendingTransition(0, 0);
                            }
                        }
                    }).setButton2Click(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    builder.dismiss();
                    Intent timelineIntent = new Intent(getApplicationContext(), MainMenuActivity.getMainMenuClass());
                    timelineIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//                    getApplicationContext().startActivity(timelineIntent);
                    startActivity(timelineIntent);
                    finish();
                }
            }).show();
        }
    }

    private void showErrorDialog(String message) {
        if (synchronizeAll != null)
            synchronizeAll.cancel(true);
        if (!isFinishing()) {
            final NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(activity);
            builder.withTitle("Error").withMessage(message)
                    .withButton1Text("Try Again").withButton2Text("Later")
                    .isCancelable(false)
                    .setButton1Click(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            builder.dismiss();
                            if (Build.VERSION.SDK_INT >= 11) {
                                recreate();
                            } else {
                                Intent intent = getIntent();
                                intent.addFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION);
                                finish();
                                overridePendingTransition(0, 0);

                                startActivity(intent);
                                overridePendingTransition(0, 0);
                            }
                        }
                    }).setButton2Click(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    builder.dismiss();
                    Intent timelineIntent = new Intent(
                            SynchronizeActivity.this, MainMenuActivity
                            .getMainMenuClass());
                    timelineIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//                    getApplicationContext().startActivity(timelineIntent);
                    startActivity(timelineIntent);
                    finish();
                }
            }).show();
        }
    }

    public void sendErrorDataToACRA(String message) {
        ACRA.getErrorReporter().putCustomData("errorSynchronize", message);
        ACRA.getErrorReporter().handleSilentException(new Exception("Error: Synchronize Error " + message));
    }

    public class SynchronizeAll extends AsyncTask<Void, Void, Void> {
        @Override
        protected Void doInBackground(Void... params) {
            try {
                final String res1 = getScheme();
                if (res1.equals(Global.TRUE_STRING)) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            isSyncScheme = true;
                            progressUpdated(1);
                        }
                    });
                    getQuestionSet();
                    syncLookup();
                } else {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (Global.FALSE_STRING.equals(res1)) {
                                showErrorDialog();
                            } else if ("Data has gone".equals(res1)) {
                                String message = "data has been corrupted. Please exit the application and relogin.";
                                DialogManager.showForceExitAlert(activity, message);
                            } else if (HttpConnection.ERROR_STATUSCODE_FROM_SERVER.equals(res1)) {
                                if (Global.IS_DEV)
                                    Logger.i("INFO", HttpConnection.ERROR_STATUSCODE_FROM_SERVER);
                            } else {
                                showErrorDialog(res1);
                            }
                        }
                    });
                }
            } catch (final IOException e) {
                FireCrash.log(e);
                if (Global.IS_DEV)
                    e.printStackTrace();
                sendErrorDataToACRA(e.getMessage());
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        showErrorDialog();
                    }
                });
            }
            return null;
        }
    }

    public void checkDatabaseVersion() {}
}
