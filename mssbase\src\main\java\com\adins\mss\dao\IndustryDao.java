package com.adins.mss.dao;

import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;

import com.adins.mss.dao.Industry;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "MS_INDUSTRY".
*/
public class IndustryDao extends AbstractDao<Industry, Long> {

    public static final String TABLENAME = "MS_INDUSTRY";

    /**
     * Properties of entity Industry.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Id = new Property(0, long.class, "id", true, "ID");
        public final static Property Is_deleted = new Property(1, Integer.class, "is_deleted", false, "IS_DELETED");
        public final static Property Type_code = new Property(2, String.class, "type_code", false, "TYPE_CODE");
        public final static Property Margin = new Property(3, Double.class, "margin", false, "MARGIN");
        public final static Property Dtm_upd = new Property(4, java.util.Date.class, "dtm_upd", false, "DTM_UPD");
    };


    public IndustryDao(DaoConfig config) {
        super(config);
    }
    
    public IndustryDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"MS_INDUSTRY\" (" + //
                "\"ID\" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL ," + // 0: id
                "\"IS_DELETED\" INTEGER," + // 1: is_deleted
                "\"TYPE_CODE\" TEXT," + // 2: type_code
                "\"MARGIN\" REAL," + // 3: margin
                "\"DTM_UPD\" INTEGER);"); // 4: dtm_upd
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"MS_INDUSTRY\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, Industry entity) {
        stmt.clearBindings();
        stmt.bindLong(1, entity.getId());
 
        Integer is_deleted = entity.getIs_deleted();
        if (is_deleted != null) {
            stmt.bindLong(2, is_deleted);
        }
 
        String type_code = entity.getType_code();
        if (type_code != null) {
            stmt.bindString(3, type_code);
        }
 
        Double margin = entity.getMargin();
        if (margin != null) {
            stmt.bindDouble(4, margin);
        }
 
        java.util.Date dtm_upd = entity.getDtm_upd();
        if (dtm_upd != null) {
            stmt.bindLong(5, dtm_upd.getTime());
        }
    }

    /** @inheritdoc */
    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.getLong(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public Industry readEntity(Cursor cursor, int offset) {
        Industry entity = new Industry( //
            cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getInt(offset + 1), // is_deleted
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // type_code
            cursor.isNull(offset + 3) ? null : cursor.getDouble(offset + 3), // margin
            cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)) // dtm_upd
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, Industry entity, int offset) {
        entity.setId(cursor.getLong(offset + 0));
        entity.setIs_deleted(cursor.isNull(offset + 1) ? null : cursor.getInt(offset + 1));
        entity.setType_code(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setMargin(cursor.isNull(offset + 3) ? null : cursor.getDouble(offset + 3));
        entity.setDtm_upd(cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)));
     }
    
    /** @inheritdoc */
    @Override
    protected Long updateKeyAfterInsert(Industry entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    /** @inheritdoc */
    @Override
    public Long getKey(Industry entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
}
