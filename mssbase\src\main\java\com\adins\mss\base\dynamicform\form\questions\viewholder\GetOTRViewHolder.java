package com.adins.mss.base.dynamicform.form.questions.viewholder;

import android.app.ProgressDialog;
import android.os.AsyncTask;
import android.text.Editable;
import android.text.InputType;
import android.text.TextWatcher;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;
import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.dynamicform.Constant;
import com.adins.mss.base.dynamicform.DynamicFormActivity;
import com.adins.mss.base.dynamicform.form.ScrollingLinearLayoutManager;
import com.adins.mss.base.getotr.GetOTRApi;
import com.adins.mss.base.getotr.JsonRequestGetOTR;
import com.adins.mss.base.getotr.JsonResponseGetOTR;
import com.adins.mss.constant.Global;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.questiongenerator.QuestionBean;
import com.adins.mss.foundation.questiongenerator.form.QuestionView;
import org.apache.commons.lang3.StringUtils;
import java.util.HashMap;
import java.util.LinkedHashMap;

public class GetOTRViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {

    private FragmentActivity mActivity;
    private QuestionBean qBean;
    private TextView mQuestionLabel;
    private TextView mTextResult;
    private EditText mTextOTR;
    private Button mButtonOTR;
    private QuestionView mQuestionLayout;
    private String tmpTxt;

    public GetOTRViewHolder(View itemView, FragmentActivity mActivity) {
        super(itemView);
        this.mActivity = mActivity;
        this.mQuestionLayout = itemView.findViewById(R.id.questionGetOTRLayout);
        this.mQuestionLabel = itemView.findViewById(R.id.lblQuestionGetOTR);
        this.mTextResult = itemView.findViewById(R.id.txtResult);
        this.mButtonOTR = itemView.findViewById(R.id.btnGetOTRResult);
        this.mTextOTR = itemView.findViewById(R.id.txtGetOTR);
    }

    public void bind(final QuestionBean item, final int number) {

        qBean = item;
        String qLabel = number + ". " + qBean.getQuestion_label();
        mQuestionLabel.setText(qLabel);

        mTextResult.setCursorVisible(false);
        mTextResult.setEnabled(false);
        mTextOTR.setInputType(InputType.TYPE_CLASS_NUMBER);

        String answer = qBean.getAnswer();
        if (null != answer && !answer.isEmpty()) {
            mTextOTR.setText(Tool.separateThousand(answer));
            qBean.setReadOnly(true);
        } else {
            mTextOTR.setText("");
            qBean.setReadOnly(false);
        }
        qBean.setChange(true);
        mTextOTR.setEnabled(false);
        mTextResult.setEnabled(false);

        mButtonOTR.setOnClickListener(this);

        mTextOTR.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                if (qBean.getAnswer() != null) tmpTxt = Tool.separateThousand(qBean.getAnswer().trim());
                if (tmpTxt == null) tmpTxt = "";
            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {}

            @Override
            public void afterTextChanged(Editable editable) {
                qBean.setAnswer(mTextOTR.getText().toString().trim().replace(",", ""));
                qBean.setChange(true);
            }
        });
    }

    @Override public void onClick(View view) {
        int id = view.getId();

        if (id == R.id.btnGetOTRResult) {
            mTextResult.setText("");
            mTextResult.setHint("Press button get OTR");

            qBean.setAnswer("");
            qBean.setChange(true);

            HashMap<String, Object> mapValues = new LinkedHashMap<>();
            String [] choiceFilter = Tool.split(qBean.getChoice_filter(), Global.DELIMETER_DATA3);
            if (choiceFilter.length > 0) {
                for (String filter : choiceFilter) {
                    if ("".equalsIgnoreCase(filter)) {
                        break;
                    }
                    filter = filter.replace("{", "");
                    filter = filter.replace("}", "");

                    int idxOfOpenAbs = filter.indexOf("$");
                    if (idxOfOpenAbs != -1) {
                        String finalIdentifier = filter.substring(idxOfOpenAbs + 1);
                        if (finalIdentifier.equals(Global.IDF_LOGIN_ID)) {
                            String loginId = GlobalData.getSharedGlobalData().getUser().getLogin_id();
                            int idxOfOpenAt = loginId.indexOf('@');
                            if (idxOfOpenAt != -1) {
                                loginId = loginId.substring(0, idxOfOpenAt);
                            }
                            mapValues.put(filter, loginId);
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_ID)) {
                            String branchId = GlobalData.getSharedGlobalData().getUser().getBranch_id();
                            mapValues.put(filter, branchId);
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_NAME)) {
                            String branchName = GlobalData.getSharedGlobalData().getUser().getBranch_name();
                            mapValues.put(filter, branchName);
                        } else if (finalIdentifier.equals(Global.IDF_UUID_USER)) {
                            String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                            mapValues.put(filter, uuidUser);
                        } else if (finalIdentifier.equals(Global.IDF_JOB)) {
                            String job = GlobalData.getSharedGlobalData().getUser().getFlag_job();
                            mapValues.put(filter, job);
                        } else if (finalIdentifier.equals(Global.IDF_DEALER_NAME)) {
                            String dealerName = GlobalData.getSharedGlobalData().getUser().getDealer_name();
                            mapValues.put(filter, dealerName);
                        } else if (finalIdentifier.equals(Global.IDF_UUID_BRANCH)) {
                            String uuidBranch = GlobalData.getSharedGlobalData().getUser().getUuid_branch();
                            mapValues.put(filter, uuidBranch);
                        } else if (finalIdentifier.equals(Global.IDF_DEALER_ID)) {
                            String dealerId = GlobalData.getSharedGlobalData().getUser().getUuid_dealer();
                            mapValues.put(filter, dealerId);
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_TYPE)) {
                            String branchType = GlobalData.getSharedGlobalData().getUser().getBranch_type();
                            mapValues.put(filter, branchType);
                        } else if (finalIdentifier.equals(Global.IDF_TASK_ID)) {
                            String taskId = DynamicFormActivity.header.getTask_id();
                            mapValues.put(filter, taskId);
                        }
                    } else {
                        QuestionBean bean = Constant.listOfQuestion.get(filter);
                        if (null != bean) {
                            if (bean.isVisible()) {
                                if (Tool.isOptions(bean.getAnswer_type())) {
                                    if(bean.getLovCode()==null || "".equalsIgnoreCase(bean.getLovCode())) {
                                        if(bean.getSelectedOptionAnswers()!=null && !bean.getSelectedOptionAnswers().isEmpty()) {
                                            bean.setLovCode(bean.getSelectedOptionAnswers().get(0).getCode());
                                        }
                                    }
                                    mapValues.put(bean.getIdentifier_name(), bean.getLovCode());
                                } else {
                                    mapValues.put(filter, bean.getAnswer());
                                }
                            }
                        }
                    }
                }
            }

            qBean.setChange(true);
            String formName = DynamicFormActivity.header.getScheme().getScheme_description();
            GetOTRViewHolder.GetOTR getOTR = new GetOTRViewHolder.GetOTR(mapValues, formName);
            getOTR.execute();
        }
    }

    public class GetOTR extends AsyncTask<String, String, JsonResponseGetOTR> {
        private ProgressDialog progressDialog;
        private final String formName;
        private final HashMap<String, Object> mapValues;

        public GetOTR(HashMap<String, Object> mapValues, String formName) {
            this.mapValues = mapValues;
            this.formName = formName;
        }

        @Override
        protected  void onPreExecute() {
            super.onPreExecute();
            progressDialog = ProgressDialog.show(mActivity, "", mActivity.getString(R.string.progressWait),
                    true, false);
        }

        @Override
        protected JsonResponseGetOTR doInBackground(String... strings) {
            JsonResponseGetOTR response = new JsonResponseGetOTR();
            if (Tool.isInternetconnected(mActivity)) {
                JsonRequestGetOTR request = new JsonRequestGetOTR();
                request.setFormName(formName);
                request.setMapValues(mapValues);
                request.setAudit(GlobalData.getSharedGlobalData().getAuditData());

                GetOTRApi api = new GetOTRApi(mActivity);
                response = api.request(request);
            } else {
                JsonResponseGetOTR.Status status = new JsonResponseGetOTR.Status();
                status.setMessage(mActivity.getString(R.string.use_offline_mode));
                response.setStatus(status);
            }
            return response;
        }

        @Override
        protected void onPostExecute(JsonResponseGetOTR response) {
            super.onPostExecute(response);

            if (progressDialog.isShowing()) {
                progressDialog.dismiss();
            }
            if (response != null && response.getOtr() != null &&
                    !response.getOtr().isEmpty() && response.getStatus().getCode() == 0) {
                mTextOTR.setText(Tool.separateThousand(response.getOtr()));
                mTextOTR.setEnabled(false);
                qBean.setReadOnly(true);
                qBean.setAnswer(response.getOtr());
            } else {
                mTextOTR.setText("");
                mTextOTR.setEnabled(true);
                qBean.setReadOnly(false);
                qBean.setAnswer("");
            }
        }
    }
}
