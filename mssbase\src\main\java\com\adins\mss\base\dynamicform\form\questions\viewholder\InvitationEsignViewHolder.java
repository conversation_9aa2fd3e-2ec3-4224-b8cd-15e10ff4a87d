package com.adins.mss.base.dynamicform.form.questions.viewholder;

import static com.adins.mss.base.dynamicform.form.DynamicQuestionActivity.qBean;

import android.app.ProgressDialog;
import android.os.AsyncTask;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.dynamicform.Constant;
import com.adins.mss.base.dynamicform.DynamicFormActivity;
import com.adins.mss.base.invitationesign.InvitationEsignApi;
import com.adins.mss.base.invitationesign.JsonRequestInvitationEsign;
import com.adins.mss.constant.Global;
import com.adins.mss.foundation.dialog.NiftyDialogBuilder;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.MssResponseType;
import com.adins.mss.foundation.questiongenerator.QuestionBean;
import com.adins.mss.foundation.questiongenerator.form.QuestionView;

import java.util.HashMap;
import java.util.LinkedHashMap;

public class InvitationEsignViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {

    private QuestionView mView;
    private FragmentActivity mActivity;
    private TextView mQuestionLabel;
    private TextView mTextResult;
    private Button mButtonInvitation;
    private QuestionBean qBean;
    private static final int STATUS_CODE_VALIDATION_RESULT = 25;

    public InvitationEsignViewHolder(View itemView, FragmentActivity mActivity) {
        super(itemView);
        this.mActivity = mActivity;
        this.mView = itemView.findViewById(R.id.question_invitation_esign_layout);
        this.mQuestionLabel = itemView.findViewById(R.id.question_invitation_esign_label);
        this.mTextResult = itemView.findViewById(R.id.txt_result_esign);
        this.mButtonInvitation = itemView.findViewById(R.id.btn_invitation_esign);
    }

    public void bind(final QuestionBean item, final int number) {
        qBean = item;
        String qLabel = number + ". " + qBean.getQuestion_label();
        mQuestionLabel.setText(qLabel);

        mTextResult.setCursorVisible(false);
        mTextResult.setEnabled(false);

        String answer = qBean.getAnswer();
        if (null != answer && !answer.isEmpty()) {
            mTextResult.setText(answer);
        }
        mButtonInvitation.setOnClickListener(this);

        if (qBean.getCountRetry() >= qBean.getMaxRetry()) {
            mButtonInvitation.setEnabled(false);
            mButtonInvitation.setText(mActivity.getString(R.string.message_max_retry_reached));
        } else {
            mButtonInvitation.setEnabled(true);
            mButtonInvitation.setText(mActivity.getString(R.string.btn_invitation));
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();

        if (id == R.id.btn_invitation_esign) {
            qBean.setCountRetry(qBean.getCountRetry()+1);
            mTextResult.setText("");
            mTextResult.setHint(mActivity.getString(R.string.press_button_invitation));
            qBean.setAnswer("");

            HashMap<String, Object> mapValues = new LinkedHashMap<>();
            String[] choiceFilter = Tool.split(qBean.getChoice_filter(), Global.DELIMETER_DATA3);
            if (choiceFilter.length > 0) {
                for (String filter : choiceFilter) {
                    if ("".equalsIgnoreCase(filter)) {
                        break;
                    }
                    filter = filter.replace("{", "");
                    filter = filter.replace("}", "");

                    int idxOfOpenAbs = filter.indexOf("$");
                    if (idxOfOpenAbs != -1) {
                        String finalIdentifier = filter.substring(idxOfOpenAbs + 1);
                        if (finalIdentifier.equals(Global.IDF_LOGIN_ID)) {
                            String loginId = GlobalData.getSharedGlobalData().getUser().getLogin_id();
                            int idxOfOpenAt = loginId.indexOf('@');
                            if (idxOfOpenAt != -1) {
                                loginId = loginId.substring(0, idxOfOpenAt);
                            }
                            mapValues.put(filter, loginId);
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_ID)) {
                            String branchId = GlobalData.getSharedGlobalData().getUser().getBranch_id();
                            mapValues.put(filter, branchId);
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_NAME)) {
                            String branchName = GlobalData.getSharedGlobalData().getUser().getBranch_name();
                            mapValues.put(filter, branchName);
                        } else if (finalIdentifier.equals(Global.IDF_UUID_USER)) {
                            String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                            mapValues.put(filter, uuidUser);
                        } else if (finalIdentifier.equals(Global.IDF_JOB)) {
                            String job = GlobalData.getSharedGlobalData().getUser().getFlag_job();
                            mapValues.put(filter, job);
                        } else if (finalIdentifier.equals(Global.IDF_DEALER_NAME)) {
                            String dealerName = GlobalData.getSharedGlobalData().getUser().getDealer_name();
                            mapValues.put(filter, dealerName);
                        } else if (finalIdentifier.equals(Global.IDF_UUID_BRANCH)) {
                            String uuidBranch = GlobalData.getSharedGlobalData().getUser().getUuid_branch();
                            mapValues.put(filter, uuidBranch);
                        } else if (finalIdentifier.equals(Global.IDF_DEALER_ID)) {
                            String dealerId = GlobalData.getSharedGlobalData().getUser().getUuid_dealer();
                            mapValues.put(filter, dealerId);
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_TYPE)) {
                            String branchType = GlobalData.getSharedGlobalData().getUser().getBranch_type();
                            mapValues.put(filter, branchType);
                        } else if (finalIdentifier.equals(Global.IDF_TASK_ID)) {
                            String taskId = DynamicFormActivity.header.getTask_id();
                            mapValues.put(filter, taskId);
                        } else if (finalIdentifier.equals(Global.IDF_SOURCE_DATA)) {
                            String sourceData = DynamicFormActivity.header.getSource_data();
                            mapValues.put(filter, sourceData);
                        }
                    } else {
                        QuestionBean bean = Constant.listOfQuestion.get(filter);
                        if (null != bean) {
                            if (bean.isVisible()) {
                                if (Tool.isOptions(bean.getAnswer_type())) {
                                    if(bean.getLovCode()==null || "".equalsIgnoreCase(bean.getLovCode())) {
                                        if(bean.getSelectedOptionAnswers()!=null && !bean.getSelectedOptionAnswers().isEmpty()) {
                                            bean.setLovCode(bean.getSelectedOptionAnswers().get(0).getCode());
                                        }
                                    }
                                    mapValues.put(bean.getIdentifier_name(), bean.getLovCode());
                                } else {
                                    mapValues.put(filter, bean.getAnswer());
                                }
                            }
                        }
                    }
                }
            }
            String formName = DynamicFormActivity.header.getScheme().getScheme_description();

            GetInvitationEsign getInvitationEsign = new GetInvitationEsign(mapValues, formName);
            getInvitationEsign.execute();
        }
    }

    public class GetInvitationEsign extends AsyncTask<String, String, MssResponseType> {

        private ProgressDialog progressDialog;
        private final HashMap<String, Object> mapValues;
        private final String formName;

        public GetInvitationEsign(HashMap<String, Object> mapValues, String formName) {
            this.mapValues = mapValues;
            this.formName = formName;
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            progressDialog = ProgressDialog.show(mActivity, "", mActivity.getString(R.string.progressWait),
                    true, false);
        }

        @Override
        protected MssResponseType doInBackground(String... strings) {

            MssResponseType response = new MssResponseType();
            if (Tool.isInternetconnected(mActivity)) {
                JsonRequestInvitationEsign request = new JsonRequestInvitationEsign();
                request.setMapValues(mapValues);
                request.setFormName(formName);
                request.setAudit(GlobalData.getSharedGlobalData().getAuditData());

                InvitationEsignApi invitationEsignApi = new InvitationEsignApi(mActivity);
                response = invitationEsignApi.request(request);
            } else {
                MssResponseType.Status status = new MssResponseType.Status();
                status.setMessage(mActivity.getString(R.string.use_offline_mode));
                response.setStatus(status);
            }
            return response;
        }

        @Override
        protected void onPostExecute(MssResponseType response) {
            super.onPostExecute(response);

            if (progressDialog.isShowing()) {
                progressDialog.dismiss();
            }

            if (null != response) {
                String message = response.getStatus().getMessage();
                if (STATUS_CODE_VALIDATION_RESULT != response.getStatus().getCode()
                        && !mActivity.getString(R.string.use_offline_mode).equalsIgnoreCase(response.getStatus().getMessage())) {
                    mTextResult.setText(message);
                    qBean.setAnswer(message);
                } else if (mActivity.getString(R.string.use_offline_mode).equalsIgnoreCase(response.getStatus().getMessage())) {
                    mTextResult.setText(message);
                    qBean.setAnswer(message);
                    response.getStatus().setCode(0);
                }

                if (qBean.getCountRetry() >= qBean.getMaxRetry()) {
                    qBean.setIsValid(Global.TRUE_STRING);
                    mButtonInvitation.setEnabled(false);
                    mButtonInvitation.setText(mActivity.getString(R.string.message_max_retry_reached));
                } else if (response.getStatus().getCode() == 1) {
                    qBean.setIsValid(Global.FALSE_STRING);
                    mButtonInvitation.setEnabled(true);
                    mButtonInvitation.setText(mActivity.getString(R.string.btn_invitation));
                } else if (response.getStatus().getCode() == 0) {
                    qBean.setIsValid(Global.TRUE_STRING);
                    mButtonInvitation.setEnabled(true);
                    mButtonInvitation.setText(mActivity.getString(R.string.btn_invitation));
                }

                final NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(mActivity);
                dialogBuilder.withTitle(mActivity.getString(R.string.info_capital))
                        .withMessage(message)
                        .withButton1Text(mActivity.getString(R.string.btnClose))
                        .setButton1Click(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                dialogBuilder.dismiss();
                            }
                        })
                        .isCancelable(false)
                        .isCancelableOnTouchOutside(false)
                        .show();
            }
        }
    }
}