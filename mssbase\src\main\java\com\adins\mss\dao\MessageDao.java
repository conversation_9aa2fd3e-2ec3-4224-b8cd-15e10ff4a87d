package com.adins.mss.dao;

import java.util.List;
import java.util.ArrayList;
import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.SqlUtils;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;
import de.greenrobot.dao.query.Query;
import de.greenrobot.dao.query.QueryBuilder;

import com.adins.mss.dao.Message;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TR_MESSAGE".
*/
public class MessageDao extends AbstractDao<Message, String> {

    public static final String TABLENAME = "TR_MESSAGE";

    /**
     * Properties of entity Message.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_message = new Property(0, String.class, "uuid_message", true, "UUID_MESSAGE");
        public final static Property Message = new Property(1, String.class, "message", false, "MESSAGE");
        public final static Property Sender_id = new Property(2, String.class, "sender_id", false, "SENDER_ID");
        public final static Property Sender_name = new Property(3, String.class, "sender_name", false, "SENDER_NAME");
        public final static Property Dtm_crt_server = new Property(4, java.util.Date.class, "dtm_crt_server", false, "DTM_CRT_SERVER");
        public final static Property Time_read = new Property(5, java.util.Date.class, "time_read", false, "TIME_READ");
        public final static Property Usr_crt = new Property(6, String.class, "usr_crt", false, "USR_CRT");
        public final static Property Dtm_crt = new Property(7, java.util.Date.class, "dtm_crt", false, "DTM_CRT");
        public final static Property Uuid_user = new Property(8, String.class, "uuid_user", false, "UUID_USER");
    };

    private DaoSession daoSession;

    private Query<Message> user_MessageListQuery;

    public MessageDao(DaoConfig config) {
        super(config);
    }
    
    public MessageDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TR_MESSAGE\" (" + //
                "\"UUID_MESSAGE\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_message
                "\"MESSAGE\" TEXT," + // 1: message
                "\"SENDER_ID\" TEXT," + // 2: sender_id
                "\"SENDER_NAME\" TEXT," + // 3: sender_name
                "\"DTM_CRT_SERVER\" INTEGER," + // 4: dtm_crt_server
                "\"TIME_READ\" INTEGER," + // 5: time_read
                "\"USR_CRT\" TEXT," + // 6: usr_crt
                "\"DTM_CRT\" INTEGER," + // 7: dtm_crt
                "\"UUID_USER\" TEXT);"); // 8: uuid_user
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TR_MESSAGE\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, Message entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_message());
 
        String message = entity.getMessage();
        if (message != null) {
            stmt.bindString(2, message);
        }
 
        String sender_id = entity.getSender_id();
        if (sender_id != null) {
            stmt.bindString(3, sender_id);
        }
 
        String sender_name = entity.getSender_name();
        if (sender_name != null) {
            stmt.bindString(4, sender_name);
        }
 
        java.util.Date dtm_crt_server = entity.getDtm_crt_server();
        if (dtm_crt_server != null) {
            stmt.bindLong(5, dtm_crt_server.getTime());
        }
 
        java.util.Date time_read = entity.getTime_read();
        if (time_read != null) {
            stmt.bindLong(6, time_read.getTime());
        }
 
        String usr_crt = entity.getUsr_crt();
        if (usr_crt != null) {
            stmt.bindString(7, usr_crt);
        }
 
        java.util.Date dtm_crt = entity.getDtm_crt();
        if (dtm_crt != null) {
            stmt.bindLong(8, dtm_crt.getTime());
        }
 
        String uuid_user = entity.getUuid_user();
        if (uuid_user != null) {
            stmt.bindString(9, uuid_user);
        }
    }

    @Override
    protected void attachEntity(Message entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public Message readEntity(Cursor cursor, int offset) {
        Message entity = new Message( //
            cursor.getString(offset + 0), // uuid_message
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // message
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // sender_id
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // sender_name
            cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)), // dtm_crt_server
            cursor.isNull(offset + 5) ? null : new java.util.Date(cursor.getLong(offset + 5)), // time_read
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // usr_crt
            cursor.isNull(offset + 7) ? null : new java.util.Date(cursor.getLong(offset + 7)), // dtm_crt
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8) // uuid_user
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, Message entity, int offset) {
        entity.setUuid_message(cursor.getString(offset + 0));
        entity.setMessage(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setSender_id(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setSender_name(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setDtm_crt_server(cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)));
        entity.setTime_read(cursor.isNull(offset + 5) ? null : new java.util.Date(cursor.getLong(offset + 5)));
        entity.setUsr_crt(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setDtm_crt(cursor.isNull(offset + 7) ? null : new java.util.Date(cursor.getLong(offset + 7)));
        entity.setUuid_user(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(Message entity, long rowId) {
        return entity.getUuid_message();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(Message entity) {
        if(entity != null) {
            return entity.getUuid_message();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
    /** Internal query to resolve the "messageList" to-many relationship of User. */
    public List<Message> _queryUser_MessageList(String uuid_user) {
        synchronized (this) {
            if (user_MessageListQuery == null) {
                QueryBuilder<Message> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_user.eq(null));
                user_MessageListQuery = queryBuilder.build();
            }
        }
        Query<Message> query = user_MessageListQuery.forCurrentThread();
        query.setParameter(0, uuid_user);
        return query.list();
    }

    private String selectDeep;

    protected String getSelectDeep() {
        if (selectDeep == null) {
            StringBuilder builder = new StringBuilder("SELECT ");
            SqlUtils.appendColumns(builder, "T", getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T0", daoSession.getUserDao().getAllColumns());
            builder.append(" FROM TR_MESSAGE T");
            builder.append(" LEFT JOIN MS_USER T0 ON T.\"UUID_USER\"=T0.\"UUID_USER\"");
            builder.append(' ');
            selectDeep = builder.toString();
        }
        return selectDeep;
    }
    
    protected Message loadCurrentDeep(Cursor cursor, boolean lock) {
        Message entity = loadCurrent(cursor, 0, lock);
        int offset = getAllColumns().length;

        User user = loadCurrentOther(daoSession.getUserDao(), cursor, offset);
        entity.setUser(user);

        return entity;    
    }

    public Message loadDeep(Long key) {
        assertSinglePk();
        if (key == null) {
            return null;
        }

        StringBuilder builder = new StringBuilder(getSelectDeep());
        builder.append("WHERE ");
        SqlUtils.appendColumnsEqValue(builder, "T", getPkColumns());
        String sql = builder.toString();
        
        String[] keyArray = new String[] { key.toString() };
        Cursor cursor = db.rawQuery(sql, keyArray);
        
        try {
            boolean available = cursor.moveToFirst();
            if (!available) {
                return null;
            } else if (!cursor.isLast()) {
                throw new IllegalStateException("Expected unique result, but count was " + cursor.getCount());
            }
            return loadCurrentDeep(cursor, true);
        } finally {
            cursor.close();
        }
    }
    
    /** Reads all available rows from the given cursor and returns a list of new ImageTO objects. */
    public List<Message> loadAllDeepFromCursor(Cursor cursor) {
        int count = cursor.getCount();
        List<Message> list = new ArrayList<Message>(count);
        
        if (cursor.moveToFirst()) {
            if (identityScope != null) {
                identityScope.lock();
                identityScope.reserveRoom(count);
            }
            try {
                do {
                    list.add(loadCurrentDeep(cursor, false));
                } while (cursor.moveToNext());
            } finally {
                if (identityScope != null) {
                    identityScope.unlock();
                }
            }
        }
        return list;
    }
    
    protected List<Message> loadDeepAllAndCloseCursor(Cursor cursor) {
        try {
            return loadAllDeepFromCursor(cursor);
        } finally {
            cursor.close();
        }
    }
    

    /** A raw-style query where you can pass any WHERE clause and arguments. */
    public List<Message> queryDeep(String where, String... selectionArg) {
        Cursor cursor = db.rawQuery(getSelectDeep() + where, selectionArg);
        return loadDeepAllAndCloseCursor(cursor);
    }
 
}
