package com.adins.mss.dao;

import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;

import com.adins.mss.dao.PODealer;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "MS_PO_DEALER".
*/
public class PODealerDao extends AbstractDao<PODealer, Long> {

    public static final String TABLENAME = "MS_PO_DEALER";

    /**
     * Properties of entity PODealer.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Id = new Property(0, long.class, "id", true, "ID");
        public final static Property Dealer_id = new Property(1, String.class, "dealer_id", false, "DEALER_ID");
        public final static Property Dealer_scheme_id = new Property(2, int.class, "dealer_scheme_id", false, "DEALER_SCHEME_ID");
        public final static Property Dealer_name = new Property(3, String.class, "dealer_name", false, "DEALER_NAME");
        public final static Property Dtm_upd = new Property(4, java.util.Date.class, "dtm_upd", false, "DTM_UPD");
        public final static Property Is_deleted = new Property(5, Integer.class, "is_deleted", false, "IS_DELETED");
    };


    public PODealerDao(DaoConfig config) {
        super(config);
    }
    
    public PODealerDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"MS_PO_DEALER\" (" + //
                "\"ID\" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL ," + // 0: id
                "\"DEALER_ID\" TEXT NOT NULL ," + // 1: dealer_id
                "\"DEALER_SCHEME_ID\" INTEGER NOT NULL ," + // 2: dealer_scheme_id
                "\"DEALER_NAME\" TEXT," + // 3: dealer_name
                "\"DTM_UPD\" INTEGER," + // 4: dtm_upd
                "\"IS_DELETED\" INTEGER);"); // 5: is_deleted
        // Add Indexes
        db.execSQL("CREATE INDEX " + constraint + "IDX_MS_PO_DEALER_DEALER_ID ON MS_PO_DEALER" +
                " (\"DEALER_ID\");");
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"MS_PO_DEALER\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, PODealer entity) {
        stmt.clearBindings();
        stmt.bindLong(1, entity.getId());
        stmt.bindString(2, entity.getDealer_id());
        stmt.bindLong(3, entity.getDealer_scheme_id());
 
        String dealer_name = entity.getDealer_name();
        if (dealer_name != null) {
            stmt.bindString(4, dealer_name);
        }
 
        java.util.Date dtm_upd = entity.getDtm_upd();
        if (dtm_upd != null) {
            stmt.bindLong(5, dtm_upd.getTime());
        }
 
        Integer is_deleted = entity.getIs_deleted();
        if (is_deleted != null) {
            stmt.bindLong(6, is_deleted);
        }
    }

    /** @inheritdoc */
    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.getLong(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public PODealer readEntity(Cursor cursor, int offset) {
        PODealer entity = new PODealer( //
            cursor.getLong(offset + 0), // id
            cursor.getString(offset + 1), // dealer_id
            cursor.getInt(offset + 2), // dealer_scheme_id
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // dealer_name
            cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)), // dtm_upd
            cursor.isNull(offset + 5) ? null : cursor.getInt(offset + 5) // is_deleted
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, PODealer entity, int offset) {
        entity.setId(cursor.getLong(offset + 0));
        entity.setDealer_id(cursor.getString(offset + 1));
        entity.setDealer_scheme_id(cursor.getInt(offset + 2));
        entity.setDealer_name(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setDtm_upd(cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)));
        entity.setIs_deleted(cursor.isNull(offset + 5) ? null : cursor.getInt(offset + 5));
     }
    
    /** @inheritdoc */
    @Override
    protected Long updateKeyAfterInsert(PODealer entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    /** @inheritdoc */
    @Override
    public Long getKey(PODealer entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
}
