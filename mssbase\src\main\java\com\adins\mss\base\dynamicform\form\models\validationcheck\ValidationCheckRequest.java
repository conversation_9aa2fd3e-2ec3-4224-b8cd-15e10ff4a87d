package com.adins.mss.base.dynamicform.form.models.validationcheck;

import com.adins.mss.dao.TaskH;
import com.adins.mss.foundation.http.MssRequestType;
import com.google.gson.annotations.SerializedName;

import java.util.List;
import java.util.Map;

public class ValidationCheckRequest extends MssRequestType {

    @SerializedName("countRetry")
    private String countRetry;
    @SerializedName("filter")
    private Map<String, Map<String, String>> filter;
    @SerializedName("questionGroup")
    private List<ValidationCheckBean> validationCheckBeanList;
    @SerializedName("taskH")
    private TaskH taskH;

    public String getCountRetry() {
        return countRetry;
    }

    public void setCountRetry(String countRetry) {
        this.countRetry = countRetry;
    }

    public Map<String, Map<String, String>> getFilter() {
        return filter;
    }

    public void setFilter(Map<String, Map<String, String>> filter) {
        this.filter = filter;
    }

    public List<ValidationCheckBean> getValidationCheckBeanList() {
        return validationCheckBeanList;
    }

    public void setValidationCheckBeanList(List<ValidationCheckBean> validationCheckBeanList) {
        this.validationCheckBeanList = validationCheckBeanList;
    }

    public TaskH getTaskH() {
        return taskH;
    }

    public void setTaskH(TaskH taskH) {
        this.taskH = taskH;
    }

}
