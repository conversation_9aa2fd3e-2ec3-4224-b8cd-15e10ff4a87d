package com.adins.mss.base.todolist.form;


import android.app.ActionBar;
import android.content.Context;
import android.os.Bundle;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import android.text.format.DateFormat;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.dynamicform.CustomerFragment;
import com.adins.mss.base.dynamicform.SurveyHeaderBean;
import com.adins.mss.base.mainmenu.MainMenuActivity;
import com.adins.mss.base.todolist.ToDoList;
import com.adins.mss.base.util.Utility;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.Scheme;
import com.adins.mss.dao.TaskH;
import com.adins.mss.dao.TaskHSequence;
import com.adins.mss.foundation.db.dataaccess.SchemeDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskHDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskHSequenceDataAccess;
import com.adins.mss.foundation.dialog.DialogManager;

import org.acra.ACRA;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * A simple {@link Fragment} subclass.
 * Use the {@link AllHeaderViewerFragment#newInstance} factory method to
 * create an instance of this fragment.
 */
public class AllHeaderViewerFragment extends Fragment implements AdapterView.OnItemClickListener {
    // TODO: Rename parameter arguments, choose names that match
    // the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
    public static final String REQ_PRIORITY_LIST = "REQ_PRIORITY_LIST";
    public static final String REQ_LOG_LIST = "REQ_LOG_LIST";
    public static final String REQ_STATUS_LIST = "REQ_STATUS_LIST";
    public static final String BUND_KEY_REQ = "BUND_KEY_REQ";

    private ListView listView;
    private LinearLayout layoutView;
    TextView dataNotFound;
    private TaskHeaderAdapter listAdapter;


    private String keyRequest;

    public AllHeaderViewerFragment() {
        // Required empty public constructor
    }

    /**
     * Use this factory method to create a new instance of
     * this fragment using the provided parameters.
     *
     * @param keyRequest Parameter 1.
     * @return A new instance of fragment BlankFragment.
     */
    // TODO: Rename and change types and number of parameters
    public static AllHeaderViewerFragment newInstance(String keyRequest) {
        AllHeaderViewerFragment fragment = new AllHeaderViewerFragment();
        Bundle args = new Bundle();
        args.putString(BUND_KEY_REQ, keyRequest);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            keyRequest = getArguments().getString(BUND_KEY_REQ);
        }
        ACRA.getErrorReporter().putCustomData("LAST_CLASS_ACCESSED", getClass().getSimpleName());
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {

        View view = inflater.inflate(R.layout.all_header_viewer_layout, container, false);

        Button btnCreatePlan = (Button) view.findViewById(R.id.btnCreatePlan);

        if (Global.APPLICATION_ORDER.equalsIgnoreCase(GlobalData.getSharedGlobalData().getApplication()) || Global.APPLICATION_COLLECTION.equalsIgnoreCase(GlobalData.getSharedGlobalData().getApplication())) {
            btnCreatePlan.setVisibility(View.GONE);
        }

        btnCreatePlan.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Fragment fragment = new SurveyPlanFragment();
                FragmentTransaction transaction = MainMenuActivity.fragmentManager.beginTransaction();
                transaction.setCustomAnimations(R.anim.activity_open_translate, R.anim.activity_close_scale, R.anim.activity_open_scale, R.anim.activity_close_translate);
                transaction.replace(R.id.content_frame, fragment);
                transaction.addToBackStack(null);
                transaction.commit();
            }
        });

        return view;
    }
    @Override
    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        listView = (ListView) view.findViewById(android.R.id.list);
        layoutView = (LinearLayout) view.findViewById(R.id.layoutView);
        dataNotFound = (TextView) view.findViewById(R.id.txv_data_not_found);
        loadListView();
    }

    public void loadListView() {
        List<TaskH> taskHList = null;
        try {
            if (keyRequest != null) {
                if (REQ_PRIORITY_LIST.equals(keyRequest))
                    taskHList = ToDoList.getListTaskInPriority(getActivity(), 0, null);
                else if (REQ_STATUS_LIST.equals(keyRequest))
                    taskHList = TaskHDataAccess.getAllTaskInStatus(getActivity(), GlobalData.getSharedGlobalData().getUser().getUuid_user());
                else if (REQ_LOG_LIST.equals(keyRequest))
                    taskHList = TaskHDataAccess.getAllSentTask(getActivity(), GlobalData.getSharedGlobalData().getUser().getUuid_user());
                else
                    taskHList = new ArrayList<TaskH>();
            } else {
                taskHList = new ArrayList<TaskH>();
            }
            List<TaskHSequence> taskHSequences = TaskHSequenceDataAccess.getAllOrderAsc(getContext());
            List<TaskH> taskHLists = new ArrayList<>();
            if (taskHSequences.isEmpty()) {
                TaskHSequenceDataAccess.insertAllNewTaskHSeq(getContext(), taskHList);
                taskHSequences = TaskHSequenceDataAccess.getAllOrderAsc(getContext());

            }
            for (int i = 0; i < taskHSequences.size(); i++) {
                taskHLists.add(taskHSequences.get(i).getTaskH());
            }
            taskHList = taskHLists;
            listAdapter = new TaskHeaderAdapter(getActivity(), taskHList);
            listView.setAdapter(listAdapter);
            listView.setOnItemClickListener(this);
            if (taskHList == null || taskHList.isEmpty()) {
                dataNotFound.setVisibility(View.VISIBLE);
                layoutView.setBackgroundResource(R.drawable.bg_notfound);
            }
        } catch (Exception e) {
            FireCrash.log(e);
            ACRA.getErrorReporter().putCustomData("errorGetListTaskH", e.getMessage());
            ACRA.getErrorReporter().putCustomData("errorGetListTaskH", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
            ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat Get List TaskH"));
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        getActivity().getActionBar().removeAllTabs();
        getActivity().getActionBar().setTitle(getString(R.string.title_mn_tasklist));
        getActivity().getActionBar().setNavigationMode(ActionBar.NAVIGATION_MODE_STANDARD);
        loadListView();
        DialogManager.showTimeProviderAlert(getActivity());
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        try {
            TaskH taskH = (TaskH) parent.getAdapter().getItem(position);
            Scheme scheme = taskH.getScheme();
            if (scheme == null) {
                if (taskH.getUuid_scheme() != null) {
                    scheme = SchemeDataAccess.getOne(getActivity(),
                            taskH.getUuid_scheme());
                    if (scheme != null)
                        taskH.setScheme(scheme);
                }
            }

            if (scheme == null) {
                Toast.makeText(getActivity(), getActivity().getString(R.string.task_cant_seen),
                        Toast.LENGTH_SHORT).show();
            } else {
                SurveyHeaderBean header = new SurveyHeaderBean(taskH);
                CustomerFragment fragment = CustomerFragment.create(header);
                FragmentTransaction transaction = MainMenuActivity.fragmentManager.beginTransaction();
                transaction.setCustomAnimations(R.anim.activity_open_translate, R.anim.activity_close_scale, R.anim.activity_open_scale, R.anim.activity_close_translate);
                transaction.replace(R.id.content_frame, fragment);
                transaction.addToBackStack(null);
                transaction.commit();
            }
        } catch (Exception e) {
            FireCrash.log(e);
            ACRA.getErrorReporter().putCustomData("errorGetScheme", e.getMessage());
            ACRA.getErrorReporter().putCustomData("errorGetSchemeTime", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
            ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat Get Scheme"));
            String message = getActivity().getString(R.string.task_cant_seen2) + " " + e.getMessage();
            Toast.makeText(getActivity(), message, Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Utility.freeMemory();
    }

    public class TaskHeaderAdapter extends ArrayAdapter<TaskH> {
        public List<TaskH> listTaskH2;
        private Context mContext;
        private List<TaskH> listTaskH;

        public TaskHeaderAdapter(Context c, List<TaskH> listTaskH) {
            super(c, R.layout.all_header_viewer_item, listTaskH);
            mContext = c;
            this.listTaskH = listTaskH;
            this.listTaskH2 = listTaskH;
        }

        @Override
        public int getCount() {
            // return mThumbIds.length;
            return listTaskH.size();
        }

        @Override
        public long getItemId(int position) {
            return 0;
        }

        // create a new ImageView for each item referenced by the Adapter
        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            LayoutInflater inflater = (LayoutInflater) mContext
                    .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            View v;
            v = inflater.inflate(R.layout.all_header_viewer_item, null);
            TextView txtPriority = (TextView) v.findViewById(R.id.txtPriority);
            TextView txtCustName = (TextView) v.findViewById(R.id.txtCustName);
            TextView txtCustAddress = (TextView) v.findViewById(R.id.txtCustAddress);
            TextView txtCustPhone = (TextView) v.findViewById(R.id.txtCustPhone);
            TextView txtNotes = (TextView) v.findViewById(R.id.txtNotes);
            TaskH taskH = listTaskH.get(position);

            txtPriority.setText(taskH.getPriority());
            txtCustName.setText(taskH.getCustomer_name());
            txtCustAddress.setText(taskH.getCustomer_address());
            txtCustPhone.setText(taskH.getCustomer_phone());
            txtNotes.setText(taskH.getNotes());

            return v;
        }
    }
}
