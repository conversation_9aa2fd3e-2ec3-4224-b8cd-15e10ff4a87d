package com.adins.mss.base.tracking;

import android.content.Context;

import com.adins.mss.base.GlobalData;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.LocationInfo;
import com.adins.mss.foundation.camerainapp.helper.Logger;
import com.adins.mss.foundation.db.dataaccess.LocationInfoDataAccess;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.services.ScheduledConnectionItem;
import com.adins.mss.foundation.services.ScheduledItem.ScheduledItemHandler;

import java.util.ArrayList;
import java.util.List;

/**
 * Default ScheduledConnectionItem for location tracking. It automatically query locations info from database and try to send it over
 * Internet connection to server
 *
 * <AUTHOR>
 */
public class LocationTrackingSchedule extends ScheduledConnectionItem implements ScheduledItemHandler {

    Context context;
    List<LocationInfo> processedList;

    /**
     * @param context
     * @param id       of ScheduledItem to be registered to AutoSendService
     * @param interval time delay between trigger
     * @param url      target URL
     * @param encrypt  need of encryption
     * @param decrypt  need of decryption
     */
    public LocationTrackingSchedule(Context context, String id, int interval, String url, boolean encrypt, boolean decrypt) {
        super(id, interval, url, encrypt, decrypt);
        this.context = context;
        this.url = url;
        this.setHandler(this);

        this.processedList = new ArrayList<LocationInfo>();
    }

//	@Override
//	public void onEventTrigger(ScheduledItem schItem) {
//		Logger.d("LocationTracking", "event triggered!");
//		
//		HttpCryptedConnection conn = new HttpCryptedConnection(encrypt, decrypt);
//		HttpConnectionResult result = null;
//		try {
//			result = conn.requestHTTPPost(url, jsonString);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		
//		if (result != null && result.getStatusCode() == 200){
//			//success
//			//delete from db
//		}
//	}

    @Override
    protected String getData() {
        // glen
//		//Fetch location's from db
//		processedList.clear();			//make sure it really is cleared
//		List<LocationInfo> locations = LocationInfoDataAccess.getAll(context, user_uuid);
//		processedList.addAll(locations);
//		//create json object to convert to string
//		//add additional data like imei etc
//		LocationJsonModel jsonObj = new LocationJsonModel(true, locations);

//		String json = Formatter.getJsonFromObject(jsonObj);


        // bong 4 march 15 - replace get data
        // int interval = Global.MINUTE * 5;
        //	LocationTrackingRequestJson ltj = new LocationTrackingRequestJson();

        //get location
//		TelephonyManager tm = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
//		LocationManager lm = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);
//		LocationTrackingManager ltm = new LocationTrackingManager(tm, lm);
//		ltm.setMinimalDistanceChangeLocation(Integer.parseInt(GeneralParameterDataAccess.getOne(context, GlobalData.getSharedGlobalData().getUser().getUuid_user(), "PRM13_DIST").getGs_value()));

        //add to database
        LocationInfoDataAccess.add(context, Global.LTM.getCurrentLocation(Global.FLAG_LOCATION_TRACKING));
        //prepare list
        //	ltj.clearLocationInfoList();
        //retrieve from db
        //	ltj.addLocationInfoList(LocationInfoDataAccess.getAll(context, GlobalData.getSharedGlobalData().getUser().getUuid_user()));
        //	String json = new Gson().toJson(ltj);
        String json = "";
        return json;
    }

    @Override
    protected boolean onSuccess(HttpConnectionResult result) {
        // TODO delete from db
//		LocationInfoDataAccess.deleteAll();
        LocationInfoDataAccess.deleteList(context, processedList);
        processedList.clear();
        Logger.d("LocationTracking", "success connection");
        return false;
    }

    @Override
    protected boolean onFail(HttpConnectionResult result) {
        processedList.clear();
        Logger.d("LocationTracking", "fail connection");
        return false;
    }


}
