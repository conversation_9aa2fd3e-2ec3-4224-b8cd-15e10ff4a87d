package com.adins.mss.base;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.adins.mss.base.authentication.LoginUserResponse;
import com.adins.mss.base.commons.DividerItemDecoration;
import com.adins.mss.base.util.GsonHelper;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import java.util.List;

/**
 * <p>A fragment that shows a list of items as a modal bottom sheet.</p>
 * <p>You can show this modal bottom sheet from your activity like this:</p>
 * <pre>
 *     BranchListDialogFragment.newInstance(30).show(getSupportFragmentManager(), "dialog");
 * </pre>
 * <p>You activity (or fragment) needs to implement {@link BranchListDialogFragment.Listener}.</p>
 */
public class BranchListDialogFragment extends BottomSheetDialogFragment {

    // TODO: Customize parameter argument names
    private static final String ARG_ITEM_COUNT = "item_count";
    private Listener mListener;
    private List<LoginUserResponse.LoginId> loginIds;

    // TODO: Customize parameters
    public static BranchListDialogFragment newInstance(int itemCount) {
        final BranchListDialogFragment fragment = new BranchListDialogFragment();
        final Bundle args = new Bundle();
        args.putInt(ARG_ITEM_COUNT, itemCount);
        fragment.setArguments(args);
        return fragment;
    }

    public static BranchListDialogFragment newInstance(Bundle args) {
        final BranchListDialogFragment fragment = new BranchListDialogFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        if (getArguments() != null) {
            LoginUserResponse loginUser = GsonHelper.fromJson(getArguments().getString("data"), LoginUserResponse.class);
            loginIds = loginUser.getListLoginId();
        }
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_branch_list_dialog, container, false);
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        final RecyclerView recyclerView = (RecyclerView) view.findViewById(R.id.list);
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerView.setAdapter(new BranchAdapter(loginIds));

        RecyclerView.ItemDecoration itemDecoration = new DividerItemDecoration(getContext(), DividerItemDecoration.VERTICAL_LIST);
        recyclerView.addItemDecoration(itemDecoration);
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        final Fragment parent = getParentFragment();
        if (parent != null) {
            mListener = (Listener) parent;
        } else {
            mListener = (Listener) context;
        }
    }

    @Override
    public void onDetach() {
        mListener = null;
        super.onDetach();
    }

    public interface Listener {
        void onBranchClicked(LoginUserResponse.LoginId loginId, int position);
    }

    private class ViewHolder extends RecyclerView.ViewHolder {

        final TextView text;

        ViewHolder(LayoutInflater inflater, ViewGroup parent) {
            // TODO: Customize the item layout
            super(inflater.inflate(R.layout.fragment_branch_list_dialog_item, parent, false));
            text = (TextView) itemView.findViewById(R.id.text);
//            text.setOnClickListener(new View.OnClickListener() {
//                @Override
//                public void onClick(View v) {
//                    if (mListener != null) {
//                        mListener.onBranchClicked(getAdapterPosition());
//                        dismiss();
//                    }
//                }
//            });
        }

    }

    private class BranchAdapter extends RecyclerView.Adapter<ViewHolder> {
        private List<LoginUserResponse.LoginId> loginIds;

        BranchAdapter(List<LoginUserResponse.LoginId> loginIds) {
            this.loginIds = loginIds;
        }

        @Override
        public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
            return new ViewHolder(LayoutInflater.from(parent.getContext()), parent);
        }

        @Override
        public void onBindViewHolder(ViewHolder holder, final int position) {
            final LoginUserResponse.LoginId loginId = loginIds.get(position);
            holder.text.setText(loginId.getBranchId());

            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    mListener.onBranchClicked(loginId, position);
                }
            });
        }

        @Override
        public int getItemCount() {
            return (loginIds == null) ? 0 : loginIds.size();
        }

    }

}
