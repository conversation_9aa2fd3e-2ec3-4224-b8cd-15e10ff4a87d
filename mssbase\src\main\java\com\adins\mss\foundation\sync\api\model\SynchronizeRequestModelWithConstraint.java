package com.adins.mss.foundation.sync.api.model;

/**
 * Created by gigin.ginanjar on 23/03/2016.
 */
public class SynchronizeRequestModelWithConstraint extends SynchronizeRequestModel {
    public String constraint1;
    public String constraint2;
    public String constraint3;
    public String constraint4;
    public String constraint5;

    public String getConstraint1() {
        return constraint1;
    }

    public void setConstraint1(String constraint1) {
        this.constraint1 = constraint1;
    }

    public String getConstraint2() {
        return constraint2;
    }

    public void setConstraint2(String constraint2) {
        this.constraint2 = constraint2;
    }

    public String getConstraint3() {
        return constraint3;
    }

    public void setConstraint3(String constraint3) {
        this.constraint3 = constraint3;
    }

    public String getConstraint4() {
        return constraint4;
    }

    public void setConstraint4(String constraint4) {
        this.constraint4 = constraint4;
    }

    public String getConstraint5() {
        return constraint5;
    }

    public void setConstraint5(String constraint5) {
        this.constraint5 = constraint5;
    }
}
