package com.adins.mss.foundation.print;

import com.adins.mss.dao.PrintDate;
import com.adins.mss.foundation.http.MssRequestType;

import java.util.List;

/**
 * Created by angga.permadi on 3/3/2016.
 */
public class SubmitPrintRequest extends MssRequestType {
    private List<PrintDate> listPrint;

    public List<PrintDate> getListPrint() {
        return listPrint;
    }

    public void setListPrint(List<PrintDate> listPrint) {
        this.listPrint = listPrint;
    }
}
