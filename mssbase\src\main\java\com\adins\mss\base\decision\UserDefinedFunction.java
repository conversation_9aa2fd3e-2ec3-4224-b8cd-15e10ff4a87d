package com.adins.mss.base.decision;

import org.apache.commons.lang3.StringUtils;

public class UserDefinedFunction {
	public static boolean like(CharSequence comparison, CharSequence criteria) {
		final char wildcard = '%';		
		
		if (comparison == null && (criteria == null || criteria.length() == 0))
			return true;
		
		if (comparison != null && (criteria == null || criteria.length() == 0 ))
			return false;
		
		if (criteria.charAt(0) == wildcard && criteria.length() == 1) {
			return true;
		}
		//contains (%criteria%)
		else if ((criteria.charAt(0) == wildcard) && (criteria.charAt(criteria.length() - 1) == wildcard)) {
			return StringUtils.contains(comparison, criteria.subSequence(1, criteria.length() - 1));
		}
		//endWith (%criteria)
		else if (criteria.charAt(0) == wildcard) {
			return StringUtils.endsWith(comparison, criteria.subSequence(1, criteria.length()));
		}
		//startWith (criteria%)
		else if (criteria.charAt(criteria.length() - 1) == wildcard) {
			return StringUtils.startsWith(comparison, criteria.subSequence(0, criteria.length() - 1));
		}
		
		return StringUtils.equals(comparison, criteria);
	}
}
