package com.adins.mss.base.taskupdate.activity;

import android.app.ActionBar;
import android.app.ProgressDialog;
import android.content.Context;
import android.graphics.drawable.GradientDrawable;
import android.os.AsyncTask;
import android.os.Bundle;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import android.text.format.DateFormat;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.GridView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.dynamicform.CustomerFragment;
import com.adins.mss.base.dynamicform.SurveyHeaderBean;
import com.adins.mss.base.mainmenu.MainMenuActivity;
import com.adins.mss.base.taskupdate.adapter.TaskUpdateAdapter;
import com.adins.mss.base.taskupdate.model.helper.TaskUpdateHelper;
import com.adins.mss.base.util.Utility;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.Scheme;
import com.adins.mss.dao.TaskUpdate;
import com.adins.mss.foundation.camerainapp.helper.Logger;
import com.adins.mss.foundation.db.dataaccess.SchemeDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskUpdateDataAccess;
import com.adins.mss.foundation.dialog.NiftyDialogBuilder;

import org.acra.ACRA;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

public class TaskUpdateActivity extends Fragment {

    public static final String STATUS_TASK_UPDATE = "STATUS_TASK_UPDATE";
    public static final String TAG_CUST_HEAD_TU = "GO TO CUSTOMER HEADER";

    private View view;
    private List<TaskUpdate> objects;
    private TaskUpdate selectedObjects;
    private TaskUpdateAdapter adapter;

    private RelativeLayout layoutView;
    private TextView dataNotFound;
    private GridView gridView;
    private OnItemClickListener onItemClickListener;

    private static Menu mainMenu;

    public TaskUpdateActivity() {
        // Required empty public constructor
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        view = inflater.inflate(R.layout.task_update_layout, container, false);
        gridView = (GridView) view.findViewById(R.id.gridTaskUpdate);
        layoutView = (RelativeLayout) view.findViewById(R.id.layoutView);
        dataNotFound = (TextView) view.findViewById(R.id.txv_data_not_found);

        new GetTaskUpdateData().execute();
        return view;
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        setHasOptionsMenu(true);
        getActivity().getActionBar().setTitle(getString(R.string.title_mn_task_update));
        try {
            objects = Global.listOfTaskUpdate;

            GradientDrawable gradient = new GradientDrawable(
                    GradientDrawable.Orientation.TOP_BOTTOM,
                    new int[]{0xFF616261, 0xFF131313});
            gradient.setCornerRadius(0f);

            adapter = new TaskUpdateAdapter(getActivity(), objects);

        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: handle exception
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Utility.freeMemory();
    }

    @Override
    public void onResume() {
        super.onResume();
        getActivity().getActionBar().removeAllTabs();
        getActivity().getActionBar().setTitle(getString(R.string.title_mn_task_update));
        getActivity().getActionBar().setNavigationMode(ActionBar.NAVIGATION_MODE_STANDARD);
        MainMenuActivity.setDrawerPosition(getString(R.string.title_mn_task_update));
        try {
//            updateList();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onPrepareOptionsMenu(Menu menu) {
        super.onPrepareOptionsMenu(menu);
        mainMenu = menu;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // Handle item selection
        int id = item.getItemId();
        if (id == R.id.menuMore) {
            mainMenu.findItem(R.id.mnViewMap).setVisible(false);
            mainMenu.findItem(R.id.mnViewAllHeader).setVisible(false);
        }

        return super.onOptionsItemSelected(item);
    }

    public class GetTaskUpdateData extends AsyncTask<Void, Void, List<TaskUpdate>> {

        private ProgressDialog progressDialog;

        @Override
        protected void onPreExecute() {
            progressDialog = ProgressDialog.show(getActivity(),
                    "", getString(R.string.progressWait), true);
        }

        @Override
        protected List<TaskUpdate> doInBackground(Void... params) {
            List<TaskUpdate> result = new ArrayList<>();
            List<TaskUpdate> onlineList = TaskUpdateHelper.getTaskUpdateList(getActivity());
            try {
                if (null != onlineList || !onlineList.isEmpty()) {
                    for (TaskUpdate response : onlineList) {
                        result.add(response);
                    }
                }
            }
            catch (Exception e) {
                FireCrash.log(e);
                Logger.i("INFO", "NO DATA");
            }
            return result;
        }

        @Override
        protected void onPostExecute(List<TaskUpdate> taskUpdates) {
            super.onPostExecute(taskUpdates);
            if (getActivity() != null) {
                if (progressDialog.isShowing()) {
                    try {
                        progressDialog.dismiss();
                    } catch (Exception e) {
                        FireCrash.log(e);
                    }
                }
                if (taskUpdates == null || taskUpdates.isEmpty()) {
                    NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(getActivity());
                    dialogBuilder.withTitle(getActivity().getString(R.string.info_capital))
                            .withMessage(getActivity().getString(R.string.msgNoTaskUpdate))
                            .show();
                }

                objects.clear();
                objects = taskUpdates;
                TaskUpdateDataAccess.addOrReplace(getActivity(), objects);
                Logger.i("INFO", String.valueOf(TaskUpdateDataAccess.getTaskUpdateCounterByUser(getActivity(), GlobalData.getSharedGlobalData().getUser().getUuid_user())));
                adapter = new TaskUpdateAdapter(getActivity(), objects);
                gridView.setAdapter(adapter);
                onItemClickListener = new OnItemClickListener() {
                    @Override
                    public void onItemClick(AdapterView<?> adapterView, View view, int position, long l) {
                        selectedObjects = adapter.getItem(position);
                        if (null != selectedObjects) {
                            Scheme scheme = null;
                            scheme = SchemeDataAccess.getOne(getActivity(),
                                    selectedObjects.getUuid_scheme());
                            if (scheme == null) {
                                Toast.makeText(getActivity(), getActivity().getString(R.string.task_cant_seen),
                                        Toast.LENGTH_SHORT).show();
                            }
                            else {
                                SurveyHeaderBean header = new SurveyHeaderBean();
                                header.setScheme(scheme);
                                header.setCustomer_name(selectedObjects.getCustomer_name());
                                header.setCustomer_phone(selectedObjects.getCustomer_phone());
                                header.setCustomer_address(selectedObjects.getCustomer_address());
                                header.setNotes(selectedObjects.getNotes());
                                header.setStatus(STATUS_TASK_UPDATE);
                                header.setUuid_task_update(selectedObjects.getUuid_task_update());
                                header.setPending_notes(selectedObjects.getPending_notes());
                                header.setDocupro_feedback(selectedObjects.getDocupro_feedback());
                                header.setCategory(selectedObjects.getCategory());
                                header.setSub_category(selectedObjects.getSub_category());
                                header.setReason_detail(selectedObjects.getReason_detail());
                                header.setValidasi(selectedObjects.getValidasi());
                                header.setIs_pre_approval(selectedObjects.getIs_pre_approval());

                                Bundle bundle = new Bundle();
                                bundle.putSerializable(CustomerFragment.SURVEY_HEADER, header);
                                bundle.putInt(CustomerFragment.SURVEY_MODE, Global.MODE_TASK_UPDATE);

                                Fragment fragment = com.adins.mss.base.dynamicform.CustomerFragment.create(header);

                                FragmentTransaction transaction = MainMenuActivity.fragmentManager.beginTransaction();
                                transaction.setCustomAnimations(R.anim.activity_open_translate, R.anim.activity_close_scale, R.anim.activity_open_scale, R.anim.activity_close_translate);
                                transaction.replace(R.id.content_frame, fragment);
                                transaction.addToBackStack(null);
                                transaction.commit();
                            }
                        }
                    }
                };
                gridView.setOnItemClickListener(onItemClickListener);
                Global.listOfTaskUpdate = objects;

                adapter.notifyDataSetChanged();
                try {
                    MainMenuActivity.setDrawerCounter();
                } catch (Exception e) {
                    FireCrash.log(e);
                    ACRA.getErrorReporter().putCustomData("ErrorMainMenuActivity", e.getMessage());
                    ACRA.getErrorReporter().putCustomData("ErrorMainMenuActivity", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                    ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat set Drawer Counter"));
                }
//                updateList();
            }
        }
    }

}
