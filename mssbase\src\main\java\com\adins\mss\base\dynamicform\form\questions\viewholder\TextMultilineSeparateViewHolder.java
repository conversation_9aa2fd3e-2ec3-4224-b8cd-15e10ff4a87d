package com.adins.mss.base.dynamicform.form.questions.viewholder;

import android.app.Activity;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.adins.mss.base.R;
import com.adins.mss.foundation.questiongenerator.QuestionBean;
import com.adins.mss.foundation.questiongenerator.form.QuestionView;

public class TextMultilineSeparateViewHolder extends RecyclerView.ViewHolder {
    private final Activity activity;
    private QuestionBean bean;
    private QuestionView mView;
    private final TextView mQuestionLabel;
    private final EditText txtAnswer1;
    private final EditText txtAnswer2;
    private final EditText txtAnswer3;
    private final EditText txtAnswer4;
    private final EditText txtAnswer5;

    public TextMultilineSeparateViewHolder(View itemView, Activity context) {
        super(itemView);
        mView = itemView.findViewById(R.id.questionTextMultilineSeparateLayout);
        mQuestionLabel = itemView.findViewById(R.id.lblQuestionTxtMultiSeparate);
        txtAnswer1 = itemView.findViewById(R.id.questionTxtAns1);
        txtAnswer2 = itemView.findViewById(R.id.questionTxtAns2);
        txtAnswer3 = itemView.findViewById(R.id.questionTxtAns3);
        txtAnswer4 = itemView.findViewById(R.id.questionTxtAns4);
        txtAnswer5 = itemView.findViewById(R.id.questionTxtAns5);
        this.activity = context;
    }

    public void bind(final QuestionBean item, int number) {
        bean = item;
        String label = number +". "+ bean.getQuestion_label();
        mQuestionLabel.setText(label);

        EditText[] listAnswer = {
                txtAnswer1, txtAnswer2, txtAnswer3, txtAnswer4, txtAnswer5
        };

        if (bean.getAnswer() != null) {
            String answer = bean.getAnswer();
            String[] dataColl = answer.split("\n");
            for (int i = 0; i < dataColl.length; i++) {
                listAnswer[i].setText(dataColl[i]);
                listAnswer[i].setSingleLine(false);
                listAnswer[i].setCursorVisible(false);
                listAnswer[i].setEnabled(false);
            }
        } else {
            for (EditText editText : listAnswer) {
                editText.setText("");
                editText.setSingleLine(false);
                editText.setCursorVisible(false);
                editText.setEnabled(false);
            }
        }

    }
}
