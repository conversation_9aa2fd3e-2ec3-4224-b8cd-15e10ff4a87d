package com.adins.mss.dao;

import java.util.List;
import java.util.ArrayList;
import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.SqlUtils;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;
import de.greenrobot.dao.query.Query;
import de.greenrobot.dao.query.QueryBuilder;

import com.adins.mss.dao.QuestionSet;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "MS_QUESTIONSET".
*/
public class QuestionSetDao extends AbstractDao<QuestionSet, String> {

    public static final String TABLENAME = "MS_QUESTIONSET";

    /**
     * Properties of entity QuestionSet.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_question_set = new Property(0, String.class, "uuid_question_set", true, "UUID_QUESTION_SET");
        public final static Property Question_group_id = new Property(1, String.class, "question_group_id", false, "QUESTION_GROUP_ID");
        public final static Property Question_group_name = new Property(2, String.class, "question_group_name", false, "QUESTION_GROUP_NAME");
        public final static Property Question_group_order = new Property(3, Integer.class, "question_group_order", false, "QUESTION_GROUP_ORDER");
        public final static Property Question_id = new Property(4, String.class, "question_id", false, "QUESTION_ID");
        public final static Property Question_label = new Property(5, String.class, "question_label", false, "QUESTION_LABEL");
        public final static Property Question_order = new Property(6, Integer.class, "question_order", false, "QUESTION_ORDER");
        public final static Property Answer_type = new Property(7, String.class, "answer_type", false, "ANSWER_TYPE");
        public final static Property Option_answers = new Property(8, String.class, "option_answers", false, "OPTION_ANSWERS");
        public final static Property Choice_filter = new Property(9, String.class, "choice_filter", false, "CHOICE_FILTER");
        public final static Property Is_mandatory = new Property(10, String.class, "is_mandatory", false, "IS_MANDATORY");
        public final static Property Max_length = new Property(11, Integer.class, "max_length", false, "MAX_LENGTH");
        public final static Property Is_visible = new Property(12, String.class, "is_visible", false, "IS_VISIBLE");
        public final static Property Is_readonly = new Property(13, String.class, "is_readonly", false, "IS_READONLY");
        public final static Property Regex = new Property(14, String.class, "regex", false, "REGEX");
        public final static Property Relevant_question = new Property(15, String.class, "relevant_question", false, "RELEVANT_QUESTION");
        public final static Property Calculate = new Property(16, String.class, "calculate", false, "CALCULATE");
        public final static Property Constraint_message = new Property(17, String.class, "constraint_message", false, "CONSTRAINT_MESSAGE");
        public final static Property Usr_crt = new Property(18, String.class, "usr_crt", false, "USR_CRT");
        public final static Property Dtm_crt = new Property(19, java.util.Date.class, "dtm_crt", false, "DTM_CRT");
        public final static Property Usr_upd = new Property(20, String.class, "usr_upd", false, "USR_UPD");
        public final static Property Dtm_upd = new Property(21, java.util.Date.class, "dtm_upd", false, "DTM_UPD");
        public final static Property Identifier_name = new Property(22, String.class, "identifier_name", false, "IDENTIFIER_NAME");
        public final static Property Uuid_scheme = new Property(23, String.class, "uuid_scheme", false, "UUID_SCHEME");
        public final static Property Lov_group = new Property(24, String.class, "lov_group", false, "LOV_GROUP");
        public final static Property Tag = new Property(25, String.class, "tag", false, "TAG");
        public final static Property Is_holiday_allowed = new Property(26, String.class, "is_holiday_allowed", false, "IS_HOLIDAY_ALLOWED");
        public final static Property Img_quality = new Property(27, String.class, "img_quality", false, "IMG_QUALITY");
        public final static Property Question_validation = new Property(28, String.class, "question_validation", false, "QUESTION_VALIDATION");
        public final static Property Question_value = new Property(29, String.class, "question_value", false, "QUESTION_VALUE");
        public final static Property Validate_err_message = new Property(30, String.class, "validate_err_message", false, "VALIDATE_ERR_MESSAGE");
        public final static Property Form_version = new Property(31, String.class, "form_version", false, "FORM_VERSION");
        public final static Property Relevant_mandatory = new Property(32, String.class, "relevant_mandatory", false, "RELEVANT_MANDATORY");
    };

    private DaoSession daoSession;

    private Query<QuestionSet> scheme_QuestionSetListQuery;

    public QuestionSetDao(DaoConfig config) {
        super(config);
    }
    
    public QuestionSetDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"MS_QUESTIONSET\" (" + //
                "\"UUID_QUESTION_SET\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_question_set
                "\"QUESTION_GROUP_ID\" TEXT," + // 1: question_group_id
                "\"QUESTION_GROUP_NAME\" TEXT," + // 2: question_group_name
                "\"QUESTION_GROUP_ORDER\" INTEGER," + // 3: question_group_order
                "\"QUESTION_ID\" TEXT," + // 4: question_id
                "\"QUESTION_LABEL\" TEXT," + // 5: question_label
                "\"QUESTION_ORDER\" INTEGER," + // 6: question_order
                "\"ANSWER_TYPE\" TEXT," + // 7: answer_type
                "\"OPTION_ANSWERS\" TEXT," + // 8: option_answers
                "\"CHOICE_FILTER\" TEXT," + // 9: choice_filter
                "\"IS_MANDATORY\" TEXT," + // 10: is_mandatory
                "\"MAX_LENGTH\" INTEGER," + // 11: max_length
                "\"IS_VISIBLE\" TEXT," + // 12: is_visible
                "\"IS_READONLY\" TEXT," + // 13: is_readonly
                "\"REGEX\" TEXT," + // 14: regex
                "\"RELEVANT_QUESTION\" TEXT," + // 15: relevant_question
                "\"CALCULATE\" TEXT," + // 16: calculate
                "\"CONSTRAINT_MESSAGE\" TEXT," + // 17: constraint_message
                "\"USR_CRT\" TEXT," + // 18: usr_crt
                "\"DTM_CRT\" INTEGER," + // 19: dtm_crt
                "\"USR_UPD\" TEXT," + // 20: usr_upd
                "\"DTM_UPD\" INTEGER," + // 21: dtm_upd
                "\"IDENTIFIER_NAME\" TEXT," + // 22: identifier_name
                "\"UUID_SCHEME\" TEXT," + // 23: uuid_scheme
                "\"LOV_GROUP\" TEXT," + // 24: lov_group
                "\"TAG\" TEXT," + // 25: tag
                "\"IS_HOLIDAY_ALLOWED\" TEXT," + // 26: is_holiday_allowed
                "\"IMG_QUALITY\" TEXT," + // 27: img_quality
                "\"QUESTION_VALIDATION\" TEXT," + // 28: question_validation
                "\"QUESTION_VALUE\" TEXT," + // 29: question_value
                "\"VALIDATE_ERR_MESSAGE\" TEXT," + // 30: validate_err_message
                "\"FORM_VERSION\" TEXT," + // 31: form_version
                "\"RELEVANT_MANDATORY\" TEXT);"); // 32: relevant_mandatory
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"MS_QUESTIONSET\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, QuestionSet entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_question_set());
 
        String question_group_id = entity.getQuestion_group_id();
        if (question_group_id != null) {
            stmt.bindString(2, question_group_id);
        }
 
        String question_group_name = entity.getQuestion_group_name();
        if (question_group_name != null) {
            stmt.bindString(3, question_group_name);
        }
 
        Integer question_group_order = entity.getQuestion_group_order();
        if (question_group_order != null) {
            stmt.bindLong(4, question_group_order);
        }
 
        String question_id = entity.getQuestion_id();
        if (question_id != null) {
            stmt.bindString(5, question_id);
        }
 
        String question_label = entity.getQuestion_label();
        if (question_label != null) {
            stmt.bindString(6, question_label);
        }
 
        Integer question_order = entity.getQuestion_order();
        if (question_order != null) {
            stmt.bindLong(7, question_order);
        }
 
        String answer_type = entity.getAnswer_type();
        if (answer_type != null) {
            stmt.bindString(8, answer_type);
        }
 
        String option_answers = entity.getOption_answers();
        if (option_answers != null) {
            stmt.bindString(9, option_answers);
        }
 
        String choice_filter = entity.getChoice_filter();
        if (choice_filter != null) {
            stmt.bindString(10, choice_filter);
        }
 
        String is_mandatory = entity.getIs_mandatory();
        if (is_mandatory != null) {
            stmt.bindString(11, is_mandatory);
        }
 
        Integer max_length = entity.getMax_length();
        if (max_length != null) {
            stmt.bindLong(12, max_length);
        }
 
        String is_visible = entity.getIs_visible();
        if (is_visible != null) {
            stmt.bindString(13, is_visible);
        }
 
        String is_readonly = entity.getIs_readonly();
        if (is_readonly != null) {
            stmt.bindString(14, is_readonly);
        }
 
        String regex = entity.getRegex();
        if (regex != null) {
            stmt.bindString(15, regex);
        }
 
        String relevant_question = entity.getRelevant_question();
        if (relevant_question != null) {
            stmt.bindString(16, relevant_question);
        }
 
        String calculate = entity.getCalculate();
        if (calculate != null) {
            stmt.bindString(17, calculate);
        }
 
        String constraint_message = entity.getConstraint_message();
        if (constraint_message != null) {
            stmt.bindString(18, constraint_message);
        }
 
        String usr_crt = entity.getUsr_crt();
        if (usr_crt != null) {
            stmt.bindString(19, usr_crt);
        }
 
        java.util.Date dtm_crt = entity.getDtm_crt();
        if (dtm_crt != null) {
            stmt.bindLong(20, dtm_crt.getTime());
        }
 
        String usr_upd = entity.getUsr_upd();
        if (usr_upd != null) {
            stmt.bindString(21, usr_upd);
        }
 
        java.util.Date dtm_upd = entity.getDtm_upd();
        if (dtm_upd != null) {
            stmt.bindLong(22, dtm_upd.getTime());
        }
 
        String identifier_name = entity.getIdentifier_name();
        if (identifier_name != null) {
            stmt.bindString(23, identifier_name);
        }
 
        String uuid_scheme = entity.getUuid_scheme();
        if (uuid_scheme != null) {
            stmt.bindString(24, uuid_scheme);
        }
 
        String lov_group = entity.getLov_group();
        if (lov_group != null) {
            stmt.bindString(25, lov_group);
        }
 
        String tag = entity.getTag();
        if (tag != null) {
            stmt.bindString(26, tag);
        }
 
        String is_holiday_allowed = entity.getIs_holiday_allowed();
        if (is_holiday_allowed != null) {
            stmt.bindString(27, is_holiday_allowed);
        }
 
        String img_quality = entity.getImg_quality();
        if (img_quality != null) {
            stmt.bindString(28, img_quality);
        }
 
        String question_validation = entity.getQuestion_validation();
        if (question_validation != null) {
            stmt.bindString(29, question_validation);
        }
 
        String question_value = entity.getQuestion_value();
        if (question_value != null) {
            stmt.bindString(30, question_value);
        }
 
        String validate_err_message = entity.getValidate_err_message();
        if (validate_err_message != null) {
            stmt.bindString(31, validate_err_message);
        }
 
        String form_version = entity.getForm_version();
        if (form_version != null) {
            stmt.bindString(32, form_version);
        }
 
        String relevant_mandatory = entity.getRelevant_mandatory();
        if (relevant_mandatory != null) {
            stmt.bindString(33, relevant_mandatory);
        }
    }

    @Override
    protected void attachEntity(QuestionSet entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public QuestionSet readEntity(Cursor cursor, int offset) {
        QuestionSet entity = new QuestionSet( //
            cursor.getString(offset + 0), // uuid_question_set
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // question_group_id
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // question_group_name
            cursor.isNull(offset + 3) ? null : cursor.getInt(offset + 3), // question_group_order
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // question_id
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // question_label
            cursor.isNull(offset + 6) ? null : cursor.getInt(offset + 6), // question_order
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7), // answer_type
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8), // option_answers
            cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9), // choice_filter
            cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10), // is_mandatory
            cursor.isNull(offset + 11) ? null : cursor.getInt(offset + 11), // max_length
            cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12), // is_visible
            cursor.isNull(offset + 13) ? null : cursor.getString(offset + 13), // is_readonly
            cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14), // regex
            cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15), // relevant_question
            cursor.isNull(offset + 16) ? null : cursor.getString(offset + 16), // calculate
            cursor.isNull(offset + 17) ? null : cursor.getString(offset + 17), // constraint_message
            cursor.isNull(offset + 18) ? null : cursor.getString(offset + 18), // usr_crt
            cursor.isNull(offset + 19) ? null : new java.util.Date(cursor.getLong(offset + 19)), // dtm_crt
            cursor.isNull(offset + 20) ? null : cursor.getString(offset + 20), // usr_upd
            cursor.isNull(offset + 21) ? null : new java.util.Date(cursor.getLong(offset + 21)), // dtm_upd
            cursor.isNull(offset + 22) ? null : cursor.getString(offset + 22), // identifier_name
            cursor.isNull(offset + 23) ? null : cursor.getString(offset + 23), // uuid_scheme
            cursor.isNull(offset + 24) ? null : cursor.getString(offset + 24), // lov_group
            cursor.isNull(offset + 25) ? null : cursor.getString(offset + 25), // tag
            cursor.isNull(offset + 26) ? null : cursor.getString(offset + 26), // is_holiday_allowed
            cursor.isNull(offset + 27) ? null : cursor.getString(offset + 27), // img_quality
            cursor.isNull(offset + 28) ? null : cursor.getString(offset + 28), // question_validation
            cursor.isNull(offset + 29) ? null : cursor.getString(offset + 29), // question_value
            cursor.isNull(offset + 30) ? null : cursor.getString(offset + 30), // validate_err_message
            cursor.isNull(offset + 31) ? null : cursor.getString(offset + 31), // form_version
            cursor.isNull(offset + 32) ? null : cursor.getString(offset + 32) // relevant_mandatory
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, QuestionSet entity, int offset) {
        entity.setUuid_question_set(cursor.getString(offset + 0));
        entity.setQuestion_group_id(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setQuestion_group_name(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setQuestion_group_order(cursor.isNull(offset + 3) ? null : cursor.getInt(offset + 3));
        entity.setQuestion_id(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setQuestion_label(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setQuestion_order(cursor.isNull(offset + 6) ? null : cursor.getInt(offset + 6));
        entity.setAnswer_type(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
        entity.setOption_answers(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
        entity.setChoice_filter(cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9));
        entity.setIs_mandatory(cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10));
        entity.setMax_length(cursor.isNull(offset + 11) ? null : cursor.getInt(offset + 11));
        entity.setIs_visible(cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12));
        entity.setIs_readonly(cursor.isNull(offset + 13) ? null : cursor.getString(offset + 13));
        entity.setRegex(cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14));
        entity.setRelevant_question(cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15));
        entity.setCalculate(cursor.isNull(offset + 16) ? null : cursor.getString(offset + 16));
        entity.setConstraint_message(cursor.isNull(offset + 17) ? null : cursor.getString(offset + 17));
        entity.setUsr_crt(cursor.isNull(offset + 18) ? null : cursor.getString(offset + 18));
        entity.setDtm_crt(cursor.isNull(offset + 19) ? null : new java.util.Date(cursor.getLong(offset + 19)));
        entity.setUsr_upd(cursor.isNull(offset + 20) ? null : cursor.getString(offset + 20));
        entity.setDtm_upd(cursor.isNull(offset + 21) ? null : new java.util.Date(cursor.getLong(offset + 21)));
        entity.setIdentifier_name(cursor.isNull(offset + 22) ? null : cursor.getString(offset + 22));
        entity.setUuid_scheme(cursor.isNull(offset + 23) ? null : cursor.getString(offset + 23));
        entity.setLov_group(cursor.isNull(offset + 24) ? null : cursor.getString(offset + 24));
        entity.setTag(cursor.isNull(offset + 25) ? null : cursor.getString(offset + 25));
        entity.setIs_holiday_allowed(cursor.isNull(offset + 26) ? null : cursor.getString(offset + 26));
        entity.setImg_quality(cursor.isNull(offset + 27) ? null : cursor.getString(offset + 27));
        entity.setQuestion_validation(cursor.isNull(offset + 28) ? null : cursor.getString(offset + 28));
        entity.setQuestion_value(cursor.isNull(offset + 29) ? null : cursor.getString(offset + 29));
        entity.setValidate_err_message(cursor.isNull(offset + 30) ? null : cursor.getString(offset + 30));
        entity.setForm_version(cursor.isNull(offset + 31) ? null : cursor.getString(offset + 31));
        entity.setRelevant_mandatory(cursor.isNull(offset + 32) ? null : cursor.getString(offset + 32));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(QuestionSet entity, long rowId) {
        return entity.getUuid_question_set();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(QuestionSet entity) {
        if(entity != null) {
            return entity.getUuid_question_set();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
    /** Internal query to resolve the "questionSetList" to-many relationship of Scheme. */
    public List<QuestionSet> _queryScheme_QuestionSetList(String uuid_scheme) {
        synchronized (this) {
            if (scheme_QuestionSetListQuery == null) {
                QueryBuilder<QuestionSet> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_scheme.eq(null));
                scheme_QuestionSetListQuery = queryBuilder.build();
            }
        }
        Query<QuestionSet> query = scheme_QuestionSetListQuery.forCurrentThread();
        query.setParameter(0, uuid_scheme);
        return query.list();
    }

    private String selectDeep;

    protected String getSelectDeep() {
        if (selectDeep == null) {
            StringBuilder builder = new StringBuilder("SELECT ");
            SqlUtils.appendColumns(builder, "T", getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T0", daoSession.getSchemeDao().getAllColumns());
            builder.append(" FROM MS_QUESTIONSET T");
            builder.append(" LEFT JOIN MS_SCHEME T0 ON T.\"UUID_SCHEME\"=T0.\"UUID_SCHEME\"");
            builder.append(' ');
            selectDeep = builder.toString();
        }
        return selectDeep;
    }
    
    protected QuestionSet loadCurrentDeep(Cursor cursor, boolean lock) {
        QuestionSet entity = loadCurrent(cursor, 0, lock);
        int offset = getAllColumns().length;

        Scheme scheme = loadCurrentOther(daoSession.getSchemeDao(), cursor, offset);
        entity.setScheme(scheme);

        return entity;    
    }

    public QuestionSet loadDeep(Long key) {
        assertSinglePk();
        if (key == null) {
            return null;
        }

        StringBuilder builder = new StringBuilder(getSelectDeep());
        builder.append("WHERE ");
        SqlUtils.appendColumnsEqValue(builder, "T", getPkColumns());
        String sql = builder.toString();
        
        String[] keyArray = new String[] { key.toString() };
        Cursor cursor = db.rawQuery(sql, keyArray);
        
        try {
            boolean available = cursor.moveToFirst();
            if (!available) {
                return null;
            } else if (!cursor.isLast()) {
                throw new IllegalStateException("Expected unique result, but count was " + cursor.getCount());
            }
            return loadCurrentDeep(cursor, true);
        } finally {
            cursor.close();
        }
    }
    
    /** Reads all available rows from the given cursor and returns a list of new ImageTO objects. */
    public List<QuestionSet> loadAllDeepFromCursor(Cursor cursor) {
        int count = cursor.getCount();
        List<QuestionSet> list = new ArrayList<QuestionSet>(count);
        
        if (cursor.moveToFirst()) {
            if (identityScope != null) {
                identityScope.lock();
                identityScope.reserveRoom(count);
            }
            try {
                do {
                    list.add(loadCurrentDeep(cursor, false));
                } while (cursor.moveToNext());
            } finally {
                if (identityScope != null) {
                    identityScope.unlock();
                }
            }
        }
        return list;
    }
    
    protected List<QuestionSet> loadDeepAllAndCloseCursor(Cursor cursor) {
        try {
            return loadAllDeepFromCursor(cursor);
        } finally {
            cursor.close();
        }
    }
    

    /** A raw-style query where you can pass any WHERE clause and arguments. */
    public List<QuestionSet> queryDeep(String where, String... selectionArg) {
        Cursor cursor = db.rawQuery(getSelectDeep() + where, selectionArg);
        return loadDeepAllAndCloseCursor(cursor);
    }
 
}
