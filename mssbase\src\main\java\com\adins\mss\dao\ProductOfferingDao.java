package com.adins.mss.dao;

import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;

import com.adins.mss.dao.ProductOffering;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "MS_PO".
*/
public class ProductOfferingDao extends AbstractDao<ProductOffering, Long> {

    public static final String TABLENAME = "MS_PO";

    /**
     * Properties of entity ProductOffering.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Id = new Property(0, long.class, "id", true, "ID");
        public final static Property Asset_scheme_id = new Property(1, int.class, "asset_scheme_id", false, "ASSET_SCHEME_ID");
        public final static Property Dealer_scheme_id = new Property(2, int.class, "dealer_scheme_id", false, "DEALER_SCHEME_ID");
        public final static Property Branch_id = new Property(3, String.class, "branch_id", false, "BRANCH_ID");
        public final static Property Prod_off_id = new Property(4, String.class, "prod_off_id", false, "PROD_OFF_ID");
        public final static Property Prod_off_name = new Property(5, String.class, "prod_off_name", false, "PROD_OFF_NAME");
        public final static Property Prod_cat_code = new Property(6, String.class, "prod_cat_code", false, "PROD_CAT_CODE");
        public final static Property Prod_cat_name = new Property(7, String.class, "prod_cat_name", false, "PROD_CAT_NAME");
        public final static Property Jns_pembiayaan = new Property(8, String.class, "jns_pembiayaan", false, "JNS_PEMBIAYAAN");
        public final static Property Min_tenor = new Property(9, Integer.class, "min_tenor", false, "MIN_TENOR");
        public final static Property Max_tenor = new Property(10, Integer.class, "max_tenor", false, "MAX_TENOR");
        public final static Property Sc_id = new Property(11, String.class, "sc_id", false, "SC_ID");
        public final static Property Component = new Property(12, String.class, "component", false, "COMPONENT");
        public final static Property Rule_data_min_tdp = new Property(13, Integer.class, "rule_data_min_tdp", false, "RULE_DATA_MIN_TDP");
        public final static Property Rule_data_manf_year = new Property(14, Integer.class, "rule_data_manf_year", false, "RULE_DATA_MANF_YEAR");
        public final static Property Rule_asset_price = new Property(15, Integer.class, "rule_asset_price", false, "RULE_ASSET_PRICE");
        public final static Property Rule_min_dp = new Property(16, Integer.class, "rule_min_dp", false, "RULE_MIN_DP");
        public final static Property Rule_app_tc = new Property(17, Integer.class, "rule_app_tc", false, "RULE_APP_TC");
        public final static Property Rule_scoring = new Property(18, Integer.class, "rule_scoring", false, "RULE_SCORING");
        public final static Property Rule_matrix = new Property(19, Integer.class, "rule_matrix", false, "RULE_MATRIX");
        public final static Property Is_deleted = new Property(20, Integer.class, "is_deleted", false, "IS_DELETED");
        public final static Property Dtm_upd = new Property(21, java.util.Date.class, "dtm_upd", false, "DTM_UPD");
        public final static Property Show_pot = new Property(22, Integer.class, "show_pot", false, "SHOW_POT");
    };


    public ProductOfferingDao(DaoConfig config) {
        super(config);
    }
    
    public ProductOfferingDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"MS_PO\" (" + //
                "\"ID\" INTEGER PRIMARY KEY NOT NULL ," + // 0: id
                "\"ASSET_SCHEME_ID\" INTEGER NOT NULL ," + // 1: asset_scheme_id
                "\"DEALER_SCHEME_ID\" INTEGER NOT NULL ," + // 2: dealer_scheme_id
                "\"BRANCH_ID\" TEXT," + // 3: branch_id
                "\"PROD_OFF_ID\" TEXT NOT NULL ," + // 4: prod_off_id
                "\"PROD_OFF_NAME\" TEXT," + // 5: prod_off_name
                "\"PROD_CAT_CODE\" TEXT," + // 6: prod_cat_code
                "\"PROD_CAT_NAME\" TEXT," + // 7: prod_cat_name
                "\"JNS_PEMBIAYAAN\" TEXT," + // 8: jns_pembiayaan
                "\"MIN_TENOR\" INTEGER," + // 9: min_tenor
                "\"MAX_TENOR\" INTEGER," + // 10: max_tenor
                "\"SC_ID\" TEXT," + // 11: sc_id
                "\"COMPONENT\" TEXT," + // 12: component
                "\"RULE_DATA_MIN_TDP\" INTEGER," + // 13: rule_data_min_tdp
                "\"RULE_DATA_MANF_YEAR\" INTEGER," + // 14: rule_data_manf_year
                "\"RULE_ASSET_PRICE\" INTEGER," + // 15: rule_asset_price
                "\"RULE_MIN_DP\" INTEGER," + // 16: rule_min_dp
                "\"RULE_APP_TC\" INTEGER," + // 17: rule_app_tc
                "\"RULE_SCORING\" INTEGER," + // 18: rule_scoring
                "\"RULE_MATRIX\" INTEGER," + // 19: rule_matrix
                "\"IS_DELETED\" INTEGER," + // 20: is_deleted
                "\"DTM_UPD\" INTEGER," + // 21: dtm_upd
                "\"SHOW_POT\" INTEGER);"); // 22: show_pot
        // Add Indexes
        db.execSQL("CREATE INDEX " + constraint + "IDX_MS_PO_JNS_PEMBIAYAAN_BRANCH_ID_PROD_CAT_CODE_COMPONENT ON MS_PO" +
                " (\"JNS_PEMBIAYAAN\",\"BRANCH_ID\",\"PROD_CAT_CODE\",\"COMPONENT\");");
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"MS_PO\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, ProductOffering entity) {
        stmt.clearBindings();
        stmt.bindLong(1, entity.getId());
        stmt.bindLong(2, entity.getAsset_scheme_id());
        stmt.bindLong(3, entity.getDealer_scheme_id());
 
        String branch_id = entity.getBranch_id();
        if (branch_id != null) {
            stmt.bindString(4, branch_id);
        }
        stmt.bindString(5, entity.getProd_off_id());
 
        String prod_off_name = entity.getProd_off_name();
        if (prod_off_name != null) {
            stmt.bindString(6, prod_off_name);
        }
 
        String prod_cat_code = entity.getProd_cat_code();
        if (prod_cat_code != null) {
            stmt.bindString(7, prod_cat_code);
        }
 
        String prod_cat_name = entity.getProd_cat_name();
        if (prod_cat_name != null) {
            stmt.bindString(8, prod_cat_name);
        }
 
        String jns_pembiayaan = entity.getJns_pembiayaan();
        if (jns_pembiayaan != null) {
            stmt.bindString(9, jns_pembiayaan);
        }
 
        Integer min_tenor = entity.getMin_tenor();
        if (min_tenor != null) {
            stmt.bindLong(10, min_tenor);
        }
 
        Integer max_tenor = entity.getMax_tenor();
        if (max_tenor != null) {
            stmt.bindLong(11, max_tenor);
        }
 
        String sc_id = entity.getSc_id();
        if (sc_id != null) {
            stmt.bindString(12, sc_id);
        }
 
        String component = entity.getComponent();
        if (component != null) {
            stmt.bindString(13, component);
        }
 
        Integer rule_data_min_tdp = entity.getRule_data_min_tdp();
        if (rule_data_min_tdp != null) {
            stmt.bindLong(14, rule_data_min_tdp);
        }
 
        Integer rule_data_manf_year = entity.getRule_data_manf_year();
        if (rule_data_manf_year != null) {
            stmt.bindLong(15, rule_data_manf_year);
        }
 
        Integer rule_asset_price = entity.getRule_asset_price();
        if (rule_asset_price != null) {
            stmt.bindLong(16, rule_asset_price);
        }
 
        Integer rule_min_dp = entity.getRule_min_dp();
        if (rule_min_dp != null) {
            stmt.bindLong(17, rule_min_dp);
        }
 
        Integer rule_app_tc = entity.getRule_app_tc();
        if (rule_app_tc != null) {
            stmt.bindLong(18, rule_app_tc);
        }
 
        Integer rule_scoring = entity.getRule_scoring();
        if (rule_scoring != null) {
            stmt.bindLong(19, rule_scoring);
        }
 
        Integer rule_matrix = entity.getRule_matrix();
        if (rule_matrix != null) {
            stmt.bindLong(20, rule_matrix);
        }
 
        Integer is_deleted = entity.getIs_deleted();
        if (is_deleted != null) {
            stmt.bindLong(21, is_deleted);
        }
 
        java.util.Date dtm_upd = entity.getDtm_upd();
        if (dtm_upd != null) {
            stmt.bindLong(22, dtm_upd.getTime());
        }
 
        Integer show_pot = entity.getShow_pot();
        if (show_pot != null) {
            stmt.bindLong(23, show_pot);
        }
    }

    /** @inheritdoc */
    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.getLong(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public ProductOffering readEntity(Cursor cursor, int offset) {
        ProductOffering entity = new ProductOffering( //
            cursor.getLong(offset + 0), // id
            cursor.getInt(offset + 1), // asset_scheme_id
            cursor.getInt(offset + 2), // dealer_scheme_id
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // branch_id
            cursor.getString(offset + 4), // prod_off_id
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // prod_off_name
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // prod_cat_code
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7), // prod_cat_name
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8), // jns_pembiayaan
            cursor.isNull(offset + 9) ? null : cursor.getInt(offset + 9), // min_tenor
            cursor.isNull(offset + 10) ? null : cursor.getInt(offset + 10), // max_tenor
            cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11), // sc_id
            cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12), // component
            cursor.isNull(offset + 13) ? null : cursor.getInt(offset + 13), // rule_data_min_tdp
            cursor.isNull(offset + 14) ? null : cursor.getInt(offset + 14), // rule_data_manf_year
            cursor.isNull(offset + 15) ? null : cursor.getInt(offset + 15), // rule_asset_price
            cursor.isNull(offset + 16) ? null : cursor.getInt(offset + 16), // rule_min_dp
            cursor.isNull(offset + 17) ? null : cursor.getInt(offset + 17), // rule_app_tc
            cursor.isNull(offset + 18) ? null : cursor.getInt(offset + 18), // rule_scoring
            cursor.isNull(offset + 19) ? null : cursor.getInt(offset + 19), // rule_matrix
            cursor.isNull(offset + 20) ? null : cursor.getInt(offset + 20), // is_deleted
            cursor.isNull(offset + 21) ? null : new java.util.Date(cursor.getLong(offset + 21)), // dtm_upd
            cursor.isNull(offset + 22) ? null : cursor.getInt(offset + 22) // show_pot
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, ProductOffering entity, int offset) {
        entity.setId(cursor.getLong(offset + 0));
        entity.setAsset_scheme_id(cursor.getInt(offset + 1));
        entity.setDealer_scheme_id(cursor.getInt(offset + 2));
        entity.setBranch_id(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setProd_off_id(cursor.getString(offset + 4));
        entity.setProd_off_name(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setProd_cat_code(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setProd_cat_name(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
        entity.setJns_pembiayaan(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
        entity.setMin_tenor(cursor.isNull(offset + 9) ? null : cursor.getInt(offset + 9));
        entity.setMax_tenor(cursor.isNull(offset + 10) ? null : cursor.getInt(offset + 10));
        entity.setSc_id(cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11));
        entity.setComponent(cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12));
        entity.setRule_data_min_tdp(cursor.isNull(offset + 13) ? null : cursor.getInt(offset + 13));
        entity.setRule_data_manf_year(cursor.isNull(offset + 14) ? null : cursor.getInt(offset + 14));
        entity.setRule_asset_price(cursor.isNull(offset + 15) ? null : cursor.getInt(offset + 15));
        entity.setRule_min_dp(cursor.isNull(offset + 16) ? null : cursor.getInt(offset + 16));
        entity.setRule_app_tc(cursor.isNull(offset + 17) ? null : cursor.getInt(offset + 17));
        entity.setRule_scoring(cursor.isNull(offset + 18) ? null : cursor.getInt(offset + 18));
        entity.setRule_matrix(cursor.isNull(offset + 19) ? null : cursor.getInt(offset + 19));
        entity.setIs_deleted(cursor.isNull(offset + 20) ? null : cursor.getInt(offset + 20));
        entity.setDtm_upd(cursor.isNull(offset + 21) ? null : new java.util.Date(cursor.getLong(offset + 21)));
        entity.setShow_pot(cursor.isNull(offset + 22) ? null : cursor.getInt(offset + 22));
     }
    
    /** @inheritdoc */
    @Override
    protected Long updateKeyAfterInsert(ProductOffering entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    /** @inheritdoc */
    @Override
    public Long getKey(ProductOffering entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
}
