<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">

    <!-- Title of confirmation dialog informing user that they need to install
        Google Play services (from Play Store) [CHAR LIMIT=40] -->
    <string name="common_google_play_services_install_title" msgid="7215213145546190223">Get Google Play services</string>

    <!-- (For phones) Message in confirmation dialog informing user that
        they need to install Google Play services (from Play Store) [CHAR LIMIT=NONE] -->
    <string name="common_google_play_services_install_text_phone" msgid="2122112764540849864">This app won\'t run without Google Play services, which are missing from your phone.</string>

    <!-- (For tablets) Message in confirmation dialog informing user that
        they need to install Google Play services (from Play Store) [CHAR LIMIT=NONE] -->
    <string name="common_google_play_services_install_text_tablet" msgid="7351599665250191022">This app won\'t run without Google Play services, which are missing from your tablet.</string>

    <!-- Button in confirmation dialog for installing Google Play services [CHAR LIMIT=40] -->
    <string name="common_google_play_services_install_button" msgid="7153882981874058840">Get Google Play services</string>

    <!-- Title of confirmation dialog informing user they need to enable
        Google Play services in application settings [CHAR LIMIT=40] -->
    <string name="common_google_play_services_enable_title" msgid="5122002158466380389">Enable Google Play services</string>

    <!-- Message in confirmation dialog informing user they need to enable
        Google Play services in application settings [CHAR LIMIT=NONE] -->
    <string name="common_google_play_services_enable_text" msgid="227660514972886228">This app won\'t work unless you enable Google Play services.</string>

    <!-- Button in confirmation dialog to enable Google Play services.  Clicking it
        will direct user to application settings of Google Play services where they
        can enable it [CHAR LIMIT=40] -->
    <string name="common_google_play_services_enable_button" msgid="2523291102206661146">Enable Google Play services</string>

    <!-- Title of confirmation dialog informing user that they need to update
        Google Play services (from Play Store) [CHAR LIMIT=40] -->
    <string name="common_google_play_services_update_title" msgid="1788179980625863495">Update Google Play services</string>

    <!-- Message in confirmation dialog informing user that they need to update
        Google Play services (from Play Store) [CHAR LIMIT=NONE] -->
    <string name="common_google_play_services_update_text" msgid="9053896323427875356">This app won\'t run unless you update Google Play services.</string>

    <!-- Title of confirmation dialog informing the user that a network error occurred. [CHAR LIMIT=40] -->
    <string name="common_google_play_services_network_error_title">Network Error</string>

    <!-- Message in confirmation dialog informing the user that a network error occurred. [CHAR LIMIT=NONE] -->
    <string name="common_google_play_services_network_error_text">A data connection is required to connect to Google Play services.</string>

    <!-- Title of confirmation dialog informing the user that they provided an invalid account. [CHAR LIMIT=40] -->
    <string name="common_google_play_services_invalid_account_title">Invalid Account</string>

    <!-- Message in confirmation dialog informing the user that they provided an invalid account. [CHAR LIMIT=NONE] -->
    <string name="common_google_play_services_invalid_account_text">The specified account does not exist on this device. Please choose a different account.</string>

    <!-- Message in confirmation dialog informing user there is an unknown issue in Google Play
        services [CHAR LIMIT=NONE] -->
    <string name="common_google_play_services_unknown_issue">Unknown issue with Google Play services.</string>

    <!-- Title of confirmation dialog informing user that Google Play services is not supported on their device [CHAR LIMIT=40] -->
    <string name="common_google_play_services_unsupported_title">Google Play services</string>

    <!-- Message in confirmation dialog informing user that Google Play services is not supported on their device [CHAR LIMIT=NONE] -->
    <string name="common_google_play_services_unsupported_text">Google Play services, which some of your applications rely on, is not supported by your device. Please contact the manufacturer for assistance.</string>

    <!-- Message in confirmation dialog informing user that date on the device is not correct,
    causing certificate checks to fail. [CHAR LIMIT=NONE] -->
    <string name="common_google_play_services_unsupported_date_text">The date on the device appears to be incorrect. Please check the date on the device.</string>

    <!-- Button in confirmation dialog for updating Google Play services [CHAR LIMIT=40] -->
    <string name="common_google_play_services_update_button" msgid="6556509956452265614">Update</string>

    <!-- Sign-in button text [CHAR LIMIT=15] -->
    <string name="common_signin_button_text">Sign in</string>

    <!-- Long form sign-in button text [CHAR LIMIT=30] -->
    <string name="common_signin_button_text_long">Sign in with Google</string>


  <!-- Auth client code resources (prefix with auth_client --><skip />
  <!--  Title for notification shown when a bad version of GooglePlayServices
        has been installed and needs correction for an application to work.
        [CHAR LIMIT=70] -->
  <string name="auth_client_using_bad_version_title">
      An application attempted to use a bad version of Google Play Services.
  </string>
  <!--  Title for notification shown when GooglePlayServices needs to be
        enabled for a application to work. [CHAR LIMIT=70] -->
  <string name="auth_client_needs_enabling_title">
      An application requires Google Play Services to be enabled.
  </string>
  <!--  Title for notification shown when GooglePlayServices needs to be
        installed for a application to work. [CHAR LIMIT=70] -->
  <string name="auth_client_needs_installation_title">
      An application requires installation of Google Play Services.
  </string>
  <!--  Title for notification shown when GooglePlayServices needs to be
        udpated for a application to work. [CHAR LIMIT=70] -->
  <string name="auth_client_needs_update_title">
      An application requires an update for Google Play Services.
  </string>

  <!--  Title for notification shown when GooglePlayServices is unavailable [CHAR LIMIT=42] -->
  <string name="auth_client_play_services_err_notification_msg">Google Play services error</string>

  <!--  Requested by string saying which app requested the notification. [CHAR LIMIT=42] -->
  <string name="auth_client_requested_by_msg">Requested by <xliff:g id="app_name">%1$s</xliff:g></string>
  <!-- End Auth client resources --><skip />

    <!-- Location client code resources (prefix with location_client) -->

    <string name="location_client_powered_by_google">Powered by Google</string>

    <!-- End location client resources -->
</resources>
