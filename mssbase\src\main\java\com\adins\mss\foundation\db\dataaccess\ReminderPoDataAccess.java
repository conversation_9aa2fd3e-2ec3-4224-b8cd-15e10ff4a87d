package com.adins.mss.foundation.db.dataaccess;

import android.content.Context;

import com.adins.mss.base.GlobalData;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.DaoSession;
import com.adins.mss.dao.ReminderPo;
import com.adins.mss.dao.ReminderPoDao;
import com.adins.mss.foundation.db.DaoOpenHelper;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import de.greenrobot.dao.query.QueryBuilder;

public class ReminderPoDataAccess {

    /**
     * use to generate dao session that you can access modelDao
     *
     * @param context --> context from activity
     * @return
     */
    protected static DaoSession getDaoSession(Context context) {
        return DaoOpenHelper.getDaoSession(context);
    }

    /**
     * get reminderPo dao and you can access the DB
     *
     * @param context
     * @return
     */
    protected static ReminderPoDao getReminderPoDao(Context context) {
        return getDaoSession(context).getReminderPoDao();
    }

    /**
     * Clear session, close db and set daoOpenHelper to null
     */
    public static void closeAll() {
        DaoOpenHelper.closeAll();
    }

    /**
     * add reminderPo as entity
     *
     * @param context
     * @param reminderPo
     */
    public static void add(Context context, ReminderPo reminderPo) {
        getReminderPoDao(context).insert(reminderPo);
    }

    /**
     * add reminderPo as list entity
     *
     * @param context
     * @param reminderPoList
     */
    public static void add(Context context, List<ReminderPo> reminderPoList) {
        getReminderPoDao(context).insertInTx(reminderPoList);
    }

    /**
     * add or replace data reminderPo
     *
     * @param context
     * @param reminderPo
     */
    public static void addOrReplace(Context context, ReminderPo reminderPo) {
        getReminderPoDao(context).insertOrReplaceInTx(reminderPo);
        getDaoSession(context).clear();
    }

    /**
     * add or replace list data reminderPo
     *
     * @param context
     * @param reminderPoList
     */
    public static void addOrReplace(Context context, List<ReminderPo> reminderPoList) {
        getReminderPoDao(context).insertOrReplaceInTx(reminderPoList);
        getDaoSession(context).clear();
    }

    /**
     * @param context
     * @param reminderPo
     */
    public static void update(Context context, ReminderPo reminderPo) {
        getReminderPoDao(context).update(reminderPo);
    }

    /**
     * delete all content in table.
     *
     * @param context
     */
    public static void clean(Context context) {
        getReminderPoDao(context).deleteAll();
    }

    /**
     * @param context
     * @param reminderPo
     */
    public static void delete(Context context, ReminderPo reminderPo) {
        getReminderPoDao(context).delete(reminderPo);
    }

    /**
     * delete all record by uuidUser
     *
     * @param context
     * @param uuidUser
     */
    public static void delete(Context context, String uuidUser) {
        QueryBuilder<ReminderPo> qb = getReminderPoDao(context).queryBuilder();
        qb.where(ReminderPoDao.Properties.Uuid_user.eq(uuidUser));
        qb.build();
        getReminderPoDao(context).deleteInTx(qb.list());
    }

    /**
     * select * from table where uuid_task_h = param
     *
     * @param context
     * @param keyTaskH
     * @return
     */
    public static ReminderPo getOneByUuidTaskH(Context context, String keyTaskH) {
        QueryBuilder<ReminderPo> qb = getReminderPoDao(context).queryBuilder();
        qb.where(ReminderPoDao.Properties.Uuid_task_h.eq(keyTaskH));
        qb.build().forCurrentThread();
        if (qb.list().isEmpty()) {
            return null;
        }
        return qb.list().get(0);
    }

    /**
     * select * from table where uuid_task_h = param and is status downloaded = param
     *
     * @param context
     * @param keyTaskH
     * @return
     */
    public static ReminderPo getOneTaskReminderPo(Context context, String keyTaskH, String isTaskDownload) {
        QueryBuilder<ReminderPo> qb = getReminderPoDao(context).queryBuilder();
        qb.where(ReminderPoDao.Properties.Uuid_task_h.eq(keyTaskH),
                ReminderPoDao.Properties.Is_task_downloaded.eq(isTaskDownload));
        qb.build().forCurrentThread();
        if (qb.list().isEmpty()) {
            return null;
        }
        return qb.list().get(0);
    }

    /**
     * Get list of task h download by download status
     *
     * @param context
     * @param isTaskDownload
     * @return
     */
    public static List<ReminderPo> getAllByDownloadStatus(Context context, String isTaskDownload) {
        QueryBuilder<ReminderPo> qb = getReminderPoDao(context).queryBuilder();
        qb.where(ReminderPoDao.Properties.Uuid_user.eq(GlobalData.getSharedGlobalData().getUser().getUuid_user()));
        if (StringUtils.isNotBlank(isTaskDownload)) {
            qb.where(ReminderPoDao.Properties.Is_task_downloaded.eq(isTaskDownload));
        }
        String currentDate = DateFormatUtils.format(new Date(), "yyyy-MM-dd");
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        try{
            Date startDate = format.parse(currentDate+" 00:00:00.000");
            Date endDate = format.parse(currentDate+" 23:59:59.997");
            qb.where(ReminderPoDao.Properties.Dtm_crt.between(startDate, endDate));
        } catch (Exception ex) {
            if(Global.IS_DEV) {
                ex.printStackTrace();
            }
        }
        return qb.list();
    }

    /**
     * select * from table where uuid_user = param
     *
     * @param context
     * @param uuidUser
     * @return
     */
    public static List<ReminderPo> getAllTaskReminderPo(Context context, String uuidUser) {
        QueryBuilder<ReminderPo> qb = getReminderPoDao(context).queryBuilder();
        qb.where(ReminderPoDao.Properties.Uuid_user.eq(uuidUser));
        qb.build().forCurrentThread();
        return qb.list();
    }

}
