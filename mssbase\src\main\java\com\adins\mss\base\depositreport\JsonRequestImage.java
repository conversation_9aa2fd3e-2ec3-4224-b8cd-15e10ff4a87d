package com.adins.mss.base.depositreport;

/**
 * Created by <PERSON><PERSON><PERSON> on 11/03/17.
 */

import com.adins.mss.foundation.http.MssRequestType;
import com.google.gson.annotations.SerializedName;

public class JsonRequestImage extends MssRequestType {
    /**
     * Property uuid_task_h
     */
    @SerializedName("uuid_task_h")
    String uuid_task_h;

    /**
     * Property question_id
     */
    @SerializedName("question_id")
    String question_id;

    /**
     * Gets the uuid_task_h
     */
    public String getUuid_task_h() {
        return this.uuid_task_h;
    }

    /**
     * Sets the uuid_task_h
     */
    public void setUuid_task_h(String value) {
        this.uuid_task_h = value;
    }

    /**
     * Gets the question_id
     */
    public String getQuestion_id() {
        return this.question_id;
    }

    /**
     * Sets the question_id
     */
    public void setQuestion_id(String value) {
        this.question_id = value;
    }
}
