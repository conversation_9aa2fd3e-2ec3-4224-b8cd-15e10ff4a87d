package com.adins.mss.base.login;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.location.LocationManager;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.provider.Settings;
import android.text.format.DateFormat;
import android.view.View;
import android.widget.Toast;

import com.adins.mss.base.BuildConfig;
import com.adins.mss.base.ChangePasswordActivity;
import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.authentication.Authentication;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.util.FtpHelper;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.User;
import com.adins.mss.foundation.dialog.DialogManager;
import com.adins.mss.foundation.dialog.NiftyDialogBuilder;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.MssResponseType;
import com.adins.mss.foundation.oauth2.OAuth2Client;
import com.adins.mss.foundation.oauth2.Token;
import com.adins.mss.foundation.oauth2.store.SharedPreferencesTokenStore;
import com.adins.mss.foundation.security.storepreferences.ObscuredSharedPreferences;
import com.google.firebase.messaging.FirebaseMessaging;

import org.acra.ACRA;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.Calendar;
import java.util.regex.Pattern;

/**
 * Created by Aditya Purwa on 1/6/2015.
 * Default implementation of login.
 */
public abstract class DefaultLoginModel extends LoginModel implements Authentication.AuthenticationHandler {

    public static final String LOGIN_PREFERENCES = "login_preferences";
    public static final String LOGIN_PREFERENCES_USERNAME = "login_preferences.USERNAME";
    public static final String LOGIN_PREFERENCES_USERNAME2= "login_preferences.USERNAME2";
    public static final String LOGIN_PREFERENCES_PASSWORD = "login_preferences.PASSWORD";
    public static final String LOGIN_PREFERENCES_REMEMBER_ME = "login_preferences.REMEMBER_ME";
    public static final String PWD_EXP = "passwordExpired";
    public static String tenantId;
    public static onImportSuccess importSuccess;
    private final ObscuredSharedPreferences loginPreferences;
    private String username;
    private String password;
    private boolean isRememberMe;
    private ProgressDialog progressDialog;

    /**
     * Initialize a new instance of context model.
     *
     * @param context The context for the model. Must be an activity.
     */
    public DefaultLoginModel(Context context) {
        super(context);
        loginPreferences = ObscuredSharedPreferences.getPrefs(context, LOGIN_PREFERENCES, Context.MODE_PRIVATE);
    }

    public static void showGPSAlert(final Activity activity) {
        try {
            LocationManager lm = (LocationManager) activity.getSystemService(Context.LOCATION_SERVICE);
            final boolean gpsEnabled = Tool.locationEnabled(lm);
            if (!gpsEnabled) {
                final NiftyDialogBuilder ndb = NiftyDialogBuilder.getInstance(activity);
                ndb.withTitle(activity.getString(R.string.gps_unable))
                        .withMessage(activity.getString(R.string.gps_warning))
                        .withButton1Text(activity.getString(R.string.gps_button))
                        .setButton1Click(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                ndb.dismiss();
                                //enableLocationSettings(activity);
                                Intent settingsIntent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                                activity.startActivity(settingsIntent);
                            }
                        });
                ndb.isCancelable(false);
                ndb.show();
            }
        } catch (Exception e) {
            FireCrash.log(e);
            e.printStackTrace();
        }
    }

    boolean isRememberMe() {
        return isRememberMe;
    }

    public void setRememberMe(boolean isRememberMe) {
        this.isRememberMe = isRememberMe;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public void setTenantId(String username) {
        String[] idNtenant = Tool.split(username, "@");
        if (idNtenant.length > 1)
            tenantId = idNtenant[1];
    }

    @SuppressLint("NewApi")
    @Override
    public boolean login() {

        if (!ValidateInput()) {
            Toast.makeText(getContext(), getContext().getString(R.string.login_mandatory), Toast.LENGTH_SHORT).show();
            return false;
        }
        if (GlobalData.getSharedGlobalData().isRequiresAccessToken()) {
            getAccessTokenAndLogin(getContext());
        } else {
            doLogin();
        }
        return false;
    }

    @SuppressLint("StaticFieldLeak")
    private void getAccessTokenAndLogin(final Context context) {
        new AsyncTask<Void, Void, String>() {
            @Override
            protected void onPreExecute() {
                super.onPreExecute();
                progressDialog = ProgressDialog.show(getContext(), "", getContext().getString(R.string.please_wait), true);
                progressDialog.show();
            }

            @Override
            protected String doInBackground(Void... params) {
                String result = "";
                String urlService = GlobalData.getSharedGlobalData().getUrlMain();
                int idx = urlService.indexOf("/services");
                String urlMain = urlService.substring(0, idx);
                OAuth2Client client = new OAuth2Client(getUsername(), getPassword(), GlobalData.getSharedGlobalData().getClientId(), null, urlMain);
                GlobalData.getSharedGlobalData().setoAuth2Client(client);
//                if(GlobalData.getSharedGlobalData().getToken()==null) {
                try {
                    Token token = client.getAccessToken(context);
                    result = "success";
                    if (result.equals("success")) {
                        SharedPreferencesTokenStore tokenStore = new SharedPreferencesTokenStore(context);
                        tokenStore.store(GlobalData.getSharedGlobalData().getoAuth2Client().getUsername(), token);
                        GlobalData.getSharedGlobalData().setToken(token);
                    }
                } catch (RuntimeException ex) {
                    result = ex.getMessage();
                } catch (IOException e) {
                    FireCrash.log(e);
                    e.printStackTrace();
                    result = context.getResources().getString(R.string.token_failed_to_store);
                }
//                }else{
//                    result="success";
//                }

                return result;
            }

            @Override
            protected void onPostExecute(String result) {
                super.onPostExecute(result);
                closeProgress();
                if (result.equals("success")) {
                    doLogin();
                } else {
                    Toast.makeText(context, result, Toast.LENGTH_SHORT).show();
                }
            }
        }.execute();
    }

    protected void doLogin() {
        ObscuredSharedPreferences.Editor loginPreferencesEditor = loginPreferences.edit();
        if (isRememberMe()) {
            loginPreferencesEditor.putString(LOGIN_PREFERENCES_USERNAME, getUsername());
            loginPreferencesEditor.putString(LOGIN_PREFERENCES_PASSWORD, getPassword());
            loginPreferencesEditor.putBoolean(LOGIN_PREFERENCES_REMEMBER_ME, isRememberMe());
            loginPreferencesEditor.apply();
        } else {
            loginPreferencesEditor.clear();
            loginPreferencesEditor.apply();
        }
        try {
            if (Tool.isInternetconnected(getContext())) {
                setTenantId(getUsername());
                progressDialog = ProgressDialog.show(getContext(), "", getContext().getString(R.string.please_wait), true);
                progressDialog.show();
                Global.IS_LOGIN = true;
                Authentication.authenticateOnBackground(
                        getContext(),
                        getUsername(),
                        getPassword(),
                        getBuildNumber(),
                        this);
            } else {
                Toast.makeText(getContext(), getContext().getString(R.string.connection_failed), Toast.LENGTH_SHORT).show();
            }
        } catch (Exception ex) {
            FireCrash.log(ex);
            ex.printStackTrace();
        }
    }

    protected abstract int getBuildNumber();

    private boolean ValidateInput() {
        return
                (getUsername() != null && getPassword() != null) &&
                        (getUsername().length() > 0 && getPassword().length() > 0);
    }

    @Override
    public boolean exit() {
        getContextAsActivity().finish();
        return false;
    }

    @Override
    public void onConnectionFail(Authentication authentication, HttpConnectionResult httpConnectionResult) {
        closeProgress();

        //bong 12 may 15 - info untuk uninstall setelah application cleansing
        /*SharedPreferences prefs = getContext().getSharedPreferences(LOGIN_PREFERENCES, Context.MODE_PRIVATE); 
        String restoredText = prefs.getString(Authentication.LOGIN_PREFERENCES_APPLICATION_CLEANSING, null);
    	if (restoredText != null) {
    		Toast.makeText(
	                getContext(),
                    getContext().getString(R.string.db_corrupt),
                    Toast.LENGTH_LONG
	        ).show();
    		return;
    	}*/

        //bong 6 may 15 - penjagaan message yang ditampilkan saat login dengan inactive user
        if (authentication == null) {
            Toast.makeText(
                    getContext(),
                    httpConnectionResult.getResult(),
                    Toast.LENGTH_LONG
            ).show();
            return;
        }
        //bong 7 may 15 - penjagaan jika belum login ke wifi
        MssResponseType responseFromServer = null;
        if (httpConnectionResult.isOK()) {
            responseFromServer = GsonHelper.fromJson(httpConnectionResult.getResult(), MssResponseType.class);

            if (responseFromServer.getStatus().getCode() == Global.STATUS_CODE_APPL_CLEANSING) {
                Toast.makeText(
                        getContext(),
                        responseFromServer.getStatus().getMessage(),
                        Toast.LENGTH_LONG
                ).show();
                return;
            }

        }

        Toast.makeText(
                getContext(),
                getContext().getString(R.string.connection_failed_http) + httpConnectionResult.getStatusCode()
                        + getContext().getString(R.string.divider_vertical_bar) + httpConnectionResult.getResult(),
                Toast.LENGTH_LONG
        ).show();
    }

    @Override
    public void onLoginFail(Authentication authentication, String message) {
        closeProgress();
        Toast.makeText(getContext(), message, Toast.LENGTH_LONG).show();
    }

    public void onForceUpdate(Authentication authentication, String message, String otaLink) {
        closeProgress();
        showForceUpdateDialog(otaLink);
    }

    private void showForceUpdateDialog(final String otaLink) {
        final NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(getContext());
            builder.withTitle(getContext().getString(R.string.server))
            .withMessage(getContext().getString(R.string.critical_update))
            .withButton1Text(getContext().getString(R.string.update)).setButton1Click(
                    new View.OnClickListener() {

                        @Override
                        public void onClick(View v) {
                            openUpdate(otaLink);
                            builder.dismiss();
                        }
                    }
            ).isCancelable(false)
            .isCancelableOnTouchOutside(false);
        builder.show();

    }

    @SuppressLint("StaticFieldLeak")
    private void openUpdate(final String otaLink) {
        if (Pattern.compile("^ftp").matcher(otaLink).find()) {
            new AsyncTask<Void, Integer, String>() {
                ProgressDialog dialog;

                @Override
                protected String doInBackground(Void... voids) {
                    String url = StringUtils.remove(otaLink, "ftp://");
                    String urlPath = url;
                    String user = StringUtils.EMPTY;
                    String password = StringUtils.EMPTY;

                    if (StringUtils.contains(url, "@")) {
                        String userPath[] = StringUtils.split(url, "@");
                        user = StringUtils.substringBefore(userPath[0], ":");
                        password = StringUtils.substringAfter(userPath[0], ":");
                        urlPath = userPath[1];
                    }

                    String urlHost = StringUtils.substringBefore(urlPath, "/");
                    String host = StringUtils.EMPTY;
                    if (StringUtils.contains(urlHost, ":")) {
                        host = StringUtils.substringBefore(urlHost, ":");
                    } else {
                        host = urlHost;
                    }

                    String path = StringUtils.substringAfter(urlPath, "/");
                    //**********************************/MSSSVY-*******-27-product-release.apk

                    FtpHelper.download(host, user, password, path, 21, new FtpHelper.OnProgress() {
                        @Override
                        public void onUpdate(Integer progress) {
                            publishProgress(progress);
                        }
                    });

                    return path;
                }

                @Override
                protected void onPreExecute() {
                    super.onPreExecute();

                    dialog = new ProgressDialog(getContext());
                    dialog.setMessage("Downloading Update...");
                    dialog.setCancelable(false);
                    dialog.setIndeterminate(false);
                    dialog.setProgress(0);
                    dialog.setMax(100);
                    dialog.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
                    dialog.show();
                }

                @Override
                protected void onProgressUpdate(Integer... values) {
                    super.onProgressUpdate(values);
                    dialog.setProgress(values[0]);
                }

                @Override
                protected void onPostExecute(String s) {
                    super.onPostExecute(s);

                    dialog.dismiss();
                    Toast.makeText(getContext(), "Downloading complete...", Toast.LENGTH_SHORT).show();
                    exit();
                }
            }.execute();
        } else {
            Intent downloadIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(otaLink));
            getContext().startActivity(downloadIntent);

            // @ Aditya Purwa
            // User may reinstall the application, closing the app may be the correct action for now.
            exit();
        }
    }

    @Override
    public void onInactiveUser(Authentication authentication) {
        try {
            closeProgress();
            String fullName = GlobalData.getSharedGlobalData().getUser() != null ? GlobalData.getSharedGlobalData().getUser().getFullname() : "";
            String message = getContext().getString(R.string.inactive_user, fullName);
            Toast.makeText(getContext(), message, Toast.LENGTH_LONG).show();
            DialogManager.uninstallAPK(getContext());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return;
    }

//    //bong 25 mei 15 - if server force user to change password
//    @Override
//    public void onForceUpdatePassword(Authentication auth, String message){
//        closeProgress();
//        Toast.makeText(
//                getContext(),
//                message,
//                Toast.LENGTH_LONG).show();
//        forceUpdatePassword();
//    }

    @Override
    public void onLoginSuccess(Authentication authentication, String otaLink, boolean needUpdatePassword,
                               boolean pwdExp, boolean needUpdateApplication,
                               String message, User authenticatedUser) {
        closeProgress();
        importSuccess = new onImportSuccess();
        if (needUpdateApplication) {
            showAskForUpdateDialog(otaLink, needUpdatePassword, pwdExp);
            return;
        }
        if (needUpdatePassword || pwdExp) {
            forceUpdatePassword(pwdExp);
            return;
        }

        GlobalData.getSharedGlobalData().setUser(authenticatedUser);
//        FileSyncHelper.senderID = 0;
//        FileSyncHelper.startFileSync(getContext());
        getContextAsActivity().finish();
        Global.isForceLogout = false;
        goToSynchronize();
    }

    private void forceUpdatePassword(boolean pwdExp) {
        Intent intent = new Intent(getContext(), ChangePasswordActivity.class);
        if (pwdExp)
            intent.putExtra(PWD_EXP, "1");
        else intent.putExtra(PWD_EXP, "0");
        getContext().startActivity(intent);
        getContextAsActivity().finish();
    }

    private void showForcePasswordUpdate() {
        NiftyDialogBuilder builder = new NiftyDialogBuilder(getContext())
                .withTitle(getContext().getString(R.string.server))
                .withMessage(getContext().getString(R.string.change_password_required))
                .withButton1Text(getContext().getString(R.string.mnChangePassword))
                .setButton1Click(new View.OnClickListener() {

                    @Override
                    public void onClick(View v) {
                        // Change password here?
                        exit(); // Is a restart or re login required?
                    }
                });
        builder.setOnCancelListener(new DialogInterface.OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                // What will happen when the dialog is dismissed without password change?
            }
        });
        builder.show();

    }

    private void closeProgress() {
        if (progressDialog != null && progressDialog.isShowing()) {
            progressDialog.dismiss();
        }
    }

    private void goToSynchronize() {
        doSubscribe();
        Intent syncIntent = getIntentSynchronize();
        getContext().startActivity(syncIntent);
    }

    protected abstract Intent getIntentSynchronize();

    private void showAskForUpdateDialog(final String otaLink, final boolean needUpdatePassword, final boolean pwdExp) {
        final NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(getContext());
        builder.withTitle(getContext().getString(R.string.server))
                .withMessage(getContext().getString(R.string.update_available))
                .withButton1Text(getContext().getString(R.string.later))
                .isCancelable(false)
                .isCancelableOnTouchOutside(false)
                .setButton1Click(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        builder.dismiss();
                        if (needUpdatePassword || pwdExp) {
                            forceUpdatePassword(pwdExp);
                            exit();
                        } else {
                            goToSynchronize();
                            exit();
                        }
                    }
                })
                .withButton2Text(getContext().getString(R.string.update))
                .setButton2Click(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        builder.dismiss();
                        openUpdate(otaLink);
//                        exit();
                    }
                }).show();
    }

    public void doSubscribe() {
        try {
            User user = GlobalData.getSharedGlobalData().getUser();
            String application = GlobalData.getSharedGlobalData().getAuditData().getApplication();
            String buildConfig = BuildConfig.FLAVOR.toUpperCase();
            if (buildConfig.length() == 0) {
                buildConfig = Global.FLAVORS;
            }
            if (Global.APPLICATION_COLLECTION.equalsIgnoreCase(application)) {
                FirebaseMessaging.getInstance().subscribeToTopic("ALL-MC-" + buildConfig);
                if (null != user.getUuid_branch()) {
                    FirebaseMessaging.getInstance().subscribeToTopic("BRANCH-" + user.getUuid_branch() + "-" + buildConfig);
                }
                if (null != user.getUuid_group()) {
                    String[] listGroup = user.getUuid_group().split(";");
                    for (int i = 0; i < listGroup.length; i++) {
                        FirebaseMessaging.getInstance().subscribeToTopic("GROUP-" + listGroup[i] + "-" + buildConfig);
                    }
                }
            } else if (Global.APPLICATION_ORDER.equalsIgnoreCase(application)) {
                FirebaseMessaging.getInstance().subscribeToTopic("ALL-MO-" + buildConfig);
                if (null != user.getUuid_group()) {
                    String[] listGroup = user.getUuid_group().split(";");
                    for (int i = 0; i < listGroup.length; i++) {
                        FirebaseMessaging.getInstance().subscribeToTopic("GROUP-" + listGroup[i] + "-" + buildConfig);
                    }
                }
                if (null != user.getUuid_branch()) {
                    FirebaseMessaging.getInstance().subscribeToTopic("BRANCH-" + user.getUuid_branch() + "-" + buildConfig);
                }
                if (null != user.getUuid_dealer()) {
                    FirebaseMessaging.getInstance().subscribeToTopic("DEALER-" + user.getUuid_dealer() + "-" + buildConfig);
                }
            } else if (Global.APPLICATION_SURVEY.equalsIgnoreCase(application)) {
                FirebaseMessaging.getInstance().subscribeToTopic("ALL-MS-" + buildConfig);
                if (null != user.getUuid_branch()) {
                    FirebaseMessaging.getInstance().subscribeToTopic("BRANCH-" + user.getUuid_branch() + "-" + buildConfig);
                }
                if (null != user.getUuid_group()) {
                    String[] listGroup = user.getUuid_group().split(";");
                    for (int i = 0; i < listGroup.length; i++) {
                        FirebaseMessaging.getInstance().subscribeToTopic("GROUP-" + listGroup[i] + "-" + buildConfig);
                    }
                }
            }
        } catch (Exception e) {
            FireCrash.log(e);
            ACRA.getErrorReporter().putCustomData("errorToSubcribe", e.getMessage());
            ACRA.getErrorReporter().putCustomData("errorToSubcribe", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
            ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat Subcribe Topic"));
        }
    }

    public class onImportSuccess extends Handler {
        @Override
        public void handleMessage(Message msg) {
            super.handleMessage(msg);
            Bundle data = msg.getData();
            boolean success = data.getBoolean("importSuccess", false);
            if (success) {
                getContextAsActivity().finish();
                goToSynchronize();
            }
        }
    }
}