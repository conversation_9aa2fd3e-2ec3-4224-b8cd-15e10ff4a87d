package com.adins.mss.base.mainmenu;

import android.Manifest;
import android.app.ActionBar;
import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.os.PowerManager;
import android.provider.Settings;
import android.telephony.TelephonyManager;
import android.text.format.DateFormat;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.AdapterView;
import android.widget.ListView;

import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.legacy.app.ActionBarDrawerToggle;

import com.adins.mss.base.AppContext;
import com.adins.mss.base.BaseActivity;
import com.adins.mss.base.BuildConfig;
import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.about.activity.AboutInfoTab;
import com.adins.mss.base.authentication.Authentication;
import com.adins.mss.base.authentication.AuthenticationTask;
import com.adins.mss.base.checkin.activity.CheckInActivity;
import com.adins.mss.base.checkout.activity.CheckOutActivity;
import com.adins.mss.base.commons.Helper;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.dynamicform.SurveyHeaderBean;
import com.adins.mss.base.dynamicform.TaskManager;
import com.adins.mss.base.embedHtml.EmbedHtmlFragment;
import com.adins.mss.base.mainmenu.settings.SettingActivity;
import com.adins.mss.base.tasklog.LogResultActivity;
import com.adins.mss.base.tasklog.TaskLog;
import com.adins.mss.base.tasklog.TaskLogListTask;
import com.adins.mss.base.taskupdate.activity.TaskUpdateActivity;
import com.adins.mss.base.timeline.Constants;
import com.adins.mss.base.timeline.MenuAdapter;
import com.adins.mss.base.timeline.MenuModel;
import com.adins.mss.base.timeline.activity.Timeline_Activity;
import com.adins.mss.base.todolist.ToDoList;
import com.adins.mss.base.util.CustomAnimatorLayout;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.base.util.LocaleHelper;
import com.adins.mss.base.util.UserSession;
import com.adins.mss.base.util.Utility;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.GeneralParameter;
import com.adins.mss.dao.Scheme;
import com.adins.mss.dao.TaskH;
import com.adins.mss.dao.TimelineType;
import com.adins.mss.dao.User;
import com.adins.mss.foundation.camerainapp.helper.Logger;
import com.adins.mss.foundation.config.ConfigFileReader;
import com.adins.mss.foundation.db.dataaccess.GeneralParameterDataAccess;
import com.adins.mss.foundation.db.dataaccess.MenuDataAccess;
import com.adins.mss.foundation.db.dataaccess.SchemeDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskHDataAccess;
import com.adins.mss.foundation.db.dataaccess.TimelineTypeDataAccess;
import com.adins.mss.foundation.db.dataaccess.UserDataAccess;
import com.adins.mss.foundation.dialog.DialogManager;
import com.adins.mss.foundation.dialog.NiftyDialogBuilder;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.AuditDataType;
import com.adins.mss.foundation.http.AuditDataTypeGenerator;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.http.MssRequestType;
import com.adins.mss.foundation.image.Utils;
import com.adins.mss.foundation.location.LocationTrackingManager;
import com.adins.mss.foundation.location.UpdateMenuIcon;
import com.adins.mss.foundation.notification.Notification;
import com.adins.mss.foundation.oauth2.Token;
import com.adins.mss.foundation.oauth2.store.SharedPreferencesTokenStore;
import com.adins.mss.foundation.security.storepreferences.ObscuredSharedPreferences;
import com.adins.mss.foundation.sync.BackgroundServiceSynchronize;
import com.google.firebase.messaging.FirebaseMessaging;
import com.services.JsonResponseRetrieveTaskList;
import com.services.MainServices;
import com.services.PushSyncServiceBackground;
import com.services.ServiceAutoRestart;
import com.tracking.LocationTrackingService;

import org.acra.ACRA;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Properties;
import java.util.Stack;

public abstract class MainMenuActivity extends BaseActivity implements
        AdapterView.OnItemClickListener, LocationListener {

    private static final String SYNCHRONIZATION_PREFERENCE = "com.adins.mss.base.SynchronizationPreference";
    public static Intent AutoSendLocationHistoryService;
    public static Intent RunNotificationService;
    public static MainServices mainServices;
    public static boolean Force_Uninstall = false;
    public static FragmentManager fragmentManager;
    public static StackHandler mStackHandler;
    public static MenuModel mnTaskList;
    public static MenuModel mnTaskPromiseToSurvey;
    public static MenuModel mnTaskPreSurvey;
    public static MenuModel mnTaskOts;
    public static MenuModel mnTaskGuarantor;
    public static MenuModel mnLog;
    public static MenuModel mnTaskUpdate;
    public static MenuModel mnSVYVerify;
    public static MenuModel mnSVYApproval;
    public static MenuModel mnSVYVerifyByBranch;
    public static MenuModel mnSVYApprovalByBranch;
    public static MenuModel mnSVYAssignment;
    public static MenuAdapter menuAdapter;
    public static int tempPosition = 1;
    public static DrawerLeftHandler leftHandler;
    public static Class mss;
    public static Class mainMenuClass;
    public static Fragment verificationFragment;
    public static Fragment approvalFragment;
    public static Fragment verificationFragmentByBranch;
    public static Fragment approvalFragmentByBranch;
    public static Fragment assignmentFragment;
    public static Fragment statusFragment;
    //	public static Intent autoSendImageIntent;
//	public static Intent autoSendTaskIntent;
//	public static AutoSendImageThread autoSendImage;
//	public static AutoSendTaskThread autoSendTask;
    private static MainMenuActivity mainMenuActivity;
    private static Menu mainMenu;
    public boolean isMainMenuOpen = false;
    public ListView mDrawerListLeft;
    public LocationTrackingManager manager;
    public BackgroundServiceSynchronize backgroundSync;
    public ArrayList<MenuModel> models = new ArrayList<MenuModel>();
    protected DrawerLayout mDrawerLayout;
    protected ActionBarDrawerToggle mDrawerToggleLeft;
    protected Fragment fragment;
    protected CharSequence mTitle;
    protected List<String> allMenu = new ArrayList<String>();
    protected String title;
    protected String tmpTitle = "";
    protected String tmpTitle2 = "";
    protected LocationManager mLocation = null;
    protected boolean isFromSetting = false;
    Handler batteryHandler;
    Runnable checkBatteryOptimization;

    public static Class getMss() {
        return mss;
    }

    public static void setMss(Class mss) {
        MainMenuActivity.mss = mss;
    }

    public static Class getMainMenuClass() {
        return mainMenuClass;
    }

    public static void setMainMenuClass(Class mainMenuClass) {
        MainMenuActivity.mainMenuClass = mainMenuClass;
    }

    public static Fragment getVerificationFragment() {
        return MainMenuActivity.verificationFragment;
    }

    public static void setVerificationFragment(Fragment value) {
        MainMenuActivity.verificationFragment = value;
    }

    public static Fragment getApprovalFragment() {
        return MainMenuActivity.approvalFragment;
    }

    public static void setApprovalFragment(Fragment value) {
        MainMenuActivity.approvalFragment = value;
    }

    public static Fragment getAssignmentFragment() {
        return assignmentFragment;
    }

    public static void setAssignmentFragment(Fragment assignmentFragment) {
        MainMenuActivity.assignmentFragment = assignmentFragment;
    }

    public static Fragment getVerificationFragmentByBranch() {
        return MainMenuActivity.verificationFragmentByBranch;
    }

    public static void setVerificationFragmentByBranch(Fragment value) {
        MainMenuActivity.verificationFragmentByBranch = value;
    }

    public static Fragment getApprovalFragmentByBranch() {
        return MainMenuActivity.approvalFragmentByBranch;
    }

    public static void setApprovalFragmentByBranch(Fragment value) {
        MainMenuActivity.approvalFragmentByBranch = value;
    }

    public static Fragment getStatusFragment() {
        return MainMenuActivity.statusFragment;
    }

    public static void setStatusFragment(Fragment value) {
        MainMenuActivity.statusFragment = value;
    }

    public static void InitializeGlobalDataIfError(FragmentActivity activity) {
        try {
            switch (activity.getResources().getDisplayMetrics().densityDpi) {
                case DisplayMetrics.DENSITY_LOW:
                    Global.THUMBNAIL_WIDTH = 120;
                    Global.THUMBNAIL_HEIGHT = 160;
                    Global.TRIANGLE_SIZE = 18;
                    break;
                case DisplayMetrics.DENSITY_MEDIUM:
                    Global.THUMBNAIL_WIDTH = 140;
                    Global.THUMBNAIL_HEIGHT = 180;
                    Global.TRIANGLE_SIZE = 22;
                    break;
                case DisplayMetrics.DENSITY_HIGH:
                    Global.THUMBNAIL_WIDTH = 160;
                    Global.THUMBNAIL_HEIGHT = 200;
                    Global.TRIANGLE_SIZE = 26;
                    break;
                case DisplayMetrics.DENSITY_XHIGH:
                    Global.THUMBNAIL_WIDTH = 180;
                    Global.THUMBNAIL_HEIGHT = 220;
                    Global.TRIANGLE_SIZE = 28;
                    break;
                case DisplayMetrics.DENSITY_560:
                    Global.THUMBNAIL_WIDTH = 240;
                    Global.THUMBNAIL_HEIGHT = 280;
                    Global.TRIANGLE_SIZE = 36;
                    break;
                case DisplayMetrics.DENSITY_XXXHIGH:
                    Global.THUMBNAIL_WIDTH = 260;
                    Global.THUMBNAIL_HEIGHT = 300;
                    Global.TRIANGLE_SIZE = 36;
                    break;
            }
            if (GlobalData.getSharedGlobalData().getUser() == null) {
                ObscuredSharedPreferences sharedPref = ObscuredSharedPreferences.getPrefs(activity,
                        "GlobalData", Context.MODE_PRIVATE);
                String uuidUser = sharedPref.getString("UUID_USER", "");
//				boolean hasLogged = sharedPref.getBoolean("HAS_LOGGED", false);
                String urlHeader = sharedPref.getString("URL_HEADER", "");
//				String tenant = sharedPref.getString("TENANT_ID", "");

                if (uuidUser != null && uuidUser.length() > 0) {
                    Properties prop = ConfigFileReader.propertiesFromFile(activity, GlobalData.PROPERTY_FILENAME);
                    boolean encrypt = Boolean.parseBoolean(prop.getProperty(GlobalData.PROP_ENCRYPT, "false"));
                    boolean decrypt = Boolean.parseBoolean(prop.getProperty(GlobalData.PROP_DECRYPT, "false"));
                    boolean accessTokenEnable = Boolean.parseBoolean(prop.getProperty(GlobalData.PROP_IS_REQUIRED_ACCESS_TOKEN, "false"));
                    String propClientId = prop.getProperty(GlobalData.PROP_CLIENT_ID, "android");

                    boolean hasEncrypt = sharedPref.getBoolean("IS_ENCRYPT", encrypt);
                    boolean hasDecrypt = sharedPref.getBoolean("IS_DECRYPT", decrypt);
                    boolean isTokenEnable = sharedPref.getBoolean("IS_ACCESS_TOKEN_ENABLE", accessTokenEnable);
                    String clientId = sharedPref.getString("CLIENT_ID", propClientId);

                    User tempUser = UserDataAccess.getOne(activity, uuidUser);
                    GlobalData.getSharedGlobalData();
                    GlobalData.getSharedGlobalData().loadFromProperties(activity);
                    GlobalData.getSharedGlobalData().setUser(tempUser);
                    GlobalData.getSharedGlobalData().setApplication(prop.getProperty(GlobalData.PROP_APPLICATION_NAME, ""));
                    String language = LocaleHelper.getLanguage(activity);
                    GlobalData.getSharedGlobalData().setLocale(language);
                    AuditDataType tempAudit = AuditDataTypeGenerator.generateActiveUserAuditData();

                    GlobalData.getSharedGlobalData().setAuditData(tempAudit);
                    GlobalData.getSharedGlobalData().setUrlMain(urlHeader);
                    GlobalData.getSharedGlobalData().reloadUrl(activity);
//					GlobalData.getSharedGlobalData().setImei(imei);
                    Map<String, String> listImei = AuditDataTypeGenerator.getListImeiFromDevice(activity);
                    GlobalData.getSharedGlobalData().setImei(listImei.get(MssRequestType.UN_KEY_IMEI));
                    if (listImei.get(MssRequestType.UN_KEY_IMEI2) != null) {
                        GlobalData.getSharedGlobalData().setImei2(listImei.get(MssRequestType.UN_KEY_IMEI2));
                    }
                    GlobalData.getSharedGlobalData().setEncrypt(hasDecrypt);
                    GlobalData.getSharedGlobalData().setDecrypt(hasEncrypt);
                    GlobalData.getSharedGlobalData().setRequiresAccessToken(isTokenEnable);

                    if (GlobalData.getSharedGlobalData().isRequiresAccessToken()) {
                        GlobalData.getSharedGlobalData().setClientId(clientId);
                        GlobalData.getSharedGlobalData().setoAuth2Client(tempUser);
                        SharedPreferencesTokenStore tokenStore = new SharedPreferencesTokenStore(activity);
                        Token token = null;
                        try {
                            token = tokenStore.load(GlobalData.getSharedGlobalData().getoAuth2Client().getUsername());
                            GlobalData.getSharedGlobalData().setToken(token);
                        } catch (IOException e) {
                            FireCrash.log(e);
                            e.printStackTrace();
                        }
                    }

                    List<GeneralParameter> gp = GeneralParameterDataAccess.getAll(activity, uuidUser);
                    GlobalData.getSharedGlobalData().loadGeneralParameters(gp);
                    GlobalData.getSharedGlobalData().getAuditData().setCallerId(uuidUser);
                    boolean isDev = Boolean.parseBoolean(prop.getProperty(GlobalData.PROP_IS_DEVELOPER, "false"));
                    Global.IS_DEV = sharedPref.getBoolean("IS_DEV", isDev);

                    if (Global.positionStack == null) {
                        Global.positionStack = new Stack<Integer>();
                    }
                    try {
                        if (MainMenuActivity.fragmentManager == null) {
                            MainMenuActivity.fragmentManager = activity.getSupportFragmentManager();
                        }

                        Fragment fragment = new Timeline_Activity();

                        FragmentTransaction transaction = MainMenuActivity.fragmentManager.beginTransaction();
                        transaction.setCustomAnimations(R.anim.fade_in, R.anim.fade_out);
                        transaction.replace(R.id.content_frame, fragment);
                        transaction.addToBackStack(null);
                        transaction.commit();
                    } catch (Exception e) {
                        FireCrash.log(e);
                        // TODO: handle exception
                    }
                    ACRA.getErrorReporter().putCustomData("UUID_USER", GlobalData.getSharedGlobalData().getUser().getUuid_user());
                    ACRA.getErrorReporter().putCustomData("LOGIN_ID", GlobalData.getSharedGlobalData().getUser().getLogin_id());
                    ACRA.getErrorReporter().putCustomData("JOB_DESCRIPTION", GlobalData.getSharedGlobalData().getUser().getJob_description());
                    ACRA.getErrorReporter().putCustomData("BRANCH_NAME", GlobalData.getSharedGlobalData().getUser().getBranch_name());
                    ACRA.getErrorReporter().putCustomData("TENANT_ID", GlobalData.getSharedGlobalData().getTenant());
                }
            }
        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: handle exception
        }
    }

    public static void InitializeGlobalDataIfError(Context activity) {
        try {
            switch (activity.getResources().getDisplayMetrics().densityDpi) {
                case DisplayMetrics.DENSITY_LOW:
                    Global.THUMBNAIL_WIDTH = 120;
                    Global.THUMBNAIL_HEIGHT = 160;
                    Global.TRIANGLE_SIZE = 18;
                    break;
                case DisplayMetrics.DENSITY_MEDIUM:
                    Global.THUMBNAIL_WIDTH = 140;
                    Global.THUMBNAIL_HEIGHT = 180;
                    Global.TRIANGLE_SIZE = 22;
                    break;
                case DisplayMetrics.DENSITY_HIGH:
                    Global.THUMBNAIL_WIDTH = 160;
                    Global.THUMBNAIL_HEIGHT = 200;
                    Global.TRIANGLE_SIZE = 26;
                    break;
                case DisplayMetrics.DENSITY_XHIGH:
                    Global.THUMBNAIL_WIDTH = 180;
                    Global.THUMBNAIL_HEIGHT = 220;
                    Global.TRIANGLE_SIZE = 28;
                    break;
                case DisplayMetrics.DENSITY_XXHIGH:
                    Global.THUMBNAIL_WIDTH = 200;
                    Global.THUMBNAIL_HEIGHT = 240;
                    Global.TRIANGLE_SIZE = 32;
                    break;
                case DisplayMetrics.DENSITY_560:
                    Global.THUMBNAIL_WIDTH = 240;
                    Global.THUMBNAIL_HEIGHT = 280;
                    Global.TRIANGLE_SIZE = 36;
                    break;
                case DisplayMetrics.DENSITY_XXXHIGH:
                    Global.THUMBNAIL_WIDTH = 260;
                    Global.THUMBNAIL_HEIGHT = 300;
                    Global.TRIANGLE_SIZE = 36;
                    break;
            }
            if (GlobalData.getSharedGlobalData().getUser() == null) {
                ObscuredSharedPreferences sharedPref = ObscuredSharedPreferences.getPrefs(activity,
                        "GlobalData", Context.MODE_PRIVATE);
                String uuidUser = sharedPref.getString("UUID_USER", "");
//				boolean hasLogged = sharedPref.getBoolean("HAS_LOGGED", false);
                String urlHeader = sharedPref.getString("URL_HEADER", "");
//				String tenant = sharedPref.getString("TENANT_ID", "");
                if (uuidUser != null && uuidUser.length() > 0) {
                    Properties prop = ConfigFileReader.propertiesFromFile(activity, GlobalData.PROPERTY_FILENAME);
                    boolean encrypt = Boolean.parseBoolean(prop.getProperty(GlobalData.PROP_ENCRYPT, "false"));
                    boolean decrypt = Boolean.parseBoolean(prop.getProperty(GlobalData.PROP_DECRYPT, "false"));
                    boolean accessTokenEnable = Boolean.parseBoolean(prop.getProperty(GlobalData.PROP_IS_REQUIRED_ACCESS_TOKEN, "false"));
                    String propClientId = prop.getProperty(GlobalData.PROP_CLIENT_ID, "android");

                    boolean hasEncrypt = sharedPref.getBoolean("IS_ENCRYPT", encrypt);
                    boolean hasDecrypt = sharedPref.getBoolean("IS_DECRYPT", decrypt);
                    boolean isTokenEnable = sharedPref.getBoolean("IS_ACCESS_TOKEN_ENABLE", accessTokenEnable);
                    String clientId = sharedPref.getString("CLIENT_ID", propClientId);

                    User tempUser = UserDataAccess.getOne(activity, uuidUser);
                    GlobalData.getSharedGlobalData();
                    GlobalData.getSharedGlobalData().loadFromProperties(activity);
                    GlobalData.getSharedGlobalData().setUser(tempUser);
                    GlobalData.getSharedGlobalData().setApplication(prop.getProperty(GlobalData.PROP_APPLICATION_NAME, ""));
                    String language = LocaleHelper.getLanguage(activity);
                    GlobalData.getSharedGlobalData().setLocale(language);
                    AuditDataType tempAudit = AuditDataTypeGenerator.generateActiveUserAuditData();

                    GlobalData.getSharedGlobalData().setAuditData(tempAudit);
                    GlobalData.getSharedGlobalData().setUrlMain(urlHeader);
                    GlobalData.getSharedGlobalData().reloadUrl(activity);
//					GlobalData.getSharedGlobalData().setImei(imei);
                    Map<String, String> listImei = AuditDataTypeGenerator.getListImeiFromDevice(activity);
                    GlobalData.getSharedGlobalData().setImei(listImei.get(MssRequestType.UN_KEY_IMEI));
                    if (listImei.get(MssRequestType.UN_KEY_IMEI2) != null) {
                        GlobalData.getSharedGlobalData().setImei2(listImei.get(MssRequestType.UN_KEY_IMEI2));
                    }
                    GlobalData.getSharedGlobalData().setEncrypt(hasDecrypt);
                    GlobalData.getSharedGlobalData().setDecrypt(hasEncrypt);
                    GlobalData.getSharedGlobalData().setRequiresAccessToken(isTokenEnable);

                    if (GlobalData.getSharedGlobalData().isRequiresAccessToken()) {
                        GlobalData.getSharedGlobalData().setClientId(clientId);
                        GlobalData.getSharedGlobalData().setoAuth2Client(tempUser);
                        SharedPreferencesTokenStore tokenStore = new SharedPreferencesTokenStore(activity);
                        Token token = null;
                        try {
                            token = tokenStore.load(GlobalData.getSharedGlobalData().getoAuth2Client().getUsername());
                            GlobalData.getSharedGlobalData().setToken(token);
                        } catch (IOException e) {
                            FireCrash.log(e);
                            e.printStackTrace();
                        }
                    }

                    List<GeneralParameter> gp = GeneralParameterDataAccess.getAll(activity, uuidUser);
                    GlobalData.getSharedGlobalData().loadGeneralParameters(gp);
                    GlobalData.getSharedGlobalData().getAuditData().setCallerId(uuidUser);
                    boolean isDev = Boolean.parseBoolean(prop.getProperty(GlobalData.PROP_IS_DEVELOPER, "false"));
                    Global.IS_DEV = sharedPref.getBoolean("IS_DEV", isDev);

                    Global.FEATURE_RESCHEDULE_SURVEY = MenuDataAccess.isHaveRescheduleMenu(activity);
//                    Global.FEATURE_REVISIT_COLLECTION = false;
                    Global.FEATURE_REVISIT_COLLECTION = MenuDataAccess.isHaveReVisitMenu(activity); //new
                    if (Global.positionStack == null) {
                        Global.positionStack = new Stack<Integer>();
                    }
                    ACRA.getErrorReporter().putCustomData("UUID_USER", GlobalData.getSharedGlobalData().getUser().getUuid_user());
                    ACRA.getErrorReporter().putCustomData("LOGIN_ID", GlobalData.getSharedGlobalData().getUser().getLogin_id());
                    ACRA.getErrorReporter().putCustomData("JOB_DESCRIPTION", GlobalData.getSharedGlobalData().getUser().getJob_description());
                    ACRA.getErrorReporter().putCustomData("BRANCH_NAME", GlobalData.getSharedGlobalData().getUser().getBranch_name());
                    ACRA.getErrorReporter().putCustomData("TENANT_ID", GlobalData.getSharedGlobalData().getTenant());
                }
            }
        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: handle exception
        }
    }

    public static void updateMenuIcon(boolean isGPS) {
//		if(isGPS){
        UpdateMenuIcon uItem = new UpdateMenuIcon();
        uItem.updateGPSIcon(mainMenu);
//		}
    }

    public static void gotoSurveyAssignmentTask() {
        mainMenuActivity.gotoSurveyAssignment(0);
    }

    public static void setDrawerPosition(final String title) {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                Message message = new Message();
                Bundle bundle = new Bundle();
                bundle.putString(Global.BUND_KEY_CALL_MAIN_MENU, title);
                message.setData(bundle);
                MainMenuActivity.leftHandler.sendMessage(message);
            }
        }, 500);
    }

    public static void setDrawerCounter() {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                Message message = new Message();
                Bundle bundle = new Bundle();
                bundle.putBoolean(Global.BUND_KEY_REFRESHCOUNTER, true);
                message.setData(bundle);
                try {
                    MainMenuActivity.leftHandler.sendMessage(message);
                } catch (NullPointerException e) {
                    FireCrash.log(e);

                }
            }
        }, 500);
    }

    /**
     * This is used to be implemented in each application to add menu list
     */
    protected abstract ArrayList<MenuModel> getModels();

    protected abstract String getTitleGroup();

    //bong 10 apr 15 - to prevent menu slider when chg password
    protected abstract Fragment getChgPassFragment();

    public HashMap<String, Integer> getTemplateIcon() {
        HashMap<String, Integer> templateIcon = new HashMap<String, Integer>();
        templateIcon.put(getString(R.string.title_mn_home), R.drawable.ic_home);

        if("1".equalsIgnoreCase(GlobalData.getSharedGlobalData().getUser().getIs_piloting())) {
            GeneralParameter showMenuNewOrder = GeneralParameterDataAccess.getOne(getApplicationContext(), GlobalData.getSharedGlobalData().getUser().getUuid_user(), Global.GS_ENABLE_NEW_ORDER_PILOTING);
            if (showMenuNewOrder != null) {
                int codevalid = Integer.parseInt(showMenuNewOrder.getGs_value());
                if (codevalid == 1) { templateIcon.put(getString(R.string.title_mn_newtask), R.drawable.ic_new); }
            }
        }else { templateIcon.put(getString(R.string.title_mn_newtask), R.drawable.ic_new); }

        templateIcon.put(getString(R.string.title_mn_tasklist), R.drawable.ic_odrprogress);
        templateIcon.put(getString(R.string.title_mn_task_promise_to_survey), R.drawable.ic_baseline_date_range_24);
        templateIcon.put(getString(R.string.title_mn_task_pre_survey), R.drawable.ic_baseline_drafts_24);
        templateIcon.put(getString(R.string.title_mn_task_ots), R.drawable.ic_baseline_lamp_24);
        templateIcon.put(getString(R.string.title_mn_task_guarantor), R.drawable.ic_baseline_check_24);
        templateIcon.put(getString(R.string.title_mn_checkorder), R.drawable.ic_checkodr);
        templateIcon.put(getString(R.string.title_mn_log), R.drawable.ic_log);
        templateIcon.put(getString(R.string.title_mn_creditsimulation), R.drawable.ic_simulation);
        templateIcon.put(getString(R.string.title_mn_cancelorder), R.drawable.ic_updateodr);
        templateIcon.put(getString(R.string.title_mn_absentin), R.drawable.ic_checkin);
        templateIcon.put(getString(R.string.title_mn_news), R.drawable.ic_news);
        templateIcon.put(getString(R.string.title_mn_promo), R.drawable.ic_news);
        templateIcon.put(getString(R.string.title_mn_changepassword), R.drawable.ic_changepassword);
        templateIcon.put(getString(R.string.title_mn_synchronize), R.drawable.ic_synchronize);
        templateIcon.put(getString(R.string.title_mn_exit), R.drawable.ic_exit);
        templateIcon.put(getString(R.string.title_mn_about), R.drawable.ic_about);
        templateIcon.put(getString(R.string.title_mn_newlead), R.drawable.ic_new);
        templateIcon.put(getString(R.string.title_mn_surveyperformance), R.drawable.ic_performance);
        templateIcon.put(getString(R.string.title_mn_surveyverification), R.drawable.ic_verification);
        templateIcon.put(getString(R.string.title_mn_surveyapproval), R.drawable.ic_approval);
        templateIcon.put(getString(R.string.title_mn_surveyassign), R.drawable.ic_oder_ass);
        templateIcon.put(getString(R.string.title_mn_surveyreassign), R.drawable.ic_odr_reass);
        templateIcon.put(getString(R.string.title_mn_orderassign), R.drawable.ic_oder_ass);
        templateIcon.put(getString(R.string.title_mn_orderreassign), R.drawable.ic_odr_reass);
        templateIcon.put(getString(R.string.title_mn_reportsummary), R.drawable.ic_reprotsummary);
        templateIcon.put(getString(R.string.title_mn_depositreport), R.drawable.ic_depositreport);
        templateIcon.put(getString(R.string.title_mn_paymenthistory), R.drawable.ic_payment_history);
        templateIcon.put(getString(R.string.title_mn_installmentschedule), R.drawable.ic_installment_schedule);
        templateIcon.put(getString(R.string.title_mn_inquirytask), R.drawable.ic_installment_schedule);
        templateIcon.put(getString(R.string.title_mn_verification_bybranch), R.drawable.ic_verification);
        templateIcon.put(getString(R.string.title_mn_approval_bybranch), R.drawable.ic_approval);
        templateIcon.put(getString(R.string.title_mn_closing_task), R.drawable.ic_approval);
        templateIcon.put(getString(R.string.title_mn_setting), R.drawable.ic_setting);
        templateIcon.put(getString(R.string.title_mn_task_update), R.drawable.ic_task_update);

        return templateIcon;
    }

    public ArrayList<String> getTemplateMenuTitle() {
        ArrayList<String> templateMenuTitle = new ArrayList<String>();
        templateMenuTitle.add(getString(R.string.title_mn_home));
        if("1".equalsIgnoreCase(GlobalData.getSharedGlobalData().getUser().getIs_piloting())) {
            GeneralParameter showMenuNewOrder = GeneralParameterDataAccess.getOne(getApplicationContext(), GlobalData.getSharedGlobalData().getUser().getUuid_user(), Global.GS_ENABLE_NEW_ORDER_PILOTING);
            if (showMenuNewOrder != null) {
                int codevalid = Integer.parseInt(showMenuNewOrder.getGs_value());
                if (codevalid == 1) { templateMenuTitle.add(getString(R.string.title_mn_newtask));}
            }
            if ("CS".equalsIgnoreCase(GlobalData.getSharedGlobalData().getUser().getFlag_job())
                && "0".equalsIgnoreCase(GlobalData.getSharedGlobalData().getUser().getIs_piloting_cae())) {
                templateMenuTitle.add(getString(R.string.title_mn_task_promise_to_survey));
                templateMenuTitle.add(getString(R.string.title_mn_task_pre_survey));
            }
        }else { templateMenuTitle.add(getString(R.string.title_mn_newtask)); }
//        templateMenuTitle.add(getString(R.string.title_mn_newtask));
        templateMenuTitle.add(getString(R.string.title_mn_tasklist));

        if("1".equalsIgnoreCase(GlobalData.getSharedGlobalData().getUser().getIs_piloting_cae())) {
            templateMenuTitle.add(getString(R.string.title_mn_task_promise_to_survey));
            templateMenuTitle.add(getString(R.string.title_mn_task_pre_survey));
            templateMenuTitle.add(getString(R.string.title_mn_task_ots));
            templateMenuTitle.add(getString(R.string.title_mn_task_guarantor));
        }
        templateMenuTitle.add(getString(R.string.title_mn_inquirytask));
        templateMenuTitle.add(getString(R.string.title_mn_checkorder));
        templateMenuTitle.add(getString(R.string.title_mn_log));
        templateMenuTitle.add(getString(R.string.title_mn_creditsimulation));
        templateMenuTitle.add(getString(R.string.title_mn_orderassign));
        templateMenuTitle.add(getString(R.string.title_mn_orderreassign));
        templateMenuTitle.add(getString(R.string.title_mn_surveyverification));
        templateMenuTitle.add(getString(R.string.title_mn_surveyapproval));
        templateMenuTitle.add(getString(R.string.title_mn_surveyperformance));
        templateMenuTitle.add(getString(R.string.title_mn_surveyassign));
        templateMenuTitle.add(getString(R.string.title_mn_surveyreassign));
        templateMenuTitle.add(getString(R.string.title_mn_reportsummary));
        templateMenuTitle.add(getString(R.string.title_mn_depositreport));

        if(!"1".equalsIgnoreCase(GlobalData.getSharedGlobalData().getUser().getIs_piloting_cae())) {
            templateMenuTitle.add(getString(R.string.title_mn_newlead));
        }

//		templateMenuTitle.add(getString(R.string.title_mn_paymenthistory));
//		templateMenuTitle.add(getString(R.string.title_mn_installmentschedule));
        templateMenuTitle.add(getString(R.string.title_mn_cancelorder));
        templateMenuTitle.add(getString(R.string.title_mn_absentin));
        templateMenuTitle.add(getString(R.string.title_mn_news));
        templateMenuTitle.add(getString(R.string.title_mn_promo));
        templateMenuTitle.add(getString(R.string.title_mn_verification_bybranch));
        templateMenuTitle.add(getString(R.string.title_mn_approval_bybranch));
        templateMenuTitle.add(getString(R.string.title_mn_closing_task));
        templateMenuTitle.add(getString(R.string.title_mn_task_update));

        return templateMenuTitle;
    }

    public ArrayList<String> getTemplateOtherMenuTitle() {
        ArrayList<String> otherMenuTitle = new ArrayList<String>();
        otherMenuTitle.add(getString(R.string.title_mn_changepassword));
        otherMenuTitle.add(getString(R.string.title_mn_synchronize));
        otherMenuTitle.add(getString(R.string.title_mn_setting));
        otherMenuTitle.add(getString(R.string.title_mn_about));
        otherMenuTitle.add(getString(R.string.title_mn_exit));
        return otherMenuTitle;
    }

    public ArrayList<String> getServerMenuTitle() {
        ArrayList<String> serverMenuTitle = new ArrayList<String>();
        try {
            List<com.adins.mss.dao.Menu> menu = MenuDataAccess.getAll(getApplicationContext(), GlobalData.getSharedGlobalData().getUser().getUuid_user());
            for (com.adins.mss.dao.Menu menu2 : menu) {
                serverMenuTitle.add(menu2.getUuid_menu());
            }
        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: handle exception
        }
        return serverMenuTitle;
    }

    public List<String> getMainMenuTitle() {
        if (GlobalData.getSharedGlobalData().getUser() == null) {
            MainMenuActivity.InitializeGlobalDataIfError(getApplicationContext());
        }
        List<String> newMenu = MainMenuHelper.matchingMenu(getServerMenuTitle(), getTemplateMenuTitle());
        return newMenu;
    }

    public List<Integer> getMainMenuIcon() {
        List<Integer> newIcon = MainMenuHelper.matchingIcon(getMainMenuTitle(), getTemplateIcon());
        return newIcon;
    }

    public List<String> getOtherMenuTitle() {
        if (GlobalData.getSharedGlobalData().getUser() == null) {
            MainMenuActivity.InitializeGlobalDataIfError(getApplicationContext());
        }
        List<String> otherMenu = MainMenuHelper.matchingMenu(getServerMenuTitle(), getTemplateOtherMenuTitle());
        return otherMenu;
    }

    public List<Integer> getOtherMenuIcon() {
        List<Integer> otherIcon = MainMenuHelper.matchingIcon(getOtherMenuTitle(), getTemplateIcon());
        return otherIcon;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        LocaleHelper.onCreate(getApplicationContext(), LocaleHelper.ENGLSIH);
        //To can't screen shoot
        if (Tool.isProdFlavour(Global.FLAVORS)) {
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
        }
        if(Global.isForceLogout){
            DialogManager.showForceExitAlert(this, this.getString(R.string.msgLogoutUpdateSchema));
        }

        mainMenuActivity = this;
        setContentView(R.layout.main_menu_layout);
        leftHandler   = new DrawerLeftHandler();
        mStackHandler = new StackHandler();
        ObscuredSharedPreferences prefs = ObscuredSharedPreferences.getPrefs(getApplicationContext(), Authentication.LOGIN_PREFERENCES, Context.MODE_PRIVATE);
        String restoredText = prefs.getString(Authentication.LOGIN_PREFERENCES_APPLICATION_CLEANSING, null);
        if (restoredText != null && restoredText.equalsIgnoreCase("uninstall")) {
                UninstallerHandler();
        }

        fragmentManager = getSupportFragmentManager();
        ACRA.getErrorReporter().putCustomData("LAST_CLASS_ACCESSED", getClass().getSimpleName());
        StoreGlobalDataTemporary();
        mTitle = getTitle();
        Global.positionStack = new Stack<Integer>();
        mDrawerLayout = (DrawerLayout) findViewById(R.id.drawer_layout);
        mDrawerListLeft = (ListView) findViewById(R.id.left_drawer);
        mDrawerLayout.setDrawerShadow(R.drawable.drawer_shadow, GravityCompat.START);



        getActionBar().setDisplayHomeAsUpEnabled(true);
        getActionBar().setHomeButtonEnabled(true);

        mDrawerToggleLeft = new ActionBarDrawerToggle(
                this,                  /* host Activity */
                mDrawerLayout,         /* DrawerLayout object */
                R.drawable.ic_drawer,  /* nav drawer image to replace 'Up' caret */
                R.string.drawer_open,  /* "open drawer" description for accessibility */
                R.string.drawer_close  /* "close drawer" description for accessibility */
        ) {

            public void onDrawerClosed(View view) {
//            	getActionBar().setTitle(Html.fromHtml("<font color='#FFFFFF'>"+title+"</font>"));
                if (!getActionBar().isShowing()) getActionBar().show();
                /*if (getActionBar().getTabCount() != 0) {
                    if (mtitle.equals(getString(R.string.title_mn_tasklist)))
						getActionBar().setNavigationMode(ActionBar.NAVIGATION_MODE_TABS);
					else {
						getActionBar().removeAllTabs();
						getActionBar().setNavigationMode(ActionBar.NAVIGATION_MODE_STANDARD);
					}
				} else {
					getActionBar().setNavigationMode(ActionBar.NAVIGATION_MODE_STANDARD);
				}*/
            }

            public void onDrawerOpened(View drawerView) {
//            	if(title==null) title=mTitle.toString();
//            	getActionBar().setTitle(Html.fromHtml("<font color='#000000'>"+title+"</font>"));
                getActionBar().setNavigationMode(ActionBar.NAVIGATION_MODE_STANDARD);
            }

            public void onDrawerSlide(View drawerView, float slideOffset) {

                //bong 13 apr 15 - penjagaan supaya menu tidak keluar
//            	if(getChgPassFragment()!=null){
//	            	if(getChgPassFragment().isVisible()){
//	            		mDrawerLayout.closeDrawers();
//	            		return;
//	            	}
//            	}
                if (slideOffset == .05) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mDrawerListLeft.setItemChecked(Global.positionStack.lastElement(), true);
                        }
                    });
                }
//            	if(slideOffset > .01 && slideOffset< .2){
//	                	title = getActionBar().getTitle().toString();
//	                	int position = 1;
//	                	for(int i=0 ; i<models.size(); i++){
//	                		if(title.equals(models.get(i).getTitle())){
//	                			position = i;
//	                			break;
//	                		}
//	                	}
//	                	mDrawerListLeft.setItemChecked(position, true);
//	                	if(getActionBar().getTabCount()!=0)
//	                		getActionBar().hide();
//	                	getActionBar().setNavigationMode(ActionBar.NAVIGATION_MODE_STANDARD);
//	                }else if(slideOffset < .01){
//	                	getActionBar().show();
//	            		if(getActionBar().getTabCount()!=0){
//	                    	getActionBar().setNavigationMode(ActionBar.NAVIGATION_MODE_TABS);
//	                    }
//	            	}
//	                if(slideOffset> .8){
//	                	getActionBar().setNavigationMode(ActionBar.NAVIGATION_MODE_STANDARD);
//	                	if(!getActionBar().isShowing())
//	                		getActionBar().show();
//	                }

            }
        };

        mDrawerLayout.addDrawerListener(mDrawerToggleLeft);
        bindLocationListener();
        startBackgroundProcess();

        batteryHandler = new Handler();
        checkBatteryOptimization = new Runnable() {
            public void run() {
                try {
                    batteryHandler.removeCallbacks(checkBatteryOptimization);
                    if (!isBatteryOptimizationIgnored()) {
                        disableBatteryOptimization();
                        batteryHandler.postDelayed(checkBatteryOptimization, 60000);
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    e.printStackTrace();
                }
            }
        };
        disableBatteryOptimization();
        ServiceAutoRestart.startAutoRestartService(getBaseContext());

        // Kalau di UAT dimatiin, kalau di Production di nyalain
        if (Tool.isProdFlavour(Global.FLAVORS)) {
            schedulePushSync();
        }

        checkAppVersion();
    }

    public void disableBatteryOptimization() {
        Intent intent = new Intent();
        String packageName = this.getPackageName();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!isBatteryOptimizationIgnored()) {
                intent.setAction(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
                intent.setData(Uri.parse("package:" + packageName));
                intent.setFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT | Intent.FLAG_ACTIVITY_PREVIOUS_IS_TOP);
                this.startActivity(intent);
                batteryHandler.postDelayed(checkBatteryOptimization, 60000);
            }
        }
    }

    public boolean isBatteryOptimizationIgnored() {
        String packageName = this.getPackageName();
        PowerManager pm = (PowerManager) this.getSystemService(Context.POWER_SERVICE);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            return pm.isIgnoringBatteryOptimizations(packageName);
        }
        return false;
    }

    private void bindLocationListener() {
        mLocation = (LocationManager) getSystemService(LOCATION_SERVICE);
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                    Utility.checkPermissionGranted(this);
                    return;
                } else {
                    if (mLocation.getAllProviders().contains(LocationManager.GPS_PROVIDER))
                        mLocation.requestSingleUpdate(LocationManager.GPS_PROVIDER, this, null);
                }
            } else {
                if (mLocation.getAllProviders().contains(LocationManager.GPS_PROVIDER))
                    mLocation.requestSingleUpdate(LocationManager.GPS_PROVIDER, this, null);
            }
        } catch (IllegalArgumentException e) {
            FireCrash.log(e);// TODO: handle exception
        } catch (Exception e) {
            FireCrash.log(e);

        }
    }

    private void StoreGlobalDataTemporary() {
        try {
            String urlHeader = GlobalData.getSharedGlobalData().getUrlMain();
            ObscuredSharedPreferences sharedPref = ObscuredSharedPreferences.getPrefs(getApplicationContext(),
                    "GlobalData", Context.MODE_PRIVATE);

            if (GlobalData.getSharedGlobalData().getUser().getUuid_user() != null) {
                ObscuredSharedPreferences.Editor sharedPrefEditor = sharedPref.edit();
                sharedPrefEditor.putString("UUID_USER", GlobalData.getSharedGlobalData().getUser().getUuid_user());
                sharedPrefEditor.putBoolean("HAS_LOGGED", true);
                sharedPrefEditor.putString("URL_HEADER", urlHeader);
                sharedPrefEditor.putString("TENANT_ID", GlobalData.getSharedGlobalData().getTenant());
                sharedPrefEditor.putBoolean("IS_ENCRYPT", GlobalData.getSharedGlobalData().isEncrypt());
                sharedPrefEditor.putBoolean("IS_DECRYPT", GlobalData.getSharedGlobalData().isDecrypt());

                sharedPrefEditor.commit();
            }
        } catch (Exception e) {
            FireCrash.log(e);

        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        switch (requestCode) {
            case Utility.REQUEST_CODE_ASK_MULTIPLE_PERMISSIONS: {
                if (Utility.checkPermissionResult(MainMenuActivity.this, permissions, grantResults))
                    bindLocationListener();
            }
            break;
            default:
                super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        isMainMenuOpen = false;
//        Global.LTM.removeLocationListener();
    }

    @Override
    public void onResume() {
        super.onResume();
        isMainMenuOpen = true;
        Utility.checkPermissionGranted(this);
        /*try {
            long logCounter = TaskLog.getCounterLog(this);
			long taskListCounter = ToDoList.getCounterTaskList(this);
			if (MainMenuActivity.mnLog != null)
				MainMenuActivity.mnLog.setCounter(String.valueOf(logCounter));
			if (MainMenuActivity.mnTaskList != null)
				MainMenuActivity.mnTaskList.setCounter(String.valueOf(taskListCounter));
			if (MainMenuActivity.menuAdapter != null)
				MainMenuActivity.menuAdapter.notifyDataSetChanged();
		} catch (Exception e) {
                    FireCrash.log(e);
			// TODO: handle exception
		}*/
        try {
            MainMenuActivity.setDrawerCounter();
        } catch (Exception e) {
            FireCrash.log(e);
        }

//		if(MainMenuActivity.fragmentManager==null){
//			MainMenuActivity.fragmentManager=getSupportFragmentManager();
//		}
        ObscuredSharedPreferences prefs = ObscuredSharedPreferences.getPrefs(this, Authentication.LOGIN_PREFERENCES, Context.MODE_PRIVATE);
        String restoredText = prefs.getString(Authentication.LOGIN_PREFERENCES_APPLICATION_CLEANSING, null);
        if (restoredText != null && restoredText.equalsIgnoreCase("uninstall")) {
            if(MainMenuActivity.Force_Uninstall){
                UninstallerHandler();
            }
            return;
        }
        if (GlobalData.getSharedGlobalData().getUser() == null) {
            PackageInfo pInfo;
            try {
                pInfo = getPackageManager().getPackageInfo(getPackageName(), 0);
                Global.APP_VERSION = pInfo.versionName;
                Global.BUILD_VERSION = pInfo.versionCode;
            } catch (PackageManager.NameNotFoundException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
            InitializeGlobalDataIfError(getApplicationContext());
            mDrawerListLeft.setItemChecked(0, true);
        }

        if (Global.LTM == null) {
            manager = null;
            StartLocationTracking();
        }

        //NENDI: 2018/04/11 | Auto Start AutoSendLocationTrackingService
        if (Build.VERSION.SDK_INT >= 21) {
            if (!Helper.getInstance().isServiceAvailable(this, LocationTrackingService.class))
            {
                if (AutoSendLocationHistoryService != null) {
                    startService(AutoSendLocationHistoryService);
                }
            }
        }

        DialogManager.showGPSAlert(this);
        DialogManager.showTimeProviderAlert(this);

        if (UserSession.isInvalidToken()) {
            DialogManager.showForceExitAlert(this, getString(R.string.failed_refresh_token));
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_MENU) {
            try {
                mDrawerLayout.openDrawer(mDrawerListLeft);
                return true;
            } catch (ClassCastException e) {
                FireCrash.log(e);
                return super.onKeyDown(keyCode, event);
            } catch (Exception e) {
                FireCrash.log(e);
                return false;
            }
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        menu.clear();
        MenuInflater inflater = getMenuInflater();
        inflater.inflate(R.menu.main_menu, menu);
        mainMenu = menu;
        return true;
    }

    @Override
    public boolean onPrepareOptionsMenu(Menu menu) {
        updateMenuIcon(Global.isGPS);
        return super.onPrepareOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        if (mDrawerToggleLeft.onOptionsItemSelected(item)) {
            return true;
        } else if (id == R.id.mnGPS) {
            if (Global.LTM != null) {
                if (Global.LTM.isConnected) {
                    Global.LTM.removeLocationListener();
                    Global.LTM.connectLocationClient();
                } else {
                    StartLocationTracking();
                }
                Animation a = AnimationUtils.loadAnimation(this, R.anim.icon_rotate);
                findViewById(R.id.mnGPS).startAnimation(a);

            }
        }
        return super.onOptionsItemSelected(item);

    }

    @Override
    protected void onPostCreate(Bundle savedInstanceState) {
        super.onPostCreate(savedInstanceState);
        mDrawerToggleLeft.syncState();
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        mDrawerToggleLeft.onConfigurationChanged(newConfig);
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, final int position, long id) {
        try {
            Utility.freeMemory();
            if (MainMenuActivity.Force_Uninstall) {
                UninstallerHandler();
            } else {
                getActionBar().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

                if (Global.positionStack == null) {
                    Global.positionStack = new Stack<Integer>();
                }
                if (tempPosition == position) {
                    mDrawerLayout.closeDrawers();
                    if (getString(R.string.title_mn_log).equalsIgnoreCase(allMenu.get(position)) ||
                            getString(R.string.title_mn_surveyverification).equalsIgnoreCase(allMenu.get(position))
                            || getString(R.string.title_mn_surveyapproval).equalsIgnoreCase(allMenu.get(position))) {
                        if (!models.get(position).getCounter().equals("0")) {
                            mDrawerListLeft.setItemChecked(position, true);

                            Global.positionStack.push(position);
                            setTitle(models.get(position).getTitle());
                        } else {
                            try {
                                mDrawerListLeft.setItemChecked(Global.positionStack.lastElement(), true);
                                tempPosition = Global.positionStack.lastElement();
                            } catch (NoSuchElementException ex) {
                                mDrawerListLeft.setItemChecked(position, true);
                            }
                            gotoSelectedPage(position);
                        }
                    }
                } else {
                    tempPosition = position;
                    if (getString(R.string.title_mn_log).equalsIgnoreCase(allMenu.get(position)) ||
                            getString(R.string.title_mn_surveyverification).equalsIgnoreCase(allMenu.get(position))
                            || getString(R.string.title_mn_surveyapproval).equalsIgnoreCase(allMenu.get(position))) {
                        if (!models.get(position).getCounter().equals("0")) {
                            mDrawerListLeft.setItemChecked(position, true);

                            Global.positionStack.push(position);
                            setTitle(models.get(position).getTitle());
                        } else {
                            try {
                                mDrawerListLeft.setItemChecked(Global.positionStack.lastElement(), true);
                                tempPosition = Global.positionStack.lastElement();
                            } catch (NoSuchElementException ex) {
                                mDrawerListLeft.setItemChecked(position, true);
                            }
                        }
                    } else if (getString(R.string.title_mn_main_menu).equalsIgnoreCase(allMenu.get(position)) ||
                            getString(R.string.title_mn_other).equalsIgnoreCase(allMenu.get(position)) ||
                            getString(R.string.title_mn_synchronize).equalsIgnoreCase(allMenu.get(position)) ||
                            getString(R.string.title_mn_about).equalsIgnoreCase(allMenu.get(position)) ||
                            getString(R.string.title_mn_exit).equalsIgnoreCase(allMenu.get(position)) ||
                            getString(R.string.title_mn_setting).equalsIgnoreCase(allMenu.get(position))) {
                        try {
                            mDrawerListLeft.setItemChecked(Global.positionStack.lastElement(), true);
                            tempPosition = Global.positionStack.lastElement();
                            setTitle(models.get(Global.positionStack.lastElement()).getTitle());
                        } catch (NoSuchElementException ex) {
                            mDrawerListLeft.setItemChecked(position, true);
                        }
                    } else {
                        mDrawerListLeft.setItemChecked(position, true);
                        Global.positionStack.push(position);
                        if (!getString(R.string.title_mn_closing_task).equalsIgnoreCase(allMenu.get(position))) {
                            setTitle(models.get(position).getTitle());
                        }
                    }
                    mDrawerLayout.closeDrawers();
                    gotoSelectedPage(position);

                }
            }
        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: handle exception
        }
    }

    private void gotoSelectedPage(final int position) {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {

                if (getString(R.string.title_mn_home).equalsIgnoreCase(allMenu.get(position))) {
                    goTimeline(position);
                } else if (getString(R.string.title_mn_newtask).equalsIgnoreCase(allMenu.get(position))) {
                    gotoNewTask(position);
                } else if (getString(R.string.title_mn_tasklist).equalsIgnoreCase(allMenu.get(position))) {
                    gotoTaskList(position);
                } else if (getString(R.string.title_mn_task_promise_to_survey).equalsIgnoreCase(allMenu.get(position))) {
                    goToTaskPromiseToSurvey(position);
                } else if (getString(R.string.title_mn_task_pre_survey).equalsIgnoreCase(allMenu.get(position))) {
                    goToTaskPreSurvey(position);
                } else if (getString(R.string.title_mn_task_ots).equalsIgnoreCase(allMenu.get(position))) {
                    goToTaskOts(position);
                } else if (getString(R.string.title_mn_task_guarantor).equalsIgnoreCase(allMenu.get(position))) {
                    goToTaskGuarantorList(position);
                } else if (getString(R.string.title_mn_checkorder).equalsIgnoreCase(allMenu.get(position))) {
                    gotoCheckOrder(position);
                } else if (getString(R.string.title_mn_log).equalsIgnoreCase(allMenu.get(position))) {
                    gotoLog(position);
                } else if (getString(R.string.title_mn_creditsimulation).equalsIgnoreCase(allMenu.get(position))) {
                    gotoCreditSimulation(position);
                } else if (getString(R.string.title_mn_cancelorder).equalsIgnoreCase(allMenu.get(position))) {
                    gotoCancelOrder(position);
                } else if (getString(R.string.title_mn_absentin).equalsIgnoreCase(allMenu.get(position))) {
                    gotoCheckIn(position);
                } else if (getString(R.string.title_mn_news).equalsIgnoreCase(allMenu.get(position))) {
                    gotoNews(position);
                } else if (getString(R.string.title_mn_promo).equalsIgnoreCase(allMenu.get(position))) {
                    gotoPromo(position);
                } else if (getString(R.string.title_mn_synchronize).equalsIgnoreCase(allMenu.get(position))) {
                    gotoSynchronize();
                } else if (getString(R.string.title_mn_changepassword).equalsIgnoreCase(allMenu.get(position))) {
//        		        		gotoChangePassword(position);
                } else if (getString(R.string.title_mn_about).equalsIgnoreCase(allMenu.get(position))) {
                    gotoAbout();
                } else if (getString(R.string.title_mn_newlead).equalsIgnoreCase(allMenu.get(position))) {
                    gotoEmbedHtml();
                } else if (getString(R.string.title_mn_exit).equalsIgnoreCase(allMenu.get(position))) {
                    gotoExit();
                } else if (getString(R.string.title_mn_surveyperformance).equalsIgnoreCase(allMenu.get(position))) {
                    gotoSurveyPerformance(position);
                } else if (getString(R.string.title_mn_surveyverification).equalsIgnoreCase(allMenu.get(position))) {
                    gotoSurveyVerification(position);
                } else if (getString(R.string.title_mn_surveyapproval).equalsIgnoreCase(allMenu.get(position))) {
                    gotoSurveyApproval(position);
                } else if (getString(R.string.title_mn_surveyassign).equalsIgnoreCase(allMenu.get(position))) {
                    gotoSurveyAssignment(position);
                } else if (getString(R.string.title_mn_surveyreassign).equalsIgnoreCase(allMenu.get(position))) {
                    gotoSurveyReassignment(position);
                } else if (getString(R.string.title_mn_orderassign).equalsIgnoreCase(allMenu.get(position))) {
                    gotoOrderAssignment(position);
                } else if (getString(R.string.title_mn_orderreassign).equalsIgnoreCase(allMenu.get(position))) {
                    gotoOrderReassignment(position);
                } else if (getString(R.string.title_mn_reportsummary).equalsIgnoreCase(allMenu.get(position))) {
                    gotoReportSummary(position);
                } else if (getString(R.string.title_mn_depositreport).equalsIgnoreCase(allMenu.get(position))) {
                    gotoDepositReport(position);
                } else if (getString(R.string.title_mn_paymenthistory).equalsIgnoreCase(allMenu.get(position))) {
                    gotoPaymentHistory(position);
                } else if (getString(R.string.title_mn_installmentschedule).equalsIgnoreCase(allMenu.get(position))) {
                    gotoInstallmentSchedule(position);
                } else if (getString(R.string.title_mn_verification_bybranch).equalsIgnoreCase(allMenu.get(position))) {
                    gotoSurveyVerificationByBranch(position);
                } else if (getString(R.string.title_mn_approval_bybranch).equalsIgnoreCase(allMenu.get(position))) {
                    gotoSurveyApprovalByBranch(position);
                } else if (getString(R.string.title_mn_closing_task).equalsIgnoreCase(allMenu.get(position))) {
                    gotoClosingTask(position);
                } else if (getString(R.string.title_mn_setting).equalsIgnoreCase(allMenu.get(position))) {
                    gotoSettings();
                } else if(getString(R.string.title_mn_inquirytask).equalsIgnoreCase(allMenu.get(position))) {
                    gotoInquiryTask(position);
                } else if(getString(R.string.title_mn_task_update).equalsIgnoreCase(allMenu.get(position))) {
                    gotoTaskUpdate(position);
                }
            }
        }, 500);
    }

    public void gotoInquiryTask(int position) {
        Logger.i("INFO", String.valueOf(position));
    }

    protected abstract void gotoNewTask(int position);

    protected abstract void gotoCheckOrder(int position);

    protected abstract void gotoCreditSimulation(int position);

    protected abstract void gotoCancelOrder(int position);

    protected abstract void gotoTaskList(int position);

    protected abstract void goToTaskPromiseToSurvey(int position);

    protected abstract void goToTaskPreSurvey(int position);

    protected abstract void goToTaskGuarantorList(int position);

    protected abstract void goToTaskOts(int position);

    protected abstract void gotoPromo(int position);

    protected abstract void gotoNews(int position);

    protected abstract void gotoOrderAssignment(int position);

    protected abstract void gotoOrderReassignment(int position);

    protected abstract void gotoSurveyPerformance(int position);

    protected abstract void gotoSurveyVerification(int position);

    protected abstract void gotoSurveyApproval(int position);

    protected abstract void gotoSurveyVerificationByBranch(int position);

    protected abstract void gotoSurveyApprovalByBranch(int position);

    protected abstract void gotoSurveyAssignment(int position);

    protected abstract void gotoSurveyReassignment(int position);

    protected abstract void gotoReportSummary(int position);
//    protected abstract void gotoChangePassword();

    protected abstract void gotoDepositReport(int position);

    protected abstract void gotoPaymentHistory(int position);

    protected abstract void gotoInstallmentSchedule(int position);

    protected abstract void gotoClosingTask(int position);

	/*protected void gotoSetting() {
        // TODO Auto-generated method stub
//		fragment = new SettingActivity();
//		FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
//	    transaction.replace(R.id.content_frame, fragment);
//	    transaction.commit();
//	    // update selected item and title, then close the drawer
//	    mDrawerListLeft.setItemChecked(12, true);
//	    setTitle(models.get(12).getTitle());
//	    mDrawerLayout.closeDrawers();
	}*/

    @Override
    public void onBackPressed() {
        try {
            if (mDrawerLayout.isDrawerOpen(GravityCompat.START)) {
                mDrawerLayout.closeDrawer(GravityCompat.START);
            } else {
                if (Constants.inSearchMode) {
                    if (Constants.inSearchMode) {
                        CustomAnimatorLayout animatorLayout = new CustomAnimatorLayout(1, 0, 1, 1, 500, Timeline_Activity.searchLayout, true);
                        Timeline_Activity.searchLayout.startAnimation(animatorLayout);
                    }
                    Constants.inSearchMode = false;
                } else {
                    //NENDI: 2018-04-12 | UPDATED
                    //DialogManager.showExitAlert(this, getString(R.string.alertExit));
                    int j = fragmentManager.getBackStackEntryCount();
                    if (j > 1) {
                        Log.i("MainActivity", "popping backstack");
                        MainMenuActivity.tempPosition = 0;
                        if (Global.positionStack.size() > 1)
                            Global.positionStack.pop();
                        mDrawerListLeft.setItemChecked(Global.positionStack.lastElement(), true);
                        MainMenuActivity.fragmentManager.popBackStack();
//                        fragmentManager.popBackStackImmediate();
                    } else {
                        MainMenuActivity.tempPosition = 0;
                        try {
                            mDrawerListLeft.setItemChecked(1, true);
                        } catch (Exception e) {
                            FireCrash.log(e);
                            // TODO: handle exception
                        }
                        Log.i("MainActivity", "nothing on backstack, calling super");
                        scheduleLogout();
//                        moveTaskToBack(true);
                        super.onBackPressed();
                    }
                }
            }
        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: handle exception
        }
    }

    protected void gotoExit() {
        mDrawerLayout.closeDrawers();
        doUnsubscribe();
        tempPosition = 0;
        DialogManager.showExitAlert(this, getString(R.string.alertExit));
    }

    public void doUnsubscribe() {
        try {
            User user = GlobalData.getSharedGlobalData().getUser();
            String application = GlobalData.getSharedGlobalData().getAuditData().getApplication();
            String buildConfig = BuildConfig.FLAVOR.toUpperCase();
            if (buildConfig.length() == 0) {
                buildConfig = Global.FLAVORS;
            }
            if (Global.APPLICATION_COLLECTION.equalsIgnoreCase(application)) {
                FirebaseMessaging.getInstance().unsubscribeFromTopic("ALL-MC-" + buildConfig);
                if (null != user.getUuid_branch()) {
                    FirebaseMessaging.getInstance().unsubscribeFromTopic(user.getUuid_branch() + "-" + buildConfig);
                }
                if (null != user.getUuid_group()) {
                    String[] listGroup = user.getUuid_group().split(";");
                    for (int i = 0; i < listGroup.length; i++) {
                        FirebaseMessaging.getInstance().unsubscribeFromTopic(listGroup[i] + "-" + buildConfig);
                    }
                }
            } else if (Global.APPLICATION_ORDER.equalsIgnoreCase(application)) {
                FirebaseMessaging.getInstance().unsubscribeFromTopic("ALL-MO-" + buildConfig);
                if (null != user.getUuid_group()) {
                    String[] listGroup = user.getUuid_group().split(";");
                    for (int i = 0; i < listGroup.length; i++) {
                        FirebaseMessaging.getInstance().unsubscribeFromTopic(listGroup[i] + "-" + buildConfig);
                    }
                }
                if (null != user.getUuid_branch()) {
                    FirebaseMessaging.getInstance().unsubscribeFromTopic(user.getUuid_branch() + "-" + buildConfig);
                }
                if (null != user.getUuid_dealer()) {
                    FirebaseMessaging.getInstance().unsubscribeFromTopic(user.getUuid_dealer() + "-" + buildConfig);
                }
            } else if (Global.APPLICATION_SURVEY.equalsIgnoreCase(application)) {
                FirebaseMessaging.getInstance().unsubscribeFromTopic("ALL-MS-" + buildConfig);
                if (null != user.getUuid_branch()) {
                    FirebaseMessaging.getInstance().unsubscribeFromTopic(user.getUuid_branch() + "-" + buildConfig);
                }
                if (null != user.getUuid_group()) {
                    String[] listGroup = user.getUuid_group().split(";");
                    for (int i = 0; i < listGroup.length; i++) {
                        FirebaseMessaging.getInstance().unsubscribeFromTopic(listGroup[i] + "-" + buildConfig);
                    }
                }
            }
        } catch (Exception e) {
            FireCrash.log(e);
            ACRA.getErrorReporter().putCustomData("errorToSubcribe", e.getMessage());
            ACRA.getErrorReporter().putCustomData("errorToSubcribe", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
            ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat Subcribe Topic"));
        }
    }

    protected void gotoLog(int position) {
        fragment = new LogResultActivity();

        GeneralParameter gp = GeneralParameterDataAccess.getOne(getApplicationContext(),
                GlobalData.getSharedGlobalData().getUser().getUuid_user(),
                Global.MS_STATUS_LOG_DELETE);

        if (gp != null) {
            String gpStr = gp.getGs_value().replaceAll("\\s", "");
            List<String> gpList = Arrays.asList(gpStr.split(";"));

            List<TaskH> logList = new ArrayList<>();
            TaskLog log = new TaskLog(getApplicationContext());
            logList = log.getListTaskLog();
            if (logList != null) {
                for(TaskH taskH : logList) {
                    if (taskH.getStatus_application() != null) {
                        for (String str : gpList) {
                            if (taskH.getStatus_application().equals(str)) {
                                TaskHDataAccess.delete(getApplicationContext(), taskH);
                                break;
                            }
                        }
                    }
                }
            }
        }
        TaskLogListTask task = new TaskLogListTask(this, getString(R.string.progressWait),
                getString(R.string.msgNoSent), R.id.content_frame, fragment);
        task.execute();
    }

    protected void gotoTaskUpdate(int position) {
        fragment = new TaskUpdateActivity();
        FragmentTransaction transaction = MainMenuActivity.fragmentManager.beginTransaction();
        transaction.setCustomAnimations(R.anim.activity_open_translate, R.anim.activity_close_scale, R.anim.activity_open_scale, R.anim.activity_close_translate);
        transaction.replace(R.id.content_frame, fragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

    protected void gotoCheckIn(int position) {
        fragment = new CheckInActivity();

        FragmentTransaction transaction = MainMenuActivity.fragmentManager.beginTransaction();
        transaction.setCustomAnimations(R.anim.activity_open_translate, R.anim.activity_close_scale, R.anim.activity_open_scale, R.anim.activity_close_translate);
        transaction.replace(R.id.content_frame, fragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

    protected void gotoCheckOut(int position) {
        // TODO Auto-generated method stub
        fragment = new CheckOutActivity();
        FragmentTransaction transaction = MainMenuActivity.fragmentManager.beginTransaction();
        transaction.setCustomAnimations(R.anim.activity_open_translate, R.anim.activity_close_scale, R.anim.activity_open_scale, R.anim.activity_close_translate);
        transaction.replace(R.id.content_frame, fragment);
        transaction.addToBackStack(null);
        transaction.commit();

    }

    protected void gotoSettings() {
        // TODO Auto-generated method stub
        isFromSetting = true;
        Intent intent = new Intent(getApplicationContext(), SettingActivity.class);
        startActivityForResult(intent, 199);
    }

    protected void gotoAbout() {
//		mDrawerLayout.closeDrawers();
//		startActivity(new Intent(this, AboutInfoTab.class));
        AboutInfoTab.mContext = getBaseContext();
        fragment = new AboutInfoTab();
        FragmentTransaction transaction = MainMenuActivity.fragmentManager.beginTransaction();
        transaction.setCustomAnimations(R.anim.activity_open_translate, R.anim.activity_close_scale, R.anim.activity_open_scale, R.anim.activity_close_translate);
        transaction.replace(R.id.content_frame, fragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

    protected void gotoEmbedHtml() {
        android.content.Intent intent = new android.content.Intent(getApplicationContext(), EmbedHtmlFragment.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        getApplicationContext().startActivity(intent);
    }

    protected void goTimeline(int position) {
        getActionBar().removeAllTabs();
        getActionBar().setNavigationMode(ActionBar.NAVIGATION_MODE_STANDARD);

        fragment = new Timeline_Activity();

        if (fragmentManager == null)
            fragmentManager = getSupportFragmentManager();

        FragmentTransaction transaction = fragmentManager.beginTransaction();
        //transaction.setCustomAnimations(R.anim.fade_in,R.anim.fade_out,R.anim.activity_open_scale,R.anim.activity_close_translate);
        transaction.setCustomAnimations(R.anim.activity_open_translate, R.anim.activity_close_scale, R.anim.activity_open_scale, R.anim.activity_close_translate);
        transaction.replace(R.id.content_frame, fragment);
        transaction.addToBackStack(null);
//        transaction.commitAllowingStateLoss();
        transaction.commit();

        mDrawerListLeft.setItemChecked(position, true);
        setTitle(models.get(position).getTitle());
        mDrawerLayout.closeDrawers();
    }

    public void gotoSynchronize() {
//		Intent syncIntent = getIntentSynchronize();
//        this.startActivity(syncIntent);
//        finish();
        new AuthenticationTask(MainMenuActivity.this);
    }

    protected abstract Intent getIntentSynchronize();

    @Override
    public void setTitle(CharSequence title) {
        mTitle = title;
        getActionBar().setTitle(mTitle);
    }

    @Override
    protected void onDestroy() {
        // TODO Auto-generated method stub
        super.onDestroy();
        mnTaskList = null;
        mnTaskPromiseToSurvey = null;
        mnTaskPreSurvey = null;
        mnTaskOts = null;
        mnTaskGuarantor = null;
        mnTaskUpdate = null;
        mnLog = null;
        mnSVYVerify = null;
        mnSVYApproval = null;
        mnSVYVerifyByBranch = null;
        mnSVYApprovalByBranch = null;
//		StatusSectionFragment.statusListAdapter = null;
//        if (mLocation != null) {
//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
//                if (checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
//                    // TODO: Consider calling
//                    //    Activity#requestPermissions
//                    // here to request the missing permissions, and then overriding
//                    //   public void onRequestPermissionsResult(int requestCode, String[] permissions,
//                    //                                          int[] grantResults)
//                    // to handle the case where the user grants the permission. See the documentation
//                    // for Activity#requestPermissions for more details.
//                    return;
//                } else {
//                    mLocation.removeUpdates(this);
//                }
//            } else {
//                mLocation.removeUpdates(this);
//            }
//        }
//        if (Global.LTM != null) {
//            if (manager != null) {
//                if (manager.isConnected) {
//                    manager.removeLocationListener();
//                }
//            }
//            if (Global.LTM.isConnected) {
//                Global.LTM.removeLocationListener();
//            }
//        }

        if (AutoSendLocationHistoryService != null) {
            stopService(AutoSendLocationHistoryService);
        }
        if (RunNotificationService != null) {
            stopService(RunNotificationService);
        }
//
//		if (autoSendImage != null) {
//			autoSendImage.requestStop();
//			autoSendImage = null;
//		}
//		if(autoSendImageIntent!=null){
//			stopService(autoSendImageIntent);
//			autoSendImageIntent=null;
//		}
//
//
//		if (autoSendTask!= null) {
//			autoSendTask.requestStop();
//			autoSendTask = null;
//		}
//		if(autoSendTaskIntent!=null){
//			stopService(autoSendTaskIntent);
//			autoSendTaskIntent = null;
//		}
//		MainServices.stopAllThread();

        Notification.getSharedNotification().clearNotifAll(this);
    }

    private void StartLocationTracking() {
        try {
            LocationManager lm = (LocationManager) getSystemService(Context.LOCATION_SERVICE);
            TelephonyManager tm = (TelephonyManager) getSystemService(Context.TELEPHONY_SERVICE);
            GeneralParameter gpDistance = GeneralParameterDataAccess.getOne(getApplicationContext(), GlobalData.getSharedGlobalData().getUser().getUuid_user(), Global.GS_DISTANCE_TRACKING);
            try {
                if (gpDistance != null) {
                    int distanceTracking = Integer.parseInt(gpDistance.getGs_value());
                    if (distanceTracking != 0) {
                        manager = new LocationTrackingManager(tm, lm, getApplicationContext());
                        manager.setMinimalDistanceChangeLocation(Integer.parseInt(GeneralParameterDataAccess.getOne(getApplicationContext(), GlobalData.getSharedGlobalData().getUser().getUuid_user(), Global.GS_DISTANCE_TRACKING).getGs_value()));
                        manager.setMinimalTimeChangeLocation(5);
                        manager.applyLocationListener(getApplicationContext());
                    }
                }
            } catch (Exception e) {
                FireCrash.log(e);
                manager = new LocationTrackingManager(tm, lm, getApplicationContext());
                manager.setMinimalDistanceChangeLocation(50);
                manager.setMinimalTimeChangeLocation(5);
                manager.applyLocationListener(getApplicationContext());
            }
//do not need to do apply

            if (Global.LTM == null) {
                Global.LTM = manager;
            } else {
                try {
                    Global.LTM = null;
                    Global.LTM = manager;
                } catch (Exception e) {
                    FireCrash.log(e);

                }

            }
        } catch (Exception e) {
            FireCrash.log(e);
            e.printStackTrace();
        }
    }

    private void startSynchronize() {
        String application = GlobalData.getSharedGlobalData().getAuditData().getApplication();
        String day = String.valueOf(Calendar.getInstance().get(Calendar.DATE));
        String month = String.valueOf(Calendar.getInstance().get(Calendar.MONTH) + 1);
        String year = String.valueOf(Calendar.getInstance().get(Calendar.YEAR));
        String todayDate = day + month + year;

        ObscuredSharedPreferences synchronizationPreference = ObscuredSharedPreferences.getPrefs(this, SYNCHRONIZATION_PREFERENCE, Context.MODE_PRIVATE);

        if (Global.APPLICATION_ORDER.equalsIgnoreCase(application)) {
            Global.LAST_SYNC = synchronizationPreference.getString("MOSyncDate", "");
        } else if (Global.APPLICATION_SURVEY.equalsIgnoreCase(application)) {
            Global.LAST_SYNC = synchronizationPreference.getString("MSSyncDate", "");
        } else if (Global.APPLICATION_COLLECTION.equalsIgnoreCase(application)) {
            Global.LAST_SYNC = synchronizationPreference.getString("MCSyncDate", "");
        }

        if (!Global.LAST_SYNC.equals(todayDate)) {
            if (Tool.isInternetconnected(this)) {
                try {
                    backgroundSync = new BackgroundServiceSynchronize(this);
                    backgroundSync.authentication(this);
                } catch (Exception e) {
                    FireCrash.log(e);
                }
            }
        }
    }

    protected void startBackgroundProcess() {
        try {
            startSynchronize();
            StartLocationTracking();
            ToDoList toDoList = new ToDoList(getApplicationContext());
            List<TaskH> listTaskH = toDoList.getListTaskInStatus(ToDoList.SEARCH_BY_ALL, null);
            ToDoList.listOfSurveyStatus = new ArrayList<SurveyHeaderBean>();
            for (TaskH taskH : listTaskH) {
                ToDoList.listOfSurveyStatus.add(new SurveyHeaderBean(taskH));
            }

            //ngisi tabel tymeline type jika masih fresh install
            ObscuredSharedPreferences sharedPref = ObscuredSharedPreferences.getPrefs(getApplicationContext(),
                    "wasInserted", Context.MODE_PRIVATE);
            String isInsert = sharedPref.getString("isInsert", Global.FALSE_STRING);
            if (isInsert.equals(Global.FALSE_STRING)) {
                ObscuredSharedPreferences.Editor sharedPrefEditor = sharedPref.edit();
                sharedPrefEditor.putString("isInsert", Global.TRUE_STRING);
                sharedPrefEditor.commit();
                String usrCrt = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                Date date = Tool.getSystemDateTime();

                TimelineType timelineTypeC = new TimelineType(Tool.getUUID(), "Timeline Type for Check In", Global.TIMELINE_TYPE_CHECKIN, usrCrt, date, null, null);
                TimelineTypeDataAccess.add(getApplicationContext(), timelineTypeC);
                TimelineType timelineTypeS = new TimelineType(Tool.getUUID(), "Timeline Type for Submitted Task", Global.TIMELINE_TYPE_SUBMITTED, usrCrt, date, null, null);
                TimelineTypeDataAccess.add(getApplicationContext(), timelineTypeS);
                TimelineType timelineTypeM = new TimelineType(Tool.getUUID(), "Timeline Type for Message", Global.TIMELINE_TYPE_MESSAGE, usrCrt, date, null, null);
                TimelineTypeDataAccess.add(getApplicationContext(), timelineTypeM);
                TimelineType timelineTypeT = new TimelineType(Tool.getUUID(), "Timeline Type for Server Task", Global.TIMELINE_TYPE_TASK, usrCrt, date, null, null);
                TimelineTypeDataAccess.add(getApplicationContext(), timelineTypeT);
                TimelineType timelineTypeV = new TimelineType(Tool.getUUID(), "Timeline Type for Verified Task", Global.TIMELINE_TYPE_VERIFIED, usrCrt, date, null, null);
                TimelineTypeDataAccess.add(getApplicationContext(), timelineTypeV);
                TimelineType timelineTypeA = new TimelineType(Tool.getUUID(), "Timeline Type for Approved Task", Global.TIMELINE_TYPE_APPROVED, usrCrt, date, null, null);
                TimelineTypeDataAccess.add(getApplicationContext(), timelineTypeA);
                TimelineType timelineTypeVer = new TimelineType(Tool.getUUID(), "Timeline Type for Verification Task", Global.TIMELINE_TYPE_VERIFICATION, usrCrt, date, null, null);
                TimelineTypeDataAccess.add(getApplicationContext(), timelineTypeVer);
                TimelineType timelineTypeApp = new TimelineType(Tool.getUUID(), "Timeline Type for Approval Task", Global.TIMELINE_TYPE_APPROVAL, usrCrt, date, null, null);
                TimelineTypeDataAccess.add(getApplicationContext(), timelineTypeApp);
                TimelineType timelineTypeR = new TimelineType(Tool.getUUID(), "Timeline Type for Rejected Task", Global.TIMELINE_TYPE_REJECTED, usrCrt, date, null, null);
                TimelineTypeDataAccess.add(getApplicationContext(), timelineTypeR);
                TimelineType timelineTypeP = new TimelineType(Tool.getUUID(), "Timeline Type for Pending Task", Global.TIMELINE_TYPE_PENDING, usrCrt, date, null, null);
                TimelineTypeDataAccess.add(getApplicationContext(), timelineTypeP);
                TimelineType timelineTypeU = new TimelineType(Tool.getUUID(), "Timeline Type for Uploading Task", Global.TIMELINE_TYPE_UPLOADING, usrCrt, date, null, null);
                TimelineTypeDataAccess.add(getApplicationContext(), timelineTypeU);
                TimelineType timelineTypePush = new TimelineType(Tool.getUUID(), "Timeline Type for Push Notification", Global.TIMELINE_TYPE_PUSH_NOTIFICATION, usrCrt, date, null, null);
                TimelineTypeDataAccess.add(getApplicationContext(), timelineTypePush);
                TimelineType timelineTypeFailedSentTask = new TimelineType(Tool.getUUID(), "Timeline Type for Failed Sent Task", Global.TIMELINE_TYPE_FAILED_SENT_TASK, usrCrt, date, null, null);
                TimelineTypeDataAccess.add(getApplicationContext(), timelineTypeFailedSentTask);
            }
            //--------------------------------------------------------


            if (Global.IS_DEV)
                Logger.i("INFO", "Before start autosendlocationhistory");
            AutoSendLocationHistoryService = new Intent(this, LocationTrackingService.class);
            startService(AutoSendLocationHistoryService);
            if (Global.IS_DEV)
                Logger.i("INFO", "Start autosendlocationhistory");

            mainServices = new MainServices();
            MainServices.mainClass = mss;
//			NotificationService.mss = mss;
//			if (Global.IS_DEV) System.out.println("Before start RunNotificationService");
            RunNotificationService = new Intent(this, MainServices.class);
            startService(RunNotificationService);
//			if (Global.IS_DEV) System.out.println("Start RunNotificationService");

//			MainServices.startActionTaskList(getApplicationContext(),"","");
            //bong 8 apr 15 penjagaan autosend (interval kirim)
            try {
                if (!GeneralParameterDataAccess.getOne(getApplicationContext(), GlobalData.getSharedGlobalData().getUser().getUuid_user(), "PRM03_ASIN").getGs_value().equals("0")) {
//					autoSendTask= new AutoSendTaskThread(this);
//					autoSendTaskIntent= new Intent(this, AutoSendTaskService.class);
//					startService(autoSendTaskIntent);
//					MainServices.startActionAutoSendTask(getApplicationContext(),"","");
                    if (TaskManager.isPartial(getApplicationContext())) {
//						autoSendImage = new AutoSendImageThread(this);
//						autoSendImageIntent = new Intent(this, AutoSendImageService.class);
//						startService(autoSendImageIntent);
//						MainServices.startActionAutoSendImage(getApplicationContext(),"","");
                    }
                }
            } catch (Exception e) {
                FireCrash.log(e);
                // TODO: handle exception
            }


            Utils.setCameraParameter(getApplicationContext());

            new AsyncTask<Void, Void, Void>() {

                @Override
                protected Void doInBackground(Void... params) {
                    List<Scheme> schemes = SchemeDataAccess.getAll(getApplicationContext());
                    if (Global.TempScheme != null)
                        Global.TempScheme = null;
                    Global.TempScheme = new HashMap<String, Date>();

                    for (Scheme scheme : schemes) {
                        Global.TempScheme.put(scheme.getUuid_scheme(), scheme.getScheme_last_update());
                    }

                    return null;
                }
            }.execute();
        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: handle exception
        }

    }

    public void UninstallerHandler() {
        String fullname = GlobalData.getSharedGlobalData().getUser().getFullname() != null ? GlobalData.getSharedGlobalData().getUser().getFullname() : "";
        String message = getString(R.string.inactive_user, fullname);
        final NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(this);
        dialogBuilder.withTitle(getString(R.string.warning_capital))
                .withMessage(message)
                .withButton1Text("Uninstall")
                .setButton1Click(new View.OnClickListener() {

                    @Override
                    public void onClick(View arg0) {
                        dialogBuilder.dismiss();
                        String packageName = getPackageName();
                        Intent intent = new Intent(Intent.ACTION_UNINSTALL_PACKAGE);
                        intent.setData(Uri.parse("package:" + packageName));
                        startActivityForResult(intent, 1);
                    }
                })
                .isCancelable(false)
                .isCancelableOnTouchOutside(false)
                .show();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 1) {
            if (resultCode == RESULT_OK) {
                Logger.d("TAG", "onActivityResult: user accepted the (un)install");
            } else if (resultCode == RESULT_CANCELED) {
                Logger.d("TAG", "onActivityResult: user canceled the (un)install");
                new CheckActiveUser().execute();
            } else if (resultCode == RESULT_FIRST_USER) {
                Logger.d("TAG", "onActivityResult: failed to (un)install");
            }
        } else if (requestCode == 199) {
            finish();
            startActivity(getIntent());
        }
    }

    @Override
    public void onLocationChanged(Location location) {
        if (location != null)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (location.isFromMockProvider())
                    DialogManager.showMockDialog(MainMenuActivity.this);
            }
    }

    @Override
    public void onStatusChanged(String provider, int status, Bundle extras) {
    }

    @Override
    public void onProviderEnabled(String provider) {
        DialogManager.closeGPSAlert();
    }
//    @Override
//    protected void onUserLeaveHint() {   
//    	//nge override home button agar pas minimize dan buka lagi aplikasi, akan kembali ke timeline
//    	//dikarenakan akan terjadi error jika di minimiza di tampilan Maps(checkin/checkout)
//    	if(Constants.inAbsent)
//    		goTimeline(1);
//    	
//    	Constants.inAbsent=false;
//        super.onUserLeaveHint();
//    }

    @Override
    public void onProviderDisabled(String provider) {
        DialogManager.showGPSAlert(this);
    }

    private void checkAppVersion() {
        if (UserSession.getAppVersion() == 0) { // simpan version code untuk pertama kali
            UserSession.setAppVersion(AppContext.getInstance().getVersionCode());
        }

        if (UserSession.getAppVersion() < AppContext.getInstance().getVersionCode()) {
            DialogManager.showForceSyncronize(this);
        }
    }

    public void checkFlagTracking() {
        try {
            User user = GlobalData.getSharedGlobalData().getUser();
            if (null != user && null != user.getIs_tracking() && user.getIs_tracking().equals("1")) {
                String trackingDays;
                List trackingDaysList;
                int thisDayInt = Calendar.getInstance().get(Calendar.DAY_OF_WEEK);
                String thisDay;
                if (thisDayInt == 1) {
                    thisDayInt = 7;
                } else {
                    thisDayInt -= 1;
                }
                thisDay = String.valueOf(thisDayInt);

                trackingDays = user.getTracking_days();
                if (null != trackingDays) {
                    trackingDaysList = Arrays.asList(trackingDays.split(";"));
                } else {
                    return;
                }

                String hourFromWebStart = user.getStart_time();
                Calendar calStart = Calendar.getInstance();
                if (null != hourFromWebStart) {
                    String hourSplitStart[] = hourFromWebStart.split(":");
                    int hourStart = Integer.parseInt(hourSplitStart[0]);
                    int minuteStart = Integer.parseInt(hourSplitStart[1]);

                    calStart.set(Calendar.HOUR_OF_DAY, hourStart);
                    calStart.set(Calendar.MINUTE, minuteStart);
                } else {
                    return;
                }

                String hourFromWeb = user.getEnd_time();
                Calendar cal = Calendar.getInstance();
                if (null != hourFromWeb) {
                    String hourSplit[] = hourFromWeb.split(":");
                    int hour = Integer.parseInt(hourSplit[0]);
                    int minute = Integer.parseInt(hourSplit[1]);

                    cal.set(Calendar.HOUR_OF_DAY, hour);
                    cal.set(Calendar.MINUTE, minute);
                } else {
                    return;
                }

                if (trackingDaysList.contains(thisDay)) {
                    if (Calendar.getInstance().after(calStart) && Calendar.getInstance().before(cal)) {
                        if (AutoSendLocationHistoryService == null) {
                            AutoSendLocationHistoryService = new Intent(getApplicationContext(), LocationTrackingService.class);
                            startService(AutoSendLocationHistoryService);
                        }
                    } else {
                        if (AutoSendLocationHistoryService != null) {
                            stopService(AutoSendLocationHistoryService);
                        }
                    }
                } else {
                    if (AutoSendLocationHistoryService != null) {
                        stopService(AutoSendLocationHistoryService);
                    }
                }
            } else {
                if (AutoSendLocationHistoryService != null) {
                    stopService(AutoSendLocationHistoryService);
                }
            }
        } catch (Exception e) {
            FireCrash.log(e);
//            ACRA.getErrorReporter().putCustomData("NotificationThread", e.getMessage());
//            ACRA.getErrorReporter().putCustomData("NotificationThread", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
//            ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat checkFlagTacking"));
        }
    }

    public class DrawerLeftHandler extends Handler {
        @Override
        public void handleMessage(Message msg) {
            try {
                Bundle bundle = msg.getData();

                boolean refreshCounter = bundle.getBoolean(Global.BUND_KEY_REFRESHCOUNTER, false);
                if (refreshCounter && isMainMenuOpen) {
                    if (MainMenuActivity.mnLog != null)
                        MainMenuActivity.menuAdapter.getMenuModel(MainMenuActivity.mnLog.getTitle()).setCounter(String.valueOf(TaskLog.getCounterLog(MainMenuActivity.this)));
                    if (MainMenuActivity.mnTaskList != null)
                        MainMenuActivity.menuAdapter.getMenuModel(MainMenuActivity.mnTaskList.getTitle()).setCounter(String.valueOf(ToDoList.getCounterTaskList(MainMenuActivity.this)));
                    if (MainMenuActivity.mnTaskPromiseToSurvey != null)
                        MainMenuActivity.menuAdapter.getMenuModel(MainMenuActivity.mnTaskPromiseToSurvey.getTitle()).setCounter(String.valueOf(ToDoList.getCounterTaskListTypes(MainMenuActivity.this, Global.FORM_NAME_PROMISE_TO_SURVEY)));
                    if (MainMenuActivity.mnTaskPreSurvey != null)
                        MainMenuActivity.menuAdapter.getMenuModel(MainMenuActivity.mnTaskPreSurvey.getTitle()).setCounter(String.valueOf(ToDoList.getCounterTaskListTypes(MainMenuActivity.this, Global.FORM_NAME_PRE_SURVEY)));
                    if (MainMenuActivity.mnTaskOts != null)
                        MainMenuActivity.menuAdapter.getMenuModel(MainMenuActivity.mnTaskOts.getTitle()).setCounter(String.valueOf(ToDoList.getCounterTaskListTypes(MainMenuActivity.this, Global.FORM_NAME_OTS)));
                    if (MainMenuActivity.mnTaskGuarantor != null)
                        MainMenuActivity.menuAdapter.getMenuModel(MainMenuActivity.mnTaskGuarantor.getTitle()).setCounter(String.valueOf(ToDoList.getCounterTaskListTypes(MainMenuActivity.this, Global.FORM_NAME_GUARANTOR)));
                    if (MainMenuActivity.mnTaskUpdate != null)
                        MainMenuActivity.menuAdapter.getMenuModel(MainMenuActivity.mnTaskUpdate.getTitle()).setCounter(String.valueOf(ToDoList.getCounterTaskUpdate(MainMenuActivity.this)));
                    if (MainMenuActivity.mnSVYVerify != null)
                        MainMenuActivity.menuAdapter.getMenuModel(MainMenuActivity.mnSVYVerify.getTitle()).setCounter(String.valueOf(ToDoList.getCounterVerificationTask(MainMenuActivity.this)));
                    if (MainMenuActivity.mnSVYApproval != null)
                        MainMenuActivity.menuAdapter.getMenuModel(MainMenuActivity.mnSVYApproval.getTitle()).setCounter(String.valueOf(ToDoList.getCounterApprovalTask(MainMenuActivity.this)));
                    if (MainMenuActivity.mnSVYVerifyByBranch != null)
                        MainMenuActivity.menuAdapter.getMenuModel(MainMenuActivity.mnSVYVerifyByBranch.getTitle()).setCounter(String.valueOf(ToDoList.getCounterVerificationTaskByBranch(MainMenuActivity.this)));
                    if (MainMenuActivity.mnSVYApprovalByBranch != null)
                        MainMenuActivity.menuAdapter.getMenuModel(MainMenuActivity.mnSVYApprovalByBranch.getTitle()).setCounter(String.valueOf(ToDoList.getCounterApprovalTaskByBranch(MainMenuActivity.this)));

                    if (MainMenuActivity.menuAdapter != null)
                        MainMenuActivity.menuAdapter.notifyDataSetChanged();
                    return;
                }
                String title = bundle.getString(Global.BUND_KEY_CALL_MAIN_MENU);
                int position = 0;
                if (getModels() != null && title != null) {
                    int i = 0;
                    for (MenuModel model : getModels()) {
                        if (model.getTitle().equals(title))
                            position = i;
                        i++;
                    }
                }
                tempPosition = position;
                mDrawerListLeft.setItemChecked(position, true);

            } catch (Exception e) {
                FireCrash.log(e);
                Logger.d("refresh_test", e.getMessage());
            }
        }
    }

    //2018-11-07 | Nendi - Add clear all back stack entry
    public void clearBackstack() {
        fragmentManager.popBackStack(null, FragmentManager.POP_BACK_STACK_INCLUSIVE);
    }

    public class StackHandler extends Handler {
        @Override
        public void handleMessage(Message msg) {
            Bundle bundle = msg.getData();
            String action = bundle.getString("action");
            //TODO: Test white on submit task
            if (action.equals(Global.MAINMENU_NOTIFICATION_KEY)) {
//                goTimeline(0);
                Fragment fragment1 = new Timeline_Activity();
                FragmentTransaction transaction = fragmentManager.beginTransaction();
                transaction.add(R.id.content_frame, fragment1);
                transaction.commitAllowingStateLoss();

                mDrawerListLeft.setItemChecked(0, true);
            }
            setTitle(models.get(0).getTitle());
        }
    }

    private class CheckActiveUser extends AsyncTask<Void, Void, Boolean> {

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
        }

        @Override
        protected Boolean doInBackground(Void... voids) {
//        JsonResponseRetrieveTaskList jrsrtl = new JsonResponseRetrieveTaskList();
            MssRequestType mrt = new MssRequestType();
            mrt.setAudit(GlobalData.getSharedGlobalData().getAuditData());

            String json = GsonHelper.toJson(mrt);

            boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
            boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
            HttpCryptedConnection httpConn = new HttpCryptedConnection(mainMenuActivity, encrypt, decrypt);
            HttpConnectionResult serverResult = null;
            String url = GlobalData.getSharedGlobalData().getURL_GET_TASKLIST();
            if (Tool.isInternetconnected(MainMenuActivity.this)) {
                try {
                    serverResult = httpConn
                            .requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
                } catch (Exception ex) {
                    // errMessage = e.getMessage();
                }
                if (serverResult != null && serverResult.isOK()) {
                    String sentStatus = serverResult.getResult();

                    JsonResponseRetrieveTaskList jrsrtl = GsonHelper.fromJson(sentStatus, JsonResponseRetrieveTaskList.class);

                    if (jrsrtl.getStatus().getCode() == Global.STATUS_CODE_APPL_CLEANSING) {

                        UninstallerHandler();
                        MainMenuActivity.Force_Uninstall = true;

                    } else {
                        MainMenuActivity.Force_Uninstall = false;

                    }
                }
            }
            return null;
        }

        @Override
        protected void onPostExecute(Boolean aVoid) {
            super.onPostExecute(aVoid);
        }
    }

    public void schedulePushSync() {
        //kalo mau tes jalanin
//        startService(new Intent(this, PushSyncServiceBackground.class));
        if(GlobalData.getSharedGlobalData().getUser() == null) {
            User tempUser = UserDataAccess.getOneData(this);
            GlobalData.getSharedGlobalData().setUser(tempUser);
        }
        String pushSyncTime = ""; // in miliseconds
//        try {
//            GeneralParameter gp = GeneralParameterDataAccess.getOne(this,
//                    GlobalData.getSharedGlobalData().getUser().getUuid_user(),
//                    Global.GS_PUSHSYNC_TIME);
//            if (gp != null) {
//                pushSyncTime = gp.getGs_value();
//            }
//        } catch (Exception e) {
//            FireCrash.log(e);
//            pushSyncTime = "";
//        }
//        pushSyncTime = GlobalData.getSharedGlobalData().getUser().getPushsync_time();
        try {
            pushSyncTime = GlobalData.getSharedGlobalData().getUser().getPushsync_time();
        } catch (Exception e) {
            FireCrash.log(e);
            pushSyncTime = "";
        }

        if (!pushSyncTime.equalsIgnoreCase("")) {
            String pushsyncTime = pushSyncTime;
//            String pushsyncTime = "11:55";
            String[] str = pushsyncTime.split(":");
            PendingIntent pendingIntent;
            Intent myIntent = new Intent(this, PushSyncServiceBackground.class);
            pendingIntent = PendingIntent.getService(this, 0, myIntent, 0);
            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
            try {
                calendar.setTime(sdf.parse(pushsyncTime));
            } catch (ParseException e) {
                e.printStackTrace();
            }
            if(str != null){
                calendar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(str[0]));
                calendar.set(Calendar.MINUTE, Integer.parseInt(str[1])); }
            //kalo mau hardcode jam triggernya
//        calendar.set(Calendar.HOUR_OF_DAY, 13);
//        calendar.set(Calendar.MINUTE, 50);
            AlarmManager alarmManager = (AlarmManager)getSystemService(ALARM_SERVICE);
            //SET DI JAM
            alarmManager.setRepeating(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(),
                    1000 * 60 * 5 , pendingIntent);
        }

    }

}