package com.adins.mss.dao;

import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;

import com.adins.mss.dao.Sync;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "MS_SYNC".
*/
public class SyncDao extends AbstractDao<Sync, String> {

    public static final String TABLENAME = "MS_SYNC";

    /**
     * Properties of entity Sync.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_sync = new Property(0, String.class, "uuid_sync", true, "UUID_SYNC");
        public final static Property Tabel_name = new Property(1, String.class, "tabel_name", false, "TABEL_NAME");
        public final static Property Lov_group = new Property(2, String.class, "lov_group", false, "LOV_GROUP");
        public final static Property Path = new Property(3, String.class, "path", false, "PATH");
        public final static Property Dtm_upd = new Property(4, java.util.Date.class, "dtm_upd", false, "DTM_UPD");
        public final static Property Flag = new Property(5, Integer.class, "flag", false, "FLAG");
    };


    public SyncDao(DaoConfig config) {
        super(config);
    }
    
    public SyncDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"MS_SYNC\" (" + //
                "\"UUID_SYNC\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_sync
                "\"TABEL_NAME\" TEXT," + // 1: tabel_name
                "\"LOV_GROUP\" TEXT," + // 2: lov_group
                "\"PATH\" TEXT," + // 3: path
                "\"DTM_UPD\" INTEGER," + // 4: dtm_upd
                "\"FLAG\" INTEGER);"); // 5: flag
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"MS_SYNC\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, Sync entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_sync());
 
        String tabel_name = entity.getTabel_name();
        if (tabel_name != null) {
            stmt.bindString(2, tabel_name);
        }
 
        String lov_group = entity.getLov_group();
        if (lov_group != null) {
            stmt.bindString(3, lov_group);
        }
 
        String path = entity.getPath();
        if (path != null) {
            stmt.bindString(4, path);
        }
 
        java.util.Date dtm_upd = entity.getDtm_upd();
        if (dtm_upd != null) {
            stmt.bindLong(5, dtm_upd.getTime());
        }
 
        Integer flag = entity.getFlag();
        if (flag != null) {
            stmt.bindLong(6, flag);
        }
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public Sync readEntity(Cursor cursor, int offset) {
        Sync entity = new Sync( //
            cursor.getString(offset + 0), // uuid_sync
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // tabel_name
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // lov_group
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // path
            cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)), // dtm_upd
            cursor.isNull(offset + 5) ? null : cursor.getInt(offset + 5) // flag
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, Sync entity, int offset) {
        entity.setUuid_sync(cursor.getString(offset + 0));
        entity.setTabel_name(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setLov_group(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setPath(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setDtm_upd(cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)));
        entity.setFlag(cursor.isNull(offset + 5) ? null : cursor.getInt(offset + 5));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(Sync entity, long rowId) {
        return entity.getUuid_sync();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(Sync entity) {
        if(entity != null) {
            return entity.getUuid_sync();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
}
