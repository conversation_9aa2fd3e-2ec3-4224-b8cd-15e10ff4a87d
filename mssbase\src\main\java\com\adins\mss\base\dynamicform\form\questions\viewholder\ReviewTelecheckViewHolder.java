package com.adins.mss.base.dynamicform.form.questions.viewholder;

import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.adins.mss.base.R;
import com.adins.mss.base.dynamicform.form.models.TeleCheckResponse;
import com.adins.mss.base.dynamicform.form.questions.OnQuestionClickListener;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.foundation.questiongenerator.QuestionBean;

public class ReviewTelecheckViewHolder extends RecyclerView.ViewHolder {
    private final RelativeLayout layout;
    private final TextView mLabelNo;
    private final TextView mQuestionLabel;
    private final TextView mQuestionAnswer;
    private final OnQuestionClickListener listener;
    private final TextView mValidAnswer;
    private QuestionBean bean;

    public ReviewTelecheckViewHolder(View itemView, OnQuestionClickListener listener) {
        super(itemView);
        layout = (RelativeLayout) itemView.findViewById(R.id.submitLayerReviewLayout);
        mLabelNo = (TextView) itemView.findViewById(R.id.questionNoLabel);
        mQuestionLabel = (TextView) itemView.findViewById(R.id.questionTextLabel);
        mQuestionAnswer = (TextView) itemView.findViewById(R.id.questionTextAnswer);
        mValidAnswer = (TextView) itemView.findViewById(R.id.questionValidAnswer);
        this.listener = listener;
    }
    public void bind(final QuestionBean item, final int group, final int number) {
        bean = item;
        mLabelNo.setText(number + ".");
        String qLabel = bean.getQuestion_label();
        mQuestionLabel.setText(qLabel);
        TeleCheckResponse response = GsonHelper.fromJson(bean.getAnswer(), TeleCheckResponse.class);


        mQuestionAnswer.setText(response.getPhoneNumber());
        mValidAnswer.setText(response.getMessage());
        layout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                listener.onReviewClickListener(bean, group, number - 1);
            }
        });
    }
}
