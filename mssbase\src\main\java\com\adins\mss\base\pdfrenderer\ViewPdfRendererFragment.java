package com.adins.mss.base.pdfrenderer;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Point;
import android.graphics.pdf.PdfRenderer;
import android.os.Build;
import android.os.Bundle;
import android.os.ParcelFileDescriptor;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.adins.mss.base.BaseActivity;
import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.dynamicform.form.FragmentQuestion;
import com.adins.mss.base.util.LocaleHelper;
import com.adins.mss.constant.Global;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

@RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
public class ViewPdfRendererFragment extends BaseActivity implements View.OnClickListener {
    private static final int MAX_BITMAP_SIZE = 100 * 1024 * 1024; // 100 MB
    private static final float DEFAULT_ZOOM = 2;
    public static final String KEY_QUESTION_BEAN = "KEY_QUESTION_BEAN";
    private float currentZoomLevel = DEFAULT_ZOOM;

    private ParcelFileDescriptor mFileDescriptor;
    private PdfRenderer mPdfRenderer;
    private PdfRenderer.Page mCurrentPage;

    private RecyclerView mRecyclerView;
    private ImageView mButtonZoomOut;
    private String answer;

    private String urlFileName;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (!Global.IS_DEV) {
            //To can't screenshoot
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
        }

        setContentView(R.layout.fragment_view_pdf_renderer);
        mRecyclerView = findViewById(R.id.recycleViewPdf);
        ImageView mButtonZoomIn = findViewById(R.id.btnZoomIn);
        mButtonZoomOut = findViewById(R.id.btnZoomOut);
        Button mButtonAgree = findViewById(R.id.btnAgree);

        answer = getIntent().getStringExtra("ANSWER");
        if (StringUtils.isNotBlank(answer) && Global.ACCEPTED_AGREEMENT.equalsIgnoreCase(answer.split(Global.DELIMETER_DATA4)[0])) {
            mButtonAgree.setVisibility(View.GONE);
        } else {
            mButtonAgree.setVisibility(View.VISIBLE);
        }

        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this,
                LinearLayoutManager.VERTICAL, false);
        mRecyclerView.setLayoutManager(linearLayoutManager);

        urlFileName = getIntent().getStringExtra("URL_FILE");

        mButtonZoomIn.setOnClickListener(this);
        mButtonZoomOut.setOnClickListener(this);
        mButtonAgree.setOnClickListener(this);

        mButtonZoomIn.setVisibility(View.GONE);
        mButtonZoomOut.setVisibility(View.GONE);
    }

    @Override
    protected void attachBaseContext(Context newBase) {
        Context context = newBase;
        Locale locale;
        try {
            if (StringUtils.isNotBlank(GlobalData.getSharedGlobalData().getLocale())) {
                locale = new Locale(GlobalData.getSharedGlobalData().getLocale());
            } else  {
                locale = new Locale(LocaleHelper.ENGLSIH);
            }
            context = LocaleHelper.wrap(newBase, locale);
        } catch (Exception e) {
            locale = new Locale(LocaleHelper.ENGLSIH);
            context = LocaleHelper.wrap(newBase, locale);
        } finally {
            super.attachBaseContext(context);
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        try {
            openRenderer(urlFileName);
        } catch (IOException e) {
            if (Global.IS_DEV) {
                e.printStackTrace();
            }
            String message = "Error View PDF: " + e.getMessage();
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
            FireCrash.log(e);
            this.finish();
        } catch (Exception ex) {
            if (Global.IS_DEV) {
                ex.printStackTrace();
            }
            String message = "Error View PDF: " + ex.getMessage();
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
            FireCrash.log(ex);
            this.finish();
        }
    }

    @Override
    public void onStop() {
        try {
            closeRenderer();
        } catch (Exception e) {
            e.printStackTrace();
        }
        super.onStop();
    }

    private int getPageCount() {
        return mPdfRenderer.getPageCount();
    }

    private void openRenderer(String urlFilename) throws IOException {
        File file = new File(urlFilename);
        mFileDescriptor = ParcelFileDescriptor.open(file, ParcelFileDescriptor.MODE_READ_ONLY);
        // This is the PdfRenderer we use to render the PDF.
        mPdfRenderer = new PdfRenderer(mFileDescriptor);
        showPagePdf();
    }

    private void showPagePdf() {
        ArrayList<Bitmap> bitmaps = new ArrayList<>();
        for (int index = 0; index < getPageCount(); index++) {
            Bitmap bitmap = getPagePdf(index);
            bitmaps.add(bitmap);
            if (null != mCurrentPage) {
                mCurrentPage.close();
            }
        }

        String watermark = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(answer) && answer.split(Global.DELIMETER_DATA4).length > 1) {
            watermark = answer.split(Global.DELIMETER_DATA4)[1];
        }
        ViewPdfAdapter adapter = new ViewPdfAdapter(bitmaps, watermark);
        mRecyclerView.setAdapter(adapter);
    }

    private Bitmap getPagePdf(int index) {
        if (mPdfRenderer.getPageCount() <= index) {
            return null;
        }
        // Use openPage to open a specific page in PDF.
        mCurrentPage = mPdfRenderer.openPage(index);
        // Important: the destination bitmap must be ARGB (not RGB).
        Display display = getWindowManager().getDefaultDisplay();
        Point size = new Point();
        display.getSize(size);
        int widthDisplay = size.x;
        int heightDisplay = size.y;

        int newWidth = mCurrentPage.getWidth();
        int newHeight = mCurrentPage.getHeight();

        float scaleMin = Math.min((float) widthDisplay / (float) newWidth, (float) heightDisplay / (float) newHeight);
        Bitmap bitmap = Bitmap.createBitmap((int) scaleMin * newWidth, (int) scaleMin * newHeight, Bitmap.Config.ARGB_8888);

        if (bitmap.getByteCount() <= MAX_BITMAP_SIZE) {
            Matrix matrix = new Matrix();

            int density=DisplayMetrics.DENSITY_TV;
            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M) {
                density = DisplayMetrics.DENSITY_DEVICE_STABLE;
            }
            float dpiAdjustedZoomLevel = currentZoomLevel *  density / getResources().getDisplayMetrics().densityDpi;
            matrix.setScale(dpiAdjustedZoomLevel, dpiAdjustedZoomLevel);

            // Here, we render the page onto the Bitmap.
            // To render a portion of the page, use the second and third parameter. Pass nulls to get the default result.
            // Pass either RENDER_MODE_FOR_DISPLAY or RENDER_MODE_FOR_PRINT for the last parameter.
            mCurrentPage.render(bitmap, null, null, PdfRenderer.Page.RENDER_MODE_FOR_DISPLAY);
            return bitmap;
        } else {
            Toast.makeText(this, "Zoom has reached Max!", Toast.LENGTH_SHORT).show();
        }
        return null;
    }

    private void closeRenderer() throws IOException {
        if (null != mCurrentPage) {
            mCurrentPage.close();
            mCurrentPage = null;
        }
        if (null != mPdfRenderer) {
            mPdfRenderer.close();
        }
        if (null != mFileDescriptor) {
            mFileDescriptor.close();
        }
    }

    @Override
    public void onClick(View v) {
        int button = v.getId();
        if (button == R.id.btnZoomOut) {
            if (currentZoomLevel - 1 > 0) {
                --currentZoomLevel;
                showPagePdf();
            }
        } else if (button == R.id.btnZoomIn) {
            ++currentZoomLevel;
            showPagePdf();
        } else if (button == R.id.btnAgree) {
            Intent intent = new Intent();
            Bundle bundle = new Bundle();
            bundle.putString(FragmentQuestion.BUND_KEY_RESULT, Global.ACCEPTED_AGREEMENT+ Global.DELIMETER_DATA4+DateFormatUtils.format(new Date(), "dd MMMM yyyy HH:mm"));
            intent.putExtras(bundle);
            setResult(Activity.RESULT_OK, intent);
            finish();
        }
    }

    private static class ViewPdfAdapter extends RecyclerView.Adapter<ViewPdfAdapter.ViewHolder> {

        List<Bitmap> dataPdf;
        String txtWatermark;

        public ViewPdfAdapter(List <Bitmap> dataPdf, String txtWatermark) {
            this.dataPdf = dataPdf;
            this.txtWatermark = txtWatermark;
        }

        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.fragment_view_pdf_page, parent, false);
            return new ViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            holder.imageView.setImageBitmap(dataPdf.get(position));
            if (StringUtils.isNotBlank(txtWatermark)) {
                holder.textWatermark.setVisibility(View.VISIBLE);
                holder.textWatermark.setText(txtWatermark);
                holder.textWatermark.setTextColor(Color.argb(80, 0, 0, 0));
            } else {
                holder.textWatermark.setVisibility(View.GONE);
            }
        }

        @Override
        public int getItemCount() {
            return dataPdf.size();
        }

        public static class ViewHolder extends RecyclerView.ViewHolder {

            ImageView imageView;
            TextView textWatermark;
            public ViewHolder(@NonNull View itemView) {
                super(itemView);

                imageView = itemView.findViewById(R.id.imageViewPdf);
                textWatermark = itemView.findViewById(R.id.textWatermark);
            }
        }
    }

}