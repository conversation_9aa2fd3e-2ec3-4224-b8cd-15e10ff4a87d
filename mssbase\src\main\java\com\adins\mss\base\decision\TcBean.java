package com.adins.mss.base.decision;

import java.util.ArrayList;
import java.util.List;

public class TcBean {
	private String tcCode;
	private String pior;
	private String mandatory;
	private List<String> listMandatory = new ArrayList<>();
	
	public String getTcCode() {
		return tcCode;
	}
	public void setTcCode(String tcCode) {
		this.tcCode = tcCode;
	}
	public String getPior() {
		return pior;
	}
	public void setPior(String pior) {
		this.pior = pior;
	}
	public String getMandatory() {
		return mandatory;
	}
	public void setMandatory(String mandatory) {
		this.mandatory = mandatory;
	}
	
	public void AddTCCode(String tcCode){
		this.tcCode = tcCode;
	}
	
	public void AddPriorTo(String pior){
		this.pior = pior;
	}
	
	public void AddMandatory(String mandatory){
		this.mandatory = mandatory;
		listing();
	}

	
	public List<String> getListMandatory() {
		return listMandatory;
	}
	public void setListMandatory(List<String> listMandatory) {
		this.listMandatory = listMandatory;
	}

	private void listing(){
		if ("APP".equalsIgnoreCase(pior) && 
				checkBool(mandatory) ) {
			listMandatory.add(tcCode);
		}
	}
	
	private boolean checkBool(String check) {
		boolean bool = false;
		if ("1".equals(check) ||
				"yes".equalsIgnoreCase(check) ||
				"y".equalsIgnoreCase(check) ||
				"true".equalsIgnoreCase(check) ||
				"t".equalsIgnoreCase(check) ) {
			bool = true;
		}
		
		return bool;
	}
	
}
