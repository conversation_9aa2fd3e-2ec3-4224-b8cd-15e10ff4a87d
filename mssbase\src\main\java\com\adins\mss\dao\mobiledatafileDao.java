package com.adins.mss.dao;

import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;

import com.adins.mss.dao.mobiledatafile;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "MS_MOBILEDATAFILES".
*/
public class mobiledatafileDao extends AbstractDao<mobiledatafile, Long> {

    public static final String TABLENAME = "MS_MOBILEDATAFILES";

    /**
     * Properties of entity mobiledatafile.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Id_datafile = new Property(0, long.class, "id_datafile", true, "ID_DATAFILE");
        public final static Property Is_active = new Property(1, String.class, "is_active", false, "IS_ACTIVE");
        public final static Property Max_timestamp = new Property(2, java.util.Date.class, "max_timestamp", false, "MAX_TIMESTAMP");
        public final static Property File_url = new Property(3, String.class, "file_url", false, "FILE_URL");
        public final static Property Alternate_file_url = new Property(4, String.class, "alternate_file_url", false, "ALTERNATE_FILE_URL");
        public final static Property Hash_sha1 = new Property(5, String.class, "hash_sha1", false, "HASH_SHA1");
        public final static Property Usr_crt = new Property(6, String.class, "usr_crt", false, "USR_CRT");
        public final static Property Dtm_crt = new Property(7, java.util.Date.class, "dtm_crt", false, "DTM_CRT");
        public final static Property Usr_upd = new Property(8, String.class, "usr_upd", false, "USR_UPD");
        public final static Property Dtm_upd = new Property(9, java.util.Date.class, "dtm_upd", false, "DTM_UPD");
        public final static Property Downloaded_file_path = new Property(10, String.class, "downloaded_file_path", false, "DOWNLOADED_FILE_PATH");
        public final static Property Import_flag = new Property(11, Boolean.class, "import_flag", false, "IMPORT_FLAG");
    };


    public mobiledatafileDao(DaoConfig config) {
        super(config);
    }
    
    public mobiledatafileDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"MS_MOBILEDATAFILES\" (" + //
                "\"ID_DATAFILE\" INTEGER PRIMARY KEY NOT NULL ," + // 0: id_datafile
                "\"IS_ACTIVE\" TEXT," + // 1: is_active
                "\"MAX_TIMESTAMP\" INTEGER," + // 2: max_timestamp
                "\"FILE_URL\" TEXT," + // 3: file_url
                "\"ALTERNATE_FILE_URL\" TEXT," + // 4: alternate_file_url
                "\"HASH_SHA1\" TEXT," + // 5: hash_sha1
                "\"USR_CRT\" TEXT," + // 6: usr_crt
                "\"DTM_CRT\" INTEGER," + // 7: dtm_crt
                "\"USR_UPD\" TEXT," + // 8: usr_upd
                "\"DTM_UPD\" INTEGER," + // 9: dtm_upd
                "\"DOWNLOADED_FILE_PATH\" TEXT," + // 10: downloaded_file_path
                "\"IMPORT_FLAG\" INTEGER);"); // 11: import_flag
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"MS_MOBILEDATAFILES\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, mobiledatafile entity) {
        stmt.clearBindings();
        stmt.bindLong(1, entity.getId_datafile());
 
        String is_active = entity.getIs_active();
        if (is_active != null) {
            stmt.bindString(2, is_active);
        }
 
        java.util.Date max_timestamp = entity.getMax_timestamp();
        if (max_timestamp != null) {
            stmt.bindLong(3, max_timestamp.getTime());
        }
 
        String file_url = entity.getFile_url();
        if (file_url != null) {
            stmt.bindString(4, file_url);
        }
 
        String alternate_file_url = entity.getAlternate_file_url();
        if (alternate_file_url != null) {
            stmt.bindString(5, alternate_file_url);
        }
 
        String hash_sha1 = entity.getHash_sha1();
        if (hash_sha1 != null) {
            stmt.bindString(6, hash_sha1);
        }
 
        String usr_crt = entity.getUsr_crt();
        if (usr_crt != null) {
            stmt.bindString(7, usr_crt);
        }
 
        java.util.Date dtm_crt = entity.getDtm_crt();
        if (dtm_crt != null) {
            stmt.bindLong(8, dtm_crt.getTime());
        }
 
        String usr_upd = entity.getUsr_upd();
        if (usr_upd != null) {
            stmt.bindString(9, usr_upd);
        }
 
        java.util.Date dtm_upd = entity.getDtm_upd();
        if (dtm_upd != null) {
            stmt.bindLong(10, dtm_upd.getTime());
        }
 
        String downloaded_file_path = entity.getDownloaded_file_path();
        if (downloaded_file_path != null) {
            stmt.bindString(11, downloaded_file_path);
        }
 
        Boolean import_flag = entity.getImport_flag();
        if (import_flag != null) {
            stmt.bindLong(12, import_flag ? 1L: 0L);
        }
    }

    /** @inheritdoc */
    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.getLong(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public mobiledatafile readEntity(Cursor cursor, int offset) {
        mobiledatafile entity = new mobiledatafile( //
            cursor.getLong(offset + 0), // id_datafile
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // is_active
            cursor.isNull(offset + 2) ? null : new java.util.Date(cursor.getLong(offset + 2)), // max_timestamp
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // file_url
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // alternate_file_url
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // hash_sha1
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // usr_crt
            cursor.isNull(offset + 7) ? null : new java.util.Date(cursor.getLong(offset + 7)), // dtm_crt
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8), // usr_upd
            cursor.isNull(offset + 9) ? null : new java.util.Date(cursor.getLong(offset + 9)), // dtm_upd
            cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10), // downloaded_file_path
            cursor.isNull(offset + 11) ? null : cursor.getShort(offset + 11) != 0 // import_flag
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, mobiledatafile entity, int offset) {
        entity.setId_datafile(cursor.getLong(offset + 0));
        entity.setIs_active(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setMax_timestamp(cursor.isNull(offset + 2) ? null : new java.util.Date(cursor.getLong(offset + 2)));
        entity.setFile_url(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setAlternate_file_url(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setHash_sha1(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setUsr_crt(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setDtm_crt(cursor.isNull(offset + 7) ? null : new java.util.Date(cursor.getLong(offset + 7)));
        entity.setUsr_upd(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
        entity.setDtm_upd(cursor.isNull(offset + 9) ? null : new java.util.Date(cursor.getLong(offset + 9)));
        entity.setDownloaded_file_path(cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10));
        entity.setImport_flag(cursor.isNull(offset + 11) ? null : cursor.getShort(offset + 11) != 0);
     }
    
    /** @inheritdoc */
    @Override
    protected Long updateKeyAfterInsert(mobiledatafile entity, long rowId) {
        entity.setId_datafile(rowId);
        return rowId;
    }
    
    /** @inheritdoc */
    @Override
    public Long getKey(mobiledatafile entity) {
        if(entity != null) {
            return entity.getId_datafile();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
}
