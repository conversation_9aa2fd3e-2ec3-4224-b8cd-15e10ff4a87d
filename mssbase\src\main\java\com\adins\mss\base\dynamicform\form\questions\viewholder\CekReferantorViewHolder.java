package com.adins.mss.base.dynamicform.form.questions.viewholder;

import android.app.ProgressDialog;
import android.os.AsyncTask;
import android.os.Bundle;
import android.text.InputType;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.dynamicform.Constant;
import com.adins.mss.base.dynamicform.DynamicFormActivity;
import com.adins.mss.base.dynamicform.form.FragmentQuestion;
import com.adins.mss.base.getotr.GetOTRApi;
import com.adins.mss.base.getotr.JsonRequestGetOTR;
import com.adins.mss.base.getotr.JsonResponseGetOTR;
import com.adins.mss.base.getreferantor.CekReferantorApi;
import com.adins.mss.base.getreferantor.JsonRequestCekReferantor;
import com.adins.mss.base.getreferantor.JsonResponseCekReferantor;
import com.adins.mss.constant.Global;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.questiongenerator.QuestionBean;
import com.adins.mss.foundation.questiongenerator.form.QuestionView;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

public class CekReferantorViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {

    private QuestionBean qBean;
    private FragmentActivity mActivity;
    private QuestionView mQuestionLayout;

    private TextView mQuestionLabel;
    private EditText mTextCekReferantor;
    private TextView mTextResult;
    private Button mButtonCekReferantor;

    public CekReferantorViewHolder(View itemView, FragmentActivity mActivity) {
        super(itemView);
        this.mActivity = mActivity;
        this.mQuestionLayout = itemView.findViewById(R.id.questionCekReferantorLayout);

        this.mQuestionLabel = itemView.findViewById(R.id.lblQuestionCekReferantor);
        this.mTextCekReferantor = itemView.findViewById(R.id.txtCekReferantor);
        this.mTextResult = itemView.findViewById(R.id.txtResult);
        this.mButtonCekReferantor = itemView.findViewById(R.id.btnCekReferantorResult);
    }

    public void bind(final QuestionBean item, final int number) {
        qBean = item;
        String qLabel = number + ". " + qBean.getQuestion_label();
        mQuestionLabel.setText(qLabel);

        mTextResult.setCursorVisible(false);
        mTextResult.setEnabled(false);

        mButtonCekReferantor.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();

        if (id == R.id.btnCekReferantorResult) {
            qBean.setAnswer("");
            mTextCekReferantor.setText("");
            mTextCekReferantor.setHint(mActivity.getString(R.string.hintCekReferantor));

            HashMap<String, Object> mapValues = new LinkedHashMap<>();
            String[] choiceFilter = Tool.split(qBean.getChoice_filter(), Global.DELIMETER_DATA3);
            if (choiceFilter.length > 0) {
                for (String filter: choiceFilter) {
                    if ("".equalsIgnoreCase(filter)) {
                        break;
                    }
                    filter = filter.replace("{", "");
                    filter = filter.replace("}", "");

                    int idxOfOpenAbs = filter.indexOf("$");
                    if (idxOfOpenAbs != -1) {
                        String finalIdentifier = filter.substring(idxOfOpenAbs + 1);
                        if (finalIdentifier.equals(Global.IDF_TASK_ID)) {
                            String taskId = DynamicFormActivity.header.getTask_id();
                            mapValues.put(filter, taskId);
                        }
                    } else {
                        QuestionBean bean = Constant.listOfQuestion.get(filter);
                        if (null != bean) {
                            if (bean.isVisible()) {
                                if (Tool.isOptions(bean.getAnswer_type())) {
                                    if(bean.getLovCode()==null || "".equalsIgnoreCase(bean.getLovCode())) {
                                        if(bean.getSelectedOptionAnswers()!=null && !bean.getSelectedOptionAnswers().isEmpty()) {
                                            bean.setLovCode(bean.getSelectedOptionAnswers().get(0).getCode());
                                        }
                                    }
                                    mapValues.put(bean.getIdentifier_name(), bean.getLovCode());
                                } else {
                                    mapValues.put(filter, bean.getAnswer());
                                }
                            }
                        }
                    }
                }
            }
            String formName = DynamicFormActivity.header.getScheme().getScheme_description();
            String odrNoCae = DynamicFormActivity.header.getTask_id();
            CekReferantorViewHolder.CekReferantor cekReferantor = new CekReferantorViewHolder.CekReferantor(formName, odrNoCae, mapValues);
            cekReferantor.execute();
        }
    }

    public class CekReferantor extends AsyncTask<String, String, JsonResponseCekReferantor> {
        private ProgressDialog progressDialog;
        private final String formName;
        private final String odrNoCae;
        private final HashMap<String, Object> mapValues;

        public CekReferantor(String formName, String odrNoCae, HashMap<String, Object> mapValues) {
            this.formName = formName;
            this.odrNoCae = odrNoCae;
            this.mapValues = mapValues;
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            progressDialog = ProgressDialog.show(mActivity, "", mActivity.getString(R.string.progressWait),
                    true, false);
        }

        @Override
        protected JsonResponseCekReferantor doInBackground(String... strings) {
            JsonResponseCekReferantor response = new JsonResponseCekReferantor();
            if (Tool.isInternetconnected(mActivity)) {
                JsonRequestCekReferantor request = new JsonRequestCekReferantor();
                request.setFormName(formName);
                request.setOdrNoCae(odrNoCae);
                request.setMapValues(mapValues);
                request.setAudit(GlobalData.getSharedGlobalData().getAuditData());

                CekReferantorApi api = new CekReferantorApi(mActivity);
                response = api.request(request);
            } else {
                JsonResponseCekReferantor.Status status = new JsonResponseCekReferantor.Status();
                status.setMessage(mActivity.getString(R.string.use_offline_mode));
                response.setStatus(status);
            }
            return response;
        }

        @Override
        protected void onPostExecute(JsonResponseCekReferantor response) {
            super.onPostExecute(response);

            if (progressDialog.isShowing()) {
                progressDialog.dismiss();
            }

            // Handle response cek referantor -- Jowoen // 14-02-2025
            if (null != response) {
                int code = response.getStatus().getCode();
                String message = response.getStatus().getMessage();

                if (1 != code) {
                    if (null != response.getObjectHashMap()) {
                        for (Map.Entry<String, Object> mapElement : response.getObjectHashMap().entrySet()) {
                            String refId = mapElement.getKey();
                            String answer = mapElement.getValue().toString();

                            QuestionBean bean = Constant.listOfQuestion.get(refId);
                            bean.setIntTextAnswer(answer);
                            bean.setAnswer(answer);
                        }
                        // Send bundle
                        android.os.Message messageIsChange = new android.os.Message();
                        Bundle bundle = new Bundle();
                        bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.RESULT_FROM_CEK_REFERANTOR);
                        bundle.putBoolean("isChange", true);
                        messageIsChange.setData(bundle);
                        FragmentQuestion.questionHandler.sendMessage(messageIsChange);
                    }
                }
                mTextCekReferantor.setText(message);
                mTextCekReferantor.setEnabled(false);
                qBean.setAnswer(message);
                qBean.setReadOnly(true);
            }
        }
    }
}