package com.adins.mss.foundation.print;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import android.os.Handler;
import android.os.Message;
import android.widget.Toast;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.dynamicform.Constant;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.PrintResult;
import com.adins.mss.logger.Logger;
import com.bixolon.printer.BixolonPrinter;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Vector;

//import com.adins.msm.constant.Global;
//import com.adins.msm.model.PrintItemBean;
//import com.adins.util.FontSato;
//import com.adins.util.Reader;
//import com.adins.util.SentencesSato;


public class BixolonPrintManager extends AbstractPrintManager_ {
    //static BixolonPrinter mBxlService_v2; //mBixolonPrinter;
    public static final int LINE_LENGTH = 32;
    protected static final String TAG = null;
    Context context;
    public final Handler mHandler = new Handler(new Handler.Callback() {
        public boolean handleMessage(Message msg) {
            switch (msg.what) {
                case BixolonPrinter.MESSAGE_STATE_CHANGE:
                    switch (msg.arg1) {
                        case BixolonPrinter.STATE_CONNECTING:
                            // TODO: Processing when connection to printer is being
                            // tried
                            break;
                        case BixolonPrinter.STATE_CONNECTED:
                            // TODO: Processing when printer connection is completed
                            connected = true;
                            break;
                        case BixolonPrinter.STATE_NONE:
                            // TODO: Processing when printer is not connected
                            connected = false;
                            break;
                    }
                    break;
                case BixolonPrinter.MESSAGE_DEVICE_NAME:
                    String connectedDeviceName = msg.getData().getString(
                            BixolonPrinter.KEY_STRING_DEVICE_NAME);
                    break;
                case BixolonPrinter.MESSAGE_TOAST:
                    Toast.makeText(
                            context.getApplicationContext(),
                            msg.getData()
                                    .getString(BixolonPrinter.KEY_STRING_TOAST),
                            Toast.LENGTH_SHORT).show();
                    break;
            }
            return true;
        }
    });
    Bitmap logo = null;
    private BxlService mBxlService;

    public BixolonPrintManager(Context context, List<PrintResult> list) {
        super(list);

        BitmapDrawable drawable = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
            drawable = (BitmapDrawable) context.getResources().getDrawable(R.drawable.adins_logo, context.getTheme());
        } else {
            drawable = (BitmapDrawable) context.getResources().getDrawable(R.drawable.adins_logo);
        }
        this.logo = drawable.getBitmap();

        this.context = context;
    }

    @Override
    public boolean connect() throws Exception {
        if (!connected) {
            mBxlService = new BxlService(BixolonPrintManager.this);
            //	mBxlService_v2 = new BixolonPrinter(context, mHandler, null); //new BxlService();
            //	mBxlService_v2.findBluetoothPrinters();
            //mBxlService.Connect();


            connected = mBxlService.Connect() == BxlService.BXL_SUCCESS;
        }
        return connected;
    }

    @Override
    public boolean disconnect() throws Exception {
        if (connected) {
            if (mBxlService != null) {
                //mBxlService_v2.disconnect();
                mBxlService.Disconnect();
                mBxlService = null;
                connected = false;
            }
        }
        return true;
    }

	/*public String getDataToPrintTest() {

    	
		StringBuffer sbToPrint = new StringBuffer();
		
		char start = (char) 27;
		char begin = (char) 2;
		char end = (char) 3;
		
		sbToPrint.append(begin).append(start);
		sbToPrint.append("A").append(start).append("IG1").append(start).append("PS");
		
		sbToPrint.append(start).append("L0057005001021000");
		sbToPrint.append(start).append("DTANDA TERIMA PEMBAYARAN");
		sbToPrint.append(start).append("L0088000101222000");
		sbToPrint.append(start).append("D--------------");
		sbToPrint.append(start).append("L0021000101222000");
		sbToPrint.append(start).append("D==============");
		
		sbToPrint.append(start).append("L0145000101011000");
		sbToPrint.append(start).append("DNo. APLIKASI");
		sbToPrint.append(start).append("L0178000101011000");
		sbToPrint.append(start).append("DNAMA CUSTOMER");
		sbToPrint.append(start).append("L0209000101011000");
		sbToPrint.append(start).append("DASSET");
		sbToPrint.append(start).append("L0242000101011000");
		sbToPrint.append(start).append("DNo. POLISI");
		sbToPrint.append(start).append("L0276000101011000");
		sbToPrint.append(start).append("DTENOR");
		sbToPrint.append(start).append("L0310000101011000");
		sbToPrint.append(start).append("DOVERDUE");
		sbToPrint.append(start).append("L0342000101011000");
		sbToPrint.append(start).append("DANGSURAN KE");
		sbToPrint.append(start).append("L0377000101011000");
		sbToPrint.append(start).append("DANGSURAN");
		sbToPrint.append(start).append("L0413000101011000");
		sbToPrint.append(start).append("DTOTAL BAYAR");
		sbToPrint.append(start).append("L0451000101011000");
		sbToPrint.append(start).append("DDITERIMA OLEH");
		
		sbToPrint.append(start).append("L0479000101222000");
		sbToPrint.append(start).append("D--------------");
		sbToPrint.append(start).append("L0516008001221000");
		sbToPrint.append(start).append("DTERIMA KASIH");
		sbToPrint.append(start).append("L0550000101222000");
		sbToPrint.append(start).append("D==============");
		
		sbToPrint.append(start).append("L0143017601211000");
		sbToPrint.append(start).append("D:");
		sbToPrint.append(start).append("L0135020201221000");
		sbToPrint.append(start).append("D").append("getContractNo");
		
		sbToPrint.append(start).append("L0176017601211000");
		sbToPrint.append(start).append("D:");
		sbToPrint.append(start).append("L0176020201011000");
		sbToPrint.append(start).append("D").append("getCustName");
		
		sbToPrint.append(start).append("L0205017701211000");
		sbToPrint.append(start).append("D:");
		sbToPrint.append(start).append("L0208020101011000");
		sbToPrint.append(start).append("D").append("getBrand");
		
		sbToPrint.append(start).append("L0239017701211000");
		sbToPrint.append(start).append("D:");
		sbToPrint.append(start).append("L0240020101011000");
		sbToPrint.append(start).append("D").append("getNoPolisi");
		
		sbToPrint.append(start).append("L0272017701211000");
		sbToPrint.append(start).append("D:");
		sbToPrint.append(start).append("L0274020101211000");
		sbToPrint.append(start).append("D").append("getTenor");
		
		sbToPrint.append(start).append("L0306017601211000");
		sbToPrint.append(start).append("D:");
		sbToPrint.append(start).append("L0308020201211000");
		sbToPrint.append(start).append("D").append("getOverdueDays");
		
		sbToPrint.append(start).append("L0340017601211000");
		sbToPrint.append(start).append("D:");
		sbToPrint.append(start).append("L0340020101211000");
		sbToPrint.append(start).append("D").append("getAngsuranke");
		
		sbToPrint.append(start).append("L0376017701211000");
		sbToPrint.append(start).append("D:");
		sbToPrint.append(start).append("L0374020101211000");
		sbToPrint.append(start).append("D").append("Rp").append("getAngsuran");
		
		sbToPrint.append(start).append("L0411017701211000");
		sbToPrint.append(start).append("D:");
		sbToPrint.append(start).append("L0411020001211000");
		sbToPrint.append(start).append("D").append("Rp").append("getTotalPembayaran");
		
		sbToPrint.append(start).append("L0450017601211000");
		sbToPrint.append(start).append("D:");
		sbToPrint.append(start).append("L0449020001011000");
		sbToPrint.append(start).append("D").append("getUserId");
		
		sbToPrint.append(start).append("Q0001").append(start).append("Z");
		sbToPrint.append(end);
		
		
		


		return sbToPrint.toString();
	}*/

    private String getDataToPrint() {
        int verticalPosition = 96; // 96, sebagai posisi awal
        char start = (char) 27;
        char end = (char) 3;

        StringBuffer sbToPrint = new StringBuffer();
        /*sbToPrint.append(begin).append(start);
        sbToPrint.append("A").append(start).append("IG1").append(start)
				.append("PS");
		*/
        //for (PrintItemBean bean : Constant.listOfPrintItem) {
        for (PrintResult bean : Constant.listOfPrintItem) {

            //String type = bean.getType();
            String type = bean.getPrint_type_id();
            String vPosition = "";

            if ((verticalPosition + 33) < 100) {
                verticalPosition = verticalPosition + 33;
                vPosition = "00" + verticalPosition;
                // sbToPrint.append(start).append("L00"+horizontalPosition+"000101111000");
            } else if ((verticalPosition + 33) < 1000) {
                verticalPosition = verticalPosition + 33;
                vPosition = "0" + verticalPosition;
                // sbToPrint.append(start).append("L0"+horizontalPosition+"000101111000");
            } else {
                vPosition = "" + verticalPosition;
                verticalPosition = verticalPosition + 33;

            }
            if (Global.PRINT_NEW_LINE.equals(type)) {
                String label = bean.getLabel();
                if ("".equals(label) || " ".equals(label)) {
                    sbToPrint.append(start).append(
                            "L" + vPosition + "000101111000");
                    sbToPrint.append(start).append("D" + label);
                } else {
                    // ***untuk label gak ti wrap karena akan
                    // menghilangkan spasi,
                    // so konsekwensinya harus di pas-in dulu dari
                    // server..
                    // cth "     SIMPAN TANDA BUKTI INI"
                    sbToPrint.append(start).append(
                            "L" + vPosition + "000101111000");
                    sbToPrint.append(start).append("D" + label);
                }
            } else if (Global.PRINT_LABEL_CENTER.equals(type)) {
                String label = bean.getLabel();
                Vector vt;
                // nilai sebenarnya 384, dikurangi aj bt jaga2 kalo slah perhtungan
                vt = FontSato.wrap(374, label.trim());

                for (int j = 0; j < vt.size(); j++) {
                    SentencesSato setn = null;

                    try {
                        setn = (SentencesSato) vt.elementAt(j);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        if (Global.IS_DEV) Logger.i("INFO", "eato " + e);
                    }

                    // ------------- agar posisi bisa ditengah,
                    int iHposition = (384 - setn.getLenghtSentemce()) / 2;
                    String sHposition = "0";

                    if (iHposition < 10) {
                        sHposition = "000" + iHposition;
                    } else if (iHposition < 100) {
                        sHposition = "00" + iHposition;
                    } else if (iHposition < 1000) {
                        sHposition = "0" + iHposition;
                    }
                    // -------------

                    if (j == 0) {
                        sbToPrint.append(start).append("L"
                                + vPosition
                                + sHposition
                                + "01111000");
                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());

                    } else {
                        sbToPrint.append(start).append("L"
                                + vPrintPosition(verticalPosition)
                                + sHposition
                                + "01111000");
                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());

                        verticalPosition = verticalPosition + 33;
                    }

                }
            } else if (Global.PRINT_ANSWER.equals(type)) {
                String label = bean.getLabel();
                String answer = bean.getValue();
                if (answer != null && !answer.equals("")) {
                    if (label.equals("")
                            || label == null
                            || label.equals("null")) { // jika pertanyaan tidak ada labelnya maka posisi ditengahkan

                        Vector vt;
                        // nilai sebenarnya 384, dikurangi aj bt jaga2 kalo slah perhtungan
                        vt = FontSato.wrap(374, answer.trim());

                        for (int j = 0; j < vt.size(); j++) {

                            SentencesSato setn = null;

                            try {
                                setn = (SentencesSato) vt.elementAt(j);
                            } catch (Exception e) {
                                FireCrash.log(e);
                                if (Global.IS_DEV) Logger.i("INFO", "eato " + e);
                            }

                            // ------------- agar posisi bisa ditengah,
                            int iHposition = (384 - setn.getLenghtSentemce()) / 2;
                            String sHposition = "0";

                            if (iHposition < 10) {
                                sHposition = "000" + iHposition;
                            } else if (iHposition < 100) {
                                sHposition = "00" + iHposition;
                            } else if (iHposition < 1000) {
                                sHposition = "0" + iHposition;
                            }
                            // -------------

                            if (j == 0) {
                                sbToPrint.append(start).append("L"
                                        + vPosition
                                        + sHposition
                                        + "01111000");
                                sbToPrint.append(start).append("D"
                                        + setn.getSentence().trim());
                            } else {
                                sbToPrint.append(start).append("L"
                                        + vPrintPosition(verticalPosition)
                                        + sHposition
                                        + "01111000");
                                sbToPrint.append(start).append("D"
                                        + setn.getSentence().trim());
                                verticalPosition = verticalPosition + 33;
                            }
                        }

                    } else {

                        sbToPrint.append(start).append("L"
                                + vPosition
                                + "000101111000");
                        sbToPrint.append(start).append("D" + label);

                        sbToPrint.append(start).append("L"
                                + vPosition
                                + "020001111000");
                        sbToPrint.append(start).append("D:");

                        Vector vt;

                        vt = FontSato.wrap(166, answer.trim()); // panjang area yang akan diprint

                        for (int j = 0; j < vt.size(); j++) {

                            SentencesSato setn = null;

                            try {
                                setn = (SentencesSato) vt.elementAt(j);
                            } catch (Exception e) {
                                FireCrash.log(e);
                                if (Global.IS_DEV) Logger.i("INFO", "eato " + e);
                            }
                            if (j == 0) {
                                sbToPrint.append(start).append("L"
                                        + vPosition
                                        + "0208"
                                        + "01111000");
                                sbToPrint.append(start).append("D"
                                        + setn.getSentence().trim());
                            } else {
                                sbToPrint.append(start).append("L"
                                        + vPrintPosition(verticalPosition)
                                        + "0208"
                                        + "01111000");
                                verticalPosition = verticalPosition + 33;
                                sbToPrint.append(start).append("D"
                                        + setn.getSentence().trim());
                            }

                        }
                    }
                } else {
                    vPosition = "";
                    if ((verticalPosition - 33) < 100) {
                        verticalPosition = verticalPosition - 33;
                        vPosition = "00" + verticalPosition;
                        // sbToPrint.append(start).append("L00"+horizontalPosition+"000101111000");
                    } else if ((verticalPosition + 33) < 1000) {
                        verticalPosition = verticalPosition - 33;
                        vPosition = "0" + verticalPosition;
                        // sbToPrint.append(start).append("L0"+horizontalPosition+"000101111000");
                    } else {
                        vPosition = "" + verticalPosition;
                        verticalPosition = verticalPosition - 33;

                    }
                }
            } else if (Global.PRINT_USER_NAME.equals(type)) {
                String label = bean.getLabel();
                String answer = GlobalData.getSharedGlobalData().getUser().getFullname();

                sbToPrint.append(start).append("L"
                        + vPosition
                        + "000101111000");
                sbToPrint.append(start).append("D" + label);

                sbToPrint.append(start).append("L"
                        + vPosition
                        + "020001111000");
                sbToPrint.append(start).append("D:");

                Vector vt;

                vt = FontSato.wrap(166, answer.trim()); // panjang area yang akan diprint

                for (int j = 0; j < vt.size(); j++) {

                    SentencesSato setn = null;

                    try {
                        setn = (SentencesSato) vt.elementAt(j);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        if (Global.IS_DEV) Logger.i("INFO", "eato " + e);
                    }
                    if (j == 0) {
                        sbToPrint.append(start).append("L"
                                + vPosition
                                + "0208"
                                + "01111000");
                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());
                    } else {
                        sbToPrint.append(start).append("L"
                                + vPrintPosition(verticalPosition)
                                + "0208"
                                + "01111000");
                        verticalPosition = verticalPosition + 33;
                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());
                    }

                }
            } else if (Global.PRINT_LOGIN_ID.equals(type)) {
                String label = bean.getLabel();
                String answer = GlobalData.getSharedGlobalData().getUser().getLogin_id();

                sbToPrint.append(start).append("L"
                        + vPosition
                        + "000101111000");
                sbToPrint.append(start).append("D" + label);

                sbToPrint.append(start).append("L"
                        + vPosition
                        + "020001111000");
                sbToPrint.append(start).append("D:");

                Vector vt;

                vt = FontSato.wrap(166, answer.trim()); // panjang area yang akan diprint

                for (int j = 0; j < vt.size(); j++) {

                    SentencesSato setn = null;

                    try {
                        setn = (SentencesSato) vt.elementAt(j);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        if (Global.IS_DEV) Logger.i("INFO", "eato " + e);
                    }
                    if (j == 0) {
                        sbToPrint.append(start).append("L"
                                + vPosition
                                + "0208"
                                + "01111000");
                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());
                    } else {
                        sbToPrint.append(start).append("L"
                                + vPrintPosition(verticalPosition)
                                + "0208"
                                + "01111000");
                        verticalPosition = verticalPosition + 33;
                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());
                    }

                }
            } else if (Global.PRINT_TIMESTAMP.equals(type)) {
                SimpleDateFormat df = new SimpleDateFormat("dd-MMM-yyyy hh:mm");
                Date date = new Date();
                String answer = df.format(date);
                String label = bean.getLabel();

                sbToPrint.append(start).append("L"
                        + vPosition
                        + "000101111000");
                sbToPrint.append(start).append("D" + label);

                sbToPrint.append(start).append("L"
                        + vPosition
                        + "020001111000");
                sbToPrint.append(start).append("D:");

                Vector vt;

                vt = FontSato.wrap(166, answer.trim()); // panjang area yang akan diprint

                for (int j = 0; j < vt.size(); j++) {

                    SentencesSato setn = null;

                    try {
                        setn = (SentencesSato) vt.elementAt(j);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        if (Global.IS_DEV) Logger.i("INFO", "eato " + e);
                    }
                    if (j == 0) {
                        sbToPrint.append(start).append("L"
                                + vPosition
                                + "0208"
                                + "01111000");
                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());
                    } else {
                        sbToPrint.append(start).append("L"
                                + vPrintPosition(verticalPosition)
                                + "0208"
                                + "01111000");
                        verticalPosition = verticalPosition + 33;
                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());
                    }

                }
            }
        }
        // ENDING LINE
        verticalPosition = verticalPosition + 33;
        sbToPrint.append(start).append(
                "L" + vPrintPosition(verticalPosition) + "000101222000");
        verticalPosition = verticalPosition + 33;
        sbToPrint.append(start).append("D----------------");

        sbToPrint.append(start).append("Q0001").append(start).append("Z");
        sbToPrint.append(end);

        return sbToPrint.toString();
    }

	/*private String getDataToPrint(String file){
        int verticalPosition = 96; // 96, sebagai posisi awal
		char start = (char) 27;
		char begin = (char) 2;
		char end = (char) 3;
		
		StringBuffer sbToPrint = new StringBuffer();
		sbToPrint.append(begin).append(start);
		sbToPrint.append("A").append(start).append("IG1").append(start)
				.append("PS");
		
		for (PrintItemBean bean : MainMenuActivity.listOfPrintItem) {	
			
			String type = bean.getType();
			String vPosition = "";
			
			if ((verticalPosition + 33) < 100) {
				verticalPosition = verticalPosition + 33;
				vPosition = "00" + verticalPosition;
				// sbToPrint.append(start).append("L00"+horizontalPosition+"000101111000");
			} else if ((verticalPosition + 33) < 1000) {
				verticalPosition = verticalPosition + 33;
				vPosition = "0" + verticalPosition;
				// sbToPrint.append(start).append("L0"+horizontalPosition+"000101111000");
			} else {
				vPosition = "" + verticalPosition;
				verticalPosition = verticalPosition + 33;

			}
			if (Global.PRINT_LOGO.equals(type)) {
				//String data = new String(file);
	
				sbToPrint.append(start).append(file);
			}
			
		}
		// ENDING LINE
		verticalPosition = verticalPosition + 33;
		sbToPrint.append(start).append(
				"L" + vPrintPosition(verticalPosition) + "000101222000");
		verticalPosition = verticalPosition + 33;
		sbToPrint.append(start).append("D----------------");

		sbToPrint.append(start).append("Q0001").append(start).append("Z");
		sbToPrint.append(end);
		
		return sbToPrint.toString();
	}*/


    public String vPrintPosition(int verticalPosition) {
        String vPosition = "";
        if ((verticalPosition + 33) < 100) {
            verticalPosition = verticalPosition + 33;
            vPosition = "00" + verticalPosition;
        } else if ((verticalPosition + 33) < 1000) {
            verticalPosition = verticalPosition + 33;
            vPosition = "0" + verticalPosition;
        } else {
            vPosition = "" + verticalPosition;
            verticalPosition = verticalPosition + 33;
        }

        return vPosition;
    }

    @Override
    public boolean printSato() throws Exception {
        if (connected) {
            //mBxlService.PrintLogoSato();
            //Thread.sleep(5000);
            mBxlService.PrintTextSato(this.getDataToPrint());
        } else {
            throw new Exception("Device is not connected to the printer.");
        }
        return true;
    }

    private String getDataToPrintZebra() {

        //for (PrintItemBean bean : Constant.listOfPrintItem) {
        for (PrintResult bean : Constant.listOfPrintItem) {

            //String type = bean.getType();
            String type = bean.getPrint_type_id();
            if (Global.PRINT_NEW_LINE.equals(type)) {
                StringBuffer sb = new StringBuffer();
                String label = bean.getLabel();
                sb.append(label);
                sb.append(blank);
                //mBxlService.PrintTextZebra(sb.toString());
            } else if (Global.PRINT_LABEL_CENTER.equals(type)) {
                StringBuffer sb = new StringBuffer();
                String label = bean.getLabel();
                int labelLen = label.length();
                //1 line = 48 char
                int spaceLength = (48 - labelLen) / 2;
                while (spaceLength > 0) {
                    sb.append(blank);
                    spaceLength--;
                }
                sb.append(label);

                //mBxlService.PrintTextZebra(sb.toString());
            } else if (Global.PRINT_USER_NAME.equals(type)) {
                StringBuffer sb = new StringBuffer();
                String label = bean.getLabel();
                int labelLen = label.length();
                sb.append(label);
                int spaceLength = separatorStart - labelLen;
                while (spaceLength > 0) {
                    sb.append(blank);
                    spaceLength--;
                }
                sb.append(valueSeparator);
                sb.append(blank);

                String value = GlobalData.getSharedGlobalData().getUser().getFullname();
                value = (value == null) ? "" : value;
                sb.append(value);
                //mBxlService.PrintTextZebra(sb.toString());
            } else if (Global.PRINT_LOGIN_ID.equals(type)) {
                StringBuffer sb = new StringBuffer();
                String label = bean.getLabel();
                int labelLen = label.length();
                sb.append(label);
                int spaceLength = separatorStart - labelLen;
                while (spaceLength > 0) {
                    sb.append(blank);
                    spaceLength--;
                }
                sb.append(valueSeparator);
                sb.append(blank);

                String value = GlobalData.getSharedGlobalData().getUser().getLogin_id();
                value = (value == null) ? "" : value;
                sb.append(value);
            } else if (Global.PRINT_ANSWER.equals(type)) {
                StringBuffer sb = new StringBuffer();
                String label = bean.getLabel();
                int labelLen = label.length();
                if (label == null || label.equals("")) {
                    //question no label = center
                    String value = bean.getValue();
                    int valueLen = value.length();
                    int spaceLength = (48 - valueLen) / 2;
                    while (spaceLength > 0) {
                        sb.append(blank);
                        spaceLength--;
                    }
                    sb.append(value);
                } else {
                    sb.append(label);
                    int spaceLength = separatorStart - labelLen;
                    while (spaceLength > 0) {
                        sb.append(blank);
                        spaceLength--;
                    }
                    sb.append(valueSeparator);
                    sb.append(blank);

                    String value = bean.getValue();
                    value = (value == null) ? "" : value;
                    sb.append(value);
                }
                //mBxlService.PrintTextZebra(sb.toString());
            } else if (Global.PRINT_TIMESTAMP.equals(type)) {
                StringBuffer sb = new StringBuffer();
                String label = bean.getLabel();
                int labelLen = label.length();
                sb.append(label);
                int spaceLength = separatorStart - labelLen;
                while (spaceLength > 0) {
                    sb.append(blank);
                    spaceLength--;
                }
                sb.append(valueSeparator);
                sb.append(blank);

                SimpleDateFormat df = new SimpleDateFormat("dd-MMM-yyyy hh:mm");
                Date date = new Date();
                String value = df.format(date);
                value = (value == null) ? "" : value;
                sb.append(value);
                //mBxlService.PrintTextZebra(sb.toString());
            } else if (Global.PRINT_LOGO.equals(type)) {
                /*mBxlService.PrintImageZebra(PrintActivity.bmp,
                        BxlService.BXL_WIDTH_FULL, BxlService.BXL_ALIGNMENT_CENTER, 20);*/
            }
        }

        return "";
    }

    @Override
    public boolean printZebra() throws Exception {
        if (connected) {
            //mBxlService.PrintLogoSato();
            //Thread.sleep(5000);
            this.getDataToPrintZebra();
        } else {
            throw new Exception("Device is not connected to the printer.");
        }
        return true;
    }
	
	/*@Override
	public boolean printSato(byte[] file) throws Exception {
		if (connected) {
			mBxlService.PrintTextSato(this.getDataToPrint(file));
		}
		else {
			throw new Exception ("Device is not connected to the printer.");
		}		
		return true;
	}*/

    @Override
    public boolean print() throws Exception {
        boolean isNeedFeedLine = true;
        if (connected) {
            for (PrintResult bean : list) {
                String type = bean.getPrint_type_id();
                if (Global.PRINT_NEW_LINE.equals(type)) {
                    mBxlService.PrintText(bean.getLabel(),
                            BxlService.BXL_ALIGNMENT_LEFT, BxlService.BXL_FT_DEFAULT,
                            BxlService.BXL_TS_0WIDTH | BxlService.BXL_TS_0HEIGHT);
                } else if (Global.PRINT_BRANCH_ADDRESS.equals(type)) {
                    mBxlService.PrintText(bean.getLabel(),
                            BxlService.BXL_ALIGNMENT_CENTER, BxlService.BXL_FT_DEFAULT,
                            BxlService.BXL_TS_0WIDTH | BxlService.BXL_TS_0HEIGHT);
                } else if (Global.PRINT_BRANCH_NAME.equals(type)) {
                    mBxlService.PrintText(bean.getLabel(),
                            BxlService.BXL_ALIGNMENT_CENTER, BxlService.BXL_FT_DEFAULT,
                            BxlService.BXL_TS_0WIDTH | BxlService.BXL_TS_0HEIGHT);
                } else if (Global.PRINT_LABEL_CENTER.equals(type)) {
                    mBxlService.PrintText(bean.getLabel(),
                            BxlService.BXL_ALIGNMENT_CENTER, BxlService.BXL_FT_DEFAULT,
                            BxlService.BXL_TS_0WIDTH | BxlService.BXL_TS_0HEIGHT);
                } else if (Global.PRINT_LABEL.equals(type)) {
                    mBxlService.PrintText(bean.getLabel(),
                            BxlService.BXL_ALIGNMENT_LEFT, BxlService.BXL_FT_DEFAULT,
                            BxlService.BXL_TS_0WIDTH | BxlService.BXL_TS_0HEIGHT);
                } else if (Global.PRINT_LABEL_BOLD.equals(type)) {
                    mBxlService.PrintText(bean.getLabel(),
                            BxlService.BXL_ALIGNMENT_LEFT, BxlService.BXL_FT_BOLD,
                            BxlService.BXL_TS_0WIDTH | BxlService.BXL_TS_0HEIGHT);
                } else if (Global.PRINT_LABEL_CENTER_BOLD.equals(type)) {
                    mBxlService.PrintText(bean.getLabel(),
                            BxlService.BXL_ALIGNMENT_CENTER, BxlService.BXL_FT_BOLD,
                            BxlService.BXL_TS_0WIDTH | BxlService.BXL_TS_0HEIGHT);
                } else if (Global.PRINT_LOGO.equals(type)) {
                    int SDK_INT = android.os.Build.VERSION.SDK_INT;
                    if (SDK_INT < 14) {
                        //di bawah ICS masuk sini
                        mBxlService.PrintImage(logo,
                                BxlService.BXL_WIDTH_FULL, BxlService.BXL_ALIGNMENT_CENTER, 20);
                    } else {
                        mBxlService.printBitmap(logo,
                                BixolonPrinter.ALIGNMENT_CENTER, 384, 63, true);
                    }


                } else if (Global.PRINT_ANSWER.equals(type)) {
                    StringBuilder sb = new StringBuilder();

                    String label = bean.getLabel();
                    int labelLen = label.length();
                    sb.append(label);
                    int spaceLength = separatorStart - labelLen;
                    while (spaceLength > 0) {
                        sb.append(blank);
                        spaceLength--;
                    }
                    sb.append(valueSeparator);
                    sb.append(blank);

                    String value = bean.getValue();


                    value = (value == null) ? "" : value;


                    if ("".equalsIgnoreCase(value)) {
                        isNeedFeedLine = false;
                    } else {

                        if (null == label || "".equalsIgnoreCase(label)) {
                            mBxlService.PrintText(value,
                                    BxlService.BXL_ALIGNMENT_CENTER, BxlService.BXL_FT_DEFAULT,
                                    BxlService.BXL_TS_0WIDTH | BxlService.BXL_TS_0HEIGHT);
                        } else {
                            //bong 13 may 15 - sementara dikomen dulu karena akan menggunakan print_type CURRENCY
//							//tambahin format RP.
//							if(bean.getQuestionId()==13 || bean.getQuestionId()==6 || bean.getQuestionId()==11 || bean.getQuestionId()==7 || 
//									bean.getQuestionId()==12 || bean.getQuestionId()==4){
//								value = "Rp"+Tool.getNumericDigit(value)+",-";
//							}
                            sb.append(value);
                            mBxlService.PrintText(sb.toString(),
                                    BxlService.BXL_ALIGNMENT_LEFT, BxlService.BXL_FT_DEFAULT,
                                    BxlService.BXL_TS_0WIDTH | BxlService.BXL_TS_0HEIGHT);
                        }


                    }


                } else if (Global.PRINT_USER_NAME.equals(type)) {
                    StringBuilder sb = new StringBuilder();

                    String label = bean.getLabel();
                    int labelLen = label.length();
                    sb.append(label);
                    int spaceLength = separatorStart - labelLen;
                    while (spaceLength > 0) {
                        sb.append(blank);
                        spaceLength--;
                    }
                    sb.append(valueSeparator);
                    sb.append(blank);

                    String value = GlobalData.getSharedGlobalData().getUser().getFullname();
                    value = (value == null) ? "" : value;
                    sb.append(value);

                    mBxlService.PrintText(sb.toString(),
                            BxlService.BXL_ALIGNMENT_LEFT, BxlService.BXL_FT_DEFAULT,
                            BxlService.BXL_TS_0WIDTH | BxlService.BXL_TS_0HEIGHT);
                } else if (Global.PRINT_LOGIN_ID.equals(type)) {
                    StringBuilder sb = new StringBuilder();

                    String label = bean.getLabel();
                    int labelLen = label.length();
                    sb.append(label);
                    int spaceLength = separatorStart - labelLen;
                    while (spaceLength > 0) {
                        sb.append(blank);
                        spaceLength--;
                    }
                    sb.append(valueSeparator);
                    sb.append(blank);

                    String value = GlobalData.getSharedGlobalData().getUser().getLogin_id();
                    value = (value == null) ? "" : value;
                    sb.append(value);

                    mBxlService.PrintText(sb.toString(),
                            BxlService.BXL_ALIGNMENT_LEFT, BxlService.BXL_FT_DEFAULT,
                            BxlService.BXL_TS_0WIDTH | BxlService.BXL_TS_0HEIGHT);
                } else if (Global.PRINT_BT_ID.equals(type)) {
                    StringBuilder sb = new StringBuilder();

                    String label = bean.getLabel();
                    int labelLen = label.length();
                    sb.append(label);
                    int spaceLength = separatorStart - labelLen;
                    while (spaceLength > 0) {
                        sb.append(blank);
                        spaceLength--;
                    }
                    sb.append(valueSeparator);
                    sb.append(blank);
                    String value = "?";
                    try {
                        value = bean.getValue();
                    } catch (Exception e) {
                        FireCrash.log(e);

                    }
                    value = (value == null) ? "" : value;
                    sb.append(value);

                    mBxlService.PrintText(sb.toString(),
                            BxlService.BXL_ALIGNMENT_LEFT, BxlService.BXL_FT_DEFAULT,
                            BxlService.BXL_TS_0WIDTH | BxlService.BXL_TS_0HEIGHT);
                } else if (Global.PRINT_TIMESTAMP.equals(type)) {
                    StringBuilder sb = new StringBuilder();

                    String label = bean.getLabel();
                    int labelLen = label.length();
                    sb.append(label);
                    int spaceLength = separatorStart - labelLen;
                    while (spaceLength > 0) {
                        sb.append(blank);
                        spaceLength--;
                    }
                    sb.append(valueSeparator);
                    sb.append(blank);

                    SimpleDateFormat df = new SimpleDateFormat("MM/dd/yyyy HH:mm");
                    Date date = new Date();
                    String value = df.format(date);

                    value = (value == null) ? "" : value;
                    sb.append(value);

                    mBxlService.PrintText(sb.toString(),
                            BxlService.BXL_ALIGNMENT_LEFT, BxlService.BXL_FT_DEFAULT,
                            BxlService.BXL_TS_0WIDTH | BxlService.BXL_TS_0HEIGHT);
                }
                if (isNeedFeedLine) {
                    mBxlService.LineFeed(1);
                } else {
                    isNeedFeedLine = true;
                }

            }
            mBxlService.LineFeed(2);
        } else {
            throw new Exception("Device is not connected to the printer.");
        }

        return true;
    }
}
