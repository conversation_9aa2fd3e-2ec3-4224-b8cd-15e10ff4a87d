package com.adins.mss.base.dynamicform.form.models;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * Created by gigin.ginanjar on 12/10/2016.
 */

public class ParameterAnswer implements Serializable {
    @SerializedName("refId")
    private String refId;
    @SerializedName("value")
    private String answer;

    public String getRefId() {
        return refId;
    }

    public void setRefId(String refId) {
        this.refId = refId;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    @Override
    public String toString() {
        return refId;
    }
}
