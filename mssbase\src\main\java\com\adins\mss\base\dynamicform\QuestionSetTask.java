package com.adins.mss.base.dynamicform;

import android.annotation.SuppressLint;
import android.app.ProgressDialog;
import android.content.Intent;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Handler;
import android.text.format.DateFormat;
import android.view.View;

import androidx.fragment.app.FragmentActivity;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.commons.Query;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.dynamicform.form.DynamicQuestionActivity;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.Lookup;
import com.adins.mss.dao.QuestionSet;
import com.adins.mss.dao.Scheme;
import com.adins.mss.dao.TaskD;
import com.adins.mss.dao.TaskH;
import com.adins.mss.foundation.camerainapp.helper.Logger;
import com.adins.mss.foundation.db.dataaccess.LookupDataAccess;
import com.adins.mss.foundation.db.dataaccess.QuestionSetDataAccess;
import com.adins.mss.foundation.db.dataaccess.SchemeDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskDDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskHDataAccess;
import com.adins.mss.foundation.dialog.NiftyDialogBuilder;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.questiongenerator.OptionAnswerBean;
import com.adins.mss.foundation.questiongenerator.QuestionBean;

import org.acra.ACRA;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class QuestionSetTask extends AsyncTask<Void, Void, LinkedHashMap<String, QuestionBean>> {
    FragmentActivity activity;
    Bundle bundle;
    LinkedHashMap<String, QuestionBean> questions = new LinkedHashMap<String, QuestionBean>();
    HttpCryptedConnection httpConn;
    HttpConnectionResult serverResult = null;
    int mode;
    private ProgressDialog progressDialog;
    private String errMsg = null;

    public QuestionSetTask(FragmentActivity activity, Bundle bundle) {
        this.activity = activity;
        this.bundle = bundle;
    }

    @Override
    protected void onPreExecute() {
        this.progressDialog = ProgressDialog.show(activity, "", activity.getString(R.string.progressWait), true);
    }

    @SuppressLint("DefaultLocale")
    @Override
    protected LinkedHashMap<String, QuestionBean> doInBackground(Void... arg0) {
        String formVersion = CustomerFragment.header.getForm_version(); //must not null
        mode = bundle.getInt(Global.BUND_KEY_MODE_SURVEY);
        if (mode == Global.MODE_SURVEY_TASK) {
            List<QuestionSet> checkQuestion;
            try {
                checkQuestion = QuestionSetDataAccess.getAllByFormVersion(activity, CustomerFragment.header.getUuid_scheme(), formVersion);
            } catch (Exception e) {
                FireCrash.log(e);
                Logger.e("QuestionSetTask", e);
                ACRA.getErrorReporter().putCustomData("ErrorQuestionSetTask", e.getMessage());
                ACRA.getErrorReporter().putCustomData("ErrorQuestionSetTask", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                ACRA.getErrorReporter().handleSilentException(new Exception("ErrorQuestionSetTask"));
                errMsg = activity.getString(R.string.task_cant_seen);
                return null;
            }
            boolean isHaveQuestion = (checkQuestion != null && !checkQuestion.isEmpty());
            if (Global.SchemeIsChange || (!isHaveQuestion))
                getNewQuestionSet();

            String status = CustomerFragment.header.getStatus();
            if (TaskHDataAccess.STATUS_SEND_SAVEDRAFT.equals(status)
                    || TaskHDataAccess.STATUS_SEND_DOWNLOAD.equals(status)
                    || TaskHDataAccess.STATUS_TASK_APPROVAL_DOWNLOAD.equals(status)
                    || TaskHDataAccess.STATUS_TASK_VERIFICATION_DOWNLOAD.equals(status)) {
                questions.clear();
                List<QuestionSet> qs = new ArrayList<>();
                try {
                    qs = QuestionSetDataAccess.getAllByFormVersion(activity, CustomerFragment.header.getUuid_scheme(), formVersion);
                } catch (Exception e) {
                    FireCrash.log(e);
                    Logger.e("QuestionSetTask", e);
                    ACRA.getErrorReporter().putCustomData("ErrorQuestionSetTask", e.getMessage());
                    ACRA.getErrorReporter().putCustomData("ErrorQuestionSetTask", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                }
                List<QuestionBean> listOfQuestions = new ArrayList<QuestionBean>();
                if (!qs.isEmpty()) {
                    try {
                        for (QuestionSet set : qs) {
                            QuestionBean bean = new QuestionBean(set);
                            listOfQuestions.add(bean);
                        }
                    } catch (OutOfMemoryError e) {
                        FireCrash.log(e);
                    }
                }

                try {
                    String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                    List<TaskD> listTaskDImageDonwload = TaskDDataAccess.getAllTaskDToDownloadImage(activity,uuidUser , CustomerFragment.header.getUuid_task_h());
                    if(!listTaskDImageDonwload.isEmpty()){
                        for (int x = 0 ; x < listTaskDImageDonwload.size() ; x++){
                            getImageFromServer(CustomerFragment.header.getUuid_task_h(), listTaskDImageDonwload.get(x).getQuestion_id());
                        }
                    }
                    List<TaskD> listOfAnswers = TaskDDataAccess.getAll(activity, CustomerFragment.header.getUuid_task_h(), TaskDDataAccess.ALL_TASK);
                    List<QuestionBean> questions2 = QuestionBean.matchAnswerToQuestion(activity, listOfQuestions, listOfAnswers);
                    for (QuestionBean bean : questions2) {
                        if (questions.get(bean.getIdentifier_name().toUpperCase()) != null)
                            questions.remove(bean.getIdentifier_name().toUpperCase());
                        questions.put(bean.getIdentifier_name().toUpperCase(), bean);
                        try {
                            if (CustomerFragment.isEditable && bean.getTag().equalsIgnoreCase("RV NUMBER")) {
                                QuestionBean.resetAnswer(bean);
                            }
                        } catch (NullPointerException e) {
                            FireCrash.log(e);
                        }
                    }
                } catch (OutOfMemoryError e) {
                    FireCrash.log(e);
                    errMsg = activity.getString(R.string.request_error);
                } catch (Exception ex) {
                    FireCrash.log(ex);
                    ex.printStackTrace();
                    errMsg = activity.getString(R.string.request_error);
                }

            } else if (TaskHDataAccess.STATUS_SEND_INIT.equals(status) ||
                    TaskHDataAccess.STATUS_TASK_VERIFICATION.equals(status) ||
                    TaskHDataAccess.STATUS_TASK_APPROVAL.equals(status)) {

                JsonRequestTaskD request = new JsonRequestTaskD();
                request.setAudit(GlobalData.getSharedGlobalData().getAuditData());
                request.setuuid_task_h(CustomerFragment.header.getUuid_task_h());
                String uuidScheme = CustomerFragment.header.getUuid_scheme();
                Scheme scheme = SchemeDataAccess.getOne(activity, uuidScheme);
                String json = GsonHelper.toJson(request);
                String url = GlobalData.getSharedGlobalData().getURL_GET_VERIFICATION();
                if (CustomerFragment.header.getStatus().equals(TaskHDataAccess.STATUS_SEND_DOWNLOAD)) {
                    questions.clear();
                    List<QuestionSet> qs = QuestionSetDataAccess.getAllByFormVersion(activity, scheme.getUuid_scheme(), formVersion);
                    List<QuestionBean> listOfQuestions = new ArrayList<QuestionBean>();
                    for (QuestionSet set : qs) {
                        QuestionBean bean = new QuestionBean(set);
                        listOfQuestions.add(bean);
                    }
                    List<TaskD> listOfAnswers = TaskDDataAccess.getAll(activity, CustomerFragment.header.getUuid_task_h(), TaskDDataAccess.ALL_TASK);
                    List<QuestionBean> questions2 = QuestionBean.matchQuestionWithAnswer(activity, listOfQuestions, listOfAnswers);
                    for (QuestionBean bean : questions2) {
                        if (questions.get(bean.getIdentifier_name().toUpperCase()) != null)
                            questions.remove(bean.getIdentifier_name().toUpperCase());
                        questions.put(bean.getIdentifier_name().toUpperCase(), bean);
                    }

                } else {
                    if (Tool.isInternetconnected(activity)) {
                        try {
                            questions.clear();
                            boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                            boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                            httpConn = new HttpCryptedConnection(activity, encrypt, decrypt);
                            serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
                        } catch (Exception e) {
                            FireCrash.log(e);
                            if (Global.IS_DEV)
                                e.printStackTrace();
                            errMsg = e.getMessage();
                        }
                        if (serverResult != null && serverResult.isOK()) {
                            try {
                                String result = serverResult.getResult();

                                JsonResponseTaskD response = GsonHelper.fromJson(result, JsonResponseTaskD.class);
                                if (response.getStatus().getCode() == 0) {
                                    List<TaskD> taskDs = response.getListTask();
                                    TaskH taskH = CustomerFragment.header.getTaskH();
                                    for (TaskD taskD : taskDs) {
                                        taskD.setTaskH(taskH);
//                                        TaskDDataAccess.addOrReplace(activity, taskD);
                                    }
                                    if (taskDs != null && !taskDs.isEmpty())
                                        TaskDDataAccess.addOrReplace(activity, taskDs);
                                    List<QuestionSet> qs = QuestionSetDataAccess.getAllByFormVersion(activity, CustomerFragment.header.getUuid_scheme(), formVersion);
                                    if (qs == null) {
                                        getNewQuestionSet();
                                        qs = QuestionSetDataAccess.getAllByFormVersion(activity, CustomerFragment.header.getUuid_scheme(), formVersion);
                                    }
                                    List<QuestionBean> listOfQuestions = new ArrayList<QuestionBean>();
                                    for (QuestionSet set : qs) {
                                        QuestionBean bean = new QuestionBean(set);
                                        listOfQuestions.add(bean);
                                    }
                                    if (!taskDs.isEmpty()) {
                                        String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                                        List<TaskD> listTaskDImageDonwload = TaskDDataAccess.getAllTaskDToDownloadImage(activity,uuidUser , taskH.getTask_id());
                                        if(!listTaskDImageDonwload.isEmpty()){
                                            for (int x = 0 ; x < listTaskDImageDonwload.size() ; x++){
                                                getImageFromServer(taskH.getTask_id() , listTaskDImageDonwload.get(x).getQuestion_id());
                                            }
                                        }
                                        taskDs =  TaskDDataAccess.getAll(activity, CustomerFragment.header.getUuid_task_h(), TaskDDataAccess.ALL_TASK);
                                        List<QuestionBean> questions2 = QuestionBean.matchAnswerToQuestion(activity, listOfQuestions, taskDs);
                                        for (QuestionBean bean : questions2) {
                                            questions.put(bean.getIdentifier_name().toUpperCase(), bean);
                                        }
                                        if (questions.size() > 0) {
                                            if (taskH.getStatus().equals(TaskHDataAccess.STATUS_TASK_VERIFICATION)) {
                                                taskH.setStatus(TaskHDataAccess.STATUS_TASK_VERIFICATION_DOWNLOAD);
                                            } else if (taskH.getStatus().equals(TaskHDataAccess.STATUS_TASK_APPROVAL)) {
                                                taskH.setStatus(TaskHDataAccess.STATUS_TASK_APPROVAL_DOWNLOAD);
                                            } else {
                                                taskH.setStatus(TaskHDataAccess.STATUS_SEND_DOWNLOAD);
                                            }
                                            if (taskH.getPriority() != null && taskH.getPriority().length() > 0) {
//												taskH.setStart_date(Tool.getSystemDateTime());
                                            } else {
                                                taskH.setStart_date(Tool.getSystemDateTime());
                                            }
                                            TaskHDataAccess.addOrReplace(activity, taskH);
                                        }
                                    } else {
                                        for (QuestionBean bean : listOfQuestions) {
                                            questions.put(bean.getIdentifier_name().toUpperCase(), bean);
                                        }
                                    }
//                                    getImageFromServer();
                                } else {
                                    errMsg = result;
                                }

                            } catch (Exception e) {
                                FireCrash.log(e);
                                try {
                                    if (CustomerFragment.header.getPriority() != null && CustomerFragment.header.getPriority().length() > 0) {
                                        errMsg = activity.getString(R.string.msgNoServerResponeString);
                                    } else {
                                        List<QuestionSet> qs = QuestionSetDataAccess.getAllByFormVersion(activity, CustomerFragment.header.getUuid_scheme(), formVersion);
                                        for (QuestionSet set : qs) {
                                            QuestionBean bean = new QuestionBean(set);
                                            if (questions.get(bean.getIdentifier_name().toUpperCase()) != null)
                                                questions.remove(bean.getIdentifier_name().toUpperCase());
                                            questions.put(bean.getIdentifier_name().toUpperCase(), bean);
                                        }
                                    }
                                } catch (Exception e2) {
                                    FireCrash.log(e2);
                                    errMsg = e.getMessage();
                                }
                            }
                        } else {
                            errMsg = activity.getString(R.string.msgNoServerResponeString);
                        }
                    } else {
                        errMsg = activity.getString(R.string.no_data_found_offline);
                    }
                }
            }
        } else if (mode == Global.MODE_VIEW_SENT_SURVEY) {
            List<QuestionSet> checkQuestion = new ArrayList<>();
            try {
                checkQuestion = QuestionSetDataAccess.getAllByFormVersion(activity, CustomerFragment.header.getUuid_scheme(), formVersion);
            } catch (Exception e) {
                FireCrash.log(e);
                try {
                    Scheme scheme = SchemeDataAccess.getOne(activity, CustomerFragment.header.getUuid_scheme());
                    CustomerFragment.header.getForm().setForm_version(scheme.getForm_version());
                    if (mode == Global.MODE_VIEW_SENT_SURVEY) {
                        if (null != scheme.getForm_version()) {
                            CustomerFragment.header.setForm_version(scheme.getForm_version());
                        } else {
                            CustomerFragment.header.setForm_version(CustomerFragment.header.getScheme().getForm_version());
                        }
                    } else {
                        if (null == CustomerFragment.header.getForm_version()) {
                            CustomerFragment.header.setForm_version(scheme.getForm_version());
                        }
                    }
                } catch (Exception e2) {
                    FireCrash.log(e2);
                    Logger.e("QuestionSetTask", e2);
                    ACRA.getErrorReporter().putCustomData("ErrorQuestionSetTask", e2.getMessage());
                    ACRA.getErrorReporter().putCustomData("ErrorQuestionSetTask", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                    ACRA.getErrorReporter().handleSilentException(new Exception("ErrorQuestionSetTask"));
                    errMsg = activity.getString(R.string.task_cant_seen);
                    return null;
                }
            }
            boolean isHaveQuestion = (checkQuestion != null && !checkQuestion.isEmpty());
            List<QuestionSet> qs = new ArrayList<>();
            if (Global.SchemeIsChange || !isHaveQuestion)
                getNewQuestionSet();
            try {
                qs = QuestionSetDataAccess.getAllByFormVersion(activity, CustomerFragment.header.getUuid_scheme(), formVersion);
            } catch (Exception e) {
                FireCrash.log(e);
            }
            List<QuestionBean> listOfQuestions = new ArrayList<QuestionBean>();
            if (!qs.isEmpty()) {
                for (QuestionSet set : qs) {
                    QuestionBean bean = new QuestionBean(set);
                    listOfQuestions.add(bean);
                }
            }
            List<TaskD> listOfAnswers = TaskDDataAccess.getAll(activity, CustomerFragment.header.getUuid_task_h(), TaskDDataAccess.ALL_TASK);
//			questions = QuestionBean.matchQuestionWithAnswer(activity,listOfQuestions, listOfAnswers);
            List<QuestionBean> questions2 = QuestionBean.matchAnswerToQuestion(activity, listOfQuestions, listOfAnswers);
            for (QuestionBean bean : questions2) {
                questions.put(bean.getIdentifier_name().toUpperCase(), bean);
            }
        } else { //if new task
            try {
                Scheme scheme = SchemeDataAccess.getOne(activity, CustomerFragment.header.getUuid_scheme());
                List<QuestionSet> checkQuestion = new ArrayList<>();
                checkQuestion = QuestionSetDataAccess.getAllByFormVersion(activity, CustomerFragment.header.getUuid_scheme(), scheme.getForm_version());
                CustomerFragment.header.getForm().setForm_version(scheme.getForm_version());
                if (mode == 2) {
                    if (null != scheme.getForm_version()) {
                        CustomerFragment.header.setForm_version(scheme.getForm_version());
                    } else {
                        CustomerFragment.header.setForm_version(CustomerFragment.header.getScheme().getForm_version());
                    }
                } else {
                    if (null == CustomerFragment.header.getForm_version()) {
                        CustomerFragment.header.setForm_version(scheme.getForm_version());
                    }
                }
                boolean isHaveQuestion = (checkQuestion != null && !checkQuestion.isEmpty());
                if (Global.SchemeIsChange || !isHaveQuestion)
                    getNewQuestionSet();
                else {
                    getQuestionFromDB();
                }
            } catch (Exception e) {
                FireCrash.log(e);
                Logger.e("QuestionSetTask", e);
                ACRA.getErrorReporter().putCustomData("ErrorQuestionSetTask", e.getMessage());
                ACRA.getErrorReporter().putCustomData("ErrorQuestionSetTask", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                ACRA.getErrorReporter().handleSilentException(new Exception("ErrorQuestionSetTask"));
                errMsg = activity.getString(R.string.task_cant_seen);
            }
        }
        setQuestionSet(questions);
        if (errMsg == null && questions != null && questions.size() > 0) {
            DynamicFormActivity.listOfIdentifier = new ArrayList<String>(questions.keySet());
        }
        return questions;
    }

    @Override
    protected void onCancelled() {
        super.onCancelled();
        if (activity != null) {
            if (progressDialog.isShowing()) {
                try {
                    progressDialog.dismiss();
                } catch (Exception e) {
                    FireCrash.log(e);
                }
            }
        }
    }

    private void sendAcraReport(String message) {
        ACRA.getErrorReporter().putCustomData("errorSetQuestion", message);
        ACRA.getErrorReporter().handleSilentException(new Exception("Error: Set Question Error " + message));
    }

    @Override
    protected void onPostExecute(final LinkedHashMap<String, QuestionBean> result) {

        if (errMsg != null) {

            if (result != null && result.size() > 0) {
                //final List<QuestionBean> nResult = result;
                final NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(activity);
                dialogBuilder.withTitle("WARNING").withMessage(errMsg + "\nUSE OFFLINE MODE")
                        .isCancelable(false)
                        .withIcon(android.R.drawable.ic_dialog_alert)
                        .isCancelableOnTouchOutside(true)
                        .withButton1Text("OK")
                        .setButton1Click(new View.OnClickListener() {

                            @Override
                            public void onClick(View arg0) {
//                                if (!bundle.getBoolean(Global.BUND_KEY_MODE_SIMULASI, false))
                                CustomerFragment.doBack(activity);
                                Constant.listOfQuestion = result;
                                new Handler().postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        dialogBuilder.dismiss();
                                        GlobalData.getSharedGlobalData().setDoingTask(true);
                                        Intent intent = new Intent(activity, DynamicQuestionActivity.class);
                                        intent.putExtras(bundle);
                                        activity.startActivity(intent);
                                    }
                                }, 500);
                            }
                        })
                        .show();
            } else {
                NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(activity);
                if (errMsg.equals(activity.getString(R.string.no_data_found_offline)))
                    dialogBuilder.withTitle("INFO").withIcon(android.R.drawable.ic_dialog_info);
                else
                    dialogBuilder.withTitle("ERORR").withIcon(android.R.drawable.ic_dialog_alert);
                dialogBuilder.withMessage(errMsg).isCancelable(true).show();
                GlobalData.getSharedGlobalData().setDoingTask(false);
            }
        } else {
            if (result != null) {
                String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                List<TaskD> listTaskDImageDonwload = TaskDDataAccess.getAllTaskDToDownloadImage(activity,uuidUser , CustomerFragment.header.getTask_id());
                if(!listTaskDImageDonwload.isEmpty()){
                    final NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(activity);
                    dialogBuilder.withTitle("INFO").withMessage("There is problem when download image please try again later")
                            .withButton1Text("OK")
                            .withIcon(android.R.drawable.ic_dialog_info)
                            .setButton1Click(new View.OnClickListener() {

                                @Override
                                public void onClick(View v) {
                                    dialogBuilder.dismiss();
                                    if (!bundle.getBoolean(Global.BUND_KEY_MODE_SIMULASI, false))
                                    CustomerFragment.doBack(activity);
                                    GlobalData.getSharedGlobalData().setDoingTask(false);
                                }
                            })
                            .show();
                }else {
                    if (result.size() > 0) {
//                    if (!bundle.getBoolean(Global.BUND_KEY_MODE_SIMULASI, false))
                        CustomerFragment.doBack(activity);
                        Constant.listOfQuestion = result;
                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                GlobalData.getSharedGlobalData().setDoingTask(true);
                                Intent intent = new Intent(activity, DynamicQuestionActivity.class);
                                intent.putExtras(bundle);
                                activity.startActivity(intent);
                            }
                        }, 500);
                    } else {
                        final NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(activity);
                        dialogBuilder.withTitle("INFO").withMessage("Question List Not Found")
                                .withButton1Text("OK")
                                .withIcon(android.R.drawable.ic_dialog_info)
                                .setButton1Click(new View.OnClickListener() {

                                    @Override
                                    public void onClick(View v) {
                                        dialogBuilder.dismiss();
                                        if (!bundle.getBoolean(Global.BUND_KEY_MODE_SIMULASI, false))
                                            CustomerFragment.doBack(activity);
                                        GlobalData.getSharedGlobalData().setDoingTask(false);
                                    }
                                })
                                .show();

                    }
                }
            }
        }

        if (progressDialog.isShowing()) {
            try {
                progressDialog.dismiss();
            } catch (Exception e) {
                FireCrash.log(e);
            }
        }

    }

    public void getNewQuestionSet() {
        if (Tool.isInternetconnected(activity)) {
            JsonRequestQuestionSet request = new JsonRequestQuestionSet();
            request.setAudit(GlobalData.getSharedGlobalData().getAuditData());
            request.setUuid_scheme(CustomerFragment.header.getUuid_scheme());
            Scheme scheme = CustomerFragment.header.getScheme();
            String formVersionScheme = scheme.getForm_version();
            String formVersionTaskH = CustomerFragment.header.getForm_version();
            if (mode == Global.MODE_NEW_SURVEY) {
                request.setForm_version(formVersionScheme);
            } else if (mode == Global.MODE_SURVEY_TASK) {
                request.setForm_version(formVersionTaskH);
            } else if (mode == Global.MODE_VIEW_SENT_SURVEY) {
                request.setForm_version(formVersionTaskH);
            }
            String json = GsonHelper.toJson(request);
            String url = GlobalData.getSharedGlobalData().getURL_GET_QUESTIONSET();
            try {

                boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                httpConn = new HttpCryptedConnection(activity, encrypt, decrypt);
                serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
            } catch (Exception e) {
                FireCrash.log(e);
                if (Global.IS_DEV)
                    e.printStackTrace();
                errMsg = e.getMessage();
            }
            if (serverResult != null && serverResult.isOK()) {
                try {
                    String result = serverResult.getResult();
//					result = "{\"listQuestionSet\":[{\"question_group_id\":\"870E3C4D-FA73-473C-95AC-29B8E8801711\",\"question_group_name\":\"Pertanyaan Order1\",\"question_group_order\":1,\"question_id\":\"870E3C4D-FA73-473C-95AC-29B8E8801721\",\"question_label\":\"Pertanyaan Order1\",\"answer_type\":\"001\",\"is_mandatory\":\"0\",\"question_order\":1,\"max_length\":30,\"is_visible\":\"1\",\"is_readonly\":\"0\",\"calculate\":\"Calculate01\"},{\"question_group_id\":\"870E3C4D-FA73-473C-95AC-29B8E8801722\",\"question_group_name\":\"Pertanyaan Order2\",\"question_group_order\":1,\"question_id\":\"870E3C4D-FA73-473C-95AC-29B8E8801722\",\"question_label\":\"Pertanyaan Order2\",\"answer_type\":\"002\",\"is_mandatory\":\"0\",\"question_order\":2,\"max_length\":30,\"is_visible\":\"1\",\"is_readonly\":\"0\",\"calculate\":\"Calculate02\"},{\"question_group_id\":\"870E3C4D-FA73-473C-95AC-29B8E8801733\",\"question_group_name\":\"Pertanyaan Order3\",\"question_group_order\":1,\"question_id\":\"870E3C4D-FA73-473C-95AC-29B8E8801723\",\"question_label\":\"Pertanyaan Order3\",\"answer_type\":\"016\",\"is_mandatory\":\"0\",\"question_order\":3,\"max_length\":0,\"is_visible\":\"1\",\"is_readonly\":\"0\",\"calculate\":\"Calculate03\"},{\"question_group_id\":\"870E3C4D-FA73-473C-95AC-29B8E8801744\",\"question_group_name\":\"Pertanyaan Order4\",\"question_group_order\":1,\"question_id\":\"870E3C4D-FA73-473C-95AC-29B8E8801724\",\"question_label\":\"Pertanyaan Order4\",\"answer_type\":\"017\",\"is_mandatory\":\"0\",\"question_order\":4,\"max_length\":0,\"is_visible\":\"1\",\"is_readonly\":\"0\",\"calculate\":\"Calculate04\"}],\"status\":{\"code\":0}}";
                    JsonResponseQuestionSet response = GsonHelper.fromJson(result, JsonResponseQuestionSet.class);
                    if (response.getStatus().getCode() == 0) {

                        FormBean formBean = (FormBean) bundle.getSerializable(Global.BUND_KEY_FORM_BEAN);
                        if (formBean != null) {
                            Scheme lastScheme = formBean;
                            SchemeDataAccess.addOrReplace(activity, lastScheme);
                            scheme = lastScheme;
                        }

                        List<QuestionSet> questionSets = response.getListQuestionSet();
                        QuestionSetDataAccess.delete(activity, scheme.getUuid_scheme());
                        for (QuestionSet questionSet : questionSets) {
                            questionSet.setUuid_question_set(Tool.getUUID());
                            questionSet.setScheme(scheme);
                        }
                        QuestionSetDataAccess.addOrReplace(activity, scheme.getUuid_scheme(), questionSets);
                        List<QuestionSet> qs;
                        if (mode == 2) {
                            qs = QuestionSetDataAccess.getAllByFormVersion(activity, CustomerFragment.header.getUuid_scheme(), formVersionScheme);
                        } else {
                            qs = QuestionSetDataAccess.getAllByFormVersion(activity, CustomerFragment.header.getUuid_scheme(), formVersionTaskH);
                        }
                        if (qs == null || qs.isEmpty()) {
                            if (mode == 2) {
                                qs = QuestionSetDataAccess.getAllByFormVersion(activity, CustomerFragment.header.getUuid_scheme(), formVersionScheme);
                            } else {
                                qs = QuestionSetDataAccess.getAllByFormVersion(activity, CustomerFragment.header.getUuid_scheme(), formVersionTaskH);
                            }
                        }
                        for (QuestionSet set : qs) {
                            QuestionBean bean = new QuestionBean(set);
                            questions.put(bean.getIdentifier_name().toUpperCase(), bean);

                        }
                    } else {
                        errMsg = result;
                        List<QuestionSet> qs;
                        if (mode == 2) {
                            qs = QuestionSetDataAccess.getAllByFormVersion(activity, CustomerFragment.header.getUuid_scheme(), formVersionScheme);
                        } else {
                            qs = QuestionSetDataAccess.getAllByFormVersion(activity, CustomerFragment.header.getUuid_scheme(), formVersionTaskH);
                        }
                        if (qs != null && !qs.isEmpty()) {
                            for (QuestionSet set : qs) {
                                QuestionBean bean = new QuestionBean(set);
                                questions.put(bean.getIdentifier_name().toUpperCase(), bean);

                            }
                        }
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    getQuestionFromDB();
                }
            }
        } else {
            getQuestionFromDB();
        }
    }

    public void getQuestionFromDB() {
        try {
            List<QuestionSet> qs;
            if (mode == 2) {
                qs = QuestionSetDataAccess.getAllByFormVersion(activity, CustomerFragment.header.getUuid_scheme(), CustomerFragment.header.getScheme().getForm_version());
            } else {
                qs = QuestionSetDataAccess.getAllByFormVersion(activity, CustomerFragment.header.getUuid_scheme(), CustomerFragment.header.getForm_version());
            }
            for (QuestionSet set : qs) {
                QuestionBean bean = new QuestionBean(set);
                questions.put(bean.getIdentifier_name().toUpperCase(), bean);
            }
        } catch (Exception e2) {
            sendAcraReport(e2.getMessage());
            errMsg = e2.getMessage();
        }
    }

    public void setQuestionSet(LinkedHashMap<String, QuestionBean> result) {
        for (Map.Entry<String, QuestionBean> entry : result.entrySet()) {
            QuestionBean bean = entry.getValue();
            String relevantExpression = bean.getRelevant_question();
            if (relevantExpression == null) relevantExpression = "";
            String convertedExpression = relevantExpression;
            setRelevanted(result, convertedExpression);

            if (convertedExpression.contains("{")) {
                //TODO: Add Param for validate relevant
            } else {
                String validateExpression = bean.getQuestion_validation();
                if (validateExpression == null) validateExpression = "";
                setRelevanted(result, validateExpression);
            }

            String formula = bean.getCalculate();
            setRelevanFromCalculate(result, formula);

            if (!bean.getAnswer_type().equalsIgnoreCase(Global.AT_LOOKUP_TABLE) &&
                    !bean.getAnswer_type().equalsIgnoreCase(Global.AT_TEXT_WITH_SUGGESTION_NEW)) {
                String choiceFilter = bean.getChoice_filter();
                try {
                    setRelevanFromChoiceFilter(result,choiceFilter, bean);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            String copyValue = bean.getQuestion_value();
            setRelevanFromCopyValue(result, copyValue, bean);

            //check affected otr from choice filter
            if(bean.getChoice_filter()!=null && bean.getChoice_filter().contains("fields")) {
                try {
                    Query query =  GsonHelper.fromJson(bean.getChoice_filter(), Query.class);;
                    if ("MS_MARKET_PRICE".equalsIgnoreCase(query.getTable())) {
                        setRelevanFromChoiceFilterFields(result, bean.getChoice_filter(), bean, query);
                    }
                } catch (Exception ex) {
                    if(Global.IS_DEV) {
                        ex.printStackTrace();
                    }
                }
            }
        }
    }

    private void setRelevanFromChoiceFilter(LinkedHashMap<String, QuestionBean> result, String choiceFilter, QuestionBean mBean) {
        if (StringUtils.isNotBlank(choiceFilter)) {
            String[] tempfilters = Tool.split(choiceFilter, Global.DELIMETER_DATA3);
            for (String newFilter : tempfilters) {
                newFilter = newFilter.replace("{", "");
                newFilter = newFilter.replace("{", "");
                if (!newFilter.contains("%") && result.get(newFilter.toUpperCase()) != null) {
                    QuestionBean bean2 = result.get(newFilter.toUpperCase());
                    bean2.setRelevanted(true);
                    //2022-09-26: penambahan untuk reset answer type DSR
                    if (Global.AT_DSR.equalsIgnoreCase(mBean.getAnswer_type())) {
                        bean2.addToAffectedQuestionBeanOptions(mBean);
                    }
                }
            }
        }
    }
    private void setRelevanFromChoiceFilterFields(LinkedHashMap<String, QuestionBean> result, String choiceFilter, QuestionBean mBean, Query query) {
        if (StringUtils.isNotBlank(choiceFilter)) {
            List<Query.Constraint> constraints = query.getConstraint();
            for (Query.Constraint newFilter : constraints) {
                String column = newFilter.getValue();
                if (StringUtils.isNotBlank(column) && column.contains("[") && column.contains("]")) {
                    column = column.replace("[", "");
                    column = column.replace("]", "");
                    QuestionBean beanColumn = result.get(column);
                    if (beanColumn != null) {
                        beanColumn.setRelevanted(true);
                        beanColumn.addToAffectedQuestionBeanChoiceFilterFields(mBean);
                    }
                }
            }
        }
    }
    private void setRelevanFromCalculate(LinkedHashMap<String, QuestionBean> result, String formula) {
        try {
            if (formula != null && formula.length() > 0) {
                String resultformula2 = formula.substring(0, formula.indexOf("for"));
                resultformula2 = resultformula2.replace("_var = 0", "");
                resultformula2 = resultformula2.replace("var ", "");
                resultformula2 = resultformula2.replace(" ", "");
                String[] newIdentifier = resultformula2.split(";");
                for (String identifier : newIdentifier) {
                    if (Global.IS_DEV)
                        Logger.i("INFO", identifier);
                    QuestionBean bean2 = result.get(identifier.toUpperCase());

                    if (bean2 != null) {
                        bean2.setRelevanted(true);
                    }
                }
            }
        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: handle exception
        }
    }

    public void setRelevanted(LinkedHashMap<String, QuestionBean> result, String convertedExpression) {
        if (convertedExpression != null && convertedExpression.length() > 0) {
            boolean needReplacing = true;
            while (needReplacing) {
                convertedExpression = replaceModifiers(convertedExpression);
                int idxOfOpenBrace = convertedExpression.indexOf('{');
                if (idxOfOpenBrace != -1) {                    //there's {, prepare to replace what inside the {}
                    int idxOfCloseBrace = convertedExpression.indexOf('}');
                    String identifier = convertedExpression.substring(idxOfOpenBrace + 1, idxOfCloseBrace);

                    int idxOfOpenAbs = identifier.indexOf("$");
                    if (idxOfOpenAbs != -1) {
                        convertedExpression = convertedExpression.replace("{" + identifier + "}", "\"\"");
                    } else {
                        QuestionBean bean2 = result.get(identifier.toUpperCase());
                        if (bean2 != null) {
                            bean2.setRelevanted(true);
                            convertedExpression = convertedExpression.replace("{" + identifier + "}", "\"\"");
                        } else {
                            convertedExpression = convertedExpression.replace("{" + identifier + "}", "\"\"");
                        }
                    }
                } else {
                    needReplacing = false;
                }
            }
        }
    }

    private void setRelevanFromCopyValue(LinkedHashMap<String, QuestionBean> result, String copyValueScript, QuestionBean mBean) {
        if (StringUtils.isNotBlank(copyValueScript)) {
            boolean isCopyFromLookup = false;
            boolean isCopyValue = false;
            boolean isCopyValueFromIdentifier = false;
            try {
                if (copyValueScript.contains("copyFromLookup("))
                    isCopyFromLookup = copyValueScript.substring(0, 15).equals("copyFromLookup(");
                isCopyValue = copyValueScript.substring(0, 4).equals("copy");
                isCopyValueFromIdentifier = copyValueScript.substring(0, 1).equals("{");
            } catch (Exception e) {
                FireCrash.log(e);
                isCopyFromLookup = false;
                if (Global.IS_DEV)
                    e.printStackTrace();
            }
            if (!mBean.isReadOnly() && ((isCopyFromLookup) || (isCopyValue) || (isCopyValueFromIdentifier)) &&
                    !(copyValueScript.contains("{\"parameters\":[") && copyValueScript.contains("]}"))) {
                boolean needReplacing = true;
                while (needReplacing) {
                    int idxOfOpenBrace = copyValueScript.indexOf('{');
                    if (idxOfOpenBrace != -1) {
                        int idxOfCloseBrace = copyValueScript.indexOf('}');
                        String identifier = copyValueScript.substring(idxOfOpenBrace + 1, idxOfCloseBrace);
                        int idxOfOpenAbs = identifier.indexOf("$");
                        if (idxOfOpenAbs != -1) {
                            needReplacing = false;
                        } else {
                            QuestionBean bean2 = result.get(identifier.toUpperCase());
                            copyValueScript = copyValueScript.replace("{" + identifier + "}", identifier);
                            if(bean2!=null){
                                bean2.setRelevanted(true);
                                bean2.addToAffectedQuestionBeanCopyValue(mBean);
                            }
                        }
                    }else{
                        needReplacing = false;
                    }
                }
            }
        }
    }

    private List<OptionAnswerBean> GetLookupFromDB(QuestionBean bean, List<String> filters) {
        List<OptionAnswerBean> optionAnswers = new ArrayList<OptionAnswerBean>();
        if (!filters.isEmpty()) {
            if (filters.size() == 1) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilter(activity, bean.getLov_group(), filters.get(0));
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            } else if (filters.size() == 2) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilter(activity, bean.getLov_group(), filters.get(0), filters.get(1));
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            } else if (filters.size() == 3) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilter(activity, bean.getLov_group(), filters.get(0), filters.get(1), filters.get(2));
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            } else if (filters.size() == 4) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilter(activity, bean.getLov_group(), filters.get(0), filters.get(1), filters.get(2), filters.get(3));
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            } else if (filters.size() == 5) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilter(activity, bean.getLov_group(), filters.get(0), filters.get(1), filters.get(2), filters.get(3), filters.get(4));
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            }

        } else {
            if (bean.getChoice_filter() != null && bean.getChoice_filter().length() > 0) {
                List<Lookup> lookups = new ArrayList<Lookup>();
                optionAnswers = OptionAnswerBean.getOptionList(lookups);
            } else {
                List<Lookup> lookups = LookupDataAccess.getAllByLovGroup(activity, bean.getLov_group());
                if (lookups != null)
                    optionAnswers = OptionAnswerBean.getOptionList(lookups);
            }
        }
        return optionAnswers;
    }

    protected String replaceModifiers(String sourceString) {
        String newString = sourceString;
        //replace branch modifier
        String branch = GlobalData.getSharedGlobalData().getUser().getBranch_id();
        newString = newString.replace(QuestionBean.PLACEMENT_KEY_BRANCH, branch);
        //replace user modifier
        String user = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        return newString.replace(QuestionBean.PLACEMENT_KEY_USER, user);
    }

    public void getImageFromServer(String uuidTask , String questionId){
        if (Tool.isInternetconnected(activity)) {

            JsonRequestTaskD request = new JsonRequestTaskD();
            request.setAudit(GlobalData.getSharedGlobalData().getAuditData());
            request.setuuid_task_h(CustomerFragment.header.getUuid_task_h());
            request.setUuid_question(questionId);

            String json = GsonHelper.toJson(request);
            String url = GlobalData.getSharedGlobalData().getURL_GETVERIFICATION_IMAGE();

            try {

                boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                httpConn = new HttpCryptedConnection(activity, encrypt, decrypt);
                serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
            } catch (Exception e) {
                FireCrash.log(e);
                if (Global.IS_DEV)
                    e.printStackTrace();
                errMsg = e.getMessage();
            }
            if (serverResult != null && serverResult.isOK()) {
                try {
                    String result = serverResult.getResult();
//					result = "{\"listQuestionSet\":[{\"question_group_id\":\"870E3C4D-FA73-473C-95AC-29B8E8801711\",\"question_group_name\":\"Pertanyaan Order1\",\"question_group_order\":1,\"question_id\":\"870E3C4D-FA73-473C-95AC-29B8E8801721\",\"question_label\":\"Pertanyaan Order1\",\"answer_type\":\"001\",\"is_mandatory\":\"0\",\"question_order\":1,\"max_length\":30,\"is_visible\":\"1\",\"is_readonly\":\"0\",\"calculate\":\"Calculate01\"},{\"question_group_id\":\"870E3C4D-FA73-473C-95AC-29B8E8801722\",\"question_group_name\":\"Pertanyaan Order2\",\"question_group_order\":1,\"question_id\":\"870E3C4D-FA73-473C-95AC-29B8E8801722\",\"question_label\":\"Pertanyaan Order2\",\"answer_type\":\"002\",\"is_mandatory\":\"0\",\"question_order\":2,\"max_length\":30,\"is_visible\":\"1\",\"is_readonly\":\"0\",\"calculate\":\"Calculate02\"},{\"question_group_id\":\"870E3C4D-FA73-473C-95AC-29B8E8801733\",\"question_group_name\":\"Pertanyaan Order3\",\"question_group_order\":1,\"question_id\":\"870E3C4D-FA73-473C-95AC-29B8E8801723\",\"question_label\":\"Pertanyaan Order3\",\"answer_type\":\"016\",\"is_mandatory\":\"0\",\"question_order\":3,\"max_length\":0,\"is_visible\":\"1\",\"is_readonly\":\"0\",\"calculate\":\"Calculate03\"},{\"question_group_id\":\"870E3C4D-FA73-473C-95AC-29B8E8801744\",\"question_group_name\":\"Pertanyaan Order4\",\"question_group_order\":1,\"question_id\":\"870E3C4D-FA73-473C-95AC-29B8E8801724\",\"question_label\":\"Pertanyaan Order4\",\"answer_type\":\"017\",\"is_mandatory\":\"0\",\"question_order\":4,\"max_length\":0,\"is_visible\":\"1\",\"is_readonly\":\"0\",\"calculate\":\"Calculate04\"}],\"status\":{\"code\":0}}";
                    JsonResponseTaskD response = GsonHelper.fromJson(result, JsonResponseTaskD.class);
                    if (response.getStatus().getCode() == 0) {
                        TaskDDataAccess.addOrReplace(activity , response.getListTask());
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                }
            }
        }

    }
}