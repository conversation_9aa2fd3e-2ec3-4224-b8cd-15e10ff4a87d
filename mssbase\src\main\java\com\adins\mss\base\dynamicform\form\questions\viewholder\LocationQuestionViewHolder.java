package com.adins.mss.base.dynamicform.form.questions.viewholder;

import android.content.Intent;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.dynamicform.DynamicFormActivity;
import com.adins.mss.base.dynamicform.form.questions.OnQuestionClickListener;
import com.adins.mss.base.timeline.MapsViewer;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.LocationInfo;
import com.adins.mss.foundation.dialog.DialogManager;
import com.adins.mss.foundation.location.LocationTrackingManager;
import com.adins.mss.foundation.questiongenerator.QuestionBean;
import com.adins.mss.foundation.questiongenerator.form.LocationTagingView;
import com.adins.mss.foundation.questiongenerator.form.QuestionView;

/**
 * Created by gigin.ginanjar on 31/08/2016.
 */
public class LocationQuestionViewHolder extends RecyclerView.ViewHolder {
    public QuestionView mView;
    public TextView mQuestionLabel;
    public TextView mQuestionAnswer;
    public Button mButtonSetLocation;
    public ImageView mImageAnswer;
    public QuestionBean bean;
    public FragmentActivity mActivity;
    public OnQuestionClickListener mListener;
    private int group;
    private int position;

    @Deprecated
    public LocationQuestionViewHolder(View itemView) {
        super(itemView);
        mView = (QuestionView) itemView.findViewById(R.id.questionLocationLayout);
        mQuestionLabel = (TextView) itemView.findViewById(R.id.questionLocationLabel);
        mQuestionAnswer = (TextView) itemView.findViewById(R.id.questionLocationAnswer);
        mButtonSetLocation = (Button) itemView.findViewById(R.id.btnSetLocation);
        mImageAnswer = (ImageView) itemView.findViewById(R.id.imgLocationAnswer);
    }

    public LocationQuestionViewHolder(View itemView, FragmentActivity activity, OnQuestionClickListener listener) {
        super(itemView);
        mView = (QuestionView) itemView.findViewById(R.id.questionLocationLayout);
        mQuestionLabel = (TextView) itemView.findViewById(R.id.questionLocationLabel);
        mQuestionAnswer = (TextView) itemView.findViewById(R.id.questionLocationAnswer);
        mButtonSetLocation = (Button) itemView.findViewById(R.id.btnSetLocation);
        mImageAnswer = (ImageView) itemView.findViewById(R.id.imgLocationAnswer);
        mActivity = activity;
        mListener = listener;
    }

    public void bind(final QuestionBean item, final int group, int number) {
        bean = item;
        this.group = group;
        position = number - 1;
        String qLabel = number + ". " + bean.getQuestion_label();

        mQuestionLabel.setText(qLabel);
        View.OnClickListener listener = null;

        if (Global.AT_GPS.equals(bean.getAnswer_type())) {
            listener = new View.OnClickListener() {
                public void onClick(View v) {
                    LocationInfo info = Global.LTM.getCurrentLocation(Global.FLAG_LOCATION_CAMERA);
                    bean.setLocationInfo(info);
                    bean.setLocationInfo(info);
                    mQuestionAnswer.setText(LocationTrackingManager.toAnswerString(info));
                }
            };
        } else {
            listener = new View.OnClickListener() {
                public void onClick(View v) {
                    mListener.onSetLocationClick(bean, group, position);
                    Intent intent = new Intent(mActivity, LocationTagingView.class);
                    if (bean.getAnswer_type().equals(Global.AT_LOCATION_WITH_ADDRESS)){
                        intent.putExtra("isAddress", true);
                        if (bean.locationInfo!=null) {
                            String lat = bean.getLocationInfo().getLatitude();
                            String lng = bean.getLocationInfo().getLongitude();
                            int acc = bean.getLocationInfo().getAccuracy()==null?0:bean.getLocationInfo().getAccuracy();
                            intent.putExtra("latitude", lat);
                            intent.putExtra("longitude", lng);
                            intent.putExtra("accuracy", acc);
                        }
                    } else{
                        intent.putExtra("isAddress", false);
                    }
                    mActivity.startActivityForResult(intent, Global.REQUEST_LOCATIONTAGGING);
                }
            };
        }
        mImageAnswer.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (bean.getAnswer() != null && bean.getAnswer().length() > 0) {
                    mListener.onUpdateLocationClick(bean, group, position);
                    try {
                        String lat = bean.getLocationInfo().getLatitude();
                        String lng = bean.getLocationInfo().getLongitude();
                        int acc = bean.getLocationInfo().getAccuracy();
                        Bundle extras = new Bundle();
                        extras.putString("latitude", lat);
                        extras.putString("longitude", lng);
                        extras.putInt("accuracy", acc);
                        extras.putBoolean("isAddress", bean.getAnswer_type().equals(Global.AT_LOCATION_WITH_ADDRESS));
                        Intent intent = new Intent(mActivity, MapsViewer.class);
                        intent.putExtras(extras);
                        mActivity.startActivityForResult(intent, Global.REQUEST_LOCATION_UPDATE);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        String lat = bean.getLatitude();
                        String lng = bean.getLongitude();
                        Bundle extras = new Bundle();
                        extras.putString("latitude", lat);
                        extras.putString("longitude", lng);
                        extras.putBoolean("isAddress", bean.getAnswer_type().equals(Global.AT_LOCATION_WITH_ADDRESS));
                        Intent intent = new Intent(mActivity, MapsViewer.class);
                        intent.putExtras(extras);
                        mActivity.startActivityForResult(intent, Global.REQUEST_LOCATION_UPDATE);
                    }
                } else {
                    Toast.makeText(mActivity, "Set Location First!",
                            Toast.LENGTH_LONG).show();
                }
            }
        });
        if (bean.getAnswer() != null && !bean.getAnswer().isEmpty())
            if (bean.getAnswer_type().equals(Global.AT_LOCATION_WITH_ADDRESS)){
                String[] hasil = bean.getAnswer().split("["+Global.DELIMETER_ROW+"]");
                StringBuilder finalAnswer = new StringBuilder();
                for (String s : hasil) {
                    finalAnswer.append(s).append("\n");
                }
                mQuestionAnswer.setText(finalAnswer.toString());
            }else {
                mQuestionAnswer.setText(bean.getAnswer());
            }
        else
            mQuestionAnswer.setText("");
        mButtonSetLocation.setEnabled(!bean.isReadOnly());
        mButtonSetLocation.setOnClickListener(listener);
        if (DynamicFormActivity.isVerified || DynamicFormActivity.isApproval)
            mButtonSetLocation.setVisibility(View.GONE);
        else
            mButtonSetLocation.setVisibility(View.VISIBLE);

        try {
            DialogManager.showGPSAlert(mActivity);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
