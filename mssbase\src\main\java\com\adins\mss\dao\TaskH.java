package com.adins.mss.dao;

import java.util.List;
import com.adins.mss.dao.DaoSession;
import de.greenrobot.dao.DaoException;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "TR_TASK_H".
 */
public class TaskH {

    /** Not-null value. */
     @SerializedName("uuid_task_h")
    private String uuid_task_h;
     @SerializedName("task_id")
    private String task_id;
     @SerializedName("status")
    private String status;
     @SerializedName("is_printable")
    private String is_printable;
     @SerializedName("customer_name")
    private String customer_name;
     @SerializedName("customer_phone")
    private String customer_phone;
     @SerializedName("customer_address")
    private String customer_address;
     @SerializedName("notes")
    private String notes;
     @SerializedName("submit_date")
    private java.util.Date submit_date;
     @SerializedName("submit_duration")
    private String submit_duration;
     @SerializedName("submit_size")
    private String submit_size;
     @SerializedName("submit_result")
    private String submit_result;
     @SerializedName("assignment_date")
    private java.util.Date assignment_date;
     @SerializedName("print_count")
    private Integer print_count;
     @SerializedName("draft_date")
    private java.util.Date draft_date;
     @SerializedName("usr_crt")
    private String usr_crt;
     @SerializedName("dtm_crt")
    private java.util.Date dtm_crt;
     @SerializedName("priority")
    private String priority;
     @SerializedName("latitude")
    private String latitude;
     @SerializedName("longitude")
    private String longitude;
     @SerializedName("scheme_last_update")
    private java.util.Date scheme_last_update;
     @SerializedName("is_verification")
    private String is_verification;
     @SerializedName("is_preview_server")
    private String is_preview_server;
     @SerializedName("uuid_user")
    private String uuid_user;
     @SerializedName("voice_note")
    private byte[] voice_note;
     @SerializedName("uuid_scheme")
    private String uuid_scheme;
     @SerializedName("zip_code")
    private String zip_code;
     @SerializedName("start_date")
    private java.util.Date start_date;
     @SerializedName("open_date")
    private java.util.Date open_date;
     @SerializedName("appl_no")
    private String appl_no;
     @ExcludeFromGson 
	 @SerializedName("is_prepocessed")
    private String is_prepocessed;
     @SerializedName("last_saved_question")
    private Integer last_saved_question;
     @SerializedName("is_reconciled")
    private String is_reconciled;
     @SerializedName("pts_date")
    private java.util.Date pts_date;
     @ExcludeFromGson 
	 @SerializedName("access_mode")
    private String access_mode;
     @SerializedName("rv_number")
    private String rv_number;
     @SerializedName("status_rv")
    private String status_rv;
     @SerializedName("flag")
    private String flag;
     @SerializedName("pms_date")
    private java.util.Date pms_date;
     @SerializedName("is_sent_pts")
    private String is_sent_pts;
     @SerializedName("form_version")
    private String form_version;
     @SerializedName("flag_survey")
    private String flag_survey;
     @SerializedName("uuid_resurvey_user")
    private String uuid_resurvey_user;
     @SerializedName("resurvey_suggested")
    private String resurvey_suggested;
     @SerializedName("verification_notes")
    private String verification_notes;
     @SerializedName("od")
    private String od;
     @SerializedName("amt_due")
    private String amt_due;
     @SerializedName("inst_no")
    private String inst_no;
     @SerializedName("uuid_task_update")
    private String uuid_task_update;
     @SerializedName("pending_notes")
    private String pending_notes;
     @SerializedName("docupro_feedback")
    private String docupro_feedback;
     @SerializedName("status_application")
    private String status_application;
     @SerializedName("is_sent_confins")
    private Integer is_sent_confins;
     @SerializedName("is_already_notified")
    private Integer is_already_notified;
     @SerializedName("kelurahan")
    private String kelurahan;
     @SerializedName("status_followup")
    private String status_followup;
     @SerializedName("is_revisit")
    private String is_revisit;
     @SerializedName("visit_type")
    private String visit_type;
     @SerializedName("sendTaskPromiseToSurvey")
    private String send_task_promise_to_survey;
     @SerializedName("sendTaskPreSurvey")
    private String send_task_presurvey;
     @SerializedName("sendTaskSurvey")
    private String send_task_survey;
     @SerializedName("is_piloting_cae")
    private String is_piloting_cae;
     @SerializedName("category")
    private String category;
     @SerializedName("sub_category")
    private String sub_category;
     @SerializedName("reason_detail")
    private String reason_detail;
     @SerializedName("validasi")
    private String validasi;
     @SerializedName("notes_crm")
    private String notes_crm;
     @SerializedName("is_pre_approval")
    private Integer is_pre_approval;
     @SerializedName("source_data")
    private String source_data;
     @SerializedName("is_already_download_task")
    private String is_already_download_task;
     @SerializedName("productName")
    private String product_name;
     @SerializedName("jenisAsset")
    private String jenis_asset;

    /** Used to resolve relations */
    private transient DaoSession daoSession;

    /** Used for active entity operations. */
    private transient TaskHDao myDao;

    private User user;
    private String user__resolvedKey;

    private Scheme scheme;
    private String scheme__resolvedKey;

    private List<ImageResult> imageResultList;
    private List<ReceiptVoucher> receiptVoucherList;
    private List<TaskD> taskDList;
    private List<ErrorLog> errorLogList;
    private List<Timeline> timelineList;
    private List<TaskHSequence> taskHSequenceList;
    private List<ReminderPo> reminderPoList;

    public TaskH() {
    }

    public TaskH(String uuid_task_h) {
        this.uuid_task_h = uuid_task_h;
    }

    public TaskH(String uuid_task_h, String task_id, String status, String is_printable, String customer_name, String customer_phone, String customer_address, String notes, java.util.Date submit_date, String submit_duration, String submit_size, String submit_result, java.util.Date assignment_date, Integer print_count, java.util.Date draft_date, String usr_crt, java.util.Date dtm_crt, String priority, String latitude, String longitude, java.util.Date scheme_last_update, String is_verification, String is_preview_server, String uuid_user, byte[] voice_note, String uuid_scheme, String zip_code, java.util.Date start_date, java.util.Date open_date, String appl_no, String is_prepocessed, Integer last_saved_question, String is_reconciled, java.util.Date pts_date, String access_mode, String rv_number, String status_rv, String flag, java.util.Date pms_date, String is_sent_pts, String form_version, String flag_survey, String uuid_resurvey_user, String resurvey_suggested, String verification_notes, String od, String amt_due, String inst_no, String uuid_task_update, String pending_notes, String docupro_feedback, String status_application, Integer is_sent_confins, Integer is_already_notified, String kelurahan, String status_followup, String is_revisit, String visit_type, String send_task_promise_to_survey, String send_task_presurvey, String send_task_survey, String is_piloting_cae, String category, String sub_category, String reason_detail, String validasi, String notes_crm, Integer is_pre_approval, String source_data, String is_already_download_task, String product_name, String jenis_asset) {
        this.uuid_task_h = uuid_task_h;
        this.task_id = task_id;
        this.status = status;
        this.is_printable = is_printable;
        this.customer_name = customer_name;
        this.customer_phone = customer_phone;
        this.customer_address = customer_address;
        this.notes = notes;
        this.submit_date = submit_date;
        this.submit_duration = submit_duration;
        this.submit_size = submit_size;
        this.submit_result = submit_result;
        this.assignment_date = assignment_date;
        this.print_count = print_count;
        this.draft_date = draft_date;
        this.usr_crt = usr_crt;
        this.dtm_crt = dtm_crt;
        this.priority = priority;
        this.latitude = latitude;
        this.longitude = longitude;
        this.scheme_last_update = scheme_last_update;
        this.is_verification = is_verification;
        this.is_preview_server = is_preview_server;
        this.uuid_user = uuid_user;
        this.voice_note = voice_note;
        this.uuid_scheme = uuid_scheme;
        this.zip_code = zip_code;
        this.start_date = start_date;
        this.open_date = open_date;
        this.appl_no = appl_no;
        this.is_prepocessed = is_prepocessed;
        this.last_saved_question = last_saved_question;
        this.is_reconciled = is_reconciled;
        this.pts_date = pts_date;
        this.access_mode = access_mode;
        this.rv_number = rv_number;
        this.status_rv = status_rv;
        this.flag = flag;
        this.pms_date = pms_date;
        this.is_sent_pts = is_sent_pts;
        this.form_version = form_version;
        this.flag_survey = flag_survey;
        this.uuid_resurvey_user = uuid_resurvey_user;
        this.resurvey_suggested = resurvey_suggested;
        this.verification_notes = verification_notes;
        this.od = od;
        this.amt_due = amt_due;
        this.inst_no = inst_no;
        this.uuid_task_update = uuid_task_update;
        this.pending_notes = pending_notes;
        this.docupro_feedback = docupro_feedback;
        this.status_application = status_application;
        this.is_sent_confins = is_sent_confins;
        this.is_already_notified = is_already_notified;
        this.kelurahan = kelurahan;
        this.status_followup = status_followup;
        this.is_revisit = is_revisit;
        this.visit_type = visit_type;
        this.send_task_promise_to_survey = send_task_promise_to_survey;
        this.send_task_presurvey = send_task_presurvey;
        this.send_task_survey = send_task_survey;
        this.is_piloting_cae = is_piloting_cae;
        this.category = category;
        this.sub_category = sub_category;
        this.reason_detail = reason_detail;
        this.validasi = validasi;
        this.notes_crm = notes_crm;
        this.is_pre_approval = is_pre_approval;
        this.source_data = source_data;
        this.is_already_download_task = is_already_download_task;
        this.product_name = product_name;
        this.jenis_asset = jenis_asset;
    }

    private String message;

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    /** called by internal mechanisms, do not call yourself. */
    public void __setDaoSession(DaoSession daoSession) {
        this.daoSession = daoSession;
        myDao = daoSession != null ? daoSession.getTaskHDao() : null;
    }

    /** Not-null value. */
    public String getUuid_task_h() {
        return uuid_task_h;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_task_h(String uuid_task_h) {
        this.uuid_task_h = uuid_task_h;
    }

    public String getTask_id() {
        return task_id;
    }

    public void setTask_id(String task_id) {
        this.task_id = task_id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getIs_printable() {
        return is_printable;
    }

    public void setIs_printable(String is_printable) {
        this.is_printable = is_printable;
    }

    public String getCustomer_name() {
        return customer_name;
    }

    public void setCustomer_name(String customer_name) {
        this.customer_name = customer_name;
    }

    public String getCustomer_phone() {
        return customer_phone;
    }

    public void setCustomer_phone(String customer_phone) {
        this.customer_phone = customer_phone;
    }

    public String getCustomer_address() {
        return customer_address;
    }

    public void setCustomer_address(String customer_address) {
        this.customer_address = customer_address;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public java.util.Date getSubmit_date() {
        return submit_date;
    }

    public void setSubmit_date(java.util.Date submit_date) {
        this.submit_date = submit_date;
    }

    public String getSubmit_duration() {
        return submit_duration;
    }

    public void setSubmit_duration(String submit_duration) {
        this.submit_duration = submit_duration;
    }

    public String getSubmit_size() {
        return submit_size;
    }

    public void setSubmit_size(String submit_size) {
        this.submit_size = submit_size;
    }

    public String getSubmit_result() {
        return submit_result;
    }

    public void setSubmit_result(String submit_result) {
        this.submit_result = submit_result;
    }

    public java.util.Date getAssignment_date() {
        return assignment_date;
    }

    public void setAssignment_date(java.util.Date assignment_date) {
        this.assignment_date = assignment_date;
    }

    public Integer getPrint_count() {
        return print_count;
    }

    public void setPrint_count(Integer print_count) {
        this.print_count = print_count;
    }

    public java.util.Date getDraft_date() {
        return draft_date;
    }

    public void setDraft_date(java.util.Date draft_date) {
        this.draft_date = draft_date;
    }

    public String getUsr_crt() {
        return usr_crt;
    }

    public void setUsr_crt(String usr_crt) {
        this.usr_crt = usr_crt;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public java.util.Date getScheme_last_update() {
        return scheme_last_update;
    }

    public void setScheme_last_update(java.util.Date scheme_last_update) {
        this.scheme_last_update = scheme_last_update;
    }

    public String getIs_verification() {
        return is_verification;
    }

    public void setIs_verification(String is_verification) {
        this.is_verification = is_verification;
    }

    public String getIs_preview_server() {
        return is_preview_server;
    }

    public void setIs_preview_server(String is_preview_server) {
        this.is_preview_server = is_preview_server;
    }

    public String getUuid_user() {
        return uuid_user;
    }

    public void setUuid_user(String uuid_user) {
        this.uuid_user = uuid_user;
    }

    public byte[] getVoice_note() {
        return voice_note;
    }

    public void setVoice_note(byte[] voice_note) {
        this.voice_note = voice_note;
    }

    public String getUuid_scheme() {
        return uuid_scheme;
    }

    public void setUuid_scheme(String uuid_scheme) {
        this.uuid_scheme = uuid_scheme;
    }

    public String getZip_code() {
        return zip_code;
    }

    public void setZip_code(String zip_code) {
        this.zip_code = zip_code;
    }

    public java.util.Date getStart_date() {
        return start_date;
    }

    public void setStart_date(java.util.Date start_date) {
        this.start_date = start_date;
    }

    public java.util.Date getOpen_date() {
        return open_date;
    }

    public void setOpen_date(java.util.Date open_date) {
        this.open_date = open_date;
    }

    public String getAppl_no() {
        return appl_no;
    }

    public void setAppl_no(String appl_no) {
        this.appl_no = appl_no;
    }

    public String getIs_prepocessed() {
        return is_prepocessed;
    }

    public void setIs_prepocessed(String is_prepocessed) {
        this.is_prepocessed = is_prepocessed;
    }

    public Integer getLast_saved_question() {
        return last_saved_question;
    }

    public void setLast_saved_question(Integer last_saved_question) {
        this.last_saved_question = last_saved_question;
    }

    public String getIs_reconciled() {
        return is_reconciled;
    }

    public void setIs_reconciled(String is_reconciled) {
        this.is_reconciled = is_reconciled;
    }

    public java.util.Date getPts_date() {
        return pts_date;
    }

    public void setPts_date(java.util.Date pts_date) {
        this.pts_date = pts_date;
    }

    public String getAccess_mode() {
        return access_mode;
    }

    public void setAccess_mode(String access_mode) {
        this.access_mode = access_mode;
    }

    public String getRv_number() {
        return rv_number;
    }

    public void setRv_number(String rv_number) {
        this.rv_number = rv_number;
    }

    public String getStatus_rv() {
        return status_rv;
    }

    public void setStatus_rv(String status_rv) {
        this.status_rv = status_rv;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public java.util.Date getPms_date() {
        return pms_date;
    }

    public void setPms_date(java.util.Date pms_date) {
        this.pms_date = pms_date;
    }

    public String getIs_sent_pts() {
        return is_sent_pts;
    }

    public void setIs_sent_pts(String is_sent_pts) {
        this.is_sent_pts = is_sent_pts;
    }

    public String getForm_version() {
        return form_version;
    }

    public void setForm_version(String form_version) {
        this.form_version = form_version;
    }

    public String getFlag_survey() {
        return flag_survey;
    }

    public void setFlag_survey(String flag_survey) {
        this.flag_survey = flag_survey;
    }

    public String getUuid_resurvey_user() {
        return uuid_resurvey_user;
    }

    public void setUuid_resurvey_user(String uuid_resurvey_user) {
        this.uuid_resurvey_user = uuid_resurvey_user;
    }

    public String getResurvey_suggested() {
        return resurvey_suggested;
    }

    public void setResurvey_suggested(String resurvey_suggested) {
        this.resurvey_suggested = resurvey_suggested;
    }

    public String getVerification_notes() {
        return verification_notes;
    }

    public void setVerification_notes(String verification_notes) {
        this.verification_notes = verification_notes;
    }

    public String getOd() {
        return od;
    }

    public void setOd(String od) {
        this.od = od;
    }

    public String getAmt_due() {
        return amt_due;
    }

    public void setAmt_due(String amt_due) {
        this.amt_due = amt_due;
    }

    public String getInst_no() {
        return inst_no;
    }

    public void setInst_no(String inst_no) {
        this.inst_no = inst_no;
    }

    public String getUuid_task_update() {
        return uuid_task_update;
    }

    public void setUuid_task_update(String uuid_task_update) {
        this.uuid_task_update = uuid_task_update;
    }

    public String getPending_notes() {
        return pending_notes;
    }

    public void setPending_notes(String pending_notes) {
        this.pending_notes = pending_notes;
    }

    public String getDocupro_feedback() {
        return docupro_feedback;
    }

    public void setDocupro_feedback(String docupro_feedback) {
        this.docupro_feedback = docupro_feedback;
    }

    public String getStatus_application() {
        return status_application;
    }

    public void setStatus_application(String status_application) {
        this.status_application = status_application;
    }

    public Integer getIs_sent_confins() {
        return is_sent_confins;
    }

    public void setIs_sent_confins(Integer is_sent_confins) {
        this.is_sent_confins = is_sent_confins;
    }

    public Integer getIs_already_notified() {
        return is_already_notified;
    }

    public void setIs_already_notified(Integer is_already_notified) {
        this.is_already_notified = is_already_notified;
    }

    public String getKelurahan() {
        return kelurahan;
    }

    public void setKelurahan(String kelurahan) {
        this.kelurahan = kelurahan;
    }

    public String getStatus_followup() {
        return status_followup;
    }

    public void setStatus_followup(String status_followup) {
        this.status_followup = status_followup;
    }

    public String getIs_revisit() {
        return is_revisit;
    }

    public void setIs_revisit(String is_revisit) {
        this.is_revisit = is_revisit;
    }

    public String getVisit_type() {
        return visit_type;
    }

    public void setVisit_type(String visit_type) {
        this.visit_type = visit_type;
    }

    public String getSend_task_promise_to_survey() {
        return send_task_promise_to_survey;
    }

    public void setSend_task_promise_to_survey(String send_task_promise_to_survey) {
        this.send_task_promise_to_survey = send_task_promise_to_survey;
    }

    public String getSend_task_presurvey() {
        return send_task_presurvey;
    }

    public void setSend_task_presurvey(String send_task_presurvey) {
        this.send_task_presurvey = send_task_presurvey;
    }

    public String getSend_task_survey() {
        return send_task_survey;
    }

    public void setSend_task_survey(String send_task_survey) {
        this.send_task_survey = send_task_survey;
    }

    public String getIs_piloting_cae() {
        return is_piloting_cae;
    }

    public void setIs_piloting_cae(String is_piloting_cae) {
        this.is_piloting_cae = is_piloting_cae;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getSub_category() {
        return sub_category;
    }

    public void setSub_category(String sub_category) {
        this.sub_category = sub_category;
    }

    public String getReason_detail() {
        return reason_detail;
    }

    public void setReason_detail(String reason_detail) {
        this.reason_detail = reason_detail;
    }

    public String getValidasi() {
        return validasi;
    }

    public void setValidasi(String validasi) {
        this.validasi = validasi;
    }

    public String getNotes_crm() {
        return notes_crm;
    }

    public void setNotes_crm(String notes_crm) {
        this.notes_crm = notes_crm;
    }

    public Integer getIs_pre_approval() {
        return is_pre_approval;
    }

    public void setIs_pre_approval(Integer is_pre_approval) {
        this.is_pre_approval = is_pre_approval;
    }

    public String getSource_data() {
        return source_data;
    }

    public void setSource_data(String source_data) {
        this.source_data = source_data;
    }

    public String getIs_already_download_task() {
        return is_already_download_task;
    }

    public void setIs_already_download_task(String is_already_download_task) {
        this.is_already_download_task = is_already_download_task;
    }

    public String getProduct_name() {
        return product_name;
    }

    public void setProduct_name(String product_name) {
        this.product_name = product_name;
    }

    public String getJenis_asset() {
        return jenis_asset;
    }

    public void setJenis_asset(String jenis_asset) {
        this.jenis_asset = jenis_asset;
    }

    /** To-one relationship, resolved on first access. */
    public User getUser() {
        String __key = this.uuid_user;
        if (user__resolvedKey == null || user__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            UserDao targetDao = daoSession.getUserDao();
            User userNew = targetDao.load(__key);
            synchronized (this) {
                user = userNew;
            	user__resolvedKey = __key;
            }
        }
        return user;
    }

    public void setUser(User user) {
        synchronized (this) {
            this.user = user;
            uuid_user = user == null ? null : user.getUuid_user();
            user__resolvedKey = uuid_user;
        }
    }

    /** To-one relationship, resolved on first access. */
    public Scheme getScheme() {
        String __key = this.uuid_scheme;
        if (scheme__resolvedKey == null || scheme__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            SchemeDao targetDao = daoSession.getSchemeDao();
            Scheme schemeNew = targetDao.load(__key);
            synchronized (this) {
                scheme = schemeNew;
            	scheme__resolvedKey = __key;
            }
        }
        return scheme;
    }

    public void setScheme(Scheme scheme) {
        synchronized (this) {
            this.scheme = scheme;
            uuid_scheme = scheme == null ? null : scheme.getUuid_scheme();
            scheme__resolvedKey = uuid_scheme;
        }
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<ImageResult> getImageResultList() {
        if (imageResultList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            ImageResultDao targetDao = daoSession.getImageResultDao();
            List<ImageResult> imageResultListNew = targetDao._queryTaskH_ImageResultList(uuid_task_h);
            synchronized (this) {
                if(imageResultList == null) {
                    imageResultList = imageResultListNew;
                }
            }
        }
        return imageResultList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetImageResultList() {
        imageResultList = null;
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<ReceiptVoucher> getReceiptVoucherList() {
        if (receiptVoucherList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            ReceiptVoucherDao targetDao = daoSession.getReceiptVoucherDao();
            List<ReceiptVoucher> receiptVoucherListNew = targetDao._queryTaskH_ReceiptVoucherList(uuid_task_h);
            synchronized (this) {
                if(receiptVoucherList == null) {
                    receiptVoucherList = receiptVoucherListNew;
                }
            }
        }
        return receiptVoucherList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetReceiptVoucherList() {
        receiptVoucherList = null;
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<TaskD> getTaskDList() {
        if (taskDList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            TaskDDao targetDao = daoSession.getTaskDDao();
            List<TaskD> taskDListNew = targetDao._queryTaskH_TaskDList(uuid_task_h);
            synchronized (this) {
                if(taskDList == null) {
                    taskDList = taskDListNew;
                }
            }
        }
        return taskDList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetTaskDList() {
        taskDList = null;
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<ErrorLog> getErrorLogList() {
        if (errorLogList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            ErrorLogDao targetDao = daoSession.getErrorLogDao();
            List<ErrorLog> errorLogListNew = targetDao._queryTaskH_ErrorLogList(uuid_task_h);
            synchronized (this) {
                if(errorLogList == null) {
                    errorLogList = errorLogListNew;
                }
            }
        }
        return errorLogList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetErrorLogList() {
        errorLogList = null;
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<Timeline> getTimelineList() {
        if (timelineList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            TimelineDao targetDao = daoSession.getTimelineDao();
            List<Timeline> timelineListNew = targetDao._queryTaskH_TimelineList(uuid_task_h);
            synchronized (this) {
                if(timelineList == null) {
                    timelineList = timelineListNew;
                }
            }
        }
        return timelineList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetTimelineList() {
        timelineList = null;
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<TaskHSequence> getTaskHSequenceList() {
        if (taskHSequenceList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            TaskHSequenceDao targetDao = daoSession.getTaskHSequenceDao();
            List<TaskHSequence> taskHSequenceListNew = targetDao._queryTaskH_TaskHSequenceList(uuid_task_h);
            synchronized (this) {
                if(taskHSequenceList == null) {
                    taskHSequenceList = taskHSequenceListNew;
                }
            }
        }
        return taskHSequenceList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetTaskHSequenceList() {
        taskHSequenceList = null;
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<ReminderPo> getReminderPoList() {
        if (reminderPoList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            ReminderPoDao targetDao = daoSession.getReminderPoDao();
            List<ReminderPo> reminderPoListNew = targetDao._queryTaskH_ReminderPoList(uuid_task_h);
            synchronized (this) {
                if(reminderPoList == null) {
                    reminderPoList = reminderPoListNew;
                }
            }
        }
        return reminderPoList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetReminderPoList() {
        reminderPoList = null;
    }

    /** Convenient call for {@link AbstractDao#delete(Object)}. Entity must attached to an entity context. */
    public void delete() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.delete(this);
    }

    /** Convenient call for {@link AbstractDao#update(Object)}. Entity must attached to an entity context. */
    public void update() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.update(this);
    }

    /** Convenient call for {@link AbstractDao#refresh(Object)}. Entity must attached to an entity context. */
    public void refresh() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.refresh(this);
    }

}
