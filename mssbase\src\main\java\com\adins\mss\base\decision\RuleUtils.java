package com.adins.mss.base.decision;


public class RuleUtils {
	public static boolean isExpressionMethod(String exp) {
		if (exp == null || "".equals(exp.trim()))
			return false;
		
		return exp.contains("()");
	}
	
	/**
	 * <p>Concat {@code objName}, {@code objFieldMethod} and {@code val} into an expression.
	 * Numbers support the use of the negative sign, exponential notation, and decimal points.
	 * By default real numbers are parsed by Spring using Double.parseDouble().
	 * 
	 * <p>By default SpEL uses the conversion service available in Spring core
	 * (org.springframework.core.convert.ConversionService).
	 * This conversion service comes with many converters built in for common conversions
	 * but is also fully extensible so custom conversions between types can be added.
	 * Additionally it has the key capability that it is generics aware.
	 * This means that when working with generic types in expressions,
	 * SpEL will attempt conversions to maintain type correctness for any objects it encounters.
	 * 
	 * <p>Example:
	 * <pre>
	 *  objName | objFieldMethod | val   | expression
	 *  restBean| sayHello()     |       | restBean.sayHello()
	 *  restBean| sayHello()     | World | restBean.sayHello('World')
	 *  restBean| plusOne()      | 4     | restBean.plusOne(4.0)
	 *  restBean| field1         | Hello | restBean.field1 = 'Hello'
	 *  restBean| field2         | 4     | restBean.field2 = 4.0
	 *  varName |                | 4     | varName = 4.0
	 *  map     | ['key1']       | MR    | map['key1'] = 'MR' 
	 * <pre>
	 * 
	 * @param objName
	 * @param objFieldMethod
	 * @param val
	 * @return
	 */
	public static String createExpression(String objName, String objFieldMethod, String val) {
		if (objName == null || "".equals(objName.trim()))
			throw new IllegalArgumentException("'objName' must not empty");
		
		boolean isMethod = isExpressionMethod(objFieldMethod);
		
		if (isMethod) {
			StringBuilder sb = new StringBuilder(objName).append('.');
			sb.append(objFieldMethod);
			
			if (val == null || "".equals(val.trim()))
				return sb.toString(); //objName.ObjFieldMethod()
			
			int idxArg = sb.indexOf(")");
			sb.insert(idxArg, toLiteral(val));
			return sb.toString(); //objName.ObjFieldMethod(val)
		}
		else {
			if (objFieldMethod == null || "".equals(objFieldMethod.trim())) {
				//(!!!SpEL cannot assign to java.lang.String variables [no effect])
				return objName + " = " + toLiteral(val); //objName = val
			}
			
			boolean isMap = isMap(objFieldMethod);
			if (isMap) {
				 StringBuilder sb = new StringBuilder();
				 sb.append(objName).append(objFieldMethod).append(" = ").append(toLiteral(val));
				 return sb.toString(); //objNameObjFieldMethod = val
			}
			else {
				 StringBuilder sb = new StringBuilder();
				 sb.append(objName).append(".").append(objFieldMethod).append(" = ").append(toLiteral(val));
				 return sb.toString(); //objName.ObjFieldMethod = val
			}
		}
	}
	
	/**
	 * <p>Converting value into SpEL value.
	 * 
	 * <pre>
	 * 	RuleUtils.toLiteral(null) 	= "null"
	 *  RuleUtils.toLiteral("1234")	= "1234"
	 *  RuleUtils.toLiteral("TRUE")	= "true"
	 *  RuleUtils.toLiteral("FALSE")= "false"
	 *  RuleUtils.toLiteral("Hello")= "'Hello'"
	 * </pre>
	 * @param str
	 * @return
	 * 
	 */
	public static String toLiteral(String str) {
		if (isNumber(str)) {
			return str;
		}
		else {
			//for null value
			if (str == null || "null".equals(str))
				return "null";
			
			//for boolean value
			Boolean bol = toBooleanObject(str);
			if (bol != null)
				return str.toLowerCase();
						
			//for string value
			return "'" + str + "'";
		}
	}
	
	public static boolean isMap(String field) {
		if (field == null)
			return false;
		
		return field.startsWith("[") && field.endsWith("]");
	}

    /**
     * <p>Checks whether the <code>String</code> contains only
     * digit characters.</p>
     *
     * <p><code>Null</code> and empty String will return
     * <code>false</code>.</p>
     *
     * @param str  the <code>String</code> to check
     * @return <code>true</code> if str contains only Unicode numeric
     */
	public static boolean isDigits(String str) {
        if (str == null || "".equals(str.trim())) {
            return false;
        }
        for (int i = 0; i < str.length(); i++) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }
	
    /**
     * <p>Checks whether the String a valid Java number.</p>
     *
     * <p>Valid numbers include hexadecimal marked with the <code>0x</code>
     * qualifier, scientific notation and numbers marked with a type
     * qualifier (e.g. 123L).</p>
     *
     * <p><code>Null</code> and empty String will return
     * <code>false</code>.</p>
     *
     * @param str  the <code>String</code> to check
     * @return <code>true</code> if the string is a correctly formatted number
     */
    public static boolean isNumber(String str) {
        if (str == null || "".equals(str.trim())) {
            return false;
        }
        char[] chars = str.toCharArray();
        int sz = chars.length;
        boolean hasExp = false;
        boolean hasDecPoint = false;
        boolean allowSigns = false;
        boolean foundDigit = false;
        // deal with any possible sign up front
        int start = (chars[0] == '-') ? 1 : 0;
        if (sz > start + 1 && chars[start] == '0' && chars[start + 1] == 'x') {
            int i = start + 2;
            if (i == sz) {
                return false; // str == "0x"
            }
            // checking hex (it can't be anything else)
            for (; i < chars.length; i++) {
                if ((chars[i] < '0' || chars[i] > '9')
                    && (chars[i] < 'a' || chars[i] > 'f')
                    && (chars[i] < 'A' || chars[i] > 'F')) {
                    return false;
                }
            }
            return true;
        }
        sz--; // don't want to loop to the last char, check it afterwords
              // for type qualifiers
        int i = start;
        // loop to the next to last char or to the last char if we need another digit to
        // make a valid number (e.g. chars[0..5] = "1234E")
        while (i < sz || (i < sz + 1 && allowSigns && !foundDigit)) {
            if (chars[i] >= '0' && chars[i] <= '9') {
                foundDigit = true;
                allowSigns = false;

            } else if (chars[i] == '.') {
                if (hasDecPoint || hasExp) {
                    // two decimal points or dec in exponent   
                    return false;
                }
                hasDecPoint = true;
            } else if (chars[i] == 'e' || chars[i] == 'E') {
                // we've already taken care of hex.
                if (hasExp) {
                    // two E's
                    return false;
                }
                if (!foundDigit) {
                    return false;
                }
                hasExp = true;
                allowSigns = true;
            } else if (chars[i] == '+' || chars[i] == '-') {
                if (!allowSigns) {
                    return false;
                }
                allowSigns = false;
                foundDigit = false; // we need a digit after the E
            } else {
                return false;
            }
            i++;
        }
        if (i < chars.length) {
            if (chars[i] >= '0' && chars[i] <= '9') {
                // no type qualifier, OK
                return true;
            }
            if (chars[i] == 'e' || chars[i] == 'E') {
                // can't have an E at the last byte
                return false;
            }
            if (chars[i] == '.') {
                if (hasDecPoint || hasExp) {
                    // two decimal points or dec in exponent
                    return false;
                }
                // single trailing decimal point after non-exponent is ok
                return foundDigit;
            }
            if (!allowSigns
                && (chars[i] == 'd'
                    || chars[i] == 'D'
                    || chars[i] == 'f'
                    || chars[i] == 'F')) {
                return foundDigit;
            }
            if (chars[i] == 'l'
                || chars[i] == 'L') {
                // not allowing L with an exponent or decimal point
                return foundDigit && !hasExp && !hasDecPoint;
            }
            // last character is illegal
            return false;
        }
        // allowSigns is true iff the val ends in 'E'
        // found digit it to make sure weird stuff like '.' and '1E-' doesn't pass
        return !allowSigns && foundDigit;
    }
	
    /**
     * <p>Converts a String to a Boolean.</p>
     *
     * <p>{@code 'true'}
     * (case insensitive) will return {@code true}.
     * {@code 'false'}
     * (case insensitive) will return {@code false}.
     * Otherwise, {@code null} is returned.</p>
     *
     * <p>NOTE: This returns null and will throw a NullPointerException if autoboxed to a boolean. </p>
     *
     * <pre>
     *   BooleanUtils.toBooleanObject(null)    = null
     *   BooleanUtils.toBooleanObject("true")  = Boolean.TRUE
     *   BooleanUtils.toBooleanObject("false") = Boolean.FALSE
     *   BooleanUtils.toBooleanObject("blue")  = null
     * </pre>
     *
     * @param str  the String to check
     * @return the Boolean value of the string, {@code null} if no match or {@code null} input
     */
    public static Boolean toBooleanObject(String str) {
        // Previously used equalsIgnoreCase, which was fast for interned 'true'.
        // Non interned 'true' matched 15 times slower.
        //
        // Optimisation provides same performance as before for interned 'true'.
        // Similar performance for null, 'false', and other strings not length 2/3/4.
        // 'true'/'TRUE' match 4 times slower, 'tRUE'/'True' 7 times slower.
        if (str == "true") {
            return Boolean.TRUE;
        }
        if (str == null) {
            return null;
        }
        switch (str.length()) {
            case 4: {
                char ch0 = str.charAt(0);
                char ch1 = str.charAt(1);
                char ch2 = str.charAt(2);
                char ch3 = str.charAt(3);
                if ((ch0 == 't' || ch0 == 'T') &&
                    (ch1 == 'r' || ch1 == 'R') &&
                    (ch2 == 'u' || ch2 == 'U') &&
                    (ch3 == 'e' || ch3 == 'E') ) {
                    return Boolean.TRUE;
                }
                break;
            }
            case 5: {
                char ch0 = str.charAt(0);
                char ch1 = str.charAt(1);
                char ch2 = str.charAt(2);
                char ch3 = str.charAt(3);
                char ch4 = str.charAt(4);
                if ((ch0 == 'f' || ch0 == 'F') &&
                    (ch1 == 'a' || ch1 == 'A') &&
                    (ch2 == 'l' || ch2 == 'L') &&
                    (ch3 == 's' || ch3 == 'S') &&
                    (ch4 == 'e' || ch4 == 'E') ) {
                    return Boolean.FALSE;
                }
                break;
            }
        }

        return null;
    }
}
