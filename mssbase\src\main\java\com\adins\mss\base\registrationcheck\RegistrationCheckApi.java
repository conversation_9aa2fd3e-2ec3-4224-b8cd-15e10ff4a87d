package com.adins.mss.base.registrationcheck;

import android.content.Context;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.http.MssResponseType;

public class RegistrationCheckApi {

    private Context context;

    public RegistrationCheckApi(Context context) {
        this.context = context;
    }

    public MssResponseType request(JsonRequestRegistrationCheck jsonRequestCheckRegistration) {

        String url = GlobalData.getSharedGlobalData().getURL_REGISTRATION_CHECK();
        String requestJson = GsonHelper.toJson(jsonRequestCheckRegistration);

        boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
        boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
        HttpCryptedConnection httpCryptedConnection = new HttpCryptedConnection(context, encrypt, decrypt);
        HttpConnectionResult serverResult = null;
        try {
            serverResult = httpCryptedConnection.requestToServer(url, requestJson, Global.SORTCONNECTIONTIMEOUT);
        } catch (Exception e) {
            e.printStackTrace();
        }
        MssResponseType response = null;
        String responseJson;
        if (null != serverResult && serverResult.isOK()) {
            try {
                responseJson = serverResult.getResult();
                response = GsonHelper.fromJson(responseJson, MssResponseType.class);
            } catch (Exception e) {
                return null;
            }
        } else {
            response = new MssResponseType();
            MssResponseType.Status status = new MssResponseType.Status();
            status.setMessage(serverResult.getResult());
            status.setCode(serverResult.getStatusCode());
            response.setStatus(status);
        }
        return response;
    }
}
