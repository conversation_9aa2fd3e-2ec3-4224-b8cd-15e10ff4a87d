package com.adins.mss.base.decision;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 20/02/18.
 */

public class PriceResult {
    public static String template = "{\"file\":\"OTR\",\"keyvalues\":[{\"id\":\"SupplBranch.SupplBranchCode\",\"type\":\"java.lang.String\",\"value\":\"{SVY_NAMA_DEALER}\"},{\"id\":\"AssetHierarchyL1.AssetHierarchyL1Code\",\"type\":\"java.lang.String\",\"value\":\"{SVY_BRAND}\"},{\"id\":\"AssetHierarchyL2.AssetHierarchyL2Code\",\"type\":\"java.lang.String\",\"value\":\"{SVY_MODEL}\"},{\"id\":\"AssetCode\",\"type\":\"java.lang.String\",\"value\":\"{SVY_TYPE}\"},{\"id\":\"ManufacturingYear\",\"type\":\"java.lang.Integer\",\"value\":\"{SVY_TAHUN_KENDARAAN}\"},{\"id\":\"AgrmntAsset.MrAssetCondition\",\"type\":\"java.lang.String\",\"value\":\"{ref_id}\"}]}";

    private Double basePrice = 0d;
    private String behaviour;
    private Integer tolerance;

    public Double getBasePrice() {
        return basePrice;
    }

    public void setBasePrice(Double basePrice) {
        this.basePrice = basePrice;
    }

    public String getBehaviour() {
        return behaviour;
    }

    public void setBehaviour(String behaviour) {
        this.behaviour = behaviour;
    }

    public Integer getTolerance() {
        return tolerance;
    }

    public void setTolerance(Integer tolerance) {
        this.tolerance = tolerance;
    }

    public void AddListOTRAmt(double basePrice) {
        this.basePrice = basePrice;
    }

    public void AddListBehaviour(String behaviour) {
        this.behaviour = behaviour;
    }

    public void AddListTolerancePrctg(int tolerance) {
        this.tolerance = tolerance;
    }
}
