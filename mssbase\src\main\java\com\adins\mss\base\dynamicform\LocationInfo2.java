package com.adins.mss.base.dynamicform;

import com.adins.mss.dao.LocationInfo;

public class LocationInfo2 extends LocationInfo implements Cloneable {


    public LocationInfo2(LocationInfo info) {
        this.setUuid_location_info(info.getUuid_location_info());
        this.setAccuracy(info.getAccuracy());
        this.setCid(info.getCid());
        this.setDtm_crt(info.getDtm_crt());
        this.setGps_time(info.getGps_time());
        this.setHandset_time(info.getHandset_time());
        this.setIs_gps_time(info.getIs_gps_time());
        this.setLac(info.getLac());
        this.setLatitude(info.getLatitude());
        this.setLocation_type(info.getLocation_type());
        this.setLongitude(info.getLongitude());
        this.setMcc(info.getMcc());
        this.setMnc(info.getMnc());
        this.setMode(info.getMode());
        this.setUser(info.getUser());
        this.setUsr_crt(info.getUsr_crt());
        this.setUuid_user(info.getUuid_user());
    }

    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
//			LocationInfo2 cloned = (LocationInfo2)super.clone();
//	      // the above is applicable in case of primitive member types, 
//	      // however, in case of non primitive types
//	      // cloned.setNonPrimitiveType(cloned.getNonPrimitiveType().clone());
//	      return cloned;
    }
}
