package com.adins.mss.dao;

import java.util.List;
import java.util.ArrayList;
import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.SqlUtils;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;
import de.greenrobot.dao.query.Query;
import de.greenrobot.dao.query.QueryBuilder;

import com.adins.mss.dao.TaskHSequence;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TR_TASK_H_SEQUENCE".
*/
public class TaskHSequenceDao extends AbstractDao<TaskHSequence, Void> {

    public static final String TABLENAME = "TR_TASK_H_SEQUENCE";

    /**
     * Properties of entity TaskHSequence.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Sequence = new Property(0, int.class, "sequence", false, "SEQUENCE");
        public final static Property Uuid_task_h = new Property(1, String.class, "uuid_task_h", false, "UUID_TASK_H");
    };

    private DaoSession daoSession;

    private Query<TaskHSequence> taskH_TaskHSequenceListQuery;

    public TaskHSequenceDao(DaoConfig config) {
        super(config);
    }
    
    public TaskHSequenceDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TR_TASK_H_SEQUENCE\" (" + //
                "\"SEQUENCE\" INTEGER NOT NULL ," + // 0: sequence
                "\"UUID_TASK_H\" TEXT);"); // 1: uuid_task_h
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TR_TASK_H_SEQUENCE\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, TaskHSequence entity) {
        stmt.clearBindings();
        stmt.bindLong(1, entity.getSequence());
 
        String uuid_task_h = entity.getUuid_task_h();
        if (uuid_task_h != null) {
            stmt.bindString(2, uuid_task_h);
        }
    }

    @Override
    protected void attachEntity(TaskHSequence entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    /** @inheritdoc */
    @Override
    public Void readKey(Cursor cursor, int offset) {
        return null;
    }    

    /** @inheritdoc */
    @Override
    public TaskHSequence readEntity(Cursor cursor, int offset) {
        TaskHSequence entity = new TaskHSequence( //
            cursor.getInt(offset + 0), // sequence
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1) // uuid_task_h
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, TaskHSequence entity, int offset) {
        entity.setSequence(cursor.getInt(offset + 0));
        entity.setUuid_task_h(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
     }
    
    /** @inheritdoc */
    @Override
    protected Void updateKeyAfterInsert(TaskHSequence entity, long rowId) {
        // Unsupported or missing PK type
        return null;
    }
    
    /** @inheritdoc */
    @Override
    public Void getKey(TaskHSequence entity) {
        return null;
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
    /** Internal query to resolve the "taskHSequenceList" to-many relationship of TaskH. */
    public List<TaskHSequence> _queryTaskH_TaskHSequenceList(String uuid_task_h) {
        synchronized (this) {
            if (taskH_TaskHSequenceListQuery == null) {
                QueryBuilder<TaskHSequence> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_task_h.eq(null));
                taskH_TaskHSequenceListQuery = queryBuilder.build();
            }
        }
        Query<TaskHSequence> query = taskH_TaskHSequenceListQuery.forCurrentThread();
        query.setParameter(0, uuid_task_h);
        return query.list();
    }

    private String selectDeep;

    protected String getSelectDeep() {
        if (selectDeep == null) {
            StringBuilder builder = new StringBuilder("SELECT ");
            SqlUtils.appendColumns(builder, "T", getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T0", daoSession.getTaskHDao().getAllColumns());
            builder.append(" FROM TR_TASK_H_SEQUENCE T");
            builder.append(" LEFT JOIN TR_TASK_H T0 ON T.\"UUID_TASK_H\"=T0.\"UUID_TASK_H\"");
            builder.append(' ');
            selectDeep = builder.toString();
        }
        return selectDeep;
    }
    
    protected TaskHSequence loadCurrentDeep(Cursor cursor, boolean lock) {
        TaskHSequence entity = loadCurrent(cursor, 0, lock);
        int offset = getAllColumns().length;

        TaskH taskH = loadCurrentOther(daoSession.getTaskHDao(), cursor, offset);
        entity.setTaskH(taskH);

        return entity;    
    }

    public TaskHSequence loadDeep(Long key) {
        assertSinglePk();
        if (key == null) {
            return null;
        }

        StringBuilder builder = new StringBuilder(getSelectDeep());
        builder.append("WHERE ");
        SqlUtils.appendColumnsEqValue(builder, "T", getPkColumns());
        String sql = builder.toString();
        
        String[] keyArray = new String[] { key.toString() };
        Cursor cursor = db.rawQuery(sql, keyArray);
        
        try {
            boolean available = cursor.moveToFirst();
            if (!available) {
                return null;
            } else if (!cursor.isLast()) {
                throw new IllegalStateException("Expected unique result, but count was " + cursor.getCount());
            }
            return loadCurrentDeep(cursor, true);
        } finally {
            cursor.close();
        }
    }
    
    /** Reads all available rows from the given cursor and returns a list of new ImageTO objects. */
    public List<TaskHSequence> loadAllDeepFromCursor(Cursor cursor) {
        int count = cursor.getCount();
        List<TaskHSequence> list = new ArrayList<TaskHSequence>(count);
        
        if (cursor.moveToFirst()) {
            if (identityScope != null) {
                identityScope.lock();
                identityScope.reserveRoom(count);
            }
            try {
                do {
                    list.add(loadCurrentDeep(cursor, false));
                } while (cursor.moveToNext());
            } finally {
                if (identityScope != null) {
                    identityScope.unlock();
                }
            }
        }
        return list;
    }
    
    protected List<TaskHSequence> loadDeepAllAndCloseCursor(Cursor cursor) {
        try {
            return loadAllDeepFromCursor(cursor);
        } finally {
            cursor.close();
        }
    }
    

    /** A raw-style query where you can pass any WHERE clause and arguments. */
    public List<TaskHSequence> queryDeep(String where, String... selectionArg) {
        Cursor cursor = db.rawQuery(getSelectDeep() + where, selectionArg);
        return loadDeepAllAndCloseCursor(cursor);
    }
 
}
