package com.adins.mss.base.log;

import android.app.Activity;
import android.content.Context;

import com.adins.mss.base.GlobalData;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.Lookup;
import com.adins.mss.dao.PrintItem;
import com.adins.mss.dao.PrintResult;
import com.adins.mss.dao.QuestionSet;
import com.adins.mss.dao.ReminderPo;
import com.adins.mss.dao.TaskD;
import com.adins.mss.dao.TaskH;
import com.adins.mss.foundation.db.dataaccess.LookupDataAccess;
import com.adins.mss.foundation.db.dataaccess.PrintItemDataAccess;
import com.adins.mss.foundation.db.dataaccess.QuestionSetDataAccess;
import com.adins.mss.foundation.db.dataaccess.ReminderPoDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskDDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskHDataAccess;
import com.adins.mss.foundation.formatter.Formatter;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.questiongenerator.OptionAnswerBean;
import com.adins.mss.foundation.questiongenerator.QuestionBean;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class Log {
    public static final int SECOND = 1000;
    public static final int MINUTE = 60 * SECOND;
    public static final int HOUR = 60 * MINUTE;
    public static final int DAY = 24 * HOUR;
    public static final int IMAGE_ONLY = 1;
    public static final int NON_IMAGE_ONLY = 2;
    public static final int ALL_TASK = 3;
    public static final String AT_LOV = "015";
    public static final String AT_LOV_W_FILTER = "016";
    private static final int MAXIMUM_SIZE_KEEP = 30;
    private static final int MAXIMUM_DAYS_KEEP = 1 * DAY;
    /* PRINT ITEM TYPE */
    public static String PRINT_NO_ANSWER = "001";
    public static String PRINT_ANSWER = "002";
    //Glen 9 Aug 2014, new type : timestamp
    public static String PRINT_TIMESTAMP = "004";
    // bong Oct 28th, 2014 - adding from fif
    public static String PRINT_LOGO = "005";
    public static String PRINT_USER = "006";
    public static String PRINT_LABEL_CENTER = "007";
    public static String PRINT_LABEL_CENTER_BOLD = "008";
    public static String PRINT_PRINTER_ID = "003";
    private static List<TaskH> listTaskH;
    private static List<TaskD> listTaskD;
    private Context context;
    private String userId;

    public Log(Context context) {
        this.context = context;
        userId = GlobalData.getSharedGlobalData().getUser().getUuid_user();
    }

    public List<TaskH> getAllSentTask() {
        listTaskH = TaskHDataAccess.getAllSentTask(context, userId);
        int listSize = listTaskH.size();
        List<TaskH> listTaskHDelete = new ArrayList<TaskH>();
        if (listSize > 0) {
            int flag = MAXIMUM_SIZE_KEEP;
            switch (flag) {
                case MAXIMUM_DAYS_KEEP: {
                    Date sysdate = Tool.getSystemDateTime();
                    long batasDel = sysdate.getTime()
                            - MAXIMUM_DAYS_KEEP;
                    listTaskHDelete = TaskHDataAccess.getAllDeleteTask(context, userId, String.valueOf(batasDel));
                    TaskHDataAccess.deleteListWithRelation(context, listTaskHDelete);
                }
                break;
                case MAXIMUM_SIZE_KEEP: {
                    if (listSize > MAXIMUM_SIZE_KEEP) {
                        for (int i = MAXIMUM_SIZE_KEEP; i <= listSize; i++) {
                            listTaskHDelete.add(listTaskH.get(i));
                        }
                        TaskHDataAccess.deleteListWithRelation(context, listTaskHDelete);
                    }
                }
                break;
            }
        }
        return listTaskH = TaskHDataAccess.getAllSentTask(context, userId);
    }

    public List<TaskH> getAllSentTaskWithLimited() {
        List<TaskH> taskHList = new ArrayList<>();
        listTaskH = TaskHDataAccess.getAllSentTask(context, userId);
        int listSize = listTaskH.size();
        List<TaskH> listTaskHDelete = new ArrayList<TaskH>();
        List<TaskH> listTaskPoDelete = new ArrayList<>();
        if (listSize > 0) {
            // Adding the number of max task log limit for reminder po task (2022-08-15)
            for (TaskH taskH : listTaskH) {
                ReminderPo taskPo = ReminderPoDataAccess.getOneTaskReminderPo(context, taskH.getUuid_task_h(), Global.TRUE_STRING);
                if (null != taskPo) {
                    String taskCreateDate = Formatter.formatDate(taskPo.getDtm_crt(), Global.DATE_STR_FORMAT2);
                    String currentDate = Formatter.formatDate(new Date(), Global.DATE_STR_FORMAT2);
                    boolean isCurrentTask = taskCreateDate.compareToIgnoreCase(currentDate) == 0;
                    if (isCurrentTask) {
                        taskHList.add(taskH);
                        listTaskPoDelete.add(taskH);
                    }
                }
            }
            // End adding the number of max task log limit for reminder po task
            int maximumDataKeep = GlobalData.getSharedGlobalData().getMaxDataInLog();
            if (maximumDataKeep != 0 && listSize > maximumDataKeep) {
                for (int i = maximumDataKeep; i < listSize; i++) {
                    if(!taskHList.contains(listTaskH.get(i))) {
                        listTaskHDelete.add(listTaskH.get(i));
                    }
                }
                TaskHDataAccess
                        .deleteListWithRelation(context, listTaskHDelete);
            }
        }
        listTaskH = TaskHDataAccess.getAllSentTask(context, userId);
        listTaskH.removeAll(listTaskPoDelete);
        if (null != listTaskH && !listTaskH.isEmpty()) {
            taskHList.addAll(listTaskH);
        }
        return taskHList;
    }

    /**
     * This is used to get all task detail from a taskId
     *
     * @param taskId
     * @param withImage - IMAGE_ONLY = 1, NON_IMAGE_ONLY = 2, ALL_TASK = 3
     * @return
     */
    public List<TaskD> getTaskD(String taskId, int withImage) {
        return listTaskD = TaskDDataAccess.getAllByTaskId(context, userId, taskId, withImage);
    }

    /**
     * Get question set of a taskId
     *
     * @param taskId
     * @return
     */
    public List<QuestionSet> getListQuestionSet(String taskId) {
        TaskH taskH = TaskHDataAccess.getOneTaskHeader(context, taskId);
        return QuestionSetDataAccess.getAllByFormVersion(context, taskH.getUuid_scheme(), taskH.getForm_version());
    }

    /**
     * Close this activity
     *
     * @param activity
     */
    public void close(Activity activity) {
        activity.finish();
    }

    /**
     * Refresh list of sent task
     */
    public void doRefreshListSentTask() {
        listTaskH = getAllSentTask();
    }


    public List<PrintResult> getReadyPrintItem(Context context, String taskId) {
        TaskH taskH = TaskHDataAccess.getOneTaskHeader(context, taskId);
        List<PrintItem> listPrintItem = PrintItemDataAccess.getAll(context, taskH.getScheme().getUuid_scheme());
        List<QuestionSet> listQuestionSet = QuestionSetDataAccess.getAll(context, taskH.getScheme().getUuid_scheme());
        List<TaskD> listTaskD = TaskDDataAccess.getAllByTaskId(context, userId, taskId, ALL_TASK);
//		List<PrintResult> listPrintResult = this.makePrintResult(listPrintItem, listQuestionSet,listTaskD);
        List<PrintResult> listPrintResult = null;

        List<QuestionBean> listQuestionBean = new ArrayList<QuestionBean>();
        for (QuestionSet qs : listQuestionSet)
            listQuestionBean.add((QuestionBean) qs);
        this.matchAnswerToQuestion(listQuestionBean, listTaskD);
        listPrintResult = checkNeedToPrint(listPrintItem, listQuestionBean, taskH);

        listQuestionBean = loadSelectedOptionForQuestionBean(listQuestionBean);
        if (listPrintResult == null || listQuestionBean == null) return null;

        this.matchPrintItemWithAnswer(listPrintResult, listQuestionBean);
        return listPrintResult;
    }


    private List<PrintResult> checkNeedToPrint(List<PrintItem> listPrintItem,
                                               List<QuestionBean> listQuestionBean, TaskH taskH) {
        List<PrintResult> result = new ArrayList<PrintResult>();

        for (PrintItem printItem : listPrintItem) {
            String qgid = printItem.getQuestion_group_id();
            String qid = printItem.getQuestion_id();
            boolean shouldSkipPrintItem = false;
            String value = "";

            for (QuestionBean qBean : listQuestionBean) {
                if (qBean.getQuestion_group_id() == qgid && qBean.getQuestion_id() == qid) {
                    boolean isVisible = true;
                    value = qBean.getAnswer();
                    String relevant = qBean.getRelevant_question();

                    if (relevant != null && relevant.length() > 0) {
                        isVisible = Tool.isVisibleByRelevant(relevant, qBean, listQuestionBean);
                    }
                    shouldSkipPrintItem = isVisible;
                    break;
                }
            }
            if (!shouldSkipPrintItem) {
                PrintResult pr = new PrintResult();
                pr.setValue(value);
                pr.setLabel(printItem.getPrint_item_label());
                pr.setUuid_task_h(taskH.getUuid_task_h());
                pr.setPrint_type_id(printItem.getPrint_type_id());
                result.add(pr);
            }
        }
        return result;
    }

    private void matchAnswerToQuestion(List<QuestionBean> listQuestionBean,
                                       List<TaskD> listTaskD) {
        if (listQuestionBean == null || listTaskD == null)
            return;
        // make all question unvisible first
        for (QuestionSet questionSet : listQuestionBean)
            questionSet.setIs_visible(Global.FALSE_STRING);

        for (TaskD taskD : listTaskD) {
            String qgid = taskD.getQuestion_group_id();
            String qid = taskD.getQuestion_id();
            String textAnswer = taskD.getText_answer();
            qLoop:
            for (QuestionBean qb : listQuestionBean) {
                if (qgid == qb.getQuestion_group_id() && qid == qb.getQuestion_id()) {
                    // make it visible
                    qb.setIs_visible(Global.TRUE_STRING);

                    String answerType = qb.getAnswer_type();
                    if (Tool.isOptions(answerType)) {
                        String optId;
                        if (qb.getTag() != null && qb.getTag().equalsIgnoreCase("JOB MH")) {
                            optId = taskD.getUuid_lookup();
                        } else {
                            optId = taskD.getOption_answer_id();
                        }
                        for (OptionAnswerBean optBean : qb.getOptionAnswers()) {
                            if (optId == optBean.getOption_id()) {
                                optBean.setSelected(true);
                                // optBean.setDescription(textAnswer);
                                break qLoop;
                            }
                        }
                        qb.getSelectedOptionAnswers().add(new OptionAnswerBean("0", "", taskD.getLov()));
                        qb.setLovCode(taskD.getLov());
                    } else if (Tool.isImage(answerType)) {
                        byte[] imgAnswer = taskD.getImage();
                        qb.setImgAnswer(imgAnswer);
                        break qLoop;
                    }
//						else if(AT_LOV.equals(answerType)||AT_LOV_W_FILTER.equals(answerType)){
//							qb.setLovId(lovId)
//							qb.setAnswer(textAnswer);
//							break qLoop;
//						}
                    else {
                        qb.setAnswer(textAnswer);
                        break qLoop;
                    }
                }
            }
        }
    }

    public List<QuestionBean> loadSelectedOptionForQuestionBean(List<QuestionBean> listQuestionBean) {
        List<QuestionBean> loadedBeans = new ArrayList<QuestionBean>(listQuestionBean);

        for (QuestionBean qb : loadedBeans) {
            if (!Tool.isOptions(qb.getAnswer_type())) continue;
            List<OptionAnswerBean> optAnsBean = new ArrayList<OptionAnswerBean>();
            OptionAnswerBean selectedOption = null;

            String lookUpId = qb.getLookupId();
            String flatLovCode = qb.getLovCode();
            if (flatLovCode == null) continue;
            String[] lovCodes = Tool.split(flatLovCode, Global.DELIMETER_DATA);
            for (int i = 0; i < lovCodes.length; i++) {
                String lovCode = lovCodes[i];
                if (lookUpId != null && lovCode != null) {
                    Lookup lookup = LookupDataAccess.getOneByCode(context, lookUpId, lovCode);
                    selectedOption = new OptionAnswerBean(lookup);
                    selectedOption.setSelected(true);
                    optAnsBean.add(selectedOption);
                }
            }
            qb.setSelectedOptionAnswers(optAnsBean);
        }
        return loadedBeans;
    }

	/*private List<PrintResult> makePrintResult(List<PrintItem> listPrintItem,
            List<QuestionSet> listQuestionSet, List<TaskD> listTaskD2) {
		List<PrintResult> listPrintResult = new ArrayList<PrintResult>();		
		
		for(TaskD answerBean : listTaskD){
			String qgid = answerBean.getQuestion_group_id();
			String qid = answerBean.getQuestion_id();
			String textAnswer = answerBean.getText_answer();
			boolean shouldSkipPrintItem = false;
			
			// prepare for printResult
			PrintResult printResult;
			
			qLoop:
			for(QuestionSet questionSet : listQuestionSet){
				printResult = null;
				if(qgid == questionSet.getQuestion_group_id() && qid == questionSet.getQuestion_id()){
					questionSet.setIs_visible(true);
					
					for (PrintItem printItem : listPrintItem){
						if(PRINT_ANSWER.equals(printItem.getPrint_type_id())){
							if(qgid == printItem.getQuestion_group_id() && qid == printItem.getQuestion_id()){
								printResult = new PrintResult();
								printResult.setUuid_task_h(answerBean.getUuid_task_h());
								printResult.setPrint_type_id(printItem.getPrint_type_id());
								printResult.setValue(answerBean.getText_answer());
								printResult.setLabel(questionSet.getQuestion_label());
							}
						}else if(PRINT_TIMESTAMP.equals(printItem.getPrint_type_id())){
							//print the date
							Date date = new Date();
							String dateString = Formatter.formatDate(date, "dd/MM/yyyy hh:mm");
							printResult.setValue(dateString);
							printResult.setPrint_type_id(printItem.getPrint_type_id());
							printResult.setLabel(questionSet.getQuestion_label());
							printResult.setUuid_task_h(answerBean.getUuid_task_h());
						}
					}
					
					boolean isVisible = true;
					String relevant = questionSet.getRelevant_question();
					if(relevant!=null && relevant.length()>0)
//						isVisible = Tool.
						shouldSkipPrintItem = isVisible;
				}
				
				if(!shouldSkipPrintItem)
					if(printResult!=null)
						listPrintResult.add(printResult);
			}
		}		
		return listPrintResult;
	}
	*/

    public void matchPrintItemWithAnswer(List<PrintResult> listPrintResult, List<QuestionBean> listQuestionBean) {
        if (listPrintResult == null || listQuestionBean == null) return;
        for (PrintResult printResult : listPrintResult) {
            if (PRINT_ANSWER.equals(printResult.getPrint_type_id())) {
                for (QuestionBean qb : listQuestionBean) {
                    if (qb.getQuestion_label() == printResult.getLabel()) {
                        printResult.setValue(qb.getAnswer());
                        break;
                    }
                }
            } else if (PRINT_TIMESTAMP.equals(printResult.getPrint_type_id())) {
                Date date = new Date();
                String dateString = Formatter.formatDate(date, "dd-MM-yyyy hh:mm");
                printResult.setValue(dateString);
            }
        }
    }
}
