package com.adins.mss.dao;

import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;

import com.adins.mss.dao.InstallmentSchedule;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TR_INSTALLMENTSCHEDULE".
*/
public class InstallmentScheduleDao extends AbstractDao<InstallmentSchedule, String> {

    public static final String TABLENAME = "TR_INSTALLMENTSCHEDULE";

    /**
     * Properties of entity InstallmentSchedule.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_installment_schedule = new Property(0, String.class, "uuid_installment_schedule", true, "UUID_INSTALLMENT_SCHEDULE");
        public final static Property Uuid_task_h = new Property(1, String.class, "uuid_task_h", false, "UUID_TASK_H");
        public final static Property Agreement_no = new Property(2, String.class, "agreement_no", false, "AGREEMENT_NO");
        public final static Property Branch_code = new Property(3, String.class, "branch_code", false, "BRANCH_CODE");
        public final static Property Installment_no = new Property(4, String.class, "installment_no", false, "INSTALLMENT_NO");
        public final static Property Installment_amount = new Property(5, String.class, "installment_amount", false, "INSTALLMENT_AMOUNT");
        public final static Property Instl_paid_amount = new Property(6, String.class, "instl_paid_amount", false, "INSTL_PAID_AMOUNT");
        public final static Property Lc_instl_amount = new Property(7, String.class, "lc_instl_amount", false, "LC_INSTL_AMOUNT");
        public final static Property Lc_instl_paid = new Property(8, String.class, "lc_instl_paid", false, "LC_INSTL_PAID");
        public final static Property Lc_instl_waived = new Property(9, String.class, "lc_instl_waived", false, "LC_INSTL_WAIVED");
        public final static Property Principal_amount = new Property(10, String.class, "principal_amount", false, "PRINCIPAL_AMOUNT");
        public final static Property Interest_amount = new Property(11, String.class, "interest_amount", false, "INTEREST_AMOUNT");
        public final static Property Os_principal_amount = new Property(12, String.class, "os_principal_amount", false, "OS_PRINCIPAL_AMOUNT");
        public final static Property Os_interest_amount = new Property(13, String.class, "os_interest_amount", false, "OS_INTEREST_AMOUNT");
        public final static Property Lc_days = new Property(14, String.class, "lc_days", false, "LC_DAYS");
        public final static Property Lc_admin_fee = new Property(15, String.class, "lc_admin_fee", false, "LC_ADMIN_FEE");
        public final static Property Lc_admin_fee_paid = new Property(16, String.class, "lc_admin_fee_paid", false, "LC_ADMIN_FEE_PAID");
        public final static Property Lc_admin_fee_waive = new Property(17, String.class, "lc_admin_fee_waive", false, "LC_ADMIN_FEE_WAIVE");
        public final static Property Usr_crt = new Property(18, String.class, "usr_crt", false, "USR_CRT");
        public final static Property Dtm_crt = new Property(19, java.util.Date.class, "dtm_crt", false, "DTM_CRT");
        public final static Property Due_date = new Property(20, java.util.Date.class, "due_date", false, "DUE_DATE");
        public final static Property Instl_paid_date = new Property(21, java.util.Date.class, "instl_paid_date", false, "INSTL_PAID_DATE");
    };


    public InstallmentScheduleDao(DaoConfig config) {
        super(config);
    }
    
    public InstallmentScheduleDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TR_INSTALLMENTSCHEDULE\" (" + //
                "\"UUID_INSTALLMENT_SCHEDULE\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_installment_schedule
                "\"UUID_TASK_H\" TEXT," + // 1: uuid_task_h
                "\"AGREEMENT_NO\" TEXT," + // 2: agreement_no
                "\"BRANCH_CODE\" TEXT," + // 3: branch_code
                "\"INSTALLMENT_NO\" TEXT," + // 4: installment_no
                "\"INSTALLMENT_AMOUNT\" TEXT," + // 5: installment_amount
                "\"INSTL_PAID_AMOUNT\" TEXT," + // 6: instl_paid_amount
                "\"LC_INSTL_AMOUNT\" TEXT," + // 7: lc_instl_amount
                "\"LC_INSTL_PAID\" TEXT," + // 8: lc_instl_paid
                "\"LC_INSTL_WAIVED\" TEXT," + // 9: lc_instl_waived
                "\"PRINCIPAL_AMOUNT\" TEXT," + // 10: principal_amount
                "\"INTEREST_AMOUNT\" TEXT," + // 11: interest_amount
                "\"OS_PRINCIPAL_AMOUNT\" TEXT," + // 12: os_principal_amount
                "\"OS_INTEREST_AMOUNT\" TEXT," + // 13: os_interest_amount
                "\"LC_DAYS\" TEXT," + // 14: lc_days
                "\"LC_ADMIN_FEE\" TEXT," + // 15: lc_admin_fee
                "\"LC_ADMIN_FEE_PAID\" TEXT," + // 16: lc_admin_fee_paid
                "\"LC_ADMIN_FEE_WAIVE\" TEXT," + // 17: lc_admin_fee_waive
                "\"USR_CRT\" TEXT," + // 18: usr_crt
                "\"DTM_CRT\" INTEGER," + // 19: dtm_crt
                "\"DUE_DATE\" INTEGER," + // 20: due_date
                "\"INSTL_PAID_DATE\" INTEGER);"); // 21: instl_paid_date
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TR_INSTALLMENTSCHEDULE\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, InstallmentSchedule entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_installment_schedule());
 
        String uuid_task_h = entity.getUuid_task_h();
        if (uuid_task_h != null) {
            stmt.bindString(2, uuid_task_h);
        }
 
        String agreement_no = entity.getAgreement_no();
        if (agreement_no != null) {
            stmt.bindString(3, agreement_no);
        }
 
        String branch_code = entity.getBranch_code();
        if (branch_code != null) {
            stmt.bindString(4, branch_code);
        }
 
        String installment_no = entity.getInstallment_no();
        if (installment_no != null) {
            stmt.bindString(5, installment_no);
        }
 
        String installment_amount = entity.getInstallment_amount();
        if (installment_amount != null) {
            stmt.bindString(6, installment_amount);
        }
 
        String instl_paid_amount = entity.getInstl_paid_amount();
        if (instl_paid_amount != null) {
            stmt.bindString(7, instl_paid_amount);
        }
 
        String lc_instl_amount = entity.getLc_instl_amount();
        if (lc_instl_amount != null) {
            stmt.bindString(8, lc_instl_amount);
        }
 
        String lc_instl_paid = entity.getLc_instl_paid();
        if (lc_instl_paid != null) {
            stmt.bindString(9, lc_instl_paid);
        }
 
        String lc_instl_waived = entity.getLc_instl_waived();
        if (lc_instl_waived != null) {
            stmt.bindString(10, lc_instl_waived);
        }
 
        String principal_amount = entity.getPrincipal_amount();
        if (principal_amount != null) {
            stmt.bindString(11, principal_amount);
        }
 
        String interest_amount = entity.getInterest_amount();
        if (interest_amount != null) {
            stmt.bindString(12, interest_amount);
        }
 
        String os_principal_amount = entity.getOs_principal_amount();
        if (os_principal_amount != null) {
            stmt.bindString(13, os_principal_amount);
        }
 
        String os_interest_amount = entity.getOs_interest_amount();
        if (os_interest_amount != null) {
            stmt.bindString(14, os_interest_amount);
        }
 
        String lc_days = entity.getLc_days();
        if (lc_days != null) {
            stmt.bindString(15, lc_days);
        }
 
        String lc_admin_fee = entity.getLc_admin_fee();
        if (lc_admin_fee != null) {
            stmt.bindString(16, lc_admin_fee);
        }
 
        String lc_admin_fee_paid = entity.getLc_admin_fee_paid();
        if (lc_admin_fee_paid != null) {
            stmt.bindString(17, lc_admin_fee_paid);
        }
 
        String lc_admin_fee_waive = entity.getLc_admin_fee_waive();
        if (lc_admin_fee_waive != null) {
            stmt.bindString(18, lc_admin_fee_waive);
        }
 
        String usr_crt = entity.getUsr_crt();
        if (usr_crt != null) {
            stmt.bindString(19, usr_crt);
        }
 
        java.util.Date dtm_crt = entity.getDtm_crt();
        if (dtm_crt != null) {
            stmt.bindLong(20, dtm_crt.getTime());
        }
 
        java.util.Date due_date = entity.getDue_date();
        if (due_date != null) {
            stmt.bindLong(21, due_date.getTime());
        }
 
        java.util.Date instl_paid_date = entity.getInstl_paid_date();
        if (instl_paid_date != null) {
            stmt.bindLong(22, instl_paid_date.getTime());
        }
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public InstallmentSchedule readEntity(Cursor cursor, int offset) {
        InstallmentSchedule entity = new InstallmentSchedule( //
            cursor.getString(offset + 0), // uuid_installment_schedule
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // uuid_task_h
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // agreement_no
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // branch_code
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // installment_no
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // installment_amount
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // instl_paid_amount
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7), // lc_instl_amount
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8), // lc_instl_paid
            cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9), // lc_instl_waived
            cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10), // principal_amount
            cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11), // interest_amount
            cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12), // os_principal_amount
            cursor.isNull(offset + 13) ? null : cursor.getString(offset + 13), // os_interest_amount
            cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14), // lc_days
            cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15), // lc_admin_fee
            cursor.isNull(offset + 16) ? null : cursor.getString(offset + 16), // lc_admin_fee_paid
            cursor.isNull(offset + 17) ? null : cursor.getString(offset + 17), // lc_admin_fee_waive
            cursor.isNull(offset + 18) ? null : cursor.getString(offset + 18), // usr_crt
            cursor.isNull(offset + 19) ? null : new java.util.Date(cursor.getLong(offset + 19)), // dtm_crt
            cursor.isNull(offset + 20) ? null : new java.util.Date(cursor.getLong(offset + 20)), // due_date
            cursor.isNull(offset + 21) ? null : new java.util.Date(cursor.getLong(offset + 21)) // instl_paid_date
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, InstallmentSchedule entity, int offset) {
        entity.setUuid_installment_schedule(cursor.getString(offset + 0));
        entity.setUuid_task_h(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setAgreement_no(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setBranch_code(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setInstallment_no(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setInstallment_amount(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setInstl_paid_amount(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setLc_instl_amount(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
        entity.setLc_instl_paid(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
        entity.setLc_instl_waived(cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9));
        entity.setPrincipal_amount(cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10));
        entity.setInterest_amount(cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11));
        entity.setOs_principal_amount(cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12));
        entity.setOs_interest_amount(cursor.isNull(offset + 13) ? null : cursor.getString(offset + 13));
        entity.setLc_days(cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14));
        entity.setLc_admin_fee(cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15));
        entity.setLc_admin_fee_paid(cursor.isNull(offset + 16) ? null : cursor.getString(offset + 16));
        entity.setLc_admin_fee_waive(cursor.isNull(offset + 17) ? null : cursor.getString(offset + 17));
        entity.setUsr_crt(cursor.isNull(offset + 18) ? null : cursor.getString(offset + 18));
        entity.setDtm_crt(cursor.isNull(offset + 19) ? null : new java.util.Date(cursor.getLong(offset + 19)));
        entity.setDue_date(cursor.isNull(offset + 20) ? null : new java.util.Date(cursor.getLong(offset + 20)));
        entity.setInstl_paid_date(cursor.isNull(offset + 21) ? null : new java.util.Date(cursor.getLong(offset + 21)));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(InstallmentSchedule entity, long rowId) {
        return entity.getUuid_installment_schedule();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(InstallmentSchedule entity) {
        if(entity != null) {
            return entity.getUuid_installment_schedule();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
}
