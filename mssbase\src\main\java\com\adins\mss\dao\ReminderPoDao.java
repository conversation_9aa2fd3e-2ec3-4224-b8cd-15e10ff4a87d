package com.adins.mss.dao;

import java.util.List;
import java.util.ArrayList;
import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.SqlUtils;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;
import de.greenrobot.dao.query.Query;
import de.greenrobot.dao.query.QueryBuilder;

import com.adins.mss.dao.ReminderPo;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TR_REMINDER_PO".
*/
public class ReminderPoDao extends AbstractDao<ReminderPo, String> {

    public static final String TABLENAME = "TR_REMINDER_PO";

    /**
     * Properties of entity ReminderPo.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Id = new Property(0, String.class, "id", true, "ID");
        public final static Property Dtm_crt = new Property(1, java.util.Date.class, "dtm_crt", false, "DTM_CRT");
        public final static Property Uuid_task_h = new Property(2, String.class, "uuid_task_h", false, "UUID_TASK_H");
        public final static Property Is_task_downloaded = new Property(3, String.class, "is_task_downloaded", false, "IS_TASK_DOWNLOADED");
        public final static Property Expired_date = new Property(4, java.util.Date.class, "expired_date", false, "EXPIRED_DATE");
        public final static Property Status_po = new Property(5, String.class, "status_po", false, "STATUS_PO");
        public final static Property Uuid_user = new Property(6, String.class, "uuid_user", false, "UUID_USER");
    };

    private DaoSession daoSession;

    private Query<ReminderPo> taskH_ReminderPoListQuery;

    public ReminderPoDao(DaoConfig config) {
        super(config);
    }
    
    public ReminderPoDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TR_REMINDER_PO\" (" + //
                "\"ID\" TEXT PRIMARY KEY NOT NULL ," + // 0: id
                "\"DTM_CRT\" INTEGER," + // 1: dtm_crt
                "\"UUID_TASK_H\" TEXT," + // 2: uuid_task_h
                "\"IS_TASK_DOWNLOADED\" TEXT," + // 3: is_task_downloaded
                "\"EXPIRED_DATE\" INTEGER," + // 4: expired_date
                "\"STATUS_PO\" TEXT," + // 5: status_po
                "\"UUID_USER\" TEXT);"); // 6: uuid_user
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TR_REMINDER_PO\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, ReminderPo entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getId());
 
        java.util.Date dtm_crt = entity.getDtm_crt();
        if (dtm_crt != null) {
            stmt.bindLong(2, dtm_crt.getTime());
        }
 
        String uuid_task_h = entity.getUuid_task_h();
        if (uuid_task_h != null) {
            stmt.bindString(3, uuid_task_h);
        }
 
        String is_task_downloaded = entity.getIs_task_downloaded();
        if (is_task_downloaded != null) {
            stmt.bindString(4, is_task_downloaded);
        }
 
        java.util.Date expired_date = entity.getExpired_date();
        if (expired_date != null) {
            stmt.bindLong(5, expired_date.getTime());
        }
 
        String status_po = entity.getStatus_po();
        if (status_po != null) {
            stmt.bindString(6, status_po);
        }
 
        String uuid_user = entity.getUuid_user();
        if (uuid_user != null) {
            stmt.bindString(7, uuid_user);
        }
    }

    @Override
    protected void attachEntity(ReminderPo entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public ReminderPo readEntity(Cursor cursor, int offset) {
        ReminderPo entity = new ReminderPo( //
            cursor.getString(offset + 0), // id
            cursor.isNull(offset + 1) ? null : new java.util.Date(cursor.getLong(offset + 1)), // dtm_crt
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // uuid_task_h
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // is_task_downloaded
            cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)), // expired_date
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // status_po
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6) // uuid_user
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, ReminderPo entity, int offset) {
        entity.setId(cursor.getString(offset + 0));
        entity.setDtm_crt(cursor.isNull(offset + 1) ? null : new java.util.Date(cursor.getLong(offset + 1)));
        entity.setUuid_task_h(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setIs_task_downloaded(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setExpired_date(cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)));
        entity.setStatus_po(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setUuid_user(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(ReminderPo entity, long rowId) {
        return entity.getId();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(ReminderPo entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
    /** Internal query to resolve the "reminderPoList" to-many relationship of TaskH. */
    public List<ReminderPo> _queryTaskH_ReminderPoList(String uuid_task_h) {
        synchronized (this) {
            if (taskH_ReminderPoListQuery == null) {
                QueryBuilder<ReminderPo> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_task_h.eq(null));
                taskH_ReminderPoListQuery = queryBuilder.build();
            }
        }
        Query<ReminderPo> query = taskH_ReminderPoListQuery.forCurrentThread();
        query.setParameter(0, uuid_task_h);
        return query.list();
    }

    private String selectDeep;

    protected String getSelectDeep() {
        if (selectDeep == null) {
            StringBuilder builder = new StringBuilder("SELECT ");
            SqlUtils.appendColumns(builder, "T", getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T0", daoSession.getTaskHDao().getAllColumns());
            builder.append(" FROM TR_REMINDER_PO T");
            builder.append(" LEFT JOIN TR_TASK_H T0 ON T.\"UUID_TASK_H\"=T0.\"UUID_TASK_H\"");
            builder.append(' ');
            selectDeep = builder.toString();
        }
        return selectDeep;
    }
    
    protected ReminderPo loadCurrentDeep(Cursor cursor, boolean lock) {
        ReminderPo entity = loadCurrent(cursor, 0, lock);
        int offset = getAllColumns().length;

        TaskH taskH = loadCurrentOther(daoSession.getTaskHDao(), cursor, offset);
        entity.setTaskH(taskH);

        return entity;    
    }

    public ReminderPo loadDeep(Long key) {
        assertSinglePk();
        if (key == null) {
            return null;
        }

        StringBuilder builder = new StringBuilder(getSelectDeep());
        builder.append("WHERE ");
        SqlUtils.appendColumnsEqValue(builder, "T", getPkColumns());
        String sql = builder.toString();
        
        String[] keyArray = new String[] { key.toString() };
        Cursor cursor = db.rawQuery(sql, keyArray);
        
        try {
            boolean available = cursor.moveToFirst();
            if (!available) {
                return null;
            } else if (!cursor.isLast()) {
                throw new IllegalStateException("Expected unique result, but count was " + cursor.getCount());
            }
            return loadCurrentDeep(cursor, true);
        } finally {
            cursor.close();
        }
    }
    
    /** Reads all available rows from the given cursor and returns a list of new ImageTO objects. */
    public List<ReminderPo> loadAllDeepFromCursor(Cursor cursor) {
        int count = cursor.getCount();
        List<ReminderPo> list = new ArrayList<ReminderPo>(count);
        
        if (cursor.moveToFirst()) {
            if (identityScope != null) {
                identityScope.lock();
                identityScope.reserveRoom(count);
            }
            try {
                do {
                    list.add(loadCurrentDeep(cursor, false));
                } while (cursor.moveToNext());
            } finally {
                if (identityScope != null) {
                    identityScope.unlock();
                }
            }
        }
        return list;
    }
    
    protected List<ReminderPo> loadDeepAllAndCloseCursor(Cursor cursor) {
        try {
            return loadAllDeepFromCursor(cursor);
        } finally {
            cursor.close();
        }
    }
    

    /** A raw-style query where you can pass any WHERE clause and arguments. */
    public List<ReminderPo> queryDeep(String where, String... selectionArg) {
        Cursor cursor = db.rawQuery(getSelectDeep() + where, selectionArg);
        return loadDeepAllAndCloseCursor(cursor);
    }
 
}
