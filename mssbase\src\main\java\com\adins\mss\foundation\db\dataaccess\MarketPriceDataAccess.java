package com.adins.mss.foundation.db.dataaccess;

import android.content.Context;

import com.adins.mss.base.commons.Query;
import com.adins.mss.dao.DaoSession;
import com.adins.mss.dao.MarketPrice;
import com.adins.mss.dao.MarketPriceDao;
import com.adins.mss.dao.POAsset;
import com.adins.mss.dao.POAssetDao;
import com.adins.mss.foundation.db.DaoOpenHelper;

import java.util.List;

import de.greenrobot.dao.query.QueryBuilder;

public class MarketPriceDataAccess {
    /**
     * use to generate dao session that you can access modelDao
     *
     * @param context --> context from activity
     * @return
     */
    protected static DaoSession getDaoSession(Context context) {
        /*if(daoOpenHelper==null){
//			if(daoOpenHelper.getDaoSession()==null)
				daoOpenHelper = new DaoOpenHelper(context);
		}
		DaoSession daoSeesion = daoOpenHelper.getDaoSession();
		return daoSeesion;*/
        return DaoOpenHelper.getDaoSession(context);
    }

    /**
     * Method for get MarketPriceDao
     * @param context
     * @return
     */
    protected static MarketPriceDao getMarketPriceDao(Context context) {
        return getDaoSession(context).getMarketPriceDao();
    }

    /**
     * Method add or replace for single entity
     * @param context
     * @param marketPrice
     */
    public static void addOrReplace(Context context, MarketPrice marketPrice) {
        getMarketPriceDao(context).insertOrReplace(marketPrice);
        getDaoSession(context).clear();
    }

    /**
     * Method for add or replace multiple entities
     * @param context
     * @param prices
     */
    public static void addOrReplace(Context context, List<MarketPrice> prices) {
        getMarketPriceDao(context).insertOrReplaceInTx(prices);
        getDaoSession(context).clear();
    }

    public static void delete(Context context, long id) {
        MarketPrice price = find(context, id);

        if (price != null) {
            getMarketPriceDao(context).delete(price);
            getDaoSession(context).clear();
        }
    }

    public static MarketPrice find(Context context, long id) {
        QueryBuilder<MarketPrice> qb = getMarketPriceDao(context).queryBuilder();
        qb.where(MarketPriceDao.Properties.Price_id.eq(id)).limit(1).build();

        if (!qb.list().isEmpty()) return qb.list().get(0);
        return null;
    }

    public static MarketPrice find(Context context, List<Query.Constraint> constraints) {
        QueryBuilder<MarketPrice> qb = getMarketPriceDao(context).queryBuilder();

        for (Query.Constraint constraint : constraints) {
            switch (constraint.getColumn()) {
                case "isDeleted":
                    qb.where(MarketPriceDao.Properties.Is_deleted.eq(constraint.getValue()));
                    break;
                case "assetCode":
                    qb.where(MarketPriceDao.Properties.Asset_code.eq(constraint.getValue()));
                    break;
                case "manufacturingYear":
                    qb.where(MarketPriceDao.Properties.Manufacturing_year.eq(constraint.getValue()));
                    break;
            }
        }

        qb.build();

        if (!qb.list().isEmpty()) return qb.list().get(0);
        return null;
    }

    public static MarketPrice findByQuery(Context context, List<Query.Constraint> constraints) {
        QueryBuilder<MarketPrice> qb = getMarketPriceDao(context).queryBuilder();
        for (Query.Constraint constraint : constraints) {
            switch (constraint.getColumn()) {
                case "IS_DELETED":
                    qb.where(MarketPriceDao.Properties.Is_deleted.eq(constraint.getValue()));
                    break;
                case "MASTER_NAME":
                    qb.join(MarketPriceDao.Properties.Asset_code, POAsset.class, POAssetDao.Properties.Master_code).where(POAssetDao.Properties.Master_name.eq(constraint.getValue()));
                    break;
                case "MANUFACTURING_YEAR":
                    qb.where(MarketPriceDao.Properties.Manufacturing_year.eq(constraint.getValue()));
                    break;
                case "OFFICE_CODE":
                    qb.where(MarketPriceDao.Properties.Office_code.eq(constraint.getValue()));
                    break;
                default:
                    break;
            }
        }

        qb.build();

        if (!qb.list().isEmpty()) return qb.list().get(0);
        return null;
    }

    public static MarketPrice last(Context context) {
        QueryBuilder<MarketPrice> qb = getMarketPriceDao(context).queryBuilder();
        qb.orderDesc(MarketPriceDao.Properties.Dtm_upd);
        qb.limit(1);
        qb.build();

        if (!qb.list().isEmpty()) return qb.list().get(0);
        return null;
    }

    public static void addOrUpdateAll(final Context context, final List<MarketPrice> marketPrices) {
        getMarketPriceDao(context).insertOrReplaceInTx(marketPrices);
        getDaoSession(context).clear();
    }
}
