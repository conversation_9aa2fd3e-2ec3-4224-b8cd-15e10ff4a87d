package com.adins.mss.foundation.camera;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.hardware.Camera.Parameters;
import android.widget.ImageView;
import android.widget.Toast;

import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.util.ByteFormatter;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.LocationInfo;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.location.LocationTrackingManager;
import com.adins.mss.foundation.questiongenerator.QuestionBean;

import java.util.Date;

public class FormCamera extends Camera {

    private QuestionBean questionInFocus;
    private ImageView thumbInFocus;

    public FormCamera(Context context, Activity activity,
                      android.hardware.Camera camera, Parameters params, QuestionBean questionInFocus) {
        super(context, activity, camera, params);
        // TODO Auto-generated constructor stub
        this.questionInFocus = questionInFocus;
        thumbInFocus = new ImageView(context);
    }

    @Override
    protected void processImage(byte[] dataPicWithExif, LocationInfo locationInfo) {
        // delegate
        this.imageCallBack.onPictureTaken(dataPicWithExif, locationInfo);

        LocationTrackingManager ltm = Global.LTM;
        saveImage(dataPicWithExif);

        boolean getGPS = true;

//		QuestionBean bean = DynamicSurveyActivity.getQuestionInFocus();
//		ib.setQuestionInFocus(DynamicSurveyActivity.getQuestionInFocus());
        // Glen 7 Oct 2014, new AT
//		boolean isGeoTagged = Global.AT_IMAGE_W_LOCATION.equals(bean.getAnswerType());
//		boolean isGeoTaggedGPSOnly = Global.AT_IMAGE_W_GPS_ONLY.equals(bean.getAnswerType());

        boolean isGeoTagged = Global.AT_IMAGE_W_LOCATION.equals(questionInFocus.getAnswer_type())
//				|| Global.AT_IMAGE_W_LOCATION_TIMESTAMP.equals(questionInFocus.getAnswer_type())
                ;
        boolean isGeoTaggedGPSOnly = Global.AT_IMAGE_W_GPS_ONLY.equals(questionInFocus.getAnswer_type())
//				|| Global.AT_IMAGE_W_GPS_ONLY_TIMESTAMP.equals(questionInFocus.getAnswer_type())
                ;

        //Glen 7 Oct 2014, check for need of timestamp
        boolean needTimeStamp = false;
//		if (Global.AT_IMAGE_W_GPS_ONLY_TIMESTAMP.equals(answerType)
//				|| Global.AT_IMAGE_W_LOCATION_TIMESTAMP.equals(answerType)
//				|| Global.AT_IMAGE_W_TIMESTAMP.equals(answerType)
//				){
//			needTimeStamp = true;
//		}


        //Glen 7 Oct 2014,  create timestamp, copy logic from MSMTOW, as default value if not using location
        try {
            long date = (new Date()).getTime();
            getQuestionInFocus().setLovId(String.valueOf(date));
        } catch (Exception e) {
            FireCrash.log(e);
            getQuestionInFocus().setLovId("0");
        }


        if (isGeoTagged) {
//			PhoneManager pm = ApplicationBean.getInstance().getPhoneManager();
            if (ltm != null) {
                LocationInfo locInfo = ltm.getCurrentLocation(Global.FLAG_LOCATION_CAMERA);
                getQuestionInFocus().setAnswer(locationInfoToSubmitString(locInfo));
                //Glen 7 Oct 2014, add timestamp
                if (needTimeStamp) {
                    long date = locInfo.getGps_time().getTime();
                    if (date > 0) {
                        getQuestionInFocus().setLovId(String.valueOf(date));
                    }
                }

            }
        }

        if (isGeoTaggedGPSOnly) {
            // PhoneManager pm = ApplicationBean.getInstance().getPhoneManager();
            if (ltm != null) {
                LocationInfo locInfo = ltm.getCurrentLocation(Global.FLAG_LOCATION_CAMERA);
                //Glen 7 Oct 2014, add timestamp
                if (needTimeStamp) {
                    long date = locInfo.getGps_time().getTime();
                    if (date > 0) {
                        getQuestionInFocus().setLovId(String.valueOf(date));
                    }
                }
                if (Double.parseDouble(locInfo.getLatitude()) == 0.0 || Double.parseDouble(locInfo.getLongitude()) == 0.0) {
                    if (getQuestionInFocus().isMandatory() || getQuestionInFocus().isRelevantMandatory()) {
                        String msg = getContext().getResources().getString(R.string.gps_error);
                        Toast.makeText(getContext(), msg, Toast.LENGTH_LONG).show();
                        saveImage(null);
                        getGPS = false;
                    }
                } else {
                    getQuestionInFocus().setAnswer(locationInfoToSubmitString(locInfo));
                }
            }
        }
        // set thumbnail
        if (thumbInFocus != null && getGPS) {
            Bitmap bm = BitmapFactory.decodeByteArray(dataPicWithExif, 0, dataPicWithExif.length);

            int[] res = Tool.getThumbnailResolution(bm.getWidth(), bm.getHeight());
            Bitmap thumbnail = Bitmap.createScaledBitmap(bm, res[0], res[1], true);
            setThumb(thumbnail);

            //Glen 21 Oct 2014, format byte
            long size = getQuestionInFocus().getImgAnswer().length;
            String formattedSize = ByteFormatter.formatByteSize(size);
//						DynamicSurveyActivity.setTxtDetailInFocus("   "+bm.getWidth()+" x " +bm.getHeight()+". Size "+bean.getImgAnswer().length +" Bytes");
            setTxtDetail("   " + bm.getWidth() + " x " + bm.getHeight() + ". Size " + formattedSize);

            questionInFocus = null;
        }
    }

    public QuestionBean getQuestionInFocus() {
        return questionInFocus;
    }

    public ImageView getThumbInFocus() {
        return thumbInFocus;
    }

    public ImageView getThumb() {
        return thumbInFocus;
    }

    public void setThumb(Bitmap bitmap) {
        thumbInFocus.setImageBitmap(bitmap);
    }

    public void saveImage(byte[] imgAnswer) {
        questionInFocus.setImgAnswer(imgAnswer);
    }
}
