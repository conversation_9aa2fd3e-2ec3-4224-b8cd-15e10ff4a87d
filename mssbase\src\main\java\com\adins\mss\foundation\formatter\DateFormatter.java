package com.adins.mss.foundation.formatter;

import androidx.annotation.Keep;

import com.adins.mss.constant.Global;

import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;

/**
 * Created by gigin.ginanjar on 28/09/2016.
 */

public class DateFormatter {

    @Keep
    public int age(String birthDate) {
        Date birth = null;
        try {
            birth = Formatter.parseDate(birthDate, Global.DATE_STR_FORMAT);
        } catch (ParseException e) {
            try {
                birth = Formatter.parseDate(birthDate, Global.DATE_STR_FORMAT_GSON);
            } catch (ParseException e1) {
                e1.printStackTrace();
            }
            e.printStackTrace();
        }
        Date now = new Date();
        long timeBetween = now.getTime() - birth.getTime();
        double yearsBetween = timeBetween / 3.15576e+10;
        int age = (int) Math.floor(yearsBetween);
        return age;
    }

    public int month(String intDate) {
        Date start = null;
        try {
            start  = Formatter.parseDate(intDate, Global.DATE_STR_FORMAT);
        } catch (ParseException e) {
            try {
                start = Formatter.parseDate(intDate, Global.DATE_STR_FORMAT_GSON);
            } catch (ParseException e1) {
                e1.printStackTrace();
            }
            e.printStackTrace();
        }

        Date now = new Date();
        Calendar startCalendar = Calendar.getInstance();
        startCalendar.setTime(start);
        Calendar endCalendar   = Calendar.getInstance();
        endCalendar.setTime(now);

        int diffYear = endCalendar.get(Calendar.YEAR) - startCalendar.get(Calendar.YEAR);
        return diffYear * 12 + endCalendar.get(Calendar.MONTH) - startCalendar.get(Calendar.MONTH);
//        return diffMonth;
    }
}
