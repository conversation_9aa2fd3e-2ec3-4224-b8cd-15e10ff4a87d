package com.adins.mss.base.taskupdate.model;

import com.adins.mss.dao.TaskUpdate;
import com.adins.mss.foundation.http.MssResponseType;
import com.google.gson.annotations.SerializedName;

import java.util.List;

public class JsonResponseTaskUpdate extends MssResponseType {

    @SerializedName("listTaskUpdate")
    List<TaskUpdate> taskUpdateResponse;

    public List<TaskUpdate> getTaskUpdateResponse() {
        return taskUpdateResponse;
    }

}
