package com.adins.mss.dao;

import com.google.gson.annotations.Since;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "MS_EMBEDDED_INFO".
 */
public class EmbeddedInfo {

    private long id_embedded;
    private java.util.Date dtm_crt;
    private String usr_crt;
    private String embedded_info;
    private String embedded_note;

    public EmbeddedInfo() {
    }

    public EmbeddedInfo(long id_embedded) {
        this.id_embedded = id_embedded;
    }

    public EmbeddedInfo(long id_embedded, java.util.Date dtm_crt, String usr_crt, String embedded_info, String embedded_note) {
        this.id_embedded = id_embedded;
        this.dtm_crt = dtm_crt;
        this.usr_crt = usr_crt;
        this.embedded_info = embedded_info;
        this.embedded_note = embedded_note;
    }

    public long getId_embedded() {
        return id_embedded;
    }

    public void setId_embedded(long id_embedded) {
        this.id_embedded = id_embedded;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public String getUsr_crt() {
        return usr_crt;
    }

    public void setUsr_crt(String usr_crt) {
        this.usr_crt = usr_crt;
    }

    public String getEmbedded_info() {
        return embedded_info;
    }

    public void setEmbedded_info(String embedded_info) {
        this.embedded_info = embedded_info;
    }

    public String getEmbedded_note() {
        return embedded_note;
    }

    public void setEmbedded_note(String embedded_note) {
        this.embedded_note = embedded_note;
    }

}
