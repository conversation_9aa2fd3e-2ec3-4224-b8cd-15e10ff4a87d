package com.adins.mss.base;

import android.app.Activity;
import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import android.util.Log;

import com.adins.mss.foundation.security.storepreferences.ObscuredSharedPreferences;
import com.services.AutoLogoutService;


public class BaseActivity extends FragmentActivity {
    private AlarmManager alarmManager;
    private Activity activity;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        alarmManager=  (AlarmManager) getSystemService(Context.ALARM_SERVICE);

        activity = this;
        IntentFilter screenFilter   = new IntentFilter(Intent.ACTION_SCREEN_ON);
        screenFilter.addAction(Intent.ACTION_SCREEN_OFF);
        BroadcastReceiver mReceiver = new ScreenReceiver();
        registerReceiver(mReceiver, screenFilter);
    }

    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    protected void onResume() {
        super.onResume();
        checkAutoLogout();
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    @Override
    protected void onUserLeaveHint() {
        scheduleLogout();
        super.onUserLeaveHint();
    }

    protected void scheduleLogout() {
        long timeOut = GlobalData.getSharedGlobalData().getTimeOut() * 1000;
        alarmManager.set(AlarmManager.RTC, System.currentTimeMillis() + timeOut, autoLogoutPendingIntent());
    }

    private PendingIntent autoLogoutPendingIntent() {
        Intent intent = new Intent(this, AutoLogoutService.class);
        return PendingIntent.getService(this, 9999, intent, PendingIntent.FLAG_UPDATE_CURRENT);
    }

    public class ScreenReceiver extends BroadcastReceiver {
        private boolean screenOff;

        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction().equals(Intent.ACTION_SCREEN_OFF)) {
                screenOff = true;
                scheduleLogout();
                Log.i(ScreenReceiver.class.getSimpleName(), "Screen Off");
            } else if (intent.getAction().equals(Intent.ACTION_SCREEN_ON)) {
                screenOff = false;
                checkAutoLogout();
                Log.i(ScreenReceiver.class.getSimpleName(), "Screen On");
            }
        }
    }

    private void checkAutoLogout() {
        ObscuredSharedPreferences sharedPref = ObscuredSharedPreferences.getPrefs(getApplicationContext(),
                "GlobalData", Context.MODE_PRIVATE);
        boolean hasLogged = sharedPref.getBoolean("HAS_LOGGED", false);
        if(!hasLogged){
            finish(); //2018-04-12: NENDI - Call Finish() to avoid back to LoginActivity
            Intent intent = new Intent(this, AppContext.getInstance().getLoginClass());
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intent);
        } else {
            alarmManager.cancel(autoLogoutPendingIntent());
        }
    }
}
