package com.adins.mss.base.dynamicform;

import com.adins.mss.constant.Global;
import com.adins.mss.dao.Scheme;

import java.io.Serializable;
import java.util.Date;

//import com.adins.util.Tool;

public class FormBean extends Scheme implements Serializable {
//	private String schemeId;
//	private String schemeDescription;
//	private Date schemeLastUpdate;
//	private boolean isPrintable;
//	private String printItemStr;
    //Glen 7 Aug 2014, add previewNeedServer
    /*jadi ceritanya ada form tertentu yang kalo nampilin preview,
    dia sbnernya kirim ke server isianya,
	 trus server balikin message (waktu tu hasil kalkulasi), trus tampilin di preview
	di collection klo ga sala waktu itu
	setelah isi2 form, pas preview dia kirim dlu ke server
	ntar server itung score, balikin ke hp, hp tampilin di preview
	ntar klo send, kirim lagi */
//	private boolean previewServer;

    public FormBean() {
    }

    public FormBean(Scheme scheme) {
        if (scheme == null)
            throw new IllegalArgumentException("Scheme == null !");


        setUuid_scheme(scheme.getUuid_scheme());
        setScheme_description(scheme.getScheme_description());
        setScheme_last_update(scheme.getScheme_last_update());
        setIs_printable(scheme.getIs_printable());
        setForm_id(scheme.getForm_id());
        setUsr_crt(scheme.getUsr_crt());
        setIs_preview_server(scheme.getIs_preview_server());
        setDtm_crt(scheme.getDtm_crt());
        setUsr_upd(scheme.getUsr_upd());
        setDtm_upd(scheme.getDtm_upd());
        setForm_type(scheme.getForm_type());
        setIs_active(scheme.getIs_active());
        setForm_version(scheme.getForm_version());//new
//			this.schemeId = scheme.getForm_id();
//			this.schemeDescription = scheme.getScheme_description();
//			this.schemeLastUpdate = scheme.getScheme_last_update();
//			this.isPrintable = "1".equals(scheme.getIs_printable()) ? true : false;
//			//Glen 7 Aug 2014
//			this.previewServer = "1".equals(scheme.getIs_preview_server()) ? true : false;

    }

    public FormBean(String id, Date lastUpdate, String isPrintable) {
        setUuid_scheme(id);
        setScheme_last_update(lastUpdate);
        setIs_printable(isPrintable);

    }

//	public String getSchemeId() {
//		return schemeId;
//	}
//
//	public void setSchemeId(String schemeId) {
//		this.schemeId = schemeId;
//	}
//
//	public String getSchemeDescription() {
//		return schemeDescription;
//	}
//
//	public void setSchemeDescription(String schemeDescription) {
//		this.schemeDescription = schemeDescription;
//	}
//
//	public Date getSchemeLastUpdate() {
//		return schemeLastUpdate;
//	}
//
//	public void setSchemeLastUpdate(Date schemeLastUpdate) {
//		this.schemeLastUpdate = schemeLastUpdate;
//	}
//
//	public boolean isPrintable() {
//		return isPrintable;
//	}
//
//	public void setPrintable(boolean isPrintable) {
//		this.isPrintable = isPrintable;
//	}
//
//	public String getPrintItemStr() {
//		return printItemStr;
//	}
//
//	public void setPrintItemStr(String printItemStr) {
//		this.printItemStr = printItemStr;
//	}

    public boolean isPreviewServer() {
        return getIs_preview_server().equals(Global.TRUE_STRING);
    }

    public void setPreviewServer(String bool) {
        setIs_preview_server(bool);
        //this.previewServer = previewServer;
    }

    public String toString() {
        return getForm_id() + " - " + getScheme_description();
    }


}
