package com.adins.mss.base.tasklog;

import android.app.ActionBar;
import android.app.ProgressDialog;
import android.content.Context;
import android.graphics.drawable.GradientDrawable;
import android.os.AsyncTask;
import android.os.Bundle;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import android.text.format.DateFormat;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.AdapterView;
import android.widget.AdapterView.OnItemClickListener;
import android.widget.GridView;
import android.widget.ImageButton;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.authentication.Authentication;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.depositreport.TaskLogHelper;
import com.adins.mss.base.dynamicform.CustomerFragment;
import com.adins.mss.base.dynamicform.SurveyHeaderBean;
import com.adins.mss.base.mainmenu.MainMenuActivity;
import com.adins.mss.base.tasklog.JsonRequestApplicationStatus.TaskStatus;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.base.util.Utility;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.ReminderPo;
import com.adins.mss.dao.Scheme;
import com.adins.mss.dao.TaskH;
import com.adins.mss.foundation.camerainapp.helper.Logger;
import com.adins.mss.foundation.db.dataaccess.ReminderPoDataAccess;
import com.adins.mss.foundation.db.dataaccess.SchemeDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskHDataAccess;
import com.adins.mss.foundation.dialog.DialogManager;
import com.adins.mss.foundation.dialog.NiftyDialogBuilder;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.security.storepreferences.ObscuredSharedPreferences;

import org.acra.ACRA;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

public class LogResultActivity extends Fragment implements OnItemClickListener {
    public static TaskH selectedLog;
    private static Menu mainMenu;
    private List<TaskH> objects;
    private TaskLogArrayAdapter adapter;
    private List<TaskStatus> statusResponseList = new ArrayList<>();
    OnItemClickListener myOnItemClickListener = new OnItemClickListener() {

        @Override
        public void onItemClick(AdapterView<?> parent, View view, int position,
                                long id) {
            // TODO Action Ketika item di klik
            selectedLog = adapter.getItem(position);
            try {
                Scheme scheme = null;
                scheme = selectedLog.getScheme();
                if (scheme == null) {
                    if (selectedLog.getUuid_scheme() != null) {
                        scheme = SchemeDataAccess.getOne(getActivity(), selectedLog.getUuid_scheme());
                        if (scheme != null)
                            selectedLog.setScheme(scheme);
                    }
                }

                if (scheme == null) {
                    Toast.makeText(getActivity(), getActivity().getString(R.string.task_cant_seen),
                            Toast.LENGTH_SHORT).show();
                } else {
                    SurveyHeaderBean header = new SurveyHeaderBean(selectedLog);
//				if(header.getIs_prepocessed()!=null && header.getIs_prepocessed().equals(RescheduleFragment.TASK_RESCHEDULE)){
//					Toast.makeText(getActivity(), "Rescheduled task, details can not be seen",
//							Toast.LENGTH_SHORT).show();
//				}
//				else{
                    Bundle bundle = new Bundle();
                    bundle.putSerializable(CustomerFragment.SURVEY_HEADER, header);
                    bundle.putInt(CustomerFragment.SURVEY_MODE, Global.MODE_VIEW_SENT_SURVEY);
                    Fragment fragment = com.adins.mss.base.dynamicform.CustomerFragment.create(header);

                    FragmentTransaction transaction = MainMenuActivity.fragmentManager.beginTransaction();
                    transaction.setCustomAnimations(R.anim.activity_open_translate, R.anim.activity_close_scale, R.anim.activity_open_scale, R.anim.activity_close_translate);
                    transaction.replace(R.id.content_frame, fragment);
                    transaction.addToBackStack(null);
                    transaction.commit();
//				}
                }
            } catch (Exception e) {
                FireCrash.log(e);
                Toast.makeText(getActivity(), getActivity().getString(R.string.scheme_not_found_sync) + " " + e.getMessage(),
                        Toast.LENGTH_SHORT).show();
            }
        }
    };
    private GetOnlineLog onlineLog;
    private RelativeLayout layoutView;
    private TextView dataNotFound;
    private GridView gridView;
    private OnItemClickListener itemClickListener;
    private GetTaskHReminderPo onlineTaskHPo;
    private NiftyDialogBuilder dialogBuilder;

    protected void setGradientBg() {
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Utility.freeMemory();
    }

    @Override
    public void onAttach(Context activity) {
        super.onAttach(activity);
        setHasOptionsMenu(true);
        getActivity().getActionBar().setTitle(getString(R.string.title_mn_log));
        try {
            objects = Global.listOfSentTask;
            if (objects == null || objects.isEmpty()) {
                updateList();
            }
            GradientDrawable gradient = new GradientDrawable(
                    GradientDrawable.Orientation.TOP_BOTTOM,
                    new int[]{0xFF616261, 0xFF131313});
            gradient.setCornerRadius(0f);

            //if use color for background
            //adapter = new TaskLogArrayAdapter(activity, objects, Color.BLACK);

            //if use gradient for background
            adapter = new TaskLogArrayAdapter(activity, objects, true);

        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: handle exception
        }
    }

    public void updateList() {
        TaskLog log = new TaskLog(getActivity());
        objects = log.getListTaskLog();
        //Global.listOfSentTask = objects;

        if (adapter != null) {
            adapter.setObjects(objects);
            objects = adapter.getObjects();
            Global.listOfSentTask = objects;
            adapter.notifyDataSetChanged();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        cancelOnlineLog();
        cancelOnlineTaskHPo();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        //To can't screenshoot
        if (Tool.isProdFlavour(Global.FLAVORS)) {
            getActivity().getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
        }

        View view = inflater.inflate(R.layout.log_layout, container, false);
        gridView = (GridView) view.findViewById(R.id.gridLog);
        dataNotFound = (TextView) view.findViewById(R.id.txv_data_not_found);
        if (adapter == null) {
            try {
                if (objects == null || objects.isEmpty()) {
                    TaskLog log = new TaskLog(getActivity());
                    objects = log.getListTaskLog();
                    Global.listOfSentTask = objects;
                }
                GradientDrawable gradient = new GradientDrawable(
                        GradientDrawable.Orientation.TOP_BOTTOM,
                        new int[]{0xFF616261, 0xFF131313});
                gradient.setCornerRadius(0f);
                adapter = new TaskLogArrayAdapter(getActivity(), objects, true);
            } catch (Exception e) {
                FireCrash.log(e);
                // TODO: handle exception
            }
        }
        itemClickListener = this;

        gridView.setAdapter(adapter);
        gridView.setOnItemClickListener(myOnItemClickListener);
        layoutView = (RelativeLayout) view.findViewById(R.id.layoutView);
        if (objects == null || objects.isEmpty()) {
            dataNotFound.setVisibility(View.VISIBLE);
            layoutView.setBackgroundResource(R.drawable.bg_notfound);

            if (Global.APPLICATION_SURVEY.equals(GlobalData.getSharedGlobalData().getApplication())) {
                dialogBuilder = NiftyDialogBuilder.getInstance(getActivity());
                dialogBuilder.withTitle(getActivity().getString(R.string.info_capital))
                        .withMessage(getActivity().getString(R.string.msgNoSent))
                        .show();
            }
        }
        ImageButton refreshButton = (ImageButton) view.findViewById(R.id.btnRefresh);
        if (GlobalData.getSharedGlobalData().getApplication().equals(Global.APPLICATION_COLLECTION)) {
            refreshButton.setVisibility(View.VISIBLE);
        }
        refreshButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Animation a = AnimationUtils.loadAnimation(getActivity(), R.anim.icon_rotate);
                v.startAnimation(a);
                callOnlineLog();
            }
        });

        // Adding a process for requesting TaskH when opening the log menu (2022-08-12)
        if (Global.APPLICATION_SURVEY.equals(GlobalData.getSharedGlobalData().getApplication())) {
            getOnlineTaskHPo();
        }

        return view;
    }

    private void callOnlineLog() {
        cancelOnlineLog();
        onlineLog = new GetOnlineLog();
        onlineLog.execute();
    }

    private void cancelOnlineLog() {
        if (onlineLog != null) {
            onlineLog.cancel(true);
            onlineLog = null;
        }
    }

    private void getOnlineTaskHPo() {
        cancelOnlineTaskHPo();
        List<ReminderPo> listReminderPo = ReminderPoDataAccess.getAllByDownloadStatus(getContext(), Global.FALSE_STRING);
        onlineTaskHPo = new GetTaskHReminderPo(listReminderPo);
        onlineTaskHPo.execute();
    }

    private void cancelOnlineTaskHPo() {
        if (onlineTaskHPo != null) {
            onlineTaskHPo.cancel(true);
            onlineTaskHPo = null;
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();

        adapter = null;
        Global.listOfSentTask = null;
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View v, int position, long id) {
        // TODO Action Ketika item di klik
        selectedLog = adapter.getItem(position);
        try {
            Scheme scheme = null;
            scheme = selectedLog.getScheme();
            if (scheme == null) {
                if (selectedLog.getUuid_scheme() != null) {
                    scheme = SchemeDataAccess.getOne(getActivity(), selectedLog.getUuid_scheme());
                    if (scheme != null)
                        selectedLog.setScheme(scheme);
                }
            }

            if (scheme == null) {
                Toast.makeText(getActivity(), getActivity().getString(R.string.task_cant_seen),
                        Toast.LENGTH_SHORT).show();
            } else {
                SurveyHeaderBean header = new SurveyHeaderBean(selectedLog);
//				if(header.getIs_prepocessed()!=null && header.getIs_prepocessed().equals(RescheduleFragment.TASK_RESCHEDULE)){
//					Toast.makeText(getActivity(), "Rescheduled task, details can not be seen",
//							Toast.LENGTH_SHORT).show();
//				}
//				else{
                Bundle bundle = new Bundle();
                bundle.putSerializable(CustomerFragment.SURVEY_HEADER, header);
                bundle.putInt(CustomerFragment.SURVEY_MODE, Global.MODE_VIEW_SENT_SURVEY);
                Fragment fragment = com.adins.mss.base.dynamicform.CustomerFragment.create(header);

                FragmentTransaction transaction = MainMenuActivity.fragmentManager.beginTransaction();
                transaction.setCustomAnimations(R.anim.activity_open_translate, R.anim.activity_close_scale, R.anim.activity_open_scale, R.anim.activity_close_translate);
                transaction.replace(R.id.content_frame, fragment);
                transaction.addToBackStack(null);
                transaction.commit();
//				}
            }
        } catch (Exception e) {
            FireCrash.log(e);
            Toast.makeText(getActivity(), getActivity().getString(R.string.scheme_not_found_sync) + " " + e.getMessage(),
                    Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        getActivity().getActionBar().removeAllTabs();
        getActivity().getActionBar().setTitle(getString(R.string.title_mn_log));
        getActivity().getActionBar().setNavigationMode(ActionBar.NAVIGATION_MODE_STANDARD);
        MainMenuActivity.setDrawerPosition(getString(R.string.title_mn_log));
        try {
            updateList();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onPrepareOptionsMenu(Menu menu) {
        super.onPrepareOptionsMenu(menu);
        mainMenu = menu;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // Handle item selection
        int id = item.getItemId();
        if (id == R.id.menuMore) {
            mainMenu.findItem(R.id.mnViewMap).setVisible(false);
            mainMenu.findItem(R.id.mnViewAllHeader).setVisible(false);
        }

        return super.onOptionsItemSelected(item);
    }

    class GetOnlineLog extends AsyncTask<Void, Void, List<TaskH>> {
        private ProgressDialog progressDialog;

        @Override
        protected void onPreExecute() {
            progressDialog = ProgressDialog.show(getActivity(), "", getString(R.string.progressWait), true);
        }

        @Override
        protected List<TaskH> doInBackground(Void... params) {
            List<TaskH> result = null;
            if (GlobalData.getSharedGlobalData().getApplication().equals(Global.APPLICATION_COLLECTION)) {
                try {
                    TaskLog log = new TaskLog(getActivity());
                    result = log.getListTaskLog();
                    List<TaskH> onlineLog = TaskLogHelper.getTaskLog(getActivity());
                    if (onlineLog != null) {
                        if (result == null) result = new ArrayList<>();
                        List<String> uuidListTaskH = new ArrayList<>();

                        for (TaskH taskH : result) {
                            uuidListTaskH.add(taskH.getUuid_task_h());
                        }

                        Iterator<TaskH> iterator = onlineLog.iterator();
                        List<TaskH> listTaskHWithFlag = new ArrayList<>();

                        while (iterator.hasNext()) {
                            TaskH taskH = iterator.next();

                            if (uuidListTaskH.contains(taskH.getUuid_task_h())) {
                                try {
                                    TaskH taskHWithFlag = TaskHDataAccess.getOneHeader(getContext(), taskH.getUuid_task_h());
                                    taskHWithFlag.setFlag(taskH.getFlag());
                                    listTaskHWithFlag.add(taskHWithFlag);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                                iterator.remove();
                            }

                        }
                        if (!listTaskHWithFlag.isEmpty()) {
                            TaskHDataAccess.addOrReplace(getContext(), listTaskHWithFlag);
                        }

                        if (!onlineLog.isEmpty()) {
                            for (TaskH taskH : onlineLog) {
                                if (!uuidListTaskH.contains(taskH.getUuid_task_h())) {
                                    taskH.setUuid_user(GlobalData.getSharedGlobalData().getUser().getUuid_user());
                                    taskH.setStatus(TaskHDataAccess.STATUS_SEND_SENT);
                                }
                                TaskHDataAccess.addOrReplace(getActivity(), taskH);
                                result.add(taskH);
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            return result;
        }

        @Override
        protected void onPostExecute(List<TaskH> taskHs) {
            super.onPostExecute(taskHs);
            if (getActivity() != null) {
                if (progressDialog.isShowing()) {
                    try {
                        progressDialog.dismiss();
                    } catch (Exception e) {
                        FireCrash.log(e);
                    }
                }
                if (taskHs == null || taskHs.isEmpty()) {
                    NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(getActivity());
                    dialogBuilder.withTitle(getActivity().getString(R.string.info_capital))
                            .withMessage(getActivity().getString(R.string.msgNoSent))
                            .show();
                }

                adapter.setObjects(taskHs);
                objects = adapter.getObjects();

                Global.listOfSentTask = objects;

                if (objects == null || objects.isEmpty()) {
                    layoutView.setBackgroundResource(R.drawable.bg_notfound);
                    dataNotFound.setVisibility(View.VISIBLE);
                } else {
                    layoutView.setBackgroundResource(R.drawable.bg_grayscale);
                }

                adapter.notifyDataSetChanged();
                try {
                    MainMenuActivity.setDrawerCounter();
                } catch (Exception e) {
                    FireCrash.log(e);
                    ACRA.getErrorReporter().putCustomData("ErrorMainMenuActivity", e.getMessage());
                    ACRA.getErrorReporter().putCustomData("ErrorMainMenuActivity", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                    ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat set Drawer Counter"));
                }
                updateList();
            }
        }
    }

    public List<TaskStatus> getServerTaskStatus() {

        JsonRequestApplicationStatus requestApplicationStatus = new JsonRequestApplicationStatus();
        requestApplicationStatus.setAudit(GlobalData.getSharedGlobalData().getAuditData());

        JsonResponseApplicationStatus jrsrtl = new JsonResponseApplicationStatus();

        TaskLog log = new TaskLog(getActivity());
        List<TaskH> logList = new ArrayList<>();
        logList = log.getListTaskLog();
        List<TaskStatus> statusList = new ArrayList<>();

        if (logList != null || !logList.isEmpty()) {
            for (int i = 0; i < logList.size(); i++) {
                JsonRequestApplicationStatus.TaskStatus taskStatus = new TaskStatus();
                taskStatus.setUuidTaskH(logList.get(i).getUuid_task_h());
                taskStatus.setApplNo(logList.get(i).getAppl_no());
                taskStatus.setStatusApplication("");
                taskStatus.setIsSentConfins(0);
                statusList.add(taskStatus);
            }
            requestApplicationStatus.setTaskStatusList(statusList);

            String json = GsonHelper.toJson(requestApplicationStatus);

            boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
            boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
            HttpCryptedConnection httpConn = new HttpCryptedConnection(getActivity(), encrypt, decrypt);
            HttpConnectionResult serverResult = null;
            String url = GlobalData.getSharedGlobalData().getURL_GET_TASKLIST();

            try {
                serverResult = httpConn
                        .requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
            } catch (Exception ex) {
                // errMessage = e.getMessage();
            }

            if (serverResult != null && serverResult.isOK()) {
                String sentStatus = serverResult.getResult();
                if (Global.IS_DEV)
                    Logger.i("INFO", "Adding: " + "Here is response TaskStatus from notif : " + sentStatus);
                jrsrtl = GsonHelper.fromJson(sentStatus, JsonResponseApplicationStatus.class);

                if (jrsrtl.getStatus().getCode() == Global.STATUS_CODE_APPL_CLEANSING) {
                    //ApplicationCleansingManager.deleteAllDataInAllTable(context);
                    MainMenuActivity.Force_Uninstall = true;
                    ObscuredSharedPreferences.Editor editor = ObscuredSharedPreferences.getPrefs(getActivity(), Authentication.LOGIN_PREFERENCES, Context.MODE_PRIVATE).edit();
                    editor.putString(Authentication.LOGIN_PREFERENCES_APPLICATION_CLEANSING, "uninstall");
                    editor.apply();
                    //bong 10 apr 15 - Gigin minta untuk uninstall aplikasi jika user tidak aktif
                    DialogManager.uninstallAPK(getActivity());
                } else {
                    MainMenuActivity.Force_Uninstall = false;
                }
            }
        }

        if (jrsrtl.getListTaskStatus() != null)
            return jrsrtl.getListTaskStatus();
        else
            return null;
    }

    private class GetTaskHReminderPo extends AsyncTask<Void, Void, List<TaskH>> {
        private ProgressDialog progressDialog;
        private List<ReminderPo> listUuidTaskH;

        public GetTaskHReminderPo(List<ReminderPo> listUuidTaskH) {
            this.listUuidTaskH = listUuidTaskH;
        }

        @Override
        protected void onPreExecute() {
            progressDialog = ProgressDialog.show(getActivity(), "", getString(R.string.progressWait), true);
        }

        @Override
        protected List<TaskH> doInBackground(Void... params) {
            JsonTaskHReminderPoResponse response = null;

            if (Tool.isInternetconnected(getContext())) {
                JsonTaskHReminderPoRequest request = new JsonTaskHReminderPoRequest();
                if (null != listUuidTaskH && !listUuidTaskH.isEmpty()) {
                    List<JsonTaskHReminderPoRequest.TaskHPo> listTaskPo = new ArrayList<>();
                    for (ReminderPo taskPo : listUuidTaskH) {
                        JsonTaskHReminderPoRequest.TaskHPo taskHPo = new JsonTaskHReminderPoRequest.TaskHPo();
                        taskHPo.setUuidTaskH(taskPo.getUuid_task_h());
                        listTaskPo.add(taskHPo);
                    }
                    request.setListTaskH(listTaskPo);
                    request.setAudit(GlobalData.getSharedGlobalData().getAuditData());

                    String json = GsonHelper.toJson(request);
                    String url = GlobalData.getSharedGlobalData().getURL_GET_TASK_H_REMINDER_PO();

                    boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                    boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                    HttpCryptedConnection httpConn = new HttpCryptedConnection(getActivity(), encrypt, decrypt);
                    HttpConnectionResult serverResult;

                    try {
                        serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
                        if (serverResult != null && serverResult.isOK()) {
                            try {
                                response = GsonHelper.fromJson(serverResult.getResult(), JsonTaskHReminderPoResponse.class);
                            } catch (Exception e) {
                                FireCrash.log(e);
                                ACRA.getErrorReporter().putCustomData("errorGetMessageFromServer", e.getMessage());
                                ACRA.getErrorReporter().putCustomData("errorGetMessageFromServer", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                                ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat convert json dari Server"));
                                e.printStackTrace();
                            }
                        }
                    } catch (Exception e) {
                        FireCrash.log(e);
                        ACRA.getErrorReporter().putCustomData("errorRequestToServer", e.getMessage());
                        ACRA.getErrorReporter().putCustomData("errorRequestToServer", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                        ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat request to server"));
                        e.printStackTrace();
                    }
                }
            }

            if (null != response) {
                return response.getListTaskList();
            }
            return Collections.emptyList();
        }

        @Override
        protected void onPostExecute(List<TaskH> listTaskH) {
            super.onPostExecute(listTaskH);
            if (null != getActivity()) {
                if (progressDialog.isShowing()) {
                    try {
                        progressDialog.dismiss();
                    } catch (Exception e) {
                        FireCrash.log(e);
                    }
                }

                if (null != listTaskH && !listTaskH.isEmpty()) {
                    if(dialogBuilder != null && dialogBuilder.isShowing()) {
                        dialogBuilder.dismiss();
                    }
                    for (TaskH taskH : listTaskH) {
                        taskH.setStatus(TaskHDataAccess.STATUS_SEND_SENT);
                        taskH.setUuid_user(GlobalData.getSharedGlobalData().getUser().getUuid_user());
                        taskH.setUser(GlobalData.getSharedGlobalData().getUser());
                        TaskHDataAccess.addOrReplace(getContext(), taskH);

                        ReminderPo reminderPo = ReminderPoDataAccess.getOneByUuidTaskH(getContext(), taskH.getUuid_task_h());
                        if (null != reminderPo) {
                            reminderPo.setIs_task_downloaded(Global.TRUE_STRING);
                            ReminderPoDataAccess.addOrReplace(getContext(), reminderPo);
                        }
                    }

                    layoutView.setBackgroundResource(R.drawable.bg_grayscale);
                    dataNotFound.setVisibility(View.GONE);
                    try {
                        MainMenuActivity.setDrawerCounter();
                    } catch (Exception e) {
                        FireCrash.log(e);
                        ACRA.getErrorReporter().putCustomData("ErrorMainMenuActivity", e.getMessage());
                        ACRA.getErrorReporter().putCustomData("ErrorMainMenuActivity", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                        ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat set Drawer Counter"));
                    }
                    updateList();
                }
            }
        }
    }

}
