package com.adins.mss.base.commons;

import android.annotation.TargetApi;
import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.provider.Settings;

import android.util.Base64;

import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.util.UserSession;
import com.adins.mss.foundation.db.DaoOpenHelper;
import com.adins.mss.foundation.security.storepreferences.ObscuredSharedPreferences;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.channels.FileChannel;
import java.security.MessageDigest;
import java.util.Random;

import de.greenrobot.dao.database.Database;

/**
 * Helper class to support development
 * <AUTHOR>
 * 31/01/2018
 */
public class Helper {
    private static int BUFFER = 2048;
    public static String CHIPER_SHA1 = "SHA-1";
    public static String CHIPER_MD5  = "MD5";

    public static Helper getInstance(){
        return new Helper();
    }

    public String salt(int length) {
        String saltChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";
        StringBuilder salt = new StringBuilder();
        Random rnd = new Random();
        while (salt.length() < length) { // length of the random string.
            int index = (int) (rnd.nextFloat() * saltChars.length());
            salt.append(saltChars.charAt(index));
        }
        String saltStr = salt.toString();
        return saltStr;
    }

    public String getNameFromUrl(String url) {
        return url.replaceFirst(".*/([^/?]+).*", "$1");
    }

    public String signature(File file, String algorithm) throws Exception {
        MessageDigest digest = MessageDigest.getInstance(algorithm);
        InputStream is = new FileInputStream(file);

        int n = 0;
        byte[] buffer  = new byte[BUFFER];
        while (n != -1) {
            n = is.read(buffer);
            if (n > 0) {
                digest.update(buffer, 0, n);
            }
        }

        is.close();
        byte[] result    = digest.digest();
        String hexString = bytesToHex(result);
        return hexString.toUpperCase();
    }

    public byte[] fromBase64(String encoded) {
//        return Base64.getDecoder().decode(encoded);
        return Base64.decode(encoded, Base64.DEFAULT);
    }

    public String toBase64(File file) throws IOException {
        String textB64  = null;
        byte[] bytes    = loadFile(file);
//        byte[] encoded  = Base64.getEncoder().encode(bytes);
        byte[] encoded  = Base64.encode(bytes, Base64.DEFAULT);

        textB64 = new String(encoded);
        return textB64;
    }

    public String toBase64(byte[] bytes) {
//        byte[] encoded = Base64.getEncoder().encode(bytes);
        byte[] encoded = Base64.encode(bytes, Base64.DEFAULT);
        return new String(encoded);
    }

    public boolean isFileExist(String path) {
        File file = new File(path);
        return file.exists();
    }

    public File createFile(byte[] bytes, String pathFile) {
        try {
            File file = new File(pathFile);
            FileOutputStream fos = new FileOutputStream(file);
            fos.write(bytes);
            fos.flush();
            fos.close();

            return file;
        } catch (IOException io) {
            io.printStackTrace();
        }

        return null;
    }

    public byte[] loadFile(File file) throws IOException {
        InputStream is = new FileInputStream(file);

        long length = file.length();
        if (length > Integer.MAX_VALUE) {
            //
        }

        byte[] bytes= new byte[(int) length];
        int offset  = 0;
        int read    = 0;

        while (offset < bytes.length &&
                (read = is.read(bytes, offset, bytes.length-offset)) >= 0) {
            offset += read;
        }

        if (offset < bytes.length) {
            throw new IOException("Could not completely read file " + file.getName());
        }

        is.close();
        return bytes;
    }

    public File copy(File sourceFile, File destFile) throws IOException {
//        Path srcPath = Paths.get(source.toURI());
//        Path dstPath = Paths.get(target.toURI());
//
//        return Files.copy (
//                srcPath,
//                dstPath,
//                StandardCopyOption.REPLACE_EXISTING
//        );

        if (!destFile.getParentFile().exists())
            destFile.getParentFile().mkdirs();

        if (!destFile.exists()) {
            destFile.createNewFile();
        }

        FileChannel source = null;
        FileChannel destination = null;

        try {
            source = new FileInputStream(sourceFile).getChannel();
            destination = new FileOutputStream(destFile).getChannel();
            destination.transferFrom(source, 0, source.size());
        } finally {
            if (source != null) {
                source.close();
            }
            if (destination != null) {
                destination.close();
            }
        }

        return destFile;
    }

    public String bytesToHex(byte[] bytes)
    {
        StringBuffer buffer = new StringBuffer();
        for(int i=0; i<bytes.length; i++)
        {
            if(((int)bytes[i] & 0xff) < 0x10)
                buffer.append("0");
            buffer.append(Long.toString((int) bytes[i] & 0xff, 16));
        }

        return buffer.toString();
    }


    public boolean isServiceAvailable(Context context, Class<?> serviceClass) {
        ActivityManager manager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);

        if (manager != null) {
            for (ActivityManager.RunningServiceInfo service : manager.getRunningServices(Integer.MAX_VALUE)) {
                if (serviceClass.getName().equalsIgnoreCase(service.service.getClassName())) {
                    return true;
                }
            }
        }

        return false;
    }

    public static void clearSession(Context context) {
        try {
            UserSession.clear();
            ObscuredSharedPreferences sharedPref = ObscuredSharedPreferences.getPrefs(context,
                    "GlobalData", Context.MODE_PRIVATE);

            ObscuredSharedPreferences.Editor sharedPrefEditor = sharedPref.edit();
            sharedPrefEditor.remove("HAS_LOGGED");
            sharedPrefEditor.commit();
        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: handle exception
        }
    }

    public static void updateDatabase(Context context, String path) {
        Database db = DaoOpenHelper.getDb(context);

        try {
            db.execSQL("ATTACH '" + path + "' AS EXTDB");
            db.execSQL("REPLACE INTO MS_LOOKUP SELECT * FROM EXTDB.MS_LOOKUP");
            db.execSQL("REPLACE INTO MS_MIGRATION SELECT * FROM EXTDB.MS_MIGRATION");
            db.execSQL("REPLACE INTO MS_BLACKLIST SELECT * FROM EXTDB.MS_BLACKLIST");
            db.execSQL("REPLACE INTO MS_PO SELECT * FROM EXTDB.MS_PO");
            db.execSQL("REPLACE INTO MS_RULES SELECT * FROM EXTDB.MS_RULES");
            db.execSQL("REPLACE INTO MS_PO_ASSET SELECT * FROM EXTDB.MS_PO_ASSET");
            db.execSQL("REPLACE INTO MS_PO_DEALER SELECT * FROM EXTDB.MS_PO_DEALER");
            db.execSQL("REPLACE INTO MS_TMP_RULE SELECT * FROM EXTDB.MS_TMP_RULE");
            db.execSQL("REPLACE INTO MS_SYNC SELECT * FROM EXTDB.MS_SYNC");
            db.execSQL("DETACH 'EXTDB'");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void copyFiles(Context context) {
        //
    }

    public static void resetSession(Activity activity, Class tClass) {
        Helper.clearSession(activity);
        Intent login = new Intent(activity, tClass);
        login.setFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION);
        login.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        activity.startActivity(login);
        activity.finish();
    }

    public static boolean isAlwaysFinishActivities(Context context) {
        int alwaysFinishActivitiesInt = 0;
        if (Build.VERSION.SDK_INT >= 17) {
            alwaysFinishActivitiesInt = Settings.System.getInt(context.getContentResolver(), Settings.Global.ALWAYS_FINISH_ACTIVITIES, 0);
        } else {
            alwaysFinishActivitiesInt = Settings.System.getInt(context.getContentResolver(), Settings.System.ALWAYS_FINISH_ACTIVITIES, 0);
        }

        return alwaysFinishActivitiesInt == 1;
    }

    public static void alwaysFinishActivityState(Context context, boolean state) {
        int mode = (state) ? 1 : 0;
        if (Build.VERSION.SDK_INT >= 17) {
            Settings.System.putInt(context.getContentResolver(), Settings.Global.ALWAYS_FINISH_ACTIVITIES, mode);
        } else {
            Settings.System.putInt(context.getContentResolver(), Settings.System.ALWAYS_FINISH_ACTIVITIES, mode);
        }
    }

    //Method for read developer options state
    @TargetApi(Build.VERSION_CODES.JELLY_BEAN)
    public static boolean isDevMode(Context context) {
        int mode = 0;

        if (Build.VERSION.SDK_INT >= 17) {
            mode = Settings.Secure.getInt(context.getContentResolver(), Settings.Global.DEVELOPMENT_SETTINGS_ENABLED, 0);
        } else {
            mode = Settings.Secure.getInt(context.getContentResolver(), Settings.Secure.DEVELOPMENT_SETTINGS_ENABLED, 0);
        }

        return mode == 1;
    }

}