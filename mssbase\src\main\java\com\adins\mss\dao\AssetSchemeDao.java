package com.adins.mss.dao;

import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;

import com.adins.mss.dao.AssetScheme;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "MS_ASSET_SCHEME".
*/
public class AssetSchemeDao extends AbstractDao<AssetScheme, Long> {

    public static final String TABLENAME = "MS_ASSET_SCHEME";

    /**
     * Properties of entity AssetScheme.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Id = new Property(0, long.class, "id", true, "ID");
        public final static Property Asset_scheme_id = new Property(1, int.class, "asset_scheme_id", false, "ASSET_SCHEME_ID");
        public final static Property Type_code = new Property(2, String.class, "type_code", false, "TYPE_CODE");
        public final static Property Type_name = new Property(3, String.class, "type_name", false, "TYPE_NAME");
        public final static Property Is_deleted = new Property(4, Integer.class, "is_deleted", false, "IS_DELETED");
        public final static Property Dtm_upd = new Property(5, java.util.Date.class, "dtm_upd", false, "DTM_UPD");
    };


    public AssetSchemeDao(DaoConfig config) {
        super(config);
    }
    
    public AssetSchemeDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"MS_ASSET_SCHEME\" (" + //
                "\"ID\" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL ," + // 0: id
                "\"ASSET_SCHEME_ID\" INTEGER NOT NULL ," + // 1: asset_scheme_id
                "\"TYPE_CODE\" TEXT NOT NULL ," + // 2: type_code
                "\"TYPE_NAME\" TEXT NOT NULL ," + // 3: type_name
                "\"IS_DELETED\" INTEGER," + // 4: is_deleted
                "\"DTM_UPD\" INTEGER);"); // 5: dtm_upd
        // Add Indexes
        db.execSQL("CREATE INDEX " + constraint + "IDX_MS_ASSET_SCHEME_TYPE_CODE ON MS_ASSET_SCHEME" +
                " (\"TYPE_CODE\");");
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"MS_ASSET_SCHEME\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, AssetScheme entity) {
        stmt.clearBindings();
        stmt.bindLong(1, entity.getId());
        stmt.bindLong(2, entity.getAsset_scheme_id());
        stmt.bindString(3, entity.getType_code());
        stmt.bindString(4, entity.getType_name());
 
        Integer is_deleted = entity.getIs_deleted();
        if (is_deleted != null) {
            stmt.bindLong(5, is_deleted);
        }
 
        java.util.Date dtm_upd = entity.getDtm_upd();
        if (dtm_upd != null) {
            stmt.bindLong(6, dtm_upd.getTime());
        }
    }

    /** @inheritdoc */
    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.getLong(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public AssetScheme readEntity(Cursor cursor, int offset) {
        AssetScheme entity = new AssetScheme( //
            cursor.getLong(offset + 0), // id
            cursor.getInt(offset + 1), // asset_scheme_id
            cursor.getString(offset + 2), // type_code
            cursor.getString(offset + 3), // type_name
            cursor.isNull(offset + 4) ? null : cursor.getInt(offset + 4), // is_deleted
            cursor.isNull(offset + 5) ? null : new java.util.Date(cursor.getLong(offset + 5)) // dtm_upd
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, AssetScheme entity, int offset) {
        entity.setId(cursor.getLong(offset + 0));
        entity.setAsset_scheme_id(cursor.getInt(offset + 1));
        entity.setType_code(cursor.getString(offset + 2));
        entity.setType_name(cursor.getString(offset + 3));
        entity.setIs_deleted(cursor.isNull(offset + 4) ? null : cursor.getInt(offset + 4));
        entity.setDtm_upd(cursor.isNull(offset + 5) ? null : new java.util.Date(cursor.getLong(offset + 5)));
     }
    
    /** @inheritdoc */
    @Override
    protected Long updateKeyAfterInsert(AssetScheme entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    /** @inheritdoc */
    @Override
    public Long getKey(AssetScheme entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
}
