package com.adins.mss.base.dynamicform.form.models;

import com.adins.mss.foundation.http.MssRequestType;
import com.google.gson.annotations.SerializedName;

import java.util.Map;

public class GetDSRRequest extends MssRequestType {
    @SerializedName("filter") private Map<String, String> filter;
    @SerializedName("formName") private String formName;
    @SerializedName("uuid_task_h") private String uuidTaskH;

    public Map<String, String> getFilter() {
        return filter;
    }

    public void setFilter(Map<String, String> filter) {
        this.filter = filter;
    }

    public String getFormName() {
        return formName;
    }

    public void setFormName(String formName) {
        this.formName = formName;
    }

    public String getUuidTaskH() {
        return uuidTaskH;
    }

    public void setUuidTaskH(String uuidTaskH) {
        this.uuidTaskH = uuidTaskH;
    }
}

