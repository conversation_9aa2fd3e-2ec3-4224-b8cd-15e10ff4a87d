package com.adins.mss.dao;

import com.adins.mss.dao.DaoSession;
import de.greenrobot.dao.DaoException;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "TR_RECEIPTVOUCHER".
 */
public class ReceiptVoucher {

    /** Not-null value. */
     @SerializedName("uuid_rv_number")
    private String uuid_receipt_voucher;
     @SerializedName("status_rv")
    private String rv_status;
     @SerializedName("rv_number")
    private String rv_number;
     @SerializedName("usr_crt")
    private String usr_crt;
     @SerializedName("dtm_crt")
    private java.util.Date dtm_crt;
     @SerializedName("dtm_use")
    private java.util.Date dtm_use;
     @SerializedName("uuid_user")
    private String uuid_user;
     @SerializedName("uuid_task_h")
    private String uuid_task_h;

    /** Used to resolve relations */
    private transient DaoSession daoSession;

    /** Used for active entity operations. */
    private transient ReceiptVoucherDao myDao;

    private User user;
    private String user__resolvedKey;

    private TaskH taskH;
    private String taskH__resolvedKey;


    public ReceiptVoucher() {
    }

    public ReceiptVoucher(String uuid_receipt_voucher) {
        this.uuid_receipt_voucher = uuid_receipt_voucher;
    }

    public ReceiptVoucher(String uuid_receipt_voucher, String rv_status, String rv_number, String usr_crt, java.util.Date dtm_crt, java.util.Date dtm_use, String uuid_user, String uuid_task_h) {
        this.uuid_receipt_voucher = uuid_receipt_voucher;
        this.rv_status = rv_status;
        this.rv_number = rv_number;
        this.usr_crt = usr_crt;
        this.dtm_crt = dtm_crt;
        this.dtm_use = dtm_use;
        this.uuid_user = uuid_user;
        this.uuid_task_h = uuid_task_h;
    }

    /** called by internal mechanisms, do not call yourself. */
    public void __setDaoSession(DaoSession daoSession) {
        this.daoSession = daoSession;
        myDao = daoSession != null ? daoSession.getReceiptVoucherDao() : null;
    }

    /** Not-null value. */
    public String getUuid_receipt_voucher() {
        return uuid_receipt_voucher;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_receipt_voucher(String uuid_receipt_voucher) {
        this.uuid_receipt_voucher = uuid_receipt_voucher;
    }

    public String getRv_status() {
        return rv_status;
    }

    public void setRv_status(String rv_status) {
        this.rv_status = rv_status;
    }

    public String getRv_number() {
        return rv_number;
    }

    public void setRv_number(String rv_number) {
        this.rv_number = rv_number;
    }

    public String getUsr_crt() {
        return usr_crt;
    }

    public void setUsr_crt(String usr_crt) {
        this.usr_crt = usr_crt;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public java.util.Date getDtm_use() {
        return dtm_use;
    }

    public void setDtm_use(java.util.Date dtm_use) {
        this.dtm_use = dtm_use;
    }

    public String getUuid_user() {
        return uuid_user;
    }

    public void setUuid_user(String uuid_user) {
        this.uuid_user = uuid_user;
    }

    public String getUuid_task_h() {
        return uuid_task_h;
    }

    public void setUuid_task_h(String uuid_task_h) {
        this.uuid_task_h = uuid_task_h;
    }

    /** To-one relationship, resolved on first access. */
    public User getUser() {
        String __key = this.uuid_user;
        if (user__resolvedKey == null || user__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            UserDao targetDao = daoSession.getUserDao();
            User userNew = targetDao.load(__key);
            synchronized (this) {
                user = userNew;
            	user__resolvedKey = __key;
            }
        }
        return user;
    }

    public void setUser(User user) {
        synchronized (this) {
            this.user = user;
            uuid_user = user == null ? null : user.getUuid_user();
            user__resolvedKey = uuid_user;
        }
    }

    /** To-one relationship, resolved on first access. */
    public TaskH getTaskH() {
        String __key = this.uuid_task_h;
        if (taskH__resolvedKey == null || taskH__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            TaskHDao targetDao = daoSession.getTaskHDao();
            TaskH taskHNew = targetDao.load(__key);
            synchronized (this) {
                taskH = taskHNew;
            	taskH__resolvedKey = __key;
            }
        }
        return taskH;
    }

    public void setTaskH(TaskH taskH) {
        synchronized (this) {
            this.taskH = taskH;
            uuid_task_h = taskH == null ? null : taskH.getUuid_task_h();
            taskH__resolvedKey = uuid_task_h;
        }
    }

    /** Convenient call for {@link AbstractDao#delete(Object)}. Entity must attached to an entity context. */
    public void delete() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.delete(this);
    }

    /** Convenient call for {@link AbstractDao#update(Object)}. Entity must attached to an entity context. */
    public void update() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.update(this);
    }

    /** Convenient call for {@link AbstractDao#refresh(Object)}. Entity must attached to an entity context. */
    public void refresh() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.refresh(this);
    }

}
