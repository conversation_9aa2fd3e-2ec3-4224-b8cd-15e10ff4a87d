// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext.kotlin_version = '1.3.10'
    repositories {
        google()
        maven {
            url 'http://mss-webdev-svr.ad-ins.com:8081/artifactory/libs-release'
        }
        maven {
            url 'https://jitpack.io'
        }
        maven {
            url 'https://dl.bintray.com/firebase/gradle/'
        }
        jcenter()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:3.4.1'
        classpath 'com.google.gms:google-services:4.3.3'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.3.0'
        classpath "org.sonarsource.scanner.gradle:sonarqube-gradle-plugin:2.6.2"
        classpath "org.jacoco:org.jacoco.core:0.8.6"

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}
apply plugin: 'org.sonarqube'
sonarqube
        {
            properties
                    {
                        property "sonar.projectName", "WOMFMSS_ANDROID"
                        property "sonar.projectKey", "WOMFMSS_ANDROID"
                        property "sonar.language", "java"
                        property "sonar.sources", "src"
                        property "sonar.binaries", "mssbase/build/generated,msssvy/build/generated"
                        property "sonar.sourceEncoding", "UTF-8"
                        property "sonar.login", "****************************************"
                    }
        }

allprojects {
    apply plugin: 'jacoco'

    repositories {
        google()
        maven {
            url 'http://mss-webdev-svr.ad-ins.com:8081/artifactory/libs-release'
        }
        jcenter {
            url "https://jcenter.bintray.com/"
        }
        flatDir {
            dirs 'libs'
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
