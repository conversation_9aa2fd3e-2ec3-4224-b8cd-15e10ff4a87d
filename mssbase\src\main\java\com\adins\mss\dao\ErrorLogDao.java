package com.adins.mss.dao;

import java.util.List;
import java.util.ArrayList;
import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.SqlUtils;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;
import de.greenrobot.dao.query.Query;
import de.greenrobot.dao.query.QueryBuilder;

import com.adins.mss.dao.ErrorLog;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TR_ERROR_LOG".
*/
public class ErrorLogDao extends AbstractDao<ErrorLog, String> {

    public static final String TABLENAME = "TR_ERROR_LOG";

    /**
     * Properties of entity ErrorLog.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_error_log = new Property(0, String.class, "uuid_error_log", true, "UUID_ERROR_LOG");
        public final static Property Error_description = new Property(1, String.class, "error_description", false, "ERROR_DESCRIPTION");
        public final static Property Device_name = new Property(2, String.class, "device_name", false, "DEVICE_NAME");
        public final static Property Dtm_activity = new Property(3, java.util.Date.class, "dtm_activity", false, "DTM_ACTIVITY");
        public final static Property Uuid_user = new Property(4, String.class, "uuid_user", false, "UUID_USER");
        public final static Property Task_id = new Property(5, String.class, "task_id", false, "TASK_ID");
    };

    private DaoSession daoSession;

    private Query<ErrorLog> user_ErrorLogListQuery;
    private Query<ErrorLog> taskH_ErrorLogListQuery;

    public ErrorLogDao(DaoConfig config) {
        super(config);
    }
    
    public ErrorLogDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TR_ERROR_LOG\" (" + //
                "\"UUID_ERROR_LOG\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_error_log
                "\"ERROR_DESCRIPTION\" TEXT," + // 1: error_description
                "\"DEVICE_NAME\" TEXT," + // 2: device_name
                "\"DTM_ACTIVITY\" INTEGER," + // 3: dtm_activity
                "\"UUID_USER\" TEXT," + // 4: uuid_user
                "\"TASK_ID\" TEXT);"); // 5: task_id
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TR_ERROR_LOG\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, ErrorLog entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_error_log());
 
        String error_description = entity.getError_description();
        if (error_description != null) {
            stmt.bindString(2, error_description);
        }
 
        String device_name = entity.getDevice_name();
        if (device_name != null) {
            stmt.bindString(3, device_name);
        }
 
        java.util.Date dtm_activity = entity.getDtm_activity();
        if (dtm_activity != null) {
            stmt.bindLong(4, dtm_activity.getTime());
        }
 
        String uuid_user = entity.getUuid_user();
        if (uuid_user != null) {
            stmt.bindString(5, uuid_user);
        }
 
        String task_id = entity.getTask_id();
        if (task_id != null) {
            stmt.bindString(6, task_id);
        }
    }

    @Override
    protected void attachEntity(ErrorLog entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public ErrorLog readEntity(Cursor cursor, int offset) {
        ErrorLog entity = new ErrorLog( //
            cursor.getString(offset + 0), // uuid_error_log
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // error_description
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // device_name
            cursor.isNull(offset + 3) ? null : new java.util.Date(cursor.getLong(offset + 3)), // dtm_activity
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // uuid_user
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5) // task_id
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, ErrorLog entity, int offset) {
        entity.setUuid_error_log(cursor.getString(offset + 0));
        entity.setError_description(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setDevice_name(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setDtm_activity(cursor.isNull(offset + 3) ? null : new java.util.Date(cursor.getLong(offset + 3)));
        entity.setUuid_user(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setTask_id(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(ErrorLog entity, long rowId) {
        return entity.getUuid_error_log();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(ErrorLog entity) {
        if(entity != null) {
            return entity.getUuid_error_log();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
    /** Internal query to resolve the "errorLogList" to-many relationship of User. */
    public List<ErrorLog> _queryUser_ErrorLogList(String uuid_user) {
        synchronized (this) {
            if (user_ErrorLogListQuery == null) {
                QueryBuilder<ErrorLog> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_user.eq(null));
                user_ErrorLogListQuery = queryBuilder.build();
            }
        }
        Query<ErrorLog> query = user_ErrorLogListQuery.forCurrentThread();
        query.setParameter(0, uuid_user);
        return query.list();
    }

    /** Internal query to resolve the "errorLogList" to-many relationship of TaskH. */
    public List<ErrorLog> _queryTaskH_ErrorLogList(String task_id) {
        synchronized (this) {
            if (taskH_ErrorLogListQuery == null) {
                QueryBuilder<ErrorLog> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Task_id.eq(null));
                taskH_ErrorLogListQuery = queryBuilder.build();
            }
        }
        Query<ErrorLog> query = taskH_ErrorLogListQuery.forCurrentThread();
        query.setParameter(0, task_id);
        return query.list();
    }

    private String selectDeep;

    protected String getSelectDeep() {
        if (selectDeep == null) {
            StringBuilder builder = new StringBuilder("SELECT ");
            SqlUtils.appendColumns(builder, "T", getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T0", daoSession.getUserDao().getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T1", daoSession.getTaskHDao().getAllColumns());
            builder.append(" FROM TR_ERROR_LOG T");
            builder.append(" LEFT JOIN MS_USER T0 ON T.\"UUID_USER\"=T0.\"UUID_USER\"");
            builder.append(" LEFT JOIN TR_TASK_H T1 ON T.\"TASK_ID\"=T1.\"UUID_TASK_H\"");
            builder.append(' ');
            selectDeep = builder.toString();
        }
        return selectDeep;
    }
    
    protected ErrorLog loadCurrentDeep(Cursor cursor, boolean lock) {
        ErrorLog entity = loadCurrent(cursor, 0, lock);
        int offset = getAllColumns().length;

        User user = loadCurrentOther(daoSession.getUserDao(), cursor, offset);
        entity.setUser(user);
        offset += daoSession.getUserDao().getAllColumns().length;

        TaskH taskH = loadCurrentOther(daoSession.getTaskHDao(), cursor, offset);
        entity.setTaskH(taskH);

        return entity;    
    }

    public ErrorLog loadDeep(Long key) {
        assertSinglePk();
        if (key == null) {
            return null;
        }

        StringBuilder builder = new StringBuilder(getSelectDeep());
        builder.append("WHERE ");
        SqlUtils.appendColumnsEqValue(builder, "T", getPkColumns());
        String sql = builder.toString();
        
        String[] keyArray = new String[] { key.toString() };
        Cursor cursor = db.rawQuery(sql, keyArray);
        
        try {
            boolean available = cursor.moveToFirst();
            if (!available) {
                return null;
            } else if (!cursor.isLast()) {
                throw new IllegalStateException("Expected unique result, but count was " + cursor.getCount());
            }
            return loadCurrentDeep(cursor, true);
        } finally {
            cursor.close();
        }
    }
    
    /** Reads all available rows from the given cursor and returns a list of new ImageTO objects. */
    public List<ErrorLog> loadAllDeepFromCursor(Cursor cursor) {
        int count = cursor.getCount();
        List<ErrorLog> list = new ArrayList<ErrorLog>(count);
        
        if (cursor.moveToFirst()) {
            if (identityScope != null) {
                identityScope.lock();
                identityScope.reserveRoom(count);
            }
            try {
                do {
                    list.add(loadCurrentDeep(cursor, false));
                } while (cursor.moveToNext());
            } finally {
                if (identityScope != null) {
                    identityScope.unlock();
                }
            }
        }
        return list;
    }
    
    protected List<ErrorLog> loadDeepAllAndCloseCursor(Cursor cursor) {
        try {
            return loadAllDeepFromCursor(cursor);
        } finally {
            cursor.close();
        }
    }
    

    /** A raw-style query where you can pass any WHERE clause and arguments. */
    public List<ErrorLog> queryDeep(String where, String... selectionArg) {
        Cursor cursor = db.rawQuery(getSelectDeep() + where, selectionArg);
        return loadDeepAllAndCloseCursor(cursor);
    }
 
}
