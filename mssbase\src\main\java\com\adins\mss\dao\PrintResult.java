package com.adins.mss.dao;

import com.adins.mss.dao.DaoSession;
import de.greenrobot.dao.DaoException;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "TR_PRINTRESULT".
 */
public class PrintResult {

    /** Not-null value. */
     @SerializedName("uuid_print_result")
    private String uuid_print_result;
     @SerializedName("dtm_crt_server")
    private java.util.Date dtm_crt_server;
     @SerializedName("label")
    private String label;
     @SerializedName("value")
    private String value;
     @SerializedName("print_type_id")
    private String print_type_id;
     @SerializedName("usr_crt")
    private String usr_crt;
     @SerializedName("dtm_crt")
    private java.util.Date dtm_crt;
     @SerializedName("uuid_task_h")
    private String uuid_task_h;

    /** Used to resolve relations */
    private transient DaoSession daoSession;

    /** Used for active entity operations. */
    private transient PrintResultDao myDao;

    private User user;
    private String user__resolvedKey;


    public PrintResult() {
    }

    public PrintResult(String uuid_print_result) {
        this.uuid_print_result = uuid_print_result;
    }

    public PrintResult(String uuid_print_result, java.util.Date dtm_crt_server, String label, String value, String print_type_id, String usr_crt, java.util.Date dtm_crt, String uuid_task_h) {
        this.uuid_print_result = uuid_print_result;
        this.dtm_crt_server = dtm_crt_server;
        this.label = label;
        this.value = value;
        this.print_type_id = print_type_id;
        this.usr_crt = usr_crt;
        this.dtm_crt = dtm_crt;
        this.uuid_task_h = uuid_task_h;
    }

    /** called by internal mechanisms, do not call yourself. */
    public void __setDaoSession(DaoSession daoSession) {
        this.daoSession = daoSession;
        myDao = daoSession != null ? daoSession.getPrintResultDao() : null;
    }

    /** Not-null value. */
    public String getUuid_print_result() {
        return uuid_print_result;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_print_result(String uuid_print_result) {
        this.uuid_print_result = uuid_print_result;
    }

    public java.util.Date getDtm_crt_server() {
        return dtm_crt_server;
    }

    public void setDtm_crt_server(java.util.Date dtm_crt_server) {
        this.dtm_crt_server = dtm_crt_server;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getPrint_type_id() {
        return print_type_id;
    }

    public void setPrint_type_id(String print_type_id) {
        this.print_type_id = print_type_id;
    }

    public String getUsr_crt() {
        return usr_crt;
    }

    public void setUsr_crt(String usr_crt) {
        this.usr_crt = usr_crt;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public String getUuid_task_h() {
        return uuid_task_h;
    }

    public void setUuid_task_h(String uuid_task_h) {
        this.uuid_task_h = uuid_task_h;
    }

    /** To-one relationship, resolved on first access. */
    public User getUser() {
        String __key = this.uuid_task_h;
        if (user__resolvedKey == null || user__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            UserDao targetDao = daoSession.getUserDao();
            User userNew = targetDao.load(__key);
            synchronized (this) {
                user = userNew;
            	user__resolvedKey = __key;
            }
        }
        return user;
    }

    public void setUser(User user) {
        synchronized (this) {
            this.user = user;
            uuid_task_h = user == null ? null : user.getUuid_user();
            user__resolvedKey = uuid_task_h;
        }
    }

    /** Convenient call for {@link AbstractDao#delete(Object)}. Entity must attached to an entity context. */
    public void delete() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.delete(this);
    }

    /** Convenient call for {@link AbstractDao#update(Object)}. Entity must attached to an entity context. */
    public void update() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.update(this);
    }

    /** Convenient call for {@link AbstractDao#refresh(Object)}. Entity must attached to an entity context. */
    public void refresh() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.refresh(this);
    }

}
