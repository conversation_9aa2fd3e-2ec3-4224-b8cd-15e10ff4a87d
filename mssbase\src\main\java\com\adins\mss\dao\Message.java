package com.adins.mss.dao;

import java.util.List;
import com.adins.mss.dao.DaoSession;
import de.greenrobot.dao.DaoException;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "TR_MESSAGE".
 */
public class Message {

    /** Not-null value. */
     @SerializedName("uuid_message")
    private String uuid_message;
     @SerializedName("message")
    private String message;
     @SerializedName("sender_id")
    private String sender_id;
     @SerializedName("sender_name")
    private String sender_name;
     @SerializedName("dtm_crt_server")
    private java.util.Date dtm_crt_server;
     @SerializedName("time_read")
    private java.util.Date time_read;
     @SerializedName("usr_crt")
    private String usr_crt;
     @SerializedName("dtm_crt")
    private java.util.Date dtm_crt;
     @SerializedName("uuid_user")
    private String uuid_user;

    /** Used to resolve relations */
    private transient DaoSession daoSession;

    /** Used for active entity operations. */
    private transient MessageDao myDao;

    private User user;
    private String user__resolvedKey;

    private List<Timeline> timelineList;

    public Message() {
    }

    public Message(String uuid_message) {
        this.uuid_message = uuid_message;
    }

    public Message(String uuid_message, String message, String sender_id, String sender_name, java.util.Date dtm_crt_server, java.util.Date time_read, String usr_crt, java.util.Date dtm_crt, String uuid_user) {
        this.uuid_message = uuid_message;
        this.message = message;
        this.sender_id = sender_id;
        this.sender_name = sender_name;
        this.dtm_crt_server = dtm_crt_server;
        this.time_read = time_read;
        this.usr_crt = usr_crt;
        this.dtm_crt = dtm_crt;
        this.uuid_user = uuid_user;
    }

    /** called by internal mechanisms, do not call yourself. */
    public void __setDaoSession(DaoSession daoSession) {
        this.daoSession = daoSession;
        myDao = daoSession != null ? daoSession.getMessageDao() : null;
    }

    /** Not-null value. */
    public String getUuid_message() {
        return uuid_message;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_message(String uuid_message) {
        this.uuid_message = uuid_message;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getSender_id() {
        return sender_id;
    }

    public void setSender_id(String sender_id) {
        this.sender_id = sender_id;
    }

    public String getSender_name() {
        return sender_name;
    }

    public void setSender_name(String sender_name) {
        this.sender_name = sender_name;
    }

    public java.util.Date getDtm_crt_server() {
        return dtm_crt_server;
    }

    public void setDtm_crt_server(java.util.Date dtm_crt_server) {
        this.dtm_crt_server = dtm_crt_server;
    }

    public java.util.Date getTime_read() {
        return time_read;
    }

    public void setTime_read(java.util.Date time_read) {
        this.time_read = time_read;
    }

    public String getUsr_crt() {
        return usr_crt;
    }

    public void setUsr_crt(String usr_crt) {
        this.usr_crt = usr_crt;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public String getUuid_user() {
        return uuid_user;
    }

    public void setUuid_user(String uuid_user) {
        this.uuid_user = uuid_user;
    }

    /** To-one relationship, resolved on first access. */
    public User getUser() {
        String __key = this.uuid_user;
        if (user__resolvedKey == null || user__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            UserDao targetDao = daoSession.getUserDao();
            User userNew = targetDao.load(__key);
            synchronized (this) {
                user = userNew;
            	user__resolvedKey = __key;
            }
        }
        return user;
    }

    public void setUser(User user) {
        synchronized (this) {
            this.user = user;
            uuid_user = user == null ? null : user.getUuid_user();
            user__resolvedKey = uuid_user;
        }
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<Timeline> getTimelineList() {
        if (timelineList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            TimelineDao targetDao = daoSession.getTimelineDao();
            List<Timeline> timelineListNew = targetDao._queryMessage_TimelineList(uuid_message);
            synchronized (this) {
                if(timelineList == null) {
                    timelineList = timelineListNew;
                }
            }
        }
        return timelineList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetTimelineList() {
        timelineList = null;
    }

    /** Convenient call for {@link AbstractDao#delete(Object)}. Entity must attached to an entity context. */
    public void delete() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.delete(this);
    }

    /** Convenient call for {@link AbstractDao#update(Object)}. Entity must attached to an entity context. */
    public void update() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.update(this);
    }

    /** Convenient call for {@link AbstractDao#refresh(Object)}. Entity must attached to an entity context. */
    public void refresh() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.refresh(this);
    }

}
