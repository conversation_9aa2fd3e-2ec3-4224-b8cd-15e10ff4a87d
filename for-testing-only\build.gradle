apply plugin: 'com.android.application'

android {
    compileSdkVersion 25
    buildToolsVersion '25.0.0'

    defaultConfig {
        applicationId "com.adins.example"
        minSdkVersion 15
        targetSdkVersion 23
        versionCode 1
        versionName "1.0"
        multiDexEnabled true
        useLibrary 'org.apache.http.legacy'
    }
    dexOptions {
        javaMaxHeapSize "4g"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    testCompile 'junit:junit:4.12'
    compile 'com.android.support:appcompat-v7:23.4.0'
    compile project(':mssbase')
}
