package com.adins.mss.base.todolist.form;

import com.adins.mss.foundation.http.MssRequestType;
import com.google.gson.annotations.SerializedName;

public class JsonRequestTaskList extends MssRequestType {
    @SerializedName("username")
    String username;
    @SerializedName("password")
    String password;
    @SerializedName("flagFreshInstall")
    String flagFreshInstall;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getFlagFreshInstall() {
        return flagFreshInstall;
    }

    public void setFlagFreshInstall(String flagFreshInstall) {
        this.flagFreshInstall = flagFreshInstall;
    }

}
