package com.adins.mss.foundation.liveness;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Matrix;
import android.hardware.camera2.CameraAccessException;
import android.hardware.camera2.CameraCharacteristics;
import android.hardware.camera2.CameraManager;
import android.media.Image;
import android.media.ImageReader;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.util.DisplayMetrics;
import android.util.Log;
import android.util.Size;
import android.view.Surface;
import android.view.View;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.util.LocaleHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.foundation.camerainapp.CameraActivity;
import com.adins.mss.foundation.camerainapp.helper.FileUtil;
import com.adins.mss.foundation.liveness.helper.CameraConnectionFragment;
import com.adins.mss.foundation.liveness.helper.ImageUtils;
import com.google.android.gms.tasks.OnFailureListener;
import com.google.android.gms.tasks.OnSuccessListener;
import com.google.android.gms.tasks.Task;
import com.google.mlkit.vision.common.InputImage;
import com.google.mlkit.vision.face.Face;
import com.google.mlkit.vision.face.FaceDetection;
import com.google.mlkit.vision.face.FaceDetector;
import com.google.mlkit.vision.face.FaceDetectorOptions;

import java.io.File;
import java.nio.ByteBuffer;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

@RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
public class LivenessActivity extends AppCompatActivity implements ImageReader.OnImageAvailableListener {

    private GraphicOverlay mGraphicOverlay;
    private ImageView mImagePreview;
    private TextView mTimerOverlay, mTextOverlay, mSecondaryTextOverlay;
    private ImageButton mBtnSwitchFacing;
    private Button btnSavePicture, btnCancel;
    private LinearLayout livenessOverlay;
    private RelativeLayout previewOverlay;

    /*
        resolution for camera
        If need to change make sure to select from supported characteristics
    */
    private final int resolutionWidth = 480,resolutionHeight = 640;
    private final float blinkThreshold = 0.6f;
    private CameraConnectionFragment fragment;
    private int cameraWidth = 0;
    private int previewHeight = 0,previewWidth = 0;
    private int sensorOrientation;

    private boolean isProcessingFrame = false, isTakingPicture = false;
    private boolean isValidatingLiveness = true;
    private boolean isBlinking = false;
    private int blinkCount = 0;
    private byte[][] yuvBytes = new byte[3][];
    private int[] rgbBytes = null;
    private int yRowStride;
    private Bitmap pictureBitmap;

    private int facingIndex = 1;

    private Handler mainHandler;
    private Timer t;
    private int timer = 60;
    private String curRuleStr = "";

    Map<String, Integer> livenessRule = new HashMap<>();
    Map<String, Boolean> livenessValidCheck = new HashMap<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_liveness);

        mGraphicOverlay = findViewById(R.id.graphic_overlay);
        mImagePreview = findViewById(R.id.iv_preview);
        mTimerOverlay = findViewById(R.id.timer_overlay);
        mTextOverlay = findViewById(R.id.text_overlay);
        mSecondaryTextOverlay = findViewById(R.id.secondary_text_overlay);
        mBtnSwitchFacing = findViewById(R.id.btn_switch_facing);
        livenessOverlay = findViewById(R.id.liveness_overlay);
        previewOverlay = findViewById(R.id.preview_overlay);
        btnSavePicture = findViewById(R.id.btn_save_picture);
        btnCancel = findViewById(R.id.btn_cancel);

        livenessOverlay.setVisibility(View.VISIBLE);
        previewOverlay.setVisibility(View.GONE);

        livenessRule.put("BLINK", 3);
        livenessValidCheck.put("BLINK", false);

        //Check for permission
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (checkSelfPermission(Manifest.permission.CAMERA) == PackageManager.PERMISSION_DENIED ) {
                ActivityCompat.requestPermissions(this, new String[]{
                        Manifest.permission.CAMERA}, 121);
            }else{
                setFragment(facingIndex);
            }
        } else {
            setFragment(facingIndex);
        }

        DisplayMetrics displayMetrics = new DisplayMetrics();
        getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
        cameraWidth = displayMetrics.widthPixels;

        mGraphicOverlay.setScaleFactor((float)cameraWidth/(float)resolutionWidth);
        mGraphicOverlay.setFacing(CameraCharacteristics.LENS_FACING_FRONT);

        if(isValidatingLiveness){
            mTextOverlay.setVisibility(View.VISIBLE);
        }

        mainHandler = new Handler(getApplicationContext().getMainLooper());

        mBtnSwitchFacing.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(facingIndex == 0){
                    //set facing front
                    facingIndex = 1;
                    mGraphicOverlay.setFacing(CameraCharacteristics.LENS_FACING_FRONT);
                } else if (facingIndex == 1){
                    //set facing back
                    facingIndex = 0;
                    mGraphicOverlay.setFacing(CameraCharacteristics.LENS_FACING_BACK);
                }

                setFragment(facingIndex);
            }
        });

        btnSavePicture.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                File file = FileUtil.bitmapToFileConverter(LivenessActivity.this, pictureBitmap);
                Uri path = Uri.fromFile(file);

                Intent intent = getIntent();
                if (null != path) {
                    intent.putExtra(CameraActivity.PICTURE_URI, path.toString());
                }
                Bundle bundle = new Bundle();
                intent.putExtras(bundle);
                setResult(Activity.RESULT_OK, intent);
                finish();
            }
        });

        btnCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //reinit liveness
                mBtnSwitchFacing.setVisibility(View.VISIBLE);
                mSecondaryTextOverlay.setVisibility(View.GONE);
                mImagePreview.setVisibility(View.GONE);
                livenessOverlay.setVisibility(View.VISIBLE);
                previewOverlay.setVisibility(View.GONE);

                for (Map.Entry<String, Boolean> entry : livenessValidCheck.entrySet()) {
                    entry.setValue(false);
                }

                isValidatingLiveness = true;

                setFragment(facingIndex);

                setLivenessTimer();

                refreshTextOverlay();
            }
        });

        setLivenessTimer();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        t.cancel();
    }

    @Override
    protected void attachBaseContext(Context newBase) {
        Context context = newBase;
        Locale locale;
        try {
            if (GlobalData.getSharedGlobalData().getLocale() != null) {
                locale = new Locale(GlobalData.getSharedGlobalData().getLocale());
            } else {
                locale = new Locale(LocaleHelper.ENGLSIH);
            }
            context = LocaleHelper.wrap(newBase, locale);
        } catch (Exception e) {
            locale = new Locale(LocaleHelper.ENGLSIH);
            context = LocaleHelper.wrap(newBase, locale);
        } finally {
            super.attachBaseContext(context);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            setFragment(facingIndex);
        } else {
            finish();
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    protected void setFragment(int cameraIdIndex) {
        final CameraManager manager = (CameraManager) getSystemService(Context.CAMERA_SERVICE);
        String cameraId = null;
        try {
            //Camera front = "1", back = "0"
            cameraId = manager.getCameraIdList()[cameraIdIndex];

        } catch (CameraAccessException e) {
            e.printStackTrace();
        }
        CameraConnectionFragment camera2Fragment =
                CameraConnectionFragment.newInstance(
                        new CameraConnectionFragment.ConnectionCallback() {
                            @Override
                            public void onPreviewSizeChosen(final Size size, final int rotation) {
                                previewHeight = size.getHeight();
                                previewWidth = size.getWidth();
                                Log.d("tryOrientation","rotation: "+rotation+"   orientation: "+getScreenOrientation()+"  "+previewWidth+"   "+previewHeight);
                                sensorOrientation = rotation - getScreenOrientation();
                            }
                        },
                        this,
                        R.layout.liveness_fragment,
                        new Size(resolutionHeight, resolutionWidth));

        camera2Fragment.setCamera(cameraId);
        fragment = camera2Fragment;
        getFragmentManager().beginTransaction().replace(R.id.container, fragment).commit();
    }

    protected void unsetFragment(){
        getFragmentManager().beginTransaction().remove(fragment).commit();
    }

    private void setLivenessTimer(){
        mTimerOverlay.setVisibility(View.VISIBLE);

        t = new Timer();
        timer = 60;
        t.schedule(new TimerTask() {
            @Override
            public void run() {
                Log.d("timer", "LOOP" + timer);

                Runnable timmerRunnable = new Runnable() {
                    @Override
                    public void run() {
                        mTimerOverlay.setText(String.valueOf(timer--));
                    }
                };
                mainHandler.post(timmerRunnable);
                if(timer < 1){
                    this.cancel();
                    Runnable cancelRunnable = new Runnable() {
                        @Override
                        public void run() {
                            finish();
                        }
                    };
                    mainHandler.post(cancelRunnable);
                }
            }
        }, 0, 1000);
    }

    protected int getScreenOrientation() {
        switch (getWindowManager().getDefaultDisplay().getRotation()) {
            case Surface.ROTATION_270:
                return 270;
            case Surface.ROTATION_180:
                return 180;
            case Surface.ROTATION_90:
                return 90;
            default:
                return 0;
        }
    }

    @Override
    public void onImageAvailable(ImageReader reader) {
        // We need wait until we have some size from onPreviewSizeChosen
        if (previewWidth == 0 || previewHeight == 0) {
            return;
        }
        if (rgbBytes == null) {
            rgbBytes = new int[previewWidth * previewHeight];
        }
        try {
            final Image image = reader.acquireLatestImage();

            if (image == null) {
                return;
            }

            if (isProcessingFrame) {
                image.close();
            } else {
                isProcessingFrame = true;

                if(isValidatingLiveness){
                    runFaceContourDetection(image, sensorOrientation);
                } else if(isTakingPicture) {
                    takePicture(image);

                    isProcessingFrame = false;
                    isTakingPicture = false;
                    image.close();
                } else {
                    isProcessingFrame = false;
                    image.close();
                }

            }


        } catch (final Exception e) {
            Log.e("BITMAP",e.getMessage());
        }
    }

    private void runFaceContourDetection(final Image mediaImg, int rotation) {
        InputImage image = InputImage.fromMediaImage(mediaImg, rotation);

        if(Global.IS_DEV){
            Log.d("IMAGE", "Run detection");
            Log.d("IMAGE", "Height:" + image.getHeight());
            Log.d("IMAGE", "Width:" + image.getWidth());
        }

        FaceDetectorOptions options =
                new FaceDetectorOptions.Builder()
                        .setPerformanceMode(FaceDetectorOptions.PERFORMANCE_MODE_FAST)
                        .setLandmarkMode(FaceDetectorOptions.LANDMARK_MODE_ALL)
                        .setClassificationMode(FaceDetectorOptions.CLASSIFICATION_MODE_ALL)
                        .build();

        final FaceDetector detector = FaceDetection.getClient(options);

        detector.process(image)
                .addOnSuccessListener(
                        new OnSuccessListener<List<Face>>() {
                            @Override
                            public void onSuccess(List<Face> faces) {
                                isProcessingFrame = false;
                                Log.d("isProcessing", "Set False On Image Detect Success");

                                Log.d("IMAGE", "Found faces:" + faces.size());
                                processFaceContourDetectionResult(faces);

                                if(!faces.isEmpty()){
                                    Face face1 = faces.get(0);

                                    checkBlinking(face1);

                                    refreshTextOverlay();
                                }

                                mediaImg.close();
                            }
                        })
                .addOnFailureListener(
                        new OnFailureListener() {
                            @Override
                            public void onFailure(@NonNull Exception e) {
                                Log.e("IMAGE", e.getMessage());
                                e.printStackTrace();
                                isProcessingFrame = false;
                                Log.d("isProcessing", "Set False On Image detect fail");
                            }
                        });
    }

    private void processFaceContourDetectionResult(List<Face> faces) {
        // Task completed successfully
        if (faces.isEmpty()) {
            return;
        }
        mGraphicOverlay.clear();

        Face face = faces.get(0);
        float rotY = face.getHeadEulerAngleY();
        float rotZ = face.getHeadEulerAngleZ();
        Log.i("Rotation", rotY +"-"+rotZ);
        FaceContourGraphic faceGraphic = new FaceContourGraphic(mGraphicOverlay);
        mGraphicOverlay.add(faceGraphic);
        faceGraphic.updateFace(face);
    }

    private void takePicture(Image image){
        final Image.Plane[] planes = image.getPlanes();
        fillBytes(planes, yuvBytes);
        yRowStride = planes[0].getRowStride();
        final int uvRowStride = planes[1].getRowStride();
        final int uvPixelStride = planes[1].getPixelStride();

        ImageUtils.convertYUV420ToARGB8888(
                yuvBytes[0],
                yuvBytes[1],
                yuvBytes[2],
                previewWidth,
                previewHeight,
                yRowStride,
                uvRowStride,
                uvPixelStride,
                rgbBytes);

        pictureBitmap = Bitmap.createBitmap(previewWidth, previewHeight, Bitmap.Config.ARGB_8888);
        pictureBitmap.setPixels(rgbBytes, 0, previewWidth, 0, 0, previewWidth, previewHeight);

        //rotate bitmap
        Matrix matrix = new Matrix();
        matrix.postRotate(-90);
        pictureBitmap = Bitmap.createBitmap(pictureBitmap, 0, 0, pictureBitmap.getWidth(), pictureBitmap.getHeight(), matrix, true);

        if(pictureBitmap != null){
            Log.d("BITMAP", "bitmap is not null");

            Runnable setPreviewRunnable = new Runnable() {
                @Override
                public void run() {
                    //disable camera
                    unsetFragment();
                    //display preview
                    mImagePreview.setImageBitmap(pictureBitmap);
                    mImagePreview.setVisibility(View.VISIBLE);
                    //show preview overlay
                    livenessOverlay.setVisibility(View.GONE);
                    previewOverlay.setVisibility(View.VISIBLE);
                }
            };
            mainHandler.post(setPreviewRunnable);
        } else {
            Toast.makeText(getApplicationContext(), getString(R.string.liveness_take_picture_error), Toast.LENGTH_SHORT).show();
        }
    }

    private void refreshTextOverlay() {
        if(curRuleStr.isEmpty() && isValidatingLiveness){
            for (Map.Entry<String, Integer> entry : livenessRule.entrySet()) {
                if(!livenessValidCheck.get(entry.getKey())){
                    curRuleStr = entry.getKey();

                    switch (entry.getKey()){
                        case "BLINK":
                            initLivenessBlink();
                            break;
                        default:
                            break;
                    }
                }
            }

            if(curRuleStr.isEmpty()){
                mGraphicOverlay.clear();
                isValidatingLiveness = false;
                mTextOverlay.setText(getString(R.string.liveness_valid));

                mSecondaryTextOverlay.setVisibility(View.VISIBLE);
                mBtnSwitchFacing.setVisibility(View.INVISIBLE);

                //cancel main timer
                t.cancel();
                //start 5 second timer
                t = new Timer();
                timer = 4;
                t.schedule(new TimerTask() {
                    @Override
                    public void run() {
                        Runnable timmerRunnable = new Runnable() {
                            @Override
                            public void run() {
                            //update text on main thread
                            mTimerOverlay.setVisibility(View.GONE);
                            mTextOverlay.setText(getText(R.string.liveness_valid));
                            mSecondaryTextOverlay.setText(String.format(getString(R.string.liveness_take_picture), timer--));
                            }
                        };
                        mainHandler.post(timmerRunnable);
                        if(timer < 1){
                            this.cancel();
                            isTakingPicture = true;
                        }
                    }
                }, 0, 1000);
            }
        } else {
            boolean valid = false;

            switch (curRuleStr){
                case "BLINK":
                    valid = validateBlinkNTimes(livenessRule.get(curRuleStr));
                    break;
                default:
                    break;
            }

            if(valid){
                livenessValidCheck.put(curRuleStr, valid);
                curRuleStr = "";
            }
        }
    }

    private void initLivenessBlink(){
        blinkCount = 0;
        mTextOverlay.setText(String.format(getString(R.string.liveness_blink), blinkCount, livenessRule.get(curRuleStr)));
    }

    private boolean validateBlinkNTimes(int n){
        return n <= blinkCount;
    }


    private void checkBlinking(Face face){
        Log.d("BLINK", face.getLeftEyeOpenProbability() + " " + face.getRightEyeOpenProbability());
        if (face.getLeftEyeOpenProbability() < blinkThreshold && face.getRightEyeOpenProbability() < blinkThreshold){
            if(!isBlinking && curRuleStr.equals("BLINK")){
                isBlinking = true;
                blinkCount++;
                mTextOverlay.setText(String.format(getString(R.string.liveness_blink), blinkCount, livenessRule.get(curRuleStr)));
            }
        } else {
            isBlinking = false;
        }
    }

    protected void fillBytes(final Image.Plane[] planes, final byte[][] yuvBytes) {
        // Because of the variable row stride it's not possible to know in
        // advance the actual necessary dimensions of the yuv planes.
        for (int i = 0; i < planes.length; ++i) {
            final ByteBuffer buffer = planes[i].getBuffer();
            if (yuvBytes[i] == null) {
                yuvBytes[i] = new byte[buffer.capacity()];
            }
            buffer.get(yuvBytes[i]);
        }
    }
}