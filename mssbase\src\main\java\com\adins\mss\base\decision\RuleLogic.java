package com.adins.mss.base.decision;

import android.content.Context;

import com.adins.mss.foundation.questiongenerator.QuestionBean;

import java.io.File;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * <p>NEXT DEV:
 * <ul>
 * <li>simulator
 * <li>validation: redundancy, impossible condition
 * </ul>
 *
 * <p>Ground rules for Rule file:<ul>
 *  <li>.xls or .xlsx only
 *  <li>Default sheet executed is first sheet of file
 *  <li>File cannot be password protected
 * 	<li><b>DATA OBJECTS</b> definition is not necessary for executing,
 * 		but needed for non-developer/simulator
 *      to know what variables availables at runtime
 *  <li><b>DATA OBJECTS</b>' var name is accessed with prefix #
 *  <li>Have at least 1 <b>CONDITION</b> and 1 <b>ACTION</b>
 *  <li>First <b>CONDITION</b> must be at first column
 *  <li><b>CONDITION</b> will be evaluated from left to right
 *  <li>Relational Operators supported: ==, !=, &lt;, &lt;=, &gt;, &gt;=
 *  <li>User defined function for comparison <b>#like</b>(<code>#dataObjectsVar</code>, <code>#val</code>)
 *  <li>Logical Operators supported: and, or, not
 *  <li>Math Operators supported: *, /, +, -, %, ^
 *	<li><b>ACTION</b> will be executed from left to right
 *  <li><b>ACTION</b> can be value assignment or method execute
 *  	without argument or with value as one argument
 *  <li><b>ACTION</b> support multisheet execution,
 *  	by using '<b>this</b>' for referring objectName and '<b>addSheetname()</b>' for method.
 *  	Sheet name is case-insensitive.
 *  <li>Default <code>skipOnFirstApplied</code> = false or
 *  	You can declare with SKIP_ON_FIRST_APPLIED before CONDITION/ACTION row.
 *  	true = skipping data evaluation after first conditions met.
 *  	<br>Value accepted:
 *  	<br><b>boolean</b> <code>TRUE</code> | <code>FALSE</code>,
 *  	<br><b>String</b>	<code>"true"</code> | <code>"false"</code>,
 *  	<br><b>numeric</b> <code>1</code> | <code>0</code>
 *  <li>Value in data is referenced with <code>#val</code> variables
 *  <li>Always true condition / skip condition, use dash (<code>-</code>) character
 *  <li>Empty condition val will be treated with last val
 *  <li>Exec by RuleCode --> currDate >= startDate and latest version found.
 * </ul>
 * <AUTHOR>
 */
public interface RuleLogic {
	public static final String VAR_SKIP_ON_FIRST_APPLIED = "SKIP_ON_FIRST_APPLIED";
	public static final String CONDITION_STRING = "CONDITION";
	public static final String ACTION_STRING = "ACTION";

	/**
	 * @param file
	 * 		Rule file both .xls or .xlsx
	 * @param dataObjects
	 * 		variables used in rule evaluation and action
	 */
	public void executeRule(File file, Map<String, Object> dataObjects);

	/**
	 *
	 * @param is
	 * 		inputStream of rule's excel file
	 * @param dataObjects
	 * 		variables used in rule evaluation and action
	 */
	public void executeRule(InputStream is, Map<String, Object> dataObjects);

	/**
	 *
	 * @param ruleCode
	 * 		code in RlRule database
	 * @param dataObjects
	 * 		variables used in rule evaluation and action
	 */
	public void executeRule(String ruleCode, Map<String, Object> dataObjects);

	/**
	 * @param file
	 * 		Rule file both .xls or .xlsx
	 * @param sheetName
	 * 		jump to sheet
	 * @param dataObjects
	 * 		variables used in rule evaluation and action
	 */
	public void executeRule(File file, String sheetName, Map<String, Object> dataObjects);

	/**
	 *
	 * @param is
	 * 		inputStream of rule's excel file
	 * @param sheetName
	 * 		jump to sheet
	 * @param dataObjects
	 * 		variables used in rule evaluation and action
	 */
	public void executeRule(InputStream is, String sheetName, Map<String, Object> dataObjects);

	/**
	 *
	 * @param ruleCode
	 * 		code in RlRule database
	 * @param sheetName
	 * 		jump to sheet
	 * @param dataObjects
	 * 		variables used in rule evaluation and action
	 */
	public void executeRule(String ruleCode, String sheetName, Map<String, Object> dataObjects);

	/**
	 * Method for Calculate Scoring
	 * @param context
	 * @param ruleParam
	 * @return float
	 */
	public float calculateScore(Context context, RuleParam ruleParam);

	/**
	 * Method for Get default value Asset Prices
	 * @param context
	 * @param ruleParam
	 * @return double
	 */
	public double basePrice(Context context, RuleParam ruleParam);

	/**
	 * Method for validate Down Payment
	 * @param context
	 * @param ruleParam
	 * @return boolean
	 */
	public boolean validateDP(Context context, QuestionBean bean, RuleParam ruleParam);

	/**
	 * Method for validate Image and Drawing
	 * @param context
	 * @param ruleParam
	 * @return
	 */
	public List<String> validateTC(Context context, RuleParam ruleParam);

	public String scoreMatrix(Context context, Double score);
}
