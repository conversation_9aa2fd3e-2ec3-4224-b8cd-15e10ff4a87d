package com.adins.mss.base.dynamicform.form.questions;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Intent;
import android.os.AsyncTask;
import android.widget.Toast;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.dynamicform.Constant;
import com.adins.mss.base.dynamicform.form.FragmentQuestion;
import com.adins.mss.base.dynamicform.form.models.CriteriaParameter;
import com.adins.mss.base.dynamicform.form.models.LookupCriteriaBean;
import com.adins.mss.base.dynamicform.form.models.LookupOnlineRequest;
import com.adins.mss.base.dynamicform.form.models.ReviewResponse;
import com.adins.mss.base.dynamicform.form.questions.viewholder.LookupCriteriaOnlineActivity;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.http.KeyValue;
import com.adins.mss.foundation.questiongenerator.QuestionBean;

import java.util.ArrayList;
import java.util.List;

import static com.adins.mss.base.dynamicform.form.questions.viewholder.LookupCriteriaOnlineActivity.KEY_WITH_FILTER;

/**
 * Created by gigin.ginanjar on 07/10/2016.
 */

public class LookupAnswerTask extends AsyncTask<String, Void, String> {
    private final String defaultResultNoConnection = "{\"listField\":[{\"key\":\"-\",\"value\":\"-\"}],\"status\":{\"code\":0,\"message\":\"Success\"}}";
    private Activity activity;
    private ProgressDialog progressDialog;
    private String errMessage;
    private QuestionBean bean;
    private CriteriaParameter criteriaParameter;

    public LookupAnswerTask(Activity activity, QuestionBean bean, CriteriaParameter criteriaParameter) {
        this.activity = activity;
        this.bean = bean;
        this.criteriaParameter = criteriaParameter;
    }

    @Override
    protected void onPreExecute() {
        progressDialog = ProgressDialog.show(activity, "", activity.getString(R.string.please_wait), true);
    }

    @Override
    protected String doInBackground(String... params) {
        if (Tool.isInternetconnected(activity)) {
            String jsonRequest = getJsonRequest();
            String url = GlobalData.getSharedGlobalData().getURL_LOOKUP_ANSWER();
            boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
            boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
            HttpCryptedConnection httpConn = new HttpCryptedConnection(activity, encrypt, decrypt);
            HttpConnectionResult serverResult = null;
            try {
                serverResult = httpConn.requestToServer(url, jsonRequest, Global.DEFAULTCONNECTIONTIMEOUT);
            } catch (Exception e) {
                FireCrash.log(e);
                errMessage = activity.getString(R.string.msgConnectionFailed);
                return null;
            }
            if (serverResult != null && serverResult.isOK()) {
                String body = serverResult.getResult();
                return body;
            } else {
                errMessage = activity.getString(R.string.connection_failed);
            }
        } else {
            return defaultResultNoConnection;
        }

        return null;
    }

    @Override
    protected void onPostExecute(String result) {
        super.onPostExecute(result);
        if (progressDialog != null) {
            if (progressDialog.isShowing()) {
                progressDialog.dismiss();
            }
            progressDialog = null;
        }
        if (errMessage != null && !errMessage.isEmpty()) {
            Toast.makeText(activity, errMessage, Toast.LENGTH_SHORT).show();
        } else if (result != null) {
            processingResponseServer(result);

        }
    }

    private void processingResponseServer(String result) {
        processLuOnline(result);
    }

    private void processLuOnline(String result) {
        ReviewResponse response = GsonHelper.fromJson(result, ReviewResponse.class);
        if (response.getStatus().getCode() == 0) {
            List<KeyValue> valueList = response.getListField();
            if (valueList != null) {
                List<LookupCriteriaBean> beanList = new ArrayList<>();
                if (!valueList.isEmpty()) {
                    for (KeyValue value : valueList) {
                        LookupCriteriaBean bean = new LookupCriteriaBean();
                        bean.setCode(value.getKey());
                        bean.setValue(value.getValue());
                        beanList.add(bean);
                    }
                    bean.setLookupCriteriaList(beanList);
                    LookupCriteriaOnlineActivity.beanList = beanList;
                    Intent intent = new Intent(activity, LookupCriteriaOnlineActivity.class);
                    if (criteriaParameter != null) {
                        intent.putExtra(KEY_WITH_FILTER, true);
                        if (FragmentQuestion.refIdMaskapai != null) {
                            if (bean.getIdentifier_name().equalsIgnoreCase(FragmentQuestion.refIdMaskapai)) {
                                intent.putExtra(Global.GS_REF_ID_TTD_MASKAPAI, bean.getIdentifier_name());
                            }
                        }
                    }
                    activity.startActivityForResult(intent, Global.REQUEST_LOOKUP_ANSWER);
                } else {
                    errMessage = activity.getString(R.string.data_not_found);
                    Toast.makeText(activity, errMessage, Toast.LENGTH_SHORT).show();
                }
            }
        } else {
            errMessage = response.getStatus().getMessage();
            Toast.makeText(activity, errMessage, Toast.LENGTH_SHORT).show();
        }
    }

    public String getJsonRequest() {
        return getJsonRequestLuOnline();
    }

    public String getJsonRequestLuOnline() {
        LookupOnlineRequest request = new LookupOnlineRequest();
        request.setAudit(GlobalData.getSharedGlobalData().getAuditData());
        request.setIdentifier(bean.getIdentifier_name());
        if (bean.getChoice_filter() != null) {
            List<String> tempfilters = getFilterAnswer(bean);
            StringBuilder stringBuilder = new StringBuilder();
            for (String filter : tempfilters) {
                if (stringBuilder.length() > 0)
                    stringBuilder.append(Global.DELIMETER_DATA_LOOKUP);
                stringBuilder.append(filter);
            }
            request.setChoiceFilter(stringBuilder.toString());
        }
        request.setUserFilter(criteriaParameter.getParameters().get(0).getAnswer());
        request.setLovGroup(bean.getLov_group());
        request.setIdentifier(bean.getIdentifier_name());
        String jsonRequest = GsonHelper.toJson(request);
        return jsonRequest;
    }

    public List<String> getFilterAnswer(QuestionBean bean) {
        List<String> filters = new ArrayList<String>();
        if (bean.getChoice_filter() != null) {
            String[] tempfilters = Tool.split(bean.getChoice_filter(), Global.DELIMETER_DATA3);

            for (String newFilter : tempfilters) {
                int idxOfOpenBrace = newFilter.indexOf('{');
                if (idxOfOpenBrace != -1) {
                    int idxOfCloseBrace = newFilter.indexOf('}');
                    String tempIdentifier = newFilter.substring(idxOfOpenBrace + 1, idxOfCloseBrace).toUpperCase();
                    if (tempIdentifier.contains("%")) {
                        filters.add(tempIdentifier);
                    } else {
                        int idxOfOpenAbs = tempIdentifier.indexOf("$");
                        if (idxOfOpenAbs != -1) {
                            String finalIdentifier = tempIdentifier.substring(idxOfOpenAbs + 1);
                            if (finalIdentifier.equals(Global.IDF_LOGIN_ID)) {
                                String loginId = GlobalData.getSharedGlobalData().getUser().getLogin_id();
                                int idxOfOpenAt = loginId.indexOf('@');
                                if (idxOfOpenAt != -1) {
                                    loginId = loginId.substring(0, idxOfOpenAt);
                                }
                                filters.add(loginId);
                            } else if (finalIdentifier.equals(Global.IDF_BRANCH_ID)) {
                                String branchId = GlobalData.getSharedGlobalData().getUser().getBranch_id();
                                filters.add(branchId);
                            } else if (finalIdentifier.equals(Global.IDF_BRANCH_NAME)) {
                                String branchName = GlobalData.getSharedGlobalData().getUser().getBranch_name();
                                filters.add(branchName);
                            } else if (finalIdentifier.equals(Global.IDF_UUID_USER)) {
                                String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                                filters.add(uuidUser);
                            } else if (finalIdentifier.equals(Global.IDF_JOB)) {
                                String job = GlobalData.getSharedGlobalData().getUser().getFlag_job();
                                filters.add(job);
                            } else if (finalIdentifier.equals(Global.IDF_DEALER_NAME)) {
                                String dealerName = GlobalData.getSharedGlobalData().getUser().getDealer_name();
                                filters.add(dealerName);
                            }
                        } else {
                            QuestionBean bean2 = Constant.listOfQuestion.get(tempIdentifier);
                            if (bean2 != null) {
                                String answer = QuestionBean.getAnswer(bean2);
                                filters.add(answer);
                                bean2.setRelevanted(true);
                            }
                        }
                    }
                }
            }
        }
        return filters;
    }
}
