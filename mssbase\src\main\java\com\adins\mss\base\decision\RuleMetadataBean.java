package com.adins.mss.base.decision;

import java.io.Serializable;

public class RuleMetadataBean implements Serializable {
	private int conditionStart = -1;
	private int conditionEnd = -1;
	private int actionStart = -1;
	private int actionEnd = -1;
	private int row = -1;
	private boolean skipOnFirstAppliedRule;
	
	public RuleMetadataBean() {}
	
	public RuleMetadataBean(int conditionStart, int conditionEnd,
			int actionStart, int actionEnd, int row, boolean skipOnFirstAppliedRule) {
		super();
		this.conditionStart = conditionStart;
		this.conditionEnd = conditionEnd;
		this.actionStart = actionStart;
		this.actionEnd = actionEnd;
		this.row = row;
		this.skipOnFirstAppliedRule = skipOnFirstAppliedRule;
	}
	
	public int getConditionStart() {
		return conditionStart;
	}
	public void setConditionStart(int conditionStart) {
		this.conditionStart = conditionStart;
	}
	public int getConditionEnd() {
		return conditionEnd;
	}
	public void setConditionEnd(int conditionEnd) {
		this.conditionEnd = conditionEnd;
	}
	public int getActionStart() {
		return actionStart;
	}
	public void setActionStart(int actionStart) {
		this.actionStart = actionStart;
	}
	public int getActionEnd() {
		return actionEnd;
	}
	public void setActionEnd(int actionEnd) {
		this.actionEnd = actionEnd;
	}

	public int getRow() {
		return row;
	}

	public void setRow(int row) {
		this.row = row;
	}

	public boolean isSkipOnFirstAppliedRule() {
		return skipOnFirstAppliedRule;
	}

	public void setSkipOnFirstAppliedRule(boolean skipOnFirstAppliedRule) {
		this.skipOnFirstAppliedRule = skipOnFirstAppliedRule;
	}
	
	public String toString() {
		StringBuilder sb = new StringBuilder();
		
		sb.append("skipOnFirstAppliedRule=").append(skipOnFirstAppliedRule)
			.append("; Condition/Action Row=").append(row)
			.append("; Condition Columns (zero-based)=").append(conditionStart)
			.append("-").append(conditionEnd)
			.append("; Action Columns (zero-based)=").append(actionStart)
			.append("-").append(actionEnd);
		
		return sb.toString();
	}
}
