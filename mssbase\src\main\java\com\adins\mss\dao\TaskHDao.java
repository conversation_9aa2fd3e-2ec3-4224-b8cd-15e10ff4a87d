package com.adins.mss.dao;

import java.util.List;
import java.util.ArrayList;
import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.SqlUtils;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;
import de.greenrobot.dao.query.Query;
import de.greenrobot.dao.query.QueryBuilder;

import com.adins.mss.dao.TaskH;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TR_TASK_H".
*/
public class TaskHDao extends AbstractDao<TaskH, String> {

    public static final String TABLENAME = "TR_TASK_H";

    /**
     * Properties of entity TaskH.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_task_h = new Property(0, String.class, "uuid_task_h", true, "UUID_TASK_H");
        public final static Property Task_id = new Property(1, String.class, "task_id", false, "TASK_ID");
        public final static Property Status = new Property(2, String.class, "status", false, "STATUS");
        public final static Property Is_printable = new Property(3, String.class, "is_printable", false, "IS_PRINTABLE");
        public final static Property Customer_name = new Property(4, String.class, "customer_name", false, "CUSTOMER_NAME");
        public final static Property Customer_phone = new Property(5, String.class, "customer_phone", false, "CUSTOMER_PHONE");
        public final static Property Customer_address = new Property(6, String.class, "customer_address", false, "CUSTOMER_ADDRESS");
        public final static Property Notes = new Property(7, String.class, "notes", false, "NOTES");
        public final static Property Submit_date = new Property(8, java.util.Date.class, "submit_date", false, "SUBMIT_DATE");
        public final static Property Submit_duration = new Property(9, String.class, "submit_duration", false, "SUBMIT_DURATION");
        public final static Property Submit_size = new Property(10, String.class, "submit_size", false, "SUBMIT_SIZE");
        public final static Property Submit_result = new Property(11, String.class, "submit_result", false, "SUBMIT_RESULT");
        public final static Property Assignment_date = new Property(12, java.util.Date.class, "assignment_date", false, "ASSIGNMENT_DATE");
        public final static Property Print_count = new Property(13, Integer.class, "print_count", false, "PRINT_COUNT");
        public final static Property Draft_date = new Property(14, java.util.Date.class, "draft_date", false, "DRAFT_DATE");
        public final static Property Usr_crt = new Property(15, String.class, "usr_crt", false, "USR_CRT");
        public final static Property Dtm_crt = new Property(16, java.util.Date.class, "dtm_crt", false, "DTM_CRT");
        public final static Property Priority = new Property(17, String.class, "priority", false, "PRIORITY");
        public final static Property Latitude = new Property(18, String.class, "latitude", false, "LATITUDE");
        public final static Property Longitude = new Property(19, String.class, "longitude", false, "LONGITUDE");
        public final static Property Scheme_last_update = new Property(20, java.util.Date.class, "scheme_last_update", false, "SCHEME_LAST_UPDATE");
        public final static Property Is_verification = new Property(21, String.class, "is_verification", false, "IS_VERIFICATION");
        public final static Property Is_preview_server = new Property(22, String.class, "is_preview_server", false, "IS_PREVIEW_SERVER");
        public final static Property Uuid_user = new Property(23, String.class, "uuid_user", false, "UUID_USER");
        public final static Property Voice_note = new Property(24, byte[].class, "voice_note", false, "VOICE_NOTE");
        public final static Property Uuid_scheme = new Property(25, String.class, "uuid_scheme", false, "UUID_SCHEME");
        public final static Property Zip_code = new Property(26, String.class, "zip_code", false, "ZIP_CODE");
        public final static Property Start_date = new Property(27, java.util.Date.class, "start_date", false, "START_DATE");
        public final static Property Open_date = new Property(28, java.util.Date.class, "open_date", false, "OPEN_DATE");
        public final static Property Appl_no = new Property(29, String.class, "appl_no", false, "APPL_NO");
        public final static Property Is_prepocessed = new Property(30, String.class, "is_prepocessed", false, "IS_PREPOCESSED");
        public final static Property Last_saved_question = new Property(31, Integer.class, "last_saved_question", false, "LAST_SAVED_QUESTION");
        public final static Property Is_reconciled = new Property(32, String.class, "is_reconciled", false, "IS_RECONCILED");
        public final static Property Pts_date = new Property(33, java.util.Date.class, "pts_date", false, "PTS_DATE");
        public final static Property Access_mode = new Property(34, String.class, "access_mode", false, "ACCESS_MODE");
        public final static Property Rv_number = new Property(35, String.class, "rv_number", false, "RV_NUMBER");
        public final static Property Status_rv = new Property(36, String.class, "status_rv", false, "STATUS_RV");
        public final static Property Flag = new Property(37, String.class, "flag", false, "FLAG");
        public final static Property Pms_date = new Property(38, java.util.Date.class, "pms_date", false, "PMS_DATE");
        public final static Property Is_sent_pts = new Property(39, String.class, "is_sent_pts", false, "IS_SENT_PTS");
        public final static Property Form_version = new Property(40, String.class, "form_version", false, "FORM_VERSION");
        public final static Property Flag_survey = new Property(41, String.class, "flag_survey", false, "FLAG_SURVEY");
        public final static Property Uuid_resurvey_user = new Property(42, String.class, "uuid_resurvey_user", false, "UUID_RESURVEY_USER");
        public final static Property Resurvey_suggested = new Property(43, String.class, "resurvey_suggested", false, "RESURVEY_SUGGESTED");
        public final static Property Verification_notes = new Property(44, String.class, "verification_notes", false, "VERIFICATION_NOTES");
        public final static Property Od = new Property(45, String.class, "od", false, "OD");
        public final static Property Amt_due = new Property(46, String.class, "amt_due", false, "AMT_DUE");
        public final static Property Inst_no = new Property(47, String.class, "inst_no", false, "INST_NO");
        public final static Property Uuid_task_update = new Property(48, String.class, "uuid_task_update", false, "UUID_TASK_UPDATE");
        public final static Property Pending_notes = new Property(49, String.class, "pending_notes", false, "PENDING_NOTES");
        public final static Property Docupro_feedback = new Property(50, String.class, "docupro_feedback", false, "DOCUPRO_FEEDBACK");
        public final static Property Status_application = new Property(51, String.class, "status_application", false, "STATUS_APPLICATION");
        public final static Property Is_sent_confins = new Property(52, Integer.class, "is_sent_confins", false, "IS_SENT_CONFINS");
        public final static Property Is_already_notified = new Property(53, Integer.class, "is_already_notified", false, "IS_ALREADY_NOTIFIED");
        public final static Property Kelurahan = new Property(54, String.class, "kelurahan", false, "KELURAHAN");
        public final static Property Status_followup = new Property(55, String.class, "status_followup", false, "STATUS_FOLLOWUP");
        public final static Property Is_revisit = new Property(56, String.class, "is_revisit", false, "IS_REVISIT");
        public final static Property Visit_type = new Property(57, String.class, "visit_type", false, "VISIT_TYPE");
        public final static Property Send_task_promise_to_survey = new Property(58, String.class, "send_task_promise_to_survey", false, "SEND_TASK_PROMISE_TO_SURVEY");
        public final static Property Send_task_presurvey = new Property(59, String.class, "send_task_presurvey", false, "SEND_TASK_PRESURVEY");
        public final static Property Send_task_survey = new Property(60, String.class, "send_task_survey", false, "SEND_TASK_SURVEY");
        public final static Property Is_piloting_cae = new Property(61, String.class, "is_piloting_cae", false, "IS_PILOTING_CAE");
        public final static Property Category = new Property(62, String.class, "category", false, "CATEGORY");
        public final static Property Sub_category = new Property(63, String.class, "sub_category", false, "SUB_CATEGORY");
        public final static Property Reason_detail = new Property(64, String.class, "reason_detail", false, "REASON_DETAIL");
        public final static Property Validasi = new Property(65, String.class, "validasi", false, "VALIDASI");
        public final static Property Notes_crm = new Property(66, String.class, "notes_crm", false, "NOTES_CRM");
        public final static Property Is_pre_approval = new Property(67, Integer.class, "is_pre_approval", false, "IS_PRE_APPROVAL");
        public final static Property Source_data = new Property(68, String.class, "source_data", false, "SOURCE_DATA");
        public final static Property Is_already_download_task = new Property(69, String.class, "is_already_download_task", false, "IS_ALREADY_DOWNLOAD_TASK");
        public final static Property Product_name = new Property(70, String.class, "product_name", false, "PRODUCT_NAME");
        public final static Property Jenis_asset = new Property(71, String.class, "jenis_asset", false, "JENIS_ASSET");
    };

    private DaoSession daoSession;

    private Query<TaskH> user_TaskHListQuery;
    private Query<TaskH> scheme_TaskHListQuery;

    public TaskHDao(DaoConfig config) {
        super(config);
    }
    
    public TaskHDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TR_TASK_H\" (" + //
                "\"UUID_TASK_H\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_task_h
                "\"TASK_ID\" TEXT," + // 1: task_id
                "\"STATUS\" TEXT," + // 2: status
                "\"IS_PRINTABLE\" TEXT," + // 3: is_printable
                "\"CUSTOMER_NAME\" TEXT," + // 4: customer_name
                "\"CUSTOMER_PHONE\" TEXT," + // 5: customer_phone
                "\"CUSTOMER_ADDRESS\" TEXT," + // 6: customer_address
                "\"NOTES\" TEXT," + // 7: notes
                "\"SUBMIT_DATE\" INTEGER," + // 8: submit_date
                "\"SUBMIT_DURATION\" TEXT," + // 9: submit_duration
                "\"SUBMIT_SIZE\" TEXT," + // 10: submit_size
                "\"SUBMIT_RESULT\" TEXT," + // 11: submit_result
                "\"ASSIGNMENT_DATE\" INTEGER," + // 12: assignment_date
                "\"PRINT_COUNT\" INTEGER," + // 13: print_count
                "\"DRAFT_DATE\" INTEGER," + // 14: draft_date
                "\"USR_CRT\" TEXT," + // 15: usr_crt
                "\"DTM_CRT\" INTEGER," + // 16: dtm_crt
                "\"PRIORITY\" TEXT," + // 17: priority
                "\"LATITUDE\" TEXT," + // 18: latitude
                "\"LONGITUDE\" TEXT," + // 19: longitude
                "\"SCHEME_LAST_UPDATE\" INTEGER," + // 20: scheme_last_update
                "\"IS_VERIFICATION\" TEXT," + // 21: is_verification
                "\"IS_PREVIEW_SERVER\" TEXT," + // 22: is_preview_server
                "\"UUID_USER\" TEXT," + // 23: uuid_user
                "\"VOICE_NOTE\" BLOB," + // 24: voice_note
                "\"UUID_SCHEME\" TEXT," + // 25: uuid_scheme
                "\"ZIP_CODE\" TEXT," + // 26: zip_code
                "\"START_DATE\" INTEGER," + // 27: start_date
                "\"OPEN_DATE\" INTEGER," + // 28: open_date
                "\"APPL_NO\" TEXT," + // 29: appl_no
                "\"IS_PREPOCESSED\" TEXT," + // 30: is_prepocessed
                "\"LAST_SAVED_QUESTION\" INTEGER," + // 31: last_saved_question
                "\"IS_RECONCILED\" TEXT," + // 32: is_reconciled
                "\"PTS_DATE\" INTEGER," + // 33: pts_date
                "\"ACCESS_MODE\" TEXT," + // 34: access_mode
                "\"RV_NUMBER\" TEXT," + // 35: rv_number
                "\"STATUS_RV\" TEXT," + // 36: status_rv
                "\"FLAG\" TEXT," + // 37: flag
                "\"PMS_DATE\" INTEGER," + // 38: pms_date
                "\"IS_SENT_PTS\" TEXT," + // 39: is_sent_pts
                "\"FORM_VERSION\" TEXT," + // 40: form_version
                "\"FLAG_SURVEY\" TEXT," + // 41: flag_survey
                "\"UUID_RESURVEY_USER\" TEXT," + // 42: uuid_resurvey_user
                "\"RESURVEY_SUGGESTED\" TEXT," + // 43: resurvey_suggested
                "\"VERIFICATION_NOTES\" TEXT," + // 44: verification_notes
                "\"OD\" TEXT," + // 45: od
                "\"AMT_DUE\" TEXT," + // 46: amt_due
                "\"INST_NO\" TEXT," + // 47: inst_no
                "\"UUID_TASK_UPDATE\" TEXT," + // 48: uuid_task_update
                "\"PENDING_NOTES\" TEXT," + // 49: pending_notes
                "\"DOCUPRO_FEEDBACK\" TEXT," + // 50: docupro_feedback
                "\"STATUS_APPLICATION\" TEXT," + // 51: status_application
                "\"IS_SENT_CONFINS\" INTEGER," + // 52: is_sent_confins
                "\"IS_ALREADY_NOTIFIED\" INTEGER," + // 53: is_already_notified
                "\"KELURAHAN\" TEXT," + // 54: kelurahan
                "\"STATUS_FOLLOWUP\" TEXT," + // 55: status_followup
                "\"IS_REVISIT\" TEXT," + // 56: is_revisit
                "\"VISIT_TYPE\" TEXT," + // 57: visit_type
                "\"SEND_TASK_PROMISE_TO_SURVEY\" TEXT," + // 58: send_task_promise_to_survey
                "\"SEND_TASK_PRESURVEY\" TEXT," + // 59: send_task_presurvey
                "\"SEND_TASK_SURVEY\" TEXT," + // 60: send_task_survey
                "\"IS_PILOTING_CAE\" TEXT," + // 61: is_piloting_cae
                "\"CATEGORY\" TEXT," + // 62: category
                "\"SUB_CATEGORY\" TEXT," + // 63: sub_category
                "\"REASON_DETAIL\" TEXT," + // 64: reason_detail
                "\"VALIDASI\" TEXT," + // 65: validasi
                "\"NOTES_CRM\" TEXT," + // 66: notes_crm
                "\"IS_PRE_APPROVAL\" INTEGER," + // 67: is_pre_approval
                "\"SOURCE_DATA\" TEXT," + // 68: source_data
                "\"IS_ALREADY_DOWNLOAD_TASK\" TEXT," + // 69: is_already_download_task
                "\"PRODUCT_NAME\" TEXT," + // 70: product_name
                "\"JENIS_ASSET\" TEXT);"); // 71: jenis_asset
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TR_TASK_H\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, TaskH entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_task_h());
 
        String task_id = entity.getTask_id();
        if (task_id != null) {
            stmt.bindString(2, task_id);
        }
 
        String status = entity.getStatus();
        if (status != null) {
            stmt.bindString(3, status);
        }
 
        String is_printable = entity.getIs_printable();
        if (is_printable != null) {
            stmt.bindString(4, is_printable);
        }
 
        String customer_name = entity.getCustomer_name();
        if (customer_name != null) {
            stmt.bindString(5, customer_name);
        }
 
        String customer_phone = entity.getCustomer_phone();
        if (customer_phone != null) {
            stmt.bindString(6, customer_phone);
        }
 
        String customer_address = entity.getCustomer_address();
        if (customer_address != null) {
            stmt.bindString(7, customer_address);
        }
 
        String notes = entity.getNotes();
        if (notes != null) {
            stmt.bindString(8, notes);
        }
 
        java.util.Date submit_date = entity.getSubmit_date();
        if (submit_date != null) {
            stmt.bindLong(9, submit_date.getTime());
        }
 
        String submit_duration = entity.getSubmit_duration();
        if (submit_duration != null) {
            stmt.bindString(10, submit_duration);
        }
 
        String submit_size = entity.getSubmit_size();
        if (submit_size != null) {
            stmt.bindString(11, submit_size);
        }
 
        String submit_result = entity.getSubmit_result();
        if (submit_result != null) {
            stmt.bindString(12, submit_result);
        }
 
        java.util.Date assignment_date = entity.getAssignment_date();
        if (assignment_date != null) {
            stmt.bindLong(13, assignment_date.getTime());
        }
 
        Integer print_count = entity.getPrint_count();
        if (print_count != null) {
            stmt.bindLong(14, print_count);
        }
 
        java.util.Date draft_date = entity.getDraft_date();
        if (draft_date != null) {
            stmt.bindLong(15, draft_date.getTime());
        }
 
        String usr_crt = entity.getUsr_crt();
        if (usr_crt != null) {
            stmt.bindString(16, usr_crt);
        }
 
        java.util.Date dtm_crt = entity.getDtm_crt();
        if (dtm_crt != null) {
            stmt.bindLong(17, dtm_crt.getTime());
        }
 
        String priority = entity.getPriority();
        if (priority != null) {
            stmt.bindString(18, priority);
        }
 
        String latitude = entity.getLatitude();
        if (latitude != null) {
            stmt.bindString(19, latitude);
        }
 
        String longitude = entity.getLongitude();
        if (longitude != null) {
            stmt.bindString(20, longitude);
        }
 
        java.util.Date scheme_last_update = entity.getScheme_last_update();
        if (scheme_last_update != null) {
            stmt.bindLong(21, scheme_last_update.getTime());
        }
 
        String is_verification = entity.getIs_verification();
        if (is_verification != null) {
            stmt.bindString(22, is_verification);
        }
 
        String is_preview_server = entity.getIs_preview_server();
        if (is_preview_server != null) {
            stmt.bindString(23, is_preview_server);
        }
 
        String uuid_user = entity.getUuid_user();
        if (uuid_user != null) {
            stmt.bindString(24, uuid_user);
        }
 
        byte[] voice_note = entity.getVoice_note();
        if (voice_note != null) {
            stmt.bindBlob(25, voice_note);
        }
 
        String uuid_scheme = entity.getUuid_scheme();
        if (uuid_scheme != null) {
            stmt.bindString(26, uuid_scheme);
        }
 
        String zip_code = entity.getZip_code();
        if (zip_code != null) {
            stmt.bindString(27, zip_code);
        }
 
        java.util.Date start_date = entity.getStart_date();
        if (start_date != null) {
            stmt.bindLong(28, start_date.getTime());
        }
 
        java.util.Date open_date = entity.getOpen_date();
        if (open_date != null) {
            stmt.bindLong(29, open_date.getTime());
        }
 
        String appl_no = entity.getAppl_no();
        if (appl_no != null) {
            stmt.bindString(30, appl_no);
        }
 
        String is_prepocessed = entity.getIs_prepocessed();
        if (is_prepocessed != null) {
            stmt.bindString(31, is_prepocessed);
        }
 
        Integer last_saved_question = entity.getLast_saved_question();
        if (last_saved_question != null) {
            stmt.bindLong(32, last_saved_question);
        }
 
        String is_reconciled = entity.getIs_reconciled();
        if (is_reconciled != null) {
            stmt.bindString(33, is_reconciled);
        }
 
        java.util.Date pts_date = entity.getPts_date();
        if (pts_date != null) {
            stmt.bindLong(34, pts_date.getTime());
        }
 
        String access_mode = entity.getAccess_mode();
        if (access_mode != null) {
            stmt.bindString(35, access_mode);
        }
 
        String rv_number = entity.getRv_number();
        if (rv_number != null) {
            stmt.bindString(36, rv_number);
        }
 
        String status_rv = entity.getStatus_rv();
        if (status_rv != null) {
            stmt.bindString(37, status_rv);
        }
 
        String flag = entity.getFlag();
        if (flag != null) {
            stmt.bindString(38, flag);
        }
 
        java.util.Date pms_date = entity.getPms_date();
        if (pms_date != null) {
            stmt.bindLong(39, pms_date.getTime());
        }
 
        String is_sent_pts = entity.getIs_sent_pts();
        if (is_sent_pts != null) {
            stmt.bindString(40, is_sent_pts);
        }
 
        String form_version = entity.getForm_version();
        if (form_version != null) {
            stmt.bindString(41, form_version);
        }
 
        String flag_survey = entity.getFlag_survey();
        if (flag_survey != null) {
            stmt.bindString(42, flag_survey);
        }
 
        String uuid_resurvey_user = entity.getUuid_resurvey_user();
        if (uuid_resurvey_user != null) {
            stmt.bindString(43, uuid_resurvey_user);
        }
 
        String resurvey_suggested = entity.getResurvey_suggested();
        if (resurvey_suggested != null) {
            stmt.bindString(44, resurvey_suggested);
        }
 
        String verification_notes = entity.getVerification_notes();
        if (verification_notes != null) {
            stmt.bindString(45, verification_notes);
        }
 
        String od = entity.getOd();
        if (od != null) {
            stmt.bindString(46, od);
        }
 
        String amt_due = entity.getAmt_due();
        if (amt_due != null) {
            stmt.bindString(47, amt_due);
        }
 
        String inst_no = entity.getInst_no();
        if (inst_no != null) {
            stmt.bindString(48, inst_no);
        }
 
        String uuid_task_update = entity.getUuid_task_update();
        if (uuid_task_update != null) {
            stmt.bindString(49, uuid_task_update);
        }
 
        String pending_notes = entity.getPending_notes();
        if (pending_notes != null) {
            stmt.bindString(50, pending_notes);
        }
 
        String docupro_feedback = entity.getDocupro_feedback();
        if (docupro_feedback != null) {
            stmt.bindString(51, docupro_feedback);
        }
 
        String status_application = entity.getStatus_application();
        if (status_application != null) {
            stmt.bindString(52, status_application);
        }
 
        Integer is_sent_confins = entity.getIs_sent_confins();
        if (is_sent_confins != null) {
            stmt.bindLong(53, is_sent_confins);
        }
 
        Integer is_already_notified = entity.getIs_already_notified();
        if (is_already_notified != null) {
            stmt.bindLong(54, is_already_notified);
        }
 
        String kelurahan = entity.getKelurahan();
        if (kelurahan != null) {
            stmt.bindString(55, kelurahan);
        }
 
        String status_followup = entity.getStatus_followup();
        if (status_followup != null) {
            stmt.bindString(56, status_followup);
        }
 
        String is_revisit = entity.getIs_revisit();
        if (is_revisit != null) {
            stmt.bindString(57, is_revisit);
        }
 
        String visit_type = entity.getVisit_type();
        if (visit_type != null) {
            stmt.bindString(58, visit_type);
        }
 
        String send_task_promise_to_survey = entity.getSend_task_promise_to_survey();
        if (send_task_promise_to_survey != null) {
            stmt.bindString(59, send_task_promise_to_survey);
        }
 
        String send_task_presurvey = entity.getSend_task_presurvey();
        if (send_task_presurvey != null) {
            stmt.bindString(60, send_task_presurvey);
        }
 
        String send_task_survey = entity.getSend_task_survey();
        if (send_task_survey != null) {
            stmt.bindString(61, send_task_survey);
        }
 
        String is_piloting_cae = entity.getIs_piloting_cae();
        if (is_piloting_cae != null) {
            stmt.bindString(62, is_piloting_cae);
        }
 
        String category = entity.getCategory();
        if (category != null) {
            stmt.bindString(63, category);
        }
 
        String sub_category = entity.getSub_category();
        if (sub_category != null) {
            stmt.bindString(64, sub_category);
        }
 
        String reason_detail = entity.getReason_detail();
        if (reason_detail != null) {
            stmt.bindString(65, reason_detail);
        }
 
        String validasi = entity.getValidasi();
        if (validasi != null) {
            stmt.bindString(66, validasi);
        }
 
        String notes_crm = entity.getNotes_crm();
        if (notes_crm != null) {
            stmt.bindString(67, notes_crm);
        }
 
        Integer is_pre_approval = entity.getIs_pre_approval();
        if (is_pre_approval != null) {
            stmt.bindLong(68, is_pre_approval);
        }
 
        String source_data = entity.getSource_data();
        if (source_data != null) {
            stmt.bindString(69, source_data);
        }
 
        String is_already_download_task = entity.getIs_already_download_task();
        if (is_already_download_task != null) {
            stmt.bindString(70, is_already_download_task);
        }
 
        String product_name = entity.getProduct_name();
        if (product_name != null) {
            stmt.bindString(71, product_name);
        }
 
        String jenis_asset = entity.getJenis_asset();
        if (jenis_asset != null) {
            stmt.bindString(72, jenis_asset);
        }
    }

    @Override
    protected void attachEntity(TaskH entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public TaskH readEntity(Cursor cursor, int offset) {
        TaskH entity = new TaskH( //
            cursor.getString(offset + 0), // uuid_task_h
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // task_id
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // status
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // is_printable
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // customer_name
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // customer_phone
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // customer_address
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7), // notes
            cursor.isNull(offset + 8) ? null : new java.util.Date(cursor.getLong(offset + 8)), // submit_date
            cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9), // submit_duration
            cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10), // submit_size
            cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11), // submit_result
            cursor.isNull(offset + 12) ? null : new java.util.Date(cursor.getLong(offset + 12)), // assignment_date
            cursor.isNull(offset + 13) ? null : cursor.getInt(offset + 13), // print_count
            cursor.isNull(offset + 14) ? null : new java.util.Date(cursor.getLong(offset + 14)), // draft_date
            cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15), // usr_crt
            cursor.isNull(offset + 16) ? null : new java.util.Date(cursor.getLong(offset + 16)), // dtm_crt
            cursor.isNull(offset + 17) ? null : cursor.getString(offset + 17), // priority
            cursor.isNull(offset + 18) ? null : cursor.getString(offset + 18), // latitude
            cursor.isNull(offset + 19) ? null : cursor.getString(offset + 19), // longitude
            cursor.isNull(offset + 20) ? null : new java.util.Date(cursor.getLong(offset + 20)), // scheme_last_update
            cursor.isNull(offset + 21) ? null : cursor.getString(offset + 21), // is_verification
            cursor.isNull(offset + 22) ? null : cursor.getString(offset + 22), // is_preview_server
            cursor.isNull(offset + 23) ? null : cursor.getString(offset + 23), // uuid_user
            cursor.isNull(offset + 24) ? null : cursor.getBlob(offset + 24), // voice_note
            cursor.isNull(offset + 25) ? null : cursor.getString(offset + 25), // uuid_scheme
            cursor.isNull(offset + 26) ? null : cursor.getString(offset + 26), // zip_code
            cursor.isNull(offset + 27) ? null : new java.util.Date(cursor.getLong(offset + 27)), // start_date
            cursor.isNull(offset + 28) ? null : new java.util.Date(cursor.getLong(offset + 28)), // open_date
            cursor.isNull(offset + 29) ? null : cursor.getString(offset + 29), // appl_no
            cursor.isNull(offset + 30) ? null : cursor.getString(offset + 30), // is_prepocessed
            cursor.isNull(offset + 31) ? null : cursor.getInt(offset + 31), // last_saved_question
            cursor.isNull(offset + 32) ? null : cursor.getString(offset + 32), // is_reconciled
            cursor.isNull(offset + 33) ? null : new java.util.Date(cursor.getLong(offset + 33)), // pts_date
            cursor.isNull(offset + 34) ? null : cursor.getString(offset + 34), // access_mode
            cursor.isNull(offset + 35) ? null : cursor.getString(offset + 35), // rv_number
            cursor.isNull(offset + 36) ? null : cursor.getString(offset + 36), // status_rv
            cursor.isNull(offset + 37) ? null : cursor.getString(offset + 37), // flag
            cursor.isNull(offset + 38) ? null : new java.util.Date(cursor.getLong(offset + 38)), // pms_date
            cursor.isNull(offset + 39) ? null : cursor.getString(offset + 39), // is_sent_pts
            cursor.isNull(offset + 40) ? null : cursor.getString(offset + 40), // form_version
            cursor.isNull(offset + 41) ? null : cursor.getString(offset + 41), // flag_survey
            cursor.isNull(offset + 42) ? null : cursor.getString(offset + 42), // uuid_resurvey_user
            cursor.isNull(offset + 43) ? null : cursor.getString(offset + 43), // resurvey_suggested
            cursor.isNull(offset + 44) ? null : cursor.getString(offset + 44), // verification_notes
            cursor.isNull(offset + 45) ? null : cursor.getString(offset + 45), // od
            cursor.isNull(offset + 46) ? null : cursor.getString(offset + 46), // amt_due
            cursor.isNull(offset + 47) ? null : cursor.getString(offset + 47), // inst_no
            cursor.isNull(offset + 48) ? null : cursor.getString(offset + 48), // uuid_task_update
            cursor.isNull(offset + 49) ? null : cursor.getString(offset + 49), // pending_notes
            cursor.isNull(offset + 50) ? null : cursor.getString(offset + 50), // docupro_feedback
            cursor.isNull(offset + 51) ? null : cursor.getString(offset + 51), // status_application
            cursor.isNull(offset + 52) ? null : cursor.getInt(offset + 52), // is_sent_confins
            cursor.isNull(offset + 53) ? null : cursor.getInt(offset + 53), // is_already_notified
            cursor.isNull(offset + 54) ? null : cursor.getString(offset + 54), // kelurahan
            cursor.isNull(offset + 55) ? null : cursor.getString(offset + 55), // status_followup
            cursor.isNull(offset + 56) ? null : cursor.getString(offset + 56), // is_revisit
            cursor.isNull(offset + 57) ? null : cursor.getString(offset + 57), // visit_type
            cursor.isNull(offset + 58) ? null : cursor.getString(offset + 58), // send_task_promise_to_survey
            cursor.isNull(offset + 59) ? null : cursor.getString(offset + 59), // send_task_presurvey
            cursor.isNull(offset + 60) ? null : cursor.getString(offset + 60), // send_task_survey
            cursor.isNull(offset + 61) ? null : cursor.getString(offset + 61), // is_piloting_cae
            cursor.isNull(offset + 62) ? null : cursor.getString(offset + 62), // category
            cursor.isNull(offset + 63) ? null : cursor.getString(offset + 63), // sub_category
            cursor.isNull(offset + 64) ? null : cursor.getString(offset + 64), // reason_detail
            cursor.isNull(offset + 65) ? null : cursor.getString(offset + 65), // validasi
            cursor.isNull(offset + 66) ? null : cursor.getString(offset + 66), // notes_crm
            cursor.isNull(offset + 67) ? null : cursor.getInt(offset + 67), // is_pre_approval
            cursor.isNull(offset + 68) ? null : cursor.getString(offset + 68), // source_data
            cursor.isNull(offset + 69) ? null : cursor.getString(offset + 69), // is_already_download_task
            cursor.isNull(offset + 70) ? null : cursor.getString(offset + 70), // product_name
            cursor.isNull(offset + 71) ? null : cursor.getString(offset + 71) // jenis_asset
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, TaskH entity, int offset) {
        entity.setUuid_task_h(cursor.getString(offset + 0));
        entity.setTask_id(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setStatus(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setIs_printable(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setCustomer_name(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setCustomer_phone(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setCustomer_address(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setNotes(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
        entity.setSubmit_date(cursor.isNull(offset + 8) ? null : new java.util.Date(cursor.getLong(offset + 8)));
        entity.setSubmit_duration(cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9));
        entity.setSubmit_size(cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10));
        entity.setSubmit_result(cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11));
        entity.setAssignment_date(cursor.isNull(offset + 12) ? null : new java.util.Date(cursor.getLong(offset + 12)));
        entity.setPrint_count(cursor.isNull(offset + 13) ? null : cursor.getInt(offset + 13));
        entity.setDraft_date(cursor.isNull(offset + 14) ? null : new java.util.Date(cursor.getLong(offset + 14)));
        entity.setUsr_crt(cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15));
        entity.setDtm_crt(cursor.isNull(offset + 16) ? null : new java.util.Date(cursor.getLong(offset + 16)));
        entity.setPriority(cursor.isNull(offset + 17) ? null : cursor.getString(offset + 17));
        entity.setLatitude(cursor.isNull(offset + 18) ? null : cursor.getString(offset + 18));
        entity.setLongitude(cursor.isNull(offset + 19) ? null : cursor.getString(offset + 19));
        entity.setScheme_last_update(cursor.isNull(offset + 20) ? null : new java.util.Date(cursor.getLong(offset + 20)));
        entity.setIs_verification(cursor.isNull(offset + 21) ? null : cursor.getString(offset + 21));
        entity.setIs_preview_server(cursor.isNull(offset + 22) ? null : cursor.getString(offset + 22));
        entity.setUuid_user(cursor.isNull(offset + 23) ? null : cursor.getString(offset + 23));
        entity.setVoice_note(cursor.isNull(offset + 24) ? null : cursor.getBlob(offset + 24));
        entity.setUuid_scheme(cursor.isNull(offset + 25) ? null : cursor.getString(offset + 25));
        entity.setZip_code(cursor.isNull(offset + 26) ? null : cursor.getString(offset + 26));
        entity.setStart_date(cursor.isNull(offset + 27) ? null : new java.util.Date(cursor.getLong(offset + 27)));
        entity.setOpen_date(cursor.isNull(offset + 28) ? null : new java.util.Date(cursor.getLong(offset + 28)));
        entity.setAppl_no(cursor.isNull(offset + 29) ? null : cursor.getString(offset + 29));
        entity.setIs_prepocessed(cursor.isNull(offset + 30) ? null : cursor.getString(offset + 30));
        entity.setLast_saved_question(cursor.isNull(offset + 31) ? null : cursor.getInt(offset + 31));
        entity.setIs_reconciled(cursor.isNull(offset + 32) ? null : cursor.getString(offset + 32));
        entity.setPts_date(cursor.isNull(offset + 33) ? null : new java.util.Date(cursor.getLong(offset + 33)));
        entity.setAccess_mode(cursor.isNull(offset + 34) ? null : cursor.getString(offset + 34));
        entity.setRv_number(cursor.isNull(offset + 35) ? null : cursor.getString(offset + 35));
        entity.setStatus_rv(cursor.isNull(offset + 36) ? null : cursor.getString(offset + 36));
        entity.setFlag(cursor.isNull(offset + 37) ? null : cursor.getString(offset + 37));
        entity.setPms_date(cursor.isNull(offset + 38) ? null : new java.util.Date(cursor.getLong(offset + 38)));
        entity.setIs_sent_pts(cursor.isNull(offset + 39) ? null : cursor.getString(offset + 39));
        entity.setForm_version(cursor.isNull(offset + 40) ? null : cursor.getString(offset + 40));
        entity.setFlag_survey(cursor.isNull(offset + 41) ? null : cursor.getString(offset + 41));
        entity.setUuid_resurvey_user(cursor.isNull(offset + 42) ? null : cursor.getString(offset + 42));
        entity.setResurvey_suggested(cursor.isNull(offset + 43) ? null : cursor.getString(offset + 43));
        entity.setVerification_notes(cursor.isNull(offset + 44) ? null : cursor.getString(offset + 44));
        entity.setOd(cursor.isNull(offset + 45) ? null : cursor.getString(offset + 45));
        entity.setAmt_due(cursor.isNull(offset + 46) ? null : cursor.getString(offset + 46));
        entity.setInst_no(cursor.isNull(offset + 47) ? null : cursor.getString(offset + 47));
        entity.setUuid_task_update(cursor.isNull(offset + 48) ? null : cursor.getString(offset + 48));
        entity.setPending_notes(cursor.isNull(offset + 49) ? null : cursor.getString(offset + 49));
        entity.setDocupro_feedback(cursor.isNull(offset + 50) ? null : cursor.getString(offset + 50));
        entity.setStatus_application(cursor.isNull(offset + 51) ? null : cursor.getString(offset + 51));
        entity.setIs_sent_confins(cursor.isNull(offset + 52) ? null : cursor.getInt(offset + 52));
        entity.setIs_already_notified(cursor.isNull(offset + 53) ? null : cursor.getInt(offset + 53));
        entity.setKelurahan(cursor.isNull(offset + 54) ? null : cursor.getString(offset + 54));
        entity.setStatus_followup(cursor.isNull(offset + 55) ? null : cursor.getString(offset + 55));
        entity.setIs_revisit(cursor.isNull(offset + 56) ? null : cursor.getString(offset + 56));
        entity.setVisit_type(cursor.isNull(offset + 57) ? null : cursor.getString(offset + 57));
        entity.setSend_task_promise_to_survey(cursor.isNull(offset + 58) ? null : cursor.getString(offset + 58));
        entity.setSend_task_presurvey(cursor.isNull(offset + 59) ? null : cursor.getString(offset + 59));
        entity.setSend_task_survey(cursor.isNull(offset + 60) ? null : cursor.getString(offset + 60));
        entity.setIs_piloting_cae(cursor.isNull(offset + 61) ? null : cursor.getString(offset + 61));
        entity.setCategory(cursor.isNull(offset + 62) ? null : cursor.getString(offset + 62));
        entity.setSub_category(cursor.isNull(offset + 63) ? null : cursor.getString(offset + 63));
        entity.setReason_detail(cursor.isNull(offset + 64) ? null : cursor.getString(offset + 64));
        entity.setValidasi(cursor.isNull(offset + 65) ? null : cursor.getString(offset + 65));
        entity.setNotes_crm(cursor.isNull(offset + 66) ? null : cursor.getString(offset + 66));
        entity.setIs_pre_approval(cursor.isNull(offset + 67) ? null : cursor.getInt(offset + 67));
        entity.setSource_data(cursor.isNull(offset + 68) ? null : cursor.getString(offset + 68));
        entity.setIs_already_download_task(cursor.isNull(offset + 69) ? null : cursor.getString(offset + 69));
        entity.setProduct_name(cursor.isNull(offset + 70) ? null : cursor.getString(offset + 70));
        entity.setJenis_asset(cursor.isNull(offset + 71) ? null : cursor.getString(offset + 71));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(TaskH entity, long rowId) {
        return entity.getUuid_task_h();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(TaskH entity) {
        if(entity != null) {
            return entity.getUuid_task_h();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
    /** Internal query to resolve the "taskHList" to-many relationship of User. */
    public List<TaskH> _queryUser_TaskHList(String uuid_user) {
        synchronized (this) {
            if (user_TaskHListQuery == null) {
                QueryBuilder<TaskH> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_user.eq(null));
                user_TaskHListQuery = queryBuilder.build();
            }
        }
        Query<TaskH> query = user_TaskHListQuery.forCurrentThread();
        query.setParameter(0, uuid_user);
        return query.list();
    }

    /** Internal query to resolve the "taskHList" to-many relationship of Scheme. */
    public List<TaskH> _queryScheme_TaskHList(String uuid_scheme) {
        synchronized (this) {
            if (scheme_TaskHListQuery == null) {
                QueryBuilder<TaskH> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_scheme.eq(null));
                scheme_TaskHListQuery = queryBuilder.build();
            }
        }
        Query<TaskH> query = scheme_TaskHListQuery.forCurrentThread();
        query.setParameter(0, uuid_scheme);
        return query.list();
    }

    private String selectDeep;

    protected String getSelectDeep() {
        if (selectDeep == null) {
            StringBuilder builder = new StringBuilder("SELECT ");
            SqlUtils.appendColumns(builder, "T", getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T0", daoSession.getUserDao().getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T1", daoSession.getSchemeDao().getAllColumns());
            builder.append(" FROM TR_TASK_H T");
            builder.append(" LEFT JOIN MS_USER T0 ON T.\"UUID_USER\"=T0.\"UUID_USER\"");
            builder.append(" LEFT JOIN MS_SCHEME T1 ON T.\"UUID_SCHEME\"=T1.\"UUID_SCHEME\"");
            builder.append(' ');
            selectDeep = builder.toString();
        }
        return selectDeep;
    }
    
    protected TaskH loadCurrentDeep(Cursor cursor, boolean lock) {
        TaskH entity = loadCurrent(cursor, 0, lock);
        int offset = getAllColumns().length;

        User user = loadCurrentOther(daoSession.getUserDao(), cursor, offset);
        entity.setUser(user);
        offset += daoSession.getUserDao().getAllColumns().length;

        Scheme scheme = loadCurrentOther(daoSession.getSchemeDao(), cursor, offset);
        entity.setScheme(scheme);

        return entity;    
    }

    public TaskH loadDeep(Long key) {
        assertSinglePk();
        if (key == null) {
            return null;
        }

        StringBuilder builder = new StringBuilder(getSelectDeep());
        builder.append("WHERE ");
        SqlUtils.appendColumnsEqValue(builder, "T", getPkColumns());
        String sql = builder.toString();
        
        String[] keyArray = new String[] { key.toString() };
        Cursor cursor = db.rawQuery(sql, keyArray);
        
        try {
            boolean available = cursor.moveToFirst();
            if (!available) {
                return null;
            } else if (!cursor.isLast()) {
                throw new IllegalStateException("Expected unique result, but count was " + cursor.getCount());
            }
            return loadCurrentDeep(cursor, true);
        } finally {
            cursor.close();
        }
    }
    
    /** Reads all available rows from the given cursor and returns a list of new ImageTO objects. */
    public List<TaskH> loadAllDeepFromCursor(Cursor cursor) {
        int count = cursor.getCount();
        List<TaskH> list = new ArrayList<TaskH>(count);
        
        if (cursor.moveToFirst()) {
            if (identityScope != null) {
                identityScope.lock();
                identityScope.reserveRoom(count);
            }
            try {
                do {
                    list.add(loadCurrentDeep(cursor, false));
                } while (cursor.moveToNext());
            } finally {
                if (identityScope != null) {
                    identityScope.unlock();
                }
            }
        }
        return list;
    }
    
    protected List<TaskH> loadDeepAllAndCloseCursor(Cursor cursor) {
        try {
            return loadAllDeepFromCursor(cursor);
        } finally {
            cursor.close();
        }
    }
    

    /** A raw-style query where you can pass any WHERE clause and arguments. */
    public List<TaskH> queryDeep(String where, String... selectionArg) {
        Cursor cursor = db.rawQuery(getSelectDeep() + where, selectionArg);
        return loadDeepAllAndCloseCursor(cursor);
    }
 
}
