package com.adins.mss.base.dynamicform.form.questions;

import android.content.Context;
import android.os.Environment;
import android.util.Log;
import android.view.View;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.commons.Query;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.decision.GenericRuleJexlLogic;
import com.adins.mss.base.decision.RuleLogic;
import com.adins.mss.base.decision.RuleParam;
import com.adins.mss.base.dukcapil.ResponseImageDkcp;
import com.adins.mss.base.dynamicform.Constant;
import com.adins.mss.base.dynamicform.DynamicFormActivity;
import com.adins.mss.base.dynamicform.form.FragmentQuestion;
import com.adins.mss.base.dynamicform.form.resolver.DateResolver;
import com.adins.mss.base.dynamicform.form.resolver.DateTimeResolver;
import com.adins.mss.base.dynamicform.form.resolver.TimeResolver;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.Blacklist;
import com.adins.mss.dao.GeneralParameter;
import com.adins.mss.dao.Holiday;
import com.adins.mss.dao.LocationInfo;
import com.adins.mss.dao.Lookup;
import com.adins.mss.dao.ProductOffering;
import com.adins.mss.foundation.camerainapp.helper.Logger;
import com.adins.mss.foundation.db.dataaccess.BlacklistDataAccess;
import com.adins.mss.foundation.db.dataaccess.GeneralParameterDataAccess;
import com.adins.mss.foundation.db.dataaccess.HolidayDataAccess;
import com.adins.mss.foundation.db.dataaccess.LookupDataAccess;
import com.adins.mss.foundation.formatter.Formatter;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.MssResponseType;
import com.adins.mss.foundation.operators.IfElseFunction;
import com.adins.mss.foundation.questiongenerator.NotEqualSymbol;
import com.adins.mss.foundation.questiongenerator.OptionAnswerBean;
import com.adins.mss.foundation.questiongenerator.QuestionBean;
import com.gadberry.utility.expression.Expression;
import com.gadberry.utility.expression.OperatorSet;

import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by gigin.ginanjar on 30/08/2016.
 */
public class QuestionsValidator {
    public static final String HOLIDAY_IS_NOT_ALLOWED = "1";
    public static final String BETWEEN = "BETWEEN";
    private final String msgRequired;
    private final Context context;

    public QuestionsValidator(String msgRequired, Context context) {
        this.msgRequired = msgRequired;
        this.context = context;
    }

    public List<String> validateGeneratedQuestionView(Context context, QuestionBean bean) {
        List<String> errMessage = null;
        String answerType = bean.getAnswer_type();
        if (bean.getIs_visible().equals(Global.FALSE_STRING))
            return null;
        if (QuestionViewAdapter.IsTextQuestion(Integer.valueOf(answerType))) {
           return validateTextQuestion(context, bean);//, (TextQuestionViewHolder) view);
        }else if(QuestionViewAdapter.IsDateTimeQuestion(Integer.valueOf(answerType))){
            return validateDateTimeQuestion(bean);
        } else if (QuestionViewAdapter.IsTextWithSuggestionQuestion(Integer.valueOf(answerType))) {
            return validateTextWithSuggestionQuestion(bean);
        } else if (QuestionViewAdapter.IsLocationQuestion(Integer.valueOf(answerType))) {
            return validateLocationQuestion(bean);
        } else if (Tool.isOptions(answerType)) {
            return validateMultipleQuestion(bean);
        } else if (QuestionViewAdapter.IsImageQuestion(Integer.valueOf(answerType))) {
            return validateImageQuestion(bean);
        } else if (QuestionViewAdapter.IsDrawingQuestion(Integer.valueOf(answerType))) {
            return validateDrawingQuestion(bean);
        } else if (QuestionViewAdapter.IsLookupQuestion(Integer.valueOf(answerType))) {
            return validateLookupQuestion(bean);
        }else if(QuestionViewAdapter.IsScoringQuestion(Integer.valueOf(answerType))){
            return validateTextQuestion(context, bean);
        } else if(QuestionViewAdapter.isSubmitLayerQuestion(Integer.parseInt(answerType))) {
            return validateSubmitLayerQuestion(bean);
        } else if (QuestionViewAdapter.isTeleCheckQuestion(Integer.parseInt(answerType))) {
            return validateTeleQuestion(bean);
        } else if (QuestionViewAdapter.isGetDSRQuestion(Integer.parseInt(answerType))) {
            return validateGetDSRQuestion(bean);
        } else if (QuestionViewAdapter.isTextMultilineSeparate(Integer.parseInt(answerType))) {
            return validateTextMultilineSeparate(bean);
        }  else if(QuestionViewAdapter.IsInvitationEsignQuestion(Integer.valueOf(answerType))) {
            return validateInvitationEsignQuestion(bean);
        } else if (QuestionViewAdapter.isRegistrationCheckQuestion(Integer.parseInt(answerType))) {
            return validateRegistrationCheckQuestion(bean);
        } else if (QuestionViewAdapter.isGetOTRQuestion(Integer.parseInt(answerType))) {
            return validateGetOTRQuestion(bean);
        } else if (QuestionViewAdapter.isCheckReferantorQuestion(Integer.parseInt(answerType))) {
            return validateCheckReferantorQuestion(bean);
        } else if (QuestionViewAdapter.isValidationCheckQuestion(Integer.parseInt(answerType))) {
            return validateCheckQuestion(bean);
        }

        return errMessage;
    }

    private List<String> validateTeleQuestion(QuestionBean bean) {
        List<String> errMessage = new ArrayList<>();
        if (bean.isRelevantMandatory() && (bean.getAnswer() == null || bean.getAnswer().isEmpty())) {
            errMessage.add(bean.getQuestion_label() + " " + msgRequired);
        }
        if (bean.getIs_mandatory().equals(Global.TRUE_STRING) && (bean.getAnswer() == null || bean.getAnswer().isEmpty())) {
            errMessage.add(bean.getQuestion_label() + " " + msgRequired);
        }
        if (errMessage.isEmpty() && bean.getQuestion_validation() != null && !bean.getQuestion_validation().isEmpty() && QuestionBean.isHaveAnswer(bean)) {
            String script = bean.getQuestion_validation();
            String answerType = bean.getAnswer_type();
            String answer = QuestionBean.getAnswer(bean);
            if (!validateByScript(answer, answerType, script)) {
                if (bean.getValidate_err_message() != null && !bean.getValidate_err_message().isEmpty()) {
                    errMessage.add(bean.getQuestion_label() + " " + bean.getValidate_err_message());
                } else {
                    errMessage.add(bean.getQuestion_label() + ": " + context.getString(R.string.input_not_valid));
                }
            }
        }

        return errMessage;
    }

    private List<String> validateLookupQuestion(QuestionBean bean) {
        List<String> errMessage = new ArrayList<String>();
        if (bean.isRelevantMandatory()) {
            if (bean.getAnswer() == null || bean.getAnswer().isEmpty()) {
                errMessage.add(bean.getQuestion_label() + " " + msgRequired);
            }
        }
        if (bean.getIs_mandatory().equals(Global.TRUE_STRING)) {
            if (bean.getAnswer() == null || bean.getAnswer().isEmpty()) {
                errMessage.add(bean.getQuestion_label() + " " + msgRequired);
            }
        }
        if (errMessage.isEmpty() && bean.getQuestion_validation() != null && !bean.getQuestion_validation().isEmpty() && QuestionBean.isHaveAnswer(bean)) {
            String script = bean.getQuestion_validation();
            String answerType = bean.getAnswer_type();
            String answer = QuestionBean.getAnswer(bean);
            if (!validateByScript(answer, answerType, script)) {
                if (bean.getValidate_err_message() != null && !bean.getValidate_err_message().isEmpty()) {
                    errMessage.add(bean.getQuestion_label() + " " + bean.getValidate_err_message());
                } else {
                    errMessage.add(bean.getQuestion_label() + ": " + context.getString(R.string.input_not_valid));
                }
            }
        }

        return errMessage;
    }

    private List<String> validateDrawingQuestion(QuestionBean bean) {
        List<String> errMessage = new ArrayList<String>();

        //Add Bypass Image Validation
        if (DynamicFormActivity.bypassImageValidation) {
            if (bean.isRepeatTtd()) {
                errMessage.add(bean.getQuestion_label() + " " + context.getString(R.string.err_need_re_sign));
            }
        } else {
            if (bean.getIs_mandatory().equals(Global.TRUE_STRING)) {
                byte[] img = bean.getImgAnswer();
                if (img == null || img.length < 1) {
                    errMessage.add(bean.getQuestion_label() + " " + msgRequired);
                }
            }
        }

        return errMessage;
    }

    private List<String> validateImageQuestion(QuestionBean bean) {
        List<String> errMessage = new ArrayList<String>();
        //NENDI: 20/01/2018 Add Bypass Image Validation on Mandatory
        if(DynamicFormActivity.isVerified || DynamicFormActivity.isApproval || DynamicFormActivity.bypassImageValidation) {
        }
        else{
            if (bean.getIs_mandatory().equals(Global.TRUE_STRING)) {
                byte[] img = bean.getImgAnswer();
                if (img == null || img.length < 1) {
                    errMessage.add(bean.getQuestion_label() + " " + msgRequired);
                    return errMessage;
                }
                String answerType = bean.getAnswer_type();
                if (answerType.equals(Global.AT_IMAGE_W_LOCATION) ||
                        answerType.equals(Global.AT_IMAGE_W_GPS_ONLY)) {
                    LocationInfo locationInfo = bean.getLocationInfo();
                    if (locationInfo != null) {
                        if (locationInfo.getLatitude().equals("0.0") || locationInfo.getLongitude().equals("0.0")) {
                            if (answerType.equals((Global.AT_IMAGE_W_GPS_ONLY))) {
                                errMessage.add(context.getString(R.string.gps_gd_error));
                            } else {
                                if (locationInfo.getMcc().equals("0") || locationInfo.getMnc().equals("0")) {
                                    errMessage.add(context.getString(R.string.lbs_gd_error));
                                }
                            }
                        }
                    } else {
                        errMessage.add(context.getString(R.string.gps_error));
                    }
                }
            }
        }

        if (Global.AT_OCR_W_GALLERY.equalsIgnoreCase(bean.getAnswer_type())
                && Global.TAG_OCR_W_GALLERY.equalsIgnoreCase(bean.getTag())) {
            ResponseImageDkcp responseDkcp = bean.getResponseImageDkcp();
            if (responseDkcp == null && "1".equalsIgnoreCase(bean.getIs_mandatory())) {
                errMessage.add(bean.getQuestion_label()+": Can't get data OCR, Please Try Again");
            } else if (responseDkcp != null && "1".equalsIgnoreCase(responseDkcp.getIsStop()) &&
                    StringUtils.isNotBlank(responseDkcp.getErrorMessage())) {
                errMessage.add(responseDkcp.getErrorMessage());
            }
        }
        return errMessage;
    }

    private List<String> validateMultipleQuestion(QuestionBean bean) {
        List<String> errMessage = new ArrayList<String>();

        if (bean.getIs_mandatory().equals(Global.TRUE_STRING) || bean.isRelevantMandatory()) {
            if (bean.getSelectedOptionAnswers() == null || bean.getSelectedOptionAnswers().isEmpty()) {
                errMessage.add(bean.getQuestion_label() + " " + msgRequired);
            }
            else if (errMessage.isEmpty() && QuestionBean.isHaveAnswer(bean)) {
                String answer = QuestionBean.getAnswer(bean);

                if (answer != null && (answer.equalsIgnoreCase("00000") || answer.equalsIgnoreCase("$$$") || answer.equalsIgnoreCase("Pilih Salah Satu"))) {
                    errMessage.add("Please choose option of " + bean.getQuestion_label());
                }
            }
        }
        if (errMessage.isEmpty() && bean.getQuestion_validation() != null && !bean.getQuestion_validation().isEmpty() && QuestionBean.isHaveAnswer(bean)) {
            String script = bean.getQuestion_validation();
            String answerType = bean.getAnswer_type();
            String answer = QuestionBean.getAnswer(bean);
            if (!validateByScript(answer, answerType, script)) {
                if (bean.getValidate_err_message() != null && !bean.getValidate_err_message().isEmpty()) {
                    errMessage.add(bean.getQuestion_label() + " " + bean.getValidate_err_message());
                } else {
                    errMessage.add(bean.getQuestion_label() + ": " + context.getString(R.string.input_not_valid));
                }
            }
        }

        // Validasinya pos dealernya emang mati
//        else if (errMessage.isEmpty() && QuestionBean.isHaveAnswer(bean)) {
//            String answer = QuestionBean.getAnswer(bean);
//            if (answer != null && (answer.equalsIgnoreCase("00000") || answer.equalsIgnoreCase("$$$") ||
//                    answer.equalsIgnoreCase("Pilih Salah Satu"))) {
//                errMessage.add("Please choose option of " + bean.getQuestion_label());
//            }
//        }
        return errMessage;
    }

    public static boolean regexIsMatch(String s, String pattern) {
        try {
            Pattern patt = Pattern.compile(pattern);
            Matcher matcher = patt.matcher(s);
            return matcher.matches();
        } catch (RuntimeException e) {
            return false;
        }
    }

    private List<String> validateTextQuestion(Context context, QuestionBean bean){//}, TextQuestionViewHolder view) {
        List<String> errMessage = new ArrayList<>();
        String answer = bean.getAnswer();//view.mQuestionAnswer.getText().toString().trim();
        if (Global.AT_CURRENCY.equals(bean.getAnswer_type())) {
            if (answer != null && answer.length() > 0) {
                String tempAnswer = Tool.deleteAll(answer, ",");
                String[] intAnswer = Tool.split(tempAnswer, ".");
                if (intAnswer.length > 1) {
                    if (intAnswer[1].equals("00"))
                        answer = intAnswer[0];
                    else {
                        answer = tempAnswer;
                    }
                } else {
                    answer = tempAnswer;
                }
            }
        } else if (Global.TAG_URL_LINK.equalsIgnoreCase(bean.getTag()) &&
                (StringUtils.isBlank(answer) || !Global.ACCEPTED_AGREEMENT.equalsIgnoreCase(answer.split(Global.DELIMETER_DATA4)[0]))) {
            errMessage.add(bean.getQuestion_label() + " " + msgRequired);
        }

        bean.setAnswer(answer);
        if (bean.getIs_mandatory().equals(Global.TRUE_STRING) || bean.isRelevantMandatory()) {
            if (answer == null || "".equals(answer)) {
                errMessage.add(bean.getQuestion_label() + " " + msgRequired);
            }
        }

        String regex = "";
        regex = bean.getRegex();
        if (regex == null) regex = "";

        if (errMessage.isEmpty()) {
            if (!regex.equals("")) {
                if ((answer != null && !answer.isEmpty()) || bean.isMandatory() || bean.isRelevantMandatory()) {
                    if (Global.IS_DEV) Logger.i("INFO", "!regex.equals" + regex);
                    if (!regexIsMatch(bean.getAnswer(), bean.getRegex())) {
                        errMessage.add(bean.getQuestion_label() + " " + "Invalid Input Format");
                    }
                }
            }
        }

        if(errMessage.isEmpty() && bean.getQuestion_validation()!=null && !bean.getQuestion_validation().isEmpty() && QuestionBean.isHaveAnswer(bean)){
            //Nendi: 23/01/2018 | Add Question Validation using BLACKLIST
            String textValidation = bean.getQuestion_validation();
            if (textValidation.contains("@")) {
                String[] rules= Tool.split(textValidation, Global.DELIMETER_DATA4);
                String source = rules[0];
                String type   = rules[1];
                int intType   = 0;
                if (source.equalsIgnoreCase("BLACKLIST")) {
                    Blacklist blacklist = new Blacklist();
                    blacklist.setExclude_type_code(type);
                    blacklist.setExclude_type_name(type);

                    if (type.equalsIgnoreCase("MBLPHN")) {
                        String exc = rules[2];
                        intType    = Blacklist.TYPE_PHONE;
                        if (exc.equalsIgnoreCase("1")) {
                            blacklist.setExclude_info1(bean.getAnswer());
                        }
                        else {
                            blacklist.setExclude_info2(bean.getAnswer());
                        }
                    }
                    else if (type.equalsIgnoreCase("NAME")) {
                        intType = Blacklist.TYPE_NAME;
                        blacklist.setExclude_info2(bean.getAnswer());
                    }

                    boolean isBlacklist = BlacklistDataAccess.validate(context, intType, blacklist);
                    if (isBlacklist) {
                        if(bean.getValidate_err_message()!=null && !bean.getValidate_err_message().isEmpty()){
                            errMessage.add(bean.getQuestion_label() + " " + bean.getValidate_err_message());
                        }else{
                            errMessage.add(bean.getQuestion_label() + ": " + context.getString(R.string.input_not_valid));
                        }
                    }
                }
            }
            else if (textValidation.contains(BETWEEN)) {
                Double value        = Double.parseDouble(bean.getAnswer());
                if (value > 0) {
                    Double marketPrice  = FragmentQuestion.marketPrice;
                    Double tolerance    = marketPrice * (FragmentQuestion.tolerancePrctg/100);
                    Double minPrice     = marketPrice - tolerance;
                    Double maxPrice     = marketPrice + tolerance;
                    if (value >= minPrice && value <= maxPrice) {
                        //
                    } else {
                        errMessage.add(bean.getQuestion_label() + ": " + context.getString(R.string.message_error_between, minPrice, maxPrice));
                    }
                }
            }
            else {
                int idxOpenAbs = textValidation.indexOf("$");
                if (idxOpenAbs != -1) {
                    //Nendi: Validate by Abs Method
                    String absMethod = textValidation.substring(idxOpenAbs + 1);
                    if (absMethod.contains("TENOR")) {
                        int value = Integer.parseInt(bean.getAnswer());
                        ProductOffering po = Constant.productOff;
                        if (value >= po.getMin_tenor() && value <= po.getMax_tenor()) {
                            //Validation Pass
                        } else {
                            if (bean.getValidate_err_message() != null && !bean.getValidate_err_message().isEmpty()) {
                                errMessage.add(bean.getQuestion_label() + " " + bean.getValidate_err_message());
                            } else {
                                errMessage.add(bean.getQuestion_label() + " value must be higher than " + po.getMin_tenor() + " and " + po.getMax_tenor());
                            }
                        }
                    } else {
                        //Validate by Script
                        String script = bean.getQuestion_validation();
                        String answerType= bean.getAnswer_type();
                        if(!validateByScript(answer, answerType, script)){
                            if(bean.getValidate_err_message()!=null && !bean.getValidate_err_message().isEmpty()){
                                errMessage.add(bean.getQuestion_label() + " " + bean.getValidate_err_message());
                            }else{
                                errMessage.add(bean.getQuestion_label() + ": " + context.getString(R.string.input_not_valid));
                            }
                        }
                    }
                } else {
                    try {
                        //Nendi: Validate by Rule Engine
                        RuleParam ruleParam = GsonHelper.fromJson(textValidation, RuleParam.class);
                        boolean validated   = false;
                        double minimum;
                        GenericRuleJexlLogic logic;
                        switch (ruleParam.getFile()) {
                            case "DP":
                                RuleLogic ruleEngine= new GenericRuleJexlLogic();
                                validated = ruleEngine.validateDP(context, bean, ruleParam);
                                //TODO: Change Error Message by Server Side
                                if (!validated) errMessage.add(bean.getQuestion_label() + " Must be Higher");
                                break;

                            case "DP_AMT":
                                logic   = new GenericRuleJexlLogic();
                                minimum = logic.getDepositAmount(context, ruleParam);
                                Double amtValue = Double.parseDouble(bean.getAnswer());
//                                double value   = Tool.separateThousand()
//                                if (!validated) errMessage.add("DP Gross Must be Higher");
                                if (amtValue < minimum) errMessage.add(bean.getQuestion_label() + " value must be higher or equal " + minimum);
                                break;

                            case "DP_PRCNT":
                                logic   = new GenericRuleJexlLogic();
                                minimum = logic.getDepositPercent(context, ruleParam, "DP_PRCNT");
                                Double prcValue = Double.parseDouble(bean.getAnswer());
                                if (prcValue < minimum) errMessage.add(bean.getQuestion_label() + " value must be higher or equal " + minimum);
                                break;

                            case "TDP":
                                logic   = new GenericRuleJexlLogic();
                                minimum = logic.getDepositPercent(context, ruleParam, "TDP");
                                Double tdpValue = Double.parseDouble(bean.getAnswer());
                                if (tdpValue < minimum) errMessage.add(bean.getQuestion_label() + " value must be higher or equal " + minimum);
                                break;

                            case "MANF_YEAR":
                                logic   = new GenericRuleJexlLogic();
                                Integer minYear = logic.minManfYear(context, ruleParam);
                                Integer valYear = Integer.parseInt(bean.getAnswer());
                                if (valYear < minYear) errMessage.add(bean.getQuestion_label() + " value must be higher or equal " + minYear);
                                break;
                        }
                    } catch (Exception ex) {
                        //Validate by Script
                        ex.printStackTrace();
                        String script = bean.getQuestion_validation();
                        String answerType= bean.getAnswer_type();
                        if(!validateByScript(answer, answerType,script)){
                            if(bean.getValidate_err_message()!=null && !bean.getValidate_err_message().isEmpty()){
                                errMessage.add(bean.getQuestion_label() + " " + bean.getValidate_err_message());
                            }else{
                                errMessage.add(bean.getQuestion_label() + ": " + context.getString(R.string.input_not_valid));
                            }
                        }
                    }
                }
            }
        }

        if (StringUtils.isNotBlank(bean.getChoice_filter())
                && bean.getChoice_filter().contains("fields") && !QuestionBean.isHaveAnswer(bean)) {
            try {
                Query query =  GsonHelper.fromJson(bean.getChoice_filter(), Query.class);;
                if ("MS_MARKET_PRICE".equalsIgnoreCase(query.getTable())) {
                    errMessage.add(bean.getQuestion_label() + ": " + context.getString(R.string.market_price_not_found));
                }
            } catch (Exception ex) {
                if(Global.IS_DEV) {
                    ex.printStackTrace();
                }
            }
        }

        return errMessage;
    }

    private List<String> validateTextWithSuggestionQuestion(QuestionBean bean) {
        List<String> errMessage = new ArrayList<String>();
        String answer = bean.getAnswer();
        Lookup selectedOption = null;
        if (answer != null && !answer.isEmpty()) {
            List<OptionAnswerBean> selectedOptions = bean.getSelectedOptionAnswers();
            if (selectedOptions == null || selectedOptions.isEmpty()) {
                selectedOption = LookupDataAccess.getOneByValueAndlovGroup(context, bean.getLov_group(), answer);
                if (selectedOption == null) {
                    if (!Tool.isInternetconnected(context))
                        errMessage.add(bean.getQuestion_label() + ": " + answer + " " + context.getString(R.string.not_available));
                    else
                        errMessage.add(bean.getQuestion_label() + ": " + answer + " " + context.getString(R.string.not_allowed));
                }
            }
        }
        if (bean.getIs_mandatory().equals(Global.TRUE_STRING) || bean.isRelevantMandatory()) {
            if (answer == null || answer.isEmpty()) {
                if (!errMessage.isEmpty())
                    errMessage.add("\n");
                errMessage.add(bean.getQuestion_label() + " " + msgRequired);
            }
        }
        String regex = "";
        regex = bean.getRegex();
        if (regex == null) regex = "";
        if (!regex.equals("") && !bean.getAnswer().trim().equals("")) {
            if (Global.IS_DEV) Logger.i("INFO", "!regex.equals" + regex);
            if (!regexIsMatch(bean.getAnswer(), bean.getRegex())) {
                errMessage.add(bean.getQuestion_label() + " " + "Invalid Input Format");
            }
        }
        if (errMessage.isEmpty() && bean.getQuestion_validation() != null && !bean.getQuestion_validation().isEmpty() && QuestionBean.isHaveAnswer(bean)) {
            String script = bean.getQuestion_validation();
            String answerType = bean.getAnswer_type();
            String answer2 = selectedOption.getCode();
            if (!validateByScript(answer2, answerType, script)) {
                if (bean.getValidate_err_message() != null && !bean.getValidate_err_message().isEmpty()) {
                    errMessage.add(bean.getQuestion_label() + " " + bean.getValidate_err_message());
                } else {
                    errMessage.add(bean.getQuestion_label() + ": " + context.getString(R.string.input_not_valid));
                }
            }
        }
        return errMessage;
    }

    public List<String> validateLocationQuestion(QuestionBean bean) {
        List<String> errMessage = new ArrayList<String>();

        if (DynamicFormActivity.isVerified || DynamicFormActivity.isApproval) {

        } else {
            String answer = bean.getAnswer();

            if (bean.getIs_mandatory().equals(Global.TRUE_STRING) || bean.isRelevantMandatory()) {
                if (answer == null || "".equals(answer)) {
                    errMessage.add(bean.getQuestion_label() + " " + msgRequired);
                }
            }
        }
        return errMessage;
    }

    public List<String> validateDateTimeQuestion(QuestionBean bean) {
        List<String> errMessage = new ArrayList<String>();

        String answer = bean.getAnswer();
        Date date2 = null;
        boolean isSet = true;
        String answerType = bean.getAnswer_type();
        try {
            String format = null;
            if (Global.AT_DATE.equals(answerType)) {
                format = Global.DATE_STR_FORMAT6;
                date2 = Formatter.parseDate(answer, format);
            } else if (Global.AT_TIME.equals(answerType)) {
                format = Global.TIME_STR_FORMAT2;
                date2 = Formatter.parseDate(answer, format);
            } else if (Global.AT_DATE_TIME.equals(answerType)) {
                format = Global.DATE_STR_FORMAT_GSON;
                date2 = Formatter.parseDate(answer, format);
            }
        } catch (Exception pe) {
            try {
                String format = null;
                if (Global.AT_DATE.equals(answerType)) {
                    format = Global.DATE_STR_FORMAT;
                    date2 = Formatter.parseDate(answer, format);
                } else if (Global.AT_TIME.equals(answerType)) {
                    format = Global.TIME_STR_FORMAT;
                    date2 = Formatter.parseDate(answer, format);
                } else if (Global.AT_DATE_TIME.equals(answerType)) {
                    format = Global.DATE_TIME_STR_FORMAT;
                    date2 = Formatter.parseDate(answer, format);
                }
            } catch (Exception e) {
                FireCrash.log(e);
                isSet = false;
            }
        }

        if (bean.getIs_holiday_allowed() != null && !bean.getIs_holiday_allowed().equals(HOLIDAY_IS_NOT_ALLOWED)) {
            Date date3 = null;
            if (Global.AT_DATE_TIME.equals(answerType)) {
                String date4;
                try {
                    date4 = Formatter.formatDate(date2, Global.DATE_STR_FORMAT2);
                    date3 = Formatter.parseDate(date4, Global.DATE_STR_FORMAT2);
                } catch (Exception e) {
                    FireCrash.log(e);
                    if (Global.IS_DEV)
                        e.printStackTrace();
                }
            } else {
                try {
                    date3 = Formatter.parseDate(answer, Global.DATE_STR_FORMAT_GSON);
                } catch (Exception e) {
                    FireCrash.log(e);
                    e.printStackTrace();
                }
            }

            if (date3 != null) {
                Holiday hday = HolidayDataAccess.getOneByDate(context, date3);
                if (hday != null) {
                    errMessage.add(bean.getQuestion_label() + " "
                            + "can not set on " + hday.getH_desc());
                }
            }
        }

        if (bean.getIs_mandatory().equals(Global.TRUE_STRING) || bean.isRelevantMandatory()) {
            if (!isSet) {
                errMessage.add(bean.getQuestion_label() + " " + msgRequired);
            }
        }

        if (Global.REF_ID_STV_VISIT_DATE.equalsIgnoreCase(bean.getIdentifier_name()) ||
                Global.REF_ID_STV_INPUT_DATE.equalsIgnoreCase(bean.getIdentifier_name())) {

            String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
            GeneralParameter gp = GeneralParameterDataAccess.getOne(context, uuidUser, Global.GS_MAX_DURATION_VISIT_DATE);

            try {
                int maxDays = gp == null ? 1 : Integer.parseInt(gp.getGs_value());
                Calendar now = Calendar.getInstance(TimeZone.getDefault());
                Calendar date = Calendar.getInstance(TimeZone.getDefault());
                date.setTime(date2);

                if (Global.AT_TIME.equals(answerType)) {
                    date.set(Calendar.YEAR, now.get(Calendar.YEAR));
                    date.set(Calendar.MONTH, now.get(Calendar.MONTH));
                    date.set(Calendar.DAY_OF_MONTH, now.get(Calendar.DAY_OF_MONTH));
                }

                double diff = (now.getTime().getTime() - date.getTime().getTime());
                double diffDay = diff / (24 * 60 * 60 * 1000);

                if (date.after(now)) {
                    diff = (date.getTime().getTime() - now.getTime().getTime());
                    diffDay = diff / (24 * 60 * 60 * 1000);
                }

                if (diffDay > maxDays) {
                    errMessage.add(bean.getQuestion_label() + " " + context.getString(R.string.not_allow_more_than_max_visit_date, String.valueOf(maxDays)));
                }
            } catch (Exception e) {
                FireCrash.log(e);
            }
        }
        else {
            int txtMaxLength = 0;
            try {
                if (bean.getMax_length() > 0) {
                    Calendar now = Calendar.getInstance(TimeZone
                            .getDefault());
                    Calendar date = Calendar.getInstance(TimeZone
                            .getDefault());
                    date.setTime(date2);
                    if (Global.AT_TIME.equals(answerType)) {
                        date.set(Calendar.YEAR, now.get(Calendar.YEAR));
                        date.set(Calendar.MONTH, now.get(Calendar.MONTH));
                        date.set(Calendar.DAY_OF_MONTH, now.get(Calendar.DAY_OF_MONTH));
                    }
                    long diff = (date.getTime().getTime() - now.getTime()
                            .getTime());
                    long diffDay = diff / 86400000;
                    long diffHour = diff % 86400000;
                    long diffHours = diffHour / 3600000;
                    if (diffHours > 0) {
                        diffDay += 1;
                    }
                    if (diffDay < 0) {
                        txtMaxLength = (int) diffDay;
                    } else if (diffDay > bean.getMax_length()) {
                        txtMaxLength = (int) diffDay;
                    } else {
                        txtMaxLength = bean.getMax_length();
                    }
                } else {
                    Date today = Tool.getSystemDate();
                    Calendar now = Calendar.getInstance(TimeZone
                            .getDefault());
                    now.setTime(today);
                    Calendar date = Calendar.getInstance(TimeZone
                            .getDefault());
                    date.setTime(date2);
                    if (Global.AT_TIME.equals(answerType)) {
                        date.set(Calendar.YEAR, now.get(Calendar.YEAR));
                        date.set(Calendar.MONTH, now.get(Calendar.MONTH));
                        date.set(Calendar.DAY_OF_MONTH, now.get(Calendar.DAY_OF_MONTH));
                    }
                    long diff = (date.getTime().getTime() - now.getTime()
                            .getTime());
                    long diffDay = diff / 86400000;

                    if (diffDay > 0) {
                        txtMaxLength = (int) diffDay;
                    } else if (diffDay < bean.getMax_length()) {
                        txtMaxLength = (int) diffDay;
                    } else {
                        txtMaxLength = bean.getMax_length();
                    }
                }
                if (bean.getMax_length() == -999) {
                    if (txtMaxLength > 0) {
                        errMessage.add(bean.getQuestion_label() + " " + context.getString(R.string.date_must_before_today));
                    }
                } else if (bean.getMax_length() < 0) {
                    if (txtMaxLength > 0) {
                        errMessage.add(bean.getQuestion_label() + " " + context.getString(R.string.date_must_before_today));
                    } else if (txtMaxLength < bean.getMax_length()) {
                        /*errMessage.add(bean.getQuestion_label()+" "+context.getString(R.string.more_than)
                                +" " + txtMaxLength + " "+context.getString(R.string.day)+"!");*/
                        Calendar date = Calendar.getInstance(TimeZone
                                .getDefault());
                        date.setTime(Tool.getSystemDateTime());
                        date.add(Calendar.DATE, bean.getMax_length());
                        Date newDate = date.getTime();
                        String sDate = Formatter.formatDate(newDate, Global.DATE_STR_FORMAT3);
                        String message = bean.getQuestion_label() + " " + context.getString(R.string.more_than_date, sDate);
                        errMessage.add(message);
                    }
                } else if (bean.getMax_length() > 0) {
                    if (txtMaxLength < 0) {
                        errMessage.add(bean.getQuestion_label() + " " + context.getString(R.string.date_must_after_today));
                    } else if (txtMaxLength > bean.getMax_length()) {
                        /*errMessage.add(bean.getQuestion_label()+" "+context.getString(R.string.less_than)
                                +" " + bean.getMax_length() + " "+context.getString(R.string.day)+"!");*/
                        Calendar date = Calendar.getInstance(TimeZone
                                .getDefault());
                        date.setTime(Tool.getSystemDateTime());
                        date.add(Calendar.DATE, bean.getMax_length());
                        Date newDate = date.getTime();
                        String sDate = Formatter.formatDate(newDate, Global.DATE_STR_FORMAT3);
                        String message = bean.getQuestion_label() + " " + context.getString(R.string.less_than_date, sDate);
                        errMessage.add(message);
                    }
                }
            } catch (Exception e) {
                FireCrash.log(e);
                if (Global.IS_DEV)
                    e.printStackTrace();
            }
        }

        if (errMessage.isEmpty() && bean.getQuestion_validation() != null && !bean.getQuestion_validation().isEmpty() && QuestionBean.isHaveAnswer(bean)) {
            String script = bean.getQuestion_validation().trim();
            if (!validateByScript(answer, answerType, script)) {
                if (bean.getValidate_err_message() != null && !bean.getValidate_err_message().isEmpty()) {
                    errMessage.add(bean.getQuestion_label() + " " + bean.getValidate_err_message());
                } else {
                    errMessage.add(bean.getQuestion_label() + ": " + context.getString(R.string.input_not_valid));
                }
            }
        }
        return errMessage;
    }

    private List<String> validateInvitationEsignQuestion(QuestionBean bean) {
        List<String> errMessage = new ArrayList<>();
        String answer = bean.getAnswer();

        if (Global.TRUE_STRING.equals(bean.getIs_mandatory()) || bean.isRelevantMandatory()) {
            if (answer == null || "".equals(answer)) {
                errMessage.add(bean.getQuestion_label() + " " + msgRequired);
            } else if (!Global.TRUE_STRING.equals(bean.isValid())) {
                errMessage.add(bean.getQuestion_label() + " " + context.getString(R.string.input_not_valid));
            }
        }

        if (StringUtils.isNotBlank(bean.getQuestion_validation())) {
            String validateScript = bean.getQuestion_validation();
            if (bean.getQuestion_validation().toUpperCase().contains("$MAXRETRY()")) {
                int validasiRetry = bean.getMaxRetry() - bean.getCountRetry();
                if (validasiRetry <= 0) {
                    validateScript = validateScript.replace("$MAXRETRY()", "true");
                } else {
                    validateScript = validateScript.replace("$MAXRETRY()", "false");
                }
            }
            if (!validateByScript(answer, bean.getAnswer_type(), validateScript)) {
                if (bean.getValidate_err_message() != null && !bean.getValidate_err_message().isEmpty()) {
                    errMessage.add(bean.getQuestion_label() + " " + bean.getValidate_err_message());
                } else {
                    errMessage.add(bean.getQuestion_label() + ": " + context.getString(R.string.input_not_valid));
                }
            }
        }
        return errMessage;
    }

    private List<String> validateRegistrationCheckQuestion(QuestionBean bean) {
        List<String> errMessage = new ArrayList<>();
        String answer = bean.getAnswer();

        if (Global.TRUE_STRING.equals(bean.getIs_mandatory()) || bean.isRelevantMandatory()) {
            if (answer == null || "".equals(answer)) {
                errMessage.add(bean.getQuestion_label() + " " + msgRequired);
            } else if (!Global.TRUE_STRING.equals(bean.isValid())) {
                errMessage.add(bean.getQuestion_label() + " " + context.getString(R.string.input_not_valid));
            }
        }
        return errMessage;
    }

    private List<String> validateCheckReferantorQuestion(QuestionBean bean) {
        List<String> errMessage = new ArrayList<>();
        String answer = bean.getAnswer();

        if (Global.TRUE_STRING.equals(bean.getIs_mandatory())) {
            if (null == answer || "".equals(answer)) {
                errMessage.add(bean.getQuestion_label() + " " + msgRequired);
            }
        }
        return errMessage;
    }

    private List<String> validateGetOTRQuestion(QuestionBean bean) {
        List<String> errMessage = new ArrayList<>();
        String answer = bean.getAnswer();

        if (Global.TRUE_STRING.equals(bean.getIs_mandatory()) || bean.isRelevantMandatory()) {
            if (answer == null || "".equals(answer)) {
                errMessage.add(bean.getQuestion_label() + " " + msgRequired);
            } else if (errMessage.isEmpty() && bean.getQuestion_validation() != null && !bean.getQuestion_validation().isEmpty()) {
                String script = bean.getQuestion_validation();
                String answerType = bean.getAnswer_type();
                if (!validateByScript(answer, answerType, script)) {
                    if (bean.getValidate_err_message() != null && !bean.getValidate_err_message().isEmpty()) {
                        errMessage.add(bean.getQuestion_label() + " " + bean.getValidate_err_message());
                    } else {
                        errMessage.add(bean.getQuestion_label() + ": " + context.getString(R.string.input_not_valid));
                    }
                }
            }
        }
        return errMessage;
    }

    private List<String> validateSubmitLayerQuestion(QuestionBean bean) {
        List<String> errMessage = new ArrayList<>();
        String answer = bean.getAnswer();
        if ((Global.TRUE_STRING.equals(bean.getIs_mandatory()) || bean.isRelevantMandatory()) &&
                (answer == null || "".equals(answer))) {
            errMessage.add(bean.getQuestion_label() + " " + msgRequired);
        }

        if (StringUtils.isNotBlank(bean.getQuestion_validation())) {
            String validateScript = bean.getQuestion_validation();
            if (bean.getQuestion_validation().toUpperCase().contains("$MAXRETRY()")) {
                int validasiRetry = bean.getMaxRetry() - bean.getCountRetry();
                if (validasiRetry <= 0) {
                    validateScript = validateScript.replace("$MAXRETRY()", "true");
                } else {
                    validateScript = validateScript.replace("$MAXRETRY()", "false");
                }
            }
            if (!validateByScript(answer, bean.getAnswer_type(), validateScript)) {
                if (bean.getValidate_err_message() != null && !bean.getValidate_err_message().isEmpty()) {
                    errMessage.add(bean.getQuestion_label() + " " + bean.getValidate_err_message());
                } else {
                    errMessage.add(bean.getQuestion_label() + ": " + context.getString(R.string.input_not_valid));
                }
            }
        }
        return errMessage;
    }

    private List<String> validateGetDSRQuestion(QuestionBean bean) {
        List<String> errMessage = new ArrayList<>();
        String answer = bean.getAnswer();
        String[] answerDsr = answer.split(Global.DELIMETER_DATA4);
        if (Global.TRUE_STRING.equals(bean.getIs_mandatory()) || bean.isRelevantMandatory()) {
            if (answer == null || "".equals(answer)) {
                errMessage.add(bean.getQuestion_label() + " " + msgRequired);
            } else {
                if (answer.contains(Global.DELIMETER_DATA4) && StringUtils.isBlank(answerDsr[0]) /*|| Double.parseDouble(answerDsr[0]) == 0*/) {
                    errMessage.add(bean.getQuestion_label() + " " + msgRequired);
                }
            }
        }

        if (StringUtils.isNotBlank(bean.getQuestion_validation())) {
            String validateScript = bean.getQuestion_validation();
            if (bean.getQuestion_validation().toUpperCase().contains("$MAXRETRY()")) {
                int validateRetry = bean.getMaxRetry() - bean.getCountRetry();
                if (validateRetry <= 0) {
                    validateScript = validateScript.replace("$MAXRETRY()", "true");
                } else {
                    validateScript = validateScript.replace("$MAXRETRY()", "false");
                }
            }

            String result = "";
            if (StringUtils.isNotBlank(answer) && answer.contains(Global.DELIMETER_DATA4)) {
                result = answer.split(Global.DELIMETER_DATA4)[1];
                String valueDsr = answer.split(Global.DELIMETER_DATA4)[0];
                if (valueDsr.equals("0")) {
                    //errMessage.add(bean.getQuestion_label() + " must be have value.");
                } else if (result.equalsIgnoreCase(context.getString(R.string.message_online_mode_dsr_failed))) {
                    result = context.getString(R.string.message_online_mode_dsr_failed);
                    if (valueDsr.equals("0")) {
                        result = "FAILED";
                    } else {
                        result = "SUCCESS";
                    }
                } else {
                    result = "SUCCESS";
                }
                if (result.equalsIgnoreCase(context.getString(R.string.message_online_mode_dsr_success))) {
                    result = "SUCCESS";
                    // Validasi untuk parameter yang berubah: Angsuran, Gaji, Gaji Pasangan, Installment WOM, Installment Other
                    if (validateDSRHistoryParameter(bean, Global.REF_PRE_ANGSURAN, 0)
                            || validateDSRHistoryParameter(bean, Global.REF_PRE_PENGHASILAN, 1)
                            || validateDSRHistoryParameter(bean, Global.REF_SVY_PGHSL_PSGN, 2)
                            || validateDSRHistoryParameter(bean, Global.REF_INSTALLMENT_WOM, 3)
                            || validateDSRHistoryParameter(bean, Global.REF_INSTALLMENT_OTHER, 4)) {
                        errMessage.add(bean.getQuestion_label() + "" + bean.getValidate_err_message());
                    }
                }
            }
            if (!validateByScript(result, bean.getAnswer_type(), validateScript)) {
                if (bean.getValidate_err_message() != null && !bean.getValidate_err_message().isEmpty()) {
                    errMessage.add(bean.getQuestion_label() + " " + bean.getValidate_err_message());
                } else {
                    errMessage.add(bean.getQuestion_label() + ": " + context.getString(R.string.input_not_valid));
                }
            } else {
                // Validasi untuk parameter yang berubah: Angsuran, Gaji, Gaji Pasangan, Installment WOM, Installment Other
                if (validateDSRHistoryParameter(bean, Global.REF_PRE_ANGSURAN, 0)
                        || validateDSRHistoryParameter(bean, Global.REF_PRE_PENGHASILAN, 1)
                        || validateDSRHistoryParameter(bean, Global.REF_SVY_PGHSL_PSGN, 2)
                        || validateDSRHistoryParameter(bean, Global.REF_INSTALLMENT_WOM, 3)
                        || validateDSRHistoryParameter(bean, Global.REF_INSTALLMENT_OTHER, 4)) {
                    errMessage.add(bean.getQuestion_label() + "" + bean.getValidate_err_message());
                }
            }
        }
        return errMessage;
    }

    private boolean validateDSRHistoryParameter(QuestionBean bean, String refId, int index) {
        QuestionBean qbBean = Constant.listOfQuestion.get(refId);
        String parameterBefore = bean.getHistoryParamsCalculateDsr().split(Global.DELIMETER_DATA4)[index];
        String parameterNow;
        if (qbBean.getAnswer() == null) {
            parameterNow = "0";
        } else {
            parameterNow = qbBean.getAnswer().replace(",", "");
        }
        if (!parameterBefore.equals(parameterNow)) {
            if (refId.equals(Global.REF_INSTALLMENT_WOM) || refId.equals(Global.REF_INSTALLMENT_OTHER)) {
                QuestionBean qbDsr = Constant.listOfQuestion.get(Global.REF_PRE_DSR);
                qbDsr.setAnswer("0");
                qbDsr.setCountRetry(1);
                FragmentQuestion.edtValueDsr.setText(qbDsr.getAnswer());
                FragmentQuestion.txtDsrOffline.setText("");
                FragmentQuestion.buttonGetDsr.setEnabled(true);
            }
            return true;
        }
        return false;
    }

    private List<String> validateTextMultilineSeparate(QuestionBean bean) {
        List<String> errMessage = new ArrayList<>();
        return errMessage;
    }

    public boolean validateByScript(String answer, String answerType, String script) {
        boolean isValid = false;
        String format = null;
        String convertedExpression = script;        //make a copy of
        if (convertedExpression == null || convertedExpression.length() == 0) {
            return true;
        } else {
            boolean needReplacing = true;
            while (needReplacing) {
                int idxOfOpenBrace = convertedExpression.indexOf('{');
                if (idxOfOpenBrace != -1) {
                    //there's {, prepare to replace what inside the {}
                    int idxOfCloseBrace = convertedExpression.indexOf('}');
                    String identifier = convertedExpression.substring(idxOfOpenBrace + 1, idxOfCloseBrace);
                    int idxOfOpenAbs = identifier.indexOf("$");
                    if (idxOfOpenAbs != -1) {
                        String finalIdentifier = identifier.substring(idxOfOpenAbs + 1);
                        String flatAnswer = "";
                        if (finalIdentifier.equals(Global.IDF_LOGIN_ID)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getLogin_id();
                            int idxOfOpenAt = flatAnswer.indexOf('@');
                            if (idxOfOpenAt != -1) {
                                flatAnswer = flatAnswer.substring(0, idxOfOpenAt);
                            }
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_ID)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_id();
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_NAME)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_name();
                        } else if (finalIdentifier.equals(Global.IDF_UUID_USER)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                        } else if (finalIdentifier.equals(Global.IDF_JOB)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getFlag_job();
                        } else if (finalIdentifier.equals(Global.IDF_DEALER_NAME)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getDealer_name();
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_TYPE)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_type();
                        } else if (finalIdentifier.equals(Global.IDF_TASK_ID)) {
                            String taskId = DynamicFormActivity.header.getTask_id();
                            flatAnswer = taskId;
                        } else if (finalIdentifier.equals(Global.IDF_ANSWER_BEAN)) {
                            try {
                                if (answerType.equals(Global.AT_TIME)) {
                                    format = Global.TIME_STR_FORMAT;
                                    String formatDate = Global.TIME_STR_FORMAT2;
                                    Date date2 = null;
                                    try {
                                        date2 = Formatter.parseDate(answer, formatDate);
                                    } catch (Exception e) {
                                        FireCrash.log(e);
                                        date2 = Formatter.parseDate(answer, format);
                                    }
                                    Calendar now = Calendar.getInstance(TimeZone
                                            .getDefault());
                                    Calendar date = Calendar.getInstance(TimeZone
                                            .getDefault());
                                    date.setTime(date2);
                                    date.set(Calendar.YEAR, now.get(Calendar.YEAR));
                                    date.set(Calendar.MONTH, now.get(Calendar.MONTH));
                                    date.set(Calendar.DAY_OF_MONTH, now.get(Calendar.DAY_OF_MONTH));
                                    flatAnswer = Formatter.formatDate(date.getTime(), format);
                                } else if (answerType.equals(Global.AT_DATE_TIME)) {
                                    format = Global.DATE_TIME_STR_FORMAT;
                                    String formatDate = Global.DATE_STR_FORMAT_GSON;
                                    Date date2 = null;
                                    try {
                                        date2 = Formatter.parseDate(answer, formatDate);
                                    } catch (Exception e) {
                                        FireCrash.log(e);
                                        date2 = Formatter.parseDate(answer, format);
                                    }
                                    Calendar date = Calendar.getInstance(TimeZone
                                            .getDefault());
                                    date.setTime(date2);
                                    flatAnswer = Formatter.formatDate(date.getTime(), format);
                                } else if (answerType.equals(Global.AT_DATE)) {
                                    format = Global.DATE_STR_FORMAT;
                                    String formatDate = Global.DATE_STR_FORMAT_GSON;
                                    Date date2 = null;
                                    try {
                                        date2 = Formatter.parseDate(answer, formatDate);
                                    } catch (Exception e) {
                                        FireCrash.log(e);
                                        date2 = Formatter.parseDate(answer, format);
                                    }
                                    Calendar date = Calendar.getInstance(TimeZone
                                            .getDefault());
                                    date.setTime(date2);
                                    flatAnswer = Formatter.formatDate(date.getTime(), format);
                                } else {
                                    flatAnswer = answer;
                                }
                            } catch (Exception e) {
                                if (Global.IS_DEV)
                                    e.printStackTrace();
                            }
                        } else if (finalIdentifier.equals(Global.IDF_THIS_YEAR)) {
                            Calendar cal = Calendar.getInstance(TimeZone.getDefault());
                            flatAnswer = String.valueOf(cal.get(Calendar.YEAR));
                        } else if (finalIdentifier.equals(Global.IDF_NOWADAYS)) {
                            Calendar cal = Calendar.getInstance(TimeZone.getDefault());
                            try {
                                if (answerType.equals(Global.AT_TIME)) {
                                    format = Global.TIME_STR_FORMAT;
                                    flatAnswer = Formatter.formatDate(cal.getTime(), format);
                                } else if (answerType.equals(Global.AT_DATE_TIME)) {
                                    format = Global.DATE_TIME_STR_FORMAT;
                                    flatAnswer = Formatter.formatDate(cal.getTime(), format);
                                } else if (answerType.equals(Global.AT_DATE)) {
                                    format = Global.DATE_STR_FORMAT;
                                    flatAnswer = Formatter.formatDate(cal.getTime(), format);
                                } else {
                                    format = Global.DATE_TIME_STR_FORMAT;
                                    flatAnswer = Formatter.formatDate(cal.getTime(), format);
                                }
                            } catch (Exception e) {
                                if (Global.IS_DEV)
                                    e.printStackTrace();
                            }
                        } else if (finalIdentifier.equals(Global.IDF_YESTERDAY)) {
                            Calendar cal = Calendar.getInstance(TimeZone.getDefault());
                            cal.add(Calendar.DATE, -1);
                            try {
                                if (answerType.equals(Global.AT_TIME)) {
                                    format = Global.TIME_STR_FORMAT;
                                    flatAnswer = Formatter.formatDate(cal.getTime(), format);
                                } else if (answerType.equals(Global.AT_DATE_TIME)) {
                                    format = Global.DATE_TIME_STR_FORMAT;
                                    flatAnswer = Formatter.formatDate(cal.getTime(), format);
                                } else if (answerType.equals(Global.AT_DATE)) {
                                    format = Global.DATE_STR_FORMAT;
                                    flatAnswer = Formatter.formatDate(cal.getTime(), format);
                                } else {
                                    format = Global.DATE_TIME_STR_FORMAT;
                                    flatAnswer = Formatter.formatDate(cal.getTime(), format);
                                }
                            } catch (Exception e) {
                                if (Global.IS_DEV)
                                    e.printStackTrace();
                            }
                        }

                        if (flatAnswer != null && flatAnswer.length() > 0) {
                            convertedExpression = convertedExpression.replace("{" + identifier + "}", flatAnswer);
                        } else {
                            //if there's no answer, just hide the question
                            return false;
                        }

                    } else {
                        QuestionBean bean = Constant.listOfQuestion.get(identifier);
                        if (bean != null) {
//                            if (bean.getIs_visible().equals(Global.FALSE_STRING)) return false;
                            String flatAnswer = QuestionBean.getAnswer(bean);
                            if (flatAnswer != null && flatAnswer.length() > 0) {
                                //NOTE: though it's possible to just iterate on flatAnswer substrings, we prefer to stay on method if size is 1
                                String answers[] = Tool.split(flatAnswer, Global.DELIMETER_DATA);
                                if (answers.length == 1) {
                                    try {
                                        if (bean.getAnswer_type().equals(Global.AT_TIME)) {
                                            format = Global.TIME_STR_FORMAT;
                                            String formatDate = Global.TIME_STR_FORMAT2;
                                            Date date2 = null;
                                            try {
                                                date2 = Formatter.parseDate(answers[0], formatDate);
                                            } catch (Exception e) {
                                                FireCrash.log(e);
                                                date2 = Formatter.parseDate(answers[0], format);
                                            }
                                            Calendar now = Calendar.getInstance(TimeZone
                                                    .getDefault());
                                            Calendar date = Calendar.getInstance(TimeZone
                                                    .getDefault());
                                            date.setTime(date2);
                                            date.set(Calendar.YEAR, now.get(Calendar.YEAR));
                                            date.set(Calendar.MONTH, now.get(Calendar.MONTH));
                                            date.set(Calendar.DAY_OF_MONTH, now.get(Calendar.DAY_OF_MONTH));
                                            flatAnswer = Formatter.formatDate(date.getTime(), format);
                                        } else if (bean.getAnswer_type().equals(Global.AT_DATE_TIME)) {
                                            format = Global.DATE_TIME_STR_FORMAT;
                                            String formatDate = Global.DATE_STR_FORMAT_GSON;
                                            Date date2 = null;
                                            try {
                                                date2 = Formatter.parseDate(answers[0], formatDate);
                                            } catch (Exception e) {
                                                FireCrash.log(e);
                                                date2 = Formatter.parseDate(answers[0], format);
                                            }
                                            Calendar date = Calendar.getInstance(TimeZone
                                                    .getDefault());
                                            date.setTime(date2);
                                            flatAnswer = Formatter.formatDate(date.getTime(), format);
                                        } else if (bean.getAnswer_type().equals(Global.AT_DATE)) {
                                            format = Global.DATE_STR_FORMAT;
                                            String formatDate = Global.DATE_STR_FORMAT_GSON;
                                            Date date2 = null;
                                            try {
                                                date2 = Formatter.parseDate(answers[0], formatDate);
                                            } catch (Exception e) {
                                                FireCrash.log(e);
                                                date2 = Formatter.parseDate(answers[0], format);
                                            }
                                            Calendar date = Calendar.getInstance(TimeZone
                                                    .getDefault());
                                            date.setTime(date2);
                                            flatAnswer = Formatter.formatDate(date.getTime(), format);
                                        } else {
                                            flatAnswer = answers[0];
                                        }
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                    convertedExpression = convertedExpression.replace("{" + identifier + "}", flatAnswer);
                                } else {
                                    //NOTE: going into in-depth loop, won't go outside of this 'else'
                                    for (int i = 0; i < answers.length; i++) {
                                        String convertedSubExpression = convertedExpression.replace("{" + identifier + "}", answers[i]);
                                        boolean isVisible = validateByScript(answer, answerType, convertedSubExpression);
                                        if (isVisible) {
                                            return true;
                                        }
                                    }
                                    return false;
                                }
                            } else {
                                flatAnswer = "0";
                                convertedExpression = convertedExpression.replace("{" + identifier + "}", flatAnswer);
                            }
                        } else {
                            convertedExpression = convertedExpression.replace("{" + identifier + "}", "\"\"");
                        }
                    }
                } else {
                    needReplacing = false;
                }
            }
            try {
                OperatorSet opSet = OperatorSet.getStandardOperatorSet();
                opSet.addOperator("!=", NotEqualSymbol.class);
                opSet.addOperator("if", IfElseFunction.class);
                Expression exp = new Expression(convertedExpression.toUpperCase());
                exp.setOperatorSet(opSet);
                if (answerType.equals(Global.AT_DATE_TIME)) {
                    exp.setResolver(new DateTimeResolver());
                } else if (answerType.equals(Global.AT_DATE)) {
                    exp.setResolver(new DateResolver());
                } else if (answerType.equals(Global.AT_TIME)) {
                    exp.setResolver(new TimeResolver());
                }
                isValid = exp.evaluate().toBoolean();
                return isValid;
            } catch (Exception e) {
                FireCrash.log(e);
                if (Global.IS_DEV)
                    e.printStackTrace();
                return false;
            }
        }
    }

public boolean validateByRule(QuestionBean bean) {
        RuleLogic logic = new GenericRuleJexlLogic();
		Result result   = new Result();

		Map<String, Object> dataObjects = new HashMap<>();
		dataObjects.put("AssetCondition", "NEW");
		dataObjects.put("ManufacturingYear", 2015);
		dataObjects.put("Tenor", 32);
		dataObjects.put("AssetCategory.AssetCategoryCode", "3MBL");
		dataObjects.put("res", result);
		File file   = new File(Environment.getExternalStorageDirectory() + "/" + "assetDP.xlsx");
		logic.executeRule(file, dataObjects);

		int x = result.getMinDpPrcntg();
		int y = result.getDevMin();
		double r = Double.parseDouble(bean.getAnswer());
        return (r > (x-y));
    }

    public static class Result {
        private int MinDpPrcntg;
        private int DevMin;

        public int getMinDpPrcntg() {
            return MinDpPrcntg;
        }

        public void setMinDpPrcntg(int minDpPrcntg) {
            MinDpPrcntg = minDpPrcntg;
        }

        public int getDevMin() {
            return DevMin;
        }

        public void setDevMin(int devMin) {
            DevMin = devMin;
        }
    }

    private List<String> validateCheckQuestion(QuestionBean bean) {
        List<String> errMessage = new ArrayList<>();
        String answer = bean.getAnswer();
        if ((Global.TRUE_STRING.equals(bean.getIs_mandatory()) || bean.isRelevantMandatory()) &&
                (null == answer || "".equals(answer))) {
            errMessage.add(bean.getQuestion_label() + " " + msgRequired);
        }

        int countMandatoryQuestion = 0;
        if (null != bean.getResponseValidationCheck()) {
            HashMap<String, Object> result = bean.getResponseValidationCheck().getListQuestionsIsMandatory();
            for (Map.Entry<String, Object> data : result.entrySet()) {
                String refId = data.getKey();
                String isMandatory = data.getValue().toString();

                QuestionBean beans = Constant.listOfQuestion.get(refId);
                if (null != beans) {
                    String refIdQuestion = beans.getIdentifier_name();
                    if (refId.equalsIgnoreCase(refIdQuestion)) {
                        if ("1".equals(isMandatory)) {
                            countMandatoryQuestion += 1;
                        }
                    }
                }
            }
        }

        if (countMandatoryQuestion > 0) {
            if (bean.getCountRetry() < bean.getMaxRetry()) {
                MssResponseType.Status status = bean.getResponseValidationCheck().getStatus();
                errMessage.add(status.getMessage());
            }
        }

        if (StringUtils.isNotBlank(bean.getQuestion_validation())) {
            String validateScript = bean.getQuestion_validation();
            if (bean.getQuestion_validation().toUpperCase().contains("$MAXRETRY()")) {
                int validationRetry = bean.getMaxRetry() - bean.getCountRetry();
                if (validationRetry <= 0) {
                    validateScript = validateScript.replace("$MAXRETRY()", "true");
                } else {
                    validateScript = validateScript.replace("$MAXRETRY()", "false");
                }
            }
            if (!validateByScript(answer, bean.getAnswer_type(), validateScript)) {
                if (null != bean.getValidate_err_message() && !bean.getValidate_err_message().isEmpty()) {
                    errMessage.add(bean.getQuestion_label() + " " + bean.getValidate_err_message());
                } else {
                    errMessage.add(bean.getQuestion_label() + " : " + context.getString(R.string.input_not_valid));
                }
            }
        }
        return errMessage;
    }

}
