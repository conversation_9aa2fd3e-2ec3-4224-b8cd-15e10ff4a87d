package com.adins.mss.base.dynamicform.form.models;

import com.adins.mss.foundation.http.KeyValue;
import com.adins.mss.foundation.http.MssResponseType;
import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * Created by gigin.ginanjar on 12/10/2016.
 */

public class ReviewResponse extends MssResponseType {
    @SerializedName("errorMessage")
    private String errMessage;
    @SerializedName("listField")
    private List<KeyValue> listField;

    public String getErrMessage() {
        return errMessage;
    }

    public void setErrMessage(String errMessage) {
        this.errMessage = errMessage;
    }

    public List<KeyValue> getListField() {
        return listField;
    }

    public void setListField(List<KeyValue> listField) {
        this.listField = listField;
    }
}
