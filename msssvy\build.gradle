plugins {
    id "de.undercouch.download" version "5.0.0"
}

apply plugin: 'com.android.application'
apply plugin: 'com.google.firebase.crashlytics'

def app_version = "2.6.2.27"
def app_version_code = 30625
app_version_code = app_version_code + getCountCommitFromGit()

def app_revision_code = getRevisionFromGit()
def app_build = 28

if (null != project.findProperty('app.version')) {
    app_version = project.findProperty('app.version')
}

android {
    lintOptions {
        disable 'MissingTranslation'
        checkReleaseBuilds false
    }

    signingConfigs {
        msmkey {
            keyAlias 'msmkeyalias'
            keyPassword 'AdIns2012'
            storeFile file('msmkey')
            storePassword 'AdIns2012'
        }
    }

    compileSdkVersion app_build
    defaultConfig {
        applicationId "com.adins.wom.svy"
        minSdkVersion 16
        targetSdkVersion app_build
        versionCode app_version_code
        versionNameSuffix app_revision_code
        versionName "${app_version}-$versionNameSuffix"
        multiDexEnabled true
        useLibrary 'org.apache.http.legacy'
        archivesBaseName = "MSSSVY-$versionName"
    }
    dexOptions {
        javaMaxHeapSize "5g"
    }
    buildTypes {
        release {
            shrinkResources false
            debuggable false
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.msmkey
        }
    }
    flavorDimensions "default"

    productFlavors {
        developer {
            applicationId "com.adins.wom.svy.dev"
            dimension "default"
            versionCode app_version_code
            versionNameSuffix app_revision_code
            versionName "${app_version}-$versionNameSuffix-D"
            buildConfigField "boolean", "IS_DEV", "true"
            buildConfigField "boolean", "IS_DBENCRYPT", "false"
            buildConfigField "boolean", "IS_BYPASSROOT", "true"
            buildConfigField "String", "IS_FLAVORS", "\"DEVELOPER\""
        }
        product {
            applicationId "com.adins.wom.svy"
            dimension "default"
            versionCode app_version_code
            versionNameSuffix app_revision_code
            versionName "${app_version}-$versionNameSuffix-P"
            buildConfigField "boolean", "IS_DEV", "false"
            buildConfigField "boolean", "IS_DBENCRYPT", "false"
            buildConfigField "boolean", "IS_BYPASSROOT", "false"
            buildConfigField "String", "IS_FLAVORS", "\"PRODUCT\""
        }
        productcloud {
            applicationId "com.adins.wom.svy.cloud"
            dimension "default"
            versionCode app_version_code
            versionNameSuffix app_revision_code
            versionName "${app_version}-$versionNameSuffix-PC"
            buildConfigField "boolean", "IS_DEV", "false"
            buildConfigField "boolean", "IS_DBENCRYPT", "false"
            buildConfigField "boolean", "IS_BYPASSROOT", "false"
            buildConfigField "String", "IS_FLAVORS", "\"PRODUCTCLOUD\""
        }
        productNonPilot {
            applicationId "com.adins.wom.svy"
            dimension "default"
            versionCode app_version_code
            versionNameSuffix app_revision_code
            versionName "${app_version}-$versionNameSuffix-PIP"
            buildConfigField "boolean", "IS_DEV", "false"
            buildConfigField "boolean", "IS_DBENCRYPT", "false"
            buildConfigField "boolean", "IS_BYPASSROOT", "false"
            buildConfigField "String", "IS_FLAVORS", "\"PRODUCT\""
        }
        trial {
            applicationId "com.adins.wom.svy.trial"
            dimension "default"
            versionCode app_version_code
            versionNameSuffix app_revision_code
            versionName "${app_version}-$versionNameSuffix-U"
            buildConfigField "boolean", "IS_DEV", "false"
            buildConfigField "boolean", "IS_DBENCRYPT", "false"
            buildConfigField "boolean", "IS_BYPASSROOT", "false"
            buildConfigField "String", "IS_FLAVORS", "\"TRIAL\""
        }

        trial2 {
            applicationId "com.adins.wom.svy.trial2"
            dimension "default"
            versionCode app_version_code
            versionNameSuffix app_revision_code
            versionName "${app_version}-$versionNameSuffix-U2"
            buildConfigField "boolean", "IS_DEV", "false"
            buildConfigField "boolean", "IS_DBENCRYPT", "false"
            buildConfigField "boolean", "IS_BYPASSROOT", "false"
            buildConfigField "String", "IS_FLAVORS", "\"TRIAL\""
        }

        trialCloud2 {
            applicationId "com.adins.wom.svy.trial.cloud2"
            dimension "default"
            versionCode app_version_code
            versionNameSuffix app_revision_code
            versionName "${app_version}-$versionNameSuffix-U-Cloud-2"
            buildConfigField "boolean", "IS_DEV", "false"
            buildConfigField "boolean", "IS_DBENCRYPT", "false"
            buildConfigField "boolean", "IS_BYPASSROOT", "false"
            buildConfigField "String", "IS_FLAVORS", "\"TRIAL\""
        }

        train {
            applicationId "com.adins.wom.svy.train"
            dimension "default"
            versionCode app_version_code
            versionNameSuffix app_revision_code
            versionName "${app_version}-$versionNameSuffix-PIP"
            buildConfigField "boolean", "IS_DEV", "false"
            buildConfigField "boolean", "IS_DBENCRYPT", "false"
            buildConfigField "boolean", "IS_BYPASSROOT", "false"
            buildConfigField "String", "IS_FLAVORS", "\"TRAIN\""
        }
        drc {
            applicationId "com.adins.wom.svy.drc"
            dimension "default"
            versionCode app_version_code
            versionNameSuffix app_revision_code
            versionName "${app_version}-$versionNameSuffix-DRC"
            buildConfigField "boolean", "IS_DEV", "false"
            buildConfigField "boolean", "IS_DBENCRYPT", "false"
            buildConfigField "boolean", "IS_BYPASSROOT", "false"
            buildConfigField "String", "IS_FLAVORS", "\"DRC\""
        }

        uat3 {
            applicationId "com.adins.wom.svy.uat3"
            dimension "default"
            versionCode app_version_code
            versionNameSuffix app_revision_code
            versionName "${app_version}-$versionNameSuffix-UAT3"
            buildConfigField "boolean", "IS_DEV", "false"
            buildConfigField "boolean", "IS_DBENCRYPT", "false"
            buildConfigField "boolean", "IS_BYPASSROOT", "false"
            buildConfigField "String", "IS_FLAVORS", "\"TRIAL\""
        }

        uat1 {
            applicationId "com.adins.wom.svy.uat1"
            dimension "default"
            versionCode app_version_code
            versionNameSuffix app_revision_code
            versionName "${app_version}-$versionNameSuffix-UAT1"
            buildConfigField "boolean", "IS_DEV", "false"
            buildConfigField "boolean", "IS_DBENCRYPT", "false"
            buildConfigField "boolean", "IS_BYPASSROOT", "false"
            buildConfigField "String", "IS_FLAVORS", "\"TRIAL\""
        }

        dev1new {
            applicationId "com.adins.wom.svy.dev1.new"
            dimension "default"
            versionCode app_version_code
            versionNameSuffix app_revision_code
            versionName "${app_version}-$versionNameSuffix-D1N"
            buildConfigField "boolean", "IS_DEV", "false"
            buildConfigField "boolean", "IS_DBENCRYPT", "false"
            buildConfigField "boolean", "IS_BYPASSROOT", "false"
            buildConfigField "String", "IS_FLAVORS", "\"TRIAL\""
        }

        dev3new {
            applicationId "com.adins.wom.svy.dev3.new"
            dimension "default"
            versionCode app_version_code
            versionNameSuffix app_revision_code
            versionName "${app_version}-$versionNameSuffix-D3N"
            buildConfigField "boolean", "IS_DEV", "false"
            buildConfigField "boolean", "IS_DBENCRYPT", "false"
            buildConfigField "boolean", "IS_BYPASSROOT", "false"
            buildConfigField "String", "IS_FLAVORS", "\"TRIAL\""
        }
    }

    packagingOptions {
        exclude 'META-INF/LICENSE'
    }
}

dependencies {
    implementation project(':mssbase')
    implementation fileTree(dir: 'libs', include: '*.jar')
    implementation 'com.google.firebase:firebase-messaging:20.2.4'
    implementation 'com.google.firebase:firebase-core:17.5.0'
    implementation 'com.google.firebase:firebase-analytics:17.5.0'
    implementation 'com.google.firebase:firebase-crashlytics:17.2.1'
    implementation 'com.google.firebase:firebase-config:11.8.0'
    implementation 'com.google.firebase:firebase-perf:19.0.8'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    testImplementation 'junit:junit:4.12'
}

def getRevisionFromGit() {
    def cmd = "git rev-parse --short HEAD"
    def proc = cmd.execute()
    return proc.text.trim();
}

def getCountCommitFromGit() {
    def revision_branching_head = project.findProperty('revision.code.initial.branching')
    def cmdCountRev = "git rev-list --count ${revision_branching_head}..HEAD"
    def procCountRev = cmdCountRev.execute()
    return Integer.parseInt(procCountRev.text.trim())
}

//Download embed msmdb
//afterEvaluate {
//    android.applicationVariants.all { variant ->
//        if (variant.buildType.name == 'debug') {
//            task("downloadFile${variant.name.capitalize()}") {
//                println "\n\nProcess 'downloadFile${variant.name.capitalize()}'..."
//                def flavorName = variant.flavorName
//                def url = project.findProperty("msmdb.${flavorName}")
//
//                if (url != null && url !='') {
//                    def file = new File("${projectDir}/src/${flavorName}/assets/msmdb")
//                    if (!file.exists()) {
//                        println "Download file msmdb for flavor ${flavorName}..."
//                        // Download the file using the gradle-download-task plugin
//                        download.run {
//                            src url
//                            dest file
//                            overwrite true
//                        }
//                    } else {
//                        println "File msmdb for flavor ${flavorName} already exist"
//                    }
//                } else {
//                    println "Link for flavor ${flavorName} doesn't exist. Please check gradle.properties"
//                }
//            }
//            // Ensure the download task runs before the preBuild task
//            variant.preBuildProvider.configure { preBuildTask ->
//                preBuildTask.dependsOn "downloadFile${variant.name.capitalize()}"
//            }
//        }
//    }
//}

apply plugin: 'com.google.gms.google-services'

