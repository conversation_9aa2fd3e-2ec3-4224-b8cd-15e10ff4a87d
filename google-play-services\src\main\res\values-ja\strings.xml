<?xml version="1.0" encoding="UTF-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="common_google_play_services_install_title" msgid="26645092511305524">"Play開発者サービスの入手"</string>
    <string name="common_google_play_services_install_text_phone" msgid="8685301130651051380">"このアプリの実行にはGoogle Play開発者サービスが必要ですが、お使いの携帯端末にはインストールされていません。"</string>
    <string name="common_google_play_services_install_text_tablet" msgid="1589957570365247855">"このアプリの実行にはGoogle Play開発者サービスが必要ですが、お使いのタブレットにはインストールされていません。"</string>
    <string name="common_google_play_services_install_button" msgid="8515591849428043265">"Play開発者サービスの入手"</string>
    <string name="common_google_play_services_enable_title" msgid="529078775174559253">"Play開発者サービスの有効化"</string>
    <string name="common_google_play_services_enable_text" msgid="7627896071867667758">"このアプリの実行には、Google Play開発者サービスの有効化が必要です。"</string>
    <string name="common_google_play_services_enable_button" msgid="4181637455539816337">"Play開発者サービスの有効化"</string>
    <string name="common_google_play_services_update_title" msgid="6006316683626838685">"Play開発者サービスの更新"</string>
    <string name="common_google_play_services_update_text" msgid="448354684997260580">"このアプリの実行には、Google Play開発者サービスの更新が必要です。"</string>
    <string name="common_google_play_services_network_error_title" msgid="3827284619958211114">"ネットワークエラー"</string>
    <string name="common_google_play_services_network_error_text" msgid="9038847255613537209">"Google Play開発者サービスに接続するには、データ接続が必要です。"</string>
    <string name="common_google_play_services_invalid_account_title" msgid="1066672360770936753">"無効なアカウント"</string>
    <string name="common_google_play_services_invalid_account_text" msgid="4983316348021735578">"指定したアカウントはこの端末上に存在しません。別のアカウントを選択してください。"</string>
    <string name="common_google_play_services_unknown_issue" msgid="4762332809710093730">"Google Play開発者サービスで原因不明の問題が発生しました。"</string>
    <string name="common_google_play_services_unsupported_title" msgid="6334768798839376943">"Google Play開発者サービス"</string>
    <string name="common_google_play_services_unsupported_text" msgid="3542578567569488671">"一部のアプリが使用しているGoogle Play開発者サービスは、お使いの端末ではサポートされていません。詳しくは、端末メーカーまでお問い合わせください。"</string>
    <string name="common_google_play_services_unsupported_date_text" msgid="4725396522367789365">"端末上の日付が正しくないようです。端末上の日付をご確認ください。"</string>
    <string name="common_google_play_services_update_button" msgid="8932944190611227642">"更新"</string>
    <string name="common_signin_button_text" msgid="9071884888741449141">"ログイン"</string>
    <string name="common_signin_button_text_long" msgid="2429381841831957106">"Googleでログイン"</string>

    <string name="auth_client_using_bad_version_title" msgid="2534454398764507874">"アプリはGoogle Play開発者サービスの不適切なバージョンを使用しようとしました。"</string>
    <string name="auth_client_needs_enabling_title" msgid="3983201110833868073">"アプリではGoogle Play開発者サービスを有効にする必要があります。"</string>
    <string name="auth_client_needs_installation_title" msgid="7999585836145154206">"アプリではGoogle Play開発者サービスをインストールする必要があります。"</string>
    <string name="auth_client_needs_update_title" msgid="6488605506794595966">"アプリではGoogle Play開発者サービスをアップデートする必要があります。"</string>
    <string name="auth_client_play_services_err_notification_msg" msgid="3635065018897986478">"Google Play開発者サービスのエラー"</string>
    <string name="auth_client_requested_by_msg" msgid="6304135633531965756">"<xliff:g id="APP_NAME">%1$s</xliff:g>によるリクエスト"</string>
</resources>
