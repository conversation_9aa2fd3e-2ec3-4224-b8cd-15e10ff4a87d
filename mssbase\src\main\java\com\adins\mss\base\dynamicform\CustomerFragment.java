package com.adins.mss.base.dynamicform;

import android.annotation.SuppressLint;
import android.app.ActionBar;
import android.app.Activity;
import android.app.AlertDialog;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.provider.MediaStore;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentTransaction;

import android.text.TextUtils;
import android.text.format.DateFormat;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.ToggleButton;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.PrintActivity;
import com.adins.mss.base.R;
import com.adins.mss.base.api.CheckResubmitApi;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.depositreport.DetailTaskHRequest;
import com.adins.mss.base.depositreport.DetailTaskHResponse;
import com.adins.mss.base.dynamicform.form.questions.ImageViewerActivity;
import com.adins.mss.base.mainmenu.MainMenuActivity;
import com.adins.mss.base.taskupdate.activity.TaskUpdateActivity;
import com.adins.mss.base.taskupdate.model.SubmitTaskUpdateRequest;
import com.adins.mss.base.taskupdate.model.SubmitTaskUpdateResponse;
import com.adins.mss.base.todo.form.JsonRequestScheme;
import com.adins.mss.base.todo.form.JsonResponseScheme;
import com.adins.mss.base.todolist.ToDoList;
import com.adins.mss.base.todolist.form.PriorityTabFragment;
import com.adins.mss.base.todolist.form.RescheduleFragment;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.base.util.Utility;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.PrintResult;
import com.adins.mss.dao.QuestionSet;
import com.adins.mss.dao.ReminderPo;
import com.adins.mss.dao.Scheme;
import com.adins.mss.dao.TaskD;
import com.adins.mss.dao.TaskH;
import com.adins.mss.foundation.audio.AudioRecord;
import com.adins.mss.foundation.camerainapp.CameraActivity;
import com.adins.mss.foundation.camerainapp.helper.Logger;
import com.adins.mss.foundation.db.dataaccess.GeneralParameterDataAccess;
import com.adins.mss.foundation.db.dataaccess.PrintResultDataAccess;
import com.adins.mss.foundation.db.dataaccess.QuestionSetDataAccess;
import com.adins.mss.foundation.db.dataaccess.ReminderPoDataAccess;
import com.adins.mss.foundation.db.dataaccess.SchemeDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskDDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskHDataAccess;
import com.adins.mss.foundation.dialog.DialogManager;
import com.adins.mss.foundation.dialog.NiftyDialogBuilder;
import com.adins.mss.foundation.formatter.Formatter;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.http.MssResponseType;
import com.adins.mss.foundation.image.Base64;
import com.adins.mss.foundation.image.Utils;
import com.github.jjobes.slidedatetimepicker.SlideDateTimeListener;
import com.github.jjobes.slidedatetimepicker.SlideDateTimePicker;
import com.services.JsonRequestCheckStatusTask;
import com.services.JsonResponseStatusTask;

import org.acra.ACRA;

import java.io.File;
import java.io.IOException;
import java.lang.ref.WeakReference;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

import de.greenrobot.dao.DaoException;
import zj.com.cn.bluetooth.sdk.Main_Activity1;

import static android.app.Activity.RESULT_OK;
import static com.adins.mss.base.taskupdate.activity.TaskUpdateActivity.STATUS_TASK_UPDATE;

public class CustomerFragment extends Fragment implements OnClickListener {
    public static final String SURVEY_MODE = "com.adins.mss.base.dynamicform.SURVEY_MODE";
    public static final String SURVEY_HEADER = "com.adins.mss.base.dynamicform.SURVEY_HEADER";
    public static final String SURVEY_UUID = "com.adins.mss.base.dynamicform.SURVEY_UUID";
    public static final String CUSTOMER_NAME = "com.adins.mss.base.dynamicform.CUSTOMER_NAME";
    public static final String CUSTOMER_PHONE = "com.adins.mss.base.dynamicform.CUSTOMER_PHONE";
    public static final String CUSTOMER_ADDRESS = "com.adins.mss.base.dynamicform.CUSTOMER_ADDRESS";
    public static final String CUSTOMER_ZIPCODE = "com.adins.mss.base.dynamicform.CUSTOMER_ZIPCODE";
    public static final String CUSTOMER_NOTES = "com.adins.mss.base.dynamicform.CUSTOMER_NOTES";
    public static final String CUSTOMER_PILOTING_CAE = "com.adins.mss.base.dynamicform.CUSTOMER_PILOTING_CAE";
    public static final String CUSTOMER_PRE_APPROVAL = "com.adins.mss.base.dynamicform.CUSTOMER_PRE_APPROVAL";
    public static final String SOURCE_DATA = "com.adins.mss.base.dynamicform.SOURCE_DATA";
    public static final String APPL_NO = "com.adins.mss.base.dynamicform.APPL_NO";
    public static final String ISTEXT = "com.adins.mss.base.dynamicform.ISTEXT";
    public static SurveyHeaderBean header;
    public static Boolean isEditable = false;
    public static boolean viewTask = false;
    private static CustomerFragment INSTANCE = null;
    private static Menu mainMenu;
    private QuestionSetTask task;
    public List<TaskH> listTaskH;
    public ToDoList toDoList;
    boolean isFotoTaskAvailable = false;
    double limit = 0;
    double cashOnHand = 0;
    EditText txtName;
    EditText txtPhone;
    EditText txtAddress;
    EditText txtZipCode;
    EditText txtNotes;
    EditText txtPts;
    EditText txtOSAmount;
    EditText txtOD;
    EditText txtInstallmentNo;
    EditText txtPendingNotes;
    EditText txtDocuproFeedback;
    EditText txtFeedbackNotes;
    EditText txtCategory;
    EditText txtSubCategory;
    EditText txtReasonDetail;
    EditText txtValidation;
    EditText bgUploadDocument;
    EditText txtPilotingCae;
    EditText txtNotesCrm;
    EditText txtPreApproval;
    EditText txtSourceData;
    TextView lblPts;
    TextView txtVoiceNote;
    TextView noVoiceNote;
    TextView lbName;
    TextView lbPhone;
    TextView lbAddress;
    TextView lbNote;
    TextView lbOSAmount;
    TextView lbOD;
    TextView lbInstallmentNo;
    TextView lblPendingNotes;
    TextView lblDocuproFeedback;
    TextView lblFeedbackNotes;
    TextView lblUploadDocument;
    TextView lblPilotingCae;
    TextView lblCategory;
    TextView lblSubCategory;
    TextView lblReasonDetail;
    TextView lblValidation;
    TextView lblNotesCrm;
    TextView lblPreApproval;
    TextView lblSourceData;
    ToggleButton btnVoiceNotes;
    Button btnPts;
    Button btnStart;
    Button btnReset;
    Button btnView;
    Button btnPrint;
    Button btnPromise;
    Button btnRevisit;
    Button btnSubmit;
    ImageButton btnPlay;
    ImageButton btnStop;
    ImageView btnUploadDocument;
    LinearLayout playerLayout;
    RelativeLayout rUploadDocLayout;
    Scheme lastUpdateScheme;
    TaskD taskdOSAmount;
    TaskD taskdOD;
    TaskD taskdInstallmentNo;
    private Activity activity;
    private AudioRecord record;
    private Bundle mArguments;
    private Context context;
    private CheckScheme checkScheme;
    private RefreshBackgroundTask backgroundTask;

    private byte[] byteDocument = null;
    private Bitmap bmDocument = null;

    HttpCryptedConnection httpConn;
    HttpConnectionResult serverResult = null;
    private String errMsg = "";

    private static String submitUrl = GlobalData.getSharedGlobalData().getURL_SUBMIT_TASK_UPDATE();

    public static CustomerFragment create(Bundle data) {
        CustomerFragment fragment = new CustomerFragment();
        fragment.setArguments(data);

        return fragment;
    }

    /**
     * @param header
     * <AUTHOR>
     */
    public static CustomerFragment create(SurveyHeaderBean header) {
        CustomerFragment.header = null;
        Bundle bundle = new Bundle();
        bundle.putString(CUSTOMER_NAME, header.getCustomer_name());
        bundle.putString(CUSTOMER_PHONE, header.getCustomer_phone());
        bundle.putString(CUSTOMER_ADDRESS, header.getCustomer_address());
        bundle.putString(CUSTOMER_ZIPCODE, header.getZip_code());
        bundle.putString(CUSTOMER_NOTES, header.getNotes());
        bundle.putString(CUSTOMER_PILOTING_CAE, "1".equalsIgnoreCase(header.getIs_piloting_cae())? "YA": "TIDAK");
        bundle.putString(CUSTOMER_PRE_APPROVAL, (1 == header.getIs_pre_approval())? "YA": "TIDAK");
        String status = header.getStatus();
        //CR WOMFMSS : Penambahan Pengecekan Task Foto Jika Belum DIkerjakan
        String applNo = header.getAppl_no();
        String isText = header.getScheme().getForm_id();

        //
        bundle.putString(SOURCE_DATA, header.getSource_data());

        int mode = 0;
        if (status.equals(TaskHDataAccess.STATUS_SEND_SAVEDRAFT) ||
                status.equals(TaskHDataAccess.STATUS_SEND_DOWNLOAD) ||
                status.equals(TaskHDataAccess.STATUS_SEND_PENDING) ||
                status.equals(TaskHDataAccess.STATUS_SEND_UPLOADING) ||
                status.equals(TaskHDataAccess.STATUS_TASK_APPROVAL_DOWNLOAD) ||
                status.equals(TaskHDataAccess.STATUS_TASK_VERIFICATION_DOWNLOAD))
            mode = Global.MODE_SURVEY_TASK;
        else if (status.equals(TaskHDataAccess.STATUS_SEND_SENT) ||
                status.equals(TaskHDataAccess.STATUS_SEND_REJECTED)) {
            mode = Global.MODE_VIEW_SENT_SURVEY;
        } else if (status.equals(TaskHDataAccess.STATUS_TASK_VERIFICATION) ||
                status.equals(TaskHDataAccess.STATUS_TASK_APPROVAL)) {
            mode = Global.MODE_SURVEY_TASK;
        } else if (status.equals(STATUS_TASK_UPDATE)) {
            mode = Global.MODE_TASK_UPDATE;
        } else {
            if (status == null) {
                header.setStatus(TaskHDataAccess.STATUS_SEND_INIT);
            }
            if (header.getStatus().equals(TaskHDataAccess.STATUS_SEND_INIT) &&
                    header.getPriority() != null) {
                mode = Global.MODE_SURVEY_TASK;
            } else mode = Global.MODE_NEW_SURVEY;
        }

        bundle.putInt(SURVEY_MODE, mode);
        bundle.putSerializable(SURVEY_HEADER, header);
        bundle.putString(SURVEY_UUID, header.getUuid_scheme());
        //CR WOMFMSS : Penambahan Pengecekan Task Foto Jika Belum DIkerjakan
        bundle.putString(APPL_NO, applNo);
        bundle.putString(ISTEXT, isText);

        return create(bundle);
    }

    public static CustomerFragment create(String name, String phone, String address, String zipcode, String notes, int mode,
                                          SurveyHeaderBean header) {
        CustomerFragment.header = null;
        Bundle bundle = new Bundle();

        bundle.putString(CUSTOMER_NAME, name);
        bundle.putString(CUSTOMER_PHONE, phone);
        bundle.putString(CUSTOMER_ADDRESS, address);
        bundle.putString(CUSTOMER_ZIPCODE, zipcode);
        bundle.putString(CUSTOMER_NOTES, notes);
        bundle.putString(CUSTOMER_PILOTING_CAE, "1".equalsIgnoreCase(header.getIs_piloting_cae())? "YA": "TIDAK");
        bundle.putInt(SURVEY_MODE, mode);
        bundle.putSerializable(SURVEY_HEADER, header);
        bundle.putString(SURVEY_UUID, header.getUuid_scheme());

        return create(bundle);
    }

    public static CustomerFragment create(String name, String phone, String address, String zipcode, String notes, int mode,
                                          String uuid) {
        CustomerFragment.header = null;
        Bundle bundle = new Bundle();

        bundle.putString(CUSTOMER_NAME, name);
        bundle.putString(CUSTOMER_PHONE, phone);
        bundle.putString(CUSTOMER_ADDRESS, address);
        bundle.putString(CUSTOMER_ZIPCODE, zipcode);
        bundle.putString(CUSTOMER_NOTES, notes);
        bundle.putString(CUSTOMER_PILOTING_CAE, "1".equalsIgnoreCase(header.getIs_piloting_cae())? "YA": "TIDAK");
        bundle.putInt(SURVEY_MODE, mode);
        bundle.putSerializable(SURVEY_HEADER, new SurveyHeaderBean());
        bundle.putString(SURVEY_UUID, uuid);

        return create(bundle);
    }

    public static void doBack(FragmentActivity activity) {
        try {
            if (MainMenuActivity.fragmentManager.getBackStackEntryCount() > 0) {
//                MainMenuActivity.fragmentManager.popBackStack();
                MainMenuActivity.fragmentManager.popBackStackImmediate();
            }
        } catch (Exception e) {
            FireCrash.log(e);
            e.printStackTrace();
        }

    }

    @Override
    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        //To can't screenshoot
        if (Tool.isProdFlavour(Global.FLAVORS)) {
            getActivity().getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
        }

        txtOSAmount = (EditText) view.findViewById(R.id.txtCustomerOSAmount);
        txtOD = (EditText) view.findViewById(R.id.txtCustomerOD);
        txtInstallmentNo = (EditText) view.findViewById(R.id.txtCustomerInstallemntNo);
        txtName = (EditText) view.findViewById(R.id.txtCustomerName);
        txtPhone = (EditText) view.findViewById(R.id.txtCustomerPhone);
        txtAddress = (EditText) view.findViewById(R.id.txtCustomerAddress);
        txtZipCode = (EditText) view.findViewById(R.id.txtZipCode);
        txtNotes = (EditText) view.findViewById(R.id.txtNotes);
        txtVoiceNote = (TextView) view.findViewById(R.id.txtVoiceNote);
        txtPts = (EditText) view.findViewById(R.id.txtPtsDate);
        txtPendingNotes = (EditText) view.findViewById(R.id.txtPendingNotes);
        txtDocuproFeedback = (EditText) view.findViewById(R.id.txtDocuproFeedback);
        txtFeedbackNotes = (EditText) view.findViewById(R.id.txtFeedbackNotes);
        txtCategory = (EditText) view.findViewById(R.id.txtCategory);
        txtSubCategory = (EditText) view.findViewById(R.id.txtSubCategory);
        txtReasonDetail = (EditText) view.findViewById(R.id.txtReasonDetail);
        txtValidation = (EditText) view.findViewById(R.id.txtValidation);
        bgUploadDocument = (EditText) view.findViewById(R.id.bgUploadDoc);
        txtPilotingCae = (EditText) view.findViewById(R.id.txtPilotingCae);
        txtNotesCrm = (EditText) view.findViewById(R.id.txtNotesCrm);
        txtPreApproval = view.findViewById(R.id.txtPreApproval);
        txtSourceData = view.findViewById(R.id.txtSourceData);

        lblPts = (TextView) view.findViewById(R.id.lblPtsDate);
        lbName = (TextView) view.findViewById(R.id.lblCustomerName);
        lbPhone = (TextView) view.findViewById(R.id.lblCustomerPhone);
        lbAddress = (TextView) view.findViewById(R.id.lblCustomerAddress);
        lbNote = (TextView) view.findViewById(R.id.lblCustomerNotes);
        lbOSAmount = (TextView) view.findViewById(R.id.lblCustomerOSAmount);
        lbOD = (TextView) view.findViewById(R.id.lblCustomerOD);
        lbInstallmentNo = (TextView) view.findViewById(R.id.lblCustomerInstallmentNo);
        lblPendingNotes = (TextView) view.findViewById(R.id.lblPendingNotes);
        lblDocuproFeedback = (TextView) view.findViewById(R.id.lblDocuproFeedback);
        lblFeedbackNotes = (TextView) view.findViewById(R.id.lblFeedbackNotes);
        lblUploadDocument = (TextView) view.findViewById(R.id.lblUploadDoc);
        lblPilotingCae = (TextView) view.findViewById(R.id.lblPilotingCae);
        lblCategory = (TextView) view.findViewById(R.id.lblCategory);
        lblSubCategory = (TextView) view.findViewById(R.id.lblSubCategory);
        lblReasonDetail = (TextView) view.findViewById(R.id.lblReasonDetail);
        lblValidation = (TextView) view.findViewById(R.id.lblValidation);
        lblNotesCrm = (TextView) view.findViewById(R.id.lblNotesCrm);
        lblPreApproval = view.findViewById(R.id.lblPreApproval);
        lblSourceData = view.findViewById(R.id.lblSourceData);

        playerLayout = (LinearLayout) view.findViewById(R.id.recorderLayout);
        rUploadDocLayout = (RelativeLayout) view.findViewById(R.id.layoutUploadDoc);

        //bong 16 apr 15 - button for print - move to print page
        btnPrint = (Button) view.findViewById(R.id.btnPrint);
        btnPrint.setOnClickListener(this);

        btnVoiceNotes = (ToggleButton) view.findViewById(R.id.btnVoiceNotes);
        noVoiceNote = (TextView) view.findViewById(R.id.txtNoVoiceNote);
        btnStart = (Button) view.findViewById(R.id.btnStartSurvey);
        btnReset = (Button) view.findViewById(R.id.btnReset);
        btnView = (Button) view.findViewById(R.id.btnViewTask);
        btnSubmit = (Button) view.findViewById(R.id.btnSubmitTaskUpdate);
        btnPlay = (ImageButton) view.findViewById(R.id.btnPlay);
        btnStop = (ImageButton) view.findViewById(R.id.btnStop);
        btnUploadDocument = (ImageView) view.findViewById(R.id.btnUploadDoc);
        btnPts = (Button) view.findViewById(R.id.btnPts);
        btnRevisit = (Button) view.findViewById(R.id.btnRevisit);
        btnPromise = (Button) view.findViewById(R.id.btnPtsNew);

        btnVoiceNotes.setOnClickListener(this);
        btnReset.setOnClickListener(this);
        btnStart.setOnClickListener(this);
        btnView.setOnClickListener(this);
        btnPlay.setOnClickListener(this);
        btnStop.setOnClickListener(this);
        btnUploadDocument.setOnClickListener(this);
        btnSubmit.setOnClickListener(this);
        //coba
//        btnRevisit.setOnClickListener(this);

        //coba
//		Global.FEATURE_REVISIT_COLLECTION = MenuDataAccess.isHaveReVisitMenu(context);

        //CR WOMFMSS : Penambahan Pengecekan Task Foto Jika Belum DIkerjakan
        String isText = mArguments.getString(ISTEXT);
        String applNo = mArguments.getString(APPL_NO);

        //CR WOMFMSS : Penambahan Pengecekan Task Foto Jika Belum DIkerjakan
        if ("1".equals(GlobalData.getSharedGlobalData().getUser().getIs_piloting())) {
            if (isText != null && !"".equals(isText)) {
                if (isText.contains("TEXT")) {
                    List<TaskH> taskForm = TaskHDataAccess.getTaskByAppl(getActivity(), applNo);
                    if (taskForm != null) {
                        for (int i = 0; i < taskForm.size(); i++) {
                            String uuid = taskForm.get(i).getUuid_scheme();
                            Scheme imageScheme = SchemeDataAccess.getAllImageScheme(getActivity(), uuid);
                            if (imageScheme != null) {
                                if (uuid.equals(imageScheme.getUuid_scheme())) {
                                    isFotoTaskAvailable = true;
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }

        header = (SurveyHeaderBean) mArguments.getSerializable(SURVEY_HEADER);
        String formName = header.getScheme().getScheme_description();
        //Disable Start Button when User not yet Promise to Survey
        boolean isFormVisit = Global.FORM_NAME_PROMISE_TO_VISIT.equalsIgnoreCase(formName) ||
                Global.FORM_NAME_VISIT_POLO.equalsIgnoreCase(formName);
        if (header != null && header.getPms_date() == null && mArguments.getString(CUSTOMER_NAME) != null) {
            if (isFormVisit) {
                btnPromise.setText(getString(R.string.btnPromiseVisit));
            } else {
                btnPromise.setText(getString(R.string.btnPTS));
            }

            btnPromise.setVisibility(View.VISIBLE);
            btnPromise.setEnabled(true);

            btnStart.setEnabled(false);
            btnStart.setVisibility(View.VISIBLE);
        } else if (header == null || header.getCustomer_name() == null || mArguments.getString(CUSTOMER_NAME) == null) {
            btnPromise.setVisibility(View.GONE);

            btnStart.setVisibility(View.VISIBLE);
            btnStart.setEnabled(true);
        } else {
            if (isFormVisit) {
                btnPromise.setText(getString(R.string.btnPromiseVisit));
            } else {
                btnPromise.setText(getString(R.string.btnPTS));
            }

            btnPromise.setVisibility(View.VISIBLE);
            btnPromise.setEnabled(false);
            btnStart.setVisibility(View.VISIBLE);
            btnStart.setEnabled(true);
        }

        //TODO: UNCOMMENT THIS
//        if (header.getPms_date() != null) {
//            lblPts.setVisibility(View.VISIBLE);
//            txtPts.setVisibility(View.VISIBLE);
//            txtPts.setText(Formatter.formatDate(header.getPms_date(), Global.DATE_TIME_STR_FORMAT));
//        }

        if (header.getStatus().equals(TaskHDataAccess.STATUS_SEND_UPLOADING) || header.getStatus().equals(TaskHDataAccess.STATUS_SEND_SENT)
                || header.getStatus().equals(TaskHDataAccess.STATUS_SEND_SAVEDRAFT)) {
            btnPromise.setVisibility(View.GONE);
            if (!btnStart.isEnabled()) btnStart.setEnabled(true);
        }

        if(header.getTask_id() != null){
            CheckStatusTask checkStatusTask = new CheckStatusTask(getActivity(), header.getUuid_task_h());
            checkStatusTask.execute();
        }


        btnPromise.setOnClickListener(this);


        if (mArguments.getInt(SURVEY_MODE) == Global.MODE_SURVEY_TASK ||
                mArguments.getInt(SURVEY_MODE) == Global.MODE_VIEW_SENT_SURVEY) {

            if (!(TaskHDataAccess.STATUS_SEND_INIT.equals(header.getIs_prepocessed()) &&
                    TaskHDataAccess.STATUS_SEND_SAVEDRAFT.equals(header.getStatus()))) {
                txtName.setEnabled(false);
                txtPhone.setEnabled(false);
                txtAddress.setEnabled(false);
                txtZipCode.setEnabled(false);
                txtNotes.setEnabled(false);
                txtOSAmount.setEnabled(false);
                txtOD.setEnabled(false);
                txtInstallmentNo.setEnabled(false);
                txtPilotingCae.setEnabled(false);
                txtPreApproval.setEnabled(false);
                btnVoiceNotes.setEnabled(false);
                btnVoiceNotes.setClickable(false);
                btnReset.setClickable(false);
                btnReset.setVisibility(View.GONE);
                if (header.getVoice_note() != null) {
                    btnVoiceNotes.setVisibility(View.GONE);
                    playerLayout.setVisibility(View.VISIBLE);
                } else {
                    btnVoiceNotes.setVisibility(View.GONE);
                    noVoiceNote.setVisibility(View.VISIBLE);
                }
            }

            if (TaskHDataAccess.STATUS_SEND_SAVEDRAFT.equals(header.getStatus()) ||
                    TaskHDataAccess.STATUS_SEND_DOWNLOAD.equals(header.getStatus()) ||
                    TaskHDataAccess.STATUS_SEND_INIT.equals(header.getStatus())) {
                if (header.getVoice_note() != null) {
//	            	btnVoiceNotes.setVisibility(View.VISIBLE);
                    btnVoiceNotes.setVisibility(View.GONE);
                    playerLayout.setVisibility(View.VISIBLE);
                } else {
                    noVoiceNote.setVisibility(View.VISIBLE);
//	            	btnVoiceNotes.setVisibility(View.VISIBLE);
                    btnVoiceNotes.setVisibility(View.GONE);
                    playerLayout.setVisibility(View.GONE);
                }
                Boolean isEnableRevisit = Global.FEATURE_REVISIT_COLLECTION;
                String application = GlobalData.getSharedGlobalData().getAuditData().getApplication();
                if (Global.APPLICATION_SURVEY.equalsIgnoreCase(application) &&
                        header.getPriority() != null) {
                    noVoiceNote.setVisibility(View.GONE);
                    txtVoiceNote.setVisibility(View.GONE);
                    if (Global.NEW_FEATURE) {
                        if (isEnableRevisit) {
                            btnPts.setVisibility(View.VISIBLE);
                            btnPts.setOnClickListener(this);
                            if (header.getPts_date() != null) {
                                String ptsDate = Formatter.formatDate(header.getPts_date(), Global.DATE_TIME_STR_FORMAT);
                                lblPts.setVisibility(View.VISIBLE);
                                txtPts.setVisibility(View.VISIBLE);
                                txtPts.setText(ptsDate);
                            }
                        } else {
                            btnPts.setVisibility(View.GONE);
                        }
                    }
                }
                if (Global.APPLICATION_COLLECTION.equalsIgnoreCase(application) &&
                        header.getPriority() != null) {
                    noVoiceNote.setVisibility(View.GONE);
                    txtVoiceNote.setVisibility(View.GONE);
                }
            } else if (TaskHDataAccess.STATUS_SEND_SENT.equals(header.getStatus())) {
                if (header.getIs_prepocessed() != null && header.getIs_prepocessed().equals(RescheduleFragment.TASK_RESCHEDULE)) {
                    if (header.getPts_date() != null) {
                        String ptsDate = Formatter.formatDate(header.getPts_date(), Global.DATE_TIME_STR_FORMAT);
                        lblPts.setVisibility(View.VISIBLE);
                        txtPts.setVisibility(View.VISIBLE);
                        txtPts.setText(ptsDate);
                    }
                }
                if (header.getVoice_note() == null) {
                    noVoiceNote.setVisibility(View.GONE);
                    txtVoiceNote.setVisibility(View.GONE);
                    btnVoiceNotes.setVisibility(View.GONE);
                    playerLayout.setVisibility(View.GONE);
                }
                String application = GlobalData.getSharedGlobalData().getAuditData().getApplication();
                if (Global.APPLICATION_COLLECTION.equalsIgnoreCase(application) &&
                        header.getPriority() != null) {
                    if (Global.NEW_FEATURE) {
                        if (Global.FEATURE_REVISIT_COLLECTION) { //new
                            btnRevisit.setVisibility(View.VISIBLE);
                            btnRevisit.setOnClickListener(this);
                        } else {
                            btnRevisit.setVisibility(View.GONE);
                        }
                    }
                }
            }
        } else {
            btnVoiceNotes.setVisibility(View.GONE);
            playerLayout.setVisibility(View.GONE);
            noVoiceNote.setVisibility(View.GONE);
            txtVoiceNote.setVisibility(View.GONE);
        }
        btnVoiceNotes.setVisibility(View.GONE);
        if (TaskHDataAccess.STATUS_SEND_PENDING.equals(header.getStatus()) ||
                TaskHDataAccess.STATUS_SEND_UPLOADING.equals(header.getStatus())) {
            LinearLayout buttonLayout = (LinearLayout) view.findViewById(R.id.buttons);
            buttonLayout.setVisibility(View.GONE);
            if (header.getVoice_note() == null) {
                noVoiceNote.setVisibility(View.GONE);
                txtVoiceNote.setVisibility(View.GONE);
                btnVoiceNotes.setVisibility(View.GONE);
                playerLayout.setVisibility(View.GONE);
            }
        }
        if (header.getPriority() != null && header.getPriority().length() > 0) {
            if (header.getOpen_date() == null) {
                header.setOpen_date(Tool.getSystemDateTime());
                new CustomerFragment.SendOpenReadTaskH(getActivity(), header).executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR);
            }
        }

        if (Global.FORM_NAME_GUARANTOR.equalsIgnoreCase(formName)
                || Global.FORM_NAME_PRE_SURVEY.equalsIgnoreCase(formName)) {
            btnPromise.setVisibility(View.GONE);
            btnStart.setEnabled(true);
        }
        // Hiding the piloting field for task visit polo form (2022-11-22)
        if (Global.FORM_NAME_VISIT_POLO.equalsIgnoreCase(formName)) {
            lblPilotingCae.setVisibility(View.GONE);
            txtPilotingCae.setVisibility(View.GONE);
        }
        // Add field source data // jowoen - 2025/01/21
        if (Global.FORM_NAME_PROMISE_TO_VISIT.equalsIgnoreCase(formName)
                || Global.FORM_NAME_PROMISE_TO_SURVEY.equalsIgnoreCase(formName)) {
            lblSourceData.setVisibility(View.VISIBLE);
            txtSourceData.setVisibility(View.VISIBLE);
            txtSourceData.setEnabled(false);
            txtSourceData.setText(mArguments.getString(SOURCE_DATA));
        }
        // Adding notes crm field (2022-12-28)
        if (Global.FORM_NAME_VISIT_POLO.equalsIgnoreCase(formName) ||
                "1".equals(header.getIs_piloting_cae())) {
            lblNotesCrm.setVisibility(View.VISIBLE);
            txtNotesCrm.setVisibility(View.VISIBLE);
            txtNotesCrm.setText(header.getNotes_crm() == null || "".equals(header.getNotes_crm()) ? "-" : header.getNotes_crm());
        }
        // Show for Pre Approval
        if ((Global.FORM_NAME_VISIT_POLO.equalsIgnoreCase(formName)
                || Global.FORM_NAME_PRE_SURVEY.equalsIgnoreCase(formName)
                || Global.FORM_NAME_PROMISE_TO_SURVEY.equalsIgnoreCase(formName)) && (1 == header.getIs_pre_approval())) {
            lblPreApproval.setVisibility(View.VISIBLE);
            txtPreApproval.setVisibility(View.VISIBLE);
        }

        record = new AudioRecord(getActivity());
        toDoList = new ToDoList(getActivity());
        listTaskH = toDoList.getListTaskInStatus(ToDoList.SEARCH_BY_ALL, "");

        isEditable = false;

        txtName.setText(mArguments.getString(CUSTOMER_NAME));
        txtPhone.setText(mArguments.getString(CUSTOMER_PHONE));
        txtAddress.setText(mArguments.getString(CUSTOMER_ADDRESS));
        txtZipCode.setText(mArguments.getString(CUSTOMER_ZIPCODE));
        txtNotes.setText(mArguments.getString(CUSTOMER_NOTES));
        txtPilotingCae.setText(mArguments.getString(CUSTOMER_PILOTING_CAE));
        txtPreApproval.setText(mArguments.getString(CUSTOMER_PRE_APPROVAL));

        String application = GlobalData.getSharedGlobalData().getAuditData().getApplication();
        if (Global.APPLICATION_COLLECTION.equalsIgnoreCase(application)) {
            taskdOSAmount = TaskDDataAccess.getOneFromTaskDWithTag(context, header.getUuid_task_h(), Global.TAG_OS_AMOUNT, header.getForm_version());
            taskdOD = TaskDDataAccess.getOneFromTaskDWithTag(context, header.getUuid_task_h(), Global.TAG_OD, header.getForm_version());
            taskdInstallmentNo = TaskDDataAccess.getOneFromTaskDWithTag(context, header.getUuid_task_h(), Global.TAG_INSTALLMENT_NO, header.getForm_version());

            lbOSAmount.setVisibility(View.VISIBLE);
            lbOD.setVisibility(View.VISIBLE);
            lbInstallmentNo.setVisibility(View.VISIBLE);
            txtOSAmount.setVisibility(View.VISIBLE);
            txtOD.setVisibility(View.VISIBLE);
            txtInstallmentNo.setVisibility(View.VISIBLE);
            if (header.getAmt_due() == null) {
                txtOSAmount.setText("-");
            } else {
                txtOSAmount.setText(Tool.separateThousand(header.getAmt_due()));
            }

            if (header.getOd() == null) {
                txtOD.setText("-");
            } else {
                String od = header.getOd() + " " + context.getString(R.string.txtDay);
                txtOD.setText(od);
            }

            if (header.getInst_no() == null) {
                txtInstallmentNo.setText("-");
            } else {
                txtInstallmentNo.setText(header.getInst_no());
            }
        }

        if (mArguments.getInt(SURVEY_MODE) == Global.MODE_TASK_UPDATE) {
            txtName.setEnabled(false);
            txtPhone.setEnabled(false);
            txtAddress.setEnabled(false);
            txtNotes.setEnabled(false);
            txtPendingNotes.setEnabled(false);
            txtDocuproFeedback.setEnabled(false);
            txtCategory.setEnabled(false);
            txtSubCategory.setEnabled(false);
            txtReasonDetail.setEnabled(false);
            txtValidation.setEnabled(false);
            lblPendingNotes.setVisibility(View.VISIBLE);
            lblDocuproFeedback.setVisibility(View.VISIBLE);
            lblFeedbackNotes.setVisibility(View.VISIBLE);
            lblUploadDocument.setVisibility(View.VISIBLE);
            lblCategory.setVisibility(View.VISIBLE);
            lblSubCategory.setVisibility(View.VISIBLE);
            lblReasonDetail.setVisibility(View.VISIBLE);
            lblValidation.setVisibility(View.VISIBLE);
            txtPendingNotes.setVisibility(View.VISIBLE);
            txtDocuproFeedback.setVisibility(View.VISIBLE);
            txtFeedbackNotes.setVisibility(View.VISIBLE);
            txtCategory.setVisibility(View.VISIBLE);
            txtSubCategory.setVisibility(View.VISIBLE);
            txtReasonDetail.setVisibility(View.VISIBLE);
            txtValidation.setVisibility(View.VISIBLE);
            rUploadDocLayout.setVisibility(View.VISIBLE);
            btnStart.setVisibility(View.GONE);
            btnPromise.setVisibility(View.GONE);
            btnSubmit.setVisibility(View.VISIBLE);
            txtPendingNotes.setText(header.getPending_notes());
            txtDocuproFeedback.setText(header.getDocupro_feedback());
            txtCategory.setText(header.getCategory() == null || "".equals(header.getCategory()) ? "-" : header.getCategory());
            txtSubCategory.setText(header.getSub_category() == null || "".equals(header.getSub_category()) ? "-" : header.getSub_category());
            txtReasonDetail.setText(header.getReason_detail() == null || "".equals(header.getReason_detail()) ? "-" : header.getReason_detail());
            txtValidation.setText(header.getValidasi() == null || "".equals(header.getValidasi()) ? "-" : header.getValidasi());
            txtPilotingCae.setVisibility(View.GONE);
            lblPilotingCae.setVisibility(View.GONE);
        }

        if (mArguments.getInt(SURVEY_MODE) == Global.MODE_VIEW_SENT_SURVEY) {
            // Flagging on task log menu for reminder po task (2022-08-12)
            String uuidTaskH = header.getUuid_task_h();
            ReminderPo reminderPo = ReminderPoDataAccess.getOneByUuidTaskH(context, uuidTaskH);
            boolean isCanViewTaskReminderPo = true;
            if (null != reminderPo) {
                String taskCreateDate = Formatter.formatDate(reminderPo.getDtm_crt(), Global.DATE_STR_FORMAT2);
                String currentDate = Formatter.formatDate(new Date(), Global.DATE_STR_FORMAT2);

                boolean isCurrentDateTask = taskCreateDate.compareToIgnoreCase(currentDate) == 0;
                boolean isHaveTaskD = false;


                List<TaskD> listTaskD = TaskDDataAccess.getAll(context, uuidTaskH);
                if (listTaskD != null && !listTaskD.isEmpty()) {
                    isHaveTaskD = true;
                }
                if (isCurrentDateTask && !isHaveTaskD) {
                    isCanViewTaskReminderPo = false;
                }
            }

            btnReset.setVisibility(View.GONE);
            btnStart.setVisibility(View.GONE);
            if (!isCanViewTaskReminderPo) {
                btnView.setVisibility(View.GONE);
            } else {
                btnView.setVisibility(View.VISIBLE);
            }

            try {
                String uuidScheme = header.getUuid_scheme();
                Scheme scheme = SchemeDataAccess.getOne(getActivity(), uuidScheme);
                if (scheme != null) {
                    String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                    List<TaskD> taskDs = TaskDDataAccess.getAll(getActivity(), header.getUuid_task_h(),
                            TaskDDataAccess.ALL_TASK);
                    if (taskDs != null && !taskDs.isEmpty()) {
                        boolean isTaskPaid = TaskDDataAccess.isTaskPaid(getActivity(), uuidUser, header.getUuid_task_h());
                        boolean isRVinFront = GeneralParameterDataAccess.isRvInFrontEnable(getActivity(), uuidUser);
                        if (isRVinFront) {
                            btnPrint.setVisibility(View.GONE);
                        } else if (!scheme.getIs_printable().equals("1") || !isTaskPaid) {
                            btnPrint.setVisibility(View.GONE);
                        } else {
                            if (header.getRv_number() != null && !header.getRv_number().isEmpty())
                                btnPrint.setVisibility(View.GONE);
                            else
                                btnPrint.setVisibility(View.VISIBLE);
                        }
                    } else {
                        if (GlobalData.getSharedGlobalData().getApplication().equals("MC")) {
                            GetTaskDOnline taskDOnline = new GetTaskDOnline(getActivity(), header);
                            taskDOnline.execute();
                        }
                    }
                }
            } catch (DaoException e) {
                FireCrash.log(e);
                if (Global.IS_DEV)
                    e.printStackTrace();
                ACRA.getErrorReporter().putCustomData("errorGetScheme", e.getMessage());
                ACRA.getErrorReporter().putCustomData("errorGetSchemeTime", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                ACRA.getErrorReporter().handleSilentException(new Exception("Just for the stacktrace, kena exception saat Get Scheme"));
                Toast.makeText(getActivity(), getActivity().getString(R.string.request_error),
                        Toast.LENGTH_SHORT).show();
                doBack(getActivity());
            } catch (Exception e) {
                FireCrash.log(e);
                if (Global.IS_DEV)
                    e.printStackTrace();
                ACRA.getErrorReporter().putCustomData("errorGetScheme", e.getMessage());
                ACRA.getErrorReporter().putCustomData("errorGetSchemeTime", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                ACRA.getErrorReporter().handleSilentException(new Exception("Just for the stacktrace, kena exception saat Get Scheme"));
                Toast.makeText(getActivity(), getActivity().getString(R.string.request_error),
                        Toast.LENGTH_SHORT).show();
                doBack(getActivity());
            }
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.customer_layout, container, false);
    }

    @Override
    public void onAttach(Context activity) {
        super.onAttach(activity);
        ACRA.getErrorReporter().putCustomData("LAST_CLASS_ACCESSED", getClass().getSimpleName());
        setHasOptionsMenu(true);
        Utility.freeMemory();
        mArguments = getArguments();
        context = activity;
        header = (SurveyHeaderBean) mArguments.getSerializable(SURVEY_HEADER);
        if (!(mArguments.getInt(SURVEY_MODE) == Global.MODE_VIEW_SENT_SURVEY)) {
            checkScheme = new CheckScheme();
            checkScheme.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR);
        } else {
            Global.SchemeIsChange = false;
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Utility.freeMemory();
    }

    @Override
    public void onDestroy() {
        if (task != null)
            task.cancel(true);
        if (checkScheme != null)
            checkScheme.cancel(true);

        super.onDestroy();
    }

    private boolean isCOHAktif() {
        String parameter = GeneralParameterDataAccess.getOne(getActivity(), GlobalData.getSharedGlobalData().getUser().getUuid_user(),
                Global.GS_CASHONHAND).getGs_value();
        return parameter != null && parameter.equals(Global.TRUE_STRING);
    }

    @SuppressLint("StaticFieldLeak")
    @Override
    public void onClick(View v) {
        // TODO Auto-generated method stub
        int button = v.getId();
        if (button == R.id.btnPts) {
            cancelCheckScheme();
            RescheduleFragment fragment = new RescheduleFragment();
            Bundle args = new Bundle();
            args.putString("taskId", header.getTask_id());
            fragment.setArguments(args);
            FragmentTransaction transaction = MainMenuActivity.fragmentManager.beginTransaction();
            transaction.setCustomAnimations(R.anim.activity_open_translate, R.anim.activity_close_scale, R.anim.activity_open_scale, R.anim.activity_close_translate);
            transaction.replace(R.id.content_frame, fragment);
            transaction.addToBackStack(null);
            transaction.commit();
        } else if (button == R.id.btnPtsNew) {
            if (Tool.isInternetconnected(context)) {
                showDateTimePicker();
            } else {
                Toast.makeText(getActivity(), getActivity().getString(R.string.no_internet_connection),
                        Toast.LENGTH_SHORT).show();
            }
        } else if (button == R.id.btnStartSurvey) {
            String application = GlobalData.getSharedGlobalData().getAuditData().getApplication();
            String cashLimit = GlobalData.getSharedGlobalData().getUser().getCash_limit();
            limit = cashLimit != null ? Double.parseDouble(cashLimit) : 0.0;
            String coh = GlobalData.getSharedGlobalData().getUser().getCash_on_hand();
            cashOnHand = coh != null ? Double.parseDouble(coh) : 0.0;
            List<TaskH> listTaskNotDownloaded = TaskHDataAccess.getTaskDetailNotDownloaded(getActivity(), header.getAppl_no());
            int countTaskNotDownloaded = listTaskNotDownloaded == null? 0: listTaskNotDownloaded.size();
            if (Global.APPLICATION_COLLECTION.equalsIgnoreCase(application) && isCOHAktif() && limit > 0 && cashOnHand >= limit) {
                DialogManager.showAlertNotif(getActivity(), getActivity().getString(R.string.limit_coh), "Cash On Hand");
            } else if (isFotoTaskAvailable) {
                DialogManager.showAlertNotif(getActivity(), getActivity().getString(R.string.task_image), "Task Image");
            } else if ( countTaskNotDownloaded > 0) {
                DialogManager.showAlertNotif(getActivity(), getActivity().getString(R.string.task_not_full_downloaded), "Task Detail");
            } else {
                isEditable = false;
                GlobalData.getSharedGlobalData().setDoingTask(true);
                if(Global.FORM_NAME_PROMISE_TO_SURVEY.equalsIgnoreCase(header.getScheme().getScheme_description()) || 
                        Global.FORM_NAME_PRE_SURVEY.equalsIgnoreCase(header.getScheme().getScheme_description())) {
                    Date currentTimestamps = new Date();
                    if (header.getTaskH().getPms_date() != null ) {
                        currentTimestamps = header.getTaskH().getPms_date();
                    }
                    header.getTaskH().setPms_date(currentTimestamps);
                    TaskHDataAccess.addOrReplace(getActivity(), header.getTaskH());
                    List<TaskH> listTask = TaskHDataAccess.getAllTaskCaeByApplNo(getActivity(), header.getTaskH().getAppl_no());
                    if (listTask != null && !listTask.isEmpty()) {
                        for (int i = 0; i<listTask.size(); i++) {
                            TaskH task = listTask.get(i);
                            if (task.getUuid_task_h().equalsIgnoreCase(header.getTaskH().getUuid_task_h())) {
                                continue;
                            }

                            if( TaskHDataAccess.STATUS_TASK_WAITING.equalsIgnoreCase(task.getStatus())
                                || TaskHDataAccess.STATUS_TASK_WAITING_DOWNLOAD.equalsIgnoreCase(task.getStatus())) {
                                task.setPms_date(currentTimestamps);
                                TaskHDataAccess.addOrReplace(getActivity(), task);
                            }
                        }
                    }
                }
                gotoNextDynamicForm();
            }
        } else if (button == R.id.btnReset) {
            txtName.setText("");
            txtAddress.setText("");
            txtNotes.setText("");
            txtPhone.setText("");
            txtZipCode.setText("");
            txtOSAmount.setText("");
            txtOD.setText("");
            txtOSAmount.setText("");
            try {
                header.setVoice_note(null);
                noVoiceNote.setVisibility(View.GONE);
                txtVoiceNote.setVisibility(View.GONE);
                btnVoiceNotes.setVisibility(View.GONE);
                playerLayout.setVisibility(View.GONE);
            } catch (Exception e) {
                FireCrash.log(e);
                e.printStackTrace();
            }
        } else if (button == R.id.btnPrint) {
            cancelCheckScheme();
            List<PrintResult> results = PrintResultDataAccess.getAll(getActivity(), header.getUuid_task_h());
            if (results == null || results.isEmpty()) {
                TaskManager.generatePrintResult(getActivity(), header.getTaskH());
            }
            try {
                //MainMenuActivity.fragmentManager.popBackStack();
            } catch (Exception e) {
                FireCrash.log(e);
                e.printStackTrace();
            }

            if (!GlobalData.getSharedGlobalData().getListPrinter().isEmpty()) {
                final String[] listPrinterDevice = GlobalData.getSharedGlobalData().getListPrinter().split(",");
                CharSequence printers[] = new CharSequence[listPrinterDevice.length];
                for (int i = 0; i < listPrinterDevice.length; i++) {
                    String printer[] = listPrinterDevice[i].split("@");
                    printers[i] = printer[0];
                }
                AlertDialog.Builder builder = new AlertDialog.Builder(this.getContext());
                builder.setTitle("Choose Printer Driver");
                builder.setItems(printers, new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        String printer[] = listPrinterDevice[which].split("@");
                        if ("0".equalsIgnoreCase(printer[1])) {
                            Intent intent = new Intent(context, PrintActivity.class);
                            //intent.putExtra(name, value);
                            intent.putExtra("taskId", header.getTask_id());
                            intent.putExtra("source", "log");
                            startActivity(intent);
                        } else {
                            Intent intent = new Intent(context, Main_Activity1.class);
                            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            intent.putExtra("taskId", header.getTask_id());
                            intent.putExtra("source", "log");
                            startActivity(intent);
                        }

                    }
                });
                builder.show();
            }

			/*CharSequence printers[] = new CharSequence[] {"Sato", "Enibit", "Winson", "Epson"};

			AlertDialog.Builder builder = new AlertDialog.Builder(this.getContext());
			builder.setTitle("Choose Printer Device");
			builder.setItems(printers, new DialogInterface.OnClickListener() {
				@Override
				public void onClick(DialogInterface dialog, int which) {
					// the user clicked on choice[which]
					if(which == 0){
						Intent intent = new Intent(context, PrintActivity.class);
						//intent.putExtra(name, value);
						intent.putExtra("taskId", header.getTask_id());
						intent.putExtra("source", "log");
						startActivity(intent);
					}
					else if(which == 1){
						Intent intent = new Intent(context, Main_Activity1.class);
						intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
						intent.putExtra("taskId", header.getTask_id());
						startActivity(intent);
					}
					else if(which == 2){
						Intent intent = new Intent(context, Main_Activity1.class );
						intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
						intent.putExtra("taskId", header.getTask_id());
						startActivity(intent);
					}else if(which == 3){
						Intent intent = new Intent(context, Main_Activity1.class );
						intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
						intent.putExtra("taskId", header.getTask_id());
						startActivity(intent);
					}
					else;

				}
			});*/
        } else if (button == R.id.btnViewTask) {
            Global.isViewer = true;
            viewTask = true;
            header = (SurveyHeaderBean) mArguments.getSerializable(SURVEY_HEADER);
            if (header.getIs_prepocessed() != null && header.getIs_prepocessed().equals(RescheduleFragment.TASK_RESCHEDULE)) {
                Toast.makeText(getActivity(), getActivity().getString(R.string.view_task_reschedule),
                        Toast.LENGTH_SHORT).show();
            } else {
                cancelCheckScheme();
                Bundle extras = new Bundle();
                extras.putInt(Global.BUND_KEY_MODE_SURVEY, mArguments.getInt(SURVEY_MODE));
                extras.putSerializable(Global.BUND_KEY_SURVEY_BEAN, mArguments.getSerializable(SURVEY_HEADER));
                QuestionSetTask task = new QuestionSetTask(getActivity(), extras);
                task.execute();
            }
//            ArrayList<QuestionBean> questions = new ArrayList<QuestionBean>();
//            TaskH h = header.getTaskH();
//            Scheme scheme = SchemeDataAccess.getOne(getActivity(), h.getUuid_scheme());
//
//            List<QuestionSet> qs = header.getScheme().getQuestionSetList();
//            for (QuestionSet set : qs) {
//                QuestionBean bean = new QuestionBean(set);
//                questions.add(bean);
//            }
//            Constant.listOfQuestion = questions;
//            Intent intent = new Intent(getActivity(), DynamicFormActivity.class);
//            intent.putExtras(extras);
//            startActivity(intent);
//            new Handler().postDelayed(new Runnable() {
//    	        @Override
//    	        public void run() {
//    	        	doBack(getActivity());
//    	        }
//    	    }, 250);
        } else if (button == R.id.btnVoiceNotes) {
            if (btnVoiceNotes.isChecked()) {
                //start record
                record.startRecording(v);
                playerLayout.setVisibility(View.GONE);
            } else {
                //stop record
                record.stop(v);
                playerLayout.setVisibility(View.VISIBLE);
            }
        } else if (button == R.id.btnPlay) {
            if (mArguments.getInt(SURVEY_MODE) == Global.MODE_SURVEY_TASK ||
                    mArguments.getInt(SURVEY_MODE) == Global.MODE_VIEW_SENT_SURVEY) {
//        		SurveyHeaderBean header = (SurveyHeaderBean) mArguments.getSerializable(SURVEY_HEADER);
                AudioRecord.playAudio(getActivity(), header.getVoice_note());
            } else {
                record.play(v);
            }
        } else if (button == R.id.btnStop) {
            if (mArguments.getInt(SURVEY_MODE) == Global.MODE_SURVEY_TASK ||
                    mArguments.getInt(SURVEY_MODE) == Global.MODE_VIEW_SENT_SURVEY) {
                AudioRecord.stopPlay(v);
            } else {
                AudioRecord.stopPlay(v);
            }
        } else if (button == R.id.btnRevisit) {
            new AsyncTask<Void, Void, MssResponseType>() {
                private ProgressDialog progressDialog;

                @Override
                protected void onPreExecute() {
                    isEditable = false;
                    progressDialog = ProgressDialog.show(context,
                            "", getString(R.string.progressWait), true);
                }

                @Override
                protected MssResponseType doInBackground(Void... params) {
                    CheckResubmitApi api = new CheckResubmitApi(context);
                    try {
                        if (Tool.isInternetconnected(context)) {
                            return api.request(header.getUuid_task_h());
                        }
                        return null;
                    } catch (IOException e) {
                        FireCrash.log(e);
                        e.printStackTrace();
                        return null;
                    }
                }

                @Override
                protected void onPostExecute(MssResponseType checkResubmitResponse) {
                    super.onPostExecute(checkResubmitResponse);
                    if (progressDialog != null && progressDialog.isShowing()) {
                        try {
                            progressDialog.dismiss();
                        } catch (Exception e) {
                            FireCrash.log(e);
                        }
                    }
                    if (checkResubmitResponse != null) {
                        if (checkResubmitResponse.getStatus().getMessage().equalsIgnoreCase("OK")) {
                            isEditable = true;
                            for (TaskH task : listTaskH) {
                                if (header.getTask_id().equalsIgnoreCase(task.getTask_id())) {
                                    isEditable = false;
                                }
                            }
                            if (isEditable) {
                                GlobalData.getSharedGlobalData().setDoingTask(true);
                                gotoNextDynamicForm();
                            } else {
                                Toast.makeText(getActivity(), getActivity().getString(R.string.edit_still_pending),
                                        Toast.LENGTH_SHORT).show();
                            }
                        } else {
                            final NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(context);
                            dialogBuilder.withTitle(context.getString(R.string.info_capital))
                                    .isCancelableOnTouchOutside(false)
                                    .withIcon(android.R.drawable.ic_dialog_alert)
                                    .withMessage(checkResubmitResponse.getStatus().getMessage())
                                    .withButton1Text(context.getString(R.string.btnOk))
                                    .setButton1Click(new OnClickListener() {
                                        @Override
                                        public void onClick(View v) {
                                            dialogBuilder.dismiss();
                                            doBack(getActivity());
                                        }
                                    })
                                    .show();
                        }
                    } else {
                        Toast.makeText(getActivity(), getActivity().getString(R.string.failed),
                                Toast.LENGTH_SHORT).show();
                    }
                }
            }.execute();

        } else if (button == R.id.btnUploadDoc) {
            if (null == bmDocument && null == byteDocument) {
                openCameraApp(getActivity());
            } else {
                final AlertDialog.Builder builder = new AlertDialog.Builder(getActivity());
                builder.setMessage(getString(R.string.picture_option));
                builder.setCancelable(true);
                builder.setPositiveButton(getString(R.string.btnView), new DialogInterface.OnClickListener() {

                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        try {
                            Global.isViewer = true;
                            Bundle extras = new Bundle();
                            extras.putByteArray(ImageViewerActivity.BUND_KEY_IMAGE, byteDocument);
                            extras.putInt(ImageViewerActivity.BUND_KEY_IMAGE_QUALITY, Utils.picQuality);
                            extras.putBoolean(ImageViewerActivity.BUND_KEY_IMAGE_ISVIEWER, Global.isViewer);
                            Intent intent = new Intent(getActivity(), ImageViewerActivity.class);
                            intent.putExtras(extras);
                            startActivityForResult(intent, Global.REQUEST_EDIT_IMAGE);
                        } catch (Exception e) {
                            FireCrash.log(e);
                        }
                    }
                });
                builder.setNeutralButton(getString(R.string.btnRetake), new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int id) {
                        openCameraApp(getActivity());
                    }
                });
                builder.setNegativeButton(getString(R.string.btnDelete), new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        bmDocument = null;
                        byteDocument = null;
                        btnUploadDocument.setImageResource(R.drawable.ic_camera);
                        bgUploadDocument.setVisibility(View.VISIBLE);
                        dialog.cancel();
                    }
                });
                builder.create().show();
            }
        } else if (button == R.id.btnSubmitTaskUpdate) {
            if (TextUtils.isEmpty(txtFeedbackNotes.getText())) {
                Toast.makeText(context, getString(R.string.feedback_notes_required),
                        Toast.LENGTH_SHORT).show();
            } else {
                new SubmitData().execute();
            }
        }
        /*
        switch (button) {
		case R.id.btnStartSurvey:
			Fragment fragment = new QuestionFragment();
	    	FragmentTransaction transaction = getActivity().getSupportFragmentManager().beginTransaction();
	        transaction.replace(R.id.content_frame, fragment);
	        transaction.commit();
			break;
		case R.id.btnReset:
			txtName.setText("");
			txtAddress.setText("");
			txtNotes.setText("");
			txtPhone.setText("");
			txtZipCode.setText("");
			break;
		case R.id.btnVoiceNotes:

			break;
		default:
			break;
		}
		*/
    }

    private void gotoNextDynamicForm() {
        Global.isViewer = false;
        String emptyMessage = "";
        if (mArguments.getInt(SURVEY_MODE) == Global.MODE_SURVEY_TASK) {
            try {
                if (header.getPriority() == null || header.getPriority().length() == 0) {
                    emptyMessage = checkForEmpty();
                }
            } catch (Exception e) {
                FireCrash.log(e);
            }
        } else if (mArguments.getInt(SURVEY_MODE) == Global.MODE_VIEW_SENT_SURVEY) {
            if (isEditable && (header.getPriority() == null || header.getPriority().length() == 0)) {
                emptyMessage = checkForEmpty();
            }
        } else {
            emptyMessage = checkForEmpty();
        }
        if (emptyMessage.length() > 0) {
            Toast.makeText(getActivity(), emptyMessage,
                    Toast.LENGTH_SHORT).show();
        } else {
            if (mArguments.getInt(SURVEY_MODE) != Global.MODE_VIEW_SENT_SURVEY || (mArguments.getInt(SURVEY_MODE) == Global.MODE_VIEW_SENT_SURVEY && isEditable)) {
                cancelCheckScheme();
                Scheme scheme = null;
                try {
                    String uuidScheme = header.getUuid_scheme();
                    scheme = SchemeDataAccess.getOne(getActivity(), uuidScheme);
                } catch (DaoException e) {
                    FireCrash.log(e);
                    ACRA.getErrorReporter().putCustomData("errorGetScheme", e.getMessage());
                    ACRA.getErrorReporter().putCustomData("errorGetSchemeTime", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                    ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat Get Scheme"));
                } catch (Exception e) {
                    FireCrash.log(e);
                    ACRA.getErrorReporter().putCustomData("errorGetScheme", e.getMessage());
                    ACRA.getErrorReporter().putCustomData("errorGetSchemeTime", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                    ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat Get Scheme"));
                }

                if (scheme != null) {
                    header.setScheme(scheme);
                    FormBean formBean = new FormBean(scheme);
                    if (lastUpdateScheme != null) {
                        formBean = null;
                        formBean = new FormBean(lastUpdateScheme);
                    }
                    header.setCustomer_name(txtName.getText().toString());
                    header.setCustomer_address(txtAddress.getText().toString());
                    header.setCustomer_phone(txtPhone.getText().toString());
//                header.setZip_code(txtZipCode.getText().toString());
                    header.setNotes(txtNotes.getText().toString());
                    if (header.getStart_date() == null) {
                        if (header.getPriority() != null && header.getPriority().length() > 0) {
//                	header.setStart_date(Tool.getSystemDateTime());
                        } else {
                            header.setStart_date(Tool.getSystemDateTime());
                        }
                    }
                    header.setForm(formBean);
                    header.setIs_preview_server(formBean.getIs_preview_server());
                    try {
                        header.setVoice_note(record.saveAudioToByte());
                    } catch (Exception e) {
                        FireCrash.log(e);
                        header.setVoice_note(header.getVoice_note());

                    }

                    Bundle extras = new Bundle();
                    if (isEditable) {
                        mArguments.putInt(SURVEY_MODE, Global.MODE_SURVEY_TASK);
                        header.setStatus(TaskHDataAccess.STATUS_SEND_DOWNLOAD);
                    }

                    extras.putInt(Global.BUND_KEY_MODE_SURVEY, mArguments.getInt(SURVEY_MODE));
                    extras.putString(Global.BUND_KEY_UUID_TASKH, header.getUuid_task_h());
                    extras.putSerializable(Global.BUND_KEY_SURVEY_BEAN, header);
                    extras.putSerializable(Global.BUND_KEY_FORM_BEAN, formBean);

                    task = new QuestionSetTask(getActivity(), extras);
                    task.execute();
                    //Tambah ini biar engga kena leak memory
//                    onDetach();
                } else {
                    Toast.makeText(getActivity(), getActivity().getString(R.string.request_error),
                            Toast.LENGTH_SHORT).show();
                    doBack(getActivity());
                }
            }
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Utils.REQUEST_IN_APP_CAMERA && resultCode == RESULT_OK) {
            Bundle bundle = data.getExtras();
            if (bundle != null) {
                Uri uri = Uri.parse(bundle.getString(CameraActivity.PICTURE_URI));
                File file = new File(uri.getPath());
                bmDocument = Utils.pathToBitmapWithRotation(file);
                byteDocument = Utils.pathBitmapToByteWithRotation(file);
                btnUploadDocument.setImageBitmap(bmDocument);
                bgUploadDocument.setVisibility(View.GONE);
            }
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        cancelCheckScheme();
    }

    private static File createImageFile(Context context) throws IOException {
        @SuppressLint("SimpleDateFormat")
        String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
        String imageFileName = "JPEG_" + timeStamp + "_";
        File storageDir;
        File image = null;
        try {
            storageDir = context.getExternalFilesDir(
                    Environment.DIRECTORY_PICTURES);
            if (!storageDir.exists()) {
                storageDir.mkdir();
            }
            image = File.createTempFile(
                    imageFileName,  /* prefix */
                    ".jpg",         /* suffix */
                    storageDir      /* directory */
            );
        } catch (IOException e) {
            Log.w("ExternalStorage", "Error writing ", e);
        }
        if (image == null)
            return image;

        return image;
    }

    private void openCameraApp(Activity activity) {
        if (GlobalData.getSharedGlobalData().isUseOwnCamera()) {
            int quality = Utils.picQuality;
            int thumbHeight = Utils.picHeight;
            int thumbWidht = Utils.picWidth;

            Intent intent = new Intent(activity, CameraActivity.class);
            intent.putExtra(CameraActivity.PICTURE_WIDTH, thumbWidht);
            intent.putExtra(CameraActivity.PICTURE_HEIGHT, thumbHeight);
            intent.putExtra(CameraActivity.PICTURE_QUALITY, quality);

            startActivityForResult(intent, Utils.REQUEST_IN_APP_CAMERA);
        } else {
            try {
                Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                if (intent.resolveActivity(activity.getPackageManager()) != null) {
                    File photoFile = null;
                    try {
                        photoFile = createImageFile(activity.getApplicationContext());
                    } catch (IOException ex) {
                        FireCrash.log(ex);
                    }
                    if (photoFile != null) {
                        intent.putExtra(MediaStore.EXTRA_OUTPUT,
                                Uri.fromFile(photoFile));
                        activity.startActivityForResult(intent, Utils.REQUEST_CAMERA);
                    }
                }
            } catch (Exception e) {
                FireCrash.log(e);
            }
        }
    }

    private String checkForEmpty() {
        // TODO Auto-generated method stub
        String name = txtName.getText().toString();
        String address = txtAddress.getText().toString();
        String phone = txtPhone.getText().toString();
        StringBuffer emptyMessage = new StringBuffer();
        String isRequired = " " + getString(R.string.msgRequired) + "\n";
        if (isEmpty(name)) {
            emptyMessage.append(txtName.getHint().toString() + isRequired);
        }
        if (isEmpty(address)) {
            emptyMessage.append(txtAddress.getHint().toString() + isRequired);
        }
//        if(isEmpty(zipcode)){
//        	emptyMessage.append(txtZipCode.getHint().toString()+" is Required\n");
//        }else if(zipcode.length()>0 && zipcode.length()!=5){
//        	emptyMessage.append(txtZipCode.getHint().toString()+" must 5 digit !\n");
//        }
        if (isEmpty(phone)) {
            emptyMessage.append(txtPhone.getHint().toString() + isRequired);
        } else if (phone.length() < 8) {
            emptyMessage.append(getString(R.string.phone_validasi));
        }

        return emptyMessage.toString();
    }

    //new
    private void initiateRefresh() {
        cancelRefreshTask();
        backgroundTask = new RefreshBackgroundTask();
        backgroundTask.execute();
    }

    private void cancelRefreshTask() {
        if (backgroundTask != null) {
            backgroundTask.cancel(true);
            backgroundTask = null;
        }
    }

    private boolean isEmpty(String str) {
        // TODO Auto-generated method stub
        boolean isEmpty = true;
        if (str.length() > 0)
            isEmpty = false;
        return isEmpty;
    }

    @Override
    public void onResume() {
        super.onResume();
        if (header.getRv_number() != null && !header.getRv_number().isEmpty())
            btnPrint.setVisibility(View.GONE);
        getActivity().getActionBar().setTitle("Customer Form");
        getActivity().getActionBar().removeAllTabs();
        getActivity().getActionBar().setNavigationMode(ActionBar.NAVIGATION_MODE_STANDARD);

        if (listTaskH != null) {
            initiateRefresh();
        }
    }

    @Override
    public void onPrepareOptionsMenu(Menu menu) {
        super.onPrepareOptionsMenu(menu);
        mainMenu = menu;
        try {
            if (GlobalData.getSharedGlobalData().getAuditData() == null)
                MainMenuActivity.InitializeGlobalDataIfError(getActivity().getApplicationContext());

            String application = GlobalData.getSharedGlobalData().getAuditData().getApplication();
            if (Global.APPLICATION_COLLECTION.equalsIgnoreCase(application)) {
                menu.findItem(R.id.menuMore).setVisible(true);
                mainMenu.findItem(R.id.mnViewMap).setVisible(false);
                mainMenu.findItem(R.id.mnViewAllHeader).setVisible(false);
                mainMenu.findItem(R.id.mnInstallmentSchedule).setVisible(true);
                mainMenu.findItem(R.id.mnPaymentHistory).setVisible(true);
                mainMenu.findItem(R.id.mnCollectionActivity).setVisible(true);
            } else {
//				if((mArguments.getInt(SURVEY_MODE) == Global.MODE_SURVEY_TASK)&&
//						header.getPriority()!=null) {
//					menu.findItem(R.id.menuMore).setVisible(true);
//					mainMenu.findItem(R.id.mnViewData).setVisible(true);
//				}else{
                menu.findItem(R.id.menuMore).setVisible(false);
//				}
            }
        } catch (Exception e) {
            FireCrash.log(e);
            ACRA.getErrorReporter().putCustomData("errorPrepareOptionMenu", e.getMessage());
            ACRA.getErrorReporter().putCustomData("errorPrepareOptionMenu", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
            ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat Prepare Option Menu"));
            if (Global.IS_DEV)
                e.printStackTrace();
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // Handle item selection
        int id = item.getItemId();
        if (id == R.id.menuMore) {
            mainMenu.findItem(R.id.mnViewMap).setVisible(false);
            mainMenu.findItem(R.id.mnViewAllHeader).setVisible(false);
        }
        if (id == R.id.mnInstallmentSchedule) {
            if (Global.installmentSchIntent != null) {
                Global.installmentSchIntent.putExtra(Global.BUND_KEY_TASK_ID, header.getTask_id());
                startActivity(Global.installmentSchIntent);
            }
        } else if (id == R.id.mnPaymentHistory) {
            if (Global.paymentHisIntent != null) {
                Global.paymentHisIntent.putExtra(Global.BUND_KEY_TASK_ID, header.getTask_id());
                startActivity(Global.paymentHisIntent);
            }
        } else if (id == R.id.mnCollectionActivity) {
            if (Global.collectionActIntent != null) {
                Global.collectionActIntent.putExtra(Global.BUND_KEY_TASK_ID, header.getTask_id());
                startActivity(Global.collectionActIntent);
            }
        }

        return super.onOptionsItemSelected(item);
    }

    public void cancelCheckScheme() {
        try {
            if (checkScheme != null) {
                checkScheme.cancel(true);
                checkScheme = null;
            }
        } catch (Exception e) {
            FireCrash.log(e);
            ACRA.getErrorReporter().putCustomData("errorGetScheme", e.getMessage());
            ACRA.getErrorReporter().putCustomData("errorGetSchemeTime", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
            ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat Get Scheme"));
            e.printStackTrace();
        }
    }

    public static class SendOpenReadTaskH extends AsyncTask<Void, Void, Void> {
        private TaskH taskH;
        private SurveyHeaderBean bean;
        private WeakReference<Context> context;
        private WeakReference<Activity> activity;

        public SendOpenReadTaskH(Activity activity, SurveyHeaderBean bean) {
            this.bean = bean;
            this.context = new WeakReference<Context>(activity);
            this.activity = new WeakReference<Activity>(activity);
        }

        @Override
        protected Void doInBackground(Void... arg0) {
            // TODO Auto-generated method stub
            if (Tool.isInternetconnected(context.get())) {
                taskH = bean.getTaskH();

                try {
                    if (null == taskH.getFlag()) {
                        taskH.setFlag("");
                    }
                } catch (NullPointerException e) {
                    FireCrash.log(e);

                    taskH.setFlag("");
                }

                if (taskH.getFlag().equalsIgnoreCase("")) {
                    JsonRequestOpenStartTask task = new JsonRequestOpenStartTask();
                    task.setAudit(GlobalData.getSharedGlobalData().getAuditData());
                    task.addImeiAndroidIdToUnstructured();
                    task.setTaskH(taskH);

                    String json = GsonHelper.toJson(task);

                    String url = GlobalData.getSharedGlobalData().getURL_SUBMITOPENREADTASK();
                    boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                    boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                    HttpCryptedConnection httpConn = new HttpCryptedConnection(activity.get(), encrypt, decrypt);
                    HttpConnectionResult serverResult = null;
                    try {
                        serverResult = httpConn.requestToServer(url, json);
                        String response = serverResult.getResult();
                        MssResponseType responseType = GsonHelper.fromJson(response, MssResponseType.class);
                        if (responseType.getStatus().getCode() == 0) {
                            try {
                                TaskHDataAccess.addOrReplace(context.get(), taskH);
                            } catch (Exception e) {
                                FireCrash.log(e);
                                ACRA.getErrorReporter().putCustomData("errorGetMessageFromServer", e.getMessage());
                                ACRA.getErrorReporter().putCustomData("errorGetMessageFromServer", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                                ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat convert json dari Server dan addOrReplace taskH"));
                            }
                        }
                    } catch (Exception e) {
                        FireCrash.log(e);
                        ACRA.getErrorReporter().putCustomData("errorRequestToServer", e.getMessage());
                        ACRA.getErrorReporter().putCustomData("errorRequestToServer", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                        ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat request to server"));
                        if (Global.IS_DEV)
                            e.printStackTrace();
                    }
                }
            }
            return null;
        }

    }

    private class GetTaskDOnline extends AsyncTask<Void, Void, List<TaskD>> {
        private ProgressDialog dialog;
        private SurveyHeaderBean bean;
        private Context context;

        public GetTaskDOnline(Context context, SurveyHeaderBean bean) {
            this.context = context;
            this.bean = bean;
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();

            dialog = ProgressDialog.show(context, "", context.getString(R.string.progressWait), true, false);
        }

        @Override
        protected List<TaskD> doInBackground(Void... params) {
            DetailTaskHResponse response = null;

            if (Tool.isInternetconnected(context)) {
                DetailTaskHRequest request = new DetailTaskHRequest();
                request.setUuidTaskH(bean.getUuid_task_h());
                request.setFlag(bean.getFlag());
                request.setAudit(GlobalData.getSharedGlobalData().getAuditData());

                HttpCryptedConnection httpConn = new HttpCryptedConnection(context,
                        GlobalData.getSharedGlobalData().isEncrypt(), GlobalData.getSharedGlobalData().isDecrypt());
                String url = GlobalData.getSharedGlobalData().getURL_GET_TASK_LOG();
                HttpConnectionResult serverResult;

                try {
                    serverResult = httpConn.requestToServer(url, GsonHelper.toJson(request), Global.DEFAULTCONNECTIONTIMEOUT);

                    if (serverResult != null && serverResult.isOK()) {
                        try {
                            response = GsonHelper.fromJson(serverResult.getResult(), DetailTaskHResponse.class);
                        } catch (Exception e) {
                            FireCrash.log(e);
                            ACRA.getErrorReporter().putCustomData("errorGetMessageFromServer", e.getMessage());
                            ACRA.getErrorReporter().putCustomData("errorGetMessageFromServer", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                            ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat convert json dari Server"));
                            e.printStackTrace();
                        }
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    ACRA.getErrorReporter().putCustomData("errorRequestToServer", e.getMessage());
                    ACRA.getErrorReporter().putCustomData("errorRequestToServer", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                    ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat request to server"));
                    e.printStackTrace();
                }
            }
            if (response != null) {
                return response.getTaskDs();
            }
            return null;
        }

        @Override
        protected void onPostExecute(List<TaskD> results) {
            super.onPostExecute(results);

            if (dialog != null && dialog.isShowing()) {
                dialog.dismiss();
            }

            if (results != null) {
                TaskDDataAccess.addOrReplace(context, results);

                boolean isTaskPaid = TaskDDataAccess.isTaskPaid(getActivity(),
                        GlobalData.getSharedGlobalData().getUser().getUuid_user(), bean.getUuid_task_h());
                if (!bean.getScheme().getIs_printable().equals("1") || !isTaskPaid) {
                    btnPrint.setVisibility(View.GONE);
                } else {
                    if (bean.getRv_number() != null && !bean.getRv_number().isEmpty())
                        btnPrint.setVisibility(View.GONE);
                    else
                        btnPrint.setVisibility(View.VISIBLE);
                }
            } else {
                Toast.makeText(getActivity(), getActivity().getString(R.string.failed_get_taskd),
                        Toast.LENGTH_SHORT).show();
                doBack(getActivity());
            }
        }
    }

    public class SubmitPromiseSurvey extends AsyncTask<Void, Void, Void> {
        private TaskH taskH;
        private SurveyHeaderBean bean;
        private Context context;
        private Activity activity;
        private JsonRequestPromiseSurvey jsonRequest;
        private ProgressDialog progressDialog;

        public SubmitPromiseSurvey(SurveyHeaderBean bean, Activity activity) {
            this.bean = bean;
            this.context = activity;
            this.activity = activity;
        }

        SubmitPromiseSurvey(Activity activity, JsonRequestPromiseSurvey jsonRequest) {
            this.context = activity;
            this.activity = activity;
            this.jsonRequest = jsonRequest;
        }

        @Override
        protected void onPreExecute() {
            this.progressDialog = ProgressDialog.show(context, "", getContext().getString(R.string.please_wait), true);
        }

        @Override
        protected Void doInBackground(Void... voids) {
            if (Tool.isInternetconnected(context)) {

                String json = GsonHelper.toJson(jsonRequest);
                String url = GlobalData.getSharedGlobalData().getURL_PROMISE_SURVEY();

                boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                HttpCryptedConnection httpConn = new HttpCryptedConnection(activity, encrypt, decrypt);
                HttpConnectionResult serverResult = null;

                try {
                    serverResult = httpConn.requestToServer(url, json);
                    String response = serverResult.getResult();
                    MssResponseType responseType = GsonHelper.fromJson(response, MssResponseType.class);
                    if (responseType.getStatus().getCode() == 0) {
                        activity.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                btnPromise.setEnabled(false);
                                btnStart.setEnabled(true);
                                btnStart.setOnClickListener(CustomerFragment.this);
                            }
                        });

                        try {
                            taskH = TaskHDataAccess.getOneTaskHeader(activity, jsonRequest.getuuid_task_h());
                            if ("1".equalsIgnoreCase(GlobalData.getSharedGlobalData().getUser().getIs_piloting())) {
                                List<TaskH> listTask = TaskHDataAccess.getAllTaskByApplNo(context, GlobalData.getSharedGlobalData().getUser().getUuid_user(), header.getAppl_no());
                                if (listTask != null && !listTask.isEmpty()) {
                                    for (TaskH task : listTask) {
                                        task.setPms_date(jsonRequest.getPts_date());
                                        //Update TaskH Promise to Survey Date and Time
                                        TaskHDataAccess.addOrReplace(context, task);
                                    }
                                } else {
                                    taskH.setPms_date(jsonRequest.getPts_date());
                                    //Update TaskH Promise to Survey Date and Time
                                    TaskHDataAccess.addOrReplace(context, taskH);
                                }
                            } else {
                                taskH.setPms_date(jsonRequest.getPts_date());
                                //Update TaskH Promise to Survey Date and Time
                                TaskHDataAccess.addOrReplace(context, taskH);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    } else {
                        activity.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                Toast.makeText(activity, getString(R.string.msgTaskUnassign2), Toast.LENGTH_SHORT).show();
                                doBack(getActivity());
                            }
                        });
                    }
                } catch (Exception e) {
                    if (Global.IS_DEV)
                        e.printStackTrace();
                }
            }
            return null;
        }

        @Override
        protected void onPostExecute(Void result) {
            if (progressDialog.isShowing()) {
                try {
                    progressDialog.dismiss();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            Handler handler = new Handler(Looper.getMainLooper());
            handler.post(new Runnable() {

                @Override
                public void run() {
                    try {
                        if (PriorityTabFragment.handler != null)
                            PriorityTabFragment.handler.sendEmptyMessage(0);
                    } catch (Exception e) {
                        ACRA.getErrorReporter().putCustomData("ErrorMainMenuActivity", e.getMessage());
                        ACRA.getErrorReporter().putCustomData("ErrorMainMenuActivity", Tool.getSystemDateTime().toLocaleString());
                        ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat set Drawer Counter"));
                    }
                }
            });
        }
    }

    public class CheckScheme extends AsyncTask<Void, Void, Boolean> {

        @Override
        protected Boolean doInBackground(Void... params) {
            try {
                String uuidScheme = header.getUuid_scheme();
                Scheme schema = SchemeDataAccess.getOne(getActivity(), uuidScheme);
                if (schema != null) {
                    JsonRequestScheme requestScheme = new JsonRequestScheme();
                    requestScheme.setAudit(GlobalData.getSharedGlobalData().getAuditData());
                    requestScheme.setUuid_user(GlobalData.getSharedGlobalData().getUser().getUuid_user());
                    requestScheme.setUuid_scheme(schema.getUuid_scheme());
                    requestScheme.setTask(Global.TASK_GETONE);

                    String json = GsonHelper.toJson(requestScheme);
                    String url = GlobalData.getSharedGlobalData().getURL_GET_SCHEME();
                    boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                    boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                    HttpCryptedConnection httpConn = new HttpCryptedConnection(getActivity(), encrypt, decrypt);
                    HttpConnectionResult serverResult = null;
                    try {
                        serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        ACRA.getErrorReporter().putCustomData("errorGetMessageFromServer", e.getMessage());
                        ACRA.getErrorReporter().putCustomData("errorGetMessageFromServer", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                        ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat request to Server"));
                        if (Global.IS_DEV)
                            e.printStackTrace();
                    }
                    if (serverResult != null && serverResult.isOK()) {
                        try {
                            String result = serverResult.getResult();
                            JsonResponseScheme responseScheme = GsonHelper.fromJson(result, JsonResponseScheme.class);
                            List<Scheme> schemes = responseScheme.getListScheme();

                            Scheme scheme = schemes.get(0);
                            try {
                                Date newLastUpdate = scheme.getScheme_last_update();
//                                Date temp_last_update = Global.TempScheme.get(scheme.getUuid_scheme());
                                Date tempLastUpdate = schema.getScheme_last_update();
                                lastUpdateScheme = scheme;
                                Global.SchemeIsChange = newLastUpdate.after(tempLastUpdate);
                            } catch (Exception e) {
                                FireCrash.log(e);
                            }

                        } catch (Exception e) {
                            FireCrash.log(e);
                            ACRA.getErrorReporter().putCustomData("errorGetScheme", e.getMessage());
                            ACRA.getErrorReporter().putCustomData("errorGetSchemeTime", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                            ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat Get Scheme"));
                            Global.SchemeIsChange = false;
                        }
                    } else {
                        Global.SchemeIsChange = false;
                    }
                }
            } catch (Exception e) {
                FireCrash.log(e);
                ACRA.getErrorReporter().putCustomData("errorGetScheme", e.getMessage());
                ACRA.getErrorReporter().putCustomData("errorGetSchemeTime", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat Get Scheme"));
                Global.SchemeIsChange = false;
            }
            return true;
        }

        @Override
        protected void onCancelled() {
            super.onCancelled();
            Global.SchemeIsChange = false;
        }
    }

    private class RefreshBackgroundTask extends AsyncTask<Void, Void, List<TaskH>> {

        static final int TASK_DURATION = 2 * 1000; // 2 seconds

        @Override
        protected List<TaskH> doInBackground(Void... params) {
            // Sleep for a small amount of time to simulate a background-task
            try {
                Thread.sleep(TASK_DURATION);
            } catch (InterruptedException e) {
                FireCrash.log(e);
                e.printStackTrace();
                // Restore interrupted state...
                Thread.currentThread().interrupt();
            }
            listTaskH.clear();
            listTaskH.addAll(toDoList.getListTaskInStatus(ToDoList.SEARCH_BY_ALL, ""));
            ToDoList.listOfSurveyStatus = null;
            List<SurveyHeaderBean> list = new ArrayList<SurveyHeaderBean>();
            for (TaskH h : listTaskH) {
                list.add(new SurveyHeaderBean(h));
            }
            ToDoList.listOfSurveyStatus = list;

            // Return a new random list of cheeses
            return listTaskH;
        }

        @Override
        protected void onCancelled() {
            super.onCancelled();
//			onRefreshComplete();
        }

        @Override
        protected void onPostExecute(List<TaskH> result) {
            super.onPostExecute(result);
//	            statusListAdapter =new StatusArrayAdapter(getActivity().getApplicationContext(), listTaskH);
//				gridView.setAdapter(statusListAdapter);
//				statusListAdapter.notifyDataSetChanged();
            // Tell the Fragment that the refresh has completed
           /* try {
                long logCounter = TaskLog.getCounterLog(getActivity());
                long taskListCounter = ToDoList.getCounterTaskList(getActivity());
                if (MainMenuActivity.mnLog != null)
                    MainMenuActivity.mnLog.setCounter(String.valueOf(logCounter));
                if (MainMenuActivity.mnTaskList != null)
                    MainMenuActivity.mnTaskList.setCounter(String
                            .valueOf(taskListCounter));
                try {
                    if (MainMenuActivity.menuAdapter != null)
                        MainMenuActivity.menuAdapter.notifyDataSetChanged();
                } catch (Exception e) {
                    FireCrash.log(e);
                    // TODO: handle exception
                }
            } catch (Exception e) {
                    FireCrash.log(e);
                // TODO: handle exception
            }*/
//			try {
//				MainMenuActivity.setDrawerCounter();
//			} catch (Exception e) {
//				// TODO: handle exception
//			}
//			viewAdapter.notifyDataSetChanged();
//			onRefreshComplete();
        }

    }

    //Nendi: Add Datetime picker for Promise to Survey
    private SlideDateTimeListener listener = new SlideDateTimeListener() {

        @Override
        public void onDateTimeSet(final Date date) {
            Calendar cal = Calendar.getInstance();
            long diff = cal.getTimeInMillis() - date.getTime();
            long diffMinutes = diff / (60 * 1000);

            if (diffMinutes <= 0) {
                try {
                    Calendar dateTime = Calendar.getInstance(TimeZone.getDefault());
                    dateTime.setTime(date);
                    String format = Global.DATE_TIME_SEC_STR_FORMAT;
                    String datetimeAnswer = Formatter.formatDate(dateTime.getTime(), format);
                    List<QuestionSet> questionSets = QuestionSetDataAccess.getOneQuestionByTag(context, header.getUuid_scheme(),
                            header.getForm_version(), Global.TAG_SURVEY_DATE);
                    if(questionSets!=null && !questionSets.isEmpty()) {
                        for (int i=0; i<questionSets.size(); i++) {
                            QuestionSet questionSet = questionSets.get(i);
                            TaskD taskD = TaskDDataAccess.getOneByQuestionAndTaskH(context, questionSet.getQuestion_id(), header.getUuid_task_h());
                            if (taskD != null) {
                                String answer = taskD.getText_answer();
                                Date dateAnswer = null;
                                try {
                                    dateAnswer = Formatter.parseDate(answer, format);
                                    long diffDate = date.getTime() - dateAnswer.getTime();
                                    long diffDateOfMinutes = diffDate / (60 * 1000);
                                    if (diffDateOfMinutes != 0) {
                                        taskD.setText_answer(datetimeAnswer);
                                        TaskDDataAccess.update(activity, taskD);
                                    }
                                } catch (Exception e) {
                                    try {
                                        dateAnswer = Formatter.parseDate(answer, Global.DATE_TIME_STR_FORMAT);
                                        long diffDate = date.getTime() - dateAnswer.getTime();
                                        long diffDateOfMinutes = diffDate / (60 * 1000);
                                        if (diffDateOfMinutes != 0) {
                                            taskD.setText_answer(datetimeAnswer);
                                            TaskDDataAccess.update(activity, taskD);
                                        }
                                    } catch (Exception e2) {
                                        taskD.setText_answer(datetimeAnswer);
                                        TaskDDataAccess.update(activity, taskD);
                                        if (Global.IS_DEV) {
                                            e2.printStackTrace();
                                        }
                                    }
                                }
                            } else {
                                TaskD taskDNew = new TaskD();
                                String newId = Tool.getUUID();
                                taskDNew.setUuid_task_d(newId);
                                taskDNew.setQuestion_group_id(questionSet.getQuestion_group_id());
                                taskDNew.setQuestion_id(questionSet.getQuestion_id());
                                taskDNew.setText_answer(datetimeAnswer);
                                taskDNew.setUuid_task_h(header.getUuid_task_h());
                                taskDNew.setQuestion_label(questionSet.getQuestion_label());
                                TaskDDataAccess.add(activity, taskDNew);
                            }
                        }
                    }

                    JsonRequestPromiseSurvey jsonRequestPromiseDate = new JsonRequestPromiseSurvey();
                    jsonRequestPromiseDate.setuuid_task_h(header.getUuid_task_h());
                    jsonRequestPromiseDate.setPts_date(date);
                    jsonRequestPromiseDate.setAudit(GlobalData.getSharedGlobalData().getAuditData());

                    new SubmitPromiseSurvey(getActivity(), jsonRequestPromiseDate).execute();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                Toast.makeText(getContext(), getString(R.string.msgInvalidHour), Toast.LENGTH_SHORT).show();
            }
        }

        @Override
        public void onDateTimeCancel() {

        }
    };

    public void showDateTimePicker() {
        Calendar calendar = Calendar.getInstance();
        Date dateTime = calendar.getTime();
        TaskD taskD = TaskDDataAccess.getOneFromTaskDWithTag(context, header.getUuid_task_h(), Global.TAG_SURVEY_DATE, header.getForm_version());
        if (taskD != null) {
            String answer = taskD.getText_answer();
            if (answer == null || answer.equals("")) {
                Calendar cal = Calendar.getInstance();
                cal.setTime(dateTime);
                dateTime = cal.getTime();
            } else {
                Date dateAnswer = new Date();
                if (answer.contains("/")) {
                    try {
                        dateAnswer = Formatter.parseDate(answer, Global.DATE_TIME_SEC_STR_FORMAT);
                    } catch (ParseException e) {
                        FireCrash.log(e);
                        e.printStackTrace();
                    }
                } else {
                    dateAnswer = new Date(Formatter.stringToDate(answer));
                }

                if (dateAnswer.before(dateTime)) {
                    Calendar cal = Calendar.getInstance();
                    cal.setTime(dateTime);
                    dateTime = cal.getTime();
                } else if (dateAnswer.after(dateTime)) {
                    Calendar cal = Calendar.getInstance();
                    cal.setTime(dateAnswer);
                    dateAnswer = cal.getTime();
                    dateTime = dateAnswer;
                }
            }
        }

        SlideDateTimePicker timePicker = new SlideDateTimePicker.Builder(getChildFragmentManager())
                .setListener(listener)
                .setInitialDate(dateTime)
                .setMinDate(new Date())
                .setIs24HourTime(true)
                .setTheme(SlideDateTimePicker.HOLO_LIGHT)
                .build();
        timePicker.show();
    }

    private class SubmitData extends AsyncTask<String, Void, String> {

        private ProgressDialog progressDialog;

        @Override
        protected void onPreExecute() {
            progressDialog = ProgressDialog.show(getActivity(),
                    "", getString(R.string.progressWait), true);
        }

        @Override
        protected String doInBackground(String... strings) {
            String result;
            if (Tool.isInternetconnected(getActivity().getApplicationContext())) {
                result = requestTaskUpdate(submitUrl);
            } else {
                result = getString(R.string.no_internet_connection);
            }
            return result;
        }

        @Override
        protected void onPostExecute(String result) {

            if (null != progressDialog && progressDialog.isShowing()) {
                try {
                    progressDialog.dismiss();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            if (null != result) {
                if (getString(R.string.no_internet_connection).equals(result)) {
                    NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(getActivity());
                    builder.withTitle(getString(R.string.info_capital))
                            .withMessage(getString(R.string.no_internet_connection))
                            .show();
                } else {
                    getServerResponse(result);
                    Logger.i("INFO", "OK");
                }
            }
        }
    }

    private String requestTaskUpdate(String submitUrl) {
        String result = null;
        if (Tool.isInternetconnected(context)) {
            SubmitTaskUpdateRequest request = new SubmitTaskUpdateRequest();
            request.setAudit(GlobalData.getSharedGlobalData().getAuditData());
            request.addImeiAndroidIdToUnstructured();
            request.setUuidTaskUpdate(header.getUuid_task_update());
            request.setFeedbackNotes(txtFeedbackNotes.getText().toString());
            if (null != byteDocument || null != bmDocument) {
                request.setUploadDocument(String.valueOf(Base64.encode(byteDocument)));
            }

            HttpCryptedConnection httpConn = new HttpCryptedConnection(context,
                    GlobalData.getSharedGlobalData().isEncrypt(), GlobalData.getSharedGlobalData().isDecrypt());
            HttpConnectionResult serverResult;

            try {
                serverResult = httpConn.requestToServer(submitUrl, GsonHelper.toJson(request), Global.DEFAULTCONNECTIONTIMEOUT);
                if (serverResult != null && serverResult.isOK()) {
                    try {
                        result = serverResult.getResult();
                    } catch (Exception e) {
                        FireCrash.log(e);
                        e.printStackTrace();
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return result;
    }

    private void getServerResponse(String result) {
        Logger.i("INFO", result);
        if (null != result) {
            try {
                SubmitTaskUpdateResponse response = GsonHelper.fromJson(result, SubmitTaskUpdateResponse.class);
                int code = response.getStatus().getCode();
                if (code == 0) {
                    try {
                        final NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(getActivity());
                        builder.withTitle(getString(R.string.success))
                                .withMessage(getString(R.string.task_update_success))
                                .withButton1Text(getString(R.string.btnOk))
                                .setButton1Click(new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        Fragment fragment = new TaskUpdateActivity();
                                        FragmentTransaction transaction = MainMenuActivity.fragmentManager.beginTransaction();
                                        transaction.setCustomAnimations(R.anim.activity_open_translate, R.anim.activity_close_scale, R.anim.activity_open_scale, R.anim.activity_close_translate);
                                        transaction.replace(R.id.content_frame, fragment);
                                        transaction.commit();
                                        builder.dismiss();
                                    }
                                })
                                .isCancelable(true)
                                .show();

                    } catch (Exception e) {
                        FireCrash.log(e);
                    }
                } else {
                    final NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(getActivity());
                    builder.withTitle(getString(R.string.error_capital))
                            .withMessage(getString(R.string.error_unknown))
                            .withButton1Text(getString(R.string.btnOk))
                            .setButton1Click(new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    Fragment fragment = new TaskUpdateActivity();
                                    FragmentTransaction transaction = MainMenuActivity.fragmentManager.beginTransaction();
                                    transaction.setCustomAnimations(R.anim.activity_open_translate, R.anim.activity_close_scale, R.anim.activity_open_scale, R.anim.activity_close_translate);
                                    transaction.replace(R.id.content_frame, fragment);
                                    transaction.commit();
                                    builder.dismiss();
                                }
                            })
                            .isCancelable(true)
                            .show();
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public class CheckStatusTask extends AsyncTask<String, Void, List<String>>{
        private ProgressDialog progressDialog;
        private Context activity;
        private String uuid_taskh;

        public CheckStatusTask(Context activity , String uuidTaskH){
            this.activity=activity;
            this.uuid_taskh=uuidTaskH;
        }
        @Override
        protected void onPreExecute() {
            this.progressDialog = ProgressDialog.show(activity, "", activity.getString(R.string.progressSend), true);
        }
        @SuppressLint("WrongThread")
        @Override
        protected List<String> doInBackground(String... params) {
            try {
                JsonRequestCheckStatusTask request = new JsonRequestCheckStatusTask();
                request.setAudit(GlobalData.getSharedGlobalData().getAuditData());
                request.setUuidTaskH(uuid_taskh);

                String json = GsonHelper.toJson(request);
                String url = GlobalData.getSharedGlobalData().getURL_CHECK_STATUS_TASK();
                boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                HttpCryptedConnection httpConn = new HttpCryptedConnection(getActivity(), encrypt, decrypt);
                HttpConnectionResult serverResult = null;
                try {
                    serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
                } catch (Exception e) {
                    if(Global.IS_DEV)
                        e.printStackTrace();
                }
                if (serverResult != null && serverResult.isOK()) {
                    try {
                        String result = serverResult.getResult();
                        JsonResponseStatusTask response = GsonHelper.fromJson(result, JsonResponseStatusTask.class);
                        if (response.getStatus().getCode() == 0 && !response.getCanStart().equalsIgnoreCase("1")) {
                            errMsg = response.getMessage();
                            btnStart.setEnabled(false);
                            btnPromise.setEnabled(false);
                        }

                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            return null;
        }
        @Override
        protected void onPostExecute(List<String> results) {
            if (progressDialog.isShowing()){
                try {
                    progressDialog.dismiss();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if(!errMsg.isEmpty()){
                final NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(activity);
                builder.withTitle("INFO").withMessage(errMsg)
                        .withButton1Text("Ok")
                        .isCancelable(false)
                        .setButton1Click(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                builder.dismiss();
                                getActivity().onBackPressed();
                            }
                        }).show();
            }

        }
    }

}