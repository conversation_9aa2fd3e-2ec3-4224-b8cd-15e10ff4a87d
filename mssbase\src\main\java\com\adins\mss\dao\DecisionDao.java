package com.adins.mss.dao;

import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;

import com.adins.mss.dao.Decision;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "MS_TMP_RULE".
*/
public class DecisionDao extends AbstractDao<Decision, Long> {

    public static final String TABLENAME = "MS_TMP_RULE";

    /**
     * Properties of entity Decision.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Id = new Property(0, long.class, "id", true, "ID");
        public final static Property Code = new Property(1, String.class, "code", false, "CODE");
        public final static Property Value = new Property(2, String.class, "value", false, "VALUE");
        public final static Property Column_no = new Property(3, Integer.class, "column_no", false, "COLUMN_NO");
        public final static Property Row_no = new Property(4, Integer.class, "row_no", false, "ROW_NO");
        public final static Property Obj_name = new Property(5, String.class, "obj_name", false, "OBJ_NAME");
    };


    public DecisionDao(DaoConfig config) {
        super(config);
    }
    
    public DecisionDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"MS_TMP_RULE\" (" + //
                "\"ID\" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL ," + // 0: id
                "\"CODE\" TEXT," + // 1: code
                "\"VALUE\" TEXT," + // 2: value
                "\"COLUMN_NO\" INTEGER," + // 3: column_no
                "\"ROW_NO\" INTEGER," + // 4: row_no
                "\"OBJ_NAME\" TEXT);"); // 5: obj_name
        // Add Indexes
        db.execSQL("CREATE INDEX " + constraint + "IDX_MS_TMP_RULE_CODE_COLUMN_NO_VALUE ON MS_TMP_RULE" +
                " (\"CODE\",\"COLUMN_NO\",\"VALUE\");");
        db.execSQL("CREATE INDEX " + constraint + "IDX_MS_TMP_RULE_CODE_VALUE ON MS_TMP_RULE" +
                " (\"CODE\",\"VALUE\");");
        db.execSQL("CREATE INDEX " + constraint + "IDX_MS_TMP_RULE_ROW_NO ON MS_TMP_RULE" +
                " (\"ROW_NO\");");
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"MS_TMP_RULE\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, Decision entity) {
        stmt.clearBindings();
        stmt.bindLong(1, entity.getId());
 
        String code = entity.getCode();
        if (code != null) {
            stmt.bindString(2, code);
        }
 
        String value = entity.getValue();
        if (value != null) {
            stmt.bindString(3, value);
        }
 
        Integer column_no = entity.getColumn_no();
        if (column_no != null) {
            stmt.bindLong(4, column_no);
        }
 
        Integer row_no = entity.getRow_no();
        if (row_no != null) {
            stmt.bindLong(5, row_no);
        }
 
        String obj_name = entity.getObj_name();
        if (obj_name != null) {
            stmt.bindString(6, obj_name);
        }
    }

    /** @inheritdoc */
    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.getLong(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public Decision readEntity(Cursor cursor, int offset) {
        Decision entity = new Decision( //
            cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // code
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // value
            cursor.isNull(offset + 3) ? null : cursor.getInt(offset + 3), // column_no
            cursor.isNull(offset + 4) ? null : cursor.getInt(offset + 4), // row_no
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5) // obj_name
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, Decision entity, int offset) {
        entity.setId(cursor.getLong(offset + 0));
        entity.setCode(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setValue(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setColumn_no(cursor.isNull(offset + 3) ? null : cursor.getInt(offset + 3));
        entity.setRow_no(cursor.isNull(offset + 4) ? null : cursor.getInt(offset + 4));
        entity.setObj_name(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
     }
    
    /** @inheritdoc */
    @Override
    protected Long updateKeyAfterInsert(Decision entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    /** @inheritdoc */
    @Override
    public Long getKey(Decision entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
}
