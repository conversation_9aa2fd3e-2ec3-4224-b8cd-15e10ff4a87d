package com.adins.mss.foundation.print.rv;

import com.adins.mss.foundation.http.MssRequestType;
import com.google.gson.annotations.SerializedName;

/**
 * Created by angga.permadi on 4/20/2016.
 */
public class RVNumberRequest extends MssRequestType {
    @SerializedName("rvBlank")
    private RVEntity rvBlank;

    public RVEntity getRvBlank() {
        return rvBlank;
    }

    public void setRvBlank(RVEntity rvBlank) {
        this.rvBlank = rvBlank;
    }

}
