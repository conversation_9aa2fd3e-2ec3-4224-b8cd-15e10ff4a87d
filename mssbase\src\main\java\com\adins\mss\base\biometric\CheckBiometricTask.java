package com.adins.mss.base.biometric;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Context;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Message;
import android.widget.Toast;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.dynamicform.Constant;
import com.adins.mss.base.dynamicform.form.FragmentQuestion;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.base.util.Utility;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.Scheme;
import com.adins.mss.foundation.db.dataaccess.SchemeDataAccess;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.questiongenerator.QuestionBean;
import com.google.firebase.perf.FirebasePerformance;
import com.google.firebase.perf.metrics.HttpMetric;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Map;

public class CheckBiometricTask extends AsyncTask<String, Void, String> {

    private ProgressDialog progressDialog;
    private WeakReference<Activity> activity;
    private Context context;
    private String message;
    private QuestionBean bean;
    private String uuidTaskH;

    public CheckBiometricTask(QuestionBean bean, String uuidTaskH, Context context, Activity activity) {
        this.bean = bean;
        this.uuidTaskH = uuidTaskH;
        this.context = context;
        this.activity = new WeakReference<>(activity);
    }

    @Override
    protected void onPreExecute() {
        progressDialog = ProgressDialog.show(activity.get(), "", activity.get().getString(R.string.please_wait), true);
        super.onPreExecute();
    }

    @Override
    protected String doInBackground(String... params) {
        String imgBase64 = params[0];
        if (Tool.isInternetconnected(activity.get())) {
           String jsonRequest = createJsonBiometricRequest(imgBase64);
            String url = GlobalData.getSharedGlobalData().getURL_CHECK_BIOMETRIC();
            boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
            boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
            HttpCryptedConnection httpConn = new HttpCryptedConnection(activity.get(), encrypt, decrypt);
            HttpConnectionResult serverResult = null;

            //Firebase Performance Trace HTTP Request
            HttpMetric networkMetric = FirebasePerformance.getInstance().newHttpMetric(url, FirebasePerformance.HttpMethod.POST);
            Utility.metricStart(networkMetric, jsonRequest);

            try {
                serverResult = httpConn.requestToServer(url, jsonRequest, Global.DEFAULTCONNECTIONTIMEOUT);
                Utility.metricStop(networkMetric, serverResult);
            } catch (Exception e) {
                FireCrash.log(e);
                message = activity.get().getString(R.string.msgConnectionFailed);
                return null;
            }
            if (serverResult != null && serverResult.isOK()) {
                return serverResult.getResult();
            } else {
                message = activity.get().getString(R.string.connection_failed);
            }
        } else {
            message = activity.get().getString(R.string.no_internet_connection);
        }
        return null;
    }

    @Override
    protected void onPostExecute(String jsonResponse) {
        super.onPostExecute(jsonResponse);
        JsonCheckBiometricResponse response = GsonHelper.fromJson(jsonResponse, JsonCheckBiometricResponse.class);
        if (response != null) {
            message = response.getStatus().getMessage();
            boolean isChange = processResponseBiometric(response);
            if (isChange) {
                Message messageChange = new android.os.Message();
                Bundle bundle = new Bundle();
                bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.RESULT_FROM_CHECK_BIOMETRIC);
                bundle.putBoolean("isChange", isChange);
                messageChange.setData(bundle);
                FragmentQuestion.questionHandler.sendMessage(messageChange);
            }
        }

        if (message != null && !"".equalsIgnoreCase(message)) {
            Toast.makeText(context,message, Toast.LENGTH_SHORT).show();
        }

        if (progressDialog != null && progressDialog.isShowing()) {
            progressDialog.dismiss();
        }
    }

    private String createJsonBiometricRequest(String imgBase64) {
        JsonCheckBiometricRequest request = new JsonCheckBiometricRequest();
        request.setAudit(GlobalData.getSharedGlobalData().getAuditData());
        request.setImage(imgBase64);

        Scheme form = SchemeDataAccess.getOne(activity.get(), bean.getUuid_scheme());
        request.setFormName(form.getScheme_description());
        request.setUuidTaskH(uuidTaskH);

        String choiceFilter = bean.getChoice_filter();
        choiceFilter = choiceFilter.replace("{", "");
        choiceFilter = choiceFilter.replace("}", "");
        Map<String, String> mapFilter = new HashMap<>();
        if (choiceFilter != null && !"".equalsIgnoreCase(choiceFilter)) {
            String [] identifierFilters = choiceFilter.split(",");
            for (int i=0; i<identifierFilters.length; i++) {
                String identifier = identifierFilters[i];
                QuestionBean beanFilter = Constant.listOfQuestion.get(identifier);
                String answer = beanFilter == null? null: QuestionBean.getAnswer(beanFilter);
                if (answer == null) {
                    answer = "";
                }
                mapFilter.put(identifier, answer);
            }
            request.setFilter(mapFilter);
        }
        return GsonHelper.toJson(request);
    }

    private boolean processResponseBiometric(JsonCheckBiometricResponse response) {
        boolean isChange = false;
        if (response.getObjectHashMap()!=null && response.getObjectHashMap().size()>0) {
            for (Map.Entry<String, Object> mapElement : response.getObjectHashMap().entrySet()) {
                String refId = mapElement.getKey();
                String answer = mapElement.getValue().toString();
                QuestionBean beanProcess = Constant.listOfQuestion.get(refId);
                if (beanProcess != null && beanProcess.getAnswer() != null && answer != null && !"".equalsIgnoreCase(answer)) {
                    if (!beanProcess.getAnswer().equalsIgnoreCase(answer)) {
                        beanProcess.setIntTextAnswer(answer);
                        beanProcess.setAnswer(answer);
                        isChange = true;
                    }
                } else if (beanProcess != null && (beanProcess.getAnswer() == null || "".equalsIgnoreCase(beanProcess.getAnswer()))) {
                    beanProcess.setIntTextAnswer(answer);
                    beanProcess.setAnswer(answer);
                    isChange = true;
                }

            }
        }
        return isChange;
    }
}
