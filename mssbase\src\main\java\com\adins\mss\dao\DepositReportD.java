package com.adins.mss.dao;

import com.adins.mss.dao.DaoSession;
import de.greenrobot.dao.DaoException;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "TR_DEPOSITREPORT_D".
 */
public class DepositReportD {

    /** Not-null value. */
     @SerializedName("uuid_deposit_report_d")
    private String uuid_deposit_report_d;
     @SerializedName("uuid_task_h")
    private String uuid_task_h;
     @SerializedName("deposit_amt")
    private String deposit_amt;
     @SerializedName("usr_crt")
    private String usr_crt;
     @SerializedName("dtm_crt")
    private java.util.Date dtm_crt;
     @SerializedName("uuid_deposit_report_h")
    private String uuid_deposit_report_h;
     @SerializedName("is_sent")
    private String is_sent;

    /** Used to resolve relations */
    private transient DaoSession daoSession;

    /** Used for active entity operations. */
    private transient DepositReportDDao myDao;

    private DepositReportH depositReportH;
    private String depositReportH__resolvedKey;


    public DepositReportD() {
    }

    public DepositReportD(String uuid_deposit_report_d) {
        this.uuid_deposit_report_d = uuid_deposit_report_d;
    }

    public DepositReportD(String uuid_deposit_report_d, String uuid_task_h, String deposit_amt, String usr_crt, java.util.Date dtm_crt, String uuid_deposit_report_h, String is_sent) {
        this.uuid_deposit_report_d = uuid_deposit_report_d;
        this.uuid_task_h = uuid_task_h;
        this.deposit_amt = deposit_amt;
        this.usr_crt = usr_crt;
        this.dtm_crt = dtm_crt;
        this.uuid_deposit_report_h = uuid_deposit_report_h;
        this.is_sent = is_sent;
    }

    /** called by internal mechanisms, do not call yourself. */
    public void __setDaoSession(DaoSession daoSession) {
        this.daoSession = daoSession;
        myDao = daoSession != null ? daoSession.getDepositReportDDao() : null;
    }

    /** Not-null value. */
    public String getUuid_deposit_report_d() {
        return uuid_deposit_report_d;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_deposit_report_d(String uuid_deposit_report_d) {
        this.uuid_deposit_report_d = uuid_deposit_report_d;
    }

    public String getUuid_task_h() {
        return uuid_task_h;
    }

    public void setUuid_task_h(String uuid_task_h) {
        this.uuid_task_h = uuid_task_h;
    }

    public String getDeposit_amt() {
        return deposit_amt;
    }

    public void setDeposit_amt(String deposit_amt) {
        this.deposit_amt = deposit_amt;
    }

    public String getUsr_crt() {
        return usr_crt;
    }

    public void setUsr_crt(String usr_crt) {
        this.usr_crt = usr_crt;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public String getUuid_deposit_report_h() {
        return uuid_deposit_report_h;
    }

    public void setUuid_deposit_report_h(String uuid_deposit_report_h) {
        this.uuid_deposit_report_h = uuid_deposit_report_h;
    }

    public String getIs_sent() {
        return is_sent;
    }

    public void setIs_sent(String is_sent) {
        this.is_sent = is_sent;
    }

    /** To-one relationship, resolved on first access. */
    public DepositReportH getDepositReportH() {
        String __key = this.uuid_deposit_report_h;
        if (depositReportH__resolvedKey == null || depositReportH__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            DepositReportHDao targetDao = daoSession.getDepositReportHDao();
            DepositReportH depositReportHNew = targetDao.load(__key);
            synchronized (this) {
                depositReportH = depositReportHNew;
            	depositReportH__resolvedKey = __key;
            }
        }
        return depositReportH;
    }

    public void setDepositReportH(DepositReportH depositReportH) {
        synchronized (this) {
            this.depositReportH = depositReportH;
            uuid_deposit_report_h = depositReportH == null ? null : depositReportH.getUuid_deposit_report_h();
            depositReportH__resolvedKey = uuid_deposit_report_h;
        }
    }

    /** Convenient call for {@link AbstractDao#delete(Object)}. Entity must attached to an entity context. */
    public void delete() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.delete(this);
    }

    /** Convenient call for {@link AbstractDao#update(Object)}. Entity must attached to an entity context. */
    public void update() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.update(this);
    }

    /** Convenient call for {@link AbstractDao#refresh(Object)}. Entity must attached to an entity context. */
    public void refresh() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.refresh(this);
    }

}
