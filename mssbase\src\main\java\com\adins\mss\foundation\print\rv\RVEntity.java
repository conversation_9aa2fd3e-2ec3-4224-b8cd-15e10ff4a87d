package com.adins.mss.foundation.print.rv;

import com.google.gson.annotations.SerializedName;

import java.util.Date;

/**
 * Created by angga.permadi on 4/20/2016.
 */
public class RVEntity {
    @SerializedName("uuid_task_h")
    private String uuid_task_h;
    @SerializedName("rv_number")
    private String rv_number;
    @SerializedName("dtm_use")
    private Date dtm_use;

    public String getUuid_task_h() {
        return uuid_task_h;
    }

    public void setUuid_task_h(String uuid_task_h) {
        this.uuid_task_h = uuid_task_h;
    }

    public String getRv_number() {
        return rv_number;
    }

    public void setRv_number(String rv_number) {
        this.rv_number = rv_number;
    }

    public Date getDtm_use() {
        return dtm_use;
    }

    public void setDtm_use(Date dtm_use) {
        this.dtm_use = dtm_use;
    }
}
