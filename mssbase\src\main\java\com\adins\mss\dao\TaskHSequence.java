package com.adins.mss.dao;

import com.adins.mss.dao.DaoSession;
import de.greenrobot.dao.DaoException;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "TR_TASK_H_SEQUENCE".
 */
public class TaskHSequence {

     @SerializedName("sequence")
    private int sequence;
     @SerializedName("uuid_task_h")
    private String uuid_task_h;

    /** Used to resolve relations */
    private transient DaoSession daoSession;

    /** Used for active entity operations. */
    private transient TaskHSequenceDao myDao;

    private TaskH taskH;
    private String taskH__resolvedKey;


    public TaskHSequence() {
    }

    public TaskHSequence(int sequence, String uuid_task_h) {
        this.sequence = sequence;
        this.uuid_task_h = uuid_task_h;
    }

    /** called by internal mechanisms, do not call yourself. */
    public void __setDaoSession(DaoSession daoSession) {
        this.daoSession = daoSession;
        myDao = daoSession != null ? daoSession.getTaskHSequenceDao() : null;
    }

    public int getSequence() {
        return sequence;
    }

    public void setSequence(int sequence) {
        this.sequence = sequence;
    }

    public String getUuid_task_h() {
        return uuid_task_h;
    }

    public void setUuid_task_h(String uuid_task_h) {
        this.uuid_task_h = uuid_task_h;
    }

    /** To-one relationship, resolved on first access. */
    public TaskH getTaskH() {
        String __key = this.uuid_task_h;
        if (taskH__resolvedKey == null || taskH__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            TaskHDao targetDao = daoSession.getTaskHDao();
            TaskH taskHNew = targetDao.load(__key);
            synchronized (this) {
                taskH = taskHNew;
            	taskH__resolvedKey = __key;
            }
        }
        return taskH;
    }

    public void setTaskH(TaskH taskH) {
        synchronized (this) {
            this.taskH = taskH;
            uuid_task_h = taskH == null ? null : taskH.getUuid_task_h();
            taskH__resolvedKey = uuid_task_h;
        }
    }

    /** Convenient call for {@link AbstractDao#delete(Object)}. Entity must attached to an entity context. */
    public void delete() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.delete(this);
    }

    /** Convenient call for {@link AbstractDao#update(Object)}. Entity must attached to an entity context. */
    public void update() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.update(this);
    }

    /** Convenient call for {@link AbstractDao#refresh(Object)}. Entity must attached to an entity context. */
    public void refresh() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.refresh(this);
    }

}
