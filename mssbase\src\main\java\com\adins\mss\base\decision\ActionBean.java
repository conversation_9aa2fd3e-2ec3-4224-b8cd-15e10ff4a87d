package com.adins.mss.base.decision;

import java.io.Serializable;

public class ActionBean implements Serializable {
	private String objName;
	private String objFieldMethod;
	
	public ActionBean() {}
	
	public ActionBean(String objName, String objFieldMethod) {
		super();
		this.objName = objName;
		this.objFieldMethod = objFieldMethod;
	}

	public String getObjName() {
		return objName;
	}

	public void setObjName(String objName) {
		this.objName = objName;
	}

	public String getObjFieldMethod() {
		return objFieldMethod;
	}

	public void setObjFieldMethod(String objFieldMethod) {
		this.objFieldMethod = objFieldMethod;
	}
	
	public String toString() {
		StringBuilder sb = new StringBuilder();
		sb.append(objName).append(".").append(objFieldMethod);
		return sb.toString();
	}
}
