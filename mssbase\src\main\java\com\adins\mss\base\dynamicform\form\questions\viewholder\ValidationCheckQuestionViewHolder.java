package com.adins.mss.base.dynamicform.form.questions.viewholder;

import android.app.ProgressDialog;
import android.os.AsyncTask;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.dynamicform.Constant;
import com.adins.mss.base.dynamicform.DynamicFormActivity;
import com.adins.mss.base.dynamicform.form.FragmentQuestion;
import com.adins.mss.base.dynamicform.form.models.validationcheck.ValidationCheckBean;
import com.adins.mss.base.dynamicform.form.models.validationcheck.ValidationCheckRequest;
import com.adins.mss.base.dynamicform.form.models.validationcheck.ValidationCheckResponse;
import com.adins.mss.base.timeline.TimelineManager;
import com.adins.mss.base.util.EventBusHelper;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.LocationInfo;
import com.adins.mss.dao.Lookup;
import com.adins.mss.dao.Message;
import com.adins.mss.dao.TaskD;
import com.adins.mss.dao.TaskH;
import com.adins.mss.foundation.db.dataaccess.LookupDataAccess;
import com.adins.mss.foundation.db.dataaccess.MessageDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskHDataAccess;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.questiongenerator.OptionAnswerBean;
import com.adins.mss.foundation.questiongenerator.QuestionBean;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class ValidationCheckQuestionViewHolder extends RecyclerView.ViewHolder {

    private final FragmentActivity mActivity;
    private final TextView mQuestionLabel;
    private final TextView mQuestionAnswer;
    private final Button btnValidationCheck;
    private final TaskH taskH;
    private final LinkedHashMap<String, List<QuestionBean>> mapQuestionBeans;
    private final List<QuestionBean> listQuestionBeans;
    private Map<String, QuestionBean> listValidationCheckQuestion;
    private QuestionBean bean;
    private Map<String, Map<String, String>> filters;
    private String errorMessage;

    public ValidationCheckQuestionViewHolder(View view, FragmentActivity mActivity, LinkedHashMap<String, List<QuestionBean>> mapQuestionBeans, List<QuestionBean> listQuestionBeans) {
        super(view);
        this.mActivity = mActivity;
        this.mapQuestionBeans = mapQuestionBeans;
        this.listQuestionBeans = listQuestionBeans;
        this.taskH = DynamicFormActivity.header.getTaskH();

        this.mQuestionLabel = view.findViewById(R.id.questionValidationLabel);
        this.mQuestionAnswer = view.findViewById(R.id.questionValidationAnswer);
        this.btnValidationCheck = view.findViewById(R.id.btnValidationCheck);
    }

    public void bind(final QuestionBean item, int number) {
        bean = item;
        String qLabel = number + ". " + bean.getQuestion_label();
        mQuestionLabel.setText(qLabel);
        String qAnswer = bean.getAnswer();
        showAnswer(qAnswer);
        onClickHandler();

        listValidationCheckQuestion = new HashMap<>();
        filters = new HashMap<>();

        for (QuestionBean bean : listQuestionBeans) {
            // Create List Question Bean Check Validation Button Send Each Layer
            if (Global.AT_VALIDATION_CHECK.equals(bean.getAnswer_type())) {
                listValidationCheckQuestion.put(bean.getQuestion_group_id(), bean);
            }
        }

        if (bean.getCountRetry() >= bean.getMaxRetry()) {
            btnValidationCheck.setEnabled(false);
            btnValidationCheck.setText("Maximum retry has been reached");
        } else {
            btnValidationCheck.setEnabled(true);
            btnValidationCheck.setText(mActivity.getString(R.string.btnValidationCheck));
        }
    }

    private void showAnswer(String qAnswer) {
        if (null != qAnswer && !qAnswer.isEmpty()) {
            if (null == bean.getResponseValidationCheck()) {
                mQuestionAnswer.setText(qAnswer);
                mQuestionAnswer.setVisibility(View.VISIBLE);
            }
        } else {
            mQuestionAnswer.setVisibility(View.GONE);
        }
    }

    private void onClickHandler() {
        btnValidationCheck.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                bean.setCountRetry(bean.getCountRetry() + 1);
                ValidationCheckRequest validationCheckRequest = createRequest();

                ValidationCheckTask task = new ValidationCheckTask(mActivity, validationCheckRequest);
                task.execute();

                if (bean.getCountRetry() >= bean.getMaxRetry()) {
                    btnValidationCheck.setEnabled(false);
                    btnValidationCheck.setText("Maximum retry has been reached");
                } else {
                    btnValidationCheck.setEnabled(true);
                    btnValidationCheck.setText(mActivity.getString(R.string.btnValidationCheck));
                }
            }
        });
    }

    private ValidationCheckRequest createRequest() {
        List<ValidationCheckBean> validationCheckList = new ArrayList<>();

        for (String uuidQuestionGroup : listValidationCheckQuestion.keySet()) {
            QuestionBean cvQuestion = listValidationCheckQuestion.get(uuidQuestionGroup);

            List<TaskD> listTaskD = convertTaskD(mapQuestionBeans.get(cvQuestion.getQuestion_group_name()));
            List<TaskD> excludeTaskDs = new ArrayList<>();
            for (TaskD taskD2 : listTaskD) {
                if (null != taskD2.getText_answer() && "Pilih Salah Satu".equalsIgnoreCase(taskD2.getText_answer())) {
                    excludeTaskDs.add(taskD2);
                }
            }

            for (TaskD excludeTask : excludeTaskDs) {
                listTaskD.remove(excludeTask);
            }

            ValidationCheckBean validationCheckBean = new ValidationCheckBean();
            validationCheckBean.setSeqNoQuestion(String.valueOf(cvQuestion.getQuestion_group_order()));
            validationCheckBean.setListTaskD(listTaskD);
            validationCheckBean.setUuidQuestionGroup(uuidQuestionGroup);
            validationCheckList.add(validationCheckBean);

            Map<String, String> mapKeyValue = new HashMap<>();
            if (null != cvQuestion.getChoice_filter() && cvQuestion.getChoice_filter().length() > 0) {
                String[] refIdList = Tool.split(cvQuestion.getChoice_filter(), Global.DELIMETER_DATA3);
                if (null != refIdList && refIdList.length > 0) {
                    for (String identifier : refIdList) {
                        identifier = identifier.replace("{", "");
                        identifier = identifier.replace("}", "");

                        int idxOfOpenAbs = identifier.indexOf("$");
                        if (idxOfOpenAbs != -1) {
                            String finalIdentifier = identifier.substring(idxOfOpenAbs + 1);
                            if (finalIdentifier.equals(Global.IDF_SOURCE_DATA)) {
                                String sourceData = DynamicFormActivity.header.getSource_data();
                                mapKeyValue.put(identifier, sourceData);
                            }
                        } else {
                            QuestionBean bean = Constant.listOfQuestion.get(identifier);
                            if (null != bean) {
                                if (bean.isVisible()) {
                                    String answer = QuestionBean.getAnswer(bean);
                                    if (null == answer) {
                                        answer = "";
                                    }
                                    mapKeyValue.put(bean.getIdentifier_name(), answer);
                                }
                            }
                        }
                    }
                }
            }

            filters.put(uuidQuestionGroup, mapKeyValue);
        }

        Collections.sort(validationCheckList, new Comparator<ValidationCheckBean>() {
            @Override
            public int compare(ValidationCheckBean o1, ValidationCheckBean o2) {
                return (Integer.parseInt(o1.getSeqNoQuestion()) - Integer.parseInt(o2.getSeqNoQuestion()));
            }
        });

        ValidationCheckRequest request = new ValidationCheckRequest();
        request.setCountRetry(String.valueOf(bean.getCountRetry()));
        request.setFilter(filters);
        request.setValidationCheckBeanList(validationCheckList);
        request.setTaskH(taskH);

        return request;
    }

    private List<TaskD> convertTaskD(List<QuestionBean> beans) {
        String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        Date dtmCrt = new Date(System.currentTimeMillis());
        List<TaskD> listTaskD = new ArrayList<>();

        for (QuestionBean beanDetail : beans) {
            if (Tool.isOptions(beanDetail.getAnswer_type())) {
                listTaskD.addAll(listTaskDLov(beanDetail, uuidUser, dtmCrt));
            } else {
                listTaskD.add(listTaskD(beanDetail, uuidUser, dtmCrt));
            }
        }
        return listTaskD;
    }

    private List<TaskD> listTaskDLov(QuestionBean beanDetail, String uuidUser, Date dtmCrt) {
        List<TaskD> listTaskDs = new ArrayList<>();
        try {
            List<OptionAnswerBean> optAnsBean = beanDetail.getSelectedOptionAnswers();
            String answer = beanDetail.getAnswer();
            String[] finalAnswer = new String[1];

            if (null != answer && answer.length() > 0) {
                finalAnswer = Tool.split(answer, Global.DELIMETER_DATA);
            } else {
                finalAnswer[0] = "";
            }

            int j = 0;
            if (optAnsBean.isEmpty()) {
                OptionAnswerBean opt = new OptionAnswerBean("", "");
                optAnsBean.add(opt);
            }

            for (OptionAnswerBean selectedOption : optAnsBean) {
                TaskD taskD = new TaskD(Tool.getUUID());
                taskD.setQuestion_group_id(beanDetail.getQuestion_group_id());
                taskD.setQuestion_id(beanDetail.getQuestion_id());

                taskD.setImage(beanDetail.getImgAnswer());
                taskD.setLov(beanDetail.getLovId());
                taskD.setUsr_crt(uuidUser);
                taskD.setDtm_crt(dtmCrt);
                taskD.setUuid_task_h(taskH.getUuid_task_h());
                taskD.setQuestion_label(beanDetail.getQuestion_label());
                taskD.setIs_visible(Global.TRUE_STRING);
                taskD.setRegex(beanDetail.getRegex());
                taskD.setIs_readonly(beanDetail.getIs_readonly());

                String lookUpId = selectedOption.getUuid_lookup();
                String lovCode = selectedOption.getCode();
                String lovGroup = selectedOption.getLov_group();

                boolean isDbSource = false;
                if (beanDetail.getAnswer_type().equalsIgnoreCase(Global.AT_LOOKUP_TABLE) ||
                        beanDetail.getAnswer_type().equalsIgnoreCase(Global.AT_TEXT_WITH_SUGGESTION_NEW)) {
                    isDbSource = true;
                }

                if (null != lookUpId && null != lovCode && !isDbSource) {
                    Lookup lookup = LookupDataAccess.getOne(mActivity, lookUpId, lovGroup);
                    OptionAnswerBean selectedOption2 = new OptionAnswerBean(lookup);
                    selectedOption2.setSelected(true);
                    if (beanDetail.getTag() != null && beanDetail.getTag().equalsIgnoreCase("Job MH")) {
                        taskD.setOption_answer_id(selectedOption2.getCode());
                        taskD.setTag(bean.getTag());
                    } else {
                        taskD.setOption_answer_id(selectedOption2.getUuid_lookup());
                    }
                    taskD.setUuid_lookup(selectedOption2.getUuid_lookup());
                } else {
                    taskD.setText_answer(selectedOption.getValue());
                    taskD.setUuid_lookup(selectedOption.getOption_id());
                }
                taskD.setLov(lovCode);

                if (Tool.isOptionsWithDescription(bean.getAnswer_type())) {
                    taskD.setText_answer(finalAnswer[j]);
                } else if (Global.AT_MULTIPLE_ONE_DESCRIPTION.equals(bean.getAnswer_type()) ||
                        Global.AT_DROPDOWN_W_DESCRIPTION.equals(bean.getAnswer_type()) ||
                        Global.AT_TEXT_WITH_SUGGESTION.equals(bean.getAnswer_type()) ||
                        Global.AT_TEXT_WITH_SUGGESTION_NEW.equals(bean.getAnswer_type()))
                    taskD.setText_answer(bean.getAnswer());
                listTaskDs.add(taskD);
                j++;
            }
        } catch (Exception e) {
            FireCrash.log(e);
        }
        return listTaskDs;
    }

    private TaskD listTaskD(QuestionBean beanDetail, String uuidUser, Date dtmCrt) {
        TaskD taskD = new TaskD(Tool.getUUID());
        taskD.setQuestion_group_id(beanDetail.getQuestion_group_id());
        taskD.setQuestion_id(beanDetail.getQuestion_id());
        taskD.setText_answer(beanDetail.getAnswer());
        taskD.setImage(beanDetail.getImgAnswer());

        taskD.setLov(beanDetail.getLovId());
        taskD.setUsr_crt(uuidUser);
        taskD.setDtm_crt(dtmCrt);
        taskD.setUuid_task_h(taskH.getUuid_task_h());
        taskD.setQuestion_label(beanDetail.getQuestion_label());

        if (beanDetail.isVisible()) {
            taskD.setIs_visible(Global.TRUE_STRING);
        } else {
            taskD.setIs_visible(Global.FALSE_STRING);
        }

        if (beanDetail.getAnswer_type().equals(Global.AT_GPS) ||
                beanDetail.getAnswer_type().equals(Global.AT_GPS_N_LBS) ||
                beanDetail.getAnswer_type().equals(Global.AT_LOCATION) ||
                beanDetail.getAnswer_type().equals(Global.AT_IMAGE_W_GPS_ONLY) ||
                beanDetail.getAnswer_type().equals(Global.AT_IMAGE_W_LOCATION)) {
            try {
                LocationInfo info = beanDetail.getLocationInfo();
                taskD.setLatitude(info.getLatitude());
                taskD.setLongitude(info.getLongitude());
                taskD.setCid(info.getCid());
                taskD.setMcc(info.getMcc());
                taskD.setMnc(info.getMnc());
                taskD.setLac(info.getLac());
                taskD.setAccuracy(info.getAccuracy());
                taskD.setGps_time(info.getGps_time());
                taskD.setLocation_image(beanDetail.getImgLocation());
            } catch (Exception e) {
                FireCrash.log(e);
            }
        }
        taskD.setRegex(beanDetail.getRegex());
        taskD.setIs_readonly(beanDetail.getIs_readonly());
        return taskD;
    }

    private class ValidationCheckTask extends AsyncTask<Void, Void, String> {
        private ProgressDialog dialog;
        private final FragmentActivity activity;
        private final ValidationCheckRequest requestBean;
        private final String TAG_FAILED = "FAILED";
        private String TAG_ERROR = "ERROR";
        private String errMsg;

        public ValidationCheckTask(FragmentActivity activity, ValidationCheckRequest requestBean) {
            this.activity = activity;
            this.requestBean = requestBean;
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            dialog = ProgressDialog.show(activity, "", activity.getString(R.string.progressWait), true, false);
        }

        @Override
        protected String doInBackground(Void... voids) {
            try {
                if (Tool.isInternetconnected(activity)) {
                    HttpConnectionResult serverResult;
                    try {
                        requestBean.setAudit(GlobalData.getSharedGlobalData().getAuditData());
                        serverResult = request();
                    } catch (Exception e) {
                        FireCrash.log(e);
                        errMsg = e.getMessage();
                        return errMsg;
                    }

                    if (null != serverResult) {
                        if (serverResult.isOK()) {
                            try {
                                return serverResult.getResult();
                            } catch (Exception e) {
                                FireCrash.log(e);
                                errMsg = e.getMessage();
                                return errMsg;
                            }
                        } else {
                            errMsg = serverResult.getResult();
                            return errMsg;
                        }
                    }
                } else {
                    errMsg = activity.getString(R.string.no_internet_connection);
                    return errMsg;
                }
            } catch (Exception exception) {
                exception.printStackTrace();
            }

            return null;
        }

        @Override
        protected void onPostExecute(String results) {
            super.onPostExecute(results);
            dismissDialog(dialog);
            if (mActivity.getString(R.string.no_internet_connection).equals(results)) {
                Toast.makeText(mActivity, mActivity.getString(R.string.no_internet_connection), Toast.LENGTH_SHORT).show();
                errMsg = TAG_ERROR;
            }
            if (null == errMsg) {
                ValidationCheckResponse response = GsonHelper.fromJson(results, ValidationCheckResponse.class);
                processResponse(response);
            } else {
                bean.setAnswer(errMsg);
                bean.setIntTextAnswer(errMsg);
                mQuestionAnswer.setText(errMsg);
                mQuestionAnswer.setVisibility(View.VISIBLE);
            }
        }

        private HttpConnectionResult request() {
            String json = GsonHelper.toJson(requestBean);
            String url = GlobalData.getSharedGlobalData().getURL_VALIDATION_CHECK_QUESTION();
            boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
            boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
            HttpCryptedConnection httpConn = new HttpCryptedConnection(mActivity, encrypt, decrypt);
            HttpConnectionResult httpConnectionResult = null;

            try {
                httpConnectionResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
            } catch (Exception e) {
                FireCrash.log(e);
            }
            return httpConnectionResult;
        }

        private void dismissDialog(ProgressDialog dialog) {
            if (dialog.isShowing()) {
                dialog.dismiss();
            }
        }

        private void processResponse(ValidationCheckResponse response) {
            if (response.getStatus().getCode() != 0) {
                if (null != response.getListQuestionsIsMandatory()) {
                    bean.setResponseValidationCheck(response);
                    bean.setAnswer(response.getStatus().getMessage());
                } else {
                    bean.setResponseValidationCheck(null);
                    bean.setAnswer(response.getStatus().getMessage());
                    mQuestionAnswer.setText(response.getStatus().getMessage());
                    mQuestionAnswer.setVisibility(View.VISIBLE);
                }
            } else if (response.getStatus().getMessage().toLowerCase().contains(TAG_FAILED.toLowerCase())) {
                Message message = new Message();
                message.setUuid_message(Tool.getUUID());
                message.setDtm_crt(Tool.getSystemDate());
                message.setMessage(taskH.getTask_id() + "Telah dihapus karena \n" + response.getStatus().getMessage());
                MessageDataAccess.add(mActivity, message);
                TimelineManager.insertTimeline(activity, message);
            } else {
                if (null != response.getResult()) {
                    bean.setResponseValidationCheck(null);
                    processResult(response.getResult());
                } else {
                    bean.setResponseValidationCheck(null);
                    bean.setAnswer(response.getStatus().getMessage());
                    mQuestionAnswer.setText(response.getStatus().getMessage());
                    mQuestionAnswer.setVisibility(View.VISIBLE);
                }
            }
        }

        private void processResult(HashMap<String, Object> result) {
            StringBuilder message = new StringBuilder();

            for (Map.Entry<String, Object> data : result.entrySet()) {
                String refId = data.getKey();
                String answer = data.getValue().toString();
                setAnswer(refId, answer);

                if (answer.toLowerCase().contains(TAG_FAILED.toLowerCase())) {
                    if (bean.getCountRetry() >= bean.getMaxRetry()) {
                        message.append("Drop Task ");
                        message.append(taskH.getAppl_no());
                        message.append(" dikarenakan oleh ");
                        QuestionBean bean = Constant.listOfQuestion.get(refId);
                        message.append(bean.getQuestion_label());
                        message.append(" - ");
                        message.append(answer);
                    } else {
                        int count = bean.getMaxRetry() - bean.getCountRetry();
                        message.append("Task ");
                        message.append(taskH.getAppl_no());
                        message.append(" - ");
                        message.append(answer);
                        message.append(". Silahkan coba lagi, anda masih mempunyai kesempatan " + count + " kali");
                    }
                }
            }

            // Drop Task
            if (null != message.toString() && message.toString().length() > 0) {
                if (bean.getCountRetry() >= bean.getMaxRetry()) {
                    taskH.setMessage(message.toString());
                    taskH.setStatus(TaskHDataAccess.STATUS_SEND_DROP);
                    TaskHDataAccess.update(mActivity, taskH);
                    TimelineManager.insertTimeline(mActivity, taskH);
                } else {
                    android.os.Message messageIsChange = new android.os.Message();
                    Bundle bundle = new Bundle();
                    bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.RESULT_FROM_CHECK_VALIDATION);
                    bundle.putBoolean("isChange", true);
                    messageIsChange.setData(bundle);
                    FragmentQuestion.questionHandler.sendMessage(messageIsChange);
                }
                errorMessage = message.toString();
                EventBusHelper.post(ValidationCheckQuestionViewHolder.this);
            } else {
                android.os.Message messageIsChange = new android.os.Message();
                Bundle bundle = new Bundle();
                bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.RESULT_FROM_CHECK_VALIDATION);
                bundle.putBoolean("isChange", true);
                messageIsChange.setData(bundle);
                FragmentQuestion.questionHandler.sendMessage(messageIsChange);
            }
        }

        private void setAnswer(String refId, String answer) {
            QuestionBean beans = Constant.listOfQuestion.get(refId);
            beans.setAnswer(answer);
            beans.setIntTextAnswer(answer);
            if (bean.getIdentifier_name().equalsIgnoreCase(beans.getIdentifier_name())) {
                mQuestionAnswer.setText(beans.getAnswer());
                mQuestionAnswer.setVisibility(View.VISIBLE);
            }
        }
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

}
