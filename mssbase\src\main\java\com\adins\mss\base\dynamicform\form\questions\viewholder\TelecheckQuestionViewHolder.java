package com.adins.mss.base.dynamicform.form.questions.viewholder;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Context;
import android.os.AsyncTask;
import android.os.CountDownTimer;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.recyclerview.widget.RecyclerView;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.dynamicform.form.models.TeleCheckRequest;
import com.adins.mss.base.dynamicform.form.models.TeleCheckResponse;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.GeneralParameter;
import com.adins.mss.foundation.db.dataaccess.GeneralParameterDataAccess;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.questiongenerator.QuestionBean;

public class TelecheckQuestionViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {

    private final Activity activity;
    private final EditText mPhoneEdt;
    private final TextView mQuestionLabel;
    private final TextView mAnswerLabel;
    private final Button btnGetResult;
    private final Button btnSubmitTele;
    private QuestionBean bean;
    private int maxRetry;

    public TelecheckQuestionViewHolder(View view, Activity context) {
        super(view);
        mQuestionLabel = (TextView) view.findViewById(R.id.questionTeleLabel);
        mAnswerLabel = (TextView) view.findViewById(R.id.answerTeleLabel);
        mPhoneEdt = (EditText) view.findViewById(R.id.edtTelecheck);
        btnGetResult =(Button) view.findViewById(R.id.btnGetResult);
        btnSubmitTele =(Button) view.findViewById(R.id.btnSubmitTele);
        activity = context;
    }

    public void bind(final QuestionBean item, int number) {
        bean = item;
        String qLabel = number + ". " + bean.getQuestion_label();
        mQuestionLabel.setText(qLabel);
        String qAnswer = bean.getAnswer();
        showAnswer(qAnswer);
        initTimer();
        checkMaxRetry();
        btnGetResult.setOnClickListener(this);
        btnSubmitTele.setOnClickListener(this);
        if (bean.getIs_mandatory().equals(Global.TRUE_STRING)) {
            mPhoneEdt.setHint(activity.getString(R.string.requiredField));
        } else {
            mPhoneEdt.setHint("");
        }
    }

    /**
     * function to initialize CountdownTimer for prevent multiple click button submit tele check
     */
    private void initTimer() {
        new CountDownTimer(2000, 1000) {

            public void onTick(long millisUntilFinished) {
                btnSubmitTele.setText(activity.getString(R.string.please_wait));
                updateStateSubmit(false);
            }

            public void onFinish() {
                // enable button
                updateStateSubmit(true);
                btnSubmitTele.setText(R.string.btnSubmit);
            }
        };
    }

    /**
     * function show answer on dynamic form from QuestionBean
     * @param qAnswer field answer from QuestionBean
     */
    private void showAnswer(String qAnswer) {
        if (null != qAnswer && !qAnswer.isEmpty()) {
            TeleCheckResponse response = GsonHelper.fromJson(qAnswer, TeleCheckResponse.class);
            mAnswerLabel.setVisibility(View.VISIBLE);
            mAnswerLabel.setText(response.getMessage());
            mPhoneEdt.setText(response.getPhoneNumber());
        } else {
            mAnswerLabel.setVisibility(View.GONE);
        }
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        if (id == R.id.btnSubmitTele) {
            handleSubmitTele();
        } else if (id == R.id.btnGetResult) {
            handleResultTele();
        }

    }

    /**
     * function to handle button result telechecking
     */
    private void handleResultTele() {
        if (bean.getCountRetry() == 0) {
            showToast("Please submit Telecheck first");
        } else {
            TeleCheckRequest request = new TeleCheckRequest();
            request.setPhoneNumber(mPhoneEdt.getText().toString());
            request.setAudit(GlobalData.getSharedGlobalData().getAuditData());
            getResultTeleTask task = new getResultTeleTask(activity, request);
            task.execute();
        }
    }

    private void handleSubmitTele() {
        if (bean.getCountRetry() >= maxRetry) {
            showToast("Max Retry Reached");
            updateStateSubmit(false);
        } else if (isPhoneEmpty()) {
            showToast(activity.getString(R.string.phone_empty));
        } else {
            TeleCheckRequest request = new TeleCheckRequest();
            request.setPhoneNumber(mPhoneEdt.getText().toString());
            request.setAudit(GlobalData.getSharedGlobalData().getAuditData());
            submitTeleTask task = new submitTeleTask(activity, request);
            task.execute();
            updateCount(bean);
        }
    }

    /**
     * Asynctask Class for submit telecheck
     */
    private class submitTeleTask extends AsyncTask<Void, Void, String> {
        Context context;
        TeleCheckRequest pojoRequest;
        private String errMsg;
        private ProgressDialog dialog;

        public submitTeleTask(Activity activity, TeleCheckRequest request) {
            context = activity;
            pojoRequest = request;
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            dialog = ProgressDialog.show(activity, "",
                    activity.getString(R.string.progressWait), true, false);
        }

        @Override
        protected String doInBackground(Void... voids) {
            try {
                if (Tool.isInternetconnected(context)) {
                    HttpConnectionResult serverResult = request();
                    if (serverResult != null) {
                        if (serverResult.isOK()) {
                            return serverResult.getResult();
                        } else {
                            errMsg = serverResult.getResult();
                            return errMsg;
                        }
                    }
                } else {
                    errMsg = context.getString(R.string.no_internet_connection);
                    return errMsg;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            return null;
        }

        @Override
        protected void onPostExecute(String result) {
            super.onPostExecute(result);
            dismissDialog(dialog);
            if (null != errMsg) {
                mAnswerLabel.setText(errMsg);
            } else {
                updateAnswer(result);
            }
        }

        private HttpConnectionResult request() {
            String json = GsonHelper.toJson(pojoRequest);
            String url = GlobalData.getSharedGlobalData().getURL_RESULT_TELECHECK();
            boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
            boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
            HttpCryptedConnection httpConn = new HttpCryptedConnection(activity,
                    encrypt, decrypt);
            HttpConnectionResult httpConnectionResult = null;
            try {
                httpConnectionResult = httpConn.requestToServer(url, json,
                        Global.DEFAULTCONNECTIONTIMEOUT);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return httpConnectionResult;
        }

        private void dismissDialog(ProgressDialog dialog) {
            if (dialog.isShowing()) {
                dialog.dismiss();
            }
        }
    }
    private class getResultTeleTask extends AsyncTask<Void, Void, String> {

        private final Activity context;
        private final TeleCheckRequest pojoRequest;
        private String errMsg;
        private ProgressDialog dialog;

        public getResultTeleTask(Activity activity, TeleCheckRequest request) {
            context = activity;
            pojoRequest = request;
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            dialog = ProgressDialog.show(activity, "",
                    activity.getString(R.string.progressWait), true, false);
        }

        @Override
        protected String doInBackground(Void... voids) {
            try {
                if (Tool.isInternetconnected(context)) {
                    HttpConnectionResult serverResult = request();
                    if (serverResult != null) {
                        if (serverResult.isOK()) {
                            return serverResult.getResult();
                        } else {
                            errMsg = serverResult.getResult();
                            return errMsg;
                        }
                    }
                } else {
                    errMsg = context.getString(R.string.no_internet_connection);
                    return errMsg;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            return null;
        }

        @Override
        protected void onPostExecute(String results) {
            super.onPostExecute(results);
            dismissDialog(dialog);
            if (null != errMsg) {
                mAnswerLabel.setText(errMsg);
            } else {
                bean.setAnswer(results);
                updateAnswer(results);
            }
        }

        private HttpConnectionResult request() {
            String json = GsonHelper.toJson(pojoRequest);
            String url = GlobalData.getSharedGlobalData().getURL_RESULT_TELECHECK();
            boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
            boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
            HttpCryptedConnection httpConn = new HttpCryptedConnection(activity,
                    encrypt, decrypt);
            HttpConnectionResult httpConnectionResult = null;
            try {
                httpConnectionResult = httpConn.requestToServer(url, json,
                        Global.DEFAULTCONNECTIONTIMEOUT);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return httpConnectionResult;
        }

        private void dismissDialog(ProgressDialog dialog) {
            if (dialog.isShowing()) {
                dialog.dismiss();
            }
        }
    }

    /**
     * function to check phone number field is empty
     * @return
     */
    private boolean isPhoneEmpty() {
        return mPhoneEdt.getText().toString().isEmpty() && "".equals(mPhoneEdt.getText().toString());
    }

    /**
     * Function Helper for Check General Setting Max Retry Submit Tele Check
     */
    private void checkMaxRetry() {
        GeneralParameter gsRetry = GeneralParameterDataAccess.getOne(activity,
                GlobalData.getSharedGlobalData().getUser().getUuid_user(), Global.GS_MAX_TELECHECK);
        if (null != gsRetry) {
            String maxTry = null == gsRetry.getGs_value() || "".equalsIgnoreCase(gsRetry.getGs_value())
                    ? "0" : gsRetry.getGs_value();
            maxRetry = Integer.parseInt(maxTry);
        }
    }

    /**
     * Helper Function for update retry count submit tele check
     * @param bean for passing current QuestionBean
     */
    private void updateCount(QuestionBean bean) {
        bean.setCountRetry(bean.getCountRetry() + 1);
        if (bean.getCountRetry() == maxRetry) {
            updateStateSubmit(false);
            showToast("Maximum Retry Reached");
        }
    }

    /**
     * Helper function to show toast message
     * @param message for Message Toast
     */
    private void showToast(String message) {
        Toast.makeText(activity, message, Toast.LENGTH_SHORT).show();
    }

    /**
     * Helper function to update state Submit button
     * @param state flag for visibility and enable button submit
     */
    private void updateStateSubmit(Boolean state) {
        btnSubmitTele.setEnabled(state);
        btnSubmitTele.setClickable(state);
    }

    /**
     * function to update answer from asynctask http call
     * @param result value result from Webservice
     */
    private void updateAnswer(String result) {
        bean.setAnswer(result);
        if (null != bean.getIntTextAnswer() && !bean.getIntTextAnswer().isEmpty()) {
            bean.setIntTextAnswer(null);
        }
        TeleCheckResponse response = GsonHelper.fromJson(result, TeleCheckResponse.class);
        if (null != response) {
            mAnswerLabel.setText(response.getMessage());
            mAnswerLabel.setVisibility(View.VISIBLE);
        }

    }
}
