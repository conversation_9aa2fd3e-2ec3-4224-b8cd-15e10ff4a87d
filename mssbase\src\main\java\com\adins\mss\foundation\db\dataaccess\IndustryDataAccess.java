package com.adins.mss.foundation.db.dataaccess;

import android.content.Context;

import com.adins.mss.base.commons.Query;
import com.adins.mss.dao.DaoSession;
import com.adins.mss.dao.Industry;
import com.adins.mss.dao.IndustryDao;
import com.adins.mss.foundation.camerainapp.helper.Logger;
import com.adins.mss.foundation.db.DaoOpenHelper;

import java.util.List;

import de.greenrobot.dao.query.QueryBuilder;

/**
 * Created by developer on 1/18/18.
 */

public class IndustryDataAccess {

    public static String template = "{\"table\":\"MS_INDUSTRY\",\"field\":\"MARGIN\",\"constraint\":[{\"id\":\"TYPE_CODE\",\"value\":\"[SVY_SUB_JNS_USAHA]\"}]}";

    /**
     * use to generate dao session that you can access modelDao
     *
     * @param context --> context from activity
     * @return
     */
    protected static DaoSession getDaoSession(Context context) {
        return DaoOpenHelper.getDaoSession(context);
    }

    /**
     * get PODao and you can access db
     */
    protected static IndustryDao getIndustryDao(Context context) {
        return getDaoSession(context).getIndustryDao();
    }

    /**
     * add PO as entity
     */
    public static void add(Context context, Industry entity) {
        getIndustryDao(context).insert(entity);
        getDaoSession(context).clear();
    }

    /**
     * add PO as list
     */
    public static void add(Context context, List<Industry> entities) {
        getIndustryDao(context).insertInTx(entities);
        getDaoSession(context).clear();
    }

    public static void addOrReplace(Context context, Industry entity) {
        getIndustryDao(context).insertOrReplace(entity);
        getDaoSession(context).clear();
    }

    public static void addOrReplace(Context context, List<Industry> entities) {
        getIndustryDao(context).insertOrReplaceInTx(entities);
        getDaoSession(context).clear();
    }

    /**
     * Action for Delete
     */
    public static void delete(Context context, Industry entity) {
        getIndustryDao(context).delete(entity);
        getDaoSession(context).clear();
    }

    public static void clean(Context context) {
        getIndustryDao(context).deleteAll();
        getDaoSession(context).clear();
    }

    /**
     * Get One Entity by Id
     * @param context
     * @param id
     * @return
     */
    public static Industry getOne(Context context, int id) {
        QueryBuilder<Industry> qb = getIndustryDao(context).queryBuilder();
        qb.where(IndustryDao.Properties.Id.eq(id));
        qb.build();

        if (qb.list().isEmpty()) return null;
        else return qb.list().get(0);
    }

    public static double getByType(Context context, String code) {
        QueryBuilder<Industry> qb = getIndustryDao(context).queryBuilder();
        qb.where(IndustryDao.Properties.Type_code.eq(code));
        qb.build();

        if (qb.list().isEmpty()) return 0;
        else return qb.list().get(0).getMargin().doubleValue();
    }

    public static double getByType(Context context, Query query) {
        QueryBuilder<Industry> qb = getIndustryDao(context).queryBuilder();
        qb.where(IndustryDao.Properties.Type_code.eq(query.getConstraint().get(0).getValue()));
        qb.build();

        Logger.i("INFO", query.getConstraint().get(0).getValue());
        if (qb.list().isEmpty()) return 0;
        else return qb.list().get(0).getMargin().doubleValue();
    }

    /**
     * get All entities
     * @param context
     * @return
     */
    public static List<Industry> all(Context context) {
        List<Industry> data = getIndustryDao(context).loadAll();

        if (!data.isEmpty()) return data;
        else return null;
    }

    public static Industry getLast(Context context) {
        QueryBuilder<Industry> qb = getIndustryDao(context).queryBuilder();
        qb.orderDesc(IndustryDao.Properties.Dtm_upd);
        qb.limit(1);
        qb.build();

        if (!qb.list().isEmpty()) return qb.list().get(0);
        return null;
    }

    public static void addOrUpdateAll(Context context, List<Industry> transaction) {
        getIndustryDao(context).insertOrReplaceInTx(transaction);
        getDaoSession(context).clear();
    }
}
