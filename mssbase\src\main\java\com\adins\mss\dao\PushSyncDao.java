package com.adins.mss.dao;

import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;

import com.adins.mss.dao.PushSync;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TR_PUSHSYNC".
*/
public class PushSyncDao extends AbstractDao<PushSync, String> {

    public static final String TABLENAME = "TR_PUSHSYNC";

    /**
     * Properties of entity PushSync.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property IdPushSync = new Property(0, String.class, "idPushSync", true, "ID_PUSH_SYNC");
        public final static Property IsActive = new Property(1, String.class, "isActive", false, "IS_ACTIVE");
        public final static Property MaxTimestamps = new Property(2, String.class, "maxTimestamps", false, "MAX_TIMESTAMPS");
        public final static Property TableName = new Property(3, String.class, "tableName", false, "TABLE_NAME");
        public final static Property LovGroup = new Property(4, String.class, "lovGroup", false, "LOV_GROUP");
        public final static Property TotalRecord = new Property(5, String.class, "totalRecord", false, "TOTAL_RECORD");
        public final static Property Filename = new Property(6, String.class, "filename", false, "FILENAME");
        public final static Property FileUrl = new Property(7, String.class, "fileUrl", false, "FILE_URL");
    };


    public PushSyncDao(DaoConfig config) {
        super(config);
    }
    
    public PushSyncDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TR_PUSHSYNC\" (" + //
                "\"ID_PUSH_SYNC\" TEXT PRIMARY KEY NOT NULL ," + // 0: idPushSync
                "\"IS_ACTIVE\" TEXT," + // 1: isActive
                "\"MAX_TIMESTAMPS\" TEXT," + // 2: maxTimestamps
                "\"TABLE_NAME\" TEXT," + // 3: tableName
                "\"LOV_GROUP\" TEXT," + // 4: lovGroup
                "\"TOTAL_RECORD\" TEXT," + // 5: totalRecord
                "\"FILENAME\" TEXT," + // 6: filename
                "\"FILE_URL\" TEXT);"); // 7: fileUrl
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TR_PUSHSYNC\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, PushSync entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getIdPushSync());
 
        String isActive = entity.getIsActive();
        if (isActive != null) {
            stmt.bindString(2, isActive);
        }
 
        String maxTimestamps = entity.getMaxTimestamps();
        if (maxTimestamps != null) {
            stmt.bindString(3, maxTimestamps);
        }
 
        String tableName = entity.getTableName();
        if (tableName != null) {
            stmt.bindString(4, tableName);
        }
 
        String lovGroup = entity.getLovGroup();
        if (lovGroup != null) {
            stmt.bindString(5, lovGroup);
        }
 
        String totalRecord = entity.getTotalRecord();
        if (totalRecord != null) {
            stmt.bindString(6, totalRecord);
        }
 
        String filename = entity.getFilename();
        if (filename != null) {
            stmt.bindString(7, filename);
        }
 
        String fileUrl = entity.getFileUrl();
        if (fileUrl != null) {
            stmt.bindString(8, fileUrl);
        }
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public PushSync readEntity(Cursor cursor, int offset) {
        PushSync entity = new PushSync( //
            cursor.getString(offset + 0), // idPushSync
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // isActive
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // maxTimestamps
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // tableName
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // lovGroup
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // totalRecord
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // filename
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7) // fileUrl
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, PushSync entity, int offset) {
        entity.setIdPushSync(cursor.getString(offset + 0));
        entity.setIsActive(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setMaxTimestamps(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setTableName(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setLovGroup(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setTotalRecord(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setFilename(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setFileUrl(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(PushSync entity, long rowId) {
        return entity.getIdPushSync();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(PushSync entity) {
        if(entity != null) {
            return entity.getIdPushSync();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
}
