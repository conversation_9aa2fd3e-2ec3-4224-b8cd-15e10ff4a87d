package com.adins.mss.dao;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "TR_COLLECTIONACTIVITY".
 */
public class CollectionActivity {

    /** Not-null value. */
     @SerializedName("uuid_collection_activity")
    private String uuid_collection_activity;
     @SerializedName("uuid_task_h")
    private String uuid_task_h;
     @SerializedName("agreement_no")
    private String agreement_no;
     @SerializedName("branch_code")
    private String branch_code;
     @SerializedName("collector_name")
    private String collector_name;
     @SerializedName("activity")
    private String activity;
     @SerializedName("result")
    private String result;
     @SerializedName("notes")
    private String notes;
     @SerializedName("overdue_days")
    private String overdue_days;
     @SerializedName("activity_date")
    private java.util.Date activity_date;
     @SerializedName("ptp_date")
    private java.util.Date ptp_date;
     @SerializedName("usr_crt")
    private String usr_crt;
     @SerializedName("dtm_crt")
    private java.util.Date dtm_crt;
     @SerializedName("usr_upd")
    private String usr_upd;
     @SerializedName("dtm_upd")
    private java.util.Date dtm_upd;
     @SerializedName("next_plan_date")
    private java.util.Date next_plan_date;
     @SerializedName("next_plan_action")
    private String next_plan_action;

    public CollectionActivity() {
    }

    public CollectionActivity(String uuid_collection_activity) {
        this.uuid_collection_activity = uuid_collection_activity;
    }

    public CollectionActivity(String uuid_collection_activity, String uuid_task_h, String agreement_no, String branch_code, String collector_name, String activity, String result, String notes, String overdue_days, java.util.Date activity_date, java.util.Date ptp_date, String usr_crt, java.util.Date dtm_crt, String usr_upd, java.util.Date dtm_upd, java.util.Date next_plan_date, String next_plan_action) {
        this.uuid_collection_activity = uuid_collection_activity;
        this.uuid_task_h = uuid_task_h;
        this.agreement_no = agreement_no;
        this.branch_code = branch_code;
        this.collector_name = collector_name;
        this.activity = activity;
        this.result = result;
        this.notes = notes;
        this.overdue_days = overdue_days;
        this.activity_date = activity_date;
        this.ptp_date = ptp_date;
        this.usr_crt = usr_crt;
        this.dtm_crt = dtm_crt;
        this.usr_upd = usr_upd;
        this.dtm_upd = dtm_upd;
        this.next_plan_date = next_plan_date;
        this.next_plan_action = next_plan_action;
    }

    /** Not-null value. */
    public String getUuid_collection_activity() {
        return uuid_collection_activity;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_collection_activity(String uuid_collection_activity) {
        this.uuid_collection_activity = uuid_collection_activity;
    }

    public String getUuid_task_h() {
        return uuid_task_h;
    }

    public void setUuid_task_h(String uuid_task_h) {
        this.uuid_task_h = uuid_task_h;
    }

    public String getAgreement_no() {
        return agreement_no;
    }

    public void setAgreement_no(String agreement_no) {
        this.agreement_no = agreement_no;
    }

    public String getBranch_code() {
        return branch_code;
    }

    public void setBranch_code(String branch_code) {
        this.branch_code = branch_code;
    }

    public String getCollector_name() {
        return collector_name;
    }

    public void setCollector_name(String collector_name) {
        this.collector_name = collector_name;
    }

    public String getActivity() {
        return activity;
    }

    public void setActivity(String activity) {
        this.activity = activity;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getOverdue_days() {
        return overdue_days;
    }

    public void setOverdue_days(String overdue_days) {
        this.overdue_days = overdue_days;
    }

    public java.util.Date getActivity_date() {
        return activity_date;
    }

    public void setActivity_date(java.util.Date activity_date) {
        this.activity_date = activity_date;
    }

    public java.util.Date getPtp_date() {
        return ptp_date;
    }

    public void setPtp_date(java.util.Date ptp_date) {
        this.ptp_date = ptp_date;
    }

    public String getUsr_crt() {
        return usr_crt;
    }

    public void setUsr_crt(String usr_crt) {
        this.usr_crt = usr_crt;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public String getUsr_upd() {
        return usr_upd;
    }

    public void setUsr_upd(String usr_upd) {
        this.usr_upd = usr_upd;
    }

    public java.util.Date getDtm_upd() {
        return dtm_upd;
    }

    public void setDtm_upd(java.util.Date dtm_upd) {
        this.dtm_upd = dtm_upd;
    }

    public java.util.Date getNext_plan_date() {
        return next_plan_date;
    }

    public void setNext_plan_date(java.util.Date next_plan_date) {
        this.next_plan_date = next_plan_date;
    }

    public String getNext_plan_action() {
        return next_plan_action;
    }

    public void setNext_plan_action(String next_plan_action) {
        this.next_plan_action = next_plan_action;
    }

}
