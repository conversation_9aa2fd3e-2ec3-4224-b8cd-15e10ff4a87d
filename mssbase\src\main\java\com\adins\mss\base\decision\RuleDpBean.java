package com.adins.mss.base.decision;

public class RuleDpBean {
    private double grossDpPrctg;
    private double grossDpAmt;
    private double devMin;
    private String dpGrossBehaviour;

    public double getGrossDpPrctg() {
        return grossDpPrctg;
    }
    public void setGrossDpPrctg(double grossDpPrctg) {
        this.grossDpPrctg = grossDpPrctg;
    }
    public double getGrossDpAmt() {
        return grossDpAmt;
    }
    public void setGrossDpAmt(double grossDpAmt) {
        this.grossDpAmt = grossDpAmt;
    }
    public String getDpGrossBehaviour() {
        return dpGrossBehaviour;
    }
    public void setDpGrossBehaviour(String dpGrossBehaviour) {
        this.dpGrossBehaviour = dpGrossBehaviour;
    }

    public void AddGrossDPPrctg(double grossDpPrctg) {
        this.grossDpPrctg = grossDpPrctg;
    }
    public void AddGrossDPAmt(double grossDpAmt) {
        this.grossDpAmt = grossDpAmt;
    }
    public void AddDPGrossBehaviour(String dpGrossBehaviour) {
        this.dpGrossBehaviour = dpGrossBehaviour;
    }
    public void AddMinDpPrcntg(double grossDpPrctg){
        this.grossDpPrctg = grossDpPrctg;
    }
    public void AddDevMin(double devMin){
        this.devMin = devMin;
    }

    public double getDevMin() {
        return devMin;
    }

    public void setDevMin(double devMin) {
        this.devMin = devMin;
    }
}
