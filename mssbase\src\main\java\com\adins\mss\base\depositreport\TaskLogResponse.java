package com.adins.mss.base.depositreport;

import com.adins.mss.dao.TaskH;
import com.adins.mss.foundation.http.MssResponseType;
import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * Created by angga.permadi on 8/29/2016.
 */
public class TaskLogResponse extends MssResponseType {

    @SerializedName("listTaskH")
    private List<TaskH> taskHList;

    public List<TaskH> getTaskHList() {
        return taskHList;
    }

    public void setTaskHList(List<TaskH> taskHList) {
        this.taskHList = taskHList;
    }
}
