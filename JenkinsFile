def moduleScanSonar = 'mssbase/build/generated'
def moduleReportCoverage = 'mssbase/build/test-results/testDebugUnitTest/TEST-*.xml'
def modules = []
pipeline {
    agent any
    environment {
        JAVA_OPTS = "-Xmx1G -XX:+HeapDumpOnOutOfMemoryError"
		SONAR_PROJECT_KEY = 'WOMFMSS_20250616_ANDROID'
		SONAR_PROJECT_NAME = 'WOMFMSS_20250616_CR_OPSI_PENYELESAIAN_SENGKETA_ANDROID'
    }
	parameters {
        choice(name: 'TASK', choices: ['COMMIT_SCAN', 'BUILD', 'BUILD_ONLY'], description: 'Custom TASK to control pipeline steps')
		validatingString(
            name: 'URL_MAIN', 
            defaultValue: '', 
            regex: '.+', 
            failedValidationMessage: 'This Field is Required', 
            description: 'Custom URL_MAIN to Overide server link in repo'
        )
		choice(name: 'MODULE_NAME', choices: ['msssvy'], description: 'Select the module')
		choice(name: 'BUILD_FLAVOR', choices: ['developer', 'product', 'productcloud', 'productNonPilot', 'trial', 'trial2', 'trialCloud2', 'train', 'drc', 'uat3', 'uat1', 'dev1new', 'dev3new'], description: 'Select the flavor')
		choice(name: 'DEBUG_OR_RELEASE', choices: ['debug', 'release'], description: 'Select debug or release')
		base64File(name: 'MSMKEY_FILE', description: 'Upload a msmkey file to overide existing to signing APK')
		string(name: 'MSMKEY_ALIAS', defaultValue: '', description: 'Custom MSMKEY alias to signing APK')
		string(name: 'MSMKEY_PASSWORD', defaultValue: '', description: 'Custom MSMKEY alias to signing APK')
    }
    stages {
        stage('Prepare Workspace') {
            steps {
                checkout scm
				bat 'git reset --hard HEAD'
				// bat 'git clean -fdx'
            }
        }
		 stage('Get Active Modules') {
            steps {
                script {
                    // Read the settings.gradle file
                    def settingsFile = readFile ('settings.gradle')
                    echo "settingsFile: ${settingsFile}"

                    modules << 'mssbase'
					if (settingsFile.contains(":msssvy")) {
						modules << 'msssvy'
						moduleScanSonar = moduleScanSonar + ',msssvy/build/generated'
						moduleReportCoverage = moduleReportCoverage + ',msssvy/build/test-results/testDeveloperDebugUnitTest/TEST-*.xml'
					} else if (settingsFile.contains(":mssodr")) {
						modules << 'mssodr'
						moduleScanSonar = moduleScanSonar + ',mssodr/build/generated'
						moduleReportCoverage = moduleReportCoverage + ',mssodr/build/test-results/testDeveloperDebugUnitTest/TEST-*.xml'
					} else if (settingsFile.contains(":msscoll")) {
						modules << 'msscoll'
						moduleScanSonar = moduleScanSonar + ',msscoll/build/generated'
						moduleReportCoverage = moduleReportCoverage + ',msscoll/build/test-results/testDeveloperDebugUnitTest/TEST-*.xml'
					}
                    
                    // Print the active modules
                    echo "moduleScanSonar: ${moduleScanSonar}"
                    echo "moduleReportCoverage: ${moduleReportCoverage}"
                }
            }
        }
		stage('Modify Properties') {
			tools {
				jdk 'JDK 8' // Ensure this JDK version is configured in Jenkins
			}
            steps {
              script {
                  
                // Override local.properties
                def localProperties 
                if (fileExists("${env.WORKSPACE}/local.properties")) {
                    localProperties = readFile("${env.WORKSPACE}/local.properties")
                    localProperties = localProperties.replaceAll(/sdk.dir=.*/, "")
                    writeFile(file: "${env.WORKSPACE}/local.properties", text: localProperties)  
                }
                
                // Override gradle.properties
                def gradleProperties 
                if (fileExists("${env.WORKSPACE}/gradle.properties")) {
                    gradleProperties = readFile("${env.WORKSPACE}/gradle.properties")
                    
					gradleProperties = gradleProperties.replaceAll(/org.gradle.jvmargs=.*/, "org.gradle.jvmargs=-Xmx2G -XX:MaxPermSize=2G -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8")
					 
                    withCredentials([usernamePassword(credentialsId: 'MAVEN_USER', usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
                        gradleProperties = gradleProperties.replaceAll(/repo.username=.*/, "repo.username=${USERNAME}")
                        gradleProperties = gradleProperties.replaceAll(/repo.password=.*/, "repo.password=${PASSWORD}")
                    }
		
                    gradleProperties = gradleProperties.replaceAll(/apk.output.directory=.*/, "")
                    gradleProperties = gradleProperties.replaceAll(/apk.version=.*/, "")
                    writeFile(file: "${env.WORKSPACE}/gradle.properties", text: gradleProperties)  
                    
                    def newProperty = gradleProperties                    
                    if (params.APK_VERSION != null && params.APK_VERSION !="") {
                        newProperty = newProperty + "\napp.version=${apk_version}"
                    }
                    
                    writeFile(file: "${env.WORKSPACE}/gradle.properties", text: newProperty) 
                    
                }
                
                echo "Running Modify build.gradle ..."
                def buildGradleProject 
                if (fileExists("${env.WORKSPACE}/build.gradle")) {
                    buildGradleProject = readFile("${env.WORKSPACE}/build.gradle")
                    def mavenUrlExpected = """
                        maven {
                            credentials {
                                username = project.findProperty('repo.username') ?: 'defaultUsername'
                                password = project.findProperty('repo.password') ?: 'defaultPassword'
                            }
                            url 'http://mss-webdev-svr.ad-ins.com:8081/artifactory/libs-release'
                        }
                    """
                    buildGradleProject = buildGradleProject.replaceAll("maven \\{\\s*url 'http://mss-webdev-svr.ad-ins.com:8081/artifactory/libs-release'\\s*\\}", mavenUrlExpected)
                    echo "buildGradleProject = ${buildGradleProject}"
                    writeFile(file: "${env.WORKSPACE}/build.gradle", text: buildGradleProject)  
                }
                
                echo "Running Modify build.gradle in mssbase..."
                def buildGradleMssbase
                if (fileExists("${env.WORKSPACE}/mssbase/build.gradle")) {
                    buildGradleMssbase = readFile("${env.WORKSPACE}/mssbase/build.gradle")
                    buildGradleMssbase = buildGradleMssbase.replaceAll(/api 'jp.wasabeef:recyclerview-animators.*/, "")
                    writeFile(file: "${env.WORKSPACE}/mssbase/build.gradle", text: buildGradleMssbase)  
                } 
                
                echo "Running Modify settings.gradle to exclude :google-play-services..."
                def settingsXml
                if (fileExists("${env.WORKSPACE}/settings.gradle")) {
                    settingsXml = readFile("${env.WORKSPACE}/settings.gradle")
                    settingsXml = settingsXml.replaceAll(", ':google-play-services'", "")
                    echo "modified settingsXml = ${settingsXml}"
                    writeFile(file: "${env.WORKSPACE}/settings.gradle", text: settingsXml)  
                } 
		
                // Override application.properties
                def customPropertiesFilePath = "${env.WORKSPACE}/${params.MODULE_NAME}/src/${params.BUILD_FLAVOR}/assets/application.properties"
                def defaultPropertiesFilePath = "${env.WORKSPACE}/${params.MODULE_NAME}/src/main/assets/application.properties"
		
                def propertiesFile
                if (fileExists(customPropertiesFilePath)) {
                    defaultPropertiesFilePath = customPropertiesFilePath;
                    echo "Read properties from custome file in /${params.MODULE_NAME}/src/${params.BUILD_FLAVOR}"
                }
                propertiesFile = readFile(defaultPropertiesFilePath)
                
                if (params.URL_MAIN != null && params.URL_MAIN != "") {
                    propertiesFile = propertiesFile.replaceAll(/url_main=.*/, "url_main=${params.URL_MAIN}")
                    propertiesFile = propertiesFile.replaceAll(/#url_main=.*/, "")
                }
                
                propertiesFile = propertiesFile.replaceAll('\n\n', '\n')
                writeFile(file: defaultPropertiesFilePath, text: propertiesFile)  
                
                //echo "Running Modify build.gradle in root project..."
                //def buildGradle
                //if (fileExists("${env.WORKSPACE}/build.gradle")) {
                //    buildGradle = readFile("${env.WORKSPACE}/build.gradle")
                //    buildGradle = buildGradle.replaceAll(/allprojects \{/, 'allprojects { tasks.withType(JavaCompile) { options.compilerArgs << "-Xlint:-deprecation" } ')
                //    writeFile(file: "${env.WORKSPACE}/build.gradle", text: buildGradle)  
                //} 
              } 
            }
        }
		stage('Gradle Clean') {
			tools {
				jdk 'JDK 8' // Ensure this JDK version is configured in Jenkins
			}
            steps {
				script {
                    modules.each { module ->						
                        bat "./gradlew :${module}:clean --quiet"
                    }
                }
            }
        }
		stage('Test Debug Unit Test') {
			when {
                expression {
                    return params.TASK != 'BUILD_ONLY'
                }
            }
			tools {
				jdk 'JDK 8' // Ensure this JDK version is configured in Jenkins
			}
            steps {
				script {
                    modules.each { module ->
						if (module == 'mssbase') {
							bat "./gradlew :mssbase:testDebugUnitTest --quiet"
						} else {
							bat "./gradlew :${module}:testDeveloperDebugUnitTest --quiet"
						}
                    }
                }
            }
        }
        stage('Gradle BUILD') {
			when {
                expression {
                    return params.TASK != 'BUILD_ONLY'
                }
            }
			tools {
				jdk 'JDK 8' // Ensure this JDK version is configured in Jenkins
			}
            steps {
				script {
                    modules.each { module ->
						bat "./gradlew :${module}:build -x lint -x test --quiet"
                    }
                }
                 
            }
        }
        stage('SonarQube Analysis') {
			when {
                expression {
                    return params.TASK != 'BUILD_ONLY'
                }
            }
            tools {
                jdk "JDK 11" // the name you have given the JDK installation in Global Tool Configuration
            }
            environment {
                scannerHome = tool 'SonarQube' // the name you have given the Sonar Scanner (in Global Tool Configuration)
            }
            steps {
				withSonarQubeEnv('SonarQube') {
					withCredentials([string(credentialsId: 'SONAR_TOKEN', variable: 'SECRET')]) {
						bat """
							sonar-scanner \
							-Dsonar.projectName=${SONAR_PROJECT_NAME} \
							-Dsonar.projectKey=${SONAR_PROJECT_KEY} \
							-Dsonar.java.binaries=${moduleScanSonar} \
							-Dsonar.inclusions=**/src/main/java/com/adins/**,**/src/main/java/com/services/**,**/src/main/java/com/tracking/** \
							-Dsonar.exclusions=**/src/main/res/**,**/src/main/assets/**,**/src/main/AndroidManifest.xml,**/databasegenerator/** \
							-Dsonar.java.coveragePlugin=jacoco \
							-Dsonar.test.inclusions=**/src/test/java/**,**/src/androidTest/java/** \
							-Dsonar.junit.reportPaths=${moduleReportCoverage} \
							-Dsonar.coverage.jacoco.xmlReportPaths=**/jacoco-report/jacocoTestReportXml.xml \
							-Dsonar.log.level=ERROR \
							-Dsonar.coverage.exclusions=**/src/main/java/com/adins/mss/dao/**
						"""
					}
				}
            }
        }
		stage('Quality Gate') {
			when {
                expression {
                    return params.TASK != 'BUILD_ONLY'
                }
            }
			steps {
                timeout(time: 2, unit: 'HOURS') {
                    script {
                        def qg = waitForQualityGate()
                        if (qg.status != 'OK') {
                            currentBuild.result = 'UNSTABLE'
                            echo 'Quality gate failed'
                        }
                    }
                }
            }
        }
		stage('Replace File') {
			when {
				expression {
						return (params.TASK != 'COMMIT_SCAN') && params.MSMKEY_FILE!= null && params.MODULE_NAME!=null && params.MODULE_NAME!=''
					}
			}
            steps {
                script {
                    def targetFilePath = "${env.WORKSPACE}//${params.MODULE_NAME}//newmsmkey.jks"
                    def decodedFilePath = "${env.WORKSPACE}//newmsmkey.jks"
                    //write uploaded file
                    
                    bat """
                    powershell -Command "[System.IO.File]::WriteAllBytes('${decodedFilePath}', [System.Convert]::FromBase64String('${params.MSMKEY_FILE}'))"
                    """
                    
                    bat """
                    powershell -Command "[System.IO.File]::WriteAllBytes('${targetFilePath}', [System.Convert]::FromBase64String('${params.MSMKEY_FILE}'))"
                    """
                }
            }
        }
		stage('Build APK') {
            when {
				expression {
					return currentBuild.result != 'UNSTABLE' && params.TASK != 'COMMIT_SCAN'
				}
			}
			tools {
				jdk 'JDK 8' // Ensure this JDK version is configured in Jenkins
			}
            steps {
                bat 'gradlew --version'
                script {
                    if (params.MODULE_NAME != null && params.MODULE_NAME != "") {
						if (params.MSMKEY_FILE!= null && params.MSMKEY_ALIAS != null && params.MSMKEY_ALIAS != '' && params.MSMKEY_PASSWORD!= null && params.MSMKEY_PASSWORD != '') {
							bat "\"gradlew.bat\" :${params.MODULE_NAME}:assemble${params.BUILD_FLAVOR}${params.DEBUG_OR_RELEASE} -Pandroid.injected.signing.store.file=newmsmkey.jks -Pandroid.injected.signing.store.password=${params.MSMKEY_PASSWORD} -Pandroid.injected.signing.key.alias=${params.MSMKEY_ALIAS} -Pandroid.injected.signing.key.password=${params.MSMKEY_PASSWORD} --quiet"
						} else {
							bat "\"gradlew.bat\" :${params.MODULE_NAME}:assemble${params.BUILD_FLAVOR}${params.DEBUG_OR_RELEASE} --quiet"
						}
                        
                    } else {
						// Loop through each choice
						for (choice in params.MODULE_NAME) {
							echo "Module option: ${choice}"
							bat "\"gradlew.bat\" :${params.choice}:assembleDeveloperDebug --quiet"
						}
                    } 
                }
            }
        }
		stage('Archive APK') {
			when {
				expression {
						return currentBuild.result != 'UNSTABLE' && params.TASK != 'COMMIT_SCAN'
					}
			}
            steps {
                script {
					def timestamp = new Date().format("yyyyMMddHHmmss")
                    def newApkName = "#${BUILD_NUMBER}-${timestamp}"
					
					def apkFiles = findFiles(glob: '**/*.apk')
                    apkFiles.each { file ->
                        def directory = file.path.substring(0, file.path.lastIndexOf("\\"))
                        def newFileName = "${directory}/${file.name.replace('.apk', '')}-${newApkName}.apk"
                        bat "move ${file.path} ${newFileName}"
                        archiveArtifacts artifacts: newFileName, fingerprint: true, onlyIfSuccessful: true
                        echo "Archived APK file: ${newFileName}"
                    }
                }
            }
        }
    }
	
	post {
        always {
            script {		
				// Echo all parameter
				echo "Parameter TASK = ${params.TASK}"
				echo "Parameter URL_MAIN = ${params.URL_MAIN}"
				echo "Parameter MODULE_NAME = ${params.MODULE_NAME}"
				echo "Parameter BUILD_FLAVOR = ${params.BUILD_FLAVOR}"
				echo "Parameter DEBUG_OR_RELEASE = ${params.DEBUG_OR_RELEASE}"
				echo "Parameter MSMKEY_ALIAS = ${params.MSMKEY_ALIAS}"
				echo "Parameter MSMKEY_PASSWORD = ${params.MSMKEY_PASSWORD}"
				
                // Rule 1: Keep 5 successful builds with parameter TASK=BUILD_ONLY
                def rule1Count = 0
                def buildsToDelete = []
                currentBuild.rawBuild.project.builds.each { build ->
                    if (build.result?.toString() == 'SUCCESS' &&
                        build.getAction(ParametersAction)?.getParameter('TASK')?.value != 'COMMIT_SCAN') {
                        if (rule1Count < 5) {
                            rule1Count++
                        } else {
                            buildsToDelete << build
                        }
                    }
                }

                // Rule 2: Keep only 10 builds total
                def rule2Count = 0
                currentBuild.rawBuild.project.builds.each { build ->
                    if (!buildsToDelete.contains(build)) {
                        if (rule2Count < 10) {
                            rule2Count++
                        } else {
                            buildsToDelete << build
                        }
                    }
                }

                // Rule 3: Delete all other builds
                buildsToDelete.each { build ->
                    build.delete()
                }
            }
        }
    }
}
