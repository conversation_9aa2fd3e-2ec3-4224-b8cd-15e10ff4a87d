package com.adins.mss.base.depositreport;

import com.adins.mss.dao.TaskD;
import com.adins.mss.foundation.http.MssResponseType;
import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * Created by angga.permadi on 8/30/2016.
 */
public class DetailTaskHResponse extends MssResponseType {

    @SerializedName("detailTaskH")
    private List<TaskD> taskDs;

    public List<TaskD> getTaskDs() {
        return taskDs;
    }

    public void setTaskDs(List<TaskD> taskDs) {
        this.taskDs = taskDs;
    }
}
