package com.adins.mss.dao;

import java.util.List;
import com.adins.mss.dao.DaoSession;
import de.greenrobot.dao.DaoException;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "MS_USER".
 */
public class User {

    /** Not-null value. */
     @SerializedName("uuid_user")
    private String uuid_user;
     @SerializedName("flag_job")
    private String flag_job;
     @ExcludeFromGson 
	 @SerializedName("image_profile")
    private byte[] image_profile;
     @SerializedName("fullname")
    private String fullname;
     @SerializedName("branch_id")
    private String branch_id;
     @SerializedName("branch_name")
    private String branch_name;
     @SerializedName("is_branch")
    private String is_branch;
     @SerializedName("password")
    private String password;
     @SerializedName("task_seq")
    private Integer task_seq;
     @SerializedName("google_id")
    private String google_id;
     @SerializedName("facebook_id")
    private String facebook_id;
     @SerializedName("login_id")
    private String login_id;
     @SerializedName("fail_count")
    private Integer fail_count;
     @SerializedName("last_sync")
    private java.util.Date last_sync;
     @SerializedName("branch_address")
    private String branch_address;
     @SerializedName("usr_crt")
    private String usr_crt;
     @SerializedName("dtm_crt")
    private java.util.Date dtm_crt;
     @SerializedName("usr_upd")
    private String usr_upd;
     @SerializedName("dtm_upd")
    private java.util.Date dtm_upd;
     @ExcludeFromGson 
	 @SerializedName("image_cover")
    private byte[] image_cover;
     @SerializedName("chg_pwd")
    private String chg_pwd;
     @SerializedName("job_description")
    private String job_description;
     @SerializedName("pwd_exp")
    private String pwd_exp;
     @SerializedName("dealer_name")
    private String dealer_name;
     @SerializedName("cash_limit")
    private String cash_limit;
     @SerializedName("cash_on_hand")
    private String cash_on_hand;
     @SerializedName("uuid_branch")
    private String uuid_branch;
     @SerializedName("uuid_group")
    private String uuid_group;
     @SerializedName("uuid_dealer")
    private String uuid_dealer;
     @SerializedName("start_time")
    private String start_time;
     @SerializedName("end_time")
    private String end_time;
     @SerializedName("is_tracking")
    private String is_tracking;
     @SerializedName("tracking_days")
    private String tracking_days;
     @SerializedName("token_id_fcm")
    private String token_id_fcm;
     @SerializedName("is_piloting")
    private String is_piloting;
     @SerializedName("pushsync_time")
    private String pushsync_time;
     @SerializedName("is_piloting_cae")
    private String is_piloting_cae;
     @SerializedName("branch_type")
    private String branch_type;

    /** Used to resolve relations */
    private transient DaoSession daoSession;

    /** Used for active entity operations. */
    private transient UserDao myDao;

    private List<GeneralParameter> generalParameterList;
    private List<GroupUser> groupUserList;
    private List<Menu> menuList;
    private List<Logger> loggerList;
    private List<CollectionHistory> collectionHistoryList;
    private List<DepositReportH> depositReportHList;
    private List<LocationInfo> locationInfoList;
    private List<Message> messageList;
    private List<MobileContentH> mobileContentHList;
    private List<PrintResult> printResultList;
    private List<ReceiptVoucher> receiptVoucherList;
    private List<TaskH> taskHList;
    private List<ErrorLog> errorLogList;
    private List<Timeline> timelineList;

    public User() {
    }

    public User(String uuid_user) {
        this.uuid_user = uuid_user;
    }

    public User(String uuid_user, String flag_job, byte[] image_profile, String fullname, String branch_id, String branch_name, String is_branch, String password, Integer task_seq, String google_id, String facebook_id, String login_id, Integer fail_count, java.util.Date last_sync, String branch_address, String usr_crt, java.util.Date dtm_crt, String usr_upd, java.util.Date dtm_upd, byte[] image_cover, String chg_pwd, String job_description, String pwd_exp, String dealer_name, String cash_limit, String cash_on_hand, String uuid_branch, String uuid_group, String uuid_dealer, String start_time, String end_time, String is_tracking, String tracking_days, String token_id_fcm, String is_piloting, String pushsync_time, String is_piloting_cae, String branch_type) {
        this.uuid_user = uuid_user;
        this.flag_job = flag_job;
        this.image_profile = image_profile;
        this.fullname = fullname;
        this.branch_id = branch_id;
        this.branch_name = branch_name;
        this.is_branch = is_branch;
        this.password = password;
        this.task_seq = task_seq;
        this.google_id = google_id;
        this.facebook_id = facebook_id;
        this.login_id = login_id;
        this.fail_count = fail_count;
        this.last_sync = last_sync;
        this.branch_address = branch_address;
        this.usr_crt = usr_crt;
        this.dtm_crt = dtm_crt;
        this.usr_upd = usr_upd;
        this.dtm_upd = dtm_upd;
        this.image_cover = image_cover;
        this.chg_pwd = chg_pwd;
        this.job_description = job_description;
        this.pwd_exp = pwd_exp;
        this.dealer_name = dealer_name;
        this.cash_limit = cash_limit;
        this.cash_on_hand = cash_on_hand;
        this.uuid_branch = uuid_branch;
        this.uuid_group = uuid_group;
        this.uuid_dealer = uuid_dealer;
        this.start_time = start_time;
        this.end_time = end_time;
        this.is_tracking = is_tracking;
        this.tracking_days = tracking_days;
        this.token_id_fcm = token_id_fcm;
        this.is_piloting = is_piloting;
        this.pushsync_time = pushsync_time;
        this.is_piloting_cae = is_piloting_cae;
        this.branch_type = branch_type;
    }

    /** called by internal mechanisms, do not call yourself. */
    public void __setDaoSession(DaoSession daoSession) {
        this.daoSession = daoSession;
        myDao = daoSession != null ? daoSession.getUserDao() : null;
    }

    /** Not-null value. */
    public String getUuid_user() {
        return uuid_user;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_user(String uuid_user) {
        this.uuid_user = uuid_user;
    }

    public String getFlag_job() {
        return flag_job;
    }

    public void setFlag_job(String flag_job) {
        this.flag_job = flag_job;
    }

    public byte[] getImage_profile() {
        return image_profile;
    }

    public void setImage_profile(byte[] image_profile) {
        this.image_profile = image_profile;
    }

    public String getFullname() {
        return fullname;
    }

    public void setFullname(String fullname) {
        this.fullname = fullname;
    }

    public String getBranch_id() {
        return branch_id;
    }

    public void setBranch_id(String branch_id) {
        this.branch_id = branch_id;
    }

    public String getBranch_name() {
        return branch_name;
    }

    public void setBranch_name(String branch_name) {
        this.branch_name = branch_name;
    }

    public String getIs_branch() {
        return is_branch;
    }

    public void setIs_branch(String is_branch) {
        this.is_branch = is_branch;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getTask_seq() {
        return task_seq;
    }

    public void setTask_seq(Integer task_seq) {
        this.task_seq = task_seq;
    }

    public String getGoogle_id() {
        return google_id;
    }

    public void setGoogle_id(String google_id) {
        this.google_id = google_id;
    }

    public String getFacebook_id() {
        return facebook_id;
    }

    public void setFacebook_id(String facebook_id) {
        this.facebook_id = facebook_id;
    }

    public String getLogin_id() {
        return login_id;
    }

    public void setLogin_id(String login_id) {
        this.login_id = login_id;
    }

    public Integer getFail_count() {
        return fail_count;
    }

    public void setFail_count(Integer fail_count) {
        this.fail_count = fail_count;
    }

    public java.util.Date getLast_sync() {
        return last_sync;
    }

    public void setLast_sync(java.util.Date last_sync) {
        this.last_sync = last_sync;
    }

    public String getBranch_address() {
        return branch_address;
    }

    public void setBranch_address(String branch_address) {
        this.branch_address = branch_address;
    }

    public String getUsr_crt() {
        return usr_crt;
    }

    public void setUsr_crt(String usr_crt) {
        this.usr_crt = usr_crt;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public String getUsr_upd() {
        return usr_upd;
    }

    public void setUsr_upd(String usr_upd) {
        this.usr_upd = usr_upd;
    }

    public java.util.Date getDtm_upd() {
        return dtm_upd;
    }

    public void setDtm_upd(java.util.Date dtm_upd) {
        this.dtm_upd = dtm_upd;
    }

    public byte[] getImage_cover() {
        return image_cover;
    }

    public void setImage_cover(byte[] image_cover) {
        this.image_cover = image_cover;
    }

    public String getChg_pwd() {
        return chg_pwd;
    }

    public void setChg_pwd(String chg_pwd) {
        this.chg_pwd = chg_pwd;
    }

    public String getJob_description() {
        return job_description;
    }

    public void setJob_description(String job_description) {
        this.job_description = job_description;
    }

    public String getPwd_exp() {
        return pwd_exp;
    }

    public void setPwd_exp(String pwd_exp) {
        this.pwd_exp = pwd_exp;
    }

    public String getDealer_name() {
        return dealer_name;
    }

    public void setDealer_name(String dealer_name) {
        this.dealer_name = dealer_name;
    }

    public String getCash_limit() {
        return cash_limit;
    }

    public void setCash_limit(String cash_limit) {
        this.cash_limit = cash_limit;
    }

    public String getCash_on_hand() {
        return cash_on_hand;
    }

    public void setCash_on_hand(String cash_on_hand) {
        this.cash_on_hand = cash_on_hand;
    }

    public String getUuid_branch() {
        return uuid_branch;
    }

    public void setUuid_branch(String uuid_branch) {
        this.uuid_branch = uuid_branch;
    }

    public String getUuid_group() {
        return uuid_group;
    }

    public void setUuid_group(String uuid_group) {
        this.uuid_group = uuid_group;
    }

    public String getUuid_dealer() {
        return uuid_dealer;
    }

    public void setUuid_dealer(String uuid_dealer) {
        this.uuid_dealer = uuid_dealer;
    }

    public String getStart_time() {
        return start_time;
    }

    public void setStart_time(String start_time) {
        this.start_time = start_time;
    }

    public String getEnd_time() {
        return end_time;
    }

    public void setEnd_time(String end_time) {
        this.end_time = end_time;
    }

    public String getIs_tracking() {
        return is_tracking;
    }

    public void setIs_tracking(String is_tracking) {
        this.is_tracking = is_tracking;
    }

    public String getTracking_days() {
        return tracking_days;
    }

    public void setTracking_days(String tracking_days) {
        this.tracking_days = tracking_days;
    }

    public String getToken_id_fcm() {
        return token_id_fcm;
    }

    public void setToken_id_fcm(String token_id_fcm) {
        this.token_id_fcm = token_id_fcm;
    }

    public String getIs_piloting() {
        return is_piloting;
    }

    public void setIs_piloting(String is_piloting) {
        this.is_piloting = is_piloting;
    }

    public String getPushsync_time() {
        return pushsync_time;
    }

    public void setPushsync_time(String pushsync_time) {
        this.pushsync_time = pushsync_time;
    }

    public String getIs_piloting_cae() {
        return is_piloting_cae;
    }

    public void setIs_piloting_cae(String is_piloting_cae) {
        this.is_piloting_cae = is_piloting_cae;
    }

    public String getBranch_type() {
        return branch_type;
    }

    public void setBranch_type(String branch_type) {
        this.branch_type = branch_type;
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<GeneralParameter> getGeneralParameterList() {
        if (generalParameterList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            GeneralParameterDao targetDao = daoSession.getGeneralParameterDao();
            List<GeneralParameter> generalParameterListNew = targetDao._queryUser_GeneralParameterList(uuid_user);
            synchronized (this) {
                if(generalParameterList == null) {
                    generalParameterList = generalParameterListNew;
                }
            }
        }
        return generalParameterList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetGeneralParameterList() {
        generalParameterList = null;
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<GroupUser> getGroupUserList() {
        if (groupUserList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            GroupUserDao targetDao = daoSession.getGroupUserDao();
            List<GroupUser> groupUserListNew = targetDao._queryUser_GroupUserList(uuid_user);
            synchronized (this) {
                if(groupUserList == null) {
                    groupUserList = groupUserListNew;
                }
            }
        }
        return groupUserList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetGroupUserList() {
        groupUserList = null;
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<Menu> getMenuList() {
        if (menuList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            MenuDao targetDao = daoSession.getMenuDao();
            List<Menu> menuListNew = targetDao._queryUser_MenuList(uuid_user);
            synchronized (this) {
                if(menuList == null) {
                    menuList = menuListNew;
                }
            }
        }
        return menuList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetMenuList() {
        menuList = null;
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<Logger> getLoggerList() {
        if (loggerList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            LoggerDao targetDao = daoSession.getLoggerDao();
            List<Logger> loggerListNew = targetDao._queryUser_LoggerList(uuid_user);
            synchronized (this) {
                if(loggerList == null) {
                    loggerList = loggerListNew;
                }
            }
        }
        return loggerList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetLoggerList() {
        loggerList = null;
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<CollectionHistory> getCollectionHistoryList() {
        if (collectionHistoryList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            CollectionHistoryDao targetDao = daoSession.getCollectionHistoryDao();
            List<CollectionHistory> collectionHistoryListNew = targetDao._queryUser_CollectionHistoryList(uuid_user);
            synchronized (this) {
                if(collectionHistoryList == null) {
                    collectionHistoryList = collectionHistoryListNew;
                }
            }
        }
        return collectionHistoryList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetCollectionHistoryList() {
        collectionHistoryList = null;
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<DepositReportH> getDepositReportHList() {
        if (depositReportHList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            DepositReportHDao targetDao = daoSession.getDepositReportHDao();
            List<DepositReportH> depositReportHListNew = targetDao._queryUser_DepositReportHList(uuid_user);
            synchronized (this) {
                if(depositReportHList == null) {
                    depositReportHList = depositReportHListNew;
                }
            }
        }
        return depositReportHList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetDepositReportHList() {
        depositReportHList = null;
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<LocationInfo> getLocationInfoList() {
        if (locationInfoList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            LocationInfoDao targetDao = daoSession.getLocationInfoDao();
            List<LocationInfo> locationInfoListNew = targetDao._queryUser_LocationInfoList(uuid_user);
            synchronized (this) {
                if(locationInfoList == null) {
                    locationInfoList = locationInfoListNew;
                }
            }
        }
        return locationInfoList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetLocationInfoList() {
        locationInfoList = null;
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<Message> getMessageList() {
        if (messageList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            MessageDao targetDao = daoSession.getMessageDao();
            List<Message> messageListNew = targetDao._queryUser_MessageList(uuid_user);
            synchronized (this) {
                if(messageList == null) {
                    messageList = messageListNew;
                }
            }
        }
        return messageList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetMessageList() {
        messageList = null;
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<MobileContentH> getMobileContentHList() {
        if (mobileContentHList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            MobileContentHDao targetDao = daoSession.getMobileContentHDao();
            List<MobileContentH> mobileContentHListNew = targetDao._queryUser_MobileContentHList(uuid_user);
            synchronized (this) {
                if(mobileContentHList == null) {
                    mobileContentHList = mobileContentHListNew;
                }
            }
        }
        return mobileContentHList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetMobileContentHList() {
        mobileContentHList = null;
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<PrintResult> getPrintResultList() {
        if (printResultList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            PrintResultDao targetDao = daoSession.getPrintResultDao();
            List<PrintResult> printResultListNew = targetDao._queryUser_PrintResultList(uuid_user);
            synchronized (this) {
                if(printResultList == null) {
                    printResultList = printResultListNew;
                }
            }
        }
        return printResultList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetPrintResultList() {
        printResultList = null;
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<ReceiptVoucher> getReceiptVoucherList() {
        if (receiptVoucherList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            ReceiptVoucherDao targetDao = daoSession.getReceiptVoucherDao();
            List<ReceiptVoucher> receiptVoucherListNew = targetDao._queryUser_ReceiptVoucherList(uuid_user);
            synchronized (this) {
                if(receiptVoucherList == null) {
                    receiptVoucherList = receiptVoucherListNew;
                }
            }
        }
        return receiptVoucherList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetReceiptVoucherList() {
        receiptVoucherList = null;
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<TaskH> getTaskHList() {
        if (taskHList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            TaskHDao targetDao = daoSession.getTaskHDao();
            List<TaskH> taskHListNew = targetDao._queryUser_TaskHList(uuid_user);
            synchronized (this) {
                if(taskHList == null) {
                    taskHList = taskHListNew;
                }
            }
        }
        return taskHList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetTaskHList() {
        taskHList = null;
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<ErrorLog> getErrorLogList() {
        if (errorLogList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            ErrorLogDao targetDao = daoSession.getErrorLogDao();
            List<ErrorLog> errorLogListNew = targetDao._queryUser_ErrorLogList(uuid_user);
            synchronized (this) {
                if(errorLogList == null) {
                    errorLogList = errorLogListNew;
                }
            }
        }
        return errorLogList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetErrorLogList() {
        errorLogList = null;
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<Timeline> getTimelineList() {
        if (timelineList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            TimelineDao targetDao = daoSession.getTimelineDao();
            List<Timeline> timelineListNew = targetDao._queryUser_TimelineList(uuid_user);
            synchronized (this) {
                if(timelineList == null) {
                    timelineList = timelineListNew;
                }
            }
        }
        return timelineList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetTimelineList() {
        timelineList = null;
    }

    /** Convenient call for {@link AbstractDao#delete(Object)}. Entity must attached to an entity context. */
    public void delete() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.delete(this);
    }

    /** Convenient call for {@link AbstractDao#update(Object)}. Entity must attached to an entity context. */
    public void update() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.update(this);
    }

    /** Convenient call for {@link AbstractDao#refresh(Object)}. Entity must attached to an entity context. */
    public void refresh() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.refresh(this);
    }

}
