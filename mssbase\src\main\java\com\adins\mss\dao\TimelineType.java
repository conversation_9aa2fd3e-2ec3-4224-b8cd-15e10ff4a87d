package com.adins.mss.dao;

import java.util.List;
import com.adins.mss.dao.DaoSession;
import de.greenrobot.dao.DaoException;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "MS_TIMELINETYPE".
 */
public class TimelineType {

    /** Not-null value. */
     @SerializedName("uuid_timeline_type")
    private String uuid_timeline_type;
     @SerializedName("timeline_description")
    private String timeline_description;
     @SerializedName("timeline_type")
    private String timeline_type;
     @SerializedName("usr_crt")
    private String usr_crt;
     @SerializedName("dtm_crt")
    private java.util.Date dtm_crt;
     @SerializedName("usr_upd")
    private String usr_upd;
     @SerializedName("dtm_upd")
    private java.util.Date dtm_upd;

    /** Used to resolve relations */
    private transient DaoSession daoSession;

    /** Used for active entity operations. */
    private transient TimelineTypeDao myDao;

    private List<Timeline> timelineList;

    public TimelineType() {
    }

    public TimelineType(String uuid_timeline_type) {
        this.uuid_timeline_type = uuid_timeline_type;
    }

    public TimelineType(String uuid_timeline_type, String timeline_description, String timeline_type, String usr_crt, java.util.Date dtm_crt, String usr_upd, java.util.Date dtm_upd) {
        this.uuid_timeline_type = uuid_timeline_type;
        this.timeline_description = timeline_description;
        this.timeline_type = timeline_type;
        this.usr_crt = usr_crt;
        this.dtm_crt = dtm_crt;
        this.usr_upd = usr_upd;
        this.dtm_upd = dtm_upd;
    }

    /** called by internal mechanisms, do not call yourself. */
    public void __setDaoSession(DaoSession daoSession) {
        this.daoSession = daoSession;
        myDao = daoSession != null ? daoSession.getTimelineTypeDao() : null;
    }

    /** Not-null value. */
    public String getUuid_timeline_type() {
        return uuid_timeline_type;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_timeline_type(String uuid_timeline_type) {
        this.uuid_timeline_type = uuid_timeline_type;
    }

    public String getTimeline_description() {
        return timeline_description;
    }

    public void setTimeline_description(String timeline_description) {
        this.timeline_description = timeline_description;
    }

    public String getTimeline_type() {
        return timeline_type;
    }

    public void setTimeline_type(String timeline_type) {
        this.timeline_type = timeline_type;
    }

    public String getUsr_crt() {
        return usr_crt;
    }

    public void setUsr_crt(String usr_crt) {
        this.usr_crt = usr_crt;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public String getUsr_upd() {
        return usr_upd;
    }

    public void setUsr_upd(String usr_upd) {
        this.usr_upd = usr_upd;
    }

    public java.util.Date getDtm_upd() {
        return dtm_upd;
    }

    public void setDtm_upd(java.util.Date dtm_upd) {
        this.dtm_upd = dtm_upd;
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<Timeline> getTimelineList() {
        if (timelineList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            TimelineDao targetDao = daoSession.getTimelineDao();
            List<Timeline> timelineListNew = targetDao._queryTimelineType_TimelineList(uuid_timeline_type);
            synchronized (this) {
                if(timelineList == null) {
                    timelineList = timelineListNew;
                }
            }
        }
        return timelineList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetTimelineList() {
        timelineList = null;
    }

    /** Convenient call for {@link AbstractDao#delete(Object)}. Entity must attached to an entity context. */
    public void delete() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.delete(this);
    }

    /** Convenient call for {@link AbstractDao#update(Object)}. Entity must attached to an entity context. */
    public void update() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.update(this);
    }

    /** Convenient call for {@link AbstractDao#refresh(Object)}. Entity must attached to an entity context. */
    public void refresh() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.refresh(this);
    }

}
