package com.adins.mss.base.checkin;

import com.adins.mss.foundation.http.MssResponseType;
import com.google.gson.annotations.SerializedName;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>lalmasyhur on 21/02/2018.
 */

public class JsonResponseAbsensi extends MssResponseType {
    @SerializedName("result")
    String result;
    @SerializedName("success")
    String success;

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getSuccess() {
        return success;
    }

    public void setSuccsess(String success) {
        this.success = success;
    }
}
