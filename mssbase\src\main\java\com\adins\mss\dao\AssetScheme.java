package com.adins.mss.dao;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "MS_ASSET_SCHEME".
 */
public class AssetScheme {

     @SerializedName("id")
    private long id;
     @SerializedName("assetSchemeId")
    private int asset_scheme_id;
    /** Not-null value. */
     @SerializedName("assetTypeCode")
    private String type_code;
    /** Not-null value. */
     @SerializedName("assetTypeName")
    private String type_name;
     @SerializedName("isDeleted")
    private Integer is_deleted;
     @SerializedName("dtmUpd")
    private java.util.Date dtm_upd;

    public AssetScheme() {
    }

    public AssetScheme(long id) {
        this.id = id;
    }

    public AssetScheme(long id, int asset_scheme_id, String type_code, String type_name, Integer is_deleted, java.util.Date dtm_upd) {
        this.id = id;
        this.asset_scheme_id = asset_scheme_id;
        this.type_code = type_code;
        this.type_name = type_name;
        this.is_deleted = is_deleted;
        this.dtm_upd = dtm_upd;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getAsset_scheme_id() {
        return asset_scheme_id;
    }

    public void setAsset_scheme_id(int asset_scheme_id) {
        this.asset_scheme_id = asset_scheme_id;
    }

    /** Not-null value. */
    public String getType_code() {
        return type_code;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setType_code(String type_code) {
        this.type_code = type_code;
    }

    /** Not-null value. */
    public String getType_name() {
        return type_name;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setType_name(String type_name) {
        this.type_name = type_name;
    }

    public Integer getIs_deleted() {
        return is_deleted;
    }

    public void setIs_deleted(Integer is_deleted) {
        this.is_deleted = is_deleted;
    }

    public java.util.Date getDtm_upd() {
        return dtm_upd;
    }

    public void setDtm_upd(java.util.Date dtm_upd) {
        this.dtm_upd = dtm_upd;
    }

}
