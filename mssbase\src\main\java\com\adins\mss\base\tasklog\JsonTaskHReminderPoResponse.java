package com.adins.mss.base.tasklog;

import com.adins.mss.dao.TaskH;
import com.adins.mss.foundation.http.MssResponseType;
import com.google.gson.annotations.SerializedName;

import java.util.List;

public class JsonTaskHReminderPoResponse extends MssResponseType {

    @SerializedName("listTaskList")
    private List<TaskH> listTaskList;

    public List<TaskH> getListTaskList() {
        return listTaskList;
    }

    public void setListTaskList(List<TaskH> listTaskList) {
        this.listTaskList = listTaskList;
    }

}
