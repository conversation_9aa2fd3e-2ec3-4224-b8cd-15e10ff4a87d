package com.adins.mss.foundation.print;

import android.content.Context;
import android.os.AsyncTask;
import android.widget.Toast;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.foundation.camerainapp.helper.Logger;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.http.MssRequestType;
import com.adins.mss.foundation.http.MssResponseType;

/**
 * Created by angga.permadi on 3/3/2016.
 */
public class SubmitPrintSender extends AsyncTask<Void, Void, MssResponseType> {
    private static final String TAG = SubmitPrintSender.class.getSimpleName();

    protected String errorMessage;
    private MssRequestType entity;
    private Context context;

    public SubmitPrintSender(Context context, MssRequestType entity) {
        this.context = context;
        this.entity = entity;
    }

    @Override
    protected MssResponseType doInBackground(Void... params) {
        entity.setAudit(GlobalData.getSharedGlobalData().getAuditData());
        HttpCryptedConnection httpConn = new HttpCryptedConnection(context,
                GlobalData.getSharedGlobalData().isEncrypt(), GlobalData.getSharedGlobalData().isDecrypt());
        HttpConnectionResult serverResult = null;
        String url = GlobalData.getSharedGlobalData().getURL_SUBMIT_PRINT_COUNT();

        try {
            serverResult = httpConn.requestToServer(url, GsonHelper.toJson(entity), Global.DEFAULTCONNECTIONTIMEOUT);
        } catch (Exception e) {
            e.printStackTrace();
            errorMessage = e.getMessage();
        }

        MssResponseType resultBean = null;

        if (serverResult != null) {
            if (serverResult.isOK()) {
                try {
                    resultBean = GsonHelper.fromJson(serverResult.getResult(), MssResponseType.class);
                } catch (Exception e) {
                    errorMessage = serverResult.getResult();
                }
            } else {
                errorMessage = serverResult.getResult();
            }
        }

        return resultBean;
    }

    @Override
    protected void onPostExecute(MssResponseType mssResponseType) {
        super.onPostExecute(mssResponseType);

        if (mssResponseType != null && mssResponseType.getStatus().getCode() == 0) {
            Logger.d(TAG, "success");
        }

        if (errorMessage != null) {
            if (context != null) {
                Toast.makeText(context, errorMessage, Toast.LENGTH_SHORT).show();
            }
        }
    }
}

