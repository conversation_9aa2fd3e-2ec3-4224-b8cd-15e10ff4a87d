package com.adins.mss.dao;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "MS_HOLIDAY".
 */
public class Holiday {

    /** Not-null value. */
     @SerializedName("uuid_holiday")
    private String uuid_holiday;
     @SerializedName("h_date")
    private java.util.Date h_date;
     @SerializedName("h_desc")
    private String h_desc;
     @SerializedName("flag_holiday")
    private String flag_holiday;
     @SerializedName("dtm_upd")
    private java.util.Date dtm_upd;
     @SerializedName("flag_day")
    private String flag_day;
     @SerializedName("branch")
    private String branch;

    public Holiday() {
    }

    public Holiday(String uuid_holiday) {
        this.uuid_holiday = uuid_holiday;
    }

    public Holiday(String uuid_holiday, java.util.Date h_date, String h_desc, String flag_holiday, java.util.Date dtm_upd, String flag_day, String branch) {
        this.uuid_holiday = uuid_holiday;
        this.h_date = h_date;
        this.h_desc = h_desc;
        this.flag_holiday = flag_holiday;
        this.dtm_upd = dtm_upd;
        this.flag_day = flag_day;
        this.branch = branch;
    }

    /** Not-null value. */
    public String getUuid_holiday() {
        return uuid_holiday;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_holiday(String uuid_holiday) {
        this.uuid_holiday = uuid_holiday;
    }

    public java.util.Date getH_date() {
        return h_date;
    }

    public void setH_date(java.util.Date h_date) {
        this.h_date = h_date;
    }

    public String getH_desc() {
        return h_desc;
    }

    public void setH_desc(String h_desc) {
        this.h_desc = h_desc;
    }

    public String getFlag_holiday() {
        return flag_holiday;
    }

    public void setFlag_holiday(String flag_holiday) {
        this.flag_holiday = flag_holiday;
    }

    public java.util.Date getDtm_upd() {
        return dtm_upd;
    }

    public void setDtm_upd(java.util.Date dtm_upd) {
        this.dtm_upd = dtm_upd;
    }

    public String getFlag_day() {
        return flag_day;
    }

    public void setFlag_day(String flag_day) {
        this.flag_day = flag_day;
    }

    public String getBranch() {
        return branch;
    }

    public void setBranch(String branch) {
        this.branch = branch;
    }

}
