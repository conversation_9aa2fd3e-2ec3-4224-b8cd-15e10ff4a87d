package com.adins.mss.base.todolist.form;

import android.app.ActionBar;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.ItemTouchHelper;
import android.text.format.DateFormat;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.adins.mss.base.R;
import com.adins.mss.base.mainmenu.MainMenuActivity;
import com.adins.mss.base.todolist.ToDoList;
import com.adins.mss.base.todolist.form.helper.OnStartDragListener;
import com.adins.mss.base.todolist.form.helper.SimpleItemTouchHelperCallback;
import com.adins.mss.dao.TaskH;
import com.adins.mss.dao.TaskHSequence;
import com.adins.mss.foundation.db.dataaccess.TaskHSequenceDataAccess;

import org.acra.ACRA;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * Created by ACER 471 on 3/22/2017.
 */

public class SurveyPlanFragment extends Fragment implements OnStartDragListener {

    public static final String REQ_PRIORITY_LIST = "REQ_PRIORITY_LIST";
    public static final String REQ_LOG_LIST = "REQ_LOG_LIST";
    public static final String REQ_STATUS_LIST = "REQ_STATUS_LIST";
    public static final String BUND_KEY_REQ = "BUND_KEY_REQ";
    //    /**
//     * Use this factory method to create a new instance of
//     * this fragment using the provided parameters.
//     *
//     * @param keyRequest Parameter 1.
//     * @return A new instance of fragment BlankFragment.
//     */
//     TODO: Rename and change types and number of parameters
//    public static SurveyPlanFragment newInstance(String keyRequest) {
//        SurveyPlanFragment fragment = new SurveyPlanFragment();
//        Bundle args = new Bundle();
//        args.putString(BUND_KEY_REQ, keyRequest);
//        fragment.setArguments(args);
//        return fragment;
//    }
    RecyclerView recyclerView;
    private String keyRequest;
    private SurveyListAdapter adapter;
    private ItemTouchHelper mItemTouchHelper;
    private List<TaskH> listTaskH;
    private AllHeaderViewerFragment.TaskHeaderAdapter taskHeaderAdapter;
    private LinearLayout layoutView;
    private TextView dataNotFound;

//    public SurveyPlanFragment(List<TaskH> listTaskH) {
//        this.listTaskH = listTaskH;
//    }

    public SurveyPlanFragment() {
//        this.listTaskH = taskHeaderAdapter.listTaskH2;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.survey_plan_layout, container, false);
        recyclerView = (RecyclerView) view.findViewById(R.id.recyclerList);
        Button btnSavePlan = (Button) view.findViewById(R.id.btnSavePlan);
        dataNotFound = (TextView) view.findViewById(R.id.txv_data_not_found);

        btnSavePlan.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                TaskHSequenceDataAccess.clean(getActivity());
                TaskHSequenceDataAccess.insertAllNewTaskHSeq(getActivity(), adapter.getListTaskH());
                Toast.makeText(getActivity(), getResources().getString(R.string.plan_saved), Toast.LENGTH_SHORT).show();
//                Fragment fragment = new SurveyPlanFragment();
                FragmentTransaction transaction = MainMenuActivity.fragmentManager.beginTransaction();
                transaction.setCustomAnimations(R.anim.activity_open_translate, R.anim.activity_close_scale, R.anim.activity_open_scale, R.anim.activity_close_translate);
                getFragmentManager().popBackStack();
            }
        });
        return view;
    }


    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

//        if (getArguments() != null) {
//            keyRequest = getArguments().getString(BUND_KEY_REQ);
//        }

//        ACRA.getErrorReporter().putCustomData("LAST_CLASS_ACCESSED", getClass().getSimpleName());

        loadListView();

        adapter = new SurveyListAdapter(getActivity(), this, listTaskH);

        recyclerView.setHasFixedSize(true);
        recyclerView.setAdapter(adapter);
        recyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));

        layoutView = (LinearLayout) view.findViewById(R.id.layoutView);

        ItemTouchHelper.Callback callback = new SimpleItemTouchHelperCallback(adapter);
        mItemTouchHelper = new ItemTouchHelper(callback);
        mItemTouchHelper.attachToRecyclerView(recyclerView);


    }

    public void loadListView() {
        listTaskH = null;
        try {
//            if (keyRequest != null) {
//                if (REQ_PRIORITY_LIST.equals(keyRequest))
            listTaskH = ToDoList.getListTaskInPriority(getActivity(), 0, null);
            List<TaskHSequence> taskHSequences = TaskHSequenceDataAccess.getAllOrderAsc(getContext());
            List<TaskH> taskHList = new ArrayList<>();
            if (taskHSequences.isEmpty()) {
                TaskHSequenceDataAccess.insertAllNewTaskHSeq(getContext(), listTaskH);
                taskHSequences = TaskHSequenceDataAccess.getAllOrderAsc(getContext());

            }
            for (int i = 0; i < taskHSequences.size(); i++) {
                taskHList.add(taskHSequences.get(i).getTaskH());
            }
            listTaskH = taskHList;
//                else if (REQ_STATUS_LIST.equals(keyRequest))
//                    listTaskH = TaskHDataAccess.getAllTaskInStatus(getActivity(), GlobalData.getSharedGlobalData().getUser().getUuid_user());
//                else if (REQ_LOG_LIST.equals(keyRequest))
//                    listTaskH = TaskHDataAccess.getAllSentTask(getActivity(), GlobalData.getSharedGlobalData().getUser().getUuid_user());
//                else
//                    listTaskH = new ArrayList<TaskH>();
//            } else {
//                listTaskH = new ArrayList<TaskH>();
//            }
            if (listTaskH == null || listTaskH.isEmpty()) {
                dataNotFound.setVisibility(View.VISIBLE);
                layoutView.setBackgroundResource(R.drawable.bg_notfound);
            }
        } catch (Exception e) {
            ACRA.getErrorReporter().putCustomData("errorLoadListView", e.getMessage());
            ACRA.getErrorReporter().putCustomData("errorLoadListView", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
            ACRA.getErrorReporter().handleSilentException(new Exception("Exception saat check TaskH"));
            e.printStackTrace();
        }
    }

    @Override
    public void onStartDrag(RecyclerView.ViewHolder viewHolder) {
        mItemTouchHelper.startDrag(viewHolder);
    }

    @Override
    public void onResume() {
        super.onResume();
        getActivity().getActionBar().removeAllTabs();
        getActivity().getActionBar().setTitle(getString(R.string.title_mn_tasklist));
        getActivity().getActionBar().setNavigationMode(ActionBar.NAVIGATION_MODE_STANDARD);
    }
}
