package com.adins.mss.base.todo.form;

import com.adins.mss.foundation.http.MssRequestType;
import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 */
public class JsonRequestScheme extends MssRequestType {
    /**
     * Property task
     */
    @SerializedName("task")
    String task;

    /**
     * Property uuid_user
     */
    @SerializedName("uuid_user")
    String uuid_user;

    /**
     * Property uuid_scheme
     */
    @SerializedName("uuid_scheme")
    String uuid_scheme;

    /**
     * Gets the task
     */
    public String getTask() {
        return this.task;
    }

    /**
     * Sets the task
     */
    public void setTask(String value) {
        this.task = value;
    }

    /**
     * Gets the uuid_user
     */
    public String getUuid_user() {
        return this.uuid_user;
    }

    /**
     * Sets the uuid_user
     */
    public void setUuid_user(String value) {
        this.uuid_user = value;
    }

    /**
     * Gets the uuid_scheme
     */
    public String getUuid_scheme() {
        return this.uuid_scheme;
    }

    /**
     * Sets the uuid_scheme
     */
    public void setUuid_scheme(String value) {
        this.uuid_scheme = value;
    }
}
