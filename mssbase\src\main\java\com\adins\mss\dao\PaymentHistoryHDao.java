package com.adins.mss.dao;

import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;

import com.adins.mss.dao.PaymentHistoryH;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TR_PAYMENTHISTORY_H".
*/
public class PaymentHistoryHDao extends AbstractDao<PaymentHistoryH, String> {

    public static final String TABLENAME = "TR_PAYMENTHISTORY_H";

    /**
     * Properties of entity PaymentHistoryH.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_payment_history_h = new Property(0, String.class, "uuid_payment_history_h", true, "UUID_PAYMENT_HISTORY_H");
        public final static Property Uuid_task_h = new Property(1, String.class, "uuid_task_h", false, "UUID_TASK_H");
        public final static Property Agreement_no = new Property(2, String.class, "agreement_no", false, "AGREEMENT_NO");
        public final static Property Branch_code = new Property(3, String.class, "branch_code", false, "BRANCH_CODE");
        public final static Property Value_date = new Property(4, java.util.Date.class, "value_date", false, "VALUE_DATE");
        public final static Property Payment_amount = new Property(5, String.class, "payment_amount", false, "PAYMENT_AMOUNT");
        public final static Property Installment_amount = new Property(6, String.class, "installment_amount", false, "INSTALLMENT_AMOUNT");
        public final static Property Installment_number = new Property(7, String.class, "installment_number", false, "INSTALLMENT_NUMBER");
        public final static Property Transaction_type = new Property(8, String.class, "transaction_type", false, "TRANSACTION_TYPE");
        public final static Property Wop_code = new Property(9, String.class, "wop_code", false, "WOP_CODE");
        public final static Property Receipt_no = new Property(10, String.class, "receipt_no", false, "RECEIPT_NO");
        public final static Property Posting_date = new Property(11, java.util.Date.class, "posting_date", false, "POSTING_DATE");
        public final static Property Dtm_upd = new Property(12, java.util.Date.class, "dtm_upd", false, "DTM_UPD");
        public final static Property Usr_upd = new Property(13, String.class, "usr_upd", false, "USR_UPD");
        public final static Property Dtm_crt = new Property(14, java.util.Date.class, "dtm_crt", false, "DTM_CRT");
        public final static Property Usr_crt = new Property(15, String.class, "usr_crt", false, "USR_CRT");
    };


    public PaymentHistoryHDao(DaoConfig config) {
        super(config);
    }
    
    public PaymentHistoryHDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TR_PAYMENTHISTORY_H\" (" + //
                "\"UUID_PAYMENT_HISTORY_H\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_payment_history_h
                "\"UUID_TASK_H\" TEXT," + // 1: uuid_task_h
                "\"AGREEMENT_NO\" TEXT," + // 2: agreement_no
                "\"BRANCH_CODE\" TEXT," + // 3: branch_code
                "\"VALUE_DATE\" INTEGER," + // 4: value_date
                "\"PAYMENT_AMOUNT\" TEXT," + // 5: payment_amount
                "\"INSTALLMENT_AMOUNT\" TEXT," + // 6: installment_amount
                "\"INSTALLMENT_NUMBER\" TEXT," + // 7: installment_number
                "\"TRANSACTION_TYPE\" TEXT," + // 8: transaction_type
                "\"WOP_CODE\" TEXT," + // 9: wop_code
                "\"RECEIPT_NO\" TEXT," + // 10: receipt_no
                "\"POSTING_DATE\" INTEGER," + // 11: posting_date
                "\"DTM_UPD\" INTEGER," + // 12: dtm_upd
                "\"USR_UPD\" TEXT," + // 13: usr_upd
                "\"DTM_CRT\" INTEGER," + // 14: dtm_crt
                "\"USR_CRT\" TEXT);"); // 15: usr_crt
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TR_PAYMENTHISTORY_H\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, PaymentHistoryH entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_payment_history_h());
 
        String uuid_task_h = entity.getUuid_task_h();
        if (uuid_task_h != null) {
            stmt.bindString(2, uuid_task_h);
        }
 
        String agreement_no = entity.getAgreement_no();
        if (agreement_no != null) {
            stmt.bindString(3, agreement_no);
        }
 
        String branch_code = entity.getBranch_code();
        if (branch_code != null) {
            stmt.bindString(4, branch_code);
        }
 
        java.util.Date value_date = entity.getValue_date();
        if (value_date != null) {
            stmt.bindLong(5, value_date.getTime());
        }
 
        String payment_amount = entity.getPayment_amount();
        if (payment_amount != null) {
            stmt.bindString(6, payment_amount);
        }
 
        String installment_amount = entity.getInstallment_amount();
        if (installment_amount != null) {
            stmt.bindString(7, installment_amount);
        }
 
        String installment_number = entity.getInstallment_number();
        if (installment_number != null) {
            stmt.bindString(8, installment_number);
        }
 
        String transaction_type = entity.getTransaction_type();
        if (transaction_type != null) {
            stmt.bindString(9, transaction_type);
        }
 
        String wop_code = entity.getWop_code();
        if (wop_code != null) {
            stmt.bindString(10, wop_code);
        }
 
        String receipt_no = entity.getReceipt_no();
        if (receipt_no != null) {
            stmt.bindString(11, receipt_no);
        }
 
        java.util.Date posting_date = entity.getPosting_date();
        if (posting_date != null) {
            stmt.bindLong(12, posting_date.getTime());
        }
 
        java.util.Date dtm_upd = entity.getDtm_upd();
        if (dtm_upd != null) {
            stmt.bindLong(13, dtm_upd.getTime());
        }
 
        String usr_upd = entity.getUsr_upd();
        if (usr_upd != null) {
            stmt.bindString(14, usr_upd);
        }
 
        java.util.Date dtm_crt = entity.getDtm_crt();
        if (dtm_crt != null) {
            stmt.bindLong(15, dtm_crt.getTime());
        }
 
        String usr_crt = entity.getUsr_crt();
        if (usr_crt != null) {
            stmt.bindString(16, usr_crt);
        }
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public PaymentHistoryH readEntity(Cursor cursor, int offset) {
        PaymentHistoryH entity = new PaymentHistoryH( //
            cursor.getString(offset + 0), // uuid_payment_history_h
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // uuid_task_h
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // agreement_no
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // branch_code
            cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)), // value_date
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // payment_amount
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // installment_amount
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7), // installment_number
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8), // transaction_type
            cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9), // wop_code
            cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10), // receipt_no
            cursor.isNull(offset + 11) ? null : new java.util.Date(cursor.getLong(offset + 11)), // posting_date
            cursor.isNull(offset + 12) ? null : new java.util.Date(cursor.getLong(offset + 12)), // dtm_upd
            cursor.isNull(offset + 13) ? null : cursor.getString(offset + 13), // usr_upd
            cursor.isNull(offset + 14) ? null : new java.util.Date(cursor.getLong(offset + 14)), // dtm_crt
            cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15) // usr_crt
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, PaymentHistoryH entity, int offset) {
        entity.setUuid_payment_history_h(cursor.getString(offset + 0));
        entity.setUuid_task_h(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setAgreement_no(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setBranch_code(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setValue_date(cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)));
        entity.setPayment_amount(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setInstallment_amount(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setInstallment_number(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
        entity.setTransaction_type(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
        entity.setWop_code(cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9));
        entity.setReceipt_no(cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10));
        entity.setPosting_date(cursor.isNull(offset + 11) ? null : new java.util.Date(cursor.getLong(offset + 11)));
        entity.setDtm_upd(cursor.isNull(offset + 12) ? null : new java.util.Date(cursor.getLong(offset + 12)));
        entity.setUsr_upd(cursor.isNull(offset + 13) ? null : cursor.getString(offset + 13));
        entity.setDtm_crt(cursor.isNull(offset + 14) ? null : new java.util.Date(cursor.getLong(offset + 14)));
        entity.setUsr_crt(cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(PaymentHistoryH entity, long rowId) {
        return entity.getUuid_payment_history_h();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(PaymentHistoryH entity) {
        if(entity != null) {
            return entity.getUuid_payment_history_h();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
}
