package com.adins.mss.base.dynamicform.form.questions.viewholder;

import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.adins.mss.base.R;
import com.adins.mss.base.dynamicform.form.questions.OnQuestionClickListener;
import com.adins.mss.foundation.questiongenerator.QuestionBean;

public class ReviewSubmitLayerViewHolder extends RecyclerView.ViewHolder {

    private final RelativeLayout layout;
    private final TextView mLabelNo;
    private final TextView mQuestionLabel;
    private final TextView mQuestionAnswer;
    private final OnQuestionClickListener listener;
    private QuestionBean bean;

    public ReviewSubmitLayerViewHolder(View itemView, OnQuestionClickListener listener) {
        super(itemView);
        layout = (RelativeLayout) itemView.findViewById(R.id.submitLayerReviewLayout);
        mLabelNo = (TextView) itemView.findViewById(R.id.questionNoLabel);
        mQuestionLabel = (TextView) itemView.findViewById(R.id.questionTextLabel);
        mQuestionAnswer = (TextView) itemView.findViewById(R.id.questionTextAnswer);
        this.listener = listener;
    }
    public void bind(final QuestionBean item, final int group, final int number) {
        bean = item;
        mLabelNo.setText(number + ".");
        String qLabel = bean.getQuestion_label();
        mQuestionLabel.setText(qLabel);


        String qAnswer = bean.getAnswer();
        mQuestionAnswer.setText(qAnswer);
        layout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                listener.onReviewClickListener(bean, group, number - 1);
            }
        });
    }
}
