package com.adins.mss.dao;

import com.adins.mss.dao.DaoSession;
import de.greenrobot.dao.DaoException;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "TR_ERROR_LOG".
 */
public class ErrorLog {

    /** Not-null value. */
     @SerializedName("uuid_error_log")
    private String uuid_error_log;
     @SerializedName("description")
    private String error_description;
     @SerializedName("device_name")
    private String device_name;
     @SerializedName("dtm_activity")
    private java.util.Date dtm_activity;
     @SerializedName("uuid_user")
    private String uuid_user;
     @SerializedName("task_id")
    private String task_id;

    /** Used to resolve relations */
    private transient DaoSession daoSession;

    /** Used for active entity operations. */
    private transient ErrorLogDao myDao;

    private User user;
    private String user__resolvedKey;

    private TaskH taskH;
    private String taskH__resolvedKey;


    public ErrorLog() {
    }

    public ErrorLog(String uuid_error_log) {
        this.uuid_error_log = uuid_error_log;
    }

    public ErrorLog(String uuid_error_log, String error_description, String device_name, java.util.Date dtm_activity, String uuid_user, String task_id) {
        this.uuid_error_log = uuid_error_log;
        this.error_description = error_description;
        this.device_name = device_name;
        this.dtm_activity = dtm_activity;
        this.uuid_user = uuid_user;
        this.task_id = task_id;
    }

    /** called by internal mechanisms, do not call yourself. */
    public void __setDaoSession(DaoSession daoSession) {
        this.daoSession = daoSession;
        myDao = daoSession != null ? daoSession.getErrorLogDao() : null;
    }

    /** Not-null value. */
    public String getUuid_error_log() {
        return uuid_error_log;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_error_log(String uuid_error_log) {
        this.uuid_error_log = uuid_error_log;
    }

    public String getError_description() {
        return error_description;
    }

    public void setError_description(String error_description) {
        this.error_description = error_description;
    }

    public String getDevice_name() {
        return device_name;
    }

    public void setDevice_name(String device_name) {
        this.device_name = device_name;
    }

    public java.util.Date getDtm_activity() {
        return dtm_activity;
    }

    public void setDtm_activity(java.util.Date dtm_activity) {
        this.dtm_activity = dtm_activity;
    }

    public String getUuid_user() {
        return uuid_user;
    }

    public void setUuid_user(String uuid_user) {
        this.uuid_user = uuid_user;
    }

    public String getTask_id() {
        return task_id;
    }

    public void setTask_id(String task_id) {
        this.task_id = task_id;
    }

    /** To-one relationship, resolved on first access. */
    public User getUser() {
        String __key = this.uuid_user;
        if (user__resolvedKey == null || user__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            UserDao targetDao = daoSession.getUserDao();
            User userNew = targetDao.load(__key);
            synchronized (this) {
                user = userNew;
            	user__resolvedKey = __key;
            }
        }
        return user;
    }

    public void setUser(User user) {
        synchronized (this) {
            this.user = user;
            uuid_user = user == null ? null : user.getUuid_user();
            user__resolvedKey = uuid_user;
        }
    }

    /** To-one relationship, resolved on first access. */
    public TaskH getTaskH() {
        String __key = this.task_id;
        if (taskH__resolvedKey == null || taskH__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            TaskHDao targetDao = daoSession.getTaskHDao();
            TaskH taskHNew = targetDao.load(__key);
            synchronized (this) {
                taskH = taskHNew;
            	taskH__resolvedKey = __key;
            }
        }
        return taskH;
    }

    public void setTaskH(TaskH taskH) {
        synchronized (this) {
            this.taskH = taskH;
            task_id = taskH == null ? null : taskH.getUuid_task_h();
            taskH__resolvedKey = task_id;
        }
    }

    /** Convenient call for {@link AbstractDao#delete(Object)}. Entity must attached to an entity context. */
    public void delete() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.delete(this);
    }

    /** Convenient call for {@link AbstractDao#update(Object)}. Entity must attached to an entity context. */
    public void update() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.update(this);
    }

    /** Convenient call for {@link AbstractDao#refresh(Object)}. Entity must attached to an entity context. */
    public void refresh() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.refresh(this);
    }

}
