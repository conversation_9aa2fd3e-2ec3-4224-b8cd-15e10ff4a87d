package com.adins.mss.base.todolist.form;

import android.annotation.SuppressLint;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.Intent;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;

import androidx.core.app.NotificationCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentTransaction;

import com.adins.mss.base.AppContext;
import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.mainmenu.MainMenuActivity;
import com.adins.mss.base.timeline.TimelineManager;
import com.adins.mss.base.todo.form.JsonRequestScheme;
import com.adins.mss.base.todo.form.JsonResponseScheme;
import com.adins.mss.base.todolist.ToDoList;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.ReminderPo;
import com.adins.mss.dao.Scheme;
import com.adins.mss.dao.TaskH;
import com.adins.mss.dao.User;
import com.adins.mss.foundation.db.dataaccess.ReminderPoDataAccess;
import com.adins.mss.foundation.db.dataaccess.SchemeDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskHDataAccess;
import com.adins.mss.foundation.db.dataaccess.TimelineDataAccess;
import com.adins.mss.foundation.db.dataaccess.TimelineTypeDataAccess;
import com.adins.mss.foundation.formatter.Formatter;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.http.MssRequestType;
import com.adins.mss.base.todolist.form.JsonResponseTaskList.TaskReminderPo;
import com.adins.mss.foundation.notification.Notification;

import org.acra.ACRA;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.services.NotificationThread.LOG_NOTIFICATION_KEY;
import static com.services.NotificationThread.getNotificationIcon;
import static java.lang.Thread.NORM_PRIORITY;

@SuppressLint("NewApi")
public class TaskListTask extends AsyncTask<Void, Void, Boolean>{
    private FragmentActivity activity;
    private ProgressDialog progressDialog;
    private String errMsg = null;
    private String messageWait;
    private String messageEmpty;
    private int contentFrame;

    /*Penambahan Task Reminder PO*/
    private boolean isShowNotifReminderPo = false;
    private int countNotifReminderPo = 0;

    private Integer taskListType = null;

    public TaskListTask(FragmentActivity mainActivity, String messageWait, String messageEmpty, int contentFrame) {
        this.activity = mainActivity;
        this.messageWait = messageWait;
        this.messageEmpty = messageEmpty;
        this.contentFrame = contentFrame;
        this.taskListType = Global.TASK_LIST_TYPE_DEFAULT;
    }

    public TaskListTask(FragmentActivity mainActivity, String messageWait, String messageEmpty, int contentFrame, int taskListType) {
        this.activity = mainActivity;
        this.messageWait = messageWait;
        this.messageEmpty = messageEmpty;
        this.contentFrame = contentFrame;
        this.taskListType = taskListType;
    }

    @Override
    protected void onPreExecute() {
        this.progressDialog = ProgressDialog.show(activity, "", this.messageWait, true);
    }

    @Override
    protected Boolean doInBackground(Void... params) {
        if(Tool.isInternetconnected(activity)) {
            String result;
            User user = GlobalData.getSharedGlobalData().getUser();
            MssRequestType requestType = new MssRequestType();
            requestType.setAudit(GlobalData.getSharedGlobalData().getAuditData());
            requestType.addImeiAndroidIdToUnstructured();

            String json = GsonHelper.toJson(requestType);
            String url = GlobalData.getSharedGlobalData().getURL_GET_TASKLIST();
            boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
            boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
            HttpCryptedConnection httpConn = new HttpCryptedConnection(activity, encrypt, decrypt);
            HttpConnectionResult serverResult = null;
            try {
                serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
            } catch (Exception e) {
                e.printStackTrace();
                errMsg = e.getMessage();
            }


            List<String> listUuidTaskH = new ArrayList<String>();

            if (serverResult != null) {
                if (serverResult.isOK()) {
                    try {
                        result = serverResult.getResult();
                        JsonResponseTaskList taskList = GsonHelper.fromJson(result, JsonResponseTaskList.class);
                        if (taskList.getStatus().getCode() == 0) {
                            List<TaskH> listTaskH = taskList.getListTaskList();
                            if (listTaskH != null && !listTaskH.isEmpty()) {

                                // bong 19 may 15 - delete all new taskH local before replace with the new ones from server
                                String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                                TaskHDataAccess.deleteTaskHByStatus(activity, uuidUser, TaskHDataAccess.STATUS_SEND_INIT);

                                for (TaskH taskH : listTaskH) {
                                    taskH.setUser(user);
                                    taskH.setIs_verification(Global.TRUE_STRING);

                                    String uuid_scheme = taskH.getUuid_scheme();
                                    listUuidTaskH.add(taskH.getUuid_task_h());
                                    Scheme scheme = SchemeDataAccess.getOne(activity, uuid_scheme);
                                    if (scheme != null) {
                                        taskH.setScheme(scheme);
                                        TaskH h = TaskHDataAccess.getOneHeader(activity, taskH.getUuid_task_h());
                                        String uuid_timelineType = TimelineTypeDataAccess.getTimelineTypebyType(activity, Global.TIMELINE_TYPE_TASK).getUuid_timeline_type();
                                        boolean wasInTimeline = TimelineDataAccess.getOneTimelineByTaskH(activity, user.getUuid_user(), taskH.getUuid_task_h(), uuid_timelineType) != null;
                                        if (h != null && h.getStatus() != null) {
                                            if (!ToDoList.isOldTask(h)) {
                                                if (Global.STATUS_TASK_WEB_WAITING.equalsIgnoreCase(taskH.getStatus())) {
                                                    taskH.setStatus(TaskHDataAccess.STATUS_TASK_WAITING);
                                                } else {
                                                    taskH.setStatus(TaskHDataAccess.STATUS_SEND_INIT);
                                                    if (!wasInTimeline) {
                                                        TimelineManager.insertTimeline(activity, taskH);
                                                    }
                                                }
                                                TaskHDataAccess.addOrReplace(activity, taskH);
                                            } else {
                                                if (taskH.getPts_date() != null) {
                                                    h.setPts_date(taskH.getPts_date());
                                                    TaskHDataAccess.addOrReplace(activity, h);
                                                }

                                                if(Global.STATUS_TASK_WEB_PENDING.equalsIgnoreCase(taskH.getStatus())) {
                                                    if (TaskHDataAccess.STATUS_TASK_WAITING.equalsIgnoreCase(h.getStatus())) {
                                                        h.setStatus(TaskHDataAccess.STATUS_SEND_INIT);
                                                        TaskHDataAccess.addOrReplace(
                                                                activity, h);
                                                    } else if(TaskHDataAccess.STATUS_TASK_WAITING_DOWNLOAD.equalsIgnoreCase(h.getStatus())) {
                                                        h.setStatus(TaskHDataAccess.STATUS_SEND_DOWNLOAD);
                                                        TaskHDataAccess.addOrReplace(
                                                                activity, h);
                                                    }
                                                }
                                            }
                                        } else {
                                            if (Global.STATUS_TASK_WEB_WAITING.equalsIgnoreCase(taskH.getStatus())) {
                                                taskH.setStatus(TaskHDataAccess.STATUS_TASK_WAITING);
                                            } else {
                                                taskH.setStatus(TaskHDataAccess.STATUS_SEND_INIT);
                                                if (!wasInTimeline) {
                                                    TimelineManager.insertTimeline(activity, taskH);
                                                }
                                            }
                                            TaskHDataAccess.addOrReplace(activity, taskH);
                                        }

                                        boolean isTaskPromiseVisitSubmit = TaskHDataAccess.isTaskFormPromiseVisitSubmit(activity, uuidUser, taskH.getAppl_no());
                                        String formName = taskH.getScheme().getForm_id();
                                        if (isTaskPromiseVisitSubmit && (Global.FORM_NAME_VISIT_POLO.equalsIgnoreCase(formName) &&
                                                Global.STATUS_TASK_WEB_PENDING.equalsIgnoreCase(taskH.getStatus()))) {
                                            TaskH taskHVisit = TaskHDataAccess.getOneTaskHeader(activity, taskH.getTask_id());
                                            if (null != taskHVisit) {
                                                if (!"1".equals(taskHVisit.getIs_already_download_task())) {
                                                    taskH.setStatus(TaskHDataAccess.STATUS_SEND_INIT);
                                                    taskH.setIs_already_download_task("1");
                                                    TaskHDataAccess.addOrReplace(activity, taskH);
                                                }
                                            }
                                        }
                                    } else {
                                        errMsg = activity.getString(R.string.scheme_not_found);
                                        ACRA.getErrorReporter().putCustomData("uuid Scheme", uuid_scheme);
                                        ACRA.getErrorReporter().handleSilentException(new Exception("Error: Scheme not available in DB. "+ errMsg));
                                    }
                                }
                                List<TaskH> taskHs = TaskHDataAccess.getAllTaskByStatus(activity, GlobalData.getSharedGlobalData().getUser().getUuid_user(), TaskHDataAccess.STATUS_SEND_DOWNLOAD);
                                List<TaskH> taskHWaiting = TaskHDataAccess.getAllTaskByStatus(activity, GlobalData.getSharedGlobalData().getUser().getUuid_user(), TaskHDataAccess.STATUS_TASK_WAITING_DOWNLOAD);
                                taskHs.addAll(taskHWaiting);

                                for (TaskH h : taskHs) {
                                    String uuid_task_h = h.getUuid_task_h();
                                    boolean isSame = false;
                                    for (String uuid_from_server : listUuidTaskH) {
                                        if (uuid_task_h.equals(uuid_from_server)) {
                                            isSame = true;
                                            break;
                                        }
                                    }
                                    if (!isSame) {
                                        TaskHDataAccess.deleteWithRelation(activity, h);
                                        //								Timeline timeline = TimelineDataAccess.getOneTimelineByTask(activity, user.getUuid_user(), Global.TIMELINE_TYPE_TASK, uuid_task_h);
                                        //								TimelineDataAccess.delete(activity, timeline);
                                    }
                                }
                            }

                            // Adding code for get reminder po task from server (2022-08-12)
                            List<TaskReminderPo> listTaskReminderPo = taskList.getListTaskReminderPo();
                            processTaskReminderPo(listTaskReminderPo);

                        } else {
                            errMsg = result;
                        }
                    } catch (Exception e) {
                        errMsg = e.getMessage();
                    }
                } else {
                    errMsg = serverResult.getResult();
                }
                return serverResult.isOK();
            } else {
                return false;
            }
        }else{
            errMsg = activity.getString(R.string.use_offline_mode);
            return false;
        }
    }

    @Override
    protected void onPostExecute(Boolean result) {
        if (progressDialog.isShowing()){
            try {
                progressDialog.dismiss();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (isShowNotifReminderPo) {
            showNotifTaskReminderPo();
        }
        Bundle argument =  new Bundle();
        if(errMsg!=null){
            argument.putBoolean(TaskList_Fragment.BUND_KEY_ISERROR, true);
            argument.putString(TaskList_Fragment.BUND_KEY_MESSAGE, errMsg);
        }else if(result) {
            argument.putBoolean(TaskList_Fragment.BUND_KEY_ISERROR, false);
        }
        else {

            if(!TaskHDataAccess.getAll(activity, GlobalData.getSharedGlobalData().getUser().getUuid_user()).isEmpty()){
                argument.putBoolean(TaskList_Fragment.BUND_KEY_ISERROR, false);
            }else{
                argument.putBoolean(TaskList_Fragment.BUND_KEY_ISERROR, true);
                argument.putString(TaskList_Fragment.BUND_KEY_MESSAGE, errMsg);
            }
        }

        Fragment fragmentTaskList = null;
        if(taskListType == null || taskListType == Global.TASK_LIST_TYPE_DEFAULT){
            fragmentTaskList = new TaskListFragment_new();
        }else if(taskListType == Global.TASK_LIST_TYPE_PROMISE_TO_SURVEY){
            fragmentTaskList = new TaskPromiseToSurveyFragment();
        }else if(taskListType == Global.TASK_LIST_TYPE_PRE_SURVEY) {
            fragmentTaskList = new TaskPreSurveyFragment();
        }else if(taskListType == Global.TASK_LIST_TYPE_OTS){
            fragmentTaskList = new TaskOtsFragment();
        }else if(taskListType == Global.TASK_LIST_TYPE_GUARANTOR){
            fragmentTaskList = new TaskGuarantorFragment();
        }

        if (fragmentTaskList != null) {
            fragmentTaskList.setArguments(argument);
            FragmentTransaction transaction = MainMenuActivity.fragmentManager.beginTransaction();
            transaction.setCustomAnimations(R.anim.activity_open_translate, R.anim.activity_close_scale, R.anim.activity_open_scale, R.anim.activity_close_translate);
            transaction.replace(R.id.content_frame, fragmentTaskList);
            transaction.addToBackStack(null);
            transaction.commit();
        }
//        transaction.commitAllowingStateLoss();
    }

    public void cekScheme(Context context, String uuid_scheme) {
        JsonRequestScheme requestScheme = new JsonRequestScheme();
        requestScheme.setAudit(GlobalData.getSharedGlobalData().getAuditData());
        requestScheme.setUuid_user(GlobalData.getSharedGlobalData().getUser()
                .getUuid_user());
        requestScheme.setUuid_scheme(uuid_scheme);
        requestScheme.setTask(Global.TASK_GETONE);

        String json = GsonHelper.toJson(requestScheme);
        String url = GlobalData.getSharedGlobalData().getURL_GET_SCHEME();
        boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
        boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
        HttpCryptedConnection httpConn = new HttpCryptedConnection(activity, encrypt, decrypt);
        HttpConnectionResult serverResult = null;
        try {
            serverResult = httpConn.requestToServer(url, json,
                    Global.DEFAULTCONNECTIONTIMEOUT);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (serverResult.isOK()) {
            try {
                String result = serverResult.getResult();
                JsonResponseScheme responseScheme = GsonHelper.fromJson(result,
                        JsonResponseScheme.class);
                List<Scheme> schemes = responseScheme.getListScheme();
                try {
                    if (!schemes.isEmpty())
                        SchemeDataAccess.addOrReplace(context, schemes);
                } catch (Exception e) {
                    e.printStackTrace();
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }

    private void processTaskReminderPo(List<TaskReminderPo> listTaskReminderPo) {
        if (null != listTaskReminderPo && !listTaskReminderPo.isEmpty()) {
            List<String> listUuidTaskH = new ArrayList<>();
            for (TaskReminderPo taskPo : listTaskReminderPo) {
                String uuidTaskH = taskPo.getUuidTaskH();
                if (null != uuidTaskH && !"".equals(uuidTaskH)) {
                    listUuidTaskH.add(uuidTaskH);
                    ReminderPo dataReminderPo = ReminderPoDataAccess.getOneByUuidTaskH(activity, uuidTaskH);
                    if (null != dataReminderPo) {
                        String taskCreateDate = Formatter.formatDate(dataReminderPo.getDtm_crt(), Global.DATE_STR_FORMAT2);
                        String currentDate = Formatter.formatDate(new Date(), Global.DATE_STR_FORMAT2);
                        boolean isOldTask = taskCreateDate.compareToIgnoreCase(currentDate) < 0;
                        if (isOldTask) {
                            dataReminderPo.setDtm_crt(new Date());
                            ReminderPoDataAccess.update(activity, dataReminderPo);
                            isShowNotifReminderPo = true;
                        }

                        TaskH taskH = TaskHDataAccess.getOneHeader(activity, uuidTaskH);
                        if (null == taskH) {
                            dataReminderPo.setIs_task_downloaded("0");
                            ReminderPoDataAccess.update(activity, dataReminderPo);
                        }

                    } else {
                        ReminderPo reminderPo = new ReminderPo();
                        String newId = Tool.getUUID();
                        reminderPo.setId(newId);
                        reminderPo.setDtm_crt(new Date());
                        reminderPo.setUuid_task_h(uuidTaskH);
                        reminderPo.setExpired_date(taskPo.getExpireDate());
                        reminderPo.setStatus_po(taskPo.getStatusPo());
                        reminderPo.setUuid_user(GlobalData.getSharedGlobalData().getUser().getUuid_user());
                        TaskH taskH = TaskHDataAccess.getOneHeader(activity, uuidTaskH);
                        if (null != taskH) {
                            reminderPo.setIs_task_downloaded("1");
                        } else {
                            reminderPo.setIs_task_downloaded("0");
                        }
                        ReminderPoDataAccess.add(activity, reminderPo);
                        isShowNotifReminderPo = true;
                    }
                }
            }

            String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
            List<ReminderPo> listReminderPo = ReminderPoDataAccess.getAllTaskReminderPo(activity, uuidUser);
            for (ReminderPo reminderPo : listReminderPo) {
                String uuidTaskH = reminderPo.getUuid_task_h();
                boolean isSame = false;
                for (String uuidFromServer : listUuidTaskH) {
                    if (uuidTaskH.equals(uuidFromServer)) {
                        isSame = true;
                        break;
                    }
                }
                if (!isSame) {
                    ReminderPoDataAccess.delete(activity, reminderPo);
                }
            }

            if (isShowNotifReminderPo) {
                countNotifReminderPo = listTaskReminderPo.size();
            }
        } else {
            String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
            ReminderPoDataAccess.delete(activity, uuidUser);
        }
    }

    private void showNotifTaskReminderPo() {
        String notifTitle = activity.getString(R.string.reminder_po_task);
        String message = activity.getString(R.string.reminder_po_notification, countNotifReminderPo);

        Intent intent = new Intent(activity, AppContext.getInstance().getHomeClass());
        intent.setAction(LOG_NOTIFICATION_KEY);

        PendingIntent pendingIntent = PendingIntent.getActivity(activity, 0, intent,
                PendingIntent.FLAG_CANCEL_CURRENT);
        Notification.getSharedNotification().setDefaultIcon(R.drawable.icon_notif);

        NotificationCompat.Builder builder = new NotificationCompat.Builder(activity);
        builder.setSmallIcon(getNotificationIcon());
        builder.setContentTitle(notifTitle).setNumber(countNotifReminderPo);
        builder.setContentText(message).setNumber(countNotifReminderPo);
        builder.setPriority(NORM_PRIORITY);
        NotificationCompat.BigTextStyle inboxStyle = new NotificationCompat.BigTextStyle();
        // Sets a title for the Inbox in expanded layout
        inboxStyle.setBigContentTitle(notifTitle);
        inboxStyle.bigText(message);
        inboxStyle.setSummaryText(activity.getString(R.string.click_to_open_log));

        builder.setDefaults(android.app.Notification.DEFAULT_ALL);
        builder.setStyle(inboxStyle);
        builder.setAutoCancel(true);
        builder.setContentIntent(pendingIntent);

        NotificationManager mNotificationManager = (NotificationManager) activity.getSystemService(
                Context.NOTIFICATION_SERVICE);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            String channelId = "channel_reminder_po_task";
            NotificationChannel channel = new NotificationChannel(
                    channelId,
                    "Reminder Po Task",
                    NotificationManager.IMPORTANCE_HIGH);
            mNotificationManager.createNotificationChannel(channel);
            builder.setChannelId(channelId);
        }
        mNotificationManager.notify(5, builder.build());
    }
}
