package com.adins.mss.base;

import android.content.Context;
import android.os.Environment;

import com.adins.mss.base.tracking.LocationTrackingSchedule;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.GeneralParameter;
import com.adins.mss.dao.User;
import com.adins.mss.foundation.config.ConfigFileReader;
import com.adins.mss.foundation.db.dataaccess.GeneralParameterDataAccess;
import com.adins.mss.foundation.http.AuditDataType;
import com.adins.mss.foundation.http.AuditDataTypeGenerator;
import com.adins.mss.foundation.http.HttpConnection.ConnectionCryptor;
import com.adins.mss.foundation.oauth2.OAuth2Client;
import com.adins.mss.foundation.oauth2.Token;
import com.adins.mss.foundation.services.AutoSendService;

import java.util.List;
import java.util.Properties;

/**
 * A singleton to hold basic configurations of MSS like URL and encryption flag and other stored data.
 * Avoid modifying this code to add more properties. Create a new class instead outside of this library.
 * <p>
 * GlobalData can be initialized by loading from .properties file by calling loadFromProperties(), or can load GeneralParameter
 * on runtime with loadGeneralParameters()
 * <p>
 * If a modification to this class inside this library is necessary, things that need to be added
 * for each new property are:
 * <li> The new property along with it's setter and getter
 * <li> A constant String which store the key on .properties file
 * <li> Implementation of getting the value from .properties file (using ConfigFileReader) inside
 * loadFromProperties();
 *
 * <AUTHOR>
 */
public class GlobalData {

    //=== Constant ===//
    //Holds the key to map from .properties
    public static final String PROPERTY_FILENAME = "application";
    public static final String PROP_ENCRYPT = "encrypt";

    //=== Phone Data ===//
    public static final String PROP_DECRYPT = "decrypt";
    public static final String PROP_IS_DEVELOPER = "is_developer";
    public static final String PROP_IS_REQUIRED_ACCESS_TOKEN = "is_required_access_token";
    public static final String PROP_CLIENT_ID = "client_id";
    public static final String PROP_OWN_CAMERA = "use_own_camera";
    public static final String PROP_APPLICATION_NAME = "application";
    public static final String PROP_DISABLE_ACRA = "is_disable_acra";
    //	protected String pin;
    private static final String http = "http";
    private static final String https = "https";
    private static final String PROP_URL_MAIN = "url_main";
    private static final String PROP_URL_WEB = "url_web";
    private static final String PROP_URL_SERV_LOGIN = "url_serv_login";
    private static final String PROP_URL_SERV_SYNC = "url_serv_sync";
    private static final String PROP_URL_SERV_TRACKING = "url_serv_tracking";
    private static final String PROP_URL_SERV_SUBMIT = "url_serv_submit";
    private static final String PROP_URL_SERV_PERSONALIZATION = "url_serv_personalization";
    private static final String PROP_URL_SERV_LOCATION_HISTORY = "url_serv_location_history";
    private static final String PROP_URL_SERV_REFRESH_TASK = "url_serv_refresh_task";
    private static final String PROP_URL_SERV_RETRIEVE_TASK_LIST = "url_serv_retrieve_task_list";
    private static final String PROP_URL_SERV_RETRIEVE_NEWS = "url_serv_retrieve_news";
    private static final String PROP_URL_SERV_SVY_PERFORMANCE = "url_serv_svy_performance";
    private static final String PROP_URL_LOGIN = "url_login";
    private static final String PROP_URL_UPDATE_FCM = "url_update_fcm";
    private static final String PROP_URL_CHANGEPASSWORD = "url_changepassword";
    private static final String PROP_URL_GET_ABSENSI = "url_get_absensi";
    private static final String PROP_URL_GET_TASKLIST = "url_get_tasklist";
    private static final String PROP_URL_REFRESHTASK = "url_refreshtask";
    private static final String PROP_URL_SUBMITTASK = "url_submittask";
    private static final String PROP_URL_SUBMITOPENREADTASK = "url_submitopenreadtask";
    private static final String PROP_URL_GET_QUESTIONSET = "url_get_questionset";
    private static final String PROP_URL_GET_VERIFICATION = "url_get_verification";
    private static final String PROP_URL_GET_VERIFICATION_IMAGE = "url_get_verification_image";
    private static final String PROP_URL_GET_IMAGE = "url_get_image";
    private static final String PROP_URL_GET_SCHEME = "url_get_scheme";
    private static final String PROP_URL_GET_SVYPERFORMANCE = "url_get_svyperformance";
    private static final String PROP_URL_GET_LOOKUP = "url_get_lookup";
    private static final String PROP_URL_GET_LIST_REASSIGNMENT = "url_get_list_reassignment";
    private static final String PROP_URL_GET_DETAIL_REASSIGNMENT = "url_get_detail_reassignment";
    private static final String PROP_URL_GET_LIST_ASSIGNMENT = "url_get_list_assignment";
    private static final String PROP_URL_GET_DETAIL_ASSIGNMENT = "url_get_detail_assignment";
    private static final String PROP_URL_GET_DETAIL_ORDER = "url_get_detail_order";
    private static final String PROP_URL_GET_DETAIL_TASK = "url_get_detail_Task";
    private static final String PROP_URL_SUBMIT_ASSIGN = "url_submit_assign";
    private static final String PROP_URL_GET_LIST_VERIFICATION = "url_get_list_verification";
    private static final String PROP_URL_GET_LIST_APPROVAL = "url_get_list_approval";
    private static final String PROP_URL_SENDDEPOSITREPORT = "url_senddepositreport";
    private static final String PROP_URL_GET_PAYMENTHISTORY = "url_get_paymenthistory";
    private static final String PROP_URL_GET_INSTALLMENTSCHEDULE = "url_get_installmentschedule";
    private static final String PROP_URL_GET_COLLECTIONHISTORY = "url_get_collectionhistory";
    private static final String PROP_URL_SUBMITVERIFICATIONTASK = "url_submitverificationtask";
    private static final String PROP_URL_SUBMITAPPROVALTASK = "url_submitapprovaltask";
    private static final String PROP_URL_GET_CONTENTNEWS = "url_get_contentnews";
    private static final String PROP_URL_CHECKORDER = "url_checkorder";
    private static final String PROP_URL_GET_TASK = "url_get_task";
    private static final String PROP_URL_GET_NEWSHEADER = "url_get_newsheader";
    private static final String PROP_URL_GET_NEWSCONTENT = "url_get_newscontent";
    private static final String PROP_URL_GET_LIST_CANCELORDER = "url_get_list_cancelorder";
    private static final String PROP_URL_GET_DETAIL_CANCELORDER = "url_get_detail_cancelorder";
    private static final String PROP_URL_GET_CANCELORDER = "url_get_cancelorder";
    private static final String PROP_URL_SUBMIT_TRACK = "url_submit_track";
    private static final String PROP_URL_RETRIECECOLLECTIONTASK = "url_retriececollectiontask";
    private static final String PROP_URL_SYNCPARAM = "url_syncparam";
    private static final String PROP_URL_SYNCTABLE = "url_synctable";
    private static final String PROP_URL_GET_REPORTSUMMARY = "url_get_reportsummary";
    private static final String PROP_URL_SUBMIT_RESCHEDULE = "url_submit_reschedule";
    private static final String PROP_URL_GET_LIST_USER = "url_get_list_user";
    private static final String PROP_URL_CHECK_UPDATE = "url_check_update";
    private static final String PROP_URL_GET_RECAPITULATE = "url_get_recapitulate";
    private static final String PROP_URL_SYNCPARAM_CONSTRAINT = "url_syncparam_constraint";
    private static final String PROP_URL_SYNCTABLE_CONSTRAINT = "url_synctable_constraint";
    private static final String PROP_URL_UPDATE_CASH_ON_HAND = "url_updatecashonhand";
    private static final String PROP_URL_GET_CLOSING_TASK = "url_get_closing_task";
    private static final String PROP_URL_SUBMIT_PRINT_COUNT = "url_submit_print_count";
//	private static final String PROP_URL_GET_QUESTIONSET = "url_get_questionset";
    private static final String PROP_URL_SYNCPUSHTABLE = "url_sync_pushtable";
    private static final String PROP_URL_CHECKSTATUSTASK = "url_check_statustask";
    private static final String PROP_URL_INVITATION_ESIGN = "url_invitation_esign";
    private static final String PROP_URL_SUBMIT_LAYER = "url_submit_layer";
    private static final String PROP_URL_RESULT_TELECHECK = "url_result_telecheck";
    private static final String PROP_URL_GET_DSR = "url_get_dsr";
    private static final String PROP_URL_REGISTRATION_CHECK = "url_registration_check";
    private static final String PROP_URL_GET_OTR = "url_get_otr";

    //	-----------------------------------
    private static final String PROP_URL_RV_NUMBER = "url_submit_rv_number";
    private static final String PROP_SYNC_RV_NUMBERS = "url_sync_rv_numbers";
    private static final String PROP_URL_GET_DEALERS = "url_get_dealers";
    private static final String PROP_URL_GET_TASK_LOG = "url_get_task_log";
    private static final String PROP_URL_GET_LOOKUP_ANSWER = "url_get_lookup_answer";
    private static final String PROP_URL_CHECK_RESUBMIT = "url_check_resubmit";
    private static final String PROP_IS_SECURE_CONNECTION = "is_secure_connection";
    private static final String PROP_MAX_LOG = "max_log";
    private static final String PROP_LOG_AGE = "log_age";
    private static final String PROP_DEF_MAX_LENGHT = "default_max_lenght";
    private static final String PROP_THUMB_WIDTH = "thumb_width";
    private static final String PROP_THUMB_HEIGHT = "thumb_height";
    private static final String PROP_CAMERA_QUALITY = "camera_quality";
    private static final String PROP_MAX_PHOTO_SIZE = "max_photo_size";
    private static final String PROP_INTERVAL_TRACKING = "interval_tracking";
    private static final String PROP_INTERVAL_AUTOSEND = "interval_autosend";
    private static final String PROP_INTERVAL_GPS_TIMEOUT = "interval_gps_timeout";
    private static final String PROP_CHANGE_UUID = "change_uuid";
    private static final String PROP_UUID_DIVIDER = "divider_uuid";
    private static final String PROP_IS_BYPASSROOT = "is_bypassroot";
    /*Printer Device*/
    private static final String PROP_LIST_PRINTER = "printer_device";
    //CR external DB
    private static final String PROP_URL_SYNC_FILES = "url_sync_file";
    private static final String PROP_SAVE_PATH = "save_path";
    private static final String PROP_USE_EXTERNAL_STORAGE = "use_external_storage";
    private static final String PROP_DEVMODE = "is_developer_mode";

    private static final String PROP_URL_SUBMIT_DKCP = "url_submit_dkcp";
    private static final String PROP_URL_CHECK_BIOMETRIC = "url_check_biometric";

    private static GlobalData sharedGlobalData;
    protected String locale;
    int keepTimelineInDays;
    int maxDataInLog;

    private static final String PROP_URL_MULTILOGIN = "url_multilogin";
    private static final String PROP_URL_RECOVERY_PASSWORD = "url_reset_password";
    private static final String PROP_URL_PROMISE_SURVEY = "url_promise_survey";
    private static final String PROP_URL_INQUIRY_TASKH = "url_inquiry_taskh";
    private static final String PROP_URL_INQUIRY_TASKD = "url_inquiry_taskd";

    private static final String PROP_URL_RETRIEVE_TASK_UPDATE = "url_retrieve_task_update";
    private static final String PROP_URL_SUBMIT_TASK_UPDATE = "url_submit_task_update";
    private static final String PROP_URL_GET_TASK_H_REMINDER_PO = "url_get_task_reminder_po";

    private static final String PROP_URL_GET_CEK_REFERANTOR = "url_cek_referantor";
    private static final String PROP_URL_VALIDATION_CHECK_QUESTION = "url_validation_check";

    /**
     * Property URL_LOGIN
     */
    String URL_LOGIN;
    /**
     * Property URL_LOGIN
     */
    String URL_UPDATE_FCM;
    /**
     * Property URL_CHANGEPASSWORD
     */
    String URL_CHANGEPASSWORD;
    /**
     * Property URL_GET_ABSENSI
     */
    String URL_GET_ABSENSI;
    /**
     * Property URL_GET_TASKLIST
     */
    String URL_GET_TASKLIST;
    /**
     * Property URL_REFRESHTASK
     */
    String URL_REFRESHTASK;
    /**
     * Property URL_SUBMITTASK
     */
    String URL_SUBMITTASK;
    /**
     * Property URL_SUBMITOPENREADTASK
     */
    String URL_SUBMITOPENREADTASK;
    /**
     * Property URL_GET_QUESTIONSET
     */
    String URL_GET_QUESTIONSET;
    /**
     * Property URL_GET_VERIFICATION
     */
    String URL_GET_VERIFICATION;
    /**
     * Property URL_GET_IMAGE
     */
    String URL_GET_IMAGE;
    /**
     * Property URL_GET_SCHEME
     */
    String URL_GET_SCHEME;
    /**
     * Property URL_PRESUBMIT
     */
    String URL_PRESUBMIT;
    /**
     * Property URL_GET_SVYPERFORMANCE
     */
    String URL_GET_SVYPERFORMANCE;
    /**
     * Property URL_GET_LOOKUP
     */
    String URL_GET_LOOKUP;
    /**
     * Property URL_GET_LIST_REASSIGNMENT
     */
    String URL_GET_LIST_REASSIGNMENT;
    /**
     * Property URL_GET_DETAIL_REASSIGNMENT
     */
    String URL_GET_DETAIL_REASSIGNMENT;
    /**
     * Property URL_GET_LIST_ASSIGNMENT
     */
    String URL_GET_LIST_ASSIGNMENT;
    /**
     * Property URL_GET_DETAIL_ASSIGNMENT
     */
    String URL_GET_DETAIL_ASSIGNMENT;
    /**
     * Property URL_GET_DETAIL_ORDER
     */
    String URL_GET_DETAIL_ORDER;
    /**
     * Property URL_GET_DETAIL_TASK
     */
    String URL_GET_DETAIL_TASK;
    /**
     * Property URL_SUBMIT_ASSIGN
     */
    String URL_SUBMIT_ASSIGN;
    /**
     * Property URL_GET_LIST_VERIFICATION
     */
    String URL_GET_LIST_VERIFICATION;
    /**
     * Property URL_GET_LIST_APPROVAL
     */
    String URL_GET_LIST_APPROVAL;
    /**
     * Property URL_SENDDEPOSITREPORT
     */
    String URL_SENDDEPOSITREPORT;
    /**
     * Property URL_GET_PAYMENTHISTORY
     */
    String URL_GET_PAYMENTHISTORY;
    /**
     * Property URL_GET_INSTALLMENTSCHEDULE
     */
    String URL_GET_INSTALLMENTSCHEDULE;
//	-----------------------------------
    /**
     * Property URL_GET_COLLECTIONHISTORY
     */
    String URL_GET_COLLECTIONHISTORY;
    /**
     * Property URL_SUBMITVERIFICATIONTASK
     */
    String URL_SUBMITVERIFICATIONTASK;
    /**
     * Property URL_SUBMITAPPROVALTASK
     */
    String URL_SUBMITAPPROVALTASK;
    /**
     * Property URL_GET_CONTENTNEWS
     */
    String URL_GET_CONTENTNEWS;
    /**
     * Property URL_CHECKORDER
     */
    String URL_CHECKORDER;
    /**
     * Property URL_GET_TASK
     */
    String URL_GET_TASK;
    /**
     * Property URL_GET_NEWSHEADER
     */
    String URL_GET_NEWSHEADER;
    /**
     * Property URL_GET_NEWSCONTENT
     */
    String URL_GET_NEWSCONTENT;
    /**
     * Property URL_GET_LIST_CANCELORDER
     */
    String URL_GET_LIST_CANCELORDER;
    /**
     * Property URL_GET_DETAIL_CANCELORDER
     */
    String URL_GET_DETAIL_CANCELORDER;
    /**
     * Property URL_GET_CANCELORDER
     */
    String URL_GET_CANCELORDER;
    /**
     * Property URL_SUBMIT_TRACK
     */
    String URL_SUBMIT_TRACK;
    /**
     * Property URL_UPDATE_CASH_ON_HAND
     */
    String URL_UPDATE_CASH_ON_HAND;
    /**
     * Property URL_SYNC_RV_NUMBERS
     */
    String URL_SYNC_RV_NUMBERS;
    /**
     * Property URL_GET_TASK_LOG
     */
    String URL_GET_TASK_LOG;
    /**
     * Property URL_GET_DEALERS
     */
    String URL_GET_DEALERS;
    /**
     * Property URL_GET_CLOSING_TASK
     */
    String URL_GET_CLOSING_TASK;
    /**
     * Property URL_SUBMIT_PRINT_COUNT
     */
    String URL_SUBMIT_PRINT_COUNT;
    /**
     * Property URL_RV_NUMBER
     */
    String URL_RV_NUMBER;
    /**
     * Property CHECK_RESUBMIT
     */
    String URL_CHECK_RESUBMIT;
    /**
     * Property GET_LOOKUP_ANSWER
     */
    String URL_GET_LOOKUP_ANSWER;
    /**
     * Property URL_RETRIECECOLLECTIONTASK
     */
    String URL_RETRIECECOLLECTIONTASK;
    /**
     * Property URL_SYNCPARAM
     */
    String URL_SYNCPARAM;
    String URL_SYNCTABLE_CONSTRAINT;
    /**
     * Property URL_GET_REPORTSUMMARY
     */
    String URL_GET_REPORTSUMMARY;
    /**
     * Property URL_SUBMIT_RESCHEDULE
     */
    String URL_SUBMIT_RESCHEDULE;
    String URL_GET_LIST_USER;
    /**
     * Property URL_CHECK_UPDATE
     */
    String URL_CHECK_UPDATE;
    String URL_GET_RECAPITULATE;
    String URL_SYNCPARAM_CONSTRAINT;

    /**
     * Property URL_SUBMIT_DKCP
     */
    String URL_SUBMIT_DKCP;
    String URL_CHECK_BIOMETRIC;
    String URL_SYNCPUSHTABLE;
    String URL_RETRIEVE_TASK_UPDATE;
    String URL_SUBMIT_TASK_UPDATE;
    String URL_CHECK_STATUS_TASK;

    /**
     * Property URL_GET_TASK_H_REMINDER_PO
     */
    String URL_GET_TASK_H_REMINDER_PO;

    /**
     * Property URL_GET_OTR
     */
    String URL_GET_OTR;

    String URL_GET_CEK_REFERANTOR;

    /**
     * Property URL_VALIDATION_CHECK_QUESTION
     */
    String URL_VALIDATION_CHECK_QUESTION;

    public static String getPropUrlCheckstatustask() {
        return PROP_URL_CHECKSTATUSTASK;
    }

    public String getURL_CHECK_STATUS_TASK() {
        return URL_CHECK_STATUS_TASK;
    }

    public void setURL_CHECK_STATUS_TASK(String urlCheckStatusTask) {
        this.URL_CHECK_STATUS_TASK = urlCheckStatusTask;
    }

    public static String getPropUrlSyncpushtable() {
        return PROP_URL_SYNCPUSHTABLE;
    }

    public String getURL_SYNCPUSHTABLE() {
        return URL_SYNCPUSHTABLE;
    }

    public void setURL_SYNCPUSHTABLE(String urlSyncPushTable) {
        this.URL_SYNCPUSHTABLE = urlSyncPushTable;
    }

    String URL_GETVERIFICATION_IMAGE;

    public String getURL_GETVERIFICATION_IMAGE() {
        return URL_GETVERIFICATION_IMAGE;
    }

    public void setURL_GETVERIFICATION_IMAGE(String urlGetVerificationImage) {
        this.URL_GETVERIFICATION_IMAGE = urlGetVerificationImage;
    }

    int greenAccuracy;
    int yellowAccuracy;
    int maxAccuracySafely;
    private AuditDataType auditData;
    private String osName = "empty";
    private String deviceModel = "empty";
    private String imei = "empty";
    private String imei2 = "";
    private String androidId = "";
    private String imsi = "empty";
    //=== URL ===//
    private String urlMain;
    private String urlWeb;
    //	private String urlLogin;
//	private String urlAttendance;
//	private String urlChangePassword;
    private String urlSync;
    private String urlSyncTable;
    private String urlTracking;
    private String savePath;
    private Boolean useExternalStorage;
    private String urlSyncFiles;
    //	private String urlSubmit;
    private String urlPersonalization;
    //	private String urlLocationHistory;
//	private String urlRefreshTask;
//	private String urlRetrieveTaskList;
//	private String urlRetrieveNews;
//	private String urlSvyPerformance;
//	private String urlGetQuestionSet;
    //=== Security ===//
    private boolean encrypt;
    private boolean decrypt;
    private boolean isSecureConnection;
    private boolean isRequiresAccessToken;
    private String clientId;
    private OAuth2Client oAuth2Client;
    private Token token;

    //	public String getUrlLogin() {
//		return urlLogin;
//	}
//
//	public void setUrlLogin(String urlLogin) {
//		this.urlLogin = urlLogin;
//	}
    private ConnectionCryptor connectionCryptor;
    //=== Format ===//
    private String application;
    private int maxLog;
    private int logAge;
    private int defMaxLenght;
    private int thumbnailWidth;
    private int thumbnailHeight;
    private String cameraQuality;
    private String currencyType;
    private boolean isPartialSending;
    private int maxPhotoSize;            //in bytes
    //=== Background Processes ===//
    private AutoSendService autoSendService;
    private LocationTrackingSchedule locationTrackingSchedule;
    private int intervalTracking = -1;
    private int intervalAutoSend = -1;
    private int intervalGPSTimeout = -1;
    //=== Application State and Data ===//
    private User user;
    private boolean isAuthenticated = false;
    private Boolean isDoingTask = false;
    //=== tenant ===//
    private String tenant = "";
    private String listPrinter;
    private boolean uuidChange;
    private String uuidDivider;
    private boolean useOwnCamera;

    public GlobalData() {
    }

    public static synchronized GlobalData getSharedGlobalData() {
        if (sharedGlobalData == null) {
            sharedGlobalData = new GlobalData();
        }
        return sharedGlobalData;
    }

    public String getSavePath() {
        return savePath;
    }

    public void setSavePath(String savePath) {
        this.savePath = savePath;
    }

    public Boolean getUseExternalStorage() {
        return useExternalStorage;
    }

    public void setUseExternalStorage(Boolean useExternalStorage) {
        this.useExternalStorage = useExternalStorage;
    }

    public String getUrlSyncFiles() {
        return urlSyncFiles;
    }

/*	public String getUrlGetQuestionSet() {
        return urlGetQuestionSet;
	}

	public void setUrlGetQuestionSet(String urlGetQuestionSet) {
		this.urlGetQuestionSet = urlGetQuestionSet;
	}

	public String getUrlSvyPerformance() {
		return urlSvyPerformance;
	}

	public void setUrlSvyPerformance(String urlSvyPerformance) {
		this.urlSvyPerformance = urlSvyPerformance;
	}

	public String getUrlSubmit() {
		return urlSubmit;
	}

	public void setUrlSubmit(String urlSubmit) {
		this.urlSubmit = urlSubmit;
	}*/

    public String getUrlSyncTable() {
        return urlSyncTable;
    }

    public void setUrlSyncTable(String urlSyncTable) {
        this.urlSyncTable = urlSyncTable;
    }

    public void setUrlSyncFiles(String urlSyncFiles) {
        this.urlSyncFiles = urlSyncFiles;
    }

    public boolean isRequiresAccessToken() {
        return isRequiresAccessToken;
    }

    public void setRequiresAccessToken(boolean requiresAccessToken) {
        isRequiresAccessToken = requiresAccessToken;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public OAuth2Client getoAuth2Client() {
        return oAuth2Client;
    }

    public void setoAuth2Client(User user) {
        String urlService = GlobalData.getSharedGlobalData().getUrlMain();
        int idx = urlService.indexOf("/services");
        String urlMain = urlService.substring(0, idx);
        OAuth2Client client = new OAuth2Client(user.getLogin_id(), user.getPassword(), GlobalData.getSharedGlobalData().getClientId(), null, urlMain);
        this.oAuth2Client = client;
    }

    public void setoAuth2Client(OAuth2Client oAuth2Client) {
        this.oAuth2Client = oAuth2Client;
    }

    public Token getToken() {
        return token;
    }

    public void setToken(Token token) {
        this.token = token;
    }

    public boolean getIsAuthenticated() {
        return isAuthenticated;
    }

    public void setIsAuthenticated(boolean isAuthenticated) {
        this.isAuthenticated = isAuthenticated;
    }

    public String getListPrinter() {
        return listPrinter;
    }

    public void setListPrinter(String listPrinter) {
        this.listPrinter = listPrinter;
    }



    public static String getPropUrlGetVerificationImage() {
        return PROP_URL_GET_VERIFICATION_IMAGE;
    }

    /**
     * reload url after url main has been set from serverLinkActivity
     *
     * @param context
     */
    public void reloadUrl(Context context) {
        Properties prop = ConfigFileReader.propertiesFromFile(context, PROPERTY_FILENAME);
//		urlLogin = urlMain + prop.getProperty(PROP_URL_SERV_LOGIN, "");
//		urlSync = urlMain + prop.getProperty(PROP_URL_SERV_SYNC, "");
//		setUrlTracking(urlMain + prop.getProperty(PROP_URL_SERV_TRACKING, ""));
//		setUrlSubmit(urlMain + prop.getProperty(PROP_URL_SERV_SUBMIT, ""));
//		urlPersonalization = urlMain + prop.getProperty(PROP_URL_SERV_PERSONALIZATION, "");
//
//		urlSvyPerformance = urlMain + prop.getProperty(PROP_URL_SERV_SVY_PERFORMANCE, "");
//		urlGetQuestionSet = urlMain + prop.getProperty(PROP_URL_GET_QUESTIONSET, "");
//
//		setUrlLocationHistory(urlMain + prop.getProperty(PROP_URL_SERV_LOCATION_HISTORY, ""));
//		setUrlRefreshTask(urlMain + prop.getProperty(PROP_URL_SERV_REFRESH_TASK, ""));
//		setUrlRetrieveTaskList(urlMain + prop.getProperty(PROP_URL_SERV_RETRIEVE_TASK_LIST, ""));
//		setUrlRetrieveNews(urlMain + prop.getProperty(PROP_URL_SERV_RETRIEVE_NEWS, ""));
        setURL_PROMISE_SURVEY(urlMain + prop.getProperty(PROP_URL_PROMISE_SURVEY, ""));
        setURL_INQUIRY_TASKH(urlMain + prop.getProperty(PROP_URL_INQUIRY_TASKH, ""));
        setURL_INQUIRY_TASKD(urlMain + prop.getProperty(PROP_URL_INQUIRY_TASKD, ""));
        setURL_MULTILOGIN(urlMain + prop.getProperty(PROP_URL_MULTILOGIN, ""));
        setURL_RECOVERY_PASSWORD(urlMain + prop.getProperty(PROP_URL_RECOVERY_PASSWORD, ""));
        setUrlSync(urlMain + prop.getProperty(PROP_URL_SERV_SYNC, ""));
        setUrlTracking(urlMain + prop.getProperty(PROP_URL_SERV_TRACKING, ""));
        setUrlPersonalization(urlMain + prop.getProperty(PROP_URL_SERV_PERSONALIZATION, ""));
        setURL_LOGIN(urlMain + prop.getProperty(PROP_URL_LOGIN, ""));
        setURL_UPDATE_FCM(urlMain + prop.getProperty(PROP_URL_UPDATE_FCM, ""));
        setUrlPersonalization(urlMain + prop.getProperty(PROP_URL_SERV_PERSONALIZATION, ""));
        setURL_CHANGEPASSWORD(urlMain + prop.getProperty(PROP_URL_CHANGEPASSWORD, ""));
        setURL_GET_ABSENSI(urlMain + prop.getProperty(PROP_URL_GET_ABSENSI, ""));
        setURL_GET_TASKLIST(urlMain + prop.getProperty(PROP_URL_GET_TASKLIST, ""));
        setURL_REFRESHTASK(urlMain + prop.getProperty(PROP_URL_REFRESHTASK, ""));
        setURL_SUBMITTASK(urlMain + prop.getProperty(PROP_URL_SUBMITTASK, ""));
        setURL_SUBMITOPENREADTASK(urlMain + prop.getProperty(PROP_URL_SUBMITOPENREADTASK, ""));
        setURL_GET_QUESTIONSET(urlMain + prop.getProperty(PROP_URL_GET_QUESTIONSET, ""));
        setURL_GET_VERIFICATION(urlMain + prop.getProperty(PROP_URL_GET_VERIFICATION, ""));
        setURL_GET_IMAGE(urlMain + prop.getProperty(PROP_URL_GET_IMAGE, ""));
        setURL_GET_SCHEME(urlMain + prop.getProperty(PROP_URL_GET_SCHEME, ""));
        setURL_GET_SVYPERFORMANCE(urlMain + prop.getProperty(PROP_URL_GET_SVYPERFORMANCE, ""));
        setURL_GET_LOOKUP(urlMain + prop.getProperty(PROP_URL_GET_LOOKUP, ""));
        setURL_GET_LIST_REASSIGNMENT(urlMain + prop.getProperty(PROP_URL_GET_LIST_REASSIGNMENT, ""));
        setURL_GET_DETAIL_REASSIGNMENT(urlMain + prop.getProperty(PROP_URL_GET_DETAIL_REASSIGNMENT, ""));
        setURL_GET_LIST_ASSIGNMENT(urlMain + prop.getProperty(PROP_URL_GET_LIST_ASSIGNMENT, ""));
        setURL_GET_DETAIL_ASSIGNMENT(urlMain + prop.getProperty(PROP_URL_GET_DETAIL_ASSIGNMENT, ""));
        setURL_GET_DETAIL_ORDER(urlMain + prop.getProperty(PROP_URL_GET_DETAIL_ORDER, ""));
        setURL_GET_DETAIL_TASK(urlMain + prop.getProperty(PROP_URL_GET_DETAIL_TASK, ""));
        setURL_SUBMIT_ASSIGN(urlMain + prop.getProperty(PROP_URL_SUBMIT_ASSIGN, ""));
        setURL_GET_LIST_VERIFICATION(urlMain + prop.getProperty(PROP_URL_GET_LIST_VERIFICATION, ""));
        setURL_GET_LIST_APPROVAL(urlMain + prop.getProperty(PROP_URL_GET_LIST_APPROVAL, ""));
        setURL_SENDDEPOSITREPORT(urlMain + prop.getProperty(PROP_URL_SENDDEPOSITREPORT, ""));
        setURL_GET_PAYMENTHISTORY(urlMain + prop.getProperty(PROP_URL_GET_PAYMENTHISTORY, ""));
        setURL_GET_INSTALLMENTSCHEDULE(urlMain + prop.getProperty(PROP_URL_GET_INSTALLMENTSCHEDULE, ""));
        setURL_GET_COLLECTIONHISTORY(urlMain + prop.getProperty(PROP_URL_GET_COLLECTIONHISTORY, ""));
        setURL_SUBMITVERIFICATIONTASK(urlMain + prop.getProperty(PROP_URL_SUBMITVERIFICATIONTASK, ""));
        setURL_SUBMITAPPROVALTASK(urlMain + prop.getProperty(PROP_URL_SUBMITAPPROVALTASK, ""));
        setURL_GET_CONTENTNEWS(urlMain + prop.getProperty(PROP_URL_GET_CONTENTNEWS, ""));
        setURL_CHECKORDER(urlMain + prop.getProperty(PROP_URL_CHECKORDER, ""));
        setURL_GET_TASK(urlMain + prop.getProperty(PROP_URL_GET_TASK, ""));
        setURL_GET_NEWSHEADER(urlMain + prop.getProperty(PROP_URL_GET_NEWSHEADER, ""));
        setURL_GET_NEWSCONTENT(urlMain + prop.getProperty(PROP_URL_GET_NEWSCONTENT, ""));
        setURL_GET_LIST_CANCELORDER(urlMain + prop.getProperty(PROP_URL_GET_LIST_CANCELORDER, ""));
        setURL_GET_DETAIL_CANCELORDER(urlMain + prop.getProperty(PROP_URL_GET_DETAIL_CANCELORDER, ""));
        setURL_GET_CANCELORDER(urlMain + prop.getProperty(PROP_URL_GET_CANCELORDER, ""));
        setURL_SUBMIT_TRACK(urlMain + prop.getProperty(PROP_URL_SUBMIT_TRACK, ""));
        setURL_RETRIECECOLLECTIONTASK(urlMain + prop.getProperty(PROP_URL_RETRIECECOLLECTIONTASK, ""));
        setURL_SYNCPARAM(urlMain + prop.getProperty(PROP_URL_SYNCPARAM, ""));
        setURL_GET_REPORTSUMMARY(urlMain + prop.getProperty(PROP_URL_GET_REPORTSUMMARY, ""));
        setURL_SUBMIT_RESCHEDULE(urlMain + prop.getProperty(PROP_URL_SUBMIT_RESCHEDULE, ""));
        setURL_GET_LIST_USER(urlMain + prop.getProperty(PROP_URL_GET_LIST_USER, ""));
        setURL_CHECK_UPDATE(urlMain + prop.getProperty(PROP_URL_CHECK_UPDATE, ""));
        setURL_GET_RECAPITULATE(urlMain + prop.getProperty(PROP_URL_GET_RECAPITULATE, ""));
        setURL_SYNCPARAM_CONSTRAINT(urlMain + prop.getProperty(PROP_URL_SYNCPARAM_CONSTRAINT, ""));
        setURL_SYNCTABLE_CONSTRAINT(urlMain + prop.getProperty(PROP_URL_SYNCTABLE_CONSTRAINT, ""));
        setUrlSyncTable(urlMain + prop.getProperty(PROP_URL_SYNCTABLE, ""));
        setURL_UPDATE_CASH_ON_HAND(urlMain + prop.getProperty(PROP_URL_UPDATE_CASH_ON_HAND, ""));
        setURL_CLOSING_TASK(urlMain + prop.getProperty(PROP_URL_GET_CLOSING_TASK, ""));
        setURL_SUBMIT_PRINT_COUNT(urlMain + prop.getProperty(PROP_URL_SUBMIT_PRINT_COUNT, ""));
        setURL_RV_NUMBER(urlMain + prop.getProperty(PROP_URL_RV_NUMBER, ""));
        setURL_SYNC_RV_NUMBERS(urlMain + prop.getProperty(PROP_SYNC_RV_NUMBERS, ""));
        setURL_GET_DEALERS(urlMain + prop.getProperty(PROP_URL_GET_DEALERS, ""));
        setURL_GET_TASK_LOG(urlMain + prop.getProperty(PROP_URL_GET_TASK_LOG, ""));
        setURL_LOOKUP_ANSWER(urlMain + prop.getProperty(PROP_URL_GET_LOOKUP_ANSWER, ""));
        setUrlSyncFiles(urlMain + prop.getProperty(PROP_URL_SYNC_FILES, ""));
        setUseExternalStorage(Boolean.parseBoolean(prop.getProperty(PROP_USE_EXTERNAL_STORAGE, "")));
        setURL_GETVERIFICATION_IMAGE(urlMain + prop.getProperty(PROP_URL_GET_VERIFICATION_IMAGE, ""));
        setURL_SYNCPUSHTABLE(urlMain + prop.getProperty(PROP_URL_SYNCPUSHTABLE, ""));
        setURL_RETRIEVE_TASK_UPDATE(urlMain + prop.getProperty(PROP_URL_RETRIEVE_TASK_UPDATE, ""));
        setURL_SUBMIT_TASK_UPDATE(urlMain + prop.getProperty(PROP_URL_SUBMIT_TASK_UPDATE, ""));
        setURL_CHECK_STATUS_TASK(urlMain + prop.getProperty(PROP_URL_CHECKSTATUSTASK, ""));
        setURL_INVITATION_ESIGN(urlMain + prop.getProperty(PROP_URL_INVITATION_ESIGN, ""));
        setURL_SUBMIT_DKCP(urlMain + prop.getProperty(PROP_URL_SUBMIT_DKCP, ""));
        setURL_SUBMIT_LAYER(urlMain + prop.getProperty(PROP_URL_SUBMIT_LAYER, ""));
        setURL_RESULT_TELECHECK(urlMain + prop.getProperty(PROP_URL_RESULT_TELECHECK, ""));
        setURL_CHECK_BIOMETRIC(urlMain + prop.getProperty(PROP_URL_CHECK_BIOMETRIC, ""));
        setURL_GET_TASK_H_REMINDER_PO(urlMain + prop.getProperty(PROP_URL_GET_TASK_H_REMINDER_PO, ""));
        setURL_GET_DSR(urlMain + prop.getProperty(PROP_URL_GET_DSR, ""));
        setURL_REGISTRATION_CHECK(urlMain + prop.getProperty(PROP_URL_REGISTRATION_CHECK, ""));
        setURL_GET_OTR(urlMain + prop.getProperty(PROP_URL_GET_OTR, ""));
        setURL_GET_CEK_REFERANTOR(urlMain + prop.getProperty(PROP_URL_GET_CEK_REFERANTOR, ""));
        setURL_VALIDATION_CHECK_QUESTION(urlMain + prop.getProperty(PROP_URL_VALIDATION_CHECK_QUESTION, ""));

        if (getUseExternalStorage()) {
            setSavePath(Environment.getExternalStorageDirectory().getPath() + prop.getProperty(PROP_SAVE_PATH, ""));
        } else
            setSavePath(Environment.getExternalStorageDirectory().getPath() + prop.getProperty(PROP_SAVE_PATH, ""));

        try {
            String link = urlMain;
            String[] index = link.split("://");
            if (index != null) {
                if (index[0].equals(http)) {
                    setSecureConnection(false);
                } else if (index[0].equals(https)) {
                    setSecureConnection(true);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }//CR External DB Embed

    }

    /**
     * Load values from .properties.
     * <br>Place the implementation of fetching value and storing to local property here
     *
     * @param context
     */
    public void loadFromProperties(Context context) {
        Properties prop = ConfigFileReader.propertiesFromFile(context, PROPERTY_FILENAME);
        urlMain = prop.getProperty(PROP_URL_MAIN, "");
        urlWeb = prop.getProperty(PROP_URL_WEB,"");
//		urlLogin = urlMain + prop.getProperty(PROP_URL_SERV_LOGIN, "");
//		urlSync = urlMain + prop.getProperty(PROP_URL_SERV_SYNC, "");
//
//		setUrlTracking(urlMain + prop.getProperty(PROP_URL_SERV_TRACKING, ""));
//		setUrlSubmit(urlMain + prop.getProperty(PROP_URL_SERV_SUBMIT, ""));
//		urlPersonalization = urlMain + prop.getProperty(PROP_URL_SERV_PERSONALIZATION, "");
//
//		urlSvyPerformance = urlMain + prop.getProperty(PROP_URL_SERV_SVY_PERFORMANCE, "");
//		urlGetQuestionSet = urlMain + prop.getProperty(PROP_URL_GET_QUESTIONSET, "");
//
//		setUrlLocationHistory(urlMain + prop.getProperty(PROP_URL_SERV_LOCATION_HISTORY, ""));
//		setUrlRefreshTask(urlMain + prop.getProperty(PROP_URL_SERV_REFRESH_TASK, ""));
//		setUrlRetrieveTaskList(urlMain + prop.getProperty(PROP_URL_SERV_RETRIEVE_TASK_LIST, ""));
//		setUrlRetrieveNews(urlMain + prop.getProperty(PROP_URL_SERV_RETRIEVE_NEWS, ""));

        setDevMode(Boolean.parseBoolean(prop.getProperty(PROP_DEVMODE, "false")));
        setURL_MULTILOGIN(urlMain + prop.getProperty(PROP_URL_MULTILOGIN, ""));
        setURL_RECOVERY_PASSWORD(urlMain + prop.getProperty(PROP_URL_RECOVERY_PASSWORD, ""));
        setURL_PROMISE_SURVEY(urlMain + prop.getProperty(PROP_URL_PROMISE_SURVEY, ""));
        setURL_INQUIRY_TASKH(urlMain + prop.getProperty(PROP_URL_INQUIRY_TASKH, ""));
        setURL_INQUIRY_TASKD(urlMain + prop.getProperty(PROP_URL_INQUIRY_TASKD, ""));
        setUrlSync(urlMain + prop.getProperty(PROP_URL_SERV_SYNC, ""));

        setUrlTracking(urlMain + prop.getProperty(PROP_URL_SERV_TRACKING, ""));
        setUrlPersonalization(urlMain + prop.getProperty(PROP_URL_SERV_PERSONALIZATION, ""));
        setURL_LOGIN(urlMain + prop.getProperty(PROP_URL_LOGIN, ""));
        setURL_UPDATE_FCM(urlMain + prop.getProperty(PROP_URL_UPDATE_FCM, ""));
        setUrlPersonalization(urlMain + prop.getProperty(PROP_URL_SERV_PERSONALIZATION, ""));
        setURL_CHANGEPASSWORD(urlMain + prop.getProperty(PROP_URL_CHANGEPASSWORD, ""));
        setURL_GET_ABSENSI(urlMain + prop.getProperty(PROP_URL_GET_ABSENSI, ""));
        setURL_GET_TASKLIST(urlMain + prop.getProperty(PROP_URL_GET_TASKLIST, ""));
        setURL_REFRESHTASK(urlMain + prop.getProperty(PROP_URL_REFRESHTASK, ""));
        setURL_SUBMITTASK(urlMain + prop.getProperty(PROP_URL_SUBMITTASK, ""));
        setURL_SUBMITOPENREADTASK(urlMain + prop.getProperty(PROP_URL_SUBMITOPENREADTASK, ""));
        setURL_GET_QUESTIONSET(urlMain + prop.getProperty(PROP_URL_GET_QUESTIONSET, ""));
        setURL_GET_VERIFICATION(urlMain + prop.getProperty(PROP_URL_GET_VERIFICATION, ""));
        setURL_GET_IMAGE(urlMain + prop.getProperty(PROP_URL_GET_IMAGE, ""));
        setURL_GET_SCHEME(urlMain + prop.getProperty(PROP_URL_GET_SCHEME, ""));
        setURL_GET_SVYPERFORMANCE(urlMain + prop.getProperty(PROP_URL_GET_SVYPERFORMANCE, ""));
        setURL_GET_LOOKUP(urlMain + prop.getProperty(PROP_URL_GET_LOOKUP, ""));
        setURL_GET_LIST_REASSIGNMENT(urlMain + prop.getProperty(PROP_URL_GET_LIST_REASSIGNMENT, ""));
        setURL_GET_DETAIL_REASSIGNMENT(urlMain + prop.getProperty(PROP_URL_GET_DETAIL_REASSIGNMENT, ""));
        setURL_GET_LIST_ASSIGNMENT(urlMain + prop.getProperty(PROP_URL_GET_LIST_ASSIGNMENT, ""));
        setURL_GET_DETAIL_ASSIGNMENT(urlMain + prop.getProperty(PROP_URL_GET_DETAIL_ASSIGNMENT, ""));
        setURL_GET_DETAIL_ORDER(urlMain + prop.getProperty(PROP_URL_GET_DETAIL_ORDER, ""));
        setURL_GET_DETAIL_TASK(urlMain + prop.getProperty(PROP_URL_GET_DETAIL_TASK, ""));
        setURL_SUBMIT_ASSIGN(urlMain + prop.getProperty(PROP_URL_SUBMIT_ASSIGN, ""));
        setURL_GET_LIST_VERIFICATION(urlMain + prop.getProperty(PROP_URL_GET_LIST_VERIFICATION, ""));
        setURL_GET_LIST_APPROVAL(urlMain + prop.getProperty(PROP_URL_GET_LIST_APPROVAL, ""));
        setURL_SENDDEPOSITREPORT(urlMain + prop.getProperty(PROP_URL_SENDDEPOSITREPORT, ""));
        setURL_GET_PAYMENTHISTORY(urlMain + prop.getProperty(PROP_URL_GET_PAYMENTHISTORY, ""));
        setURL_GET_INSTALLMENTSCHEDULE(urlMain + prop.getProperty(PROP_URL_GET_INSTALLMENTSCHEDULE, ""));
        setURL_GET_COLLECTIONHISTORY(urlMain + prop.getProperty(PROP_URL_GET_COLLECTIONHISTORY, ""));
        setURL_SUBMITVERIFICATIONTASK(urlMain + prop.getProperty(PROP_URL_SUBMITVERIFICATIONTASK, ""));
        setURL_SUBMITAPPROVALTASK(urlMain + prop.getProperty(PROP_URL_SUBMITAPPROVALTASK, ""));
        setURL_GET_CONTENTNEWS(urlMain + prop.getProperty(PROP_URL_GET_CONTENTNEWS, ""));
        setURL_CHECKORDER(urlMain + prop.getProperty(PROP_URL_CHECKORDER, ""));
        setURL_GET_TASK(urlMain + prop.getProperty(PROP_URL_GET_TASK, ""));
        setURL_GET_NEWSHEADER(urlMain + prop.getProperty(PROP_URL_GET_NEWSHEADER, ""));
        setURL_GET_NEWSCONTENT(urlMain + prop.getProperty(PROP_URL_GET_NEWSCONTENT, ""));
        setURL_GET_LIST_CANCELORDER(urlMain + prop.getProperty(PROP_URL_GET_LIST_CANCELORDER, ""));
        setURL_GET_DETAIL_CANCELORDER(urlMain + prop.getProperty(PROP_URL_GET_DETAIL_CANCELORDER, ""));
        setURL_GET_CANCELORDER(urlMain + prop.getProperty(PROP_URL_GET_CANCELORDER, ""));
        setURL_SUBMIT_TRACK(urlMain + prop.getProperty(PROP_URL_SUBMIT_TRACK, ""));
        setURL_RETRIECECOLLECTIONTASK(urlMain + prop.getProperty(PROP_URL_RETRIECECOLLECTIONTASK, ""));
        setURL_SYNCPARAM(urlMain + prop.getProperty(PROP_URL_SYNCPARAM, ""));
        setUrlSyncTable(urlMain + prop.getProperty(PROP_URL_SYNCTABLE, ""));
        setURL_GET_REPORTSUMMARY(urlMain + prop.getProperty(PROP_URL_GET_REPORTSUMMARY, ""));
        setURL_SUBMIT_RESCHEDULE(urlMain + prop.getProperty(PROP_URL_SUBMIT_RESCHEDULE, ""));
        setURL_GET_LIST_USER(urlMain + prop.getProperty(PROP_URL_GET_LIST_USER, ""));
        setURL_CHECK_UPDATE(urlMain + prop.getProperty(PROP_URL_CHECK_UPDATE, ""));
        setURL_GET_RECAPITULATE(urlMain + prop.getProperty(PROP_URL_GET_RECAPITULATE, ""));
        setURL_SYNCPARAM_CONSTRAINT(urlMain + prop.getProperty(PROP_URL_SYNCPARAM_CONSTRAINT, ""));
        setURL_SYNCTABLE_CONSTRAINT(urlMain + prop.getProperty(PROP_URL_SYNCTABLE_CONSTRAINT, ""));
        setURL_UPDATE_CASH_ON_HAND(urlMain + prop.getProperty(PROP_URL_UPDATE_CASH_ON_HAND, ""));
        setURL_CLOSING_TASK(urlMain + prop.getProperty(PROP_URL_GET_CLOSING_TASK, ""));
        setURL_SUBMIT_PRINT_COUNT(urlMain + prop.getProperty(PROP_URL_SUBMIT_PRINT_COUNT, ""));
        setURL_RV_NUMBER(urlMain + prop.getProperty(PROP_URL_RV_NUMBER, ""));
        setURL_SYNC_RV_NUMBERS(urlMain + prop.getProperty(PROP_SYNC_RV_NUMBERS, ""));
        setURL_GET_DEALERS(urlMain + prop.getProperty(PROP_URL_GET_DEALERS, ""));
        setURL_GET_TASK_LOG(urlMain + prop.getProperty(PROP_URL_GET_TASK_LOG, ""));
        setURL_LOOKUP_ANSWER(urlMain + prop.getProperty(PROP_URL_GET_LOOKUP_ANSWER, ""));
//		setURL_CHECK_RESUBMIT(urlMain + prop.getProperty(PROP_URL_CHECK_RESUBMIT, ""));
        setURL_CHECK_RESUBMIT(prop.getProperty(PROP_URL_CHECK_RESUBMIT, ""));
        //String coba = urlMain;
        setURL_LOOKUP_ANSWER(urlMain + prop.getProperty(PROP_URL_GET_LOOKUP_ANSWER, ""));
        //CR External DB Embed
        setUseExternalStorage(Boolean.parseBoolean(prop.getProperty(PROP_USE_EXTERNAL_STORAGE)));
        setUrlSyncFiles(urlMain + prop.getProperty(PROP_URL_SYNC_FILES, ""));
        setURL_GETVERIFICATION_IMAGE(urlMain + prop.getProperty(PROP_URL_GET_VERIFICATION_IMAGE, ""));
        setURL_SYNCPUSHTABLE(urlMain + prop.getProperty(PROP_URL_SYNCPUSHTABLE, ""));
        setURL_RETRIEVE_TASK_UPDATE(urlMain + prop.getProperty(PROP_URL_RETRIEVE_TASK_UPDATE, ""));
        setURL_SUBMIT_TASK_UPDATE(urlMain + prop.getProperty(PROP_URL_SUBMIT_TASK_UPDATE, ""));
        setURL_CHECK_STATUS_TASK(urlMain + prop.getProperty(PROP_URL_CHECKSTATUSTASK, ""));
        setURL_INVITATION_ESIGN(urlMain + prop.getProperty(PROP_URL_INVITATION_ESIGN, ""));
        setURL_SUBMIT_DKCP(urlMain + prop.getProperty(PROP_URL_SUBMIT_DKCP, ""));
        setURL_SUBMIT_LAYER(urlMain + prop.getProperty(PROP_URL_SUBMIT_LAYER, ""));
        setURL_RESULT_TELECHECK(urlMain + prop.getProperty(PROP_URL_RESULT_TELECHECK, ""));
        setURL_CHECK_BIOMETRIC(urlMain + prop.getProperty(PROP_URL_CHECK_BIOMETRIC, ""));
        setURL_GET_TASK_H_REMINDER_PO(urlMain + prop.getProperty(PROP_URL_GET_TASK_H_REMINDER_PO, ""));
        setURL_GET_DSR(urlMain + prop.getProperty(PROP_URL_GET_DSR, ""));
        setURL_REGISTRATION_CHECK(urlMain + prop.getProperty(PROP_URL_REGISTRATION_CHECK, ""));
        setURL_GET_OTR(urlMain + prop.getProperty(PROP_URL_GET_OTR, ""));
        setURL_GET_CEK_REFERANTOR(urlMain + prop.getProperty(PROP_URL_GET_CEK_REFERANTOR, ""));
        setURL_VALIDATION_CHECK_QUESTION(urlMain + prop.getProperty(PROP_URL_VALIDATION_CHECK_QUESTION, ""));

        if (getUseExternalStorage()) {
            setSavePath(Environment.getExternalStorageDirectory().getPath() + prop.getProperty(PROP_SAVE_PATH, ""));
        } else
            setSavePath(Environment.getExternalStorageDirectory().getPath() + prop.getProperty(PROP_SAVE_PATH, ""));

        //TODO nanti pakai yang dari application.properties

//		urlMain = com.adins.mss.constant.Global.URL_HEADER;
//		urlLogin = com.adins.mss.constant.Global.URL_LOGIN;
//		urlSync = com.adins.mss.constant.Global.URL_SYNC;
//		setUrlTracking(com.adins.mss.constant.Global.URL_SUBMIT_TRACK);
//		setUrlSubmit(com.adins.mss.constant.Global.URL_SUBMIT);
//		urlPersonalization = com.adins.mss.constant.Global.URL_;

        encrypt = Boolean.parseBoolean(prop.getProperty(PROP_ENCRYPT, "false"));
        decrypt = Boolean.parseBoolean(prop.getProperty(PROP_DECRYPT, "false"));
//		Global.IS_DEV = Boolean.parseBoolean(prop.getProperty(PROP_IS_DEVELOPER, "false"));
//		Global.IS_BYPASSROOT = Boolean.parseBoolean(prop.getProperty(PROP_IS_BYPASSROOT, "false"));
        setSecureConnection(Boolean.parseBoolean(prop.getProperty(PROP_IS_SECURE_CONNECTION, "false")));
        setRequiresAccessToken(Boolean.parseBoolean(prop.getProperty(PROP_IS_REQUIRED_ACCESS_TOKEN, "false")));
        setUseOwnCamera(Boolean.parseBoolean(prop.getProperty(PROP_OWN_CAMERA, "false")));
        setClientId(prop.getProperty(PROP_CLIENT_ID, "android"));

        listPrinter = prop.getProperty(PROP_LIST_PRINTER, "");

        try {
            String link = urlMain;
            String[] index = link.split("://");
            if (index != null) {
                if (index[0].equals(http)) {
                    setSecureConnection(false);
                } else if (index[0].equals(https)) {
                    setSecureConnection(true);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

//		encrypt = com.adins.mss.constant.Global.ENCRYPT_COMM;
//		decrypt = com.adins.mss.constant.Global.DECRYPT_COMM;

        setApplication(prop.getProperty(PROP_APPLICATION_NAME, ""));
        maxLog = Integer.parseInt(prop.getProperty(PROP_MAX_LOG, "0"));
        logAge = Integer.parseInt(prop.getProperty(PROP_LOG_AGE, "0"));
        defMaxLenght = Integer.parseInt(prop.getProperty(PROP_DEF_MAX_LENGHT, "0"));
        thumbnailWidth = Integer.parseInt(prop.getProperty(PROP_THUMB_WIDTH, "0"));
        thumbnailHeight = Integer.parseInt(prop.getProperty(PROP_THUMB_HEIGHT, "0"));
        cameraQuality = prop.getProperty(PROP_CAMERA_QUALITY, "0");
        maxPhotoSize = Integer.parseInt(prop.getProperty(PROP_MAX_PHOTO_SIZE, "1024"));

//		setApplication("Mobile Solution");
//		maxLog = com.adins.mss.constant.Global.MAX_LOG;
//		logAge = com.adins.mss.constant.Global.DAYS_KEEP_SENT_SURVEY;
//		defMaxLenght = 30;
//		thumbnailWidth = com.adins.mss.constant.Global.THUMBNAIL_WIDTH;
//		thumbnailHeight = com.adins.mss.constant.Global.THUMBNAIL_HEIGHT;
//
//		cameraQuality = prop.getProperty(PROP_CAMERA_QUALITY, "0");
//		maxPhotoSize = Integer.parseInt(prop.getProperty(PROP_MAX_PHOTO_SIZE, "1024"));

        intervalTracking = Integer.parseInt(prop.getProperty(PROP_INTERVAL_TRACKING, "0"));
        intervalAutoSend = Integer.parseInt(prop.getProperty(PROP_INTERVAL_AUTOSEND, "0"));
        intervalGPSTimeout = Integer.parseInt(prop.getProperty(PROP_INTERVAL_GPS_TIMEOUT, "0"));
        setUuidChange(Boolean.parseBoolean(prop.getProperty(PROP_CHANGE_UUID, "false")));
        setUuidDivider(prop.getProperty(PROP_UUID_DIVIDER, ""));
    }

    public boolean isUuidChange() {
        return uuidChange;
    }

    public void setUuidChange(boolean uuidChange) {
        this.uuidChange = uuidChange;
    }

    public String getUuidDivider() {
        return uuidDivider;
    }

    public void setUuidDivider(String uuidDivider) {
        this.uuidDivider = uuidDivider;
    }

    public void loadGeneralParameters(List<GeneralParameter> generalParameters) {
        for (GeneralParameter generalParameter : generalParameters) {

            String param = generalParameter.getGs_code();
            String value = generalParameter.getGs_value();
            int intValue = 0;

            try {
                intValue = Integer.parseInt(value);
            } catch (Exception e) {
                intValue = 0;
            }

            if (GeneralParameterDataAccess.GS_PARAM_INTERVAL_TRACKING.equals(param)) {
                setIntervalTracking(intValue);
            } else if (GeneralParameterDataAccess.GS_PARAM_INTERVAL_AUTOSEND.equals(param)) {
                setIntervalAutoSend(intValue);
            } else if (GeneralParameterDataAccess.GS_PARAM_INTERVAL_GPS_TIMEOUT.equals(param)) {
                setIntervalGPSTimeout(intValue);
            } else if (GeneralParameterDataAccess.GS_PARAM_CAMERA.equals(param)) {
                setCameraQuality(value);
            } else if (com.adins.mss.constant.Global.GS_TENANT_ID.equals(param)) {
                setTenant(value);
            } else if (com.adins.mss.constant.Global.GS_TIMELINE_TIME.equals(param)) {
                setKeepTimelineInDays(intValue);
            } else if (com.adins.mss.constant.Global.GS_LOG_COUNTER.equals(param)) {
                setMaxDataInLog(intValue);
            } else if (com.adins.mss.constant.Global.GS_ACCURACY_G.equals(param)) {
                setGreenAccuracy(intValue);
            } else if (com.adins.mss.constant.Global.GS_ACCURACY_Y.equals(param)) {
                setYellowAccuracy(intValue);
            } else if (com.adins.mss.constant.Global.GS_ACCURACY.equals(param)) {
                setMaxAccuracySafely(intValue);
            } else if (Global.GS_CURRENCY_TYPE.equals(param)) {
                setCurrencyType(value);
            } else if (Global.MS_TIMEOUT_MOBILE.equals(param)) {
                setTimeOut(Long.parseLong(value));
            } else if (Global.GS_URL_WEB.equals(param)) {
                setUrlWeb(value);
            }
//            else if ((GlobalData.getSharedGlobalData().getApplication() + Global._TRCK_DAYS).equals(param)) {
//                user.setTracking_days(value);
//            } else if ((GlobalData.getSharedGlobalData().getApplication() + Global._TRCK_START_TIME).equals(param)) {
//                user.setStart_time(value);
//            } else if ((GlobalData.getSharedGlobalData().getApplication() + Global._TRCK_END_TIME).equals(param)) {
//                user.setEnd_time(value);
//            }
        }
    }

    private long timeOut;

    public long getTimeOut() {
        return timeOut;
    }

    public void setTimeOut(long timeOut) {
        this.timeOut = timeOut;
    }

    public String getLocale() {
        return locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public int getKeepTimelineInDays() {
        return this.keepTimelineInDays;
    }

    public void setKeepTimelineInDays(int value) {
        this.keepTimelineInDays = value;
    }

    public int getMaxDataInLog() {
        return this.maxDataInLog;
    }

    public void setMaxDataInLog(int value) {
        this.maxDataInLog = value;
    }

    public boolean isUseOwnCamera() {
        return useOwnCamera;
    }

    public void setUseOwnCamera(boolean useOwnCamera) {
        this.useOwnCamera = useOwnCamera;
    }

    //=== Getter Setter ===//
    public String getUrlMain() {
        return urlMain;
    }

    public void setUrlMain(String urlMain) {
        this.urlMain = urlMain;
    }

    public String getUrlWeb() {
        return urlWeb;
    }

    public void setUrlWeb(String urlWeb) {
        this.urlWeb = urlWeb;
    }

    public String getUrlSync() {
        return urlSync;
    }

    public void setUrlSync(String urlSync) {
        this.urlSync = urlSync;
    }

    public boolean isEncrypt() {
        return encrypt;
    }

    public void setEncrypt(boolean encrypt) {
        this.encrypt = encrypt;
    }

    public boolean isDecrypt() {
        return decrypt;
    }

    public void setDecrypt(boolean decrypt) {
        this.decrypt = decrypt;
    }

    public boolean isSecureConnection() {
        return isSecureConnection;
    }

    public void setSecureConnection(boolean isSecureConnection) {
        this.isSecureConnection = isSecureConnection;
    }

    public int getMaxLog() {
        return maxLog;
    }

    public void setMaxLog(int maxLog) {
        this.maxLog = maxLog;
    }

    public int getLogAge() {
        return logAge;
    }

    public void setLogAge(int logAge) {
        this.logAge = logAge;
    }

    public int getMaxLenght() {
        return defMaxLenght;
    }

    public void setMaxLenght(int maxLenght) {
        this.defMaxLenght = maxLenght;
    }

    public int getThumbnailWidth() {
        return thumbnailWidth;
    }

    public void setThumbnailWidth(int thumbnailWidth) {
        this.thumbnailWidth = thumbnailWidth;
    }

//	/** Property URL_GET_COLLECTIONLIST */
//	String URL_GET_COLLECTIONLIST;
//
//	/** Property URL_SUBMIT */
//	String URL_SUBMIT;
//
//	/** Property URL_INQUIRY */
//	String URL_INQUIRY;
//
//	/** Property URL_UPDATELOCATIONHISTORY */
//	String URL_UPDATELOCATIONHISTORY;
//
//	/** Property URL_ORDERTRACKING */
//	String URL_ORDERTRACKING;
//
//	/** Property URL_SYNC */
//	String URL_SYNC;
//
//	/** Property URL_UPDATETASKREAD */
//	String URL_UPDATETASKREAD;
//
//	/** Property URL_ERROR_LOGGER */
//	String URL_ERROR_LOGGER;

    public int getThumbnailHeight() {
        return thumbnailHeight;
    }

    public void setThumbnailHeight(int thumbnailHeight) {
        this.thumbnailHeight = thumbnailHeight;
    }

    public AutoSendService getAutoSendThread() {
        return autoSendService;
    }

    public void setAutoSendThread(AutoSendService autoSendThread) {
        this.autoSendService = autoSendThread;
    }

    public LocationTrackingSchedule getLocationTrackingSchedule() {
        return locationTrackingSchedule;
    }

    public void setLocationTrackingSchedule(LocationTrackingSchedule locationTrackingSchedule) {
        this.locationTrackingSchedule = locationTrackingSchedule;
    }

    public int getIntervalTracking() {
        return intervalTracking;
    }

    public void setIntervalTracking(int intervalTracking) {
        this.intervalTracking = intervalTracking;
    }

    public int getIntervalAutoSend() {
        return intervalAutoSend;
    }

    public void setIntervalAutoSend(int intervalAutoSend) {
        this.intervalAutoSend = intervalAutoSend;
    }

    public int getIntervalGPSTimeout() {
        return intervalGPSTimeout;
    }

    public void setIntervalGPSTimeout(int intervalGPSTimeout) {
        this.intervalGPSTimeout = intervalGPSTimeout;
    }

    public String getCameraQuality() {
        return cameraQuality;
    }

    public void setCameraQuality(String cameraQuality) {
        this.cameraQuality = cameraQuality;
    }

    public String getUrlTracking() {
        return urlTracking;
    }

    public void setUrlTracking(String urlTracking) {
        this.urlTracking = urlTracking;
    }

    /*	public void setUrlLocationHistory(String urlLocationHistory) {
        this.urlLocationHistory = urlLocationHistory;
    }

    public String getUrlLocationHistory(){
        return this.urlLocationHistory;
    }

    public void setUrlRefreshTask(String urlRefreshTask) {
        this.urlRefreshTask = urlRefreshTask;
    }

    public String getUrlRefreshTask(){
        return this.urlRefreshTask;
    }

    public void setUrlRetrieveTaskList(String urlRetrieveTaskList) {
        this.urlRetrieveTaskList = urlRetrieveTaskList;
    }

    public String getUrlRetrieveTaskList(){
        return this.urlRetrieveTaskList;
    }

    public void setUrlRetrieveNews(String urlRetrieveNews) {
        this.urlRetrieveNews = urlRetrieveNews;
    }

    public String getUrlRetrieveNews(){
        return this.urlRetrieveNews;
    }
*/
    public ConnectionCryptor getConnectionCryptor() {
        return connectionCryptor;
    }

    public void setConnectionCryptor(ConnectionCryptor connectionCryptor) {
        this.connectionCryptor = connectionCryptor;
    }

    public String getApplication() {
        return application;
    }

    public void setApplication(String application) {
        this.application = application;
    }

    public String getOsName() {
        return osName;
    }

    public void setOsName(String osName) {
        this.osName = osName;
    }

    public String getDeviceModel() {
        return deviceModel;
    }

    public void setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getImei2() {
        return imei2;
    }

    public void setImei2(String imei2) {
        this.imei2 = imei2;
    }

    public String getAndroidId() {
        if (androidId == null || androidId.isEmpty()) {
            androidId = AuditDataTypeGenerator.getAndroidId();
        }

        return androidId;
    }

    public String getImsi() {
        return imsi;
    }

    public void setImsi(String imsi) {
        this.imsi = imsi;
    }

    public int getDefMaxLenght() {
        return defMaxLenght;
    }

    public void setDefMaxLenght(int defMaxLenght) {
        this.defMaxLenght = defMaxLenght;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Boolean getDoingTask() {
        return isDoingTask;
    }

    public void setDoingTask(Boolean doingTask) {
        isDoingTask = doingTask;
    }

    public AuditDataType getAuditData() {
        return auditData;
    }

    public void setAuditData(AuditDataType auditData) {
        this.auditData = auditData;
    }

    public boolean isPartialSending() {
        return isPartialSending;
    }

    public void setPartialSending(boolean isPartialSending) {
        this.isPartialSending = isPartialSending;
    }

    public String getUrlPersonalization() {
        return urlPersonalization;
    }

    public void setUrlPersonalization(String urlPersonalization) {
        this.urlPersonalization = urlPersonalization;
    }

    public String getURL_SUBMIT_DKCP() {
        return URL_SUBMIT_DKCP;
    }

    public void setURL_SUBMIT_DKCP(String urlSubmitDkcp) {
        this.URL_SUBMIT_DKCP = urlSubmitDkcp;
    }

    public String getURL_CHECK_BIOMETRIC() {
        return URL_CHECK_BIOMETRIC;
    }

    public void setURL_CHECK_BIOMETRIC(String urlCheckBiometric) {
        this.URL_CHECK_BIOMETRIC = urlCheckBiometric;
    }

    public int getMaxPhotoSize() {
        return maxPhotoSize;
    }

    public void setMaxPhotoSize(int maxPhotoSize) {
        this.maxPhotoSize = maxPhotoSize;
    }

    public String getTenant() {
        return tenant;
    }

    public void setTenant(String tenant) {
        this.tenant = tenant;
    }

    public String getURL_LOOKUP_ANSWER() {
        return URL_GET_LOOKUP_ANSWER;
    }

    public void setURL_LOOKUP_ANSWER(String urlGetLookupAnswer) {
        this.URL_GET_LOOKUP_ANSWER = urlGetLookupAnswer;
    }

    /**
     * Gets URL_GET_CLOSING_TASK
     */
    public String getURL_CLOSING_TASK() {
        return this.URL_GET_CLOSING_TASK;
    }

    /**
     * Sets URL_SET_CLOSING_TASK
     */
    public void setURL_CLOSING_TASK(String value) {
        this.URL_GET_CLOSING_TASK = value;
    }

    public String getURL_SUBMIT_PRINT_COUNT() {
        return URL_SUBMIT_PRINT_COUNT;
    }

    public void setURL_SUBMIT_PRINT_COUNT(String urlSubmitPrintCount) {
        this.URL_SUBMIT_PRINT_COUNT = urlSubmitPrintCount;
    }

    public String getURL_RV_NUMBER() {
        return URL_RV_NUMBER;
    }

    public void setURL_RV_NUMBER(String urlRvNumber) {
        this.URL_RV_NUMBER = urlRvNumber;
    }

    public String getURL_GET_DEALERS() {
        return URL_GET_DEALERS;
    }

    public void setURL_GET_DEALERS(String urlGetDealers) {
        this.URL_GET_DEALERS = urlGetDealers;
    }

    public String getURL_GET_TASK_LOG() {
        return this.URL_GET_TASK_LOG;
    }

    public void setURL_GET_TASK_LOG(String urlGetTaskLog) {
        this.URL_GET_TASK_LOG = urlGetTaskLog;
    }

    public String getURL_RETRIEVE_TASK_UPDATE() {
        return URL_RETRIEVE_TASK_UPDATE;
    }

    public void setURL_RETRIEVE_TASK_UPDATE(String urlRetrieveTaskUpdate) {
        this.URL_RETRIEVE_TASK_UPDATE = urlRetrieveTaskUpdate;
    }

    public String getURL_SUBMIT_TASK_UPDATE() {
        return URL_SUBMIT_TASK_UPDATE;
    }

    public void setURL_SUBMIT_TASK_UPDATE(String urlSubmitTaskUpdate) {
        this.URL_SUBMIT_TASK_UPDATE = urlSubmitTaskUpdate;
    }

    public String getURL_SYNC_RV_NUMBERS() {
        return URL_SYNC_RV_NUMBERS;
    }

    public void setURL_SYNC_RV_NUMBERS(String urlSyncRvNumbers) {
        this.URL_SYNC_RV_NUMBERS = urlSyncRvNumbers;
    }

    public String getURL_UPDATE_CASH_ON_HAND() {
        return URL_UPDATE_CASH_ON_HAND;
    }

    public void setURL_UPDATE_CASH_ON_HAND(String urlUpdateCashOnHand) {
        this.URL_UPDATE_CASH_ON_HAND = urlUpdateCashOnHand;
    }

    /**
     * Gets the URL_LOGIN
     */
    public String getURL_LOGIN() {
        return this.URL_LOGIN;
    }

    /**
     * Sets the URL_LOGIN
     */
    public void setURL_LOGIN(String value) {
        this.URL_LOGIN = value;
    }

    /**
     * Gets the URL_LOGIN
     */
    public String getURL_UPDATE_FCM() {
        return this.URL_UPDATE_FCM;
    }

    /**
     * Sets the URL_LOGIN
     */
    public void setURL_UPDATE_FCM(String value) {
        this.URL_UPDATE_FCM = value;
    }

    /**
     * Gets the URL_CHANGEPASSWORD
     */
    public String getURL_CHANGEPASSWORD() {
        return this.URL_CHANGEPASSWORD;
    }

    /**
     * Sets the URL_CHANGEPASSWORD
     */
    public void setURL_CHANGEPASSWORD(String value) {
        this.URL_CHANGEPASSWORD = value;
    }

    /**
     * Gets the URL_GET_ABSENSI
     */
    public String getURL_GET_ABSENSI() {
        return this.URL_GET_ABSENSI;
    }

    /**
     * Sets the URL_GET_ABSENSI
     */
    public void setURL_GET_ABSENSI(String value) {
        this.URL_GET_ABSENSI = value;
    }

    /**
     * Gets the URL_GET_TASKLIST
     */
    public String getURL_GET_TASKLIST() {
        return this.URL_GET_TASKLIST;
    }

    /**
     * Sets the URL_GET_TASKLIST
     */
    public void setURL_GET_TASKLIST(String value) {
        this.URL_GET_TASKLIST = value;
    }

    /**
     * Gets the URL_REFRESHTASK
     */
    public String getURL_REFRESHTASK() {
        return this.URL_REFRESHTASK;
    }

    /**
     * Sets the URL_REFRESHTASK
     */
    public void setURL_REFRESHTASK(String value) {
        this.URL_REFRESHTASK = value;
    }

    /**
     * Gets the URL_SUBMITTASK
     */
    public String getURL_SUBMITTASK() {
        return this.URL_SUBMITTASK;
    }

    /**
     * Sets the URL_SUBMITTASK
     */
    public void setURL_SUBMITTASK(String value) {
        this.URL_SUBMITTASK = value;
    }

    /**
     * Gets the URL_SUBMITOPENREADTASK
     */
    public String getURL_SUBMITOPENREADTASK() {
        return this.URL_SUBMITOPENREADTASK;
    }

    /**
     * Sets the URL_SUBMITOPENREADTASK
     */
    public void setURL_SUBMITOPENREADTASK(String value) {
        this.URL_SUBMITOPENREADTASK = value;
    }

    /**
     * Gets the URL_GET_QUESTIONSET
     */
    public String getURL_GET_QUESTIONSET() {
        return this.URL_GET_QUESTIONSET;
    }

    /**
     * Sets the URL_GET_QUESTIONSET
     */
    public void setURL_GET_QUESTIONSET(String value) {
        this.URL_GET_QUESTIONSET = value;
    }

    /**
     * Gets the URL_GET_VERIFICATION
     */
    public String getURL_GET_VERIFICATION() {
        return this.URL_GET_VERIFICATION;
    }

    /**
     * Sets the URL_GET_VERIFICATION
     */
    public void setURL_GET_VERIFICATION(String value) {
        this.URL_GET_VERIFICATION = value;
    }

    /**
     * Gets the URL_GET_IMAGE
     */
    public String getURL_GET_IMAGE() {
        return this.URL_GET_IMAGE;
    }

    /**
     * Sets the URL_GET_IMAGE
     */
    public void setURL_GET_IMAGE(String value) {
        this.URL_GET_IMAGE = value;
    }

    /**
     * Gets the URL_GET_SCHEME
     */
    public String getURL_GET_SCHEME() {
        return this.URL_GET_SCHEME;
    }

    /**
     * Sets the URL_GET_SCHEME
     */
    public void setURL_GET_SCHEME(String value) {
        this.URL_GET_SCHEME = value;
    }

    /**
     * Gets the URL_PRESUBMIT
     */
    public String getURL_PRESUBMIT() {
        return this.URL_PRESUBMIT;
    }

    /**
     * Sets the URL_PRESUBMIT
     */
    public void setURL_PRESUBMIT(String value) {
        this.URL_PRESUBMIT = value;
    }

    /**
     * Gets the URL_GET_SVYPERFORMANCE
     */
    public String getURL_GET_SVYPERFORMANCE() {
        return this.URL_GET_SVYPERFORMANCE;
    }

    /**
     * Sets the URL_GET_SVYPERFORMANCE
     */
    public void setURL_GET_SVYPERFORMANCE(String value) {
        this.URL_GET_SVYPERFORMANCE = value;
    }

    /**
     * Gets the URL_GET_LOOKUP
     */
    public String getURL_GET_LOOKUP() {
        return this.URL_GET_LOOKUP;
    }

    /**
     * Sets the URL_GET_LOOKUP
     */
    public void setURL_GET_LOOKUP(String value) {
        this.URL_GET_LOOKUP = value;
    }

    /**
     * Gets the URL_GET_LIST_REASSIGNMENT
     */
    public String getURL_GET_LIST_REASSIGNMENT() {
        return this.URL_GET_LIST_REASSIGNMENT;
    }

    /**
     * Sets the URL_GET_LIST_REASSIGNMENT
     */
    public void setURL_GET_LIST_REASSIGNMENT(String value) {
        this.URL_GET_LIST_REASSIGNMENT = value;
    }

    /**
     * Gets the URL_GET_DETAIL_REASSIGNMENT
     */
    public String getURL_GET_DETAIL_REASSIGNMENT() {
        return this.URL_GET_DETAIL_REASSIGNMENT;
    }

    /**
     * Sets the URL_GET_DETAIL_REASSIGNMENT
     */
    public void setURL_GET_DETAIL_REASSIGNMENT(String value) {
        this.URL_GET_DETAIL_REASSIGNMENT = value;
    }

    /**
     * Gets the URL_GET_LIST_ASSIGNMENT
     */
    public String getURL_GET_LIST_ASSIGNMENT() {
        return this.URL_GET_LIST_ASSIGNMENT;
    }

    /**
     * Sets the URL_GET_LIST_ASSIGNMENT
     */
    public void setURL_GET_LIST_ASSIGNMENT(String value) {
        this.URL_GET_LIST_ASSIGNMENT = value;
    }

    /**
     * Gets the URL_GET_DETAIL_ASSIGNMENT
     */
    public String getURL_GET_DETAIL_ASSIGNMENT() {
        return this.URL_GET_DETAIL_ASSIGNMENT;
    }

    /**
     * Sets the URL_GET_DETAIL_ASSIGNMENT
     */
    public void setURL_GET_DETAIL_ASSIGNMENT(String value) {
        this.URL_GET_DETAIL_ASSIGNMENT = value;
    }

    /**
     * Gets the URL_GET_DETAIL_ORDER
     */
    public String getURL_GET_DETAIL_ORDER() {
        return this.URL_GET_DETAIL_ORDER;
    }

    /**
     * Sets the URL_GET_DETAIL_ORDER
     */
    public void setURL_GET_DETAIL_ORDER(String value) {
        this.URL_GET_DETAIL_ORDER = value;
    }

    /**
     * Gets the URL_GET_DETAIL_TASK
     */
    public String getURL_GET_DETAIL_TASK() {
        return this.URL_GET_DETAIL_TASK;
    }

    /**
     * Sets the URL_GET_DETAIL_TASK
     */
    public void setURL_GET_DETAIL_TASK(String value) {
        this.URL_GET_DETAIL_TASK = value;
    }

    /**
     * Gets the URL_SUBMIT_ASSIGN
     */
    public String getURL_SUBMIT_ASSIGN() {
        return this.URL_SUBMIT_ASSIGN;
    }

    /**
     * Sets the URL_SUBMIT_ASSIGN
     */
    public void setURL_SUBMIT_ASSIGN(String value) {
        this.URL_SUBMIT_ASSIGN = value;
    }

    /**
     * Gets the URL_GET_LIST_VERIFICATION
     */
    public String getURL_GET_LIST_VERIFICATION() {
        return this.URL_GET_LIST_VERIFICATION;
    }

    /**
     * Sets the URL_GET_LIST_VERIFICATION
     */
    public void setURL_GET_LIST_VERIFICATION(String value) {
        this.URL_GET_LIST_VERIFICATION = value;
    }

    /**
     * Gets the URL_GET_LIST_APPROVAL
     */
    public String getURL_GET_LIST_APPROVAL() {
        return this.URL_GET_LIST_APPROVAL;
    }

    /**
     * Sets the URL_GET_LIST_APPROVAL
     */
    public void setURL_GET_LIST_APPROVAL(String value) {
        this.URL_GET_LIST_APPROVAL = value;
    }

    /**
     * Gets the URL_SENDDEPOSITREPORT
     */
    public String getURL_SENDDEPOSITREPORT() {
        return this.URL_SENDDEPOSITREPORT;
    }

    /**
     * Sets the URL_SENDDEPOSITREPORT
     */
    public void setURL_SENDDEPOSITREPORT(String value) {
        this.URL_SENDDEPOSITREPORT = value;
    }

    /**
     * Gets the URL_GET_PAYMENTHISTORY
     */
    public String getURL_GET_PAYMENTHISTORY() {
        return this.URL_GET_PAYMENTHISTORY;
    }

    /**
     * Sets the URL_GET_PAYMENTHISTORY
     */
    public void setURL_GET_PAYMENTHISTORY(String value) {
        this.URL_GET_PAYMENTHISTORY = value;
    }

    /**
     * Gets the URL_GET_INSTALLMENTSCHEDULE
     */
    public String getURL_GET_INSTALLMENTSCHEDULE() {
        return this.URL_GET_INSTALLMENTSCHEDULE;
    }

    /**
     * Sets the URL_GET_INSTALLMENTSCHEDULE
     */
    public void setURL_GET_INSTALLMENTSCHEDULE(String value) {
        this.URL_GET_INSTALLMENTSCHEDULE = value;
    }

    /**
     * Gets the URL_GET_COLLECTIONHISTORY
     */
    public String getURL_GET_COLLECTIONHISTORY() {
        return this.URL_GET_COLLECTIONHISTORY;
    }

    /**
     * Sets the URL_GET_COLLECTIONHISTORY
     */
    public void setURL_GET_COLLECTIONHISTORY(String value) {
        this.URL_GET_COLLECTIONHISTORY = value;
    }

    /**
     * Gets the URL_SUBMITVERIFICATIONTASK
     */
    public String getURL_SUBMITVERIFICATIONTASK() {
        return this.URL_SUBMITVERIFICATIONTASK;
    }

    /**
     * Sets the URL_SUBMITVERIFICATIONTASK
     */
    public void setURL_SUBMITVERIFICATIONTASK(String value) {
        this.URL_SUBMITVERIFICATIONTASK = value;
    }

    /**
     * Gets the URL_SUBMITAPPROVALTASK
     */
    public String getURL_SUBMITAPPROVALTASK() {
        return this.URL_SUBMITAPPROVALTASK;
    }

    /**
     * Sets the URL_SUBMITAPPROVALTASK
     */
    public void setURL_SUBMITAPPROVALTASK(String value) {
        this.URL_SUBMITAPPROVALTASK = value;
    }

    /**
     * Gets the URL_GET_CONTENTNEWS
     */
    public String getURL_GET_CONTENTNEWS() {
        return this.URL_GET_CONTENTNEWS;
    }

    /**
     * Sets the URL_GET_CONTENTNEWS
     */
    public void setURL_GET_CONTENTNEWS(String value) {
        this.URL_GET_CONTENTNEWS = value;
    }

    /**
     * Gets the URL_CHECKORDER
     */
    public String getURL_CHECKORDER() {
        return this.URL_CHECKORDER;
    }

    /**
     * Sets the URL_CHECKORDER
     */
    public void setURL_CHECKORDER(String value) {
        this.URL_CHECKORDER = value;
    }

    /**
     * Gets the URL_GET_TASK
     */
    public String getURL_GET_TASK() {
        return this.URL_GET_TASK;
    }

    /**
     * Sets the URL_GET_TASK
     */
    public void setURL_GET_TASK(String value) {
        this.URL_GET_TASK = value;
    }

    /**
     * Gets the URL_GET_NEWSHEADER
     */
    public String getURL_GET_NEWSHEADER() {
        return this.URL_GET_NEWSHEADER;
    }

    /**
     * Sets the URL_GET_NEWSHEADER
     */
    public void setURL_GET_NEWSHEADER(String value) {
        this.URL_GET_NEWSHEADER = value;
    }

    /**
     * Gets the URL_GET_NEWSCONTENT
     */
    public String getURL_GET_NEWSCONTENT() {
        return this.URL_GET_NEWSCONTENT;
    }

    /**
     * Sets the URL_GET_NEWSCONTENT
     */
    public void setURL_GET_NEWSCONTENT(String value) {
        this.URL_GET_NEWSCONTENT = value;
    }

    /**
     * Gets the URL_GET_LIST_CANCELORDER
     */
    public String getURL_GET_LIST_CANCELORDER() {
        return this.URL_GET_LIST_CANCELORDER;
    }

    /**
     * Sets the URL_GET_LIST_CANCELORDER
     */
    public void setURL_GET_LIST_CANCELORDER(String value) {
        this.URL_GET_LIST_CANCELORDER = value;
    }

    /**
     * Gets the URL_GET_DETAIL_CANCELORDER
     */
    public String getURL_GET_DETAIL_CANCELORDER() {
        return this.URL_GET_DETAIL_CANCELORDER;
    }

    /**
     * Sets the URL_GET_DETAIL_CANCELORDER
     */
    public void setURL_GET_DETAIL_CANCELORDER(String value) {
        this.URL_GET_DETAIL_CANCELORDER = value;
    }

    /**
     * Gets the URL_GET_CANCELORDER
     */
    public String getURL_GET_CANCELORDER() {
        return this.URL_GET_CANCELORDER;
    }

    /**
     * Sets the URL_GET_CANCELORDER
     */
    public void setURL_GET_CANCELORDER(String value) {
        this.URL_GET_CANCELORDER = value;
    }

    /**
     * Gets the URL_SUBMIT_TRACK
     */
    public String getURL_SUBMIT_TRACK() {
        return this.URL_SUBMIT_TRACK;
    }

    /**
     * Sets the URL_SUBMIT_TRACK
     */
    public void setURL_SUBMIT_TRACK(String value) {
        this.URL_SUBMIT_TRACK = value;
    }

    /**
     * Gets the URL_RETRIECECOLLECTIONTASK
     */
    public String getURL_RETRIECECOLLECTIONTASK() {
        return this.URL_RETRIECECOLLECTIONTASK;
    }

    /**
     * Sets the URL_RETRIECECOLLECTIONTASK
     */
    public void setURL_RETRIECECOLLECTIONTASK(String value) {
        this.URL_RETRIECECOLLECTIONTASK = value;
    }

    /**
     * Gets the URL_SYNCPARAM
     */
    public String getURL_SYNCPARAM() {
        return this.URL_SYNCPARAM;
    }

    /**
     * Sets the URL_SYNCPARAM
     */
    public void setURL_SYNCPARAM(String value) {
        this.URL_SYNCPARAM = value;
    }

    /**
     * Gets the URL_GET_REPORTSUMMARY
     */
    public String getURL_GET_REPORTSUMMARY() {
        return this.URL_GET_REPORTSUMMARY;
    }

    /**
     * Sets the URL_GET_REPORTSUMMARY
     */
    public void setURL_GET_REPORTSUMMARY(String value) {
        this.URL_GET_REPORTSUMMARY = value;
    }

    /**
     * Gets the URL_SUBMIT_RESCHEDULE
     */
    public String getURL_SUBMIT_RESCHEDULE() {
        return this.URL_SUBMIT_RESCHEDULE;
    }

    /**
     * Sets the URL_SUBMIT_RESCHEDULE
     */
    public void setURL_SUBMIT_RESCHEDULE(String value) {
        this.URL_SUBMIT_RESCHEDULE = value;
    }

    /**
     * Gets the URL_GET_LIST_USER
     */
    public String getURL_GET_LIST_USER() {
        return this.URL_GET_LIST_USER;
    }

    /**
     * Sets the URL_GET_LIST_USER
     */
    public void setURL_GET_LIST_USER(String value) {
        this.URL_GET_LIST_USER = value;
    }

    /**
     * Gets the URL_CHECK_UPDATE
     */
    public String getURL_CHECK_UPDATE() {
        return this.URL_CHECK_UPDATE;
    }

    /**
     * Sets the URL_CHECK_UPDATE
     */
    public void setURL_CHECK_UPDATE(String value) {
        this.URL_CHECK_UPDATE = value;
    }

    /**
     * Gets the URL_CHECK_RESUBMIT
     */
    public String getURL_CHECK_RESUBMIT() {
        return this.URL_CHECK_RESUBMIT;
    }

    /**
     * Sets the URL_CHECK_RESUBMIT
     */
    public void setURL_CHECK_RESUBMIT(String value) {
        this.URL_CHECK_RESUBMIT = value;
    }

    /**
     * Setter and getter for the URL_GET_OTR
     */
    public String getURL_GET_OTR() {
        return this.URL_GET_OTR;
    }

    public void setURL_GET_OTR(String urlGetOtr) {
        this.URL_GET_OTR = urlGetOtr;
    }

    public String getURL_GET_RECAPITULATE() {
        return this.URL_GET_RECAPITULATE;
    }

    public void setURL_GET_RECAPITULATE(String value) {
        this.URL_GET_RECAPITULATE = value;
    }

    public String getURL_SYNCPARAM_CONSTRAINT() {
        return this.URL_SYNCPARAM_CONSTRAINT;
    }

    public void setURL_SYNCPARAM_CONSTRAINT(String urlSyncParamConstraint) {
        this.URL_SYNCPARAM_CONSTRAINT = urlSyncParamConstraint;
    }

    public String getURL_SYNCTABLE_CONSTRAINT() { return this.URL_SYNCTABLE_CONSTRAINT; }

    public void setURL_SYNCTABLE_CONSTRAINT(String urlSyncTableConstraint) {
        this.URL_SYNCTABLE_CONSTRAINT = urlSyncTableConstraint;
    }

    public int getGreenAccuracy() {
        return this.greenAccuracy;
    }

    public void setGreenAccuracy(int value) {
        this.greenAccuracy = value;
    }

    public int getYellowAccuracy() {
        return this.yellowAccuracy;
    }

    public void setYellowAccuracy(int value) {
        this.yellowAccuracy = value;
    }

    public int getMaxAccuracySafely() {
        return this.maxAccuracySafely;
    }

    public void setMaxAccuracySafely(int value) {
        this.maxAccuracySafely = value;
    }

    public String getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(String type) {
        this.currencyType = type;
    }

    /** Property PROMISE_SURVEY */
    String URL_PROMISE_SURVEY;

    /** Property INQUIRY_TASK_H */
    String URL_INQUIRY_TASKH;

    /** Property INQUIRY_TASK_D */
    String URL_INQUIRY_TASKD;

    /** Property MULTI LOGIN */
    String URL_MULTILOGIN;

    /** Property RECOVERY PASSWORD */
    String URL_RECOVERY_PASSWORD;

    /** Property INVITATION ESIGN */
    String URL_INVITATION_ESIGN;

    String URL_SUBMIT_LAYER;

    String URL_RESULT_TELECHECK;

    String URL_GET_DSR;

    boolean isDevMode;

    public String getURL_MULTILOGIN() {
        return URL_MULTILOGIN;
    }

    public void setURL_MULTILOGIN(String urlMultiLogin) {
        this.URL_MULTILOGIN = urlMultiLogin;
    }

    public String getURL_RECOVERY_PASSWORD() {
        return URL_RECOVERY_PASSWORD;
    }

    public void setURL_RECOVERY_PASSWORD(String urlRecoveryPassword) {
        this.URL_RECOVERY_PASSWORD = urlRecoveryPassword;
    }

    public String getURL_PROMISE_SURVEY() {
        return URL_PROMISE_SURVEY;
    }

    public void setURL_PROMISE_SURVEY(String urlPromiseSurvey) {
        this.URL_PROMISE_SURVEY = urlPromiseSurvey;
    }

    public String getURL_INQUIRY_TASKH() {
        return URL_INQUIRY_TASKH;
    }

    public void setURL_INQUIRY_TASKH(String urlInquiryTaskH) {
        this.URL_INQUIRY_TASKH = urlInquiryTaskH;
    }

    public String getURL_INQUIRY_TASKD() {
        return URL_INQUIRY_TASKD;
    }

    public void setURL_INQUIRY_TASKD(String urlInquiryTaskD) {
        this.URL_INQUIRY_TASKD = urlInquiryTaskD;
    }

    /**
     * Gets the URL_INVITATION_ESIGN
     */
    public String getURL_INVITATION_ESIGN() {
        return URL_INVITATION_ESIGN;
    }

    /**
     * Sets the URL_INVITATION_ESIGN
     */
    public void setURL_INVITATION_ESIGN(String urlInvitationESign) {
        this.URL_INVITATION_ESIGN = urlInvitationESign;
    }

    public String getURL_SUBMIT_LAYER() {
        return URL_SUBMIT_LAYER;
    }

    public void setURL_SUBMIT_LAYER(String urlSubmitLayer) {
        this.URL_SUBMIT_LAYER = urlSubmitLayer;
    }

    public String getURL_RESULT_TELECHECK() {
        return URL_RESULT_TELECHECK;
    }

    public void setURL_RESULT_TELECHECK(String urlResultTeleCheck) {
        this.URL_RESULT_TELECHECK = urlResultTeleCheck;
    }

    public String getURL_GET_DSR() {
        return URL_GET_DSR;
    }

    public void setURL_GET_DSR(String urlGetDsr) {
        this.URL_GET_DSR = urlGetDsr;
    }

    /**
     * Gets the URL_GET_TASK_H_REMINDER_PO
     */
    public String getURL_GET_TASK_H_REMINDER_PO() {
        return URL_GET_TASK_H_REMINDER_PO;
    }

    /**
     * Sets the URL_GET_TASK_H_REMINDER_PO
     */
    public void setURL_GET_TASK_H_REMINDER_PO(String urlGetTaskHReminderPo) {
        this.URL_GET_TASK_H_REMINDER_PO = urlGetTaskHReminderPo;
    }

    String URL_REGISTRATION_CHECK;

    public String getURL_REGISTRATION_CHECK () {
        return URL_REGISTRATION_CHECK;
    }

    public void setURL_REGISTRATION_CHECK(String urlRegistrationCheck) {
        this.URL_REGISTRATION_CHECK = urlRegistrationCheck;
    }

    public String getURL_GET_CEK_REFERANTOR () {
        return URL_GET_CEK_REFERANTOR;
    }

    public void setURL_GET_CEK_REFERANTOR(String urlGetCekReferantor) {
        this.URL_GET_CEK_REFERANTOR = urlGetCekReferantor;
    }

    public String getURL_VALIDATION_CHECK_QUESTION() {
        return URL_VALIDATION_CHECK_QUESTION;
    }

    public void setURL_VALIDATION_CHECK_QUESTION(String URL_VALIDATION_CHECK_QUESTION) {
        this.URL_VALIDATION_CHECK_QUESTION = URL_VALIDATION_CHECK_QUESTION;
    }

    public boolean isDevMode() {
        return isDevMode;
    }

    public void setDevMode(boolean devMode) {
        isDevMode = devMode;
    }
}
