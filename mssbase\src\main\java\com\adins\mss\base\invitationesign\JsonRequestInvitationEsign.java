package com.adins.mss.base.invitationesign;

import com.adins.mss.foundation.http.MssRequestType;
import com.google.gson.annotations.SerializedName;

import java.util.HashMap;

public class JsonRequestInvitationEsign extends MssRequestType {

    @SerializedName("form_name")
    private String formName;

    @SerializedName("map_values")
    private HashMap<String, Object> mapValues;

    public String getFormName() {
        return formName;
    }

    public void setFormName(String formName) {
        this.formName = formName;
    }

    public HashMap<String, Object> getMapValues() {
        return mapValues;
    }

    public void setMapValues(HashMap<String, Object> mapValues) {
        this.mapValues = mapValues;
    }

}
