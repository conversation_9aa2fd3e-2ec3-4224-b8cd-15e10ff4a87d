package com.adins.mss.base.util;

import android.os.Environment;
import android.util.Log;

import com.adins.mss.base.commons.Helper;

import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.apache.commons.net.io.CopyStreamAdapter;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;

public class FtpHelper {
    private FTPClient mFtpClient;
    public FtpHelper() {}

    public boolean connect(String host, String username, String password) {

        try {
            mFtpClient = new FTPClient();
            mFtpClient.connect(host);

            if (FTPReply.isPositiveCompletion(mFtpClient.getReply())) {
                boolean status = mFtpClient.login(username, password);
                mFtpClient.setFileType(FTP.BINARY_FILE_TYPE);
                mFtpClient.enterLocalPassiveMode();
                return status;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }

    public static void download(String host, String username, String password, String filename, int port, final OnProgress listener) {
        FTPClient mFtpClient = null;

        try {
            mFtpClient = new FTPClient();
            mFtpClient.connect(host, port);
            Log.d("FTP", "Connected. Reply: " + mFtpClient.getReplyString());

//            if (FTPReply.isPositiveCompletion(mFtpClient.getReply())) {
                mFtpClient.login(username, password);
                Log.d("FTP", "Logged in");

                mFtpClient.enterLocalPassiveMode();

                long fileSize = 0;
                FTPFile[] files = mFtpClient.listFiles(filename);
                if (files.length == 1 && files[0].isFile()) {
                    fileSize = files[0].getSize();
                }
                Log.d("FTP", "File size = " + fileSize);

//                FTPFile file = mFtpClient.mlistFile(filename);
//                final long mSize   = file.getSize();

                mFtpClient.setFileType(FTP.BINARY_FILE_TYPE);

                OutputStream outputStream = null;
                try {
                    File pathFolder = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).getPath());
                    if (!pathFolder.exists()) {
                        pathFolder.mkdirs();
                    }

                    File destFile   = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).getPath() +
                            "/" + Helper.getInstance().getNameFromUrl(filename));

//                    outputStream  = new BufferedOutputStream(new FileOutputStream(new File(Environment.getExternalStorageDirectory().getPath() + "/Download/"
//                            + filename)));
                    outputStream  = new BufferedOutputStream(new FileOutputStream(destFile));

                    final long finalFileSize = fileSize;
                    CopyStreamAdapter streamAdapter = new CopyStreamAdapter() {
                        @Override
                        public void bytesTransferred(long totalBytesTransferred, int bytesTransferred, long streamSize) {
                            super.bytesTransferred(totalBytesTransferred, bytesTransferred, streamSize);
                            int percent = (int)(totalBytesTransferred*100/ finalFileSize);
                            listener.onUpdate(percent);

                        }
                    };

                    mFtpClient.setCopyStreamListener(streamAdapter);
                    mFtpClient.retrieveFile(filename, outputStream);
                } finally {
                    if (outputStream != null) {
                        outputStream.close();
                    }
                }
//            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (mFtpClient != null) {
                try {
                    mFtpClient.logout();
                    mFtpClient.disconnect();
                } catch (IOException e) {
                    e.printStackTrace();
                }

            }
        }
    }

    public interface OnProgress {
        void onUpdate(Integer progress);
    }
}
