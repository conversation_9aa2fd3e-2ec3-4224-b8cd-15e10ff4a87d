package com.adins.mss.foundation.db.dataaccess;

import android.content.Context;
import android.database.Cursor;
import android.util.Log;

import com.adins.mss.base.commons.Query;
import com.adins.mss.base.dynamicform.Constant;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.dao.DaoSession;
import com.adins.mss.dao.Lookup;
import com.adins.mss.dao.POAsset;
import com.adins.mss.dao.POAssetDao;
import com.adins.mss.foundation.camerainapp.helper.Logger;
import com.adins.mss.foundation.db.DaoOpenHelper;
import com.adins.mss.foundation.questiongenerator.OptionAnswerBean;
import com.adins.mss.foundation.questiongenerator.QuestionBean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import de.greenrobot.dao.query.QueryBuilder;
import de.greenrobot.dao.query.WhereCondition;

/**
 * Created by developer on 1/18/18.
 */

public class POAssetDataAccess {
    public static final String BRAND_NAME = "BRAND_NAME";
    public static final String BRAND_CODE = "BRAND_CODE";
    public static final String MODEL_NAME = "MODEL_NAME";
    public static final String MODEL_CODE = "MODEL_CODE";
    public static final String GROUP_TYPE = "GROUP_TYPE";
    public static final String MASTER_CODE= "MASTER_CODE";
    public static final String MASTER_NAME= "MASTER_NAME";

    /**
     * use to generate dao session that you can access modelDao
     *
     * @param context --> context from activity
     * @return
     */
    protected static DaoSession getDaoSession(Context context) {
        return DaoOpenHelper.getDaoSession(context);
    }

    /**
     * get POAssetDao and you can access db
     */
    protected static POAssetDao getPOAssetDao(Context context) {
        return getDaoSession(context).getPOAssetDao();
    }

    /**
     * add po asset as entity
     */
    public static void add(Context context, POAsset poAsset) {
        getPOAssetDao(context).insert(poAsset);
        getDaoSession(context).clear();
    }

    /**
     * add po asset as list
     */
    public static void add(Context context, List<POAsset> entities) {
        getPOAssetDao(context).insertInTx(entities);
        getDaoSession(context).clear();
    }

    /**
     * add or replace entity
     */
    public static void addOrReplace(Context context, POAsset poAsset) {
        getPOAssetDao(context).insertOrReplace(poAsset);
        getDaoSession(context).clear();
    }

    public static void addOrReplace(Context context, List<POAsset> assets) {
        getPOAssetDao(context).insertOrReplaceInTx(assets);
        getDaoSession(context).clear();
    }

    /**
     * Delete transaction
     */
    public static void delete(Context context, POAsset asset) {
        getPOAssetDao(context).delete(asset);
        getDaoSession(context).clear();
    }

    public static void clean(Context context) {
        getPOAssetDao(context).deleteAll();
        getDaoSession(context).clear();
    }

    /**
     * Get Asset by Query Parameter
     */
    public static POAsset find(Context context, Query query) {
        QueryBuilder<POAsset> qb = getPOAssetDao(context).queryBuilder();

        for (Query.Constraint constraint : query.getConstraint()) {
            switch (constraint.getColumn()) {
                case "BRAND_NAME":
                    qb.where(POAssetDao.Properties.Brand_name.eq(constraint.getValue()));
                    break;
                case "MODEL_NAME":
                    qb.where(POAssetDao.Properties.Model_name.eq(constraint.getValue()));
                    break;
                case "GROUP_TYPE":
                    qb.where(POAssetDao.Properties.Group_type.eq(constraint.getValue()));
                    break;
                case "MASTER_NAME":
                    qb.where(POAssetDao.Properties.Master_name.eq(constraint.getValue()));
                    break;
            }
        }

        qb.build();

        if (qb.list().isEmpty()) return null;
        else return qb.list().get(0);
    }

    /**
     * Get Brand as List Entity
     */
    public static List<POAsset> getBrand(Context context, String brandName) {
        QueryBuilder<POAsset> qb = getPOAssetDao(context).queryBuilder();
        qb.where(POAssetDao.Properties.Brand_name.eq(brandName));
        qb.build();

        HashMap<String, POAsset> hashMap = new HashMap<>();
        List<POAsset> assets = new ArrayList<>();
        if (!qb.list().isEmpty()) {
            for (POAsset asset : qb.list()) {
                if (!assets.isEmpty()) {
                    if (!hashMap.containsKey(asset.getBrand_name())){
                        assets.add(asset);
                        hashMap.put(asset.getBrand_name(), asset);
                    }
                } else {
                    assets.add(asset);
                    if (!hashMap.containsKey(asset.getBrand_name()))
                        hashMap.put(asset.getBrand_name(), asset);
                }
            }
        }

        hashMap.clear();
        return assets;
    }

    /**
     * Get Brand as Specific Brand & Code
     */
    public static HashMap<String, String> mapBrand(Context context) {
        QueryBuilder<POAsset> qb = getPOAssetDao(context).queryBuilder();
        qb.where(new WhereCondition.StringCondition("1 GROUP BY BRAND_NAME"))
                .orderRaw("BRAND_NAME ASC")
                .build();

        int size = qb.list().size();
        if (size > 0) {
            HashMap<String, String> hashMap = new HashMap<>();
            for (POAsset asset : qb.list()) {
                hashMap.put(asset.getBrand_name(), asset.getBrand_code());
            }
            return hashMap;
        } else {
            return null;
        }
    }

    /**
     * Get Model as hasMap
     */
    public static HashMap<String, String> mapModel(Context context, String brand) {
        QueryBuilder<POAsset> qb = getPOAssetDao(context).queryBuilder();
        qb.where(new WhereCondition.StringCondition("BRAND_NAME = '" + brand + "' GROUP BY MODEL_NAME"))
                .build();

        int size = qb.list().size();
        if (size > 0) {
            HashMap<String, String> hashMap = new HashMap<>();
            for (POAsset asset : qb.list()) {
                hashMap.put(asset.getModel_name(), asset.getModel_code());
            }
            return hashMap;
        } else return null;
    }

    /**
     * Get Entity as Lookup
     */
    public static Lookup getOneAsLookup(Context context, QuestionBean qBean, long id) {
        QueryBuilder<POAsset> qb = getPOAssetDao(context).queryBuilder();
        qb.where(POAssetDao.Properties.Id.eq(id));
        qb.limit(1);
        qb.build();

        Lookup lookup = null;
        int size = qb.list().size();
        if (size != 0) {
            POAsset asset = qb.list().get(0);
            lookup = new Lookup(String.valueOf(asset.getId()));
            Query query = GsonHelper.fromJson(qBean.getChoice_filter(), Query.class);

            if (query.getCode().equalsIgnoreCase("BRAND_NAME"))
            {
                lookup.setCode(asset.getBrand_name());
                lookup.setValue(asset.getBrand_name());
            }
            else if (query.getCode().equalsIgnoreCase("MODEL_NAME"))
            {
                lookup.setCode(asset.getModel_name());
                lookup.setValue(asset.getModel_name());
            }
            else if (query.getCode().equalsIgnoreCase("GROUP_TYPE"))
            {
                lookup.setCode(asset.getGroup_type());
                lookup.setValue(asset.getGroup_type());
            }
            else if (query.getCode().equalsIgnoreCase("MASTER_NAME"))
            {
                lookup.setCode(asset.getMaster_name());
                lookup.setValue(asset.getMaster_name());
            }
//            else if (query.getCode().equalsIgnoreCase("PROD_OFF_ID"))
//            {
//                lookup.setCode(String.valueOf(asset.getProd_off_id()));
//                lookup.setValue(String.valueOf(asset.getProd_off_id()));
//            }

            lookup.setOption_id(String.valueOf(asset.getId()));
            lookup.setUuid_lookup(String.valueOf(asset.getId()));

            Constant.poAsset = asset;
        }

        return lookup;
    }

    /**
     * Get Brand as Lookup
     */
    public static List<OptionAnswerBean> lookup(Context context, Query ql, String group, String keyword) {
        List<OptionAnswerBean> answerBeans = new ArrayList<>();
        StringBuilder constraint = new StringBuilder("1");
        String groupBy  = " GROUP BY ";
        String code     = ql.getCode();
        String[] fields = ql.getFields();

        if (ql.getConstraint() != null) {
            List<Query.Constraint> constraints = ql.getConstraint();
            for (int i = 0; i < constraints.size(); i++) {
                constraint.append(" AND ")
                        .append(constraints.get(i).getColumn())
                        .append("=")
                        .append("'"+constraints.get(i).getValue()+"'");
            }

            switch (code) {
                case "BRAND_NAME":
                    constraint.append(" AND BRAND_NAME LIKE '%" + keyword.trim() + "%'");
                    break;
                case "MODEL_NAME":
                    constraint.append(" AND MODEL_NAME LIKE '%" + keyword.trim() + "%'");
                    break;
                case "GROUP_TYPE":
                    constraint.append(" AND GROUP_TYPE LIKE '%" + keyword.trim() + "%'");
                    break;
                case "MASTER_NAME":
                    constraint.append(" AND MASTER_NAME LIKE '%" + keyword.trim() + "%'");
                    break;
            }
        }

        //Print Query to Log Cat
        Logger.i("INFO", constraint.toString());
        if (ql.getGroup() != null) constraint.append(groupBy)
                .append(ql.getGroup());

        QueryBuilder<POAsset> qb = getPOAssetDao(context).queryBuilder();
        qb.where(new WhereCondition.StringCondition(String.valueOf(constraint)));
        qb.build();

        if (!qb.list().isEmpty()) {
            for (POAsset asset : qb.list()) {
                if (fields.length == 1) {
                    OptionAnswerBean optionAnswer = new OptionAnswerBean();

                    if (fields[0].equalsIgnoreCase("BRAND_NAME"))
                    {
                        optionAnswer.setOption_id(String.valueOf(asset.getId()));
                        optionAnswer.setCode(asset.getBrand_name());
                        optionAnswer.setValue(asset.getBrand_name());
                    }
                    else if (fields[0].equalsIgnoreCase("MODEL_NAME"))
                    {
                        optionAnswer.setOption_id(String.valueOf(asset.getId()));
                        optionAnswer.setCode(asset.getModel_name());
                        optionAnswer.setValue(asset.getModel_name());
                    }
                    else if (fields[0].equalsIgnoreCase("GROUP_TYPE"))
                    {
                        optionAnswer.setOption_id(String.valueOf(asset.getId()));
                        optionAnswer.setCode(asset.getGroup_type());
                        optionAnswer.setValue(asset.getGroup_type());
                    }
                    else if (fields[0].equalsIgnoreCase("MASTER_NAME"))
                    {
                        optionAnswer.setOption_id(String.valueOf(asset.getId()));
                        optionAnswer.setCode(asset.getMaster_name());
                        optionAnswer.setValue(asset.getMaster_name());
                    }
//                    else if (fields[0].equalsIgnoreCase("PROD_OFF_ID"))
//                    {
//                        optionAnswer.setOption_id(String.valueOf(asset.getId()));
//                        optionAnswer.setCode(String.valueOf(asset.getProd_off_id()));
//                        optionAnswer.setValue(String.valueOf(asset.getProd_off_id()));
//                    }
                    optionAnswer.setUuid_lookup(optionAnswer.getOption_id());
                    optionAnswer.setLov_group("MS_PO_ASSET");
                    answerBeans.add(optionAnswer);
                } else {
                    OptionAnswerBean optionAnswer = new OptionAnswerBean(null,null,null);
                    answerBeans.add(optionAnswer);
                }
            }
        }

        return answerBeans;
    }

    public static List<OptionAnswerBean> lookup(Context context, Query query, String keyword) {
        List<OptionAnswerBean> answerBeans = new ArrayList<>();
//        String statement = "SELECT t1.* FROM MS_PO_ASSET t1 LEFT JOIN MS_ASSET_SCHEME t2 ON t1.ASSET_SCHEME_ID = t2.ASSET_SCHEME_ID LEFT JOIN MS_PO t3 ON t1.ASSET_SCHEME_ID = t3.ASSET_SCHEME_ID LEFT JOIN MS_PO_DEALER t4 ON t3.PROD_OFF_ID = t4.PROD_OFF_ID WHERE %s";
        String statement = "SELECT MS_PO_ASSET.* FROM MS_PO_ASSET LEFT JOIN MS_ASSET_SCHEME ON MS_PO_ASSET.ASSET_SCHEME_ID = MS_ASSET_SCHEME.ASSET_SCHEME_ID LEFT JOIN MS_PO ON MS_PO_ASSET.ASSET_SCHEME_ID = MS_PO.ASSET_SCHEME_ID LEFT JOIN MS_PO_DEALER ON MS_PO.DEALER_SCHEME_ID = MS_PO_DEALER.DEALER_SCHEME_ID WHERE %s";

        StringBuilder constraint = new StringBuilder("1");
        String groupBy  = " GROUP BY ";
        String code     = query.getCode();
        String[] fields = query.getFields();

        if (query.getConstraint() != null) {
            List<Query.Constraint> constraints = query.getConstraint();
            for (int i = 0; i < constraints.size(); i++) {
                constraint.append(" AND ")
                        .append(constraints.get(i).getColumn());

                if (constraints.get(i).getOperator() != null) {
                    constraint.append(" ").append(constraints.get(i).getOperator().trim()).append(" ");
                    if (constraints.get(i).getOperator().toLowerCase().contains("like")) {
                        constraint.append("'%"+constraints.get(i).getValue()+"%'");
                    }
                    else if (constraints.get(i).getOperator().toLowerCase().contains("in")) {
                        constraint.append("(");
                        constraint.append(constraints.get(i).getValue());
                        constraint.append(")");
                    }
                    else {
                        constraint.append("'"+constraints.get(i).getValue()+"'");
                    }
                } else {
                    constraint.append("=");
                    constraint.append("'"+constraints.get(i).getValue()+"'");
                }
            }
        }

        if (keyword != null || !keyword.equalsIgnoreCase(" ")) {
            switch (code) {
                case BRAND_NAME:
                    constraint.append(" AND BRAND_NAME LIKE '%" + keyword.trim() + "%'");
                    break;
                case MODEL_NAME:
                    constraint.append(" AND MODEL_NAME LIKE '%" + keyword.trim() + "%'");
                    break;
                case GROUP_TYPE:
                    constraint.append(" AND GROUP_TYPE LIKE '%" + keyword.trim() + "%'");
                    break;
                case MASTER_NAME:
                    constraint.append(" AND MASTER_NAME LIKE '%" + keyword.trim() + "%'");
                    break;
            }
        }

        if (query.getGroup() != null) constraint.append(groupBy)
                .append(query.getGroup());

        statement = String.format(statement, String.valueOf(constraint));
        Log.d(POAssetDataAccess.class.getSimpleName(), statement);

        Cursor cursor = getDaoSession(context)
                .getDatabase()
                .rawQuery(statement, null);

        try {
            if (cursor.moveToFirst()) {
                do {
                    OptionAnswerBean optionAnswer = new OptionAnswerBean();

                    switch (fields[0]) {
                        case BRAND_NAME:
                            optionAnswer.setOption_id(String.valueOf(cursor.getInt(cursor.getColumnIndex("ID"))));
                            optionAnswer.setUuid_lookup(optionAnswer.getOption_id());
                            optionAnswer.setCode(cursor.getString(cursor.getColumnIndex(BRAND_NAME)));
                            optionAnswer.setValue(cursor.getString(cursor.getColumnIndex(BRAND_NAME)));
                            optionAnswer.setLov_group("PO_ASSETS");
                            answerBeans.add(optionAnswer);
                            break;
                        case MODEL_NAME:
                            optionAnswer.setOption_id(String.valueOf(cursor.getInt(cursor.getColumnIndex("ID"))));
                            optionAnswer.setUuid_lookup(optionAnswer.getOption_id());
                            optionAnswer.setCode(cursor.getString(cursor.getColumnIndex(MODEL_NAME)));
                            optionAnswer.setValue(cursor.getString(cursor.getColumnIndex(MODEL_NAME)));
                            optionAnswer.setLov_group("PO_ASSETS");
                            answerBeans.add(optionAnswer);
                            break;
                        case GROUP_TYPE:
                            optionAnswer.setOption_id(String.valueOf(cursor.getInt(cursor.getColumnIndex("ID"))));
                            optionAnswer.setUuid_lookup(optionAnswer.getOption_id());
                            optionAnswer.setCode(cursor.getString(cursor.getColumnIndex(GROUP_TYPE)));
                            optionAnswer.setValue(cursor.getString(cursor.getColumnIndex(GROUP_TYPE)));
                            optionAnswer.setLov_group("PO_ASSETS");
                            answerBeans.add(optionAnswer);
                            break;
                        case MASTER_NAME:
                            optionAnswer.setOption_id(String.valueOf(cursor.getInt(cursor.getColumnIndex("ID"))));
                            optionAnswer.setUuid_lookup(optionAnswer.getOption_id());
                            optionAnswer.setCode(cursor.getString(cursor.getColumnIndex(MASTER_NAME)));
                            optionAnswer.setValue(cursor.getString(cursor.getColumnIndex(MASTER_NAME)));
                            optionAnswer.setLov_group("PO_ASSETS");
                            answerBeans.add(optionAnswer);
                            break;
                    }
                } while (cursor.moveToNext());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            cursor.close();
        }

        return answerBeans;
    }

    //NENDI: 2018-05-25 | Add Operator on Query
    public static List<OptionAnswerBean> lookup(Context context, Query query) {
        List<OptionAnswerBean> answerBeans = new ArrayList<>();
        String statement = "SELECT MS_PO_ASSET.* FROM MS_PO_ASSET LEFT JOIN MS_ASSET_SCHEME ON MS_PO_ASSET.ASSET_SCHEME_ID = MS_ASSET_SCHEME.ASSET_SCHEME_ID LEFT JOIN MS_PO ON MS_PO_ASSET.ASSET_SCHEME_ID = MS_PO.ASSET_SCHEME_ID LEFT JOIN MS_PO_DEALER ON MS_PO.DEALER_SCHEME_ID = MS_PO_DEALER.DEALER_SCHEME_ID WHERE %s";

        StringBuilder constraint = new StringBuilder("1");
        String groupBy  = " GROUP BY ";
        String[] fields = query.getFields();

        if (query.getConstraint() != null) {
            List<Query.Constraint> constraints = query.getConstraint();
            for (int i = 0; i < constraints.size(); i++) {
                constraint.append(" AND ")
                        .append(constraints.get(i).getColumn());

                if (constraints.get(i).getOperator() != null) {
                    constraint.append(" ").append(constraints.get(i).getOperator().trim()).append(" ");
                    if (constraints.get(i).getOperator().toLowerCase().contains("like")) {
                        constraint.append("'%"+constraints.get(i).getValue()+"%'");
                    }
                    else if (constraints.get(i).getOperator().toLowerCase().contains("in")) {
                        constraint.append("(");
                        constraint.append(constraints.get(i).getValue());
                        constraint.append(")");
                    }
                    else {
                        constraint.append("'"+constraints.get(i).getValue()+"'");
                    }
                } else {
                    constraint.append("=");
                    constraint.append("'"+constraints.get(i).getValue()+"'");
                }
            }
        }

        if (query.getGroup() != null) constraint.append(groupBy)
                .append(query.getGroup());

        statement = String.format(statement, String.valueOf(constraint));
        Log.d(PODataAccess.class.getSimpleName(), statement);

        Cursor cursor = getDaoSession(context)
                .getDatabase()
                .rawQuery(statement, null);

        try {
            if (cursor.moveToFirst()) {
                do {
                    OptionAnswerBean optionAnswer = new OptionAnswerBean();

                    switch (fields[0]) {
                        case BRAND_NAME:
                            optionAnswer.setOption_id(String.valueOf(cursor.getInt(cursor.getColumnIndex("ID"))));
                            optionAnswer.setUuid_lookup(optionAnswer.getOption_id());
                            optionAnswer.setCode(cursor.getString(cursor.getColumnIndex(BRAND_NAME)));
                            optionAnswer.setValue(cursor.getString(cursor.getColumnIndex(BRAND_NAME)));
                            optionAnswer.setLov_group("PO_ASSETS");
                            answerBeans.add(optionAnswer);
                            break;
                        case MODEL_NAME:
                            optionAnswer.setOption_id(String.valueOf(cursor.getInt(cursor.getColumnIndex("ID"))));
                            optionAnswer.setUuid_lookup(optionAnswer.getOption_id());
                            optionAnswer.setCode(cursor.getString(cursor.getColumnIndex(MODEL_NAME)));
                            optionAnswer.setValue(cursor.getString(cursor.getColumnIndex(MODEL_NAME)));
                            optionAnswer.setLov_group("PO_ASSETS");
                            answerBeans.add(optionAnswer);
                            break;
                        case GROUP_TYPE:
                            optionAnswer.setOption_id(String.valueOf(cursor.getInt(cursor.getColumnIndex("ID"))));
                            optionAnswer.setUuid_lookup(optionAnswer.getOption_id());
                            optionAnswer.setCode(cursor.getString(cursor.getColumnIndex(GROUP_TYPE)));
                            optionAnswer.setValue(cursor.getString(cursor.getColumnIndex(GROUP_TYPE)));
                            optionAnswer.setLov_group("PO_ASSETS");
                            answerBeans.add(optionAnswer);
                            break;
                        case MASTER_NAME:
                            optionAnswer.setOption_id(String.valueOf(cursor.getInt(cursor.getColumnIndex("ID"))));
                            optionAnswer.setUuid_lookup(optionAnswer.getOption_id());
                            optionAnswer.setCode(cursor.getString(cursor.getColumnIndex(MASTER_NAME)));
                            optionAnswer.setValue(cursor.getString(cursor.getColumnIndex(MASTER_NAME)));
                            optionAnswer.setLov_group("PO_ASSETS");
                            answerBeans.add(optionAnswer);
                            break;
                    }
                } while (cursor.moveToNext());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            cursor.close();
        }

        return answerBeans;
    }

//    public static List<OptionAnswerBean> lookup(Context context, Query ql, String group) {
//        List<OptionAnswerBean> answerBeans = new ArrayList<>();
//        StringBuilder constraint = new StringBuilder("1");
//        String groupBy  = " GROUP BY ";
//        String code     = ql.getCode();
//        String[] fields = ql.getFields();
//
//        if (ql.getConstraint() != null) {
//            List<Query.Constraint> constraints = ql.getConstraint();
//            for (int i = 0; i < constraints.size(); i++) {
//                constraint.append(" AND ")
//                        .append(constraints.get(i).getColumn())
//                        .append("=")
//                        .append("'"+constraints.get(i).getValue()+"'");
//            }
//        }
//
//        if (ql.getGroup() != null) constraint.append(groupBy)
//                .append(ql.getGroup());
//
//        QueryBuilder<POAsset> qb = getPOAssetDao(context).queryBuilder();
//        qb.where(new WhereCondition.StringCondition(String.valueOf(constraint))).build();
//
//        if (qb.list().size() > 0) {
//            for (POAsset asset : qb.list()) {
//                if (fields.length == 1) {
//                    OptionAnswerBean optionAnswer = new OptionAnswerBean();
//
//                    if (fields[0].equalsIgnoreCase("BRAND_NAME"))
//                    {
//                        optionAnswer.setOption_id(String.valueOf(asset.getId()));
//                        optionAnswer.setCode(asset.getBrand_name());
//                        optionAnswer.setValue(asset.getBrand_name());
//                    }
//                    else if (fields[0].equalsIgnoreCase("MODEL_NAME"))
//                    {
//                        optionAnswer.setOption_id(String.valueOf(asset.getId()));
//                        optionAnswer.setCode(asset.getModel_name());
//                        optionAnswer.setValue(asset.getModel_name());
//                    }
//                    else if (fields[0].equalsIgnoreCase("GROUP_TYPE"))
//                    {
//                        optionAnswer.setOption_id(String.valueOf(asset.getId()));
//                        optionAnswer.setCode(asset.getGroup_type());
//                        optionAnswer.setValue(asset.getGroup_type());
//                    }
//                    else if (fields[0].equalsIgnoreCase("MASTER_NAME"))
//                    {
//                        optionAnswer.setOption_id(String.valueOf(asset.getId()));
//                        optionAnswer.setCode(asset.getMaster_name());
//                        optionAnswer.setValue(asset.getMaster_name());
//                    }
////                    else if (fields[0].equalsIgnoreCase("PROD_OFF_ID"))
////                    {
////                        optionAnswer.setOption_id(String.valueOf(asset.getId()));
////                        optionAnswer.setCode(String.valueOf(asset.getProd_off_id()));
////                        optionAnswer.setValue(String.valueOf(asset.getProd_off_id()));
////                    }
//
//                    optionAnswer.setLov_group("MS_PO_ASSET");
//                    answerBeans.add(optionAnswer);
//                } else {
//                    OptionAnswerBean optionAnswer = new OptionAnswerBean(null,null,null);
//                    answerBeans.add(optionAnswer);
//                }
//            }
//        }
//
//        return answerBeans;
//    }

    public static POAsset getLast(Context context) {
        QueryBuilder<POAsset> qb = getPOAssetDao(context).queryBuilder();
        qb.orderDesc(POAssetDao.Properties.Dtm_upd);
        qb.limit(1);
        qb.build();

        if (!qb.list().isEmpty()) return qb.list().get(0);
        return null;
    }

    public static void addOrUpdateAll(Context context, List<POAsset> transaction) {
        getPOAssetDao(context).insertOrReplaceInTx(transaction);
        getDaoSession(context).clear();
    }
}
