package com.adins.mss.dao;

import com.adins.mss.dao.DaoSession;
import de.greenrobot.dao.DaoException;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "TR_LOCATION".
 */
public class LocationInfo {

    /** Not-null value. */
     @SerializedName("uuid_location_info")
    private String uuid_location_info;
     @SerializedName("latitude")
    private String latitude;
     @SerializedName("longitude")
    private String longitude;
     @SerializedName("mcc")
    private String mcc;
     @SerializedName("mnc")
    private String mnc;
     @SerializedName("lac")
    private String lac;
     @SerializedName("cid")
    private String cid;
     @SerializedName("handset_time")
    private java.util.Date handset_time;
     @SerializedName("mode")
    private String mode;
     @SerializedName("accuracy")
    private Integer accuracy;
     @SerializedName("gps_time")
    private java.util.Date gps_time;
     @SerializedName("is_gps_time")
    private String is_gps_time;
     @SerializedName("usr_crt")
    private String usr_crt;
     @SerializedName("dtm_crt")
    private java.util.Date dtm_crt;
     @SerializedName("location_type")
    private String location_type;
     @SerializedName("uuid_user")
    private String uuid_user;

    /** Used to resolve relations */
    private transient DaoSession daoSession;

    /** Used for active entity operations. */
    private transient LocationInfoDao myDao;

    private User user;
    private String user__resolvedKey;


    public LocationInfo() {
    }

    public LocationInfo(String uuid_location_info) {
        this.uuid_location_info = uuid_location_info;
    }

    public LocationInfo(String uuid_location_info, String latitude, String longitude, String mcc, String mnc, String lac, String cid, java.util.Date handset_time, String mode, Integer accuracy, java.util.Date gps_time, String is_gps_time, String usr_crt, java.util.Date dtm_crt, String location_type, String uuid_user) {
        this.uuid_location_info = uuid_location_info;
        this.latitude = latitude;
        this.longitude = longitude;
        this.mcc = mcc;
        this.mnc = mnc;
        this.lac = lac;
        this.cid = cid;
        this.handset_time = handset_time;
        this.mode = mode;
        this.accuracy = accuracy;
        this.gps_time = gps_time;
        this.is_gps_time = is_gps_time;
        this.usr_crt = usr_crt;
        this.dtm_crt = dtm_crt;
        this.location_type = location_type;
        this.uuid_user = uuid_user;
    }

    /** called by internal mechanisms, do not call yourself. */
    public void __setDaoSession(DaoSession daoSession) {
        this.daoSession = daoSession;
        myDao = daoSession != null ? daoSession.getLocationInfoDao() : null;
    }

    /** Not-null value. */
    public String getUuid_location_info() {
        return uuid_location_info;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_location_info(String uuid_location_info) {
        this.uuid_location_info = uuid_location_info;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getMcc() {
        return mcc;
    }

    public void setMcc(String mcc) {
        this.mcc = mcc;
    }

    public String getMnc() {
        return mnc;
    }

    public void setMnc(String mnc) {
        this.mnc = mnc;
    }

    public String getLac() {
        return lac;
    }

    public void setLac(String lac) {
        this.lac = lac;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public java.util.Date getHandset_time() {
        return handset_time;
    }

    public void setHandset_time(java.util.Date handset_time) {
        this.handset_time = handset_time;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public Integer getAccuracy() {
        return accuracy;
    }

    public void setAccuracy(Integer accuracy) {
        this.accuracy = accuracy;
    }

    public java.util.Date getGps_time() {
        return gps_time;
    }

    public void setGps_time(java.util.Date gps_time) {
        this.gps_time = gps_time;
    }

    public String getIs_gps_time() {
        return is_gps_time;
    }

    public void setIs_gps_time(String is_gps_time) {
        this.is_gps_time = is_gps_time;
    }

    public String getUsr_crt() {
        return usr_crt;
    }

    public void setUsr_crt(String usr_crt) {
        this.usr_crt = usr_crt;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public String getLocation_type() {
        return location_type;
    }

    public void setLocation_type(String location_type) {
        this.location_type = location_type;
    }

    public String getUuid_user() {
        return uuid_user;
    }

    public void setUuid_user(String uuid_user) {
        this.uuid_user = uuid_user;
    }

    /** To-one relationship, resolved on first access. */
    public User getUser() {
        String __key = this.uuid_user;
        if (user__resolvedKey == null || user__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            UserDao targetDao = daoSession.getUserDao();
            User userNew = targetDao.load(__key);
            synchronized (this) {
                user = userNew;
            	user__resolvedKey = __key;
            }
        }
        return user;
    }

    public void setUser(User user) {
        synchronized (this) {
            this.user = user;
            uuid_user = user == null ? null : user.getUuid_user();
            user__resolvedKey = uuid_user;
        }
    }

    /** Convenient call for {@link AbstractDao#delete(Object)}. Entity must attached to an entity context. */
    public void delete() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.delete(this);
    }

    /** Convenient call for {@link AbstractDao#update(Object)}. Entity must attached to an entity context. */
    public void update() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.update(this);
    }

    /** Convenient call for {@link AbstractDao#refresh(Object)}. Entity must attached to an entity context. */
    public void refresh() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.refresh(this);
    }

}
