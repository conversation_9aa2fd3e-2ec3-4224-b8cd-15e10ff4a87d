package com.adins.mss.base.decision;

import java.util.ArrayList;
import java.util.List;

public class ScoreCard {
	private float weight;
	private String fileCode;
	private Integer RejectedScore;
	private Integer ApprovedScore;
	private List<Float> listWeight = new ArrayList<Float>();
	private List<String> listSheet = new ArrayList<String>();

	public float getWeight() {
		return weight;
	}

	public void setWeight(float weight) {
		this.weight = weight;
	}
	
	public String getFileCode() {
		return fileCode;
	}

	public void setFileCode(String fileCode) {
		this.fileCode = fileCode;
	}


	public Integer getRejectedScore() {
		return RejectedScore;
	}

	public void setRejectedScore(Integer rejectedScore) {
		this.RejectedScore = rejectedScore;
	}

	public Integer getApprovedScore() {
		return ApprovedScore;
	}

	public void setApprovedScore(Integer approvedScore) {
		this.ApprovedScore = approvedScore;
	}
	
	public void AddFileCode(String fileCode) {
		this.fileCode = fileCode;
	}
	
	public List<String> getListSheet() {
		return this.listSheet;
	}

	public List<Float> getListWeight() {
		return listWeight;
	}
	
	public void AddWeight(float weight) {
		this.listWeight.add(weight);
	}
	
	public void AddSheetName(String sheetName) {
		this.listSheet.add(sheetName);
	}

}
