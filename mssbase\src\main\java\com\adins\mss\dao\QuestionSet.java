package com.adins.mss.dao;

import com.adins.mss.dao.DaoSession;
import de.greenrobot.dao.DaoException;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "MS_QUESTIONSET".
 */
public class QuestionSet {

    /** Not-null value. */
     @SerializedName("uuid_question_set")
    private String uuid_question_set;
     @SerializedName("question_group_id")
    private String question_group_id;
     @SerializedName("question_group_name")
    private String question_group_name;
     @SerializedName("question_group_order")
    private Integer question_group_order;
     @SerializedName("question_id")
    private String question_id;
     @SerializedName("question_label")
    private String question_label;
     @SerializedName("question_order")
    private Integer question_order;
     @SerializedName("answer_type")
    private String answer_type;
     @SerializedName("option_answers")
    private String option_answers;
     @SerializedName("choice_filter")
    private String choice_filter;
     @SerializedName("is_mandatory")
    private String is_mandatory;
     @SerializedName("max_length")
    private Integer max_length;
     @SerializedName("is_visible")
    private String is_visible;
     @SerializedName("is_readonly")
    private String is_readonly;
     @SerializedName("regex")
    private String regex;
     @SerializedName("relevant_question")
    private String relevant_question;
     @SerializedName("calculate")
    private String calculate;
     @SerializedName("constraint_message")
    private String constraint_message;
     @SerializedName("usr_crt")
    private String usr_crt;
     @SerializedName("dtm_crt")
    private java.util.Date dtm_crt;
     @SerializedName("usr_upd")
    private String usr_upd;
     @SerializedName("dtm_upd")
    private java.util.Date dtm_upd;
     @SerializedName("identifier_name")
    private String identifier_name;
     @SerializedName("uuid_scheme")
    private String uuid_scheme;
     @SerializedName("lov_group")
    private String lov_group;
     @SerializedName("tag")
    private String tag;
     @SerializedName("is_holiday_allowed")
    private String is_holiday_allowed;
     @SerializedName("img_quality")
    private String img_quality;
     @SerializedName("question_validation")
    private String question_validation;
     @SerializedName("question_value")
    private String question_value;
     @SerializedName("validate_err_message")
    private String validate_err_message;
     @SerializedName("form_version")
    private String form_version;
     @SerializedName("relevant_mandatory")
    private String relevant_mandatory;

    /** Used to resolve relations */
    private transient DaoSession daoSession;

    /** Used for active entity operations. */
    private transient QuestionSetDao myDao;

    private Scheme scheme;
    private String scheme__resolvedKey;


    public QuestionSet() {
    }

    public QuestionSet(String uuid_question_set) {
        this.uuid_question_set = uuid_question_set;
    }

    public QuestionSet(String uuid_question_set, String question_group_id, String question_group_name, Integer question_group_order, String question_id, String question_label, Integer question_order, String answer_type, String option_answers, String choice_filter, String is_mandatory, Integer max_length, String is_visible, String is_readonly, String regex, String relevant_question, String calculate, String constraint_message, String usr_crt, java.util.Date dtm_crt, String usr_upd, java.util.Date dtm_upd, String identifier_name, String uuid_scheme, String lov_group, String tag, String is_holiday_allowed, String img_quality, String question_validation, String question_value, String validate_err_message, String form_version, String relevant_mandatory) {
        this.uuid_question_set = uuid_question_set;
        this.question_group_id = question_group_id;
        this.question_group_name = question_group_name;
        this.question_group_order = question_group_order;
        this.question_id = question_id;
        this.question_label = question_label;
        this.question_order = question_order;
        this.answer_type = answer_type;
        this.option_answers = option_answers;
        this.choice_filter = choice_filter;
        this.is_mandatory = is_mandatory;
        this.max_length = max_length;
        this.is_visible = is_visible;
        this.is_readonly = is_readonly;
        this.regex = regex;
        this.relevant_question = relevant_question;
        this.calculate = calculate;
        this.constraint_message = constraint_message;
        this.usr_crt = usr_crt;
        this.dtm_crt = dtm_crt;
        this.usr_upd = usr_upd;
        this.dtm_upd = dtm_upd;
        this.identifier_name = identifier_name;
        this.uuid_scheme = uuid_scheme;
        this.lov_group = lov_group;
        this.tag = tag;
        this.is_holiday_allowed = is_holiday_allowed;
        this.img_quality = img_quality;
        this.question_validation = question_validation;
        this.question_value = question_value;
        this.validate_err_message = validate_err_message;
        this.form_version = form_version;
        this.relevant_mandatory = relevant_mandatory;
    }

    /** called by internal mechanisms, do not call yourself. */
    public void __setDaoSession(DaoSession daoSession) {
        this.daoSession = daoSession;
        myDao = daoSession != null ? daoSession.getQuestionSetDao() : null;
    }

    /** Not-null value. */
    public String getUuid_question_set() {
        return uuid_question_set;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_question_set(String uuid_question_set) {
        this.uuid_question_set = uuid_question_set;
    }

    public String getQuestion_group_id() {
        return question_group_id;
    }

    public void setQuestion_group_id(String question_group_id) {
        this.question_group_id = question_group_id;
    }

    public String getQuestion_group_name() {
        return question_group_name;
    }

    public void setQuestion_group_name(String question_group_name) {
        this.question_group_name = question_group_name;
    }

    public Integer getQuestion_group_order() {
        return question_group_order;
    }

    public void setQuestion_group_order(Integer question_group_order) {
        this.question_group_order = question_group_order;
    }

    public String getQuestion_id() {
        return question_id;
    }

    public void setQuestion_id(String question_id) {
        this.question_id = question_id;
    }

    public String getQuestion_label() {
        return question_label;
    }

    public void setQuestion_label(String question_label) {
        this.question_label = question_label;
    }

    public Integer getQuestion_order() {
        return question_order;
    }

    public void setQuestion_order(Integer question_order) {
        this.question_order = question_order;
    }

    public String getAnswer_type() {
        return answer_type;
    }

    public void setAnswer_type(String answer_type) {
        this.answer_type = answer_type;
    }

    public String getOption_answers() {
        return option_answers;
    }

    public void setOption_answers(String option_answers) {
        this.option_answers = option_answers;
    }

    public String getChoice_filter() {
        return choice_filter;
    }

    public void setChoice_filter(String choice_filter) {
        this.choice_filter = choice_filter;
    }

    public String getIs_mandatory() {
        return is_mandatory;
    }

    public void setIs_mandatory(String is_mandatory) {
        this.is_mandatory = is_mandatory;
    }

    public Integer getMax_length() {
        return max_length;
    }

    public void setMax_length(Integer max_length) {
        this.max_length = max_length;
    }

    public String getIs_visible() {
        return is_visible;
    }

    public void setIs_visible(String is_visible) {
        this.is_visible = is_visible;
    }

    public String getIs_readonly() {
        return is_readonly;
    }

    public void setIs_readonly(String is_readonly) {
        this.is_readonly = is_readonly;
    }

    public String getRegex() {
        return regex;
    }

    public void setRegex(String regex) {
        this.regex = regex;
    }

    public String getRelevant_question() {
        return relevant_question;
    }

    public void setRelevant_question(String relevant_question) {
        this.relevant_question = relevant_question;
    }

    public String getCalculate() {
        return calculate;
    }

    public void setCalculate(String calculate) {
        this.calculate = calculate;
    }

    public String getConstraint_message() {
        return constraint_message;
    }

    public void setConstraint_message(String constraint_message) {
        this.constraint_message = constraint_message;
    }

    public String getUsr_crt() {
        return usr_crt;
    }

    public void setUsr_crt(String usr_crt) {
        this.usr_crt = usr_crt;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public String getUsr_upd() {
        return usr_upd;
    }

    public void setUsr_upd(String usr_upd) {
        this.usr_upd = usr_upd;
    }

    public java.util.Date getDtm_upd() {
        return dtm_upd;
    }

    public void setDtm_upd(java.util.Date dtm_upd) {
        this.dtm_upd = dtm_upd;
    }

    public String getIdentifier_name() {
        return identifier_name;
    }

    public void setIdentifier_name(String identifier_name) {
        this.identifier_name = identifier_name;
    }

    public String getUuid_scheme() {
        return uuid_scheme;
    }

    public void setUuid_scheme(String uuid_scheme) {
        this.uuid_scheme = uuid_scheme;
    }

    public String getLov_group() {
        return lov_group;
    }

    public void setLov_group(String lov_group) {
        this.lov_group = lov_group;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getIs_holiday_allowed() {
        return is_holiday_allowed;
    }

    public void setIs_holiday_allowed(String is_holiday_allowed) {
        this.is_holiday_allowed = is_holiday_allowed;
    }

    public String getImg_quality() {
        return img_quality;
    }

    public void setImg_quality(String img_quality) {
        this.img_quality = img_quality;
    }

    public String getQuestion_validation() {
        return question_validation;
    }

    public void setQuestion_validation(String question_validation) {
        this.question_validation = question_validation;
    }

    public String getQuestion_value() {
        return question_value;
    }

    public void setQuestion_value(String question_value) {
        this.question_value = question_value;
    }

    public String getValidate_err_message() {
        return validate_err_message;
    }

    public void setValidate_err_message(String validate_err_message) {
        this.validate_err_message = validate_err_message;
    }

    public String getForm_version() {
        return form_version;
    }

    public void setForm_version(String form_version) {
        this.form_version = form_version;
    }

    public String getRelevant_mandatory() {
        return relevant_mandatory;
    }

    public void setRelevant_mandatory(String relevant_mandatory) {
        this.relevant_mandatory = relevant_mandatory;
    }

    /** To-one relationship, resolved on first access. */
    public Scheme getScheme() {
        String __key = this.uuid_scheme;
        if (scheme__resolvedKey == null || scheme__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            SchemeDao targetDao = daoSession.getSchemeDao();
            Scheme schemeNew = targetDao.load(__key);
            synchronized (this) {
                scheme = schemeNew;
            	scheme__resolvedKey = __key;
            }
        }
        return scheme;
    }

    public void setScheme(Scheme scheme) {
        synchronized (this) {
            this.scheme = scheme;
            uuid_scheme = scheme == null ? null : scheme.getUuid_scheme();
            scheme__resolvedKey = uuid_scheme;
        }
    }

    /** Convenient call for {@link AbstractDao#delete(Object)}. Entity must attached to an entity context. */
    public void delete() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.delete(this);
    }

    /** Convenient call for {@link AbstractDao#update(Object)}. Entity must attached to an entity context. */
    public void update() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.update(this);
    }

    /** Convenient call for {@link AbstractDao#refresh(Object)}. Entity must attached to an entity context. */
    public void refresh() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.refresh(this);
    }

}
