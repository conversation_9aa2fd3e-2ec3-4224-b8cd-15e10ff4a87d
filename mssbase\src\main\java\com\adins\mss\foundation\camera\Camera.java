package com.adins.mss.foundation.camera;

import android.app.Activity;
import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.Matrix;
import android.hardware.Camera.AutoFocusCallback;
import android.hardware.Camera.Parameters;
import android.hardware.Camera.PictureCallback;
import android.hardware.Camera.ShutterCallback;
import android.media.ExifInterface;
import android.os.Environment;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.LocationInfo;
import com.adins.mss.foundation.camerainapp.helper.Logger;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.image.ImageManipulation;
import com.adins.mss.foundation.location.LocationTrackingManager;

import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
public class Camera {
    private static final String SUBMIT_DELIMITER = ",";
    public static String BUND_KEY_IMAGE_BYTE = "BUND_KEY_IMAGE_BYTE";
    private static Context context;
    ExifInterface exif;
    /**
     *
     */
    ShutterCallback shutterCallback = new ShutterCallback() {

        @Override
        public void onShutter() {
            // TODO Auto-generated method stub

        }

    };
//	private QuestionBean questionInFocus;
    /**
     *
     */
    PictureCallback rawCallback = new PictureCallback() {

        @Override
        public void onPictureTaken(byte[] data, android.hardware.Camera camera) {
            // TODO Auto-generated method stub

        }

    };
//	private ListAdapter listPicSize;
//	private int rotate;
//	private int actualWidth = 240;
//	private int actualHeight = 320;
//	private int jpegQuality = 70;
    /**
     * autofocus camera
     */
    AutoFocusCallback autoFocusTouch = new AutoFocusCallback() {

        public void onAutoFocus(boolean success, android.hardware.Camera camera) {
            if (!success) {
//                String[] msg = {"Autofocus not available"};
//                String alert = Tool.implode(msg, "\n");
                Toast.makeText(getContext(), getActivity().getResources().getString(R.string.error_auto_focus), Toast.LENGTH_SHORT).show();
//				isTouch=1;
            }

//			MediaPlayer mp1 = MediaPlayer.create(getApplicationContext(), R.raw.camera_focus_beep);
//	    	mp1.setAudioStreamType(AudioManager.STREAM_MUSIC);
//	    	mp1.start();
        }
    };
    ImageCallBack imageCallBack;

    //	private ImageBean ib;
    private Activity activity;
    /**
     * Handle return from camera.takepicture
     */
    PictureCallback jpegCallback = new PictureCallback() {

        @Override
        public void onPictureTaken(byte[] data, android.hardware.Camera camera) {
            // TODO parameter value is constant?
            resizeImage(data, 0, 240, 320, 70);

            LocationTrackingManager ltm = Global.LTM;

//			// set exif
//			if (Global.IS_DEV) System.out.println("getCurrentLocation = "+ ltm.getCurrentLocation());
            if (Global.IS_DEV) Logger.i("INFO", "data = " + data);
            try {
                data = getPicWithExif(data, ltm.getCurrentLocation(Global.FLAG_LOCATION_CAMERA));
            } catch (Exception e) {
                FireCrash.log(e);
                data = getPicWithExif(data, null);
            }


            getActivity().finish();
        }
    };
    private android.hardware.Camera camera;
    /**
     * autofocus to capture image
     */
    AutoFocusCallback autoFocusCallback = new AutoFocusCallback() {

        public void onAutoFocus(boolean success, android.hardware.Camera camera) {
            if (!success) {
//                String[] msg = {"Autofocus not avaiable"};
//                String alert = Tool.implode(msg, "\n");
                Toast.makeText(getContext(), getActivity().getResources().getString(R.string.error_auto_focus), Toast.LENGTH_SHORT).show();

            }
            if (Global.IS_DEV) Logger.i("INFO", "autofocus :" + success);
            getCamera().takePicture(shutterCallback, rawCallback, jpegCallback);
        }
    };
    private Parameters params;
    private TextView txtDetailInFocus;

    /** Take a picture
     *  <br>Need an implementation of ImageCallBackInterface and override onPictureTaken method
     *
     */
//	public void capture(){
//		camera.takePicture(shutterCallback, rawCallback, jpegCallback);
//	}

    /**
     * create camera object for getting these functionalities
     *
     * @param context  - application context from activity class
     * @param activity - activity class
     * @param camera   - initialized camera from android.hardware.Camera
     * @param params   - parameter setting from camera
     */
    public Camera(Context context, Activity activity, android.hardware.Camera camera,
                  Parameters params
//			, QuestionBean questionInFocus
    ) {
        Camera.context = context;
        this.camera = camera;
        this.params = params;
//		this.questionInFocus = questionInFocus;
        this.activity = activity;
//		this.isGeoTagged = isGeoTagged;
//		this.isGeoTaggedGPSOnly = isGeoTaggedGPSOnly;
//		this.needTimeStamp = needTimeStamp;

        txtDetailInFocus = new TextView(context);
    }

    /**
     * Check if this device has a camera
     */
    public static boolean checkCameraHardware(Context context) {
        // this device has a camera
// no camera on this device
        return context.getPackageManager().hasSystemFeature(PackageManager.FEATURE_CAMERA);
    }

    /**
     * This method is used to resize image with a custom size by user
     * <br>Suggested to use fit size according to size of device screen
     *
     * @param img
     * @param rotate       - integer as degree
     * @param actualWidth
     * @param actualHeight
     * @param jpegQuality  - integer as perceint
     * @return
     */
    public static byte[] resizeImage(byte[] img, int rotate, int actualWidth, int actualHeight, int jpegQuality) {
        if (Global.IS_DEV) Logger.i("INFO", "image quality : " + jpegQuality);
        Bitmap bm = BitmapFactory.decodeByteArray(img, 0, img.length);
        Bitmap bmp = Bitmap.createScaledBitmap(bm, actualWidth, actualHeight, true);

        //rotate image if potraid
        if (rotate != 0) {
            Matrix mat = new Matrix();
            mat.preRotate(rotate);// /in degree

            // Bitmap
            bmp = Bitmap.createBitmap(bmp, 0, 0, bmp.getWidth(),
                    bmp.getHeight(), mat, true);
        }

        //Bitmap bmp = intent.getExtras().get("data");
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        bmp.compress(Bitmap.CompressFormat.JPEG, jpegQuality, stream);

        return stream.toByteArray();
    }

    public static byte[] resizeImageWithWatermark(byte[] img, int rotate, int actualWidth, int actualHeight, int jpegQuality, Activity activity) {
        if (Global.IS_DEV) Logger.i("INFO", "image quality : " + jpegQuality);
        Bitmap bm = BitmapFactory.decodeByteArray(img, 0, img.length);
        Bitmap bmp = Bitmap.createScaledBitmap(bm, actualWidth, actualHeight, true);

        //rotate image if potraid
        if (rotate != 0) {
            Matrix mat = new Matrix();
            mat.preRotate(rotate);// /in degree

            // Bitmap
            bmp = Bitmap.createBitmap(bmp, 0, 0, bmp.getWidth(),
                    bmp.getHeight(), mat, true);
        }
        //untuk nambah watermark
        Bitmap bmpFinal = ImageManipulation.waterMark(bmp, activity.getString(R.string.watermark), Color.WHITE, 80, 32, false);
        //Bitmap bmp = intent.getExtras().get("data");
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        bmpFinal.compress(Bitmap.CompressFormat.JPEG, jpegQuality, stream);

        try {
            bm.recycle();
            bmp.recycle();
            bmpFinal.recycle();
        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: handle exception
        }
        return stream.toByteArray();
    }

    public static Context getContext() {
        return context;
    }

    public boolean checkSupportedAutoFocus() {
        boolean isSupported = false;
        List<String> focusMode = params.getSupportedFocusModes();
        if (focusMode.contains(Parameters.FOCUS_MODE_AUTO)) {
            isSupported = true;
        }
        return isSupported;
    }

    /**
     * Finish activity class
     */
    private void doBack() {
        activity.finish();
    }

    public void setAutoFocus() {
//		getParams().setFlashMode(Parameters.FLASH_MODE_AUTO);
        if (checkSupportedAutoFocus())
            this.params.setFocusMode(Parameters.FOCUS_MODE_AUTO);
    }
//	private void showOptions() {
//		this.loadSupportedPicSize();
//		AlertDialog.Builder builder = new AlertDialog.Builder(ib.getContext());
//		builder.setTitle(getString(R.string.picSize));
//		builder.setSingleChoiceItems(listPicSize, -1, new DialogInterface.OnClickListener() {
//		    public void onClick(DialogInterface dialog, int item) {
//		    	String resolution = (String) listPicSize.getItem(item);
//		    	String[] sz = resolution.split("x");
//		    	int w = Integer.parseInt(sz[0]);
//		    	int h = Integer.parseInt(sz[1]);
//		    	
//		    	mPreview.setPictureSize(w, h);
//		    	mPreview.mCamera.stopPreview();
//		    	Camera.Size previewSize = mPreview.getOptimalPreviewSize(w, h);
//		    	mPreview.setPreviewSize(previewSize.width, previewSize.height);
//		    	mPreview.mCamera.startPreview();
//		    	try {
//		    		dialog.dismiss();
//		    		dialog=null;
//				} catch (Exception e) {
//				}
//		    	Toast.makeText(ib.getContext(), "Picture size: " + resolution, Toast.LENGTH_SHORT).show();
//		    }
//		});
//		builder.create().show();
//	}
//	
//	private void loadSupportedPicSize() {
//		if (ib.getListPicSize() == null) {
//			List<android.hardware.Camera.Size> listCameraSize = ib.getCamera().getParameters().getSupportedPictureSizes();
//			List<String> listSize = new ArrayList<String>();
//			for(android.hardware.Camera.Size size : listCameraSize){
//				listSize.add(size.width+"x"+size.height);
//			}
//			ib.setListPicSize( new ArrayAdapter<String>(this, R.layout.picture_size_list, listSize));
//		}		
//	}

    public void startFaceDetection() {
        // Try starting Face Detection

        // start face detection only *after* preview has started
        if (params.getMaxNumDetectedFaces() > 0) {
            // camera supports face detection, so can start it:
            getCamera().startFaceDetection();
        }
    }

    /**
     * Set parameter camera to turn auto flash
     */
    public void setFlashAuto() {
//		getParams().setFlashMode(Parameters.FLASH_MODE_AUTO);
        this.params.setFlashMode(Parameters.FLASH_MODE_AUTO);
    }

    /**
     * Set parameter camera to turn auto flash and change backgroundResource spinner
     */
    public void setFlashAuto(Spinner spinner, int iconFlash) {
        spinner.setBackgroundResource(iconFlash);
//		getParams().setFlashMode(Parameters.FLASH_MODE_AUTO);
        this.params.setFlashMode(Parameters.FLASH_MODE_AUTO);
    }

    /**
     * Set parameter camera to turn on flash
     */
    public void setFlashOn() {
//		getParams().setFlashMode(Parameters.FLASH_MODE_ON);
        this.params.setFlashMode(Parameters.FLASH_MODE_ON);
    }

    /**
     * Set parameter camera to turn on flash and change backgroundResource spinner
     */
    public void setFlashOn(Spinner spinner, int iconFlash) {
        spinner.setBackgroundResource(iconFlash);
//	    getParams().setFlashMode(Parameters.FLASH_MODE_ON);
        this.params.setFlashMode(Parameters.FLASH_MODE_ON);
    }

    /**
     * Set parameter camera to turn off flash
     */
    public void setFlashOff() {
//		getParams().setFlashMode(Parameters.FLASH_MODE_OFF);
        this.params.setFlashMode(Parameters.FLASH_MODE_OFF);
    }

    /**
     * Set parameter camera to turn off flash and change backgroundResource spinner
     */
    public void setFlashOff(Spinner spinner, int iconFlash) {
        spinner.setBackgroundResource(iconFlash);
//	    getParams().setFlashMode(Parameters.FLASH_MODE_OFF);
        this.params.setFlashMode(Parameters.FLASH_MODE_OFF);
    }

    /**
     * @param locationInfo
     * @return
     */
    public String locationInfoToSubmitString(LocationInfo locationInfo) {
        StringBuilder sb = new StringBuilder();
        sb.append(locationInfo.getLatitude()).append(SUBMIT_DELIMITER)
                .append(locationInfo.getLongitude()).append(SUBMIT_DELIMITER)
                .append(locationInfo.getCid()).append(SUBMIT_DELIMITER)
                .append(locationInfo.getMcc()).append(SUBMIT_DELIMITER)
                .append(locationInfo.getMnc()).append(SUBMIT_DELIMITER)
                //Glen 25 Sept 2014, send accuracy
//			.append(lac);
                .append(locationInfo.getLac()).append(SUBMIT_DELIMITER)
                .append(locationInfo.getAccuracy());
        return sb.toString();
    }

    public TextView getTxtDetail() {
        return txtDetailInFocus;
    }

    public void setTxtDetail(String textContent) {
        txtDetailInFocus.setText(textContent);
    }

    /**
     * This method is used to get image with set exif to the image
     *
     * @param data         - captured image byte
     * @param locationInfo - location listener to get location in order to get longitude and latitude
     * @return byte - image with exif in byte
     */
    private byte[] getPicWithExif(byte[] data, LocationInfo locationInfo) {
        // prepare for creating file into local storage
        File photo = new File(Environment.getExternalStorageDirectory(), "tempImage.jpeg");
        if (photo.exists()) photo.delete();
        try {
            FileOutputStream fos = new FileOutputStream(photo.getPath());
            fos.write(data);
            fos.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

        //set exif

//			double latitude = 123;//AGlanceLocationListener.getLatitude();
//			double longitude = 32453;//AGlanceLocationListener.getLongitude();
        if (locationInfo != null) {
            double latitude = Double.parseDouble(locationInfo.getLatitude());
            double longitude = Double.parseDouble(locationInfo.getLongitude());
            try {
                // Logger.e("try catch","masuk try set exif");
                exif = new ExifInterface(photo.getPath());
                // Logger.e("try catch","flag 1");
                int num1Lat = (int) Math.floor(latitude);
                int num2Lat = (int) Math.floor((latitude - num1Lat) * 60);
                double num3Lat = (latitude - ((double) num1Lat + ((double) num2Lat / 60))) * 3600000;
                int num1Lon = (int) Math.floor(longitude);
                int num2Lon = (int) Math.floor((longitude - num1Lon) * 60);
                double num3Lon = (longitude - ((double) num1Lon + ((double) num2Lon / 60))) * 3600000;
                // Logger.e("try catch","flag 2");
                exif.setAttribute(ExifInterface.TAG_GPS_LATITUDE, num1Lat + "/1," + num2Lat + "/1," + num3Lat + "/1000");
                // Logger.e("try catch","flag 3");
                exif.setAttribute(ExifInterface.TAG_GPS_LONGITUDE, num1Lon + "/1," + num2Lon + "/1," + num3Lon + "/1000");
                if (latitude > 0) {
                    exif.setAttribute(ExifInterface.TAG_GPS_LATITUDE_REF, "N");
                } else {
                    exif.setAttribute(ExifInterface.TAG_GPS_LATITUDE_REF, "S");
                }
                if (longitude > 0) {
                    exif.setAttribute(ExifInterface.TAG_GPS_LONGITUDE_REF, "E");
                } else {
                    exif.setAttribute(ExifInterface.TAG_GPS_LONGITUDE_REF, "W");
                }
                // Logger.e("try catch","flag 4");
                exif.saveAttributes();
                // Logger.e("try catch","exit try set exif");
            } catch (IOException e) {
                FireCrash.log(e);
                Logger.e("PictureActivity", e.getLocalizedMessage());
            }
        }
        // get byte of pictWithExif
        Logger.e("photo length", photo.length() + "");
        byte[] dataPicWithExif = new byte[(int) photo.length()];
        try {
            BufferedInputStream buf = new BufferedInputStream(new FileInputStream(photo));
            buf.read(dataPicWithExif, 0, dataPicWithExif.length);
        } catch (Exception e) {
            e.printStackTrace();
        }

        //delete photo from local
        photo.delete();

        //delegate
        processImage(dataPicWithExif, locationInfo);

        return dataPicWithExif;
    }

    protected void processImage(byte[] dataPicWithExif, LocationInfo locationInfo) {
        // delegate
        this.imageCallBack.onPictureTaken(dataPicWithExif, locationInfo);
    }

//	protected void onActivityResult(int requestCode, int resultCode, Intent data){
//	        super.onActivityResult(requestCode, resultCode, data);
//	                Bitmap bm = (Bitmap) data.getExtras().get("data");
//	                MediaStore.Images.Media.insertImage(ib.getContext().getContentResolver(), bm, null, null);
//	                    ByteArrayOutputStream baos = new ByteArrayOutputStream();  
//	                    bm.compress(Bitmap.CompressFormat.JPEG, 100, baos); //bm is the bitmap object   
//	                    byte[] b = baos.toByteArray();  
//	}

//	String mCurrentPhotoPath;
//	private File createImageFile() throws IOException {
//		
//	    // Create an image file name
//	    String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
//	    String imageFileName = "JPEG_" + timeStamp + "_";
//	    File storageDir = Environment.getExternalStoragePublicDirectory(
//	            Environment.DIRECTORY_PICTURES);
//	    File image = File.createTempFile(
//	        imageFileName,  /* prefix */
//	        ".jpg",         /* suffix */
//	        storageDir      /* directory */
//	    );
//
//	    // Save a file: path for use with ACTION_VIEW intents
//	    mCurrentPhotoPath = "file:" + image.getAbsolutePath();
//	    return image;
//	}

    /**
     * This method is to capture image
     * <br>It calls method to process captured image
     * <br>In this framework, that method will call back interface onPictureTaken to activity
     *
     * @param imageCallBack - interface to be called back and receive image with exif and location
     */
    public void getPicture(ImageCallBack imageCallBack) {
        this.imageCallBack = imageCallBack;
        this.camera.setParameters(this.params);
        this.camera.takePicture(shutterCallback, rawCallback, jpegCallback);

//		Intent intent = new Intent(android.provider.MediaStore.ACTION_IMAGE_CAPTURE);
//		File photoFile=null;
//		try{
//			photoFile = createImageFile();
//		}catch(Exception e){
//		}
//		intent.putExtra(MediaStore.ACTION_IMAGE_CAPTURE, MediaStore.Images.Media.EXTERNAL_CONTENT_URI.getPath());
//		ib.getActivity().startActivityForResult(intent,1);

//		String root = Environment.getExternalStorageDirectory().toString();
//		new File(root+"/photofolder").mkdir();
//		Uri outputFile = Uri.fromFile(photoFile);

    }

    public android.hardware.Camera getCamera() {
        return camera;
    }

    public Parameters getParams() {
        return params;
    }

    public void setParams(Parameters params) {
        this.params = params;
    }

    public Activity getActivity() {
        return activity;
    }


//	public ListAdapter getListPicSize() {
//		return listPicSize;
//	}
//
//	public void setListPicSize(ListAdapter listPicSize) {
//		this.listPicSize = listPicSize;
//	}

}
