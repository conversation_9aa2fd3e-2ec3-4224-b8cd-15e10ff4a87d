package com.adins.mss.foundation.db.dataaccess;

import android.content.Context;

import com.adins.mss.dao.DaoSession;
import com.adins.mss.dao.Migration;
import com.adins.mss.dao.MigrationDao;
import com.adins.mss.foundation.db.DaoOpenHelper;

import java.util.List;

import de.greenrobot.dao.query.QueryBuilder;

public class MigrationDataAccess {

    /**
     * use to generate dao session that you can access modelDao
     *
     * @param context --> context from activity
     * @return
     */
    protected static DaoSession getDaoSession(Context context) {
        return DaoOpenHelper.getDaoSession(context);
    }

    /**
     * Get Migration Dao
     */
    protected static MigrationDao getMigrationDao(Context context) {
        return getDaoSession(context).getMigrationDao();
    }

    /**
     * Add Migration Entity
     */
    public static void add(Context context, Migration migration) {
        getMigrationDao(context).insert(migration);
        getDaoSession(context).clear();
    }

    /**
     * Replace Migration Entity
     */
    public static void replace(Context context, Migration migration) {
        getMigrationDao(context).insertOrReplace(migration);
        getDaoSession(context).clear();
    }

    /**
     * Delete Migration Entity
     */
    public static void delete(Context context, Migration migration) {
        getMigrationDao(context).delete(migration);
        getDaoSession(context).clear();
    }

    /**
     * Get All Migration
     */
    public static List<Migration> all(Context context) {
        QueryBuilder<Migration> qb = getMigrationDao(context).queryBuilder();
        qb.orderDesc(MigrationDao.Properties.Id);
        qb.build();

        if (!qb.list().isEmpty()) return qb.list();
        else return null;
    }

    /**
     * Get Migration by DB ver
     */
    public static Migration find(Context context, String version) {
        QueryBuilder<Migration> qb = getMigrationDao(context).queryBuilder();
        qb.where(MigrationDao.Properties.Version.eq(version));
        qb.orderDesc(MigrationDao.Properties.Id);
        qb.build();

        if (!qb.list().isEmpty()) return qb.list().get(0);
        else return null;
    }

    /**
     * Get Migration by DB ver
     */
    public static Migration last(Context context) {
        QueryBuilder<Migration> qb = getMigrationDao(context).queryBuilder();
        qb.orderDesc(MigrationDao.Properties.Id);
        qb.build();

        if (!qb.list().isEmpty()) return qb.list().get(0);
        else return null;
    }
}
