package com.adins.mss.dao;

import java.util.List;
import com.adins.mss.dao.DaoSession;
import de.greenrobot.dao.DaoException;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "TR_DEPOSITREPORT_H".
 */
public class DepositReportH {

    /** Not-null value. */
     @SerializedName("uuid_deposit_report_h")
    private String uuid_deposit_report_h;
     @SerializedName("last_update")
    private java.util.Date last_update;
     @SerializedName("batch_id")
    private String batch_id;
     @SerializedName("bank_account")
    private String bank_account;
     @SerializedName("bank_name")
    private String bank_name;
     @SerializedName("cashier_name")
    private String cashier_name;
     @SerializedName("transfered_date")
    private java.util.Date transfered_date;
     @SerializedName("usr_crt")
    private String usr_crt;
     @SerializedName("dtm_crt")
    private java.util.Date dtm_crt;
     @SerializedName("image")
    private byte[] image;
     @SerializedName("uuid_user")
    private String uuid_user;

    /** Used to resolve relations */
    private transient DaoSession daoSession;

    /** Used for active entity operations. */
    private transient DepositReportHDao myDao;

    private User user;
    private String user__resolvedKey;

    private List<DepositReportD> depositReportDList;

    public DepositReportH() {
    }

    public DepositReportH(String uuid_deposit_report_h) {
        this.uuid_deposit_report_h = uuid_deposit_report_h;
    }

    public DepositReportH(String uuid_deposit_report_h, java.util.Date last_update, String batch_id, String bank_account, String bank_name, String cashier_name, java.util.Date transfered_date, String usr_crt, java.util.Date dtm_crt, byte[] image, String uuid_user) {
        this.uuid_deposit_report_h = uuid_deposit_report_h;
        this.last_update = last_update;
        this.batch_id = batch_id;
        this.bank_account = bank_account;
        this.bank_name = bank_name;
        this.cashier_name = cashier_name;
        this.transfered_date = transfered_date;
        this.usr_crt = usr_crt;
        this.dtm_crt = dtm_crt;
        this.image = image;
        this.uuid_user = uuid_user;
    }

    /** called by internal mechanisms, do not call yourself. */
    public void __setDaoSession(DaoSession daoSession) {
        this.daoSession = daoSession;
        myDao = daoSession != null ? daoSession.getDepositReportHDao() : null;
    }

    /** Not-null value. */
    public String getUuid_deposit_report_h() {
        return uuid_deposit_report_h;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_deposit_report_h(String uuid_deposit_report_h) {
        this.uuid_deposit_report_h = uuid_deposit_report_h;
    }

    public java.util.Date getLast_update() {
        return last_update;
    }

    public void setLast_update(java.util.Date last_update) {
        this.last_update = last_update;
    }

    public String getBatch_id() {
        return batch_id;
    }

    public void setBatch_id(String batch_id) {
        this.batch_id = batch_id;
    }

    public String getBank_account() {
        return bank_account;
    }

    public void setBank_account(String bank_account) {
        this.bank_account = bank_account;
    }

    public String getBank_name() {
        return bank_name;
    }

    public void setBank_name(String bank_name) {
        this.bank_name = bank_name;
    }

    public String getCashier_name() {
        return cashier_name;
    }

    public void setCashier_name(String cashier_name) {
        this.cashier_name = cashier_name;
    }

    public java.util.Date getTransfered_date() {
        return transfered_date;
    }

    public void setTransfered_date(java.util.Date transfered_date) {
        this.transfered_date = transfered_date;
    }

    public String getUsr_crt() {
        return usr_crt;
    }

    public void setUsr_crt(String usr_crt) {
        this.usr_crt = usr_crt;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public byte[] getImage() {
        return image;
    }

    public void setImage(byte[] image) {
        this.image = image;
    }

    public String getUuid_user() {
        return uuid_user;
    }

    public void setUuid_user(String uuid_user) {
        this.uuid_user = uuid_user;
    }

    /** To-one relationship, resolved on first access. */
    public User getUser() {
        String __key = this.uuid_user;
        if (user__resolvedKey == null || user__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            UserDao targetDao = daoSession.getUserDao();
            User userNew = targetDao.load(__key);
            synchronized (this) {
                user = userNew;
            	user__resolvedKey = __key;
            }
        }
        return user;
    }

    public void setUser(User user) {
        synchronized (this) {
            this.user = user;
            uuid_user = user == null ? null : user.getUuid_user();
            user__resolvedKey = uuid_user;
        }
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<DepositReportD> getDepositReportDList() {
        if (depositReportDList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            DepositReportDDao targetDao = daoSession.getDepositReportDDao();
            List<DepositReportD> depositReportDListNew = targetDao._queryDepositReportH_DepositReportDList(uuid_deposit_report_h);
            synchronized (this) {
                if(depositReportDList == null) {
                    depositReportDList = depositReportDListNew;
                }
            }
        }
        return depositReportDList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetDepositReportDList() {
        depositReportDList = null;
    }

    /** Convenient call for {@link AbstractDao#delete(Object)}. Entity must attached to an entity context. */
    public void delete() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.delete(this);
    }

    /** Convenient call for {@link AbstractDao#update(Object)}. Entity must attached to an entity context. */
    public void update() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.update(this);
    }

    /** Convenient call for {@link AbstractDao#refresh(Object)}. Entity must attached to an entity context. */
    public void refresh() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.refresh(this);
    }

}
