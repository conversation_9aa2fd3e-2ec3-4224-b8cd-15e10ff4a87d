package com.adins.mss.base.util;


import com.adins.mss.base.commons.Callback;
import com.adins.mss.foundation.camerainapp.helper.Logger;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;

/**
 * Created by shaladin on 9/2/17.
 */

public class ArchiveManager {

    private String ext = ".zip";

    // Shared Instance
    public static ArchiveManager getInstance() {
        return new ArchiveManager();
    }

    /**
     * Method for archiving file
     * @param _files        ArrayList source file to be archived
     * @param archiveName   Destination File without extension (path + filename)
     */
    public void archive(ArrayList<String> _files, String archiveName) {
        try {
            BufferedInputStream origin = null;
            FileOutputStream dest   = new FileOutputStream(new File(archiveName + ext));
            ZipOutputStream out     = new ZipOutputStream(new BufferedOutputStream(dest));
            byte[] data = new byte[8192];

            for (int i = 0; i < _files.size(); i++) {
                Logger.i("INFO", "Adding: " + _files.get(i));
                FileInputStream fin = new FileInputStream(_files.get(i));
                origin = new BufferedInputStream(fin, 8192);
                ZipEntry zen = new ZipEntry(new File(_files.get(i)).getName());
                out.putNextEntry(zen);
                int count;

                while ((count = origin.read(data, 0, 8192)) != -1) {
                    out.write(data, 0, count);
                }
                //close buffered input stream
                origin.close();
            }
            //close zip input stream
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void archive(File source, String archiveName, Callback callback) {
        File destinationFile    = new File(archiveName + ext);
        try {
            BufferedInputStream origin = null;
            FileOutputStream dest   = new FileOutputStream(destinationFile);
            ZipOutputStream out     = new ZipOutputStream(new BufferedOutputStream(dest));
            byte[] data = new byte[8192];

            Logger.i("INFO", "Adding: " + source.getName());
            FileInputStream fin = new FileInputStream(source);
            origin = new BufferedInputStream(fin, 8192);
            ZipEntry zen = new ZipEntry(source.getName());
            out.putNextEntry(zen);
            int count;

            while ((count = origin.read(data, 0, 8192)) != -1) {
                out.write(data, 0, count);
            }

            //close buffered input stream
            origin.close();

            //close zip input stream
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            Logger.i("INFO", "Archive Completed!");
            callback.onComplete(destinationFile);
        }
    }

    /**
     * Method for extracting archive
     */
//    public void extract(String _file, String _targetLocation) {
//        //check target directory if doesn't exist
//        dirChecker(_targetLocation);
//
//        try {
//            FileInputStream fin = new FileInputStream(_file);
//            ZipInputStream  zin = new ZipInputStream(fin);
//            ZipEntry        ze  = null;
//
//            while ((ze = zin.getNextEntry()) != null) {
//                //create dir if required while extracting
//                if(ze.isDirectory()) {
//                    dirChecker(ze.getName());
//                } else {
//                    FileOutputStream fout = new FileOutputStream(_targetLocation + File.separator + ze.getName());
//                    for (int c = zin.read(); c != -1; c = zin.read()) {
//                        fout.write(c);
//                    }
//
//                    zin.closeEntry();
//                    fout.close();
//                }
//            }
//            zin.close();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    /**
     * Method for extracting archive
     * @param archiveFile       source archive file to be extracted
     * @param targetLocation    target location extracted file
     */
    public void extract(String archiveFile, String targetLocation) {
        try {
            int BUFFER = 2048;
            List<String> zipFiles  = new ArrayList<>();
            File sourceArchiveFile = new File(archiveFile);
            File destinationPath   = new File(targetLocation);

            ZipFile zipFile;
            zipFile = new ZipFile(sourceArchiveFile, ZipFile.OPEN_READ);
            Enumeration zipFileEntries = zipFile.entries();

            while (zipFileEntries.hasMoreElements()) {
                ZipEntry entry = (ZipEntry) zipFileEntries.nextElement();
                String currentEntry = entry.getName();

                File destFile = new File(destinationPath, currentEntry);
                if(currentEntry.endsWith(".zip")) {
                    zipFiles.add(destFile.getAbsolutePath());
                }

                File destinationParent = destFile.getParentFile();
                destinationParent.mkdirs();

                try {
                    if(!entry.isDirectory()) {
                        BufferedInputStream is = new BufferedInputStream(zipFile.getInputStream(entry));
                        int currentByte;
                        byte[] data = new byte[BUFFER];

                        FileOutputStream fos = new FileOutputStream(destFile);
                        BufferedOutputStream dest = new BufferedOutputStream(fos, BUFFER);
                        while ((currentByte = is.read(data, 0, BUFFER)) != -1) {
                            dest.write(data, 0, currentByte);
                        }

                        dest.flush();
                        dest.close();
                        is.close();
                    }
                } catch (IOException io) {
                    io.printStackTrace();
                }
            }

            zipFile.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private boolean dirChecker(String _pathLocation) {
        boolean ret = true;
        File dir    = new File(_pathLocation);
        if(!dir.exists()) dir.mkdir();
        return ret;
    }
}
