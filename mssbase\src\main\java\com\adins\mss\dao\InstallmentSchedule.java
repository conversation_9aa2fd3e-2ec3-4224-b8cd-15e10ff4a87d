package com.adins.mss.dao;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "TR_INSTALLMENTSCHEDULE".
 */
public class InstallmentSchedule {

    /** Not-null value. */
     @SerializedName("uuid_installment_schedule")
    private String uuid_installment_schedule;
     @SerializedName("uuid_task_h")
    private String uuid_task_h;
     @SerializedName("agreement_no")
    private String agreement_no;
     @SerializedName("branch_code")
    private String branch_code;
     @SerializedName("installment_no")
    private String installment_no;
     @SerializedName("installment_amount")
    private String installment_amount;
     @SerializedName("instl_paid_amount")
    private String instl_paid_amount;
     @SerializedName("lc_instl_amount")
    private String lc_instl_amount;
     @SerializedName("lc_instl_paid")
    private String lc_instl_paid;
     @SerializedName("lc_instl_waived")
    private String lc_instl_waived;
     @SerializedName("principal_amount")
    private String principal_amount;
     @SerializedName("interest_amount")
    private String interest_amount;
     @SerializedName("os_principal_amount")
    private String os_principal_amount;
     @SerializedName("os_interest_amount")
    private String os_interest_amount;
     @SerializedName("lc_days")
    private String lc_days;
     @SerializedName("lc_admin_fee")
    private String lc_admin_fee;
     @SerializedName("lc_admin_fee_paid")
    private String lc_admin_fee_paid;
     @SerializedName("lc_admin_fee_waive")
    private String lc_admin_fee_waive;
     @SerializedName("usr_crt")
    private String usr_crt;
     @SerializedName("dtm_crt")
    private java.util.Date dtm_crt;
     @SerializedName("due_date")
    private java.util.Date due_date;
     @SerializedName("instl_paid_date")
    private java.util.Date instl_paid_date;

    public InstallmentSchedule() {
    }

    public InstallmentSchedule(String uuid_installment_schedule) {
        this.uuid_installment_schedule = uuid_installment_schedule;
    }

    public InstallmentSchedule(String uuid_installment_schedule, String uuid_task_h, String agreement_no, String branch_code, String installment_no, String installment_amount, String instl_paid_amount, String lc_instl_amount, String lc_instl_paid, String lc_instl_waived, String principal_amount, String interest_amount, String os_principal_amount, String os_interest_amount, String lc_days, String lc_admin_fee, String lc_admin_fee_paid, String lc_admin_fee_waive, String usr_crt, java.util.Date dtm_crt, java.util.Date due_date, java.util.Date instl_paid_date) {
        this.uuid_installment_schedule = uuid_installment_schedule;
        this.uuid_task_h = uuid_task_h;
        this.agreement_no = agreement_no;
        this.branch_code = branch_code;
        this.installment_no = installment_no;
        this.installment_amount = installment_amount;
        this.instl_paid_amount = instl_paid_amount;
        this.lc_instl_amount = lc_instl_amount;
        this.lc_instl_paid = lc_instl_paid;
        this.lc_instl_waived = lc_instl_waived;
        this.principal_amount = principal_amount;
        this.interest_amount = interest_amount;
        this.os_principal_amount = os_principal_amount;
        this.os_interest_amount = os_interest_amount;
        this.lc_days = lc_days;
        this.lc_admin_fee = lc_admin_fee;
        this.lc_admin_fee_paid = lc_admin_fee_paid;
        this.lc_admin_fee_waive = lc_admin_fee_waive;
        this.usr_crt = usr_crt;
        this.dtm_crt = dtm_crt;
        this.due_date = due_date;
        this.instl_paid_date = instl_paid_date;
    }

    /** Not-null value. */
    public String getUuid_installment_schedule() {
        return uuid_installment_schedule;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_installment_schedule(String uuid_installment_schedule) {
        this.uuid_installment_schedule = uuid_installment_schedule;
    }

    public String getUuid_task_h() {
        return uuid_task_h;
    }

    public void setUuid_task_h(String uuid_task_h) {
        this.uuid_task_h = uuid_task_h;
    }

    public String getAgreement_no() {
        return agreement_no;
    }

    public void setAgreement_no(String agreement_no) {
        this.agreement_no = agreement_no;
    }

    public String getBranch_code() {
        return branch_code;
    }

    public void setBranch_code(String branch_code) {
        this.branch_code = branch_code;
    }

    public String getInstallment_no() {
        return installment_no;
    }

    public void setInstallment_no(String installment_no) {
        this.installment_no = installment_no;
    }

    public String getInstallment_amount() {
        return installment_amount;
    }

    public void setInstallment_amount(String installment_amount) {
        this.installment_amount = installment_amount;
    }

    public String getInstl_paid_amount() {
        return instl_paid_amount;
    }

    public void setInstl_paid_amount(String instl_paid_amount) {
        this.instl_paid_amount = instl_paid_amount;
    }

    public String getLc_instl_amount() {
        return lc_instl_amount;
    }

    public void setLc_instl_amount(String lc_instl_amount) {
        this.lc_instl_amount = lc_instl_amount;
    }

    public String getLc_instl_paid() {
        return lc_instl_paid;
    }

    public void setLc_instl_paid(String lc_instl_paid) {
        this.lc_instl_paid = lc_instl_paid;
    }

    public String getLc_instl_waived() {
        return lc_instl_waived;
    }

    public void setLc_instl_waived(String lc_instl_waived) {
        this.lc_instl_waived = lc_instl_waived;
    }

    public String getPrincipal_amount() {
        return principal_amount;
    }

    public void setPrincipal_amount(String principal_amount) {
        this.principal_amount = principal_amount;
    }

    public String getInterest_amount() {
        return interest_amount;
    }

    public void setInterest_amount(String interest_amount) {
        this.interest_amount = interest_amount;
    }

    public String getOs_principal_amount() {
        return os_principal_amount;
    }

    public void setOs_principal_amount(String os_principal_amount) {
        this.os_principal_amount = os_principal_amount;
    }

    public String getOs_interest_amount() {
        return os_interest_amount;
    }

    public void setOs_interest_amount(String os_interest_amount) {
        this.os_interest_amount = os_interest_amount;
    }

    public String getLc_days() {
        return lc_days;
    }

    public void setLc_days(String lc_days) {
        this.lc_days = lc_days;
    }

    public String getLc_admin_fee() {
        return lc_admin_fee;
    }

    public void setLc_admin_fee(String lc_admin_fee) {
        this.lc_admin_fee = lc_admin_fee;
    }

    public String getLc_admin_fee_paid() {
        return lc_admin_fee_paid;
    }

    public void setLc_admin_fee_paid(String lc_admin_fee_paid) {
        this.lc_admin_fee_paid = lc_admin_fee_paid;
    }

    public String getLc_admin_fee_waive() {
        return lc_admin_fee_waive;
    }

    public void setLc_admin_fee_waive(String lc_admin_fee_waive) {
        this.lc_admin_fee_waive = lc_admin_fee_waive;
    }

    public String getUsr_crt() {
        return usr_crt;
    }

    public void setUsr_crt(String usr_crt) {
        this.usr_crt = usr_crt;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public java.util.Date getDue_date() {
        return due_date;
    }

    public void setDue_date(java.util.Date due_date) {
        this.due_date = due_date;
    }

    public java.util.Date getInstl_paid_date() {
        return instl_paid_date;
    }

    public void setInstl_paid_date(java.util.Date instl_paid_date) {
        this.instl_paid_date = instl_paid_date;
    }

}
