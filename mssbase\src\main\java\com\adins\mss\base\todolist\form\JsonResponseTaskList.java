package com.adins.mss.base.todolist.form;

import com.adins.mss.dao.TaskH;
import com.adins.mss.foundation.http.MssResponseType;
import com.google.gson.annotations.SerializedName;

import java.util.Date;
import java.util.List;

public class JsonResponseTaskList extends MssResponseType {
    @SerializedName("listTaskList")
    List<TaskH> listTaskList;
    @SerializedName("listTaskReminderPo")
    List<TaskReminderPo> listTaskReminderPo;

    public List<TaskH> getListTaskList() {
        return listTaskList;
    }

    public void setListTaskList(List<TaskH> listTaskList) {
        this.listTaskList = listTaskList;
    }

    public List<TaskReminderPo> getListTaskReminderPo() {
        return listTaskReminderPo;
    }

    public void setListTaskReminderPo(List<TaskReminderPo> listTaskReminderPo) {
        this.listTaskReminderPo = listTaskReminderPo;
    }

    public static class TaskReminderPo {
        @SerializedName("uuid_task_h") String uuidTaskH;
        @SerializedName("expired_date") Date expireDate;
        @SerializedName("status_po") String statusPo;

        public String getUuidTaskH() {
            return uuidTaskH;
        }

        public void setUuidTaskH(String uuidTaskH) {
            this.uuidTaskH = uuidTaskH;
        }

        public Date getExpireDate() {
            return expireDate;
        }

        public void setExpireDate(Date expireDate) {
            this.expireDate = expireDate;
        }

        public String getStatusPo() {
            return statusPo;
        }

        public void setStatusPo(String statusPo) {
            this.statusPo = statusPo;
        }
    }

}
