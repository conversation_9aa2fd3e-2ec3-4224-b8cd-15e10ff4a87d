package com.adins.mss.base.biometric;

import com.adins.mss.foundation.http.MssRequestType;
import com.google.gson.annotations.SerializedName;

import java.util.Map;

public class JsonCheckBiometricRequest extends MssRequestType {
    @SerializedName("image") private String image;
    @SerializedName("filter") private Map<String, String> filter;
    @SerializedName("formName") private String formName;
    @SerializedName("uuidTaskH") private String uuidTaskH;

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public Map<String, String> getFilter() {
        return filter;
    }

    public void setFilter(Map<String, String> filter) {
        this.filter = filter;
    }

    public String getFormName() {
        return formName;
    }

    public void setFormName(String formName) {
        this.formName = formName;
    }

    public String getUuidTaskH() {
        return uuidTaskH;
    }

    public void setUuidTaskH(String uuidTaskH) {
        this.uuidTaskH = uuidTaskH;
    }
}

