package com.adins.mss.dao;

import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;

import com.adins.mss.dao.TimelineType;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "MS_TIMELINETYPE".
*/
public class TimelineTypeDao extends AbstractDao<TimelineType, String> {

    public static final String TABLENAME = "MS_TIMELINETYPE";

    /**
     * Properties of entity TimelineType.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_timeline_type = new Property(0, String.class, "uuid_timeline_type", true, "UUID_TIMELINE_TYPE");
        public final static Property Timeline_description = new Property(1, String.class, "timeline_description", false, "TIMELINE_DESCRIPTION");
        public final static Property Timeline_type = new Property(2, String.class, "timeline_type", false, "TIMELINE_TYPE");
        public final static Property Usr_crt = new Property(3, String.class, "usr_crt", false, "USR_CRT");
        public final static Property Dtm_crt = new Property(4, java.util.Date.class, "dtm_crt", false, "DTM_CRT");
        public final static Property Usr_upd = new Property(5, String.class, "usr_upd", false, "USR_UPD");
        public final static Property Dtm_upd = new Property(6, java.util.Date.class, "dtm_upd", false, "DTM_UPD");
    };

    private DaoSession daoSession;


    public TimelineTypeDao(DaoConfig config) {
        super(config);
    }
    
    public TimelineTypeDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"MS_TIMELINETYPE\" (" + //
                "\"UUID_TIMELINE_TYPE\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_timeline_type
                "\"TIMELINE_DESCRIPTION\" TEXT," + // 1: timeline_description
                "\"TIMELINE_TYPE\" TEXT," + // 2: timeline_type
                "\"USR_CRT\" TEXT," + // 3: usr_crt
                "\"DTM_CRT\" INTEGER," + // 4: dtm_crt
                "\"USR_UPD\" TEXT," + // 5: usr_upd
                "\"DTM_UPD\" INTEGER);"); // 6: dtm_upd
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"MS_TIMELINETYPE\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, TimelineType entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_timeline_type());
 
        String timeline_description = entity.getTimeline_description();
        if (timeline_description != null) {
            stmt.bindString(2, timeline_description);
        }
 
        String timeline_type = entity.getTimeline_type();
        if (timeline_type != null) {
            stmt.bindString(3, timeline_type);
        }
 
        String usr_crt = entity.getUsr_crt();
        if (usr_crt != null) {
            stmt.bindString(4, usr_crt);
        }
 
        java.util.Date dtm_crt = entity.getDtm_crt();
        if (dtm_crt != null) {
            stmt.bindLong(5, dtm_crt.getTime());
        }
 
        String usr_upd = entity.getUsr_upd();
        if (usr_upd != null) {
            stmt.bindString(6, usr_upd);
        }
 
        java.util.Date dtm_upd = entity.getDtm_upd();
        if (dtm_upd != null) {
            stmt.bindLong(7, dtm_upd.getTime());
        }
    }

    @Override
    protected void attachEntity(TimelineType entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public TimelineType readEntity(Cursor cursor, int offset) {
        TimelineType entity = new TimelineType( //
            cursor.getString(offset + 0), // uuid_timeline_type
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // timeline_description
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // timeline_type
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // usr_crt
            cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)), // dtm_crt
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // usr_upd
            cursor.isNull(offset + 6) ? null : new java.util.Date(cursor.getLong(offset + 6)) // dtm_upd
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, TimelineType entity, int offset) {
        entity.setUuid_timeline_type(cursor.getString(offset + 0));
        entity.setTimeline_description(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setTimeline_type(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setUsr_crt(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setDtm_crt(cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)));
        entity.setUsr_upd(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setDtm_upd(cursor.isNull(offset + 6) ? null : new java.util.Date(cursor.getLong(offset + 6)));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(TimelineType entity, long rowId) {
        return entity.getUuid_timeline_type();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(TimelineType entity) {
        if(entity != null) {
            return entity.getUuid_timeline_type();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
}
