package com.adins.mss.base.dynamicform.form.questions.viewholder;

import android.app.Activity;
import android.content.Intent;
import android.os.Build;
import android.text.InputType;
import android.text.Layout;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.recyclerview.widget.RecyclerView;

import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.dynamicform.form.questions.OnQuestionClickListener;
import com.adins.mss.base.dynamicform.form.questions.QuestionViewAdapter;
import com.adins.mss.base.timeline.MapsViewer;
import com.adins.mss.constant.Global;
import com.adins.mss.foundation.formatter.Formatter;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.questiongenerator.OptionAnswerBean;
import com.adins.mss.foundation.questiongenerator.QuestionBean;

import java.util.Date;
import java.util.List;

/**
 * Created by gigin.ginanjar on 06/09/2016.
 */
public class ReviewTextViewHolder extends RecyclerView.ViewHolder {
    public RelativeLayout layout;
    public TextView mLabelNo;
    public TextView mQuestionLabel;
    public TextView mQuestionAnswer;
    public ImageView mCheckLocation;
    public QuestionBean bean;
    public OnQuestionClickListener listener;
    private Activity mActivity;

    @Deprecated
    public ReviewTextViewHolder(View itemView) {
        super(itemView);
        layout = (RelativeLayout) itemView.findViewById(R.id.textReviewLayout);
        mLabelNo = (TextView) itemView.findViewById(R.id.questionNoLabel);
        mQuestionLabel = (TextView) itemView.findViewById(R.id.questionTextLabel);
        mQuestionAnswer = (TextView) itemView.findViewById(R.id.questionTextAnswer);
        mCheckLocation = (ImageView) itemView.findViewById(R.id.imgLocationAnswer);
    }

    public ReviewTextViewHolder(View itemView, OnQuestionClickListener listener) {
        super(itemView);
        layout = (RelativeLayout) itemView.findViewById(R.id.textReviewLayout);
        mLabelNo = (TextView) itemView.findViewById(R.id.questionNoLabel);
        mQuestionLabel = (TextView) itemView.findViewById(R.id.questionTextLabel);
        mQuestionAnswer = (TextView) itemView.findViewById(R.id.questionTextAnswer);
        mCheckLocation = (ImageView) itemView.findViewById(R.id.imgLocationAnswer);
        this.listener = listener;
    }

    public ReviewTextViewHolder(Activity activity, View itemView, OnQuestionClickListener listener) {
        super(itemView);
        mActivity = activity;
        layout = (RelativeLayout) itemView.findViewById(R.id.textReviewLayout);
        mLabelNo = (TextView) itemView.findViewById(R.id.questionNoLabel);
        mQuestionLabel = (TextView) itemView.findViewById(R.id.questionTextLabel);
        mQuestionAnswer = (TextView) itemView.findViewById(R.id.questionTextAnswer);
        mCheckLocation = (ImageView) itemView.findViewById(R.id.imgLocationAnswer);
        this.listener = listener;
    }

    public void bind(final QuestionBean item, final int group, final int number) {
        bean = item;
        mLabelNo.setText(number + ".");
        String qLabel = bean.getQuestion_label();
        mQuestionLabel.setText(qLabel);

        String answerType = bean.getAnswer_type();
        String qAnswer = getFinalAnswer(answerType);
        if (qAnswer != null && !qAnswer.isEmpty()) {
            if(Global.TAG_URL_LINK.equalsIgnoreCase(bean.getTag())){
                qAnswer = qAnswer.split(Global.DELIMETER_DATA4)[0];
            }

            if (answerType.equals(Global.AT_CURRENCY)) {
                showCurrencyView(qAnswer);
            }
            else {
                qAnswer = qAnswer.replaceAll("<br/>", "\n");
                mQuestionAnswer.setText(qAnswer);
                if (answerType.equals(Global.AT_TEXT_MULTILINE)) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        mQuestionAnswer.setJustificationMode(Layout.JUSTIFICATION_MODE_INTER_WORD);
                    }
                }
            }
        } else {
            mQuestionAnswer.setText("-You have no answer-");
        }
        layout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (listener != null)
                    listener.onReviewClickListener(bean, group, number - 1);
            }
        });
        if (QuestionViewAdapter.IsLocationQuestion(Integer.valueOf(answerType)) && mActivity != null) {
            mCheckLocation.setVisibility(View.VISIBLE);
            mCheckLocation.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (bean.getAnswer() != null && bean.getAnswer().length() > 0) {
                        try {
                            String lat = bean.getLocationInfo().getLatitude();
                            String lng = bean.getLocationInfo().getLongitude();
                            int acc = bean.getLocationInfo().getAccuracy();
                            Intent intent = new Intent(mActivity, MapsViewer.class);
                            intent.putExtra("latitude", lat);
                            intent.putExtra("longitude", lng);
                            intent.putExtra("accuracy", acc);
                            intent.putExtra("isViewOnly", true);
                            intent.putExtra("isAddress", bean.getAnswer_type().equals(Global.AT_LOCATION_WITH_ADDRESS));
                            mActivity.startActivity(intent);
                        } catch (Exception e) {
                            FireCrash.log(e);
                            String lat = bean.getLatitude();
                            String lng = bean.getLongitude();
                            Intent intent = new Intent(mActivity, MapsViewer.class);
                            intent.putExtra("latitude", lat);
                            intent.putExtra("longitude", lng);
                            intent.putExtra("isViewOnly", true);
                            intent.putExtra("isAddress", bean.getAnswer_type().equals(Global.AT_LOCATION_WITH_ADDRESS));
                            mActivity.startActivity(intent);
                        }
                    } else {
                        Toast.makeText(mActivity, "Set Location First!",
                                Toast.LENGTH_LONG).show();
                    }
                }
            });
        } else {
            mCheckLocation.setVisibility(View.GONE);
        }
    }

    private String getFinalAnswer(String answerType) {
        int viewType=Integer.valueOf(answerType);
        if(QuestionViewAdapter.IsTextQuestion(viewType)) {
            return bean.getAnswer();
        }else if(QuestionViewAdapter.IsDropdownQuestion(viewType)){
            return getOptionAnswer();
        }else if(QuestionViewAdapter.IsMultipleQuestion(viewType)){
            return getOptionAnswer();
        }else if(QuestionViewAdapter.IsRadioQuestion(viewType)){
            return getOptionAnswer();
        }else if(QuestionViewAdapter.IsLocationQuestion(viewType)){
            if (bean.getAnswer_type().equals(Global.AT_LOCATION_WITH_ADDRESS)){
                if(bean.getAnswer()!=null && !"".equalsIgnoreCase(bean.getAnswer())){
                    String[] hasil = bean.getAnswer().split("["+Global.DELIMETER_ROW+"]");
                    StringBuilder finalAnswer = new StringBuilder();
                    for (String s : hasil) {
                        finalAnswer.append(s).append("\n");
                    }
                    return finalAnswer.toString();
                }
                return "";
            }else {
                return bean.getAnswer();
            }
        }else if(QuestionViewAdapter.IsDateTimeQuestion(viewType)){
            return getDateTimeAnswer();
        }else if(QuestionViewAdapter.IsTextWithSuggestionQuestion(viewType)){
            return bean.getAnswer();
        }else if(QuestionViewAdapter.IsLookupQuestion(viewType)){
            return bean.getAnswer();
        }else if(QuestionViewAdapter.IsScoringQuestion(viewType)){
            return bean.getAnswer();
        } else if (QuestionViewAdapter.IsInvitationEsignQuestion(viewType)) {
            return bean.getAnswer();
        } else if (QuestionViewAdapter.isRegistrationCheckQuestion(viewType)) {
            return bean.getAnswer();
        } else if (QuestionViewAdapter.isGetOTRQuestion(viewType)) {
            return Tool.separateThousand(bean.getAnswer());
        } else if (QuestionViewAdapter.isValidationCheckQuestion(viewType)) {
            return bean.getAnswer();
        } else if (QuestionViewAdapter.isCheckReferantorQuestion(viewType)) {
            return bean.getAnswer();
        } else{
            return null;
        }
    }

    private String getDateTimeAnswer() {
        String finalformat = null;
        Date date = null;
        String finalAnswer = "";
        String answer = bean.getAnswer();
        String answerType = bean.getAnswer_type();
        try {
            if (Global.AT_DATE.equals(answerType)) {
                finalformat = Global.DATE_STR_FORMAT;
                String format = Global.DATE_STR_FORMAT_GSON;
                date = Formatter.parseDate(answer, format);
            } else if (Global.AT_TIME.equals(answerType)) {
                finalformat = Global.TIME_STR_FORMAT;
                String format = Global.TIME_STR_FORMAT2;
                date = Formatter.parseDate(answer, format);
            } else if (Global.AT_DATE_TIME.equals(answerType)) {
                finalformat = Global.DATE_TIME_STR_FORMAT;
                String format = Global.DATE_STR_FORMAT_GSON;
                date = Formatter.parseDate(answer, format);
            }
            finalAnswer = Formatter.formatDate(date, finalformat);
        } catch (Exception e) {
            try {
                finalAnswer = answer;
            } catch (Exception e1) {
                if (Global.IS_DEV)
                    e.printStackTrace();
            }
        }

        return finalAnswer;
    }

    private String getOptionAnswer() {
        StringBuilder sb = new StringBuilder();
        if (bean.getSelectedOptionAnswers().isEmpty()) {
            sb.append("- There is no selected field -");
        } else {
            List<OptionAnswerBean> listOptions = bean.getSelectedOptionAnswers();
            int i = 0;
            String[] arrSelectedAnswer = null;

            try {
                arrSelectedAnswer = Tool.split(bean.getAnswer(), Global.DELIMETER_DATA);
            } catch (Exception e) {
                FireCrash.log(e);
                arrSelectedAnswer = new String[0];
            }

            for (OptionAnswerBean optBean : listOptions) {
                if (optBean.isSelected()) {
                    if (Tool.isOptionsWithDescription(bean.getAnswer_type())) {
                        if (i < arrSelectedAnswer.length)
                            sb.append(optBean.getValue() + " - " + arrSelectedAnswer[i] + "; ");
                        else
                            sb.append(optBean.getValue() + " - ; ");
                    } else {
                        if (i > 0)
                            sb.append("\n");
                        sb.append(optBean.getValue());
                    }

                    if (Global.AT_MULTIPLE_ONE_DESCRIPTION.equals(bean.getAnswer_type())
                            || Global.AT_DROPDOWN_W_DESCRIPTION.equals(bean.getAnswer_type())) {
                        if (i == listOptions.size() - 1) {
                            sb.append("\nDesc : " + bean.getAnswer());
                        }
                    }
                } else {
                    sb.append(mActivity.getString(R.string.no_selected_field));
                }
                i++;
            }
            if (listOptions == null || listOptions.isEmpty()) {
                sb.append(mActivity.getString(R.string.no_selected_field));
            }
        }

        return sb.toString();
    }

    public void showCurrencyView(String answer) {
        mQuestionAnswer.setInputType(InputType.TYPE_CLASS_TEXT);
        String currencyView = Tool.separateThousand(answer);
        if (currencyView == null) currencyView = "";
        mQuestionAnswer.setText(currencyView);
    }
}
