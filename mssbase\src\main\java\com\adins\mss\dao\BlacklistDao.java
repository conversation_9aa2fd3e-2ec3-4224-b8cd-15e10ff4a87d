package com.adins.mss.dao;

import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;

import com.adins.mss.dao.Blacklist;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "MS_BLACKLIST".
*/
public class BlacklistDao extends AbstractDao<Blacklist, Long> {

    public static final String TABLENAME = "MS_BLACKLIST";

    /**
     * Properties of entity Blacklist.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Id = new Property(0, long.class, "id", true, "ID");
        public final static Property Exclude_info1 = new Property(1, String.class, "exclude_info1", false, "EXCLUDE_INFO1");
        public final static Property Exclude_info2 = new Property(2, String.class, "exclude_info2", false, "EXCLUDE_INFO2");
        public final static Property Exclude_type_code = new Property(3, String.class, "exclude_type_code", false, "EXCLUDE_TYPE_CODE");
        public final static Property Exclude_type_name = new Property(4, String.class, "exclude_type_name", false, "EXCLUDE_TYPE_NAME");
        public final static Property Is_deleted = new Property(5, Integer.class, "is_deleted", false, "IS_DELETED");
        public final static Property Dtm_upd = new Property(6, java.util.Date.class, "dtm_upd", false, "DTM_UPD");
    };


    public BlacklistDao(DaoConfig config) {
        super(config);
    }
    
    public BlacklistDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"MS_BLACKLIST\" (" + //
                "\"ID\" INTEGER PRIMARY KEY NOT NULL ," + // 0: id
                "\"EXCLUDE_INFO1\" TEXT," + // 1: exclude_info1
                "\"EXCLUDE_INFO2\" TEXT," + // 2: exclude_info2
                "\"EXCLUDE_TYPE_CODE\" TEXT NOT NULL ," + // 3: exclude_type_code
                "\"EXCLUDE_TYPE_NAME\" TEXT NOT NULL ," + // 4: exclude_type_name
                "\"IS_DELETED\" INTEGER," + // 5: is_deleted
                "\"DTM_UPD\" INTEGER);"); // 6: dtm_upd
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"MS_BLACKLIST\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, Blacklist entity) {
        stmt.clearBindings();
        stmt.bindLong(1, entity.getId());
 
        String exclude_info1 = entity.getExclude_info1();
        if (exclude_info1 != null) {
            stmt.bindString(2, exclude_info1);
        }
 
        String exclude_info2 = entity.getExclude_info2();
        if (exclude_info2 != null) {
            stmt.bindString(3, exclude_info2);
        }
        stmt.bindString(4, entity.getExclude_type_code());
        stmt.bindString(5, entity.getExclude_type_name());
 
        Integer is_deleted = entity.getIs_deleted();
        if (is_deleted != null) {
            stmt.bindLong(6, is_deleted);
        }
 
        java.util.Date dtm_upd = entity.getDtm_upd();
        if (dtm_upd != null) {
            stmt.bindLong(7, dtm_upd.getTime());
        }
    }

    /** @inheritdoc */
    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.getLong(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public Blacklist readEntity(Cursor cursor, int offset) {
        Blacklist entity = new Blacklist( //
            cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // exclude_info1
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // exclude_info2
            cursor.getString(offset + 3), // exclude_type_code
            cursor.getString(offset + 4), // exclude_type_name
            cursor.isNull(offset + 5) ? null : cursor.getInt(offset + 5), // is_deleted
            cursor.isNull(offset + 6) ? null : new java.util.Date(cursor.getLong(offset + 6)) // dtm_upd
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, Blacklist entity, int offset) {
        entity.setId(cursor.getLong(offset + 0));
        entity.setExclude_info1(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setExclude_info2(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setExclude_type_code(cursor.getString(offset + 3));
        entity.setExclude_type_name(cursor.getString(offset + 4));
        entity.setIs_deleted(cursor.isNull(offset + 5) ? null : cursor.getInt(offset + 5));
        entity.setDtm_upd(cursor.isNull(offset + 6) ? null : new java.util.Date(cursor.getLong(offset + 6)));
     }
    
    /** @inheritdoc */
    @Override
    protected Long updateKeyAfterInsert(Blacklist entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    /** @inheritdoc */
    @Override
    public Long getKey(Blacklist entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
}
