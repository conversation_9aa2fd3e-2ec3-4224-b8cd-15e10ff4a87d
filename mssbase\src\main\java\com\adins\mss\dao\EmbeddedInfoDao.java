package com.adins.mss.dao;

import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;

import com.adins.mss.dao.EmbeddedInfo;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "MS_EMBEDDED_INFO".
*/
public class EmbeddedInfoDao extends AbstractDao<EmbeddedInfo, Long> {

    public static final String TABLENAME = "MS_EMBEDDED_INFO";

    /**
     * Properties of entity EmbeddedInfo.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Id_embedded = new Property(0, long.class, "id_embedded", true, "ID_EMBEDDED");
        public final static Property Dtm_crt = new Property(1, java.util.Date.class, "dtm_crt", false, "DTM_CRT");
        public final static Property Usr_crt = new Property(2, String.class, "usr_crt", false, "USR_CRT");
        public final static Property Embedded_info = new Property(3, String.class, "embedded_info", false, "EMBEDDED_INFO");
        public final static Property Embedded_note = new Property(4, String.class, "embedded_note", false, "EMBEDDED_NOTE");
    };


    public EmbeddedInfoDao(DaoConfig config) {
        super(config);
    }
    
    public EmbeddedInfoDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"MS_EMBEDDED_INFO\" (" + //
                "\"ID_EMBEDDED\" INTEGER PRIMARY KEY NOT NULL ," + // 0: id_embedded
                "\"DTM_CRT\" INTEGER," + // 1: dtm_crt
                "\"USR_CRT\" TEXT," + // 2: usr_crt
                "\"EMBEDDED_INFO\" TEXT," + // 3: embedded_info
                "\"EMBEDDED_NOTE\" TEXT);"); // 4: embedded_note
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"MS_EMBEDDED_INFO\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, EmbeddedInfo entity) {
        stmt.clearBindings();
        stmt.bindLong(1, entity.getId_embedded());
 
        java.util.Date dtm_crt = entity.getDtm_crt();
        if (dtm_crt != null) {
            stmt.bindLong(2, dtm_crt.getTime());
        }
 
        String usr_crt = entity.getUsr_crt();
        if (usr_crt != null) {
            stmt.bindString(3, usr_crt);
        }
 
        String embedded_info = entity.getEmbedded_info();
        if (embedded_info != null) {
            stmt.bindString(4, embedded_info);
        }
 
        String embedded_note = entity.getEmbedded_note();
        if (embedded_note != null) {
            stmt.bindString(5, embedded_note);
        }
    }

    /** @inheritdoc */
    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.getLong(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public EmbeddedInfo readEntity(Cursor cursor, int offset) {
        EmbeddedInfo entity = new EmbeddedInfo( //
            cursor.getLong(offset + 0), // id_embedded
            cursor.isNull(offset + 1) ? null : new java.util.Date(cursor.getLong(offset + 1)), // dtm_crt
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // usr_crt
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // embedded_info
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4) // embedded_note
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, EmbeddedInfo entity, int offset) {
        entity.setId_embedded(cursor.getLong(offset + 0));
        entity.setDtm_crt(cursor.isNull(offset + 1) ? null : new java.util.Date(cursor.getLong(offset + 1)));
        entity.setUsr_crt(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setEmbedded_info(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setEmbedded_note(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
     }
    
    /** @inheritdoc */
    @Override
    protected Long updateKeyAfterInsert(EmbeddedInfo entity, long rowId) {
        entity.setId_embedded(rowId);
        return rowId;
    }
    
    /** @inheritdoc */
    @Override
    public Long getKey(EmbeddedInfo entity) {
        if(entity != null) {
            return entity.getId_embedded();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
}
