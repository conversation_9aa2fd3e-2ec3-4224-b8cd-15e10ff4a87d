package com.adins.mss.base.dukcapil;

import com.adins.mss.foundation.http.MssResponseType;
import com.google.gson.annotations.SerializedName;

public class ResponseImageDkcp extends MssResponseType {

    @SerializedName("dataDkcp") private ImageDkcpBean dataDkcp;
    @SerializedName("isStop") private String isStop;
    @SerializedName("errorMessage")  private String errorMessage;

    public ImageDkcpBean getDataDkcp() {
        return dataDkcp;
    }

    public void setDataDkcp(ImageDkcpBean dataDkcp) {
        this.dataDkcp = dataDkcp;
    }

    public String getIsStop() {
        return isStop;
    }

    public void setIsStop(String isStop) {
        this.isStop = isStop;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
