package com.adins.mss.dao;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "MS_PO_DEALER".
 */
public class PODealer {

     @SerializedName("id")
    private long id;
    /** Not-null value. */
     @SerializedName("dealerId")
    private String dealer_id;
     @SerializedName("dealerSchemeId")
    private int dealer_scheme_id;
     @SerializedName("dealerName")
    private String dealer_name;
     @SerializedName("dtmUpd")
    private java.util.Date dtm_upd;
     @SerializedName("isDeleted")
    private Integer is_deleted;

    public PODealer() {
    }

    public PODealer(long id) {
        this.id = id;
    }

    public PODealer(long id, String dealer_id, int dealer_scheme_id, String dealer_name, java.util.Date dtm_upd, Integer is_deleted) {
        this.id = id;
        this.dealer_id = dealer_id;
        this.dealer_scheme_id = dealer_scheme_id;
        this.dealer_name = dealer_name;
        this.dtm_upd = dtm_upd;
        this.is_deleted = is_deleted;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    /** Not-null value. */
    public String getDealer_id() {
        return dealer_id;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setDealer_id(String dealer_id) {
        this.dealer_id = dealer_id;
    }

    public int getDealer_scheme_id() {
        return dealer_scheme_id;
    }

    public void setDealer_scheme_id(int dealer_scheme_id) {
        this.dealer_scheme_id = dealer_scheme_id;
    }

    public String getDealer_name() {
        return dealer_name;
    }

    public void setDealer_name(String dealer_name) {
        this.dealer_name = dealer_name;
    }

    public java.util.Date getDtm_upd() {
        return dtm_upd;
    }

    public void setDtm_upd(java.util.Date dtm_upd) {
        this.dtm_upd = dtm_upd;
    }

    public Integer getIs_deleted() {
        return is_deleted;
    }

    public void setIs_deleted(Integer is_deleted) {
        this.is_deleted = is_deleted;
    }

}
