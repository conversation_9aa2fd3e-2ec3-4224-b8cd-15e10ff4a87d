package com.adins.mss.dao;

import java.util.List;
import java.util.ArrayList;
import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.SqlUtils;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;
import de.greenrobot.dao.query.Query;
import de.greenrobot.dao.query.QueryBuilder;

import com.adins.mss.dao.DepositReportD;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TR_DEPOSITREPORT_D".
*/
public class DepositReportDDao extends AbstractDao<DepositReportD, String> {

    public static final String TABLENAME = "TR_DEPOSITREPORT_D";

    /**
     * Properties of entity DepositReportD.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_deposit_report_d = new Property(0, String.class, "uuid_deposit_report_d", true, "UUID_DEPOSIT_REPORT_D");
        public final static Property Uuid_task_h = new Property(1, String.class, "uuid_task_h", false, "UUID_TASK_H");
        public final static Property Deposit_amt = new Property(2, String.class, "deposit_amt", false, "DEPOSIT_AMT");
        public final static Property Usr_crt = new Property(3, String.class, "usr_crt", false, "USR_CRT");
        public final static Property Dtm_crt = new Property(4, java.util.Date.class, "dtm_crt", false, "DTM_CRT");
        public final static Property Uuid_deposit_report_h = new Property(5, String.class, "uuid_deposit_report_h", false, "UUID_DEPOSIT_REPORT_H");
        public final static Property Is_sent = new Property(6, String.class, "is_sent", false, "IS_SENT");
    };

    private DaoSession daoSession;

    private Query<DepositReportD> depositReportH_DepositReportDListQuery;

    public DepositReportDDao(DaoConfig config) {
        super(config);
    }
    
    public DepositReportDDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TR_DEPOSITREPORT_D\" (" + //
                "\"UUID_DEPOSIT_REPORT_D\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_deposit_report_d
                "\"UUID_TASK_H\" TEXT," + // 1: uuid_task_h
                "\"DEPOSIT_AMT\" TEXT," + // 2: deposit_amt
                "\"USR_CRT\" TEXT," + // 3: usr_crt
                "\"DTM_CRT\" INTEGER," + // 4: dtm_crt
                "\"UUID_DEPOSIT_REPORT_H\" TEXT," + // 5: uuid_deposit_report_h
                "\"IS_SENT\" TEXT);"); // 6: is_sent
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TR_DEPOSITREPORT_D\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, DepositReportD entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_deposit_report_d());
 
        String uuid_task_h = entity.getUuid_task_h();
        if (uuid_task_h != null) {
            stmt.bindString(2, uuid_task_h);
        }
 
        String deposit_amt = entity.getDeposit_amt();
        if (deposit_amt != null) {
            stmt.bindString(3, deposit_amt);
        }
 
        String usr_crt = entity.getUsr_crt();
        if (usr_crt != null) {
            stmt.bindString(4, usr_crt);
        }
 
        java.util.Date dtm_crt = entity.getDtm_crt();
        if (dtm_crt != null) {
            stmt.bindLong(5, dtm_crt.getTime());
        }
 
        String uuid_deposit_report_h = entity.getUuid_deposit_report_h();
        if (uuid_deposit_report_h != null) {
            stmt.bindString(6, uuid_deposit_report_h);
        }
 
        String is_sent = entity.getIs_sent();
        if (is_sent != null) {
            stmt.bindString(7, is_sent);
        }
    }

    @Override
    protected void attachEntity(DepositReportD entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public DepositReportD readEntity(Cursor cursor, int offset) {
        DepositReportD entity = new DepositReportD( //
            cursor.getString(offset + 0), // uuid_deposit_report_d
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // uuid_task_h
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // deposit_amt
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // usr_crt
            cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)), // dtm_crt
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // uuid_deposit_report_h
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6) // is_sent
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, DepositReportD entity, int offset) {
        entity.setUuid_deposit_report_d(cursor.getString(offset + 0));
        entity.setUuid_task_h(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setDeposit_amt(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setUsr_crt(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setDtm_crt(cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)));
        entity.setUuid_deposit_report_h(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setIs_sent(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(DepositReportD entity, long rowId) {
        return entity.getUuid_deposit_report_d();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(DepositReportD entity) {
        if(entity != null) {
            return entity.getUuid_deposit_report_d();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
    /** Internal query to resolve the "depositReportDList" to-many relationship of DepositReportH. */
    public List<DepositReportD> _queryDepositReportH_DepositReportDList(String uuid_deposit_report_h) {
        synchronized (this) {
            if (depositReportH_DepositReportDListQuery == null) {
                QueryBuilder<DepositReportD> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_deposit_report_h.eq(null));
                depositReportH_DepositReportDListQuery = queryBuilder.build();
            }
        }
        Query<DepositReportD> query = depositReportH_DepositReportDListQuery.forCurrentThread();
        query.setParameter(0, uuid_deposit_report_h);
        return query.list();
    }

    private String selectDeep;

    protected String getSelectDeep() {
        if (selectDeep == null) {
            StringBuilder builder = new StringBuilder("SELECT ");
            SqlUtils.appendColumns(builder, "T", getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T0", daoSession.getDepositReportHDao().getAllColumns());
            builder.append(" FROM TR_DEPOSITREPORT_D T");
            builder.append(" LEFT JOIN TR_DEPOSITREPORT_H T0 ON T.\"UUID_DEPOSIT_REPORT_H\"=T0.\"UUID_DEPOSIT_REPORT_H\"");
            builder.append(' ');
            selectDeep = builder.toString();
        }
        return selectDeep;
    }
    
    protected DepositReportD loadCurrentDeep(Cursor cursor, boolean lock) {
        DepositReportD entity = loadCurrent(cursor, 0, lock);
        int offset = getAllColumns().length;

        DepositReportH depositReportH = loadCurrentOther(daoSession.getDepositReportHDao(), cursor, offset);
        entity.setDepositReportH(depositReportH);

        return entity;    
    }

    public DepositReportD loadDeep(Long key) {
        assertSinglePk();
        if (key == null) {
            return null;
        }

        StringBuilder builder = new StringBuilder(getSelectDeep());
        builder.append("WHERE ");
        SqlUtils.appendColumnsEqValue(builder, "T", getPkColumns());
        String sql = builder.toString();
        
        String[] keyArray = new String[] { key.toString() };
        Cursor cursor = db.rawQuery(sql, keyArray);
        
        try {
            boolean available = cursor.moveToFirst();
            if (!available) {
                return null;
            } else if (!cursor.isLast()) {
                throw new IllegalStateException("Expected unique result, but count was " + cursor.getCount());
            }
            return loadCurrentDeep(cursor, true);
        } finally {
            cursor.close();
        }
    }
    
    /** Reads all available rows from the given cursor and returns a list of new ImageTO objects. */
    public List<DepositReportD> loadAllDeepFromCursor(Cursor cursor) {
        int count = cursor.getCount();
        List<DepositReportD> list = new ArrayList<DepositReportD>(count);
        
        if (cursor.moveToFirst()) {
            if (identityScope != null) {
                identityScope.lock();
                identityScope.reserveRoom(count);
            }
            try {
                do {
                    list.add(loadCurrentDeep(cursor, false));
                } while (cursor.moveToNext());
            } finally {
                if (identityScope != null) {
                    identityScope.unlock();
                }
            }
        }
        return list;
    }
    
    protected List<DepositReportD> loadDeepAllAndCloseCursor(Cursor cursor) {
        try {
            return loadAllDeepFromCursor(cursor);
        } finally {
            cursor.close();
        }
    }
    

    /** A raw-style query where you can pass any WHERE clause and arguments. */
    public List<DepositReportD> queryDeep(String where, String... selectionArg) {
        Cursor cursor = db.rawQuery(getSelectDeep() + where, selectionArg);
        return loadDeepAllAndCloseCursor(cursor);
    }
 
}
