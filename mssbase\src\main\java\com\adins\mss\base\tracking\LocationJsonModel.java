package com.adins.mss.base.tracking;

import com.adins.mss.base.BaseCommunicationModel;
import com.adins.mss.dao.LocationInfo;

import java.util.List;

/**
 * JSON Format for location tracking upload
 *
 * <AUTHOR>
 * @deprecated as of 17 Dec 2014, as BaseCommunicationModel
 */
public class LocationJsonModel extends BaseCommunicationModel {

    private List<LocationInfo> locations;

    public LocationJsonModel() {
    }

    public LocationJsonModel(boolean useDefault, List<LocationInfo> locations) {
        super(useDefault);
        this.locations = locations;
    }

    public List<LocationInfo> getLocations() {
        return locations;
    }

    public void setLocations(List<LocationInfo> locations) {
        this.locations = locations;
    }

}
