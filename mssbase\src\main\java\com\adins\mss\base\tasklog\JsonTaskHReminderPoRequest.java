package com.adins.mss.base.tasklog;

import com.adins.mss.foundation.http.MssRequestType;
import com.google.gson.annotations.SerializedName;

import java.util.List;

public class JsonTaskHReminderPoRequest extends MssRequestType {

    @SerializedName("listTaskH")
    private List<TaskHPo> listTaskH;

    public List<TaskHPo> getListTaskH() {
        return listTaskH;
    }

    public void setListTaskH(List<TaskHPo> listTaskH) {
        this.listTaskH = listTaskH;
    }

    public static class TaskHPo {
        @SerializedName("uuid_task_h")
        String uuidTaskH;

        public String getUuidTaskH() {
            return uuidTaskH;
        }

        public void setUuidTaskH(String uuidTaskH) {
            this.uuidTaskH = uuidTaskH;
        }
    }

}
