package com.adins.mss.base.commons;

import android.content.Context;
import android.os.Environment;
import android.util.Log;

import com.adins.mss.base.util.ArchiveManager;
import com.adins.mss.base.util.FileEncryption;
import com.adins.mss.dao.Sync;
import com.adins.mss.foundation.camerainapp.helper.Logger;
import com.adins.mss.foundation.db.DaoOpenHelper;
import com.adins.mss.foundation.db.dataaccess.SyncDataAccess;
import com.google.firebase.database.DataSnapshot;
import com.google.firebase.database.DatabaseError;
import com.google.firebase.database.DatabaseReference;
import com.google.firebase.database.FirebaseDatabase;
import com.google.firebase.database.ValueEventListener;


import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import de.greenrobot.dao.database.Database;
import okhttp3.Call;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.BufferedSink;
import okio.BufferedSource;
import okio.Okio;

/**
 * Created by developer on 2/5/18.
 * <AUTHOR>
 */

public class UpdateFactory {
    private List<Sync> syncs = new ArrayList<>();
    private Integer index;
    private Context context;
    private String directory;
    private List<Metadata> metadatas;
    private FirebaseDatabase database;
    private DatabaseReference reference;

    //Shared Instance
    public static UpdateFactory getInstance(Context context) {
        return new UpdateFactory(context);
    }

    public UpdateFactory(Context context) {
        this.context = context;
        this.index   = 0;
        this.database= FirebaseDatabase.getInstance();
    }

    private void apply(Metadata metadata, Callback callback) {
        callback.onApply(metadata);
        long start = System.currentTimeMillis();
        try {
            Database db = DaoOpenHelper.getDb(context);
            db.execSQL("ATTACH '" + metadata.getPath() + "' AS EXTDB");
            db.execSQL("REPLACE INTO " + metadata.getIdentifier() + " SELECT * FROM EXTDB." + metadata.getIdentifier());
            db.execSQL("DETACH 'EXTDB'");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            Logger.i("INFO", "Update " + metadata.getIdentifier() + " Completed!");
            if (metadatas.size() == index) callback.onComplete();
        }

        long end = System.currentTimeMillis();
        Logger.i("INFO", "Took : " + ((end-start)/1000));
    }

    public void fetch(String document, final Callback callback) {
        syncs = SyncDataAccess.getAllTable(context);
        String packageName = context.getPackageName();

        directory = Environment.getExternalStorageDirectory().getPath() +
                "/Android/data/" + packageName + "/files";
        if (!new File(directory).exists()) new File(directory).mkdirs();

        reference = database.getReference(document);
        reference.addValueEventListener(new ValueEventListener() {
            @Override
            public void onDataChange(DataSnapshot dataSnapshot) {
                metadatas = new ArrayList<>();

                for (DataSnapshot snapshot : dataSnapshot.getChildren()) {
                    Metadata metadata = snapshot.getValue(Metadata.class);
                    metadatas.add(metadata);
                }

                try {
                    if (syncs == null || syncs.isEmpty()) {
                        //Check isFile Available on Disk
                        for (Metadata metadata : metadatas) {
                            String filename = Helper.getInstance().getNameFromUrl(metadata.getUrl());
                            File patchFile  = new File(directory.concat("/").concat(filename));

                            if (!patchFile.exists()) {
                                metadata.setFilename(filename);
                                metadata.setPath(patchFile.getPath());
                                download(metadata, callback);
                            } else {
                                //Todo: Check SHA-1 Signature
                            }

                            index++;
                        }
                    } else {
                        //Return to Main
                        callback.onComplete();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    Logger.i("INFO", "Patch Completed...");
//                    callback.onComplete();
                }
                Logger.i("INFO", String.valueOf(metadatas.size()));
            }

            @Override
            public void onCancelled(DatabaseError databaseError) {
                Logger.i("INFO", databaseError.toString());
                callback.onFailure(databaseError.toException());
            }
        });
    }

    private void download(final Metadata metadata, final Callback callback) {
        final File updateFile   = new File(metadata.getPath());
        OkHttpClient client     = new OkHttpClient();
        Request request = new Request.Builder()
                .url(metadata.getUrl())
                .build();

        callback.onDownload(metadata);
        client.newCall(request).enqueue(new okhttp3.Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                e.printStackTrace();
                callback.onFailure(e);
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.isSuccessful()) {
                    Response result     = response;
                    ResponseBody body   = result.body();

                    try {
                        long fileSize = body.contentLength();
                        BufferedSource source = body.source();
                        BufferedSink sink = Okio.buffer(Okio.sink(updateFile));

                        long totalRead  = 0;
                        long read       = 0;
                        while ((read = (source.read(sink.buffer(), 1024*2))) != -1) {
                            totalRead += read;
                            final int progress = (int) ((totalRead*100) / fileSize);
                            callback.onProgress(progress);
                        }

                        sink.writeAll(source);
                        sink.flush();
                        sink.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        try {
                            //Validating File Signature
                            if (updateFile.length() > 0 && Helper.getInstance().signature(updateFile, Helper.CHIPER_SHA1)
                                    .equalsIgnoreCase(metadata.getSha1())) {
                                callback.onDecrypt(metadata, 0);
                                File archived = new File(updateFile.getPath() + ".zip");
                                final File finalFile = updateFile;
                                FileEncryption.getInstance()
                                        .loadKey(metadata.getSignature())
                                        .decrypt(updateFile, archived, new com.adins.mss.base.commons.Callback() {
                                            @Override
                                            public void onComplete(Object obj) {
                                                finalFile.delete();
                                                callback.onDecrypt(metadata, 1);
                                                Logger.i("INFO", "Deleted file....");

                                                File file = (File) obj;
                                                try {
                                                    callback.onArchive(metadata, 0);
                                                    ArchiveManager.getInstance()
                                                            .extract(file.getPath(), directory);
                                                } finally {
                                                    file.delete();
                                                    callback.onArchive(metadata, 1);
                                                    apply(metadata, callback);
                                                }
                                            }
                                        });
                            } else {
                                Logger.i("INFO", "File has corrupted....");
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        } finally {
                            Logger.i("INFO", "Operation Completed....");
                        }
                    }
                } else {
                    callback.onFailure(null);
                    Log.i(getClass().getSimpleName(), "Download Failed.....");
                }
            }
        });
    }

    public interface Callback {
        void onComplete();
        void onFailure(Exception e);
        void onDownload(Metadata metadata);
        void onProgress(Integer progress);
        void onApply(Metadata metadata);
        void onDecrypt(Metadata metadata, Integer state);
        void onArchive(Metadata metadata, Integer state);
    }
}
