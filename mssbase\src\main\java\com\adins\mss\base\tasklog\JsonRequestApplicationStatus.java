package com.adins.mss.base.tasklog;

import com.adins.mss.foundation.http.MssRequestType;
import com.google.gson.annotations.SerializedName;

import java.util.List;

public class JsonRequestApplicationStatus extends MssRequestType {

    @SerializedName("taskStatusList")
    private List<TaskStatus> taskStatusList;

    public void setTaskStatusList(List<TaskStatus> taskStatusList) {
        this.taskStatusList = taskStatusList;
    }

    public static class TaskStatus {
        @SerializedName("uuid_task_h")
        String uuidTaskH;

        @SerializedName("appl_no")
        String applNo;

        @SerializedName("status_application")
        String statusApplication;

        @SerializedName("is_sent_confins")
        Integer isSentConfins;

        public TaskStatus() {
        }

        public String getUuidTaskH() {
            return uuidTaskH;
        }

        public void setUuidTaskH(String uuidTaskH) {
            this.uuidTaskH = uuidTaskH;
        }

        public String getApplNo() {
            return applNo;
        }

        public void setApplNo(String applNo) {
            this.applNo = applNo;
        }

        public String getStatusApplication() {
            return statusApplication;
        }

        public void setStatusApplication(String statusApplication) {
            this.statusApplication = statusApplication;
        }

        public Integer getIsSentConfins() {
            return isSentConfins;
        }

        public void setIsSentConfins(Integer isSentConfins) {
            this.isSentConfins = isSentConfins;
        }
    }
}
