package com.adins.mss.base.commons;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * Created by developer on 1/19/18.
 */

public class Query {
    @SerializedName("table")
    private String table;

    @SerializedName("fields")
    private String[] fields;

    @SerializedName("field")
    private String field;

    @SerializedName("code")
    private String code;

    @SerializedName("join")
    private List<Join> join;

    @SerializedName(value = "constraint", alternate = {"keyvalues"})
    private List<Constraint> constraint = null;

    @SerializedName("constraint2")
    private List<Constraint> constraint2 = null;

    @SerializedName("group")
    private String group;

    @SerializedName("order")
    private String[] order;

    public String getTable() {
        return table;
    }

    public void setTable(String table) {
        this.table = table;
    }

    public String[] getFields() {
        return fields;
    }

    public void setFields(String[] fields) {
        this.fields = fields;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public List<Join> getJoin() {
        return join;
    }

    public void setJoin(List<Join> join) {
        this.join = join;
    }

    public List<Constraint> getConstraint() {
        return constraint;
    }

    public void setConstraint(List<Constraint> constraint) {
        this.constraint = constraint;
    }

    public List<Constraint> getConstraint2() {
        return constraint2;
    }

    public void setConstraint2(List<Constraint> constraint2) {
        this.constraint2 = constraint2;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String[] getOrder() {
        return order;
    }

    public void setOrder(String[] order) {
        this.order = order;
    }

    public static class Join {
        @SerializedName("table")
        private String table;

        @SerializedName("on")
        private String on;

        public String getTable() {
            return table;
        }

        public void setTable(String table) {
            this.table = table;
        }

        public String getOn() {
            return on;
        }

        public void setOn(String on) {
            this.on = on;
        }
    }

    public static class Constraint {
        @SerializedName(value = "column", alternate = {"id"})
        private String column;

        @SerializedName("operator")
        private String operator;

        @SerializedName("value")
        private String value;

        public String getColumn() {
            return column;
        }

        public void setColumn(String column) {
            this.column = column;
        }

        public String getOperator() {
            return operator;
        }

        public void setOperator(String operator) {
            this.operator = operator;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }
}
