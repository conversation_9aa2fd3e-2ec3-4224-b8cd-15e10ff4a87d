package com.adins.mss.foundation.db.dataaccess;

import android.content.Context;

import com.adins.mss.dao.DaoSession;
import com.adins.mss.dao.Sync;
import com.adins.mss.dao.SyncDao;
import com.adins.mss.foundation.db.DaoOpenHelper;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

import de.greenrobot.dao.query.QueryBuilder;

public class SyncDataAccess {

//	private static DaoOpenHelper daoOpenHelper;

    /**
     * use to generate dao session that you can access modelDao
     *
     * @param context --> context from activity
     * @return
     */
    protected static DaoSession getDaoSession(Context context) {
        /*if(daoOpenHelper==null){
//			if(daoOpenHelper.getDaoSession()==null)
				daoOpenHelper = new DaoOpenHelper(context);
		}
		DaoSession daoSeesion = daoOpenHelper.getDaoSession();
		return daoSeesion;*/
        return DaoOpenHelper.getDaoSession(context);
    }


    /**
     * get Holiday dao and you can access the DB
     *
     * @param context
     * @return
     */
    protected static SyncDao getSyncDao(Context context) {
        return getDaoSession(context).getSyncDao();
    }

    /**
     * Clear session, close db and set daoOpenHelper to null
     */
    public static void closeAll() {
        /*if(daoOpenHelper!=null){
            daoOpenHelper.closeAll();
			daoOpenHelper = null;
		}*/
        DaoOpenHelper.closeAll();
    }

    /**
     * add holiday as entity
     *
     * @param context
     * @param sync
     */
    public static void add(Context context, Sync sync) {
        getSyncDao(context).insert(sync);
        getDaoSession(context).clear();
    }

    /**
     * add holiday as list entity
     *
     * @param context
     * @param listSync
     */
    public static void add(Context context, List<Sync> listSync) {
        getSyncDao(context).insertInTx(listSync);
        getDaoSession(context).clear();
    }


    /**
     * delete all content in table.
     *
     * @param context
     */
    public static void clean(Context context) {
        getSyncDao(context).deleteAll();
    }


    /**
     * @param context
     * @param sync
     */
    public static void delete(Context context, Sync sync) {
        getSyncDao(context).delete(sync);
        getDaoSession(context).clear();
    }

    /**
     * @param context
     * @param sync
     */
    public static void update(Context context, Sync sync) {
//		delete(context, sync);
//		add(context, sync);
        getSyncDao(context).update(sync);
        getDaoSession(context).clear();
    }


    /**
     * add or replace data taskH
     *
     * @param context
     * @param sync
     */
    public static void addOrReplace(Context context, Sync sync) {

        if (sync.getLov_group() != null) {
            Sync syncDb = getOneFromLov(context, sync.getLov_group());
            if (syncDb != null) {
                delete(context, syncDb);
                //update(context, sync);
            }
        } else {
            Sync syncDb = getOneByTable(context, sync.getTabel_name());
            if (syncDb != null) {
                delete(context, syncDb);
            }
        }

        //else {
        add(context, sync);
        //}
//		getSyncDao(context).insertOrReplaceInTx(sync);
        getDaoSession(context).clear();
    }

    public static void insertOrReplace(Context context, Sync sync) {
        getDaoSession(context).insertOrReplace(sync);
        getDaoSession(context).clear();
    }

    public static void addOrReplaceAll(Context context, List<Sync> syncs) {
        getSyncDao(context).insertOrReplaceInTx(syncs);
        getDaoSession(context).clear();
    }


    public static Sync getOne(Context context, String uuid_sync) {
        QueryBuilder<Sync> qb = getSyncDao(context).queryBuilder();
        qb.where(SyncDao.Properties.Uuid_sync.eq(uuid_sync));
        qb.build();
        if (qb.list().isEmpty())
            return null;
        return qb.list().get(0);
    }

    public static Sync getOneFromLov(Context context, String lov_group) {
        QueryBuilder<Sync> qb = getSyncDao(context).queryBuilder();
        qb.where(SyncDao.Properties.Lov_group.eq(lov_group));
        qb.build();
        if (qb.list().isEmpty())
            return null;
        return qb.list().get(0);
    }

    public static Sync getOneByLovGroupName(Context context, String lov_group) {
        QueryBuilder<Sync> qb = getSyncDao(context).queryBuilder();
        qb.where(SyncDao.Properties.Lov_group.eq(lov_group));
        qb.build();
        if (qb.list().isEmpty())
            return null;
        return qb.list().get(0);
    }

    public static Sync getOneByTable(Context context, String table) {
        QueryBuilder<Sync> qb = getSyncDao(context).queryBuilder();
        qb.where(SyncDao.Properties.Tabel_name.eq(table));
        qb.build();

        if (!qb.list().isEmpty()) return qb.list().get(0);
        else return null;
    }

    public static List<Sync> getAll(Context context) {
        QueryBuilder<Sync> qb = getSyncDao(context).queryBuilder();
        //qb.where(TaskHDao.Properties.Uuid_task_h.eq(uuid_sync));
        qb.build();
        if (qb.list().isEmpty()) {
            return null;
        }

        return qb.list();
    }

    public static List<Sync> getAllTable(Context context){
        QueryBuilder<Sync> qb = getSyncDao(context).queryBuilder();
        qb.where(SyncDao.Properties.Tabel_name.notEq(""),
                SyncDao.Properties.Tabel_name.isNotNull());
        qb.build();
        if(qb.list().isEmpty())
            return null;
        return qb.list();
    }

    public static Sync getLastUpated(Context context) {
        QueryBuilder<Sync> qb = getSyncDao(context).queryBuilder();
        qb.orderDesc(SyncDao.Properties.Dtm_upd);
        qb.limit(1);
        qb.build();
        if (qb.list().isEmpty())
            return null;
        return qb.list().get(0);
    }


    public static Sync getDay(Context context, Date date) {


        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.MILLISECOND, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.HOUR_OF_DAY, 0);

        QueryBuilder<Sync> qb = getSyncDao(context).queryBuilder();
        qb.where(SyncDao.Properties.Dtm_upd.ge(cal.getTime()));
        qb.orderAsc(SyncDao.Properties.Dtm_upd);
        qb.limit(1);
        qb.build();
        if (qb.list().isEmpty())
            return null;
        return qb.list().get(0);
    }

}
