package com.adins.mss.dao;

import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;

import com.adins.mss.dao.User;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "MS_USER".
*/
public class UserDao extends AbstractDao<User, String> {

    public static final String TABLENAME = "MS_USER";

    /**
     * Properties of entity User.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_user = new Property(0, String.class, "uuid_user", true, "UUID_USER");
        public final static Property Flag_job = new Property(1, String.class, "flag_job", false, "FLAG_JOB");
        public final static Property Image_profile = new Property(2, byte[].class, "image_profile", false, "IMAGE_PROFILE");
        public final static Property Fullname = new Property(3, String.class, "fullname", false, "FULLNAME");
        public final static Property Branch_id = new Property(4, String.class, "branch_id", false, "BRANCH_ID");
        public final static Property Branch_name = new Property(5, String.class, "branch_name", false, "BRANCH_NAME");
        public final static Property Is_branch = new Property(6, String.class, "is_branch", false, "IS_BRANCH");
        public final static Property Password = new Property(7, String.class, "password", false, "PASSWORD");
        public final static Property Task_seq = new Property(8, Integer.class, "task_seq", false, "TASK_SEQ");
        public final static Property Google_id = new Property(9, String.class, "google_id", false, "GOOGLE_ID");
        public final static Property Facebook_id = new Property(10, String.class, "facebook_id", false, "FACEBOOK_ID");
        public final static Property Login_id = new Property(11, String.class, "login_id", false, "LOGIN_ID");
        public final static Property Fail_count = new Property(12, Integer.class, "fail_count", false, "FAIL_COUNT");
        public final static Property Last_sync = new Property(13, java.util.Date.class, "last_sync", false, "LAST_SYNC");
        public final static Property Branch_address = new Property(14, String.class, "branch_address", false, "BRANCH_ADDRESS");
        public final static Property Usr_crt = new Property(15, String.class, "usr_crt", false, "USR_CRT");
        public final static Property Dtm_crt = new Property(16, java.util.Date.class, "dtm_crt", false, "DTM_CRT");
        public final static Property Usr_upd = new Property(17, String.class, "usr_upd", false, "USR_UPD");
        public final static Property Dtm_upd = new Property(18, java.util.Date.class, "dtm_upd", false, "DTM_UPD");
        public final static Property Image_cover = new Property(19, byte[].class, "image_cover", false, "IMAGE_COVER");
        public final static Property Chg_pwd = new Property(20, String.class, "chg_pwd", false, "CHG_PWD");
        public final static Property Job_description = new Property(21, String.class, "job_description", false, "JOB_DESCRIPTION");
        public final static Property Pwd_exp = new Property(22, String.class, "pwd_exp", false, "PWD_EXP");
        public final static Property Dealer_name = new Property(23, String.class, "dealer_name", false, "DEALER_NAME");
        public final static Property Cash_limit = new Property(24, String.class, "cash_limit", false, "CASH_LIMIT");
        public final static Property Cash_on_hand = new Property(25, String.class, "cash_on_hand", false, "CASH_ON_HAND");
        public final static Property Uuid_branch = new Property(26, String.class, "uuid_branch", false, "UUID_BRANCH");
        public final static Property Uuid_group = new Property(27, String.class, "uuid_group", false, "UUID_GROUP");
        public final static Property Uuid_dealer = new Property(28, String.class, "uuid_dealer", false, "UUID_DEALER");
        public final static Property Start_time = new Property(29, String.class, "start_time", false, "START_TIME");
        public final static Property End_time = new Property(30, String.class, "end_time", false, "END_TIME");
        public final static Property Is_tracking = new Property(31, String.class, "is_tracking", false, "IS_TRACKING");
        public final static Property Tracking_days = new Property(32, String.class, "tracking_days", false, "TRACKING_DAYS");
        public final static Property Token_id_fcm = new Property(33, String.class, "token_id_fcm", false, "TOKEN_ID_FCM");
        public final static Property Is_piloting = new Property(34, String.class, "is_piloting", false, "IS_PILOTING");
        public final static Property Pushsync_time = new Property(35, String.class, "pushsync_time", false, "PUSHSYNC_TIME");
        public final static Property Is_piloting_cae = new Property(36, String.class, "is_piloting_cae", false, "IS_PILOTING_CAE");
        public final static Property Branch_type = new Property(37, String.class, "branch_type", false, "BRANCH_TYPE");
    };

    private DaoSession daoSession;


    public UserDao(DaoConfig config) {
        super(config);
    }
    
    public UserDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"MS_USER\" (" + //
                "\"UUID_USER\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_user
                "\"FLAG_JOB\" TEXT," + // 1: flag_job
                "\"IMAGE_PROFILE\" BLOB," + // 2: image_profile
                "\"FULLNAME\" TEXT," + // 3: fullname
                "\"BRANCH_ID\" TEXT," + // 4: branch_id
                "\"BRANCH_NAME\" TEXT," + // 5: branch_name
                "\"IS_BRANCH\" TEXT," + // 6: is_branch
                "\"PASSWORD\" TEXT," + // 7: password
                "\"TASK_SEQ\" INTEGER," + // 8: task_seq
                "\"GOOGLE_ID\" TEXT," + // 9: google_id
                "\"FACEBOOK_ID\" TEXT," + // 10: facebook_id
                "\"LOGIN_ID\" TEXT," + // 11: login_id
                "\"FAIL_COUNT\" INTEGER," + // 12: fail_count
                "\"LAST_SYNC\" INTEGER," + // 13: last_sync
                "\"BRANCH_ADDRESS\" TEXT," + // 14: branch_address
                "\"USR_CRT\" TEXT," + // 15: usr_crt
                "\"DTM_CRT\" INTEGER," + // 16: dtm_crt
                "\"USR_UPD\" TEXT," + // 17: usr_upd
                "\"DTM_UPD\" INTEGER," + // 18: dtm_upd
                "\"IMAGE_COVER\" BLOB," + // 19: image_cover
                "\"CHG_PWD\" TEXT," + // 20: chg_pwd
                "\"JOB_DESCRIPTION\" TEXT," + // 21: job_description
                "\"PWD_EXP\" TEXT," + // 22: pwd_exp
                "\"DEALER_NAME\" TEXT," + // 23: dealer_name
                "\"CASH_LIMIT\" TEXT," + // 24: cash_limit
                "\"CASH_ON_HAND\" TEXT," + // 25: cash_on_hand
                "\"UUID_BRANCH\" TEXT," + // 26: uuid_branch
                "\"UUID_GROUP\" TEXT," + // 27: uuid_group
                "\"UUID_DEALER\" TEXT," + // 28: uuid_dealer
                "\"START_TIME\" TEXT," + // 29: start_time
                "\"END_TIME\" TEXT," + // 30: end_time
                "\"IS_TRACKING\" TEXT," + // 31: is_tracking
                "\"TRACKING_DAYS\" TEXT," + // 32: tracking_days
                "\"TOKEN_ID_FCM\" TEXT," + // 33: token_id_fcm
                "\"IS_PILOTING\" TEXT," + // 34: is_piloting
                "\"PUSHSYNC_TIME\" TEXT," + // 35: pushsync_time
                "\"IS_PILOTING_CAE\" TEXT," + // 36: is_piloting_cae
                "\"BRANCH_TYPE\" TEXT);"); // 37: branch_type
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"MS_USER\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, User entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_user());
 
        String flag_job = entity.getFlag_job();
        if (flag_job != null) {
            stmt.bindString(2, flag_job);
        }
 
        byte[] image_profile = entity.getImage_profile();
        if (image_profile != null) {
            stmt.bindBlob(3, image_profile);
        }
 
        String fullname = entity.getFullname();
        if (fullname != null) {
            stmt.bindString(4, fullname);
        }
 
        String branch_id = entity.getBranch_id();
        if (branch_id != null) {
            stmt.bindString(5, branch_id);
        }
 
        String branch_name = entity.getBranch_name();
        if (branch_name != null) {
            stmt.bindString(6, branch_name);
        }
 
        String is_branch = entity.getIs_branch();
        if (is_branch != null) {
            stmt.bindString(7, is_branch);
        }
 
        String password = entity.getPassword();
        if (password != null) {
            stmt.bindString(8, password);
        }
 
        Integer task_seq = entity.getTask_seq();
        if (task_seq != null) {
            stmt.bindLong(9, task_seq);
        }
 
        String google_id = entity.getGoogle_id();
        if (google_id != null) {
            stmt.bindString(10, google_id);
        }
 
        String facebook_id = entity.getFacebook_id();
        if (facebook_id != null) {
            stmt.bindString(11, facebook_id);
        }
 
        String login_id = entity.getLogin_id();
        if (login_id != null) {
            stmt.bindString(12, login_id);
        }
 
        Integer fail_count = entity.getFail_count();
        if (fail_count != null) {
            stmt.bindLong(13, fail_count);
        }
 
        java.util.Date last_sync = entity.getLast_sync();
        if (last_sync != null) {
            stmt.bindLong(14, last_sync.getTime());
        }
 
        String branch_address = entity.getBranch_address();
        if (branch_address != null) {
            stmt.bindString(15, branch_address);
        }
 
        String usr_crt = entity.getUsr_crt();
        if (usr_crt != null) {
            stmt.bindString(16, usr_crt);
        }
 
        java.util.Date dtm_crt = entity.getDtm_crt();
        if (dtm_crt != null) {
            stmt.bindLong(17, dtm_crt.getTime());
        }
 
        String usr_upd = entity.getUsr_upd();
        if (usr_upd != null) {
            stmt.bindString(18, usr_upd);
        }
 
        java.util.Date dtm_upd = entity.getDtm_upd();
        if (dtm_upd != null) {
            stmt.bindLong(19, dtm_upd.getTime());
        }
 
        byte[] image_cover = entity.getImage_cover();
        if (image_cover != null) {
            stmt.bindBlob(20, image_cover);
        }
 
        String chg_pwd = entity.getChg_pwd();
        if (chg_pwd != null) {
            stmt.bindString(21, chg_pwd);
        }
 
        String job_description = entity.getJob_description();
        if (job_description != null) {
            stmt.bindString(22, job_description);
        }
 
        String pwd_exp = entity.getPwd_exp();
        if (pwd_exp != null) {
            stmt.bindString(23, pwd_exp);
        }
 
        String dealer_name = entity.getDealer_name();
        if (dealer_name != null) {
            stmt.bindString(24, dealer_name);
        }
 
        String cash_limit = entity.getCash_limit();
        if (cash_limit != null) {
            stmt.bindString(25, cash_limit);
        }
 
        String cash_on_hand = entity.getCash_on_hand();
        if (cash_on_hand != null) {
            stmt.bindString(26, cash_on_hand);
        }
 
        String uuid_branch = entity.getUuid_branch();
        if (uuid_branch != null) {
            stmt.bindString(27, uuid_branch);
        }
 
        String uuid_group = entity.getUuid_group();
        if (uuid_group != null) {
            stmt.bindString(28, uuid_group);
        }
 
        String uuid_dealer = entity.getUuid_dealer();
        if (uuid_dealer != null) {
            stmt.bindString(29, uuid_dealer);
        }
 
        String start_time = entity.getStart_time();
        if (start_time != null) {
            stmt.bindString(30, start_time);
        }
 
        String end_time = entity.getEnd_time();
        if (end_time != null) {
            stmt.bindString(31, end_time);
        }
 
        String is_tracking = entity.getIs_tracking();
        if (is_tracking != null) {
            stmt.bindString(32, is_tracking);
        }
 
        String tracking_days = entity.getTracking_days();
        if (tracking_days != null) {
            stmt.bindString(33, tracking_days);
        }
 
        String token_id_fcm = entity.getToken_id_fcm();
        if (token_id_fcm != null) {
            stmt.bindString(34, token_id_fcm);
        }
 
        String is_piloting = entity.getIs_piloting();
        if (is_piloting != null) {
            stmt.bindString(35, is_piloting);
        }
 
        String pushsync_time = entity.getPushsync_time();
        if (pushsync_time != null) {
            stmt.bindString(36, pushsync_time);
        }
 
        String is_piloting_cae = entity.getIs_piloting_cae();
        if (is_piloting_cae != null) {
            stmt.bindString(37, is_piloting_cae);
        }
 
        String branch_type = entity.getBranch_type();
        if (branch_type != null) {
            stmt.bindString(38, branch_type);
        }
    }

    @Override
    protected void attachEntity(User entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public User readEntity(Cursor cursor, int offset) {
        User entity = new User( //
            cursor.getString(offset + 0), // uuid_user
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // flag_job
            cursor.isNull(offset + 2) ? null : cursor.getBlob(offset + 2), // image_profile
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // fullname
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // branch_id
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // branch_name
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // is_branch
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7), // password
            cursor.isNull(offset + 8) ? null : cursor.getInt(offset + 8), // task_seq
            cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9), // google_id
            cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10), // facebook_id
            cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11), // login_id
            cursor.isNull(offset + 12) ? null : cursor.getInt(offset + 12), // fail_count
            cursor.isNull(offset + 13) ? null : new java.util.Date(cursor.getLong(offset + 13)), // last_sync
            cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14), // branch_address
            cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15), // usr_crt
            cursor.isNull(offset + 16) ? null : new java.util.Date(cursor.getLong(offset + 16)), // dtm_crt
            cursor.isNull(offset + 17) ? null : cursor.getString(offset + 17), // usr_upd
            cursor.isNull(offset + 18) ? null : new java.util.Date(cursor.getLong(offset + 18)), // dtm_upd
            cursor.isNull(offset + 19) ? null : cursor.getBlob(offset + 19), // image_cover
            cursor.isNull(offset + 20) ? null : cursor.getString(offset + 20), // chg_pwd
            cursor.isNull(offset + 21) ? null : cursor.getString(offset + 21), // job_description
            cursor.isNull(offset + 22) ? null : cursor.getString(offset + 22), // pwd_exp
            cursor.isNull(offset + 23) ? null : cursor.getString(offset + 23), // dealer_name
            cursor.isNull(offset + 24) ? null : cursor.getString(offset + 24), // cash_limit
            cursor.isNull(offset + 25) ? null : cursor.getString(offset + 25), // cash_on_hand
            cursor.isNull(offset + 26) ? null : cursor.getString(offset + 26), // uuid_branch
            cursor.isNull(offset + 27) ? null : cursor.getString(offset + 27), // uuid_group
            cursor.isNull(offset + 28) ? null : cursor.getString(offset + 28), // uuid_dealer
            cursor.isNull(offset + 29) ? null : cursor.getString(offset + 29), // start_time
            cursor.isNull(offset + 30) ? null : cursor.getString(offset + 30), // end_time
            cursor.isNull(offset + 31) ? null : cursor.getString(offset + 31), // is_tracking
            cursor.isNull(offset + 32) ? null : cursor.getString(offset + 32), // tracking_days
            cursor.isNull(offset + 33) ? null : cursor.getString(offset + 33), // token_id_fcm
            cursor.isNull(offset + 34) ? null : cursor.getString(offset + 34), // is_piloting
            cursor.isNull(offset + 35) ? null : cursor.getString(offset + 35), // pushsync_time
            cursor.isNull(offset + 36) ? null : cursor.getString(offset + 36), // is_piloting_cae
            cursor.isNull(offset + 37) ? null : cursor.getString(offset + 37) // branch_type
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, User entity, int offset) {
        entity.setUuid_user(cursor.getString(offset + 0));
        entity.setFlag_job(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setImage_profile(cursor.isNull(offset + 2) ? null : cursor.getBlob(offset + 2));
        entity.setFullname(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setBranch_id(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setBranch_name(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setIs_branch(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setPassword(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
        entity.setTask_seq(cursor.isNull(offset + 8) ? null : cursor.getInt(offset + 8));
        entity.setGoogle_id(cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9));
        entity.setFacebook_id(cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10));
        entity.setLogin_id(cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11));
        entity.setFail_count(cursor.isNull(offset + 12) ? null : cursor.getInt(offset + 12));
        entity.setLast_sync(cursor.isNull(offset + 13) ? null : new java.util.Date(cursor.getLong(offset + 13)));
        entity.setBranch_address(cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14));
        entity.setUsr_crt(cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15));
        entity.setDtm_crt(cursor.isNull(offset + 16) ? null : new java.util.Date(cursor.getLong(offset + 16)));
        entity.setUsr_upd(cursor.isNull(offset + 17) ? null : cursor.getString(offset + 17));
        entity.setDtm_upd(cursor.isNull(offset + 18) ? null : new java.util.Date(cursor.getLong(offset + 18)));
        entity.setImage_cover(cursor.isNull(offset + 19) ? null : cursor.getBlob(offset + 19));
        entity.setChg_pwd(cursor.isNull(offset + 20) ? null : cursor.getString(offset + 20));
        entity.setJob_description(cursor.isNull(offset + 21) ? null : cursor.getString(offset + 21));
        entity.setPwd_exp(cursor.isNull(offset + 22) ? null : cursor.getString(offset + 22));
        entity.setDealer_name(cursor.isNull(offset + 23) ? null : cursor.getString(offset + 23));
        entity.setCash_limit(cursor.isNull(offset + 24) ? null : cursor.getString(offset + 24));
        entity.setCash_on_hand(cursor.isNull(offset + 25) ? null : cursor.getString(offset + 25));
        entity.setUuid_branch(cursor.isNull(offset + 26) ? null : cursor.getString(offset + 26));
        entity.setUuid_group(cursor.isNull(offset + 27) ? null : cursor.getString(offset + 27));
        entity.setUuid_dealer(cursor.isNull(offset + 28) ? null : cursor.getString(offset + 28));
        entity.setStart_time(cursor.isNull(offset + 29) ? null : cursor.getString(offset + 29));
        entity.setEnd_time(cursor.isNull(offset + 30) ? null : cursor.getString(offset + 30));
        entity.setIs_tracking(cursor.isNull(offset + 31) ? null : cursor.getString(offset + 31));
        entity.setTracking_days(cursor.isNull(offset + 32) ? null : cursor.getString(offset + 32));
        entity.setToken_id_fcm(cursor.isNull(offset + 33) ? null : cursor.getString(offset + 33));
        entity.setIs_piloting(cursor.isNull(offset + 34) ? null : cursor.getString(offset + 34));
        entity.setPushsync_time(cursor.isNull(offset + 35) ? null : cursor.getString(offset + 35));
        entity.setIs_piloting_cae(cursor.isNull(offset + 36) ? null : cursor.getString(offset + 36));
        entity.setBranch_type(cursor.isNull(offset + 37) ? null : cursor.getString(offset + 37));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(User entity, long rowId) {
        return entity.getUuid_user();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(User entity) {
        if(entity != null) {
            return entity.getUuid_user();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
}
