package com.adins.mss.base.timeline.comment;

import android.content.Context;

import com.adins.mss.dao.Comment;
import com.adins.mss.foundation.db.dataaccess.CommentDataAccess;
import com.adins.mss.foundation.formatter.Tool;

import java.util.Date;
import java.util.List;

public class CommentManager {
    private Context mContext;

    public CommentManager(Context context) {
        this.mContext = context;
    }

    public List<Comment> getAllComment(String uuidTimeline) {
        return CommentDataAccess.getAll(mContext, uuidTimeline);
    }

    public void insertComment(List<Comment> commentList) {
        CommentDataAccess.add(mContext, commentList);
    }

    public void insertComment(Comment comment) {
        CommentDataAccess.add(mContext, comment);
    }

    public void insertComment(String contentComment, String senderId, String senderName, String usrCrt, String usrUpd, String uuidTimeline) {
        String uuidComment = Tool.getUUID();
        Date dtmCrtServer = null;
        Date dtmCrt = new Date(System.currentTimeMillis());
        java.util.Date dtmUpd = new Date(System.currentTimeMillis());
        Comment comment = new Comment(uuidComment, contentComment, dtmCrtServer, senderId, senderName, usrCrt, dtmCrt, usrUpd, dtmUpd, uuidTimeline);
        CommentDataAccess.add(mContext, comment);
    }

    public void deleteComment(Comment comment) {
        CommentDataAccess.delete(mContext, comment);
    }

    public void deleteComment(String uuidTimeline) {
        CommentDataAccess.delete(mContext, uuidTimeline);
    }

    public void deleteAllComment() {
        CommentDataAccess.clean(mContext);
    }

    public void updateComment(Comment comment) {
        CommentDataAccess.update(mContext, comment);
    }
}
