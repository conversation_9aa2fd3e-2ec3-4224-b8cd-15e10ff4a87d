//package com.adins.mss.base.dynamicform;
//
//import java.io.File;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.Comparator;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.LinkedHashMap;
//import java.util.List;
//import java.util.Locale;
//import java.util.Map;
//import java.util.Timer;
//import java.util.TimerTask;
//
//import org.acra.ACRA;
//import org.apache.commons.jexl2.JexlContext;
//import org.apache.commons.jexl2.JexlEngine;
//import org.apache.commons.jexl2.MapContext;
//
//import android.app.Activity;
//import android.app.DatePickerDialog;
//import android.app.Dialog;
//import android.app.ProgressDialog;
//import android.app.TimePickerDialog;
//import android.content.Context;
//import android.content.Intent;
//import android.content.SharedPreferences;
//import android.graphics.Bitmap;
//import android.graphics.BitmapFactory;
//import android.graphics.Color;
//import android.os.AsyncTask;
//import android.os.Bundle;
//import android.os.Environment;
//import android.os.Handler;
//import android.os.Message;
//import android.provider.MediaStore;
//import android.text.SpannableString;
//import android.view.Menu;
//import android.view.MenuInflater;
//import android.view.MenuItem;
//import android.view.View;
//import android.view.View.OnClickListener;
//import android.view.ViewGroup;
//import android.view.ViewGroup.LayoutParams;
//import android.view.WindowManager;
//import android.view.animation.ScaleAnimation;
//import android.widget.ArrayAdapter;
//import android.widget.AutoCompleteTextView;
//import android.widget.ImageButton;
//import android.widget.ImageView;
//import android.widget.LinearLayout;
//import android.widget.RelativeLayout;
//import android.widget.ScrollView;
//import android.widget.Spinner;
//import android.widget.TextView;
//import android.widget.Toast;
//import android.widget.ToggleButton;
//
//import com.adins.mss.base.GlobalData;
//import com.adins.mss.base.R;
//import com.adins.mss.base.mainmenu.MainMenuActivity;
//import com.adins.mss.base.review.QuestionReviewGenerator;
//import com.adins.mss.base.todo.Task;
//import com.adins.mss.base.util.CustomAnimatorLayout;
//import com.adins.mss.base.util.GenericAsyncTask;
//import com.adins.mss.base.util.GenericAsyncTask.GenericTaskInterface;
//import com.adins.mss.base.util.GsonHelper;
//import com.adins.mss.constant.Global;
//import com.adins.mss.dao.LocationInfo;
//import com.adins.mss.dao.Lookup;
//import com.adins.mss.dao.QuestionSet;
//import com.adins.mss.dao.Scheme;
//import com.adins.mss.foundation.camera.Camera;
//import com.adins.mss.foundation.db.dataaccess.LookupDataAccess;
//import com.adins.mss.foundation.db.dataaccess.QuestionSetDataAccess;
//import com.adins.mss.foundation.db.dataaccess.SchemeDataAccess;
//import com.adins.mss.foundation.db.dataaccess.TaskHDataAccess;
//import com.adins.mss.foundation.dialog.DialogManager;
//import com.adins.mss.foundation.formatter.Formatter;
//import com.adins.mss.foundation.formatter.Tool;
//import com.adins.mss.foundation.image.Utils;
//import com.adins.mss.foundation.image.ViewImageActivity;
//import com.adins.mss.foundation.location.LocationTrackingManager;
//import com.adins.mss.foundation.location.UpdateMenuIcon;
//import com.adins.mss.foundation.questiongenerator.DateInputListener;
//import com.adins.mss.foundation.questiongenerator.DynamicQuestion;
//import com.adins.mss.foundation.questiongenerator.NotEqualSymbol;
//import com.adins.mss.foundation.questiongenerator.OptionAnswerBean;
//import com.adins.mss.foundation.questiongenerator.QuestionBean;
//import com.adins.mss.foundation.questiongenerator.QuestionViewGenerator;
//import com.adins.mss.foundation.questiongenerator.QuestionViewValidator;
//import com.adins.mss.foundation.questiongenerator.TimeInputListener;
//import com.adins.mss.foundation.questiongenerator.form.LabelFieldView;
//import com.adins.mss.foundation.questiongenerator.form.LocationTagingView;
//import com.adins.mss.foundation.questiongenerator.form.MultiOptionQuestionViewAbstract;
//import com.adins.mss.foundation.questiongenerator.form.QuestionView;
//import com.gadberry.utility.expression.Expression;
//import com.gadberry.utility.expression.OperatorSet;
//import com.google.gson.Gson;
//import com.google.gson.GsonBuilder;
//
//
//	//Glen 6 Aug 2014, implement RequestJSONFromSErverTask
//	//Glen 22/12/14
//	public class DynamicQuestionActivity extends DynamicQuestion implements OnClickListener, GenericTaskInterface {
//		private int mode;
//		public static SurveyHeaderBean header;
////		private TaskH header;
//		private LinearLayout questionContainer;
//		private static int questionSize = 0;
//		private static LinearLayout reviewContainer;
//		private static RelativeLayout searchContainer;
//		private static ScrollView scrollView;
//		private static ScrollView scrollView2;
//		private boolean isSimulasi =false;
//		private boolean isFinish =false;
//		public static LinkedHashMap<String, QuestionBean> listOfQuestion;
//		public static LinkedHashMap<Integer, String> listOfIdentifier;
//		private QuestionViewGenerator viewGenerator;
//		
//		//Glen 20 Oct 2014, TEMP, current page
//		private List<QuestionBean> currentPageBeans = new ArrayList<QuestionBean>();
//		private List<ViewGroup> currentPageViews = new ArrayList<ViewGroup>();
//
//		// Glen 6 Aug 2014, flag, wether preview need info from server or not
//		private boolean previewNeedServer = false;
//		//Glen 10 Oct 2014, create array to hold preview field, to check index when clicked
//		private List<LinearLayout> previewFields;
//		
//		//Glen 15 Oct 2014, add preview mode, where user can edit one question and get back to previewScreen without changing the field
//		private boolean inPreviewEditMode = false;
//		private QuestionBean edittedQuestion;
//		private boolean needQuickValidation = false;
//		
//		private boolean isSaveAndSending = false;
//		
//		// bong Oct 29th, 2014 - adding flag for button in screenContainer or in scrollContainer
//		private boolean isButtonInScroll = false;
//		
//		//use for calculation
//		private static JexlEngine jexlEngine;
//		
//		private DataForDynamicQuestion dfdq = new DataForDynamicQuestion();
//		/* BUTTON */
////		private Button btnBack;
////		private Button btnNext;
////		private Button btnReview;
////		private Button btnSend;
//		private ImageButton btnSearch;
//		private ImageButton btnBack;
//		private ImageButton btnNext;
//		private ImageButton btnSave;
//		private ImageButton btnSend;
//		private ImageButton btnVerified;
//		private ImageButton btnReject;
//		private ImageButton btnApprove;
//		private ImageButton btnClose;
//		private ToggleButton btnSearchBar;
//		private AutoCompleteTextView txtSearch;
//		
//		//task abstract class
//		private Task task;
//
//		/* STATIC REF */
////		private static TextView txtDetailInFocus;
////		private static TextView txtInFocus;
////		private static ImageView thumbInFocus;
////		private static QuestionBean questionInFocus;
//		public static DynamicSurveyHandler handler;
//		public static SpannableString deletePhoto;
//		
//		public static boolean isApproval = false;
//		public static boolean isVerified= false;
//		public static boolean allowImageEdit= true;
//		// bong Oct20th, 2014 - create FormOpenHelper object for QuestionViewValidator
////		public FormOpenHelper formOpenHelper() {
////			FormOpenHelper formOpenHelper = new FormOpenHelper(getApplicationContext());
////			return formOpenHelper;
////		}
//
//		//GIGIN 21/1/2014
//		ArrayList<QuestionBean> visibleQuestion= new ArrayList<QuestionBean>();
//		ArrayList<String> questionLabel= new ArrayList<String>();
//		ArrayAdapter<String> adapter ;
//		private QuestionReviewGenerator reviewGenerator;
////		private String form_type;
//		
//		@Override
//		protected void onCreate(Bundle savedInstanceState) {
//			super.onCreate(savedInstanceState);
//			setContentView(R.layout.dynamic_form);
//			this.getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
//			overridePendingTransition(R.anim.activity_open_translate,R.anim.activity_close_scale);
//			getActionBar().setDisplayHomeAsUpEnabled(true);
//			ACRA.getErrorReporter().putCustomData("LAST_CLASS_ACCESSED", getClass().getSimpleName());
////	        getActionBar().setHomeButtonEnabled(true);
//			new AsyncTask<Void, Void, Void>() {
//
//				@Override
//				protected Void doInBackground(Void... params) {
//					List<Scheme> schemes = SchemeDataAccess.getAll(getApplicationContext());				
//					Global.TempScheme = new HashMap<String, Date>();
//					
//					for(Scheme scheme : schemes){
//						Global.TempScheme.put(scheme.getUuid_scheme(), scheme.getScheme_last_update());
//					}
//					
//					Global.SchemeIsChange = true;
//					return null;
//				}
//	         }.execute();
//	         
//			initialize();
//		}
//
//		
//		@Override
//		protected void onDestroy() {
//			super.onDestroy();			
//			Constant.listOfQuestion = null;
//			CustomerFragment.header=null;
//			//DynamicQuestionActivity.header = null;
//			DynamicQuestionActivity.idxQuestion=0;
//			DynamicQuestionActivity.idxPosition=0;
//			isApproval = false;
//			isVerified= false;
//			allowImageEdit = true;
//		}
//
//		public static String mCurrentPhotoPath;
//
//		private void deleteLatestPictureCreate() {
//			try {
//  			  getContentResolver().delete(MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
//  			          MediaStore.Images.Media.DATA
//  			              + "='"
//  			              + mCurrentPhotoPath
//  			              + "'", null);
//
//  			  File f = Environment.getExternalStoragePublicDirectory(
//  			            Environment.DIRECTORY_PICTURES);
//  			  File [] files = f.listFiles();
//  			  Arrays.sort( files, new Comparator()
//  			  {
//  			      public int compare(Object o1, Object o2) {
//
//  			          if (((File)o1).lastModified() > ((File)o2).lastModified()) {
//  			              return -1;
//  			          } else if (((File)o1).lastModified() < ((File)o2).lastModified()) {
//  			              return +1;
//  			          } else {
//  			              return 0;
//  			          }
//  			      }
//  			  }); 
//  			  files[0].delete();
//  			} catch (Exception e) {
//  			        e.printStackTrace();
//
//  			}
//		}
//		
//		@Override
//		public void onActivityResult(int requestCode, int resultCode, Intent result) {
//	    	super.onActivityResult(requestCode, resultCode, result);
//	    	if(requestCode == Utils.REQUEST_CAMERA && resultCode == Activity.RESULT_OK) {
//	    		try {
//	    			
//					File file = new File(mCurrentPhotoPath);
////					File filesave = new File(getFilesDir()+"/imgShoot");
////	    			Bitmap bmp_data = Utils.pathToBitmap(file);
//					int rotate = Utils.neededRotation(file);
//					
//					int quality = Utils.picQuality;
//					int thumbHeight= Utils.picHeight;
//					int thumbWidht= Utils.picWidth;
//					QuestionBean bean = DynamicQuestionActivity.getQuestionInFocus();
//					if(bean.getImg_quality().equalsIgnoreCase(Global.IMAGE_HQ)){
//						thumbHeight= 1240;
//						thumbWidht= 720;
//						quality = 90;
//					}					
//					
//					Bitmap bmp_data = Utils.decodeSampledBitmapFromResource(file, thumbWidht, thumbHeight);
////	    			Bundle extras=result.getExtras();
////	    			Bitmap bmp_data = (Bitmap)extras.get(MediaStore.EXTRA_OUTPUT);
//					System.gc();
//		    		byte[] _data = null ;
//		    		try {
//		    			_data = Utils.bitmapToByte(bmp_data, quality);
//					} catch (Exception e) {
//						// TODO: handle exception
//					}
//		    		if(_data!=null){
//		    			if(thumbHeight <=0 || thumbHeight >= 20000)
//		    				thumbHeight=640;
//		    			if(thumbWidht <=0 || thumbWidht >= 20000)
//		    				thumbWidht=480;
//		    				
//		    			_data=Camera.resizeImageWithWatermark(_data, rotate, thumbWidht, thumbHeight, quality, this);
//			    		
//			    		deleteLatestPictureCreate();
//			    		try {
//			    			if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.KITKAT) {
//			    				Utils.deleteLatestPicture();
//			    			}
//						} catch (Exception e) {
//							try {
//								String manufacture = android.os.Build.MANUFACTURER;
//								if(manufacture.contains("LGE")){
//									Utils.deleteLatestPictureLGE();
//								}
//							} catch (Exception e2) {
//								// TODO: handle exception
//							}
//						}
//			    		
//			    		
//						DynamicQuestionActivity.saveImage(_data);
//						
//						boolean getGPS = true;
//								
//						
//						LocationInfo locBean = null;
//						String indicatorGPS = "";
//						byte[] imgLocation = null;
//						boolean isGeoTagged = Global.AT_IMAGE_W_LOCATION.equals(bean.getAnswer_type());
//						boolean isGeoTaggedGPSOnly = Global.AT_IMAGE_W_GPS_ONLY.equals(bean.getAnswer_type());
//						if (isGeoTagged) {
//							LocationTrackingManager pm = Global.LTM;
//							if(pm!=null){
//							locBean = pm.getCurrentLocation();
//							LocationInfo2 infoFinal = new LocationInfo2(locBean);							
//							bean.setAnswer(LocationTrackingManager.toAnswerString_short(infoFinal));	
//							bean.setLocationInfo(infoFinal);
//							indicatorGPS =
//									bean.getAnswer();
//							}
////							try {
////								imgLocation = new GetLocationImage(getApplicationContext(), locBean).execute().get();
////								DynamicQuestionActivity.saveImageLocation(imgLocation);
////							} catch (Exception e) {
////							}
//						}
//						
//						if (isGeoTaggedGPSOnly) {
//							LocationTrackingManager pm = Global.LTM;
//							if(pm!=null){
//								locBean = pm.getCurrentLocation();
//								LocationInfo2 infoFinal = new LocationInfo2(locBean);								 
//								if(infoFinal.getLatitude().equals("0.0")||infoFinal.getLongitude().equals("0.0")){
//									
//									if(bean.isMandatory()){
//										bean.setLocationInfo(infoFinal);
//										String[] msg = {"Can't get GPS location"};
//										String alert2 = Tool.implode(msg, "\n");
//										Toast.makeText(this, alert2, Toast.LENGTH_LONG).show();
//										DynamicQuestionActivity.saveImage(null);
//										DynamicQuestionActivity.saveImageLocation(null);
//										getGPS = false;
//									}
//										
//								}else{
//									bean.setAnswer(LocationTrackingManager.toAnswerString_short(infoFinal));
//									bean.setLocationInfo(infoFinal);
//									indicatorGPS =
//											bean.getAnswer();
////									try {
////										imgLocation = new GetLocationImage(getApplicationContext(), locBean).execute().get();
////										DynamicQuestionActivity.saveImageLocation(imgLocation);
////									} catch (Exception e) {
////									}
//								}
//							}
//						}
//						
//						// set thumbnail
//						if (DynamicQuestionActivity.getThumbInFocus() != null && getGPS ) {
//							Bitmap bm = BitmapFactory.decodeByteArray(_data, 0, _data.length);
//							
//							int[] res = Tool.getThumbnailResolution(bm.getWidth(), bm.getHeight());
//							Bitmap thumbnail = Bitmap.createScaledBitmap(bm, res[0], res[1], true);
//							DynamicQuestionActivity.setThumbInFocusImage(thumbnail);
//							
//							if(isGeoTagged||isGeoTaggedGPSOnly){
//								try {
////									Bitmap bitmapLocation = BitmapFactory.decodeByteArray(imgLocation, 0, imgLocation.length);;
////									int[] res2 = Tool.getThumbnailResolution(bitmapLocation.getWidth(), bitmapLocation.getHeight());
//									Bitmap thumbLocation =BitmapFactory.decodeResource(getResources(), R.drawable.ic_absent); 
////											Bitmap.createScaledBitmap(bitmapLocation, res[0], res[1], true);
//									try {
//										DynamicQuestionActivity.setThumbLocationInfoImage(thumbLocation);
//									} catch (Exception e) {
//										DynamicQuestionActivity.setThumbLocationInfoImage(null);
//									}		
//								} catch (Exception e) {
////									System.out.println(e.getMessage());
//								}
//								DynamicQuestionActivity.setTxtDetailInFocus(bm.getWidth()+" x " +bm.getHeight()+
//										". Size "+bean.getImgAnswer().length +" Bytes\n"+indicatorGPS);
//							}else{
//								DynamicQuestionActivity.setTxtDetailInFocus(bm.getWidth()+" x " +bm.getHeight()+
//									". Size "+bean.getImgAnswer().length +" Bytes");
//							}
//							if(bm!=null)
//								bm.recycle();
//						}
//						if(bmp_data!=null){
//							bmp_data.recycle();
//							bmp_data=null;
//						}
//						System.gc();
//		    		}else{
//		    			Toast.makeText(getApplicationContext(), "Can't get image from camera, please try again", Toast.LENGTH_SHORT).show();
//		    		}
//	    		} catch (Exception e) {
//	    			Toast.makeText(getApplicationContext(), "Can't get image from camera, please try again", Toast.LENGTH_SHORT).show();
//				}	
//			}else if(resultCode == Global.REQUEST_LOCATIONTAGGING){
//				QuestionBean bean = DynamicQuestionActivity.getQuestionInFocus();
//				LocationInfo info = LocationTagingView.locationInfo;
//				LocationInfo2 infoFinal = new LocationInfo2(info);				
//				bean.setAnswer(LocationTrackingManager.toAnswerString_short(infoFinal));	
//				bean.setLocationInfo(infoFinal);
////		    	DynamicQuestion.setQuestionInFocus(bean.getAnswer());
////				DynamicQuestion.setTxtDetailInFocus(bean.getAnswer());
//				DynamicQuestionActivity.setTxtInFocusText(bean.getAnswer());
//				
//			}
//			else if(resultCode == Global.REQUEST_VOICE_NOTES){
//				byte[] voiceNotes = result.getByteArrayExtra(Global.BUND_KEY_DETAIL_DATA);				
//				if(voiceNotes!=null && voiceNotes.length>0){
//					header.setVoice_note(voiceNotes);
//				}
//			}
//		}
//		
//		private static int idxPosition = 0;
//		Timer myTimer;
//		private void initialize() {
//			Bundle extras = getIntent().getExtras();
//			mode = extras.getInt(Global.BUND_KEY_MODE_SURVEY);
////			header =(SurveyHeaderBean) extras.getSerializable(Global.BUND_KEY_SURVEY_BEAN);
//			header = CustomerFragment.header;
//			String uuid_taskH = extras.getString(Global.BUND_KEY_UUID_TASKH);
////			header = TaskHDataAccess.getOneHeader(getApplicationContext(), uuid_taskH);
//			task = (Task) extras.getSerializable(Global.BUND_KEY_TASK);
//			listOfQuestion =new LinkedHashMap<String, QuestionBean>();
//			listOfIdentifier = new LinkedHashMap<Integer, String>();
//			int i=0;
//			for(QuestionBean bean :Constant.listOfQuestion){
//					listOfQuestion.put(bean.getIdentifier_name(), bean);
//					listOfIdentifier.put(i, bean.getIdentifier_name());
//					i++;
//			}
//			
//			viewGenerator = new QuestionViewGenerator();
//			reviewGenerator = new QuestionReviewGenerator();
////			form_type = header.getScheme().getForm_type();
//			jexlEngine = new JexlEngine();
//			isSimulasi = extras.getBoolean(Global.BUND_KEY_MODE_SIMULASI, false);
//
//			//----------------
//			//---Gigin : save to temporary data
//			
//			/*dfdq.setSelectedHeader(header);
//			dfdq.setListOfQuestion(listOfQuestion);
//			dfdq.setSelectedForm(Constant.selectedForm);
//			dfdq.setMode(mode);
//			new SavingDynamicData().execute();*/
//			//SaveDataToTemporary(dfdq);
//			//-----------------
//			
//			// Glen 7 Aug 2014, set previewNeedServer Flag
//			if (Constant.selectedForm != null) {
//				this.previewNeedServer = Constant.selectedForm
//						.isPreviewServer();
//				// header.setPreviewNeedServer(previewNeedServer);
//			} else {
//				// this.previewNeedServer = header.previewNeedServer();
//				try {
//					this.previewNeedServer = Formatter.stringToBoolean(header.getIs_preview_server());
//				} catch (Exception e) {
//					// TODO: handle exception
//				}
//			}
//
//			try {
//				handler = new DynamicSurveyHandler();
//				if(header.getPriority()!=null&&header.getPriority().length()>0){
//					if(header.getStart_date()==null){
//						header.setStart_date(Tool.getSystemDateTime());
//						new CustomerFragment.SendOpenReadTaskH(getApplicationContext(),header).execute();
//					}
//		        }
//			} catch (Exception e) {
//				// ** 20 feb 2012
//				// kasus pada TBG
//				// start screen detail paging.setTotalRecord(listOfQuestion.size());
//				// sering mendapatkan null, list questions nya tidak terisi
//				// soo kalo eror dibaliin ke halaman sebelumnya dulu, suruh coba lg
//				// aja
//
//				// DialogManager.showAlert(DynamicSurveyActivity.this,
//				// DialogManager.TYPE_ERROR,
//				// "Failed open questions, please try again");
//				String[] msg = { "Failed open questions,\nplease try again" };
//				String alert = Tool.implode(msg, "\n");
//				Toast.makeText(this, alert, Toast.LENGTH_SHORT).show();
//				try {
//					// plan B
////					Intent intent = new Intent(this, CustomerActivity.class);
////					extras = new Bundle();
////					extras.putInt(Global.BUND_KEY_MODE_SURVEY,
////							Global.MODE_SURVEY_TASK);
////					extras.putSerializable(Global.BUND_KEY_SURVEY_BEAN,
////							MainMenuActivity.selectedSurvey);
////					intent.putExtras(extras);
////					this.startActivity(intent);
////					this.finish();
//				} catch (Exception e2) {
//					// TODO : perlu perbaikan lagi plan C kalo plan B error,
////					Intent i = new Intent(this, SurveyListActivity.class);
////					this.startActivity(i);
////					this.finish();
//				}
//
//			}
//
//			initScreenLayout();
//
//			try {
//				if(TaskHDataAccess.STATUS_TASK_APPROVAL.equalsIgnoreCase(header.getStatus())
//						||TaskHDataAccess.STATUS_TASK_APPROVAL_DOWNLOAD.equalsIgnoreCase(header.getStatus())
//						||mode == Global.MODE_VIEW_SENT_SURVEY){
//					try {
//						allowImageEdit = false;
//						isApproval=true;
//						btnClose.setClickable(false);
//						isFinish = true;
//						if(mode!=Global.MODE_VIEW_SENT_SURVEY){
//							isVerified=true;
//						}
//						showFinishScreen();
////						while (isApproval) {
////							try {
////								showFinishScreen();
//////								doNext(true);
//////								if(isFinish)
//////									break;
////							} catch (Exception e) {
////								break;
////							}
////							
////						}
//					} catch (Exception e) {
//						loadDynamicForm();
//					}
//				}else if(TaskHDataAccess.STATUS_TASK_VERIFICATION.equalsIgnoreCase(header.getStatus())||
//						TaskHDataAccess.STATUS_TASK_VERIFICATION_DOWNLOAD.equalsIgnoreCase(header.getStatus())){
//					try{
//						allowImageEdit = false;
//						isVerified=true;
//						myTimer = new Timer();
//						
//						myTimer.schedule(new TimerTask() {
//	
//							@Override
//							public void run() {
//								// TODO Auto-generated method stub
//								runOnUiThread(new Runnable() {										
//									public void run() {
//										if (!doNext(true)) {
//											cancelTimer1();											
//											btnVerified.setClickable(false);
//											btnVerified.setImageResource(R.drawable.ic_verified_off);
//											btnReject.setClickable(false);
//											btnReject.setImageResource(R.drawable.ic_reject_off);											
//										}
//										if(isFinish)
//											cancelTimer1();
//									}
//								});
//							}
//						}, 0, 500);
//						
////						while(isVerified){
////							try {
////								if(!doNext(true)){
////									btnVerified.setClickable(false);
////									btnVerified.setImageResource(R.drawable.ic_verified_off);
////									btnReject.setClickable(false);
////									btnReject.setImageResource(R.drawable.ic_reject_off);
////									break;
////								}
////								if(isFinish)
////									break;
////							} catch (Exception e) {
////								// TODO: handle exception
////							}
////						}
//					}catch (Exception e) {
//						loadDynamicForm();
//					}
//				}
//				else if(TaskHDataAccess.STATUS_SEND_SAVEDRAFT.equals(header.getStatus())){
//					try {
//						//loadDraftData();
//						btnBack.setClickable(false);
//						btnNext.setClickable(false);
//						btnSend.setClickable(false);
//						btnSave.setClickable(false);
//						btnSearch.setClickable(false);
//						myTimer = new Timer();
//						
//						myTimer.schedule(new TimerTask() {
//	
//							@Override
//							public void run() {
//								// TODO Auto-generated method stub
//								runOnUiThread(new Runnable() {										
//									public void run() {
//										if (!doNext(true)) {
//											cancelTimer1();	
//											synchronized (this) {
//												this.notifyAll();
//											}											
//										}
//										
//										if (idxPosition >= header.getLast_saved_question()) {
//											cancelTimer1();	
//											synchronized (this) {
//												this.notifyAll();
//											}
//										}
//									}
//								});
//							}
//						}, 0, 500);
//						    
////						int i=0;
////						while(i<header.getLast_saved_question()){
////							if(!doNext(true))
////								break;	
////							i++;
////						}
//					} catch (Exception e) {
//						loadDynamicForm();
//					}
//										
//				}else{
//					loadDynamicForm();
//				}	
//			} catch (Exception e) {
//				loadDynamicForm();
//			}
//			
//		}
//		
//		private void SaveDataToTemporary(DataForDynamicQuestion dfdq2) {
//			// TODO Auto-generated method stub
//			Gson gson = new GsonBuilder().setDateFormat("ddMMyyyyHHmmss").registerTypeHierarchyAdapter(byte[].class,
//		            new GsonHelper.ByteArrayToBase64TypeAdapter()).create();			     
//	        try {	        	      		        	
//	        	String tempDynamicdata = gson.toJson(dfdq2);
//	        	
//	        	SharedPreferences sharedPref = this.getSharedPreferences(
//	    		        "TempDynamicData", Context.MODE_PRIVATE);	        	
//	            	SharedPreferences.Editor sharedPrefEditor = sharedPref.edit();
//	        		sharedPrefEditor.putString("SelectedDynamicData", tempDynamicdata);	        	  	
//	        		sharedPrefEditor.commit();	
//	            	
//			} catch (Exception e) {
//				System.out.println(e.getMessage());
//			}
//		}
//
//		/*public void RestoreDataToTemporary(Context context) {			
//	    	try {							
//		        if(listOfQuestion==null){
//		        	Gson gson = new GsonBuilder().setDateFormat("ddMMyyyyHHmmss").registerTypeHierarchyAdapter(byte[].class,
//				            new GsonHelper.ByteArrayToBase64TypeAdapter()).create();
//					SharedPreferences sharedPref = context.getSharedPreferences(
//					        "TempDynamicData", Context.MODE_PRIVATE);
//					String sTempDynamicdata = sharedPref.getString("SelectedDynamicData", "");
//					if(sTempDynamicdata!=null){
//			        	DataForDynamicQuestion tempDynamicdata = gson.fromJson(sTempDynamicdata, DataForDynamicQuestion.class);		        	
//			        	mode = tempDynamicdata.getMode();
//						header = tempDynamicdata.getSelectedHeader();
//						CustomerFragment.header =header; 									
//						listOfQuestion = tempDynamicdata.getListOfQuestion(); 
//						if(listOfQuestion == null) listOfQuestion = new ArrayList<QuestionBean>();
//						Constant.listOfQuestion =listOfQuestion;					
//						Constant.selectedForm = tempDynamicdata.getSelectedForm();						
//					}
//					new RestoreGlobalData().execute();
//		        }
//			} catch (Exception e) {
//			}
//		}*/
//
//		private class RestoreGlobalData extends AsyncTask<Void, Void, Void> {
//		     protected Void doInBackground(Void... urls) {
//		    	 MainMenuActivity.InitializeGlobalDataIfError(getApplicationContext());
//		         return null;
//		     }
//
//
//		     protected void onPostExecute(Void result) {
//		         
//		     }
//		 }
//		
//		private class SavingDynamicData extends AsyncTask<Void, Void, Void> {
//			protected Void doInBackground(Void... urls) {
//				SaveDataToTemporary(dfdq);
//				return null;
//			}
//	
//			protected void onPostExecute(Void result) {
//	
//			}
//		}
//		
//		public void loadDraftData() {		
//	        new AsyncTask<Void, Void, Void>() {
//	        	private ProgressDialog progressDialog;
//	            @Override
//	            protected void onPreExecute() {
//	                progressDialog = ProgressDialog.show(getApplicationContext(),
//	                        "", getString(R.string.progressWait), true);
//
//	            }
//	            @Override
//	            protected Void doInBackground(Void... params) {
//	                
//	                try {
//	                	int i=0;
//						while(i<header.getLast_saved_question()){
//							if(!doNext(true))
//								break;
//							i++;
//						}
//	                    return null;
//	                } catch (Exception e) {
//	                    e.printStackTrace();
//	                    return null;
//	                }
//	            }
//
//	            protected void onPostExecute() {
//	                super.onPostExecute(null);
//	                if (progressDialog!=null&&progressDialog.isShowing()){
//	                    try {
//	                        progressDialog.dismiss();
//	                    } catch (Exception e) {
//	                    }
//	                }	                
//	            }
//	        }.execute();
//	    }
//		
//		private void initScreenLayout() {
//			btnBack = (ImageButton)findViewById(R.id.btnBack);
//			btnNext = (ImageButton)findViewById(R.id.btnNext);
//			btnSend = (ImageButton)findViewById(R.id.btnSend);
//			btnSave = (ImageButton)findViewById(R.id.btnSave);
//			btnSearch = (ImageButton)findViewById(R.id.btnSearch);
//			btnVerified = (ImageButton)findViewById(R.id.btnVerified);
//			btnReject = (ImageButton)findViewById(R.id.btnReject);
//			btnApprove = (ImageButton)findViewById(R.id.btnApprove);
//			btnClose = (ImageButton)findViewById(R.id.btnClose);
//			btnSearchBar =(ToggleButton)findViewById(R.id.btnSearchBar);
//			
//			adapter = new ArrayAdapter<String>(this,  android.R.layout.simple_dropdown_item_1line, questionLabel);
//			txtSearch = (AutoCompleteTextView)findViewById(R.id.autoCompleteSearch);
//			txtSearch.setAdapter(adapter);
//			txtSearch.setOnFocusChangeListener(new View.OnFocusChangeListener() {
//				
//				@Override
//				public void onFocusChange(View arg0, boolean hasFocused) {
//					// TODO Auto-generated method stub
//					if(hasFocused){
//						adapter.notifyDataSetChanged();
//						txtSearch.setAdapter(adapter);
//					}
//				}
//			});
//			
//			btnBack.setOnClickListener(this);
//			btnNext.setOnClickListener(this);
//			btnVerified.setOnClickListener(this);
//			btnSave.setOnClickListener(this);
//			btnSearch.setOnClickListener(this);
//			btnReject.setOnClickListener(this);
//			btnApprove.setOnClickListener(this);
//			btnSearchBar.setOnClickListener(this);
//			btnClose.setOnClickListener(this);
//			
//			
//			questionContainer = (LinearLayout)findViewById(R.id.questionContainer);
//			reviewContainer = (LinearLayout)findViewById(R.id.reviewContainer);
//			scrollView = (ScrollView)findViewById(R.id.scrollContainer);
//			scrollView2 = (ScrollView)findViewById(R.id.scrollContainer2);
//			searchContainer = (RelativeLayout)findViewById(R.id.searchLayout);
//			searchContainer.setVisibility(View.GONE);
//			
//			LinearLayout sendLayout = (LinearLayout)findViewById(R.id.btnSendLayout);
//			LinearLayout verifyLayout = (LinearLayout)findViewById(R.id.btnVerifiedLayout);
//			LinearLayout rejectLayout = (LinearLayout)findViewById(R.id.btnRejectLayout);
//			LinearLayout approveLayout = (LinearLayout)findViewById(R.id.btnApproveLayout);
//			LinearLayout nextLayout = (LinearLayout)findViewById(R.id.btnNextLayout);
//			LinearLayout saveLayout = (LinearLayout)findViewById(R.id.btnSaveLayout);
//			LinearLayout searchLayout = (LinearLayout)findViewById(R.id.btnSearchLayout);
//			LinearLayout backLayout = (LinearLayout)findViewById(R.id.btnBackLayout);
//			LinearLayout closeLayout = (LinearLayout)findViewById(R.id.btnCloseLayout);
//
//			try {
//				if(TaskHDataAccess.STATUS_TASK_VERIFICATION.equalsIgnoreCase(header.getStatus())||
//						TaskHDataAccess.STATUS_TASK_VERIFICATION_DOWNLOAD.equalsIgnoreCase(header.getStatus())){
//					backLayout.setVisibility(View.GONE);
//					sendLayout.setVisibility(View.GONE);
//					saveLayout.setVisibility(View.GONE);
//					approveLayout.setVisibility(View.GONE);
//					//ganti ke halaman baaru 
//					if(!Global.NEW_FEATURE){
//						rejectLayout.setVisibility(View.VISIBLE);
//						verifyLayout.setVisibility(View.VISIBLE);	
//					}
//					
//				}
//				if(TaskHDataAccess.STATUS_TASK_APPROVAL.equalsIgnoreCase(header.getStatus())||
//						TaskHDataAccess.STATUS_TASK_APPROVAL_DOWNLOAD.equalsIgnoreCase(header.getStatus())){
//					backLayout.setVisibility(View.GONE);					
//					sendLayout.setVisibility(View.GONE);
//					searchLayout.setVisibility(View.GONE);
//					verifyLayout.setVisibility(View.GONE);
//					saveLayout.setVisibility(View.GONE);
//					if(!Global.NEW_FEATURE){
//						nextLayout.setVisibility(View.GONE);
//						rejectLayout.setVisibility(View.VISIBLE);
//						approveLayout.setVisibility(View.VISIBLE);
//					}
//					searchContainer.setVisibility(View.GONE);
//				}	
//			} catch (Exception e) {
//				// TODO: handle exception
//			}			
//			if(mode == Global.MODE_VIEW_SENT_SURVEY){
//				backLayout.setVisibility(View.GONE);
//				nextLayout.setVisibility(View.GONE);
//				sendLayout.setVisibility(View.GONE);
//				searchLayout.setVisibility(View.GONE);
//				verifyLayout.setVisibility(View.GONE);
//				rejectLayout.setVisibility(View.GONE);
//				approveLayout.setVisibility(View.GONE);
//				saveLayout.setVisibility(View.GONE);
//				closeLayout.setVisibility(View.VISIBLE);
//				searchContainer.setVisibility(View.GONE);
//			}
//			if(isSimulasi){
//				saveLayout.setVisibility(View.GONE);
//				sendLayout.setVisibility(View.GONE);
//			}
//		}
//
////		public static List<QuestionBean> getListOfQuestion() {
////			return listOfQuestion;
////		}
//
//		private void loadOptionsToView(MultiOptionQuestionViewAbstract view){
//			List<OptionAnswerBean> options = getOptionsForQuestion(view.getQuestionBean());
//			view.setOptions(this, options);
//		}
//		
//		private boolean loadDynamicForm() {
//			boolean isLastQuestion = true;
//
//			int start = -1;
//			int x=0;
//			if(listOfQuestion ==null){
//				try {
////					RestoreDataToTemporary(getApplicationContext());
//				} catch (Exception e) {
//			}
//			int end = listOfQuestion.size();
//			
//			try {
//				 x = questionContainer.getChildCount();
//				QuestionView questionView = (QuestionView) questionContainer.getChildAt(x-1);
//				 start = questionView.getSequence();
//			} catch (Exception e) {
//				System.out.println(e);
//			}					
//
//			start++; 
//			for ( ; start < end; start++) {	
//				QuestionBean bean = listOfQuestion.get(listOfIdentifier.get(start));
//				questionSize=start+1;
//				idxPosition++;
//				if (bean.isVisible()) {
//					String relevantExpression = bean.getRelevant_question();
//					if(relevantExpression==null) relevantExpression="";
//					if (isQuestVisibleIfRelevant(relevantExpression, bean)) {					
//						bean.setVisible(true);
////						QuestionView view = this.getQuestionView(bean, start + 1);
//						
//						/*for(int j = 0 ; j<questionSize;j++){
//							QuestionBean bean2 = listOfQuestion.get(j);
//							if(bean.getQuestion_id().equals(bean2.getQuestion_id()) && bean.getIdentifier_name().equals(bean2.getIdentifier_name())){
//								try {
//									if(!bean.getQuestion_group_id().equals(bean2.getQuestion_group_id()))
//										bean.setIs_readonly(Global.TRUE_STRING);
//								} catch (Exception e) {
//									// TODO: handle exception
//								}
//								if(Tool.isHaveLocation(bean2.getAnswer_type())){
//									bean.setLocationInfo(bean2.getLocationInfo());
//									bean.setLatitude(bean2.getLatitude());
//									bean.setLongitude(bean2.getLongitude());									
//								}
//								if(Tool.isOptions(bean2.getAnswer_type())){
//									bean.setSelectedOptionAnswers(bean2.getSelectedOptionAnswers());
//									bean.setOptionAnswers(bean2.getOptionAnswers());
//									bean2.setRelevanted(true);
//									break;
//								}else if(Tool.isImage(bean2.getAnswer_type())){
//									if(bean2.getImgAnswer()!=null)
//										bean.setImgAnswer(bean2.getImgAnswer());
//									bean.setAnswer(bean2.getAnswer());
//									bean2.setRelevanted(true);
//									break;
//								}
//								else{
//									bean.setAnswer(bean2.getAnswer());
//									bean2.setRelevanted(true);
//									break;
//								}
//							}
//						}*/
//						
//						if(null!=bean.getCalculate() && !"".equalsIgnoreCase(bean.getCalculate())){
//							String resultCalculate = doCalculate(bean);
//							bean.setAnswer(resultCalculate);	
//						}
//						
//						QuestionView view = this.getQuestionViewWithIcon(bean, start+1, R.drawable.ic_camera);						
//						try {
//							view.setSequence(start);
//						} catch (NullPointerException e) {
//							SharedPreferences sharedPref = this.getSharedPreferences(
//				    		        "TempDynamicData", Context.MODE_PRIVATE);
//							int tempStartPosition = sharedPref.getInt("StartPosition",0);	
//							start = tempStartPosition;						
//							view.setSequence(start);							
//						}
//												
//						view.setFocusableInTouchMode(true);
//						view.requestFocus();
//						
//						ScaleAnimation anim = new ScaleAnimation(0,1,0,1);
//				        anim.setDuration(200);
//				        anim.setFillAfter(true);
//						view.startAnimation(anim);
//						String answerType = bean.getAnswer_type();
//						
//						if(Tool.isOptions(answerType)){
//							if (bean.getOptionAnswers() == null || bean.getOptionAnswers().size() == 0 || bean.getOptionRelevances().length > 0){
//
//								MultiOptionQuestionViewAbstract multiOptionView = (MultiOptionQuestionViewAbstract) view;
//								loadOptionsToView(multiOptionView);
//								
//							}
//						}
//						
//						else if (Global.AT_CALCULATION.equals(answerType)){
//							setCalculationResult(bean);
//							((LabelFieldView) view).updateValue();
//						}
//						
//						
//						
//		
//						questionContainer.addView(view, LayoutParams.FILL_PARENT,
//								LayoutParams.WRAP_CONTENT);
//						view.getChildAt(1).requestFocus();
//						isLastQuestion = false;
//						
//						currentPageBeans.add(bean);
//						currentPageViews.add(view);
//						visibleQuestion.add(bean);
//						questionLabel.add(bean.getQuestion_label());
//						adapter.notifyDataSetChanged();
//						break;
//					} else {
//						bean.setVisible(false);
////						isLastQuestion = false;
//						if(questionLabel.size()>start)
//							questionLabel.remove(questionLabel.size()-1);
//						adapter.notifyDataSetChanged();
////						break;
//					}	
////					questionSize++;
////					visibleQuestion.addLast(bean);
//					
////					adapter.notifyDataSetChanged();
////					break;
//				}
//				else{
//					if (start == 0){
//						doNext(false);
//					}
//					else{
//						String relevantExpression = bean.getRelevant_question();
//						if(relevantExpression==null) relevantExpression="";
//						else if (isQuestVisibleIfRelevant(relevantExpression, bean)) {					
//							QuestionSet tempQuestion = QuestionSetDataAccess.getOne(getApplicationContext(), header.getUuid_scheme(), bean.getQuestion_id(), bean.getQuestion_group_id());
//							if(tempQuestion!=null){
//								if(tempQuestion.getIs_visible().equals(Global.TRUE_STRING)){
//									bean.setVisible(true);
//									
//									/*for(int j = 0 ; j<questionSize;j++){
//										QuestionBean bean2 = listOfQuestion.get(j);
//										if(bean.getQuestion_id().equals(bean2.getQuestion_id()) && bean.getIdentifier_name().equals(bean2.getIdentifier_name())){
//											try {
//												if(!bean.getQuestion_group_id().equals(bean2.getQuestion_group_id()))
//													bean.setIs_readonly(Global.TRUE_STRING);
//											} catch (Exception e) {
//												// TODO: handle exception
//											}
//											if(Tool.isHaveLocation(bean2.getAnswer_type())){
//												bean.setLocationInfo(bean2.getLocationInfo());
//												bean.setLatitude(bean2.getLatitude());
//												bean.setLongitude(bean2.getLongitude());									
//											}
//											if(Tool.isOptions(bean2.getAnswer_type())){
//												bean.setSelectedOptionAnswers(bean2.getSelectedOptionAnswers());
//												bean.setOptionAnswers(bean2.getOptionAnswers());
//												bean2.setRelevanted(true);
//												break;
//											}else if(Tool.isImage(bean2.getAnswer_type())){
//												if(bean2.getImgAnswer()!=null)
//													bean.setImgAnswer(bean2.getImgAnswer());
//												bean.setAnswer(bean2.getAnswer());
//												bean2.setRelevanted(true);
//												break;
//											}
//											else{
//												bean.setAnswer(bean2.getAnswer());
//												bean2.setRelevanted(true);
//												break;
//											}
//										}
//									}*/
////									QuestionView view = this.getQuestionView(bean, start + 1);
//									QuestionView view = this.getQuestionViewWithIcon(bean, start+1, R.drawable.ic_camera);
//									view.setSequence(start);
//									
//									view.setFocusableInTouchMode(true);
//									view.requestFocus();
//									
//									ScaleAnimation anim = new ScaleAnimation(0,1,0,1);
//							        anim.setDuration(200);
//							        anim.setFillAfter(true);
//									view.startAnimation(anim);
//									String answerType = bean.getAnswer_type();
//									
//									if(Tool.isOptions(answerType)){
//										if (bean.getOptionAnswers() == null || bean.getOptionAnswers().size() == 0 || bean.getOptionRelevances().length > 0){
//
//											MultiOptionQuestionViewAbstract multiOptionView = (MultiOptionQuestionViewAbstract) view;
//											loadOptionsToView(multiOptionView);
//											
//										}
//									}
//									
//									else if (Global.AT_CALCULATION.equals(answerType)){
//										setCalculationResult(bean);
//										((LabelFieldView) view).updateValue();
//									}
//							
//									questionContainer.addView(view, LayoutParams.FILL_PARENT,
//											LayoutParams.WRAP_CONTENT);
//									view.getChildAt(1).requestFocus();
//									isLastQuestion = false;
//									
//									currentPageBeans.add(bean);
//									currentPageViews.add(view);
//									visibleQuestion.add(bean);
//									questionLabel.add(bean.getQuestion_label());
//									adapter.notifyDataSetChanged();
//									break;
//								}
//								else{
//									bean.setVisible(false);
//								}
//							}
//	/*						bean.setVisible(true);
////							QuestionView view = this.getQuestionView(bean, start + 1);
//							QuestionView view = this.getQuestionViewWithIcon(bean, start+1, R.drawable.ic_camera);
//							view.setSequence(start);
//							
//							view.setFocusableInTouchMode(true);
//							view.requestFocus();
//							
//							ScaleAnimation anim = new ScaleAnimation(0,1,0,1);
//					        anim.setDuration(200);
//					        anim.setFillAfter(true);
//							view.startAnimation(anim);
//							String answerType = bean.getAnswer_type();
//							
//							if(Tool.isOptions(answerType)){
//								if (bean.getOptionAnswers() == null || bean.getOptionAnswers().size() == 0 || bean.getOptionRelevances().length > 0){
//
//									MultiOptionQuestionViewAbstract multiOptionView = (MultiOptionQuestionViewAbstract) view;
//									loadOptionsToView(multiOptionView);
//									
//								}
//							}
//							
//							else if (Global.AT_CALCULATION.equals(answerType)){
//								setCalculationResult(bean);
//								((LabelFieldView) view).updateValue();
//							}
//							
//							
//		
//							
//							
//			
//							questionContainer.addView(view, LayoutParams.FILL_PARENT,
//									LayoutParams.WRAP_CONTENT);
//							view.getChildAt(1).requestFocus();
//							isLastQuestion = false;
//							
//							currentPageBeans.add(bean);
//							currentPageViews.add(view);
//							visibleQuestion.add(bean);
//							questionLabel.add(bean.getQuestion_label());
//							adapter.notifyDataSetChanged();
//							break;*/
//							
//							
//						}
//					}
//					
//					
////					break;
//				}
//				if(end==start){
//					//last question
//					isLastQuestion=true;
//				}
//				System.gc();
//			}
//			return isLastQuestion;
//		}
//		
//		private boolean loadBackDynamicForm() {
//			boolean isCurrentPage = true;
//				
//			final int pos  = questionContainer.getChildCount()-1;
//			final View minView = questionContainer.getChildAt(pos-1);
//			final View view = questionContainer.getChildAt(pos);
//			ScaleAnimation anim = new ScaleAnimation(1,0,1,0);
//		 	anim.setDuration(200);
//		 	anim.setFillAfter(true);
//		 	view.startAnimation(anim);
////			Handler handler = new Handler(); 
////			handler.postDelayed(new Runnable() { 
////				public void run() { 
//			    	questionContainer.removeViewAt(pos);
//			    	if(visibleQuestion.size()>0)
//			    		visibleQuestion.remove(visibleQuestion.size()-1);
//			    	if(questionLabel.size()>0)
//			    		questionLabel.remove(questionLabel.size()-1);
//			    	adapter.notifyDataSetChanged();
//			    	questionSize--;
//			    	minView.setFocusableInTouchMode(true);
//					minView.requestFocus();
//					QuestionView questionView  =  (QuestionView) questionContainer.getChildAt(questionContainer.getChildCount()-1);
//					questionView.getChildAt(1).requestFocus();
////			    } 
////			}, 205); 
//			return isCurrentPage;
//		}
//		
//		private String doCalculate(QuestionBean bean){
//			String formula =  bean.getCalculate();
//			String expression = formula;
//			String total = "0";
//			
//			/*expression= "" +
//					"" +
//					"var " +
//					"for(QuestionBean bean : listOfQuestion) {bean }";*/
//			
//			/*expression=	
//			//"var hargaUnit = 0;" +
//			"var hargaKaroseri = 0;" +
//			"var totalHutang = 0;" +			
//			"for( qBean : listOfQuestion ) " +
//			"{" +				
//			"	if('HARGAKAROSERI'.equals(qBean.identifier_name))" +
//			"	{" +
//			"		hargaKaroseri = qBean.answer;" +
//		//	"total=total+qBean.answer;"+
//			"	}" +
//			//"system:println('hargaKaroseri'+hargaKaroseri) "+
//			"	if('HARGAUNIT'.equals(qBean.identifier_name))" +
//			"	{" +
//			"		hargaUnit = qBean.answer;" +
//		//	"total=total+qBean.answer;"+
//			"	} " +
//			//"hargaUnit=hargaUnit+1;"+
//			//"total=total+2;"+
//			
//			//"system:println('hargaUnit'+hargaUnit) "+
//			"}"+
//			"total=hargaKaroseri+hargaUnit;"+
//		//	"totalHutang = hargaKaroseri + hargaUnit;" +
//			//"system:println('totalHutang'+hargaUnit) "+
//			//"total = hargaKaroseri + hargaUnit;" + 
//			//"bean.setAnswer(5000000);" +
//			//"system:println('getAnswer'+bean.getAnswer) "
//			"";*/
//				
//				
//			
//			
//			
//			
//			JexlContext context = new MapContext();
//			//Person somePerson = new Person("488572222", "Sam", new Address("jspx st", "SF", 32), 2);
//			
//			context.set("listOfQuestion", listOfQuestion);
//			context.set("qBean", new QuestionBean(bean));
//			context.set("bean", bean);
////			context.set("isOption", Tool.isOptions("qBean.answer_type"));
////			context.set("isOption", "com.adins.mss.foundation.formatter.Tool.isOptions(qBean.answer_type)");
//			context.set("result", total);
//				
////			if(expression.contains("ORD_TENOR_var"))
////			expression = "var ORD_TENOR_var = 0; var ORD_PAY_FREQ_var = 0; for ( qBean : listOfQuestion ) { 	if('ORD_TENOR'.equalsIgnoreCase(qBean.identifier_name)) {  		if(qBean.answer_type.equalsIgnoreCase('009') || qBean.answer_type.equalsIgnoreCase('010') || qBean.answer_type.equalsIgnoreCase('011') || qBean.answer_type.equalsIgnoreCase('012')){	ORD_TENOR_var = qBean.selectedOptionAnswers[0].code; 		}else{	ORD_TENOR_var = qBean.answer;  		} 	}  	if('ORD_PAY_FREQ'.equalsIgnoreCase(qBean.identifier_name)) {	if(qBean.answer_type.equalsIgnoreCase('009') || qBean.answer_type.equalsIgnoreCase('010') || qBean.answer_type.equalsIgnoreCase('011') || qBean.answer_type.equalsIgnoreCase('012')){	ORD_PAY_FREQ_var = qBean.selectedOptionAnswers[0].code; 				}else{ 			ORD_PAY_FREQ_var = qBean.answer;  		} 	}  }   /*start*/ result= /*$*/ ORD_TENOR_var /*$*/ / /*$*/ ORD_PAY_FREQ_var /*$*/  /*end*/ ";
////			else
////			expression = "var ORD_OTR_var = 0; for ( qBean : listOfQuestion ) {  if('ORD_OTR'.equalsIgnoreCase(qBean.identifier_name)) {  if(qBean.answer_type.equalsIgnoreCase('009') || qBean.answer_type.equalsIgnoreCase('010') || qBean.answer_type.equalsIgnoreCase('011') || qBean.answer_type.equalsIgnoreCase('012')) { ORD_OTR_var = qBean.selectedOptionAnswers[0].code; } else { ORD_OTR_var = qBean.answer; } } }  /*start*/ result= /*$*/ ORD_OTR_var /*$*/ -0.3 /*end*/ ";
//			
//			Object value = jexlEngine.createScript(expression).execute(context);			
//			
//			if(value!=null){
//				try {
//					return String.format(Locale.US, "%.2f", value);
//				} catch (Exception e) {
//					return value.toString();
//				}					
//			}else{
//				return "0";
//			}
//				
//			
//		}
//		
//		public boolean isContaininUI(String qId){
//			boolean result = false;
//			
//			for (int i = 0; i < questionContainer.getChildCount(); i++) {
//				
//				QuestionView questionView = (QuestionView ) questionContainer.getChildAt(i);
//				
//				if(qId.equalsIgnoreCase(questionView.getQuestionId())){
//					result  = true;
//					break;
//				}
//			}
//			return result;
//		}
//		
//		// Glen 28 Aug 2014, method to get qbean of certain questionId
//		/*protected QuestionBean getQuestionBeanForId(String idName) {
//			for (QuestionBean bean : listOfQuestion) {
//				if (bean.getIdentifier_name().equals(idName)) {
//					return bean;
//				}
//			}
//			return null;
//		}*/
//		
//		// Glen 17 Oct 2014
//		protected QuestionBean getQuestionBeanForIdentifier(String identifier) {
//			QuestionBean bean = listOfQuestion.get(identifier);
//			return bean;			
//		}
//		
//		//Glen 15 Oct 2014, method to replace string with variable from application
//		protected String getReplacementForKey(String key){
//			String stringReplacement = "";
//			if (QuestionBean.PLACEMENT_KEY_BRANCH.equals(key)){
////				filter = ;
//			}
//			else if (QuestionBean.PLACEMENT_KEY_USER.equals(key)){
////				filter = ;
//			}
//			return stringReplacement;
//		}
//		
//		protected String replaceModifiers(String sourceString){
//			String newString = new String(sourceString);
//			//replace branch modifier
//			String branch = GlobalData.getSharedGlobalData().getUser().getBranch_id();
//			newString.replace(QuestionBean.PLACEMENT_KEY_BRANCH, branch);
//			//replace user modifier
//			String user = GlobalData.getSharedGlobalData().getUser().getUuid_user();
//			newString.replace(QuestionBean.PLACEMENT_KEY_USER, user);
//			return newString;
//		}
//		
//		//Glen 28 Aug 2014, method to load dropdown option from database
//		protected List<OptionAnswerBean> getOptionsForQuestion(QuestionBean bean){
//
//			//Gigin, validasi for Choice Filter
//			
//			List<List<OptionAnswerBean>> selectedAnswer=new ArrayList<List<OptionAnswerBean>>();
//			List<String> textAnswer = new ArrayList<String>();
//			List<String> filters =new ArrayList<String>();
//			if(bean.getChoice_filter()!=null){
//				String[] tempfilters = Tool.split(bean.getChoice_filter(), Global.DELIMETER_DATA3);
//				
//				for(String newFilter : tempfilters){
//					int idxOfOpenBrace = newFilter.indexOf('{');
//					if (idxOfOpenBrace != -1){					//there's {, prepare to replace what inside the {}
//						int idxOfCloseBrace = newFilter.indexOf('}');
//						String tempIdentifier = newFilter.substring(idxOfOpenBrace+1, idxOfCloseBrace);
//						QuestionBean bean2 = listOfQuestion.get(tempIdentifier);
//							if(bean2!=null){
//								if(Global.AT_TEXT_WITH_SUGGESTION.equals(bean2.getAnswer_type())){
//									textAnswer.add(bean2.getAnswer());
//								}
//								else{
//									selectedAnswer.add(bean2.getSelectedOptionAnswers());
//								}
//								bean2.setRelevanted(true);
//							}						
//					}
//				}
//				if(selectedAnswer!=null && selectedAnswer.size()>0){
//					for(List<OptionAnswerBean> optionbeans: selectedAnswer){
//						for(OptionAnswerBean answerBean : optionbeans){
//							filters.add(answerBean.getCode());
//						}
//					}					
//				}else if(textAnswer!=null && textAnswer.size()>0){
//					for(String answer : textAnswer){
//						filters.add(answer);
//					}
//				}
//			}
//			
//			List<OptionAnswerBean> optionAnswers =new ArrayList<OptionAnswerBean>();
//			if(filters.size()>0){
//				if(filters.size()==1){
//					List<Lookup> nLookups = LookupDataAccess.getAllByFilter(getApplicationContext(), bean.getLov_group(), filters.get(0));				
//					optionAnswers = OptionAnswerBean.getOptionList(nLookups);	
//				}else if(filters.size()==2){
//					List<Lookup> nLookups = LookupDataAccess.getAllByFilter(getApplicationContext(), bean.getLov_group(), filters.get(0), filters.get(1));				
//					optionAnswers = OptionAnswerBean.getOptionList(nLookups);
//				}else if(filters.size()==3){
//					List<Lookup> nLookups = LookupDataAccess.getAllByFilter(getApplicationContext(), bean.getLov_group(), filters.get(0), filters.get(1), filters.get(2));				
//					optionAnswers = OptionAnswerBean.getOptionList(nLookups);
//				}else if(filters.size()==4){
//					List<Lookup> nLookups = LookupDataAccess.getAllByFilter(getApplicationContext(), bean.getLov_group(), filters.get(0), filters.get(1), filters.get(2), filters.get(3));				
//					optionAnswers = OptionAnswerBean.getOptionList(nLookups);
//				}else if(filters.size()==5){
//					List<Lookup> nLookups = LookupDataAccess.getAllByFilter(getApplicationContext(), bean.getLov_group(), filters.get(0), filters.get(1), filters.get(2), filters.get(3), filters.get(4));				
//					optionAnswers = OptionAnswerBean.getOptionList(nLookups);
//				}
//				
//			}
//			else{
//				if(bean.getChoice_filter()!=null && bean.getChoice_filter().length()>0){
//					List<Lookup> lookups = new ArrayList<Lookup>();
//					optionAnswers = OptionAnswerBean.getOptionList(lookups);
//				}else{
//					List<Lookup> lookups = LookupDataAccess.getAllByLovGroup(getApplicationContext(), bean.getLov_group());
//					optionAnswers = OptionAnswerBean.getOptionList(lookups);
//				}
//			}
//			return optionAnswers;
//		}
//		
//		public List<String> extractIdentifierFromString(String rawString){
//			List<String> extractedIdentifiers = new ArrayList<String>();
//			boolean needExtract = true;
//			
//			while (needExtract){
//				
//				int idxOfOpenBrace = rawString.indexOf('{');
//				if (idxOfOpenBrace != -1){					//there's {, prepare to extract what is inside the {}
//					int idxOfCloseBrace = rawString.indexOf('}');
//					String identifier = rawString.substring(idxOfOpenBrace+1, idxOfCloseBrace);
//					if (identifier != null) extractedIdentifiers.add(identifier);
//					rawString = rawString.substring(idxOfCloseBrace+1);			//cut extracted part
//				}
//				//no more extracting needed
//				else{
//					needExtract = false;
//				}
//				
//			}
//			return extractedIdentifiers;
//		}
//		
//		public boolean isQuestVisibleIfRelevant(String relevantExpression, QuestionBean question){
//			boolean result = false;
//			String convertedExpression = new String(relevantExpression);		//make a copy of
//			if (convertedExpression == null || convertedExpression.length() == 0){
//				return true;
//			}
//			else{
//				
//				//TODO, use extractIdentifierFromString next time to simplify
//				boolean needReplacing = true;
//				while (needReplacing){
//					
//					//replace application modifier
//					convertedExpression = replaceModifiers(convertedExpression);
//					
//					int idxOfOpenBrace = convertedExpression.indexOf('{');
//					if (idxOfOpenBrace != -1){					//there's {, prepare to replace what inside the {}
//						int idxOfCloseBrace = convertedExpression.indexOf('}');
//						String identifier = convertedExpression.substring(idxOfOpenBrace+1, idxOfCloseBrace);
//						
//						QuestionBean bean = listOfQuestion.get(identifier);
//						
//						if (bean != null){
//
//							//Glen 21 Oct 2014, if it relate to question which is not visible, make it not visible too
//							if (bean.getIs_visible().equals(Global.FALSE_STRING)) return false;
//							
//							String flatAnswer = QuestionBean.getAnswer(bean);
//							
//							if(Tool.isOptions(bean.getAnswer_type())){
//								try {
//									flatAnswer = bean.getSelectedOptionAnswers().get(0).getCode();
//								} catch (Exception e) {
//									// TODO: handle exception
//								}						
//							}
//							
//							if (flatAnswer != null && flatAnswer.length() > 0){
//								//Glen 22 Oct 2014, enable multi-depth checking for 'multiple' question
//								//NOTE: though it's possible to just iterate on flatAnswer substrings, we prefer to stay on method if size is 1
//								String answers[] = Tool.split(flatAnswer, Global.DELIMETER_DATA);
//								if (answers.length == 1){
////									convertedExpression = convertedExpression.replace("{"+identifier+"}", flatAnswer);
//									convertedExpression = convertedExpression.replace("{"+identifier+"}", answers[0]);
//								}
//								else{
//									//NOTE: going into in-depth loop, won't go outside of this 'else'
//									for (int i = 0; i < answers.length; i++){
//										String convertedSubExpression = convertedExpression.replace("{"+identifier+"}", answers[i]);
//										boolean isVisible = isQuestVisibleIfRelevant(convertedSubExpression, question);
//										if (isVisible){
//											return true;
//										}
//									}
//									return false;
//								}
//								
//								//Glen 16 Oct 2014, added as affected bean visibility
////								bean.addToAffectedQuestionBeans(question);
//								bean.addToAffectedQuestionBeanVisibility(question);
//							}
//							else{			//if there's no answer, just hide the question
//								return false;
//							}
//						}
//						else{
////							convertedExpression.replaceAll("{"+identifier+"}", "");
//							convertedExpression = convertedExpression.replace("{"+identifier+"}", "\"\"");
//						}
//					}
//					//moved up
////					else if (convertedExpression.indexOf('r') != -1){
////						convertedExpression = replaceModifiers(convertedExpression);
////					}
//					
//					//no more replacing needed
//					else{
//						needReplacing = false;
//					}
//					
//				}
//				try {
//					OperatorSet opSet = OperatorSet.getStandardOperatorSet();
//					opSet.addOperator("!=", NotEqualSymbol.class);										 									 
//					Expression exp = new Expression(convertedExpression);
//					exp.setOperatorSet(opSet);
//					result = exp.evaluate().toBoolean();					
//					return result;
//				} catch (Exception e) {
//					e.printStackTrace();
//					return false;
//				}
//				
//			}
////			return result;
//		}
//		
//		//Glen 17 Oct 2014
//		private double getCalculationResult(QuestionBean bean){
//			String expression = bean.getCalculate();
//			if (expression == null || expression.length() == 0) return 0;
//			
//			String convertedExpression = new String(expression);
//			
//			List<String> identifiers = extractIdentifierFromString(expression);
//			for (String identifier : identifiers){
//				QuestionBean lookupBean = getQuestionBeanForIdentifier(identifier);
//				String answer = QuestionBean.getAnswer(lookupBean);
//				
//				if (answer == null || answer.length() == 0) return 0;
//				
//				convertedExpression = convertedExpression.replace("{"+identifier+"}", String.valueOf(answer));
//				
////				bean.addToAffectedQuestionBeanCalculation(lookupBean);
//				lookupBean.addToAffectedQuestionBeanCalculation(bean);
//			}
//			
//			try {
//				double result = Expression.evaluate(convertedExpression).toDouble();
//				return result;
//			} catch (Exception e) {
//				e.printStackTrace();
//				return 0;
//			}
//			
//		}
//		
//		private double setCalculationResult(QuestionBean bean){
//			double result = getCalculationResult(bean);
//	   		bean.setAnswer(String.valueOf(result));
//			return result;
//		}
//		
//		//Glen 10 Oct 2014, new question relevant with jexel
//		//Glen 17 Oct 2014, use isQuestVisibleIfRelevant
////		public boolean isQuestVisible2(List<String> questRelevant) {
////			boolean result =  false;
////			if(questRelevant!=null){
////			for (String stringRelevant : questRelevant) {				
//////						String relevantQuestion
////					int idxOfOpenBrace = stringRelevant.indexOf('{');
////					if (idxOfOpenBrace != -1){
////						int idxOfCloseBrace = stringRelevant.indexOf('}');
////						String identifier = stringRelevant.substring(idxOfOpenBrace+1, idxOfCloseBrace-1);
////						
////						QuestionBean bean = Tool.getBeanWithIdentifier(identifier, listOfQuestion);
////						String flatAnswer = bean.getAnswer();
////						
//////						List<String> answers = new ArrayList<String>();
////						
//////						if (flatAnswer.indexOf(Global.DELIMETER_DATA) > -1){	//artinya pertanyaan yg jawabanya multiple
//////							
//////						}else{
//////							answers.add(flatAnswer);
//////						}
////						
//////						for (String answer : answers){
//////							String translatedExpression = stringRelevant.replaceFirst("{(\\S+)}", answer);
//////							try {
//////								result = Expression.evaluate(translatedExpression).toBoolean();
//////							} catch (ArgumentCastException e) {
//////								e.printStackTrace();
//////							} catch (InvalidExpressionException e) {
//////								e.printStackTrace();
//////							}
//////						}	
////					} else{
////						//TODO add logic for predefined 
////					}
////				if(result){
////					break;
////				}
////			}		
////			}else{
////				// berarti gak ada dependensi nya,jadi langsung ditampilkan
////				return true;
////			}
////			return result;
////		}
//
//		private void clearListView() {
//			int childCount = questionContainer.getChildCount();
//			if (childCount > 0)
//				questionContainer.removeViews(0, childCount);
//			
//			//Glen 20 Oct 2014
//			currentPageBeans.clear();
//			currentPageViews.clear();
//			
//		}
//		
//		private QuestionView getQuestionView(QuestionBean qBean, int number) {
//			QuestionView linear = null;
//			try {
//				
//				//TODO bangkit seharusnya ada handling nya lagi 
//				linear = viewGenerator.generateByAnswerType(this, qBean, number, 0, null, null);
//			}
//			catch (Exception ex) {
//				ex.printStackTrace();
//			}
//			return linear;
//		}
//		
//		private QuestionView getQuestionViewWithIcon(QuestionBean qBean, int number,int resIdIcon) {
//			QuestionView linear = null;
//			try {
//				//TODO bangkit seharusnya ada handling nya lagi 
//				linear = viewGenerator.generateByAnswerType(this, qBean, number, resIdIcon, ViewImageActivity.class, Camera.class);
//			}
//			catch (Exception ex) {
//				ex.printStackTrace();
//			}
//			return linear;
//		}
//
//		@Override
//		protected void onResume() {
//
//			super.onResume();
//			if(Global.isVerifiedByUser){
//				Global.isVerifiedByUser = false;
//				this.finish();
//			}
//			else{
//				/*try {
//					RestoreDataToTemporary(getApplicationContext());
//				//TODO BANGKIT	DialogManager.showGPSAlert(this);
//				} catch (Exception e) {
//					if(listOfQuestion == null) listOfQuestion = new ArrayList<QuestionBean>();
//				}*/
//			}
//		}
//
//		public void onClick(View v) {
//			int id = v.getId();
//			try {
//				cancelTimer1();
//				cancelTimer2();
//			} catch (Exception e) {
//				// TODO: handle exception
//			}
//			if (id == R.id.btnBack) {
//				this.doBack();
//			}
//			else if (id == R.id.btnNext) {
//				//Glen 15 Oct 2014, added new param
////				this.doNext();				
//				if(isFinish && isVerified){
//					if(Global.NEW_FEATURE){
//						if (needQuickValidation){
//							if (validateAllMandatory(true)){
//								//TODO : alihkan ke halaman verification Action
//								Intent intent = new Intent(getApplicationContext(), Global.VerificationActivityClass);
//								intent.putExtra(Global.BUND_KEY_UUID_TASKH, header.getUuid_task_h());
//								intent.putExtra(Global.BUND_KEY_MODE_SURVEY, mode);
//								if(isApproval)
//									intent.putExtra(Global.BUND_KEY_FORM_NAME, Global.APPROVAL_FLAG);
//								else
//									intent.putExtra(Global.BUND_KEY_FORM_NAME, Global.VERIFICATION_FLAG);
//								startActivity(intent);
//							}
//						}
//						else{
//							//TODO : alihkan ke halaman verification Action
//							Intent intent = new Intent(getApplicationContext(), Global.VerificationActivityClass);
//							intent.putExtra(Global.BUND_KEY_UUID_TASKH, header.getUuid_task_h());
//							intent.putExtra(Global.BUND_KEY_MODE_SURVEY, mode);
//							if(isApproval)
//								intent.putExtra(Global.BUND_KEY_FORM_NAME, Global.APPROVAL_FLAG);
//							else
//								intent.putExtra(Global.BUND_KEY_FORM_NAME, Global.VERIFICATION_FLAG);
//							startActivity(intent);
//						}	
//					}
//				}
//				else{
//					this.doNext(true);
//				}
//			}
//			else if (id == R.id.btnSend){				
//				doSend();
//				
//				//TODO: Gigin : ini hanya sample (not for use)
////				Bundle extras = new Bundle();
////				Intent intent = new Intent(getApplicationContext(), SendResultActivity.class);
////				
////				extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, false);
////				extras.putString(Global.BUND_KEY_SEND_RESULT, "Data berhasil dikirim");
////				extras.putString(Global.BUND_KEY_SEND_SIZE, "128");
////				extras.putString(Global.BUND_KEY_SEND_TIME, "5");
////				extras.putString(Global.BUND_KEY_TASK_ID, "t002");
////				extras.putBoolean(Global.BUND_KEY_TASK_IS_PRINTABLE, false);
////				intent.putExtras(extras);
////				startActivity(intent);				
////				this.finish();
//				
//				// bong Oct 14th, 2014 - deleting tasklistview from local when sending
//				
////				String taskId = svyMgr.saveSurvey(getApplicationContext(), mode, header,
////       					listOfQuestion);
////				String taskId = header.getUuid_task_h();
////				if (Global.IS_DEV) System.out.println("taskId = "+taskId);
////				TaskListViewOpenHelper taskListViewOpenHelper = new TaskListViewOpenHelper(getApplicationContext());
////				taskListViewOpenHelper.delete(ApplicationBean.getInstance().getUserId(), taskId);
////				
////				TaskHDataAccess.delete(this, header);
//			}
//			else if(id == R.id.btnSearchBar){
////				adapter = new ArrayAdapter<String>(this,  android.R.layout.simple_dropdown_item_1line, questionLabel);
////				txtSearch.setAdapter(adapter);
//				if(btnSearchBar.isChecked()){
////					ScaleAnimation anim = new ScaleAnimation(0,1,0,1);
////			        anim.setDuration(500);
////			        anim.setFillAfter(true);
//			        CustomAnimatorLayout animatorLayout = new CustomAnimatorLayout(0, 1, 0, 1, 500, searchContainer, false);
//			        searchContainer.setVisibility(View.VISIBLE);
//			        searchContainer.startAnimation(animatorLayout);
//				}else{
////			        ScaleAnimation anim = new ScaleAnimation(1,0,1,0);
////			        anim.setDuration(500);
////			        anim.setFillAfter(true);
////					searchContainer.setVisibility(View.GONE);
//					CustomAnimatorLayout animatorLayout = new CustomAnimatorLayout(1, 0, 1, 0, 500, searchContainer, true);
//					searchContainer.startAnimation(animatorLayout);
////					searchContainer.setVisibility(View.GONE);
//				}
//			}
//			else if(id == R.id.btnSearch){
////				ScaleAnimation anim = new ScaleAnimation(1,0,1,0);
//				btnSearchBar.setChecked(false);
////		        anim.setDuration(500);
////		        anim.setFillAfter(true);
//				CustomAnimatorLayout animatorLayout = new CustomAnimatorLayout(1, 0, 1, 0, 500, searchContainer, true);
////				searchContainer.setVisibility(View.GONE);
//				searchContainer.startAnimation(animatorLayout);
//				String searchKey="";
//	         	   if(txtSearch.getText().length()>0)
//	         		  searchKey = txtSearch.getText().toString().toLowerCase();   
//	         	  searchQuestion(searchKey, false);
//			}
//			else if(id == R.id.btnSave){
//				doSave();
//			}
//			else if(id==R.id.btnVerified){
//				doVerify();
//			}
//			else if(id==R.id.btnApprove){
//				doApprove();
//			}
//			else if(id==R.id.btnReject){
//				doReject();
//			}
//			else if(id==R.id.btnClose){
//				this.finish();
//			}
//			else if (previewFields.contains(v)){	
//				if(TaskHDataAccess.STATUS_TASK_APPROVAL.equalsIgnoreCase(header.getStatus())
//						||TaskHDataAccess.STATUS_TASK_APPROVAL_DOWNLOAD.equalsIgnoreCase(header.getStatus())
//						||mode == Global.MODE_VIEW_SENT_SURVEY){
//					
//				}else{
//					onClickInPreview(v);
//				}
//			}
//		}
//		
//		
//		private void doReject() {
//			// TODO Auto-generated method stub
//			boolean isApprovalTask = true;
//			btnReject.setEnabled(false);
//			if(TaskHDataAccess.STATUS_TASK_VERIFICATION.equals(header.getStatus())||
//					TaskHDataAccess.STATUS_TASK_VERIFICATION_DOWNLOAD.equals(header.getStatus())){
//				header.setIs_prepocessed(Global.FORM_TYPE_VERIFICATION);
//				isApprovalTask =false;
//			}			
//			if(TaskHDataAccess.STATUS_TASK_APPROVAL.equals(header.getStatus())||
//					TaskHDataAccess.STATUS_TASK_APPROVAL_DOWNLOAD.equals(header.getStatus())){
//				header.setIs_prepocessed(Global.FORM_TYPE_APPROVAL);
//				isApprovalTask =true;
//			}
//			new TaskManager().sendApprovalTask(this, header, Global.FLAG_FOR_REJECTEDTASK, isApprovalTask);
//		}
//
//
//		private void doApprove() {
//			// TODO Auto-generated method stub
//			boolean isApprovalTask = true;
//			btnApprove.setEnabled(false);
//			new TaskManager().sendApprovalTask(this, header, Global.FLAG_FOR_APPROVALTASK, isApprovalTask);
//		}
//
//
//		private void doVerify() {
//			btnVerified.setEnabled(false);
//			if (needQuickValidation){
//				if (validateAllMandatory(true)){
//					sendVerification();
//				}
//			}
//			else{
//				sendVerification();
//			}
//		}
//
//		private void sendVerification(){
//			header.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);
//			header.setIs_prepocessed(Global.FORM_TYPE_VERIFICATION);
////	    	new TaskManager().saveAndSendTask(this, mode, header, listOfQuestion);
//		}
//
//		private void onClickInPreview(Object v) {
//			idxPosition=0;
//			idxQuestion=0;
//				btnReject.setClickable(false);
//				btnReject.setImageResource(R.drawable.ic_reject_off);
//				int idx = previewFields.indexOf(v);
//				QuestionBean bean = visibleQuestion.get(idx);
//				doBack();
//				QuestionView questionView  =  (QuestionView) questionContainer.getChildAt(idx);				
//				if(idx>0)
//					questionContainer.getChildAt(idx-1).requestFocus();
//				else
//					questionContainer.getChildAt(idx).requestFocus();
//				
//				if(Global.AT_DROPDOWN.equals(bean.getAnswer_type())||
//						Global.AT_DROPDOWN_W_DESCRIPTION.equals(bean.getAnswer_type())){
//					Spinner spinner = (Spinner)questionView.getChildAt(1);
//					spinner.requestFocusFromTouch();
//				}else if(Tool.isImage(bean.getAnswer_type())){
//					
//				}else{					
//					questionView.getChildAt(1).requestFocus();
//				}
//			
//		}
//
//		protected void searchQuestion(String key, boolean needValidation) {
//			if (this.validateCurrentPage(needValidation)) {
//				int start = -1;
//				int end = listOfQuestion.size();
//				int idx = -1;
//				int page=0;
//				start++; 
//				for ( ; start < end; start++) {
//					QuestionBean bean = listOfQuestion.get(start);
//					
//					if (listOfQuestion.get(start).isVisible()) {
////						if (isQuestVisible(bean.getRelevant_question()) {
////							String relevantExpression = bean.getRelevant_question();
////							if (isQuestVisibleIfRelevant(relevantExpression, bean)) {
//								idx++;
//								String label = bean.getQuestion_label().toLowerCase();
//								if(label.indexOf(key)!=-1){
//									page = idx;
//									break;
//								}
////							}
////						}						
//					}
//				}
//				if (isFinish) {
//					isFinish = false;
//					ScaleAnimation anim = new ScaleAnimation(0,1,0,1);
//			        anim.setDuration(500);
//			        anim.setFillAfter(true);
//			        questionContainer.startAnimation(anim);
//					btnNext.setClickable(true);
//					btnNext.setImageResource(R.drawable.ic_next);
//					btnBack.setVisibility(View.VISIBLE);
//					btnSend.setClickable(false);
//					btnSend.setImageResource(R.drawable.ic_send_off);
//					if (visibleQuestion.size()==questionContainer.getChildCount()) {
////						if (visibleQuestion.size()==questionSize) {
//						scrollView2.setVisibility(View.GONE);
//						scrollView.setVisibility(View.VISIBLE);
//						int childCount = reviewContainer.getChildCount();
//						if (childCount > 0)
//							reviewContainer.removeViews(0, childCount);
//					}
//				}
//				txtSearch.setText("");
//				QuestionView questionView  =  (QuestionView) questionContainer.getChildAt(page);
//				
//				questionContainer.getChildAt(page).requestFocus();
//				
//				questionView.getChildAt(1).requestFocus();
//			}
//		}
//		
//		private void doSend() {
//			//Glen 22 Oct 2014, try to avoid double tap
//			btnSend.setEnabled(false);
////			btnVerified.setEnabled(false);
//			
//			//Glen 16 Oct 2014, validate all before sending, if edit preview happened before
//			if (needQuickValidation){
//				if (validateAllMandatory(true)){
////					if (Global.STATUS_SEND_INIT.equals(header.getSentStatus())) {
////						MainMenuActivity.notifCount--;
////						MainMenuActivity.notifHandler.sendEmptyMessage(0);
////					}
////					
////			    	header.setSentStatus(Global.STATUS_SEND_PENDING);
////			    	new SurveySaveTask(this, mode, header, listOfQuestion, true).execute();
//					saveAndSendSurvey();
//			//		return true;
//				}
//			}
//			else{
//				saveAndSendSurvey();
//			}
//			
//			//Glen 22 Oct 2014, try to avoid double tap
////			btnSend.setEnabled(true);
//			
//			if (TaskHDataAccess.STATUS_SEND_INIT.equals(header.getStatus())) {
//				Constant.notifCount--;
//				//TODO bangkit Constant.notifHandler.sendEmptyMessage(0);
//			}
//
//			header.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);
//			
//			//TODO : Buat Save TAsk ~GG
////			task.saveTask(this, mode, header, listOfQuestion, true);
//			
////			new SurveySaveTask(this, mode, header, listOfQuestion, true)
////					.execute();
//		}
//		
//		//Glen 17 Oct 2014 send method
//		//Glen 12/12/14 constant moced from Global to TaskHDataAccess
//		public synchronized void saveAndSendSurvey(){
//			if (isSaveAndSending == false){
//				isSaveAndSending = true;
//				if (TaskHDataAccess.STATUS_SEND_INIT.equals(header.getStatus())) {
//					Constant.notifCount--;
//					//TODO bangkit Constant.notifHandler.sendEmptyMessage(0);
//				}
//				
//		    	header.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);
//		    	//TODO bangkit 
////		    	new TaskManager().saveAndSendTask(this, mode, header, listOfQuestion);
//			}
//			
//		}
//
//		@Override
//		protected Dialog onCreateDialog(int id) {
//			Date sysdate = Tool.getSystemDateTime();
//			switch (id) {
//			case QuestionViewGenerator.TYPE_DATE:
//				String dt = Formatter.formatDate(sysdate, Global.DATE_STR_FORMAT);
//				String[] temp1 = dt.split("/");
//				int dayOfMonth = Integer.parseInt(temp1[0]);
//				int month = Integer.parseInt((temp1[1])) - 1;
//				int year = Integer.parseInt(temp1[2]);
//				DateInputListener dtListener = new DateInputListener();
//				return new DatePickerDialog(this, dtListener.getmDateSetListener(),
//						year, month, dayOfMonth);
//			case QuestionViewGenerator.TYPE_TIME:
//				String tm = Formatter.formatDate(sysdate, Global.TIME_STR_FORMAT);
//				String[] temp2 = tm.split(":");
//				int hourOfDay = Integer.parseInt(temp2[0]);
//				int minute = Integer.parseInt(temp2[1]);
//				TimeInputListener tmListener = new TimeInputListener();
//				return new TimePickerDialog(this, tmListener.getmTimeSetListener(),
//						hourOfDay, minute, true);
//			case QuestionViewGenerator.TYPE_DATE_TIME:
//				DialogManager.showDateTimePicker(DynamicQuestionActivity.this);
//				break;
//			}
//			return null;
//		}
//
//		private void doBack() {
//			
//			//Glen 15 Oct 2014, when in previewEditMode, gotoNext without validation
//			if (inPreviewEditMode){
//				//Glen 16 OCt 2014, changed to true, because we no longer use btnNext
//				doNext(true);
//				edittedQuestion = null;
//				inPreviewEditMode = false;
//				return;
//			}
//			
//			if (isFinish) {
//				isFinish = false;
//				ScaleAnimation anim = new ScaleAnimation(0,1,0,1);
//		        anim.setDuration(500);
//		        anim.setFillAfter(true);
//		        questionContainer.startAnimation(anim);
//				btnNext.setClickable(true);
//				btnNext.setImageResource(R.drawable.ic_next);
//				btnBack.setVisibility(View.VISIBLE);
//				btnSend.setClickable(false);
//				btnSend.setImageResource(R.drawable.ic_send_off);
//				btnVerified.setClickable(false);
//				btnVerified.setImageResource(R.drawable.ic_verified_off);
//				if (visibleQuestion.size()==questionContainer.getChildCount()) {
////					if (visibleQuestion.size()==questionSize) {
//					scrollView2.setVisibility(View.GONE);
//					scrollView.setVisibility(View.VISIBLE);
//					int childCount = reviewContainer.getChildCount();
//					if (childCount > 0)
//						reviewContainer.removeViews(0, childCount);
//				}
//				
//				QuestionView questionView  =  (QuestionView) questionContainer.getChildAt(questionContainer.getChildCount()-1);
//				
//				questionView.getChildAt(1).requestFocus();
////				for(int i=0; i<listOfQuestion.size();i++){
//////				for(int i=0; i<paging.getTotalPage();i++){
////					loadDynamicForm();
////				}
//			}
//			else {
//				if(questionContainer.getChildCount()==1)
//					DialogManager.showExitAlertQuestion(this, getString(R.string.alertExitSurvey));
//				else{
//					loadBackDynamicForm();
//				}
//			}
//		}
//		
//		//Glen 15 Oct 2014, new version for previewMode
//		private void updateAffectedBeanAnswer(QuestionBean bean){
//			
//			if (bean == null) return;
//			
//			List<QuestionBean> iteratedList;
//			
//			//empty answer for all affected bean
//			//Glen 16 Oct 2014, empty bean which option may be affected by the changes
//			//Glen 22 Oct 2014, copy database to prevent concurrentmodifyexception
////			for (QuestionBean affectedBean : bean.getAffectedQuestionBeans()){
////			for (QuestionBean affectedBean : bean.getAffectedQuestionBeanOptions()){
//			iteratedList = new ArrayList<QuestionBean>(bean.getAffectedQuestionBeanOptions());
//			for (QuestionBean affectedBean : iteratedList){
//				affectedBean.setAnswer(null);
//				affectedBean.setLovCode(null);
//				affected
//				updateAffectedBeanAnswer(affectedBean);				//loop
//			}
//			
////			for (QuestionBean affectedBean : bean.getAffectedQuestionBeanVisibility()){
//			iteratedList = new ArrayList<QuestionBean>(bean.getAffectedQuestionBeanVisibility());
//			for (QuestionBean affectedBean : iteratedList){
//				String relevantExpression = affectedBean.getRelevant_question();
//				//Glen 29 Oct 2014, fix error
////				boolean isVisible = isQuestVisibleIfRelevant(relevantExpression, bean);
//				boolean isVisible = isQuestVisibleIfRelevant(relevantExpression, affectedBean);
//				affectedBean.setIs_visible(Formatter.booleanToString(isVisible));
//				updateAffectedBeanAnswer(affectedBean);				//loop
//			}
//			
////			for (QuestionBean affectedBean : bean.getAffectedQuestionBeanCalculation()){
//			iteratedList = new ArrayList<QuestionBean>(bean.getAffectedQuestionBeanCalculation());
//			for (QuestionBean affectedBean : iteratedList){
//				setCalculationResult(affectedBean);
//				updateAffectedBeanAnswer(affectedBean);
//			}
//			
//		}
//		
//		//Glen 15 Oct 2014, add new param for validation
//		private boolean doNext(boolean validate) {
//			boolean result = false;
//			if(isApproval){
//				while(loadDynamicForm()){
//					isFinish = false;						
////					if (visibleQuestion.size()==questionContainer.getChildCount()) {
//					if (listOfQuestion.size()==questionSize) {
//						isApproval=false;
////						isVerified=false;
//						isFinish = true;
////						ScaleAnimation anim = new ScaleAnimation(1,0,1,0);
////				        anim.setDuration(200);
////				        anim.setFillAfter(true);
////				        questionContainer.startAnimation(anim);
//						showFinishScreen();
//						break;
//					}else{
//						loadDynamicForm();
//						break;
//					}
//				}			
//				result = true;
//			}else{
//				if (this.validateCurrentPage(validate)) {
//					 
//					if (inPreviewEditMode){
//						
//						needQuickValidation = true;			//flag as need to re-validate all
//						
//						updateAffectedBeanAnswer(edittedQuestion);
//						edittedQuestion = null;
//						inPreviewEditMode = false;
//					}
//					else{
//						while(loadDynamicForm()){
//							isFinish = false;						
////							if (visibleQuestion.size()==questionContainer.getChildCount()) {
//							if (listOfQuestion.size()==questionSize) {
//								isApproval=false;
////								isVerified=false;
//								isFinish = true;
////								ScaleAnimation anim = new ScaleAnimation(1,0,1,0);
////						        anim.setDuration(200);
////						        anim.setFillAfter(true);
////						        questionContainer.startAnimation(anim);
//								showFinishScreen();
//								break;
//							}else{
//								loadDynamicForm();
//								break;
//							}
//						}
//					}
//					result = true;
//				}else{
////					isVerified=false;
//					isApproval=false;					
//					result = false;
//				}
//			}
//			return result;
//		}
//
//		// bong Oct 10th, 2014 jump to page and validate every page which get passed
////		private void doJumpToPage(int jumpPage) {
////			int current = paging.getPage();
////			AlertDialog.Builder builder = new AlertDialog.Builder(DynamicQuestionActivity.this)
////			.setTitle("Mandatory Question")
////	        .setMessage("Mandatory Question(s) must be filled")
////	        .setCancelable(false)
////	        .setPositiveButton("Okay",
////	                new DialogInterface.OnClickListener() {
////	            public void onClick(DialogInterface dialog,
////	                    int whichButton) {
////	            	//TASK YOU WANT TO PERFORM
////	            }
////	        });
////			
////			while (paging.getPage() != jumpPage) {
////				//Glen 16 Oct 2014, new param
////				if(paging.getPage()<paging.getTotalPage()){
////					if (this.doNext(true)){
////						current=paging.getPage();
////						current++;
////					}
////					else {
////						builder.create().show();
////						break;
////					}
////				}
////				else {
////					// bong Oct 21st, 2014 - jump to preview if hiddenQuestion is on page before preview page
////					break;
////				}
////			}
////			// print page
////			updateDisplayPage();
////		}
//		
//		// bong Oct 10th, 2014 add a jump to page feature
////		private void doJump(){
////			// pop up dialog to input page
////			AlertDialog.Builder builder = new AlertDialog.Builder(DynamicQuestionActivity.this);
////		    // Get the layout inflater
//////		    LayoutInflater inflater = DynamicSurveyActivity.this.getLayoutInflater();
////		    // Inflate and set the layout for the dialog
////		    // Pass null as the parent view because its going in the dialog layout
//////		    	builder.setView(inflater.inflate(R.layout.jump_to_page, null));
////			
////			 // Setting Dialog Title
////	        builder.setTitle("Jump to Page");
////
////	        // Setting Dialog Message
////	        builder.setMessage("Input Page");
////	        final EditText inputPage = new EditText(DynamicQuestionActivity.this);
////	        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(
////	                                LinearLayout.LayoutParams.MATCH_PARENT,
////	                                LinearLayout.LayoutParams.MATCH_PARENT);
////	          inputPage.setLayoutParams(lp);
////	          // set inputType to number
////	          inputPage.setInputType(InputType.TYPE_CLASS_NUMBER);
////	          inputPage.setPadding(20, 2, 20, 2);
////	          builder.setView(inputPage);
////	          
////		    // Add action buttons
////		           builder.setPositiveButton("Go", new DialogInterface.OnClickListener() {
////		               @Override
////		               public void onClick(DialogInterface dialog, int id) {
////		            	   int jumpPage=0;
////		            	   if(inputPage.getText().toString().length()>0)
////		            		   jumpPage = Integer.parseInt(inputPage.getText().toString());   
////		            	   // Doing jumpPage
////		            	   int current = paging.getPage();
//////		            	   final EditText inputPage = (EditText) findViewById(R.id.etPageNumber);
//////		            	   if (Global.IS_DEV) System.out.println("inputPage = "+inputPage.getText().toString());
//////		            	   int jumpPage = Integer.parseInt(inputPage.getText().toString());
////		            	   
////		            	   if(jumpPage>paging.getTotalPage()||jumpPage<1){
////		            		   AlertDialog.Builder errPage = new AlertDialog.Builder(DynamicQuestionActivity.this)
////		            			.setTitle("Page Not Available")
////		            	        .setMessage("Requested page is not available")
////		            	        .setCancelable(false)
////		            	        .setPositiveButton("Okay",
////		            	                new DialogInterface.OnClickListener() {
////		            	            public void onClick(DialogInterface dialog,
////		            	                    int whichButton) {
////		            	            	//TASK YOU WANT TO PERFORM
////		            	            }
////		            	        });
////		            		   errPage.create().show();
////		            	   }
////		            	   else{
////		            		   if (jumpPage < current) {
////			            		   jumpToPage(jumpPage, false);
////			            	   } else if (jumpPage > current) {
////			            		   doJumpToPage(jumpPage);
////			            	   }
////		            	   }
////		               }
////		           })
////		           // bong Oct 14th, 2014 - adding last edited page button
////		           .setNeutralButton("Last Edited Page", new DialogInterface.OnClickListener() {
////		               public void onClick(DialogInterface dialog, int id) {
////		                   // return to last edited page
////		            	   int lastEditPage=1; // must be initialize first
////		            	   int current = paging.getPage();
////		            	   
////		            	   // how to get last edited page	            	   
//////		            	   FormOpenHelper formOpenHelper = new FormOpenHelper(getApplicationContext());
//////		            	   List<QuestionBean> questionBeanList = formOpenHelper.getQuestionSet(header.getForm().getSchemeId());
//////		            	   if (Global.IS_DEV) System.out.println("schemeId dari bean = "+questionBean.get(0).getSchemeId());
//////		            	   if (Global.IS_DEV) System.out.println("schemeId = "+header.getForm().getSchemeId());
////		            	   
////		            	   List<QuestionBean> questionBeanList = new ArrayList<QuestionBean>();
////		            	   questionBeanList = listOfQuestion;
////		            	   int flag=0; // flag for relevan
////		            	   if(paging.getPageSize()==Global.ROW_PER_PAGE)
////		            		   flag=0;
////		            	   else if (paging.getPageSize()==1)
////		            		   flag=1; // having relevan
////		            	   
////		            	   int page=1;
////		            	   int idx=0;
////		            	   lastEditPage=page;
////		            	   while(idx<questionBeanList.size()){
////		            		   QuestionBean bean = questionBeanList.get(idx);
////		            		   if(QuestionBean.getAnswer(bean)==null||QuestionBean.getAnswer(bean).toString().length()==0){
////		            			   
////		            		   }
////		            		   else {
////		            			   if (Global.IS_DEV) System.out.println("answer is : " + QuestionBean.getAnswer(bean));
////		            			   lastEditPage=page;
////		            		   }
////		            		   if(idx%Global.ROW_PER_PAGE==0 && idx!=0)
////	            				   page++;
////	            			   else if(flag==1)
////	            				   page++;
////		            		   idx++;
////		            	   }
////		            	   if(lastEditPage==0)lastEditPage=1;
//////		            	   lastEditPage=page;
////		            	   if (Global.IS_DEV) System.out.println("LastEditPage = "+lastEditPage);
////		            	   // then jump to page
////		            	   if (lastEditPage < current) {
////		            		   jumpToPage(lastEditPage, false);
////		            	   } else if (lastEditPage > current) {
////		            		   doJumpToPage(lastEditPage);
////		            	   }
////		               }
////		           })
////		           .setNegativeButton("Cancel", new DialogInterface.OnClickListener() {
////		               public void onClick(DialogInterface dialog, int id) {
////		                   // Cancel jumpPage
////		               }
////		           });  
////		    	builder.create().show();
////		}
//
//		private void doSave(){
//			if (!isFinish) {
//				this.validateCurrentPage(false);
//			}
//			
//			header.setStatus(TaskHDataAccess.STATUS_SEND_SAVEDRAFT);
////			header.
////			new SurveySaveTask(this, mode, header, listOfQuestion, false)
////					.execute();
//			//TODO : Buat Save TAsk ~GG
//			String uuid_last_question= visibleQuestion.get(visibleQuestion.size()-1).getUuid_question_set();
////			new TaskManager().saveTask(this, mode, header, listOfQuestion, uuid_last_question, false);
//			this.finish();
//			// bong Oct 14th, 2014 - deleting tasklistview from local when saving
//			//if (Global.IS_DEV) System.out.println("taskId = "+header.getId());
////			SurveyManager svyMgr = new SurveyManager();
////			String taskId = svyMgr.saveSurvey(getApplicationContext(), mode, header,
////   					listOfQuestion);
//			//String taskId = header.getId();
//			//if (Global.IS_DEV) System.out.println("taskId saat save = "+taskId);
//			//TaskListViewOpenHelper taskListViewOpenHelper = new TaskListViewOpenHelper(getApplicationContext());
//			//taskListViewOpenHelper.delete(ApplicationBean.getInstance().getUserId(), taskId);
//			
//		}
//		
//		//Glen 16 Oct 2014, validate all page quickly without saving
//		private boolean validateAllMandatory(boolean displayMessage){
//			boolean result = true;
//			for(Map.Entry<String, QuestionBean> entry : listOfQuestion.entrySet()){						
//				//Glen 10 Nov 2014, fix unanswered question skipped validation, and prevent validation on invisible field
//				QuestionBean bean = entry.getValue();
//				if (bean.getIs_mandatory().equals(Global.TRUE_STRING) && bean.getIs_visible().equals(Global.TRUE_STRING)){
//					String answer = QuestionBean.getAnswer(bean);
//					if( answer == null || answer.length() == 0 ){		//tidak ada isi
//						Toast.makeText(this, bean.getQuestion_label() + " " + getString(R.string.msgRequired), Toast.LENGTH_SHORT).show();
//						result = false;
//					}
//				}
//			}
//			return result;
//		}
//
//		private boolean validateCurrentPage(boolean isCekValidate) {
//			List<String> errMessage = new ArrayList<String>();
//			final String msgRequired = getString(R.string.msgRequired);
//			QuestionViewValidator validator = new QuestionViewValidator(msgRequired, getApplicationContext());
//
//			
//			//Glen 20 Oct 2014, use list instead of index and paging
////			int idx = paging.getStart() - 1;
////			for (int i = 0; i < questionContainer.getChildCount(); i++) {
////				try {
////					QuestionBean qBean = listOfQuestion.get(idx);
//	//
////					LinearLayout qContainer = (LinearLayout) questionContainer
////							.getChildAt(i);
////					List<String> err = validator.validateGeneratedQuestionView(
////							qBean, idx, qContainer);
////					if (err != null && err.size() > 0)
////						errMessage.addAll(err);
////				} finally {
////					idx++;
////				}
////			}
//			for (int i = 0; i < currentPageViews.size(); i++) {
//				try{
//					QuestionBean qBean = currentPageBeans.get(i);
//					QuestionView qContainer = (QuestionView) currentPageViews.get(i);
//					List<String> err = validator.validateGeneratedQuestionView(qBean, 0, qContainer);		//idx don't seem to be used, so we put 0
//					if (err != null && err.size() > 0) errMessage.addAll(err);
//					
//					if(qContainer.isChanged()){
//						if(qBean.isRelevanted()){
//							((QuestionView) questionContainer.getChildAt(i)).setChanged(false);
//							int childCount  = questionContainer.getChildCount();
//						//	QuestionView qContainer2 = (QuestionView) questionContainer.getChildAt(childCount-1);
//							//questionContainer.removeViewAt(childCount);
//							for(int x = (childCount-1); x > i; x--){
////								questionContainer.removeViewAt(x);
////								questionSize--;
////								visibleQuestion.removeLast();
//								currentPageBeans.remove(x);
//								currentPageViews.remove(x);
//								loadBackDynamicForm();
//								
//							}
//						}else{
//							((QuestionView) questionContainer.getChildAt(i)).setChanged(false);
//						}
//					}
//				} catch(Exception e){
//					e.printStackTrace();
//				}
//			}
//
//			if (errMessage.size() > 0 && isCekValidate) {
//				String[] msg = (String[]) errMessage.toArray(new String[errMessage
//						.size()]);
//				String alert = Tool.implode(msg, "\n");
//				if(!isApproval){				
//					Toast.makeText(this, alert, Toast.LENGTH_SHORT).show();
//				}
//				return false;
//			}
//			return true;
//		}
//		
//		private static Menu mainMenu;
//		public static void updateMenuIcon(boolean isGPS) {
//			UpdateMenuIcon uItem = new UpdateMenuIcon();
//			uItem.updateGPSIcon(mainMenu);
//	    }
//		
//		@Override
//		public boolean onCreateOptionsMenu(Menu menu) {
//			menu.clear();
//			MenuInflater inflater = getMenuInflater();
//			inflater.inflate(R.menu.main_menu, menu);
//			mainMenu = menu;
//			return true;
//		}
//
//		@Override
//		public boolean onPrepareOptionsMenu(Menu menu) {
//			updateMenuIcon(Global.isGPS);
//			if (Global.IS_DEV) {
//				mainMenu.findItem(R.id.menuMore).setVisible(true);
//				mainMenu.findItem(R.id.mnPendingTask).setVisible(true);				
//			}
//			
//			if (!isSimulasi) {
//				mainMenu.findItem(R.id.menuMore).setVisible(true);
//				mainMenu.findItem(R.id.mnRecord).setVisible(true);
//			}
//
//			return super.onPrepareOptionsMenu(menu);
//		}
//		@Override
//		public boolean onOptionsItemSelected(MenuItem item) {
//			// Handle item selection
//			int id = item.getItemId();
//			if(id == R.id.mnPendingTask){
//				if (Global.IS_DEV) {
//					header.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);
//					//TODO : Buat Save TAsk ~GG
////					task.saveTask(this, mode, header, listOfQuestion, false);
////					new TaskManager().saveTask(this, mode, header, listOfQuestion, "1", false);
//					finish();
//				}
//			}else if(id == android.R.id.home){
//				onBackPressed();
//			}
//			if(id == R.id.mnRecord){
//				Intent intent = new Intent(this, VoiceNotePage.class);
//				Bundle extras = new Bundle();
//				extras.putInt(Global.BUND_KEY_MODE_SURVEY, mode); 
//				extras.putSerializable(Global.BUND_KEY_SURVEY_BEAN, DynamicQuestionActivity.header);
//				intent.putExtras(extras);
//				startActivityForResult(intent, Global.REQUEST_VOICE_NOTES);
//			}
//			return true;
//			
//		}
//
//		private void showFinishScreen() {
//			cancelTimer1();
//			showFinishScreen(null);
//		}
//		
//		private static int idxQuestion=0;
//		Timer myTimer2;
//		private void cancelTimer1() {
//			// TODO Auto-generated method stub
//			if(myTimer!=null){
//				btnBack.setClickable(true);
//				btnNext.setClickable(true);
//				btnSend.setClickable(true);
//				btnSave.setClickable(true);
//				btnSearch.setClickable(true);
//				myTimer.cancel();				
//				myTimer.purge();
//				synchronized (myTimer) {
//					myTimer.notifyAll();
//				}
//			}
//		}
//		private void cancelTimer2() {
//			// TODO Auto-generated method stub
//			if(myTimer2!=null){
//				btnClose.setClickable(true);
//				myTimer2.cancel();
//				myTimer2.purge();
//				synchronized (myTimer2) {
//					myTimer2.notifyAll();
//				}
//			}
//		}
//		//Glen 6 Aug 2014, show finish screen with message (add parameter, and extract an empty parameter method)
//		private void showFinishScreen(String message) {
//			validateCurrentPage(false);
//			reviewContainer.removeAllViews();
//			setTitle("Review Task");
//			scrollView.setVisibility(View.GONE);
//			ScaleAnimation anim = new ScaleAnimation(0,1,0,1);
//	        anim.setDuration(500);
//	        anim.setFillAfter(true);
//	        reviewContainer.startAnimation(anim);
////			btnReview.setVisibility(View.GONE);
//	        if(Global.NEW_FEATURE){
//	        	if(Global.FEATURE_REJECT_WITH_RESURVEY==true){
//					if (!isVerified) {
//						btnNext.setClickable(false);
//						btnNext.setImageResource(R.drawable.ic_next_off);
//					}
//	        	}
//	        }
//	        else{
//	        	btnNext.setClickable(false);
//				btnNext.setImageResource(R.drawable.ic_next_off);
//	        }
//			
//			btnBack.setVisibility(View.VISIBLE);
//			btnSend.setEnabled(true);
//			btnSend.setClickable(true);			
//			btnSend.setImageResource(R.drawable.ic_send);
//			btnSend.setOnClickListener(this);		
//			btnReject.setClickable(true);
//			btnReject.setImageResource(R.drawable.ic_reject);
//			btnVerified.setClickable(true);
//			btnVerified.setImageResource(R.drawable.ic_verified);
//			//Glen 6 August 2014, generate preview
//			if (previewFields == null) previewFields = new ArrayList<LinearLayout>();
//			previewFields.clear();
//			scrollView2.setVisibility(View.VISIBLE);
//			
//			myTimer2 = new Timer();			
//			myTimer2.schedule(new TimerTask() {
//
//				@Override
//				public void run() {
//					// TODO Auto-generated method stub
//					runOnUiThread(new Runnable() {										
//						public void run() {
//							if(idxQuestion<listOfQuestion.size()){
//								QuestionBean questionBean = listOfQuestion.get(idxQuestion);
//								if (questionBean.isVisible()) {								
//				                    String relevantExpression = questionBean.getRelevant_question();
//									if(relevantExpression==null) relevantExpression="";
//									if (isQuestVisibleIfRelevant(relevantExpression, questionBean)) {
//										LinearLayout field = null;
//										try {
//											field = reviewGenerator.generateReviewQuestion(DynamicQuestionActivity.this, questionBean, ViewImageActivity.class);
//										} catch (Exception e) {
//											// TODO Auto-generated catch block
//											e.printStackTrace();
//										}
//										if(field!=null){
//											LinearLayout layout = new LinearLayout(DynamicQuestionActivity.this);
//											layout.setBackgroundColor(Color.TRANSPARENT);
//											reviewContainer.addView(field, LayoutParams.FILL_PARENT,
//													LayoutParams.WRAP_CONTENT);
//											reviewContainer.addView(layout, LayoutParams.FILL_PARENT,
//													10);
//											field.setOnClickListener(DynamicQuestionActivity.this);
//	//										ScaleAnimation anim = new ScaleAnimation(0,1,0,1);
//	//								        anim.setDuration(200);
//	//								        anim.setFillAfter(true);
//	//								        field.startAnimation(anim);
//											previewFields.add(field);
//										}
//									}
//								}
//								else if (!questionBean.isVisible()){
//									//Glen 17 Oct 2014, add empty view, to make sure the order of field in previewFields is still correct
//					//				previewFields.add(null);
//					//				continue;
//									String relevantExpression = questionBean.getRelevant_question();
//									if(relevantExpression==null) relevantExpression="";
//									else if (isQuestVisibleIfRelevant(relevantExpression, questionBean)) {					
//										QuestionSet tempQuestion = QuestionSetDataAccess.getOne(getApplicationContext(), header.getUuid_scheme(), questionBean.getQuestion_id(), questionBean.getQuestion_group_id());
//										if(tempQuestion!=null){
//											if(tempQuestion.getIs_visible().equals(Global.TRUE_STRING)){
//												questionBean.setVisible(true);
//												LinearLayout field = null;
//												try {
//													field = reviewGenerator.generateReviewQuestion(DynamicQuestionActivity.this, questionBean, ViewImageActivity.class);
//												} catch (Exception e) {
//													// TODO Auto-generated catch block
//													e.printStackTrace();
//												}
//												if(field!=null){
//													LinearLayout layout = new LinearLayout(DynamicQuestionActivity.this);
//													layout.setBackgroundColor(Color.TRANSPARENT);
//													reviewContainer.addView(field, LayoutParams.FILL_PARENT,
//															LayoutParams.WRAP_CONTENT);
//													reviewContainer.addView(layout, LayoutParams.FILL_PARENT,
//															10);
//													field.setOnClickListener(DynamicQuestionActivity.this);
//													previewFields.add(field);
//												}
//											}
//											else{
//												questionBean.setVisible(false);
//											}
//										}
//									}
//								}
//								idxQuestion++;								
//							}
//							else{
//								cancelTimer2();
//							}
//						}
//					
//					});
//				}
//			}, 0, 100);
//			
////			for (final QuestionBean questionBean : listOfQuestion){
////				try {
////					if (questionBean.isVisible()) {
////						
////					                    String relevantExpression = questionBean.getRelevant_question();
////										if(relevantExpression==null) relevantExpression="";
////										if (isQuestVisibleIfRelevant(relevantExpression, questionBean)) {
////											LinearLayout field = reviewGenerator.generateReviewQuestion(DynamicQuestionActivity.this, questionBean, ViewImageActivity.class);
////											LinearLayout layout = new LinearLayout(DynamicQuestionActivity.this);
////											layout.setBackgroundColor(Color.TRANSPARENT);
////											reviewContainer.addView(field, LayoutParams.FILL_PARENT,
////													LayoutParams.WRAP_CONTENT);
////											reviewContainer.addView(layout, LayoutParams.FILL_PARENT,
////													10);
////											field.setOnClickListener(DynamicQuestionActivity.this);											
////											previewFields.add(field);	
////										}
////					                
////					                      			
////    
////						
////					}
////					else if (!questionBean.isVisible()){
////						//Glen 17 Oct 2014, add empty view, to make sure the order of field in previewFields is still correct
//////						previewFields.add(null);
//////						continue;
////					}
//////					LinearLayout field = reviewGenerator.generateReviewQuestion(this, questionBean, ViewImageActivity.class);
//////					LinearLayout layout = new LinearLayout(this);
//////					layout.setBackgroundColor(Color.TRANSPARENT);
//////					//Glen 22 Oct 2014, add empty space
////////					Space space = new Space(this);
//////					questionContainer.addView(field, LayoutParams.FILL_PARENT,
//////							LayoutParams.WRAP_CONTENT);
//////					questionContainer.addView(layout, LayoutParams.FILL_PARENT,
//////							20);
//////					//Glen 10 Oct 2014, test click listener
//////					field.setOnClickListener(this);
//////					
//////					previewFields.add(field);					//to hold it's index to get it's questionbean from listOfQuestion
////				} catch (Exception e) {
////					e.printStackTrace();
////				}
////			}
//			
//		}
//		
//		
//		/*//Glen 10 Oct 2014, on preview click
////		@TargetApi(Build.VERSION_CODES.HONEYCOMB)
//		public void onPreviewClicked(QuestionBean bean){		//bean of selected preview item to be editted
//			idxPosition=0;
//			idxQuestion=0;
//			//Glen 21 Oct 2014, disable menu
//			invalidateOptionsMenu();
//			
//			edittedQuestion = bean;
//			
//			inPreviewEditMode = true;
//			//hack paging set to previous page, so when user press doNext they go to previewScreen again
////			paging.previous();
//			
//			this.clearListView();
//			int idx = listOfQuestion.get(bean.getIdentifier_name());
//			ViewGroup questionField = getQuestionView(bean, idx+1);
//			if (Tool.isOptions(bean.getAnswer_type())){
//				MultiOptionQuestionViewAbstract multipleOptionQuestionField = (MultiOptionQuestionViewAbstract) questionField;
//				loadOptionsToView(multipleOptionQuestionField);
//			}
//			questionContainer.addView(questionField);
//			
//			//Glen 20 Oct 2014
//			currentPageBeans.add(bean);
//			currentPageViews.add(questionField);
//			
//			
//			btnNext.setClickable(true);
//			btnNext.setImageResource(R.drawable.ic_next);
//			btnSend.setClickable(false);
//			btnSend.setImageResource(R.drawable.ic_send_off);
//		}*/
//
//		@Override
//		public void onBackPressed() {
//			try {
//				cancelTimer1();
//				cancelTimer2();
//			} catch (Exception e) {
//				// TODO: handle exception
//			}
//			if(mode == Global.MODE_VIEW_SENT_SURVEY)
//				super.onBackPressed();
//			else{
//				DialogManager.showExitAlertQuestion(this, getString(R.string.alertExitSurvey));						
//			}
//		}
//
//		final class DynamicSurveyHandler extends Handler {
//
//			@Override
//			public void handleMessage(Message msg) {
//				this.setQuestionInFocusByModel(msg.what);
//			}
//
//			private void setQuestionInFocusByModel(int idx) {
//				setQuestionInFocus(listOfQuestion.get(idx));
//
//				int mod = ++idx % listOfQuestion.size();
//				int position = (mod == 0) ?  listOfQuestion.size() : mod;
//
//				int start = 0;
//				int end = listOfQuestion.size();
//
//				if (idx >= start && idx <= end) {
//					LinearLayout qContainer = (LinearLayout) questionContainer
//							.getChildAt(--position);
//					setThumbInFocus((ImageView) qContainer.getChildAt(1));
//					if(qContainer.getChildCount()>2){
//						setThumbLocationInfo((ImageView) qContainer.getChildAt(2));
//						setTxtDetailInFocus((TextView) qContainer.getChildAt(3));
//					}else{
//						setTxtDetailInFocus((TextView) qContainer.getChildAt(2));
//					}
//				} else {
//					setThumbInFocus(null);
//				}
//			}
//		}
//
//		
//		//=== Request JSON From Server Delegate ===//
////		@Override
////		public void onConnectionFinish(RequestJsonFromURLTask request, String result) {
////			if (result != null && result.length() > 0){
////				//Yes there is result
////				showFinishScreen(result);
////			}
////			else{
////				//throw as connection failed
////				onConnectionFailed(request, null, "Failed to connect to server\nPlease try again later");
////			}
////			
////		}
//	//
////		@Override
////		public void onConnectionFailed(RequestJsonFromURLTask request,
////				String result, String errMessage) {
////			
////			Toast.makeText(this, errMessage, Toast.LENGTH_SHORT).show();
////			
////		}
//
//		
//		@Override
//		public void onPreExecute(GenericAsyncTask task) {
//			// TODO Auto-generated method stub
//			
//		}
//
//		//Glen 7 Jan 2015, updated GenericAsyncTask
//		@Override
//		public String doInBackground(GenericAsyncTask task, String... args) {
//			String[] result = null;
//			//SurveyManager svyMgr = new SurveyManager();
//				try {
//
//					header.setStatus(TaskHDataAccess.STATUS_SEND_SAVEDRAFT);
//	//
//					//TODO : Buat Save TAsk ~GG
////				String taskId = this.task.saveTask(this, mode, header,listOfQuestion);
////					// SurveySaveForPreSubmitTask fakeTask = new
////					// SurveySaveForPreSubmitTask(this, mode, header, listOfQuestion,
////					// false);
////					// fakeTask.do
////
////					//header.setUuid_task_h(taskId);
////					header.setTask_id(taskId);
////					mode = Global.MODE_SURVEY_TASK;
////					result = this.task.doPreSubmitNewSurveyTask(this, taskId, true);
//				} catch (Exception e) {
//					result = null;
//					e.printStackTrace();
//					return null;
//				}
//
//				String flattenedResult = Tool.implode(result, Global.DELIMETER_DATA2);
//
//				return flattenedResult;
//		}
//
//		@Override
//		public void onPostExecute(GenericAsyncTask task, String result,
//				String errMsg) {
//			if (result != null && result.length() > 0){
//				
//				String[] explodedResult = Tool.explode(result, Global.DELIMETER_DATA2);
//				String[] serverResponses = Tool.explode(explodedResult[0], Global.DELIMETER_ROW);
//				//index0 = server message, index1 = null, index2 = status: 1=success -1=fail
//				String serverMessage = serverResponses[0];
//				showFinishScreen(serverMessage);
//			}
//			//Glen 11 Aug 2014, return toast message for failure
//			else{
//				Toast.makeText(this, getString(R.string.msgConnectionFailed), Toast.LENGTH_SHORT).show();
//			}
//		}
//	}
//
