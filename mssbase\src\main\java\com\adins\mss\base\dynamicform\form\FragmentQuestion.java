package com.adins.mss.base.dynamicform.form;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.provider.MediaStore;
import android.text.format.DateFormat;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.ScaleAnimation;
import android.view.inputmethod.InputMethodManager;
import android.widget.AutoCompleteTextView;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.biometric.CheckBiometricTask;
import com.adins.mss.base.checkin.CheckInManager;
import com.adins.mss.base.commons.Query;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.decision.GenericRuleJexlLogic;
import com.adins.mss.base.decision.RuleLogic;
import com.adins.mss.base.decision.RuleParam;
import com.adins.mss.base.dukcapil.SubmitImageDkcp;
import com.adins.mss.base.dynamicform.Constant;
import com.adins.mss.base.dynamicform.DynamicFormActivity;
import com.adins.mss.base.dynamicform.LocationInfo2;
import com.adins.mss.base.dynamicform.SurveyHeaderBean;
import com.adins.mss.base.dynamicform.TaskManager;
import com.adins.mss.base.dynamicform.VoiceNotePage;
import com.adins.mss.base.dynamicform.form.models.CriteriaParameter;
import com.adins.mss.base.dynamicform.form.models.LookupAnswerBean;
import com.adins.mss.base.dynamicform.form.models.LookupCriteriaBean;
import com.adins.mss.base.dynamicform.form.models.LookupOnlineRequest;
import com.adins.mss.base.dynamicform.form.models.Parameter;
import com.adins.mss.base.dynamicform.form.models.ParameterAnswer;
import com.adins.mss.base.dynamicform.form.models.ReviewResponse;
import com.adins.mss.base.dynamicform.form.questions.DrawingCanvasActivity;
import com.adins.mss.base.dynamicform.form.questions.ImageViewerActivity;
import com.adins.mss.base.dynamicform.form.questions.OnIsOfflineDSRListener;
import com.adins.mss.base.dynamicform.form.questions.OnQuestionClickListener;
import com.adins.mss.base.dynamicform.form.questions.QuestionReviewAdapter;
import com.adins.mss.base.dynamicform.form.questions.QuestionViewAdapter;
import com.adins.mss.base.dynamicform.form.questions.QuestionsValidator;
import com.adins.mss.base.dynamicform.form.questions.viewholder.ExpandableRecyclerView;
import com.adins.mss.base.dynamicform.form.questions.viewholder.LookupCriteriaOnlineActivity;
import com.adins.mss.base.dynamicform.form.questions.viewholder.LookupFilterActivity;
import com.adins.mss.base.dynamicform.form.questions.viewholder.TextQuestionViewHolder;
import com.adins.mss.base.dynamicform.form.questions.viewholder.TextWithSuggestionQuestionViewHolder;
import com.adins.mss.base.getreferantor.JsonResponseCekReferantor;
import com.adins.mss.base.mainmenu.MainMenuActivity;
import com.adins.mss.base.todolist.form.StatusViewAdapter;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.base.util.Utility;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.GeneralParameter;
import com.adins.mss.dao.LocationInfo;
import com.adins.mss.dao.Lookup;
import com.adins.mss.dao.LookupDao;
import com.adins.mss.dao.MarketPrice;
import com.adins.mss.dao.ProductOffering;
import com.adins.mss.dao.QuestionSet;
import com.adins.mss.dao.ReceiptVoucher;
import com.adins.mss.dao.Scheme;
import com.adins.mss.foundation.camerainapp.CameraActivity;
import com.adins.mss.foundation.camerainapp.helper.Logger;
import com.adins.mss.foundation.db.dataaccess.AssetSchemeDataAccess;
import com.adins.mss.foundation.db.dataaccess.GeneralParameterDataAccess;
import com.adins.mss.foundation.db.dataaccess.IndustryDataAccess;
import com.adins.mss.foundation.db.dataaccess.LookupDataAccess;
import com.adins.mss.foundation.db.dataaccess.MarketPriceDataAccess;
import com.adins.mss.foundation.db.dataaccess.POAssetDataAccess;
import com.adins.mss.foundation.db.dataaccess.PODataAccess;
import com.adins.mss.foundation.db.dataaccess.QuestionSetDataAccess;
import com.adins.mss.foundation.db.dataaccess.ReceiptVoucherDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskHDataAccess;
import com.adins.mss.foundation.dialog.DialogManager;
import com.adins.mss.foundation.dialog.NiftyDialogBuilder;
import com.adins.mss.foundation.formatter.DateFormatter;
import com.adins.mss.foundation.formatter.Formatter;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.http.KeyValue;
import com.adins.mss.foundation.image.ExifData;
import com.adins.mss.foundation.image.Utils;
import com.adins.mss.foundation.location.LocationTrackingManager;
import com.adins.mss.foundation.location.UpdateMenuIcon;
import com.adins.mss.foundation.operators.IfElseFunctionDummy;
import com.adins.mss.foundation.operators.IfElseFunctionForCopyValue;
import com.adins.mss.foundation.print.rv.syncs.SyncRVRequest;
import com.adins.mss.foundation.print.rv.syncs.SyncRVResponse;
import com.adins.mss.foundation.print.rv.syncs.SyncRVTask;
import com.adins.mss.foundation.print.rv.syncs.SyncRvListener;
import com.adins.mss.foundation.questiongenerator.NotEqualSymbol;
import com.adins.mss.foundation.questiongenerator.OptionAnswerBean;
import com.adins.mss.foundation.questiongenerator.QuestionBean;
import com.adins.mss.foundation.questiongenerator.form.LocationTagingView;
import com.adins.mss.foundation.sync.api.SynchronizeResponseLookup;
import com.adins.mss.foundation.sync.api.SynchronizeResponseTable;
import com.adins.mss.foundation.sync.api.model.JsonRequestSyncTableConstraint;
import com.adins.mss.foundation.sync.api.model.SynchronizeRequestModel;
import com.gadberry.utility.expression.Expression;
import com.gadberry.utility.expression.OperatorSet;
import com.gadberry.utility.expression.symbol.AndSymbol;
import com.gadberry.utility.expression.symbol.OrSymbol;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.soundcloud.android.crop.util.Log;

import org.acra.ACRA;
import org.apache.commons.jexl2.JexlContext;
import org.apache.commons.jexl2.JexlEngine;
import org.apache.commons.jexl2.MapContext;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;

import java.io.File;
import java.io.Serializable;
import java.lang.reflect.Type;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Stack;
import java.util.TimeZone;

import static com.adins.mss.base.dynamicform.Constant.listOfQuestion;
import static com.adins.mss.base.dynamicform.form.questions.viewholder.LookupCriteriaOnlineActivity.KEY_SELECTED_CRITERIA;
import static com.adins.mss.base.dynamicform.form.questions.viewholder.LookupCriteriaOnlineActivity.KEY_WITH_FILTER;

//import org.apache.commons.jexl3.JexlBuilder;
//import org.apache.commons.jexl3.JexlContext;
//import org.apache.commons.jexl3.JexlEngine;
//import org.apache.commons.jexl3.MapContext;


/**
 * A fragment representing a list of Items.
 * <p/>
 */
public class FragmentQuestion extends Fragment implements OnQuestionClickListener, OnIsOfflineDSRListener {
    private static final String ARG_COLUMN_COUNT = "column-count";
    public static int NEXT_QUESTION = 111;
    public static int SAVE_QUESTION = 112;
    public static int SEND_QUESTION = 113;
    public static int SEARCH_QUESTION = 114;
    public static int VERIFY_QUESTION = 115;
    public static int APPROVE_QUESTION = 116;
    public static int REJECT_QUESTION = 117;
    public static int CLOSE_QUESTION = 118;

    public static int RESULT_FROM_DRAWING_QUESTION = 222;
    public static int RESULT_FROM_IMAGE_QUESTION = 223;
    public static int RESULT_FROM_LOCATION_QUESTION = 224;
    public static int RESULT_FROM_BUILT_IN_CAMERA = 225;
    public static int RESULT_FROM_ANDROID_CAMERA = 226;
    public static int RESULT_FROM_EDIT_IMAGE = 227;
    public static int RESULT_FROM_LOOKUP_CRITERIA = 228;
    public static int RESULT_FROM_CAMERA_ERROR = 230;
    public static int RESULT_FROM_ANDROID_GALLERY = 231;
    public static int RESULT_FROM_PICK_IMAGE = 232;
    public static int RESULT_FROM_IMAGE_LOC_UPDATE = 233;
    public static int RESULT_FROM_LOCATION_UPDATE = 234;
    public static int RESULT_FROM_ACCEPTED_AGREEMENT = 235;

    public static int RESULT_FROM_SUBMITDKCP = 333;
    public static int RESULT_FROM_SUBMITLAYER = 334;
    public static int RESULT_FROM_CHECK_BIOMETRIC = 335;
    public static int RESULT_FROM_GET_DSR = 336;
    public static int RESULT_FROM_CEK_REFERANTOR = 337;
    public static int RESULT_FROM_CHECK_VALIDATION = 338;

    public static String BUND_KEY_ACTION = "BUND_KEY_ACTION";
    public static String BUND_KEY_SEARCH_ACTION = "BUND_KEY_SEARCH_ACTION";
    public static String BUND_KEY_PICK_IMAGE = "BUND_KEY_PICK_IMAGE";
    public static String BUND_KEY_RESULT = "BUND_KEY_RESULT";
    public static boolean isTxtDsrOfflineShowed = false;
    public static QuestionHandler questionHandler;
    public static double marketPrice;
    public static double tolerancePrctg;
    public static String refIdMaskapai;
    public static String[] refIdTtdQuestion;
    public static QuestionViewAdapter questionAdapter;
    private static Menu mainMenu;
    public static ImageView uiImgViewTtdCustomer;
    public static ImageView uiImgViewTtdSpouse;
    public static ImageView uiImgViewTtdGuarantor;
    public static TextView txtDsrOffline;
    public static EditText edtValueDsr;
    public static Button buttonGetDsr;
    List<QuestionBean> listOfQuestions;
    private String messages;
    //Nendi: 14/02/2018 | Add list QuestionImage
    Map mapImgQuest;
    List<QuestionBean> listOfImageQuestion;
    ExpandableRecyclerView qRecyclerView;
    ExpandableRecyclerView rRecyclerView;
    QuestionsValidator questionsValidator;
    String lastQuestionGroup;
    private int mColumnCount = 1;
    private StatusViewAdapter viewAdapter;
    private QuestionReviewAdapter reviewAdapter;
    private List<String> listOfQuestionGroup;
    private LinkedHashMap<String, List<QuestionBean>> listOfQuestionBean;
    private QuestionBean focusBean;
    private int focusGroup;
    private int focusPosition;
    private int questionSize = 0;
    private JexlEngine jexlEngine;
    private boolean needStop = false;
    private boolean hasLoading = false;
    private boolean isSimulasi = false;
    private boolean isFinish = false;
    private int mode;
    private boolean isSaveAndSending = false;
    private LinkedHashMap<String, List<LookupCriteriaBean>> listOfLookupOnlineStored;
    private int maxRetry;
    private final String MS_LAYER_MAX_RETRY = "MS_LAYER_MAX_RETRY";
    private int maxRetryDsr;
    private String[] listOfAutoHideQuestion = null;
    private final String GET_DSR_MAX_RETRY = "MAX_RETRY_DSR";
    private final String GET_AUTO_HIDE_QUESTION = "AUTO_HIDE_QUESTION";
    private List<String> filterData;
    private boolean statusSyncParamConstraints = false;

    //
    private int maxRetryInvitationEsign;
    private final String MAX_RETRY_INVITATION_ESIGN = "MAX_RETRY_INVITATION_ESIGN";
    private int maxRetryRegistrationEsign;
    private final String MAX_RETRY_CHECK_REG_ESIGN = "MAX_RETRY_CHECK_REG_ESIGN";
    private int maxRetryValidationCheck;
    private final String MAX_RETRY_VALIDATION_CHECK = "MAX_RETRY_VALIDATION_CHECK";

    //
    private String PROD_OFF_CODE = "";
    /**
     * Mandatory empty constructor for the fragment manager to instantiate the
     * fragment (e.g. upon screen orientation changes).
     */
    public FragmentQuestion() {
    }

    // TODO: Customize parameter initialization
    @SuppressWarnings("unused")
    public static FragmentQuestion newInstance(int columnCount) {
        FragmentQuestion fragment = new FragmentQuestion();
        Bundle args = new Bundle();
        args.putInt(ARG_COLUMN_COUNT, columnCount);
        fragment.setArguments(args);
        return fragment;
    }

    public static void updateMenuIcon(boolean isGPS) {
        UpdateMenuIcon uItem = new UpdateMenuIcon();
        uItem.updateGPSIcon(mainMenu);
    }

    private static String removeLastChar(String str) {
        if (str != null && str.length() > 0)
            return str.substring(0, str.length() - 1);
        else return "";
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        GlobalData.getSharedGlobalData().setDoingTask(true);
        if (getArguments() != null) {
            mColumnCount = getArguments().getInt(ARG_COLUMN_COUNT);
        }
        String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        questionHandler = new QuestionHandler();
        listOfQuestions = new ArrayList<>();
        listOfQuestionBean = new LinkedHashMap<>();
        listOfQuestionGroup = new ArrayList<>();
        String msgRequired = getActivity().getString(R.string.msgRequired);
        questionsValidator = new QuestionsValidator(msgRequired, getActivity());
        listOfLookupOnlineStored = new LinkedHashMap<>();
        GeneralParameter maxRetryGp = GeneralParameterDataAccess.getOne(getActivity(), uuidUser, MS_LAYER_MAX_RETRY);
        maxRetry = maxRetryGp == null? 0:Integer.parseInt(maxRetryGp.getGs_value());
        GeneralParameter maxRetryGetDsr = GeneralParameterDataAccess.getOne(getActivity(), uuidUser, GET_DSR_MAX_RETRY);
        maxRetryDsr = maxRetryGetDsr == null? 0:Integer.parseInt(maxRetryGetDsr.getGs_value());
        setGsAutoHideQuestion(uuidUser);
        setGsRefIdMaskapaiAsuransi(uuidUser);

        // CR Esign
        GeneralParameter maxRetryInvitationEsignGp = GeneralParameterDataAccess.getOne(getActivity(), uuidUser, MAX_RETRY_INVITATION_ESIGN);
        maxRetryInvitationEsign = maxRetryInvitationEsignGp == null? 0:Integer.parseInt(maxRetryInvitationEsignGp.getGs_value());
        GeneralParameter maxRetryRegistrationEsignGp = GeneralParameterDataAccess.getOne(getActivity(), uuidUser, MAX_RETRY_CHECK_REG_ESIGN);
        maxRetryRegistrationEsign = maxRetryRegistrationEsignGp == null? 0:Integer.parseInt(maxRetryRegistrationEsignGp.getGs_value());

        // CR Third Party
        GeneralParameter maxRetryValidationCheckGp = GeneralParameterDataAccess.getOne(getActivity(), uuidUser, MAX_RETRY_VALIDATION_CHECK);
        maxRetryValidationCheck = maxRetryValidationCheckGp == null ? 0 : Integer.parseInt(maxRetryValidationCheckGp.getGs_value());
    }

    private void setGsAutoHideQuestion(String uuidUser) {
        GeneralParameter autoHideQuestion = GeneralParameterDataAccess.getOne(getActivity(), uuidUser, GET_AUTO_HIDE_QUESTION);
        if (autoHideQuestion != null) {
            String strAutoHideQuestion = autoHideQuestion.getGs_value();
            try {
                String str = Tool.getRefIdFromJsonObject(strAutoHideQuestion, "setting", 0);
                String strReplace = str.replace("{", "").replace("}", "");
                listOfAutoHideQuestion = strReplace.split(Global.DELIMETER_DATA3);
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
        }
    }

    private void setGsRefIdMaskapaiAsuransi(String uuidUser) {
        GeneralParameter refIdTtd = GeneralParameterDataAccess.getOne(getActivity(), uuidUser, Global.GS_REF_ID_TTD_MASKAPAI);
        if (refIdTtd != null) {
            String gsValue = refIdTtd.getGs_value();
            String[] qsRefId = gsValue.split(":");
            refIdMaskapai = qsRefId[0];
            refIdTtdQuestion = qsRefId[1].split(Global.DELIMETER_DATA3);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Constant.productOff = null;
        Constant.poAsset = null;
    }

    @Override
    public void onCreateOptionsMenu(Menu menu, MenuInflater inflater) {
        menu.clear();
        inflater.inflate(R.menu.main_menu, menu);
        mainMenu = menu;
        super.onCreateOptionsMenu(menu, inflater);
    }

    @Override
    public void onPrepareOptionsMenu(Menu menu) {

        updateMenuIcon(Global.isGPS);
        if (Global.IS_DEV) {
            mainMenu.findItem(R.id.menuMore).setVisible(true);
            mainMenu.findItem(R.id.mnPendingTask).setVisible(isFinish && mode != Global.MODE_VIEW_SENT_SURVEY && !isSimulasi);
        }

        /*20170411 - Hilangkan voice note*/
        mainMenu.findItem(R.id.menuMore).setVisible(false);

        if (!isSimulasi) {
            //mainMenu.findItem(R.id.menuMore).setVisible(true);
            // mainMenu.findItem(R.id.mnRecord).setVisible(true);
        }
        super.onPrepareOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {

        int id = item.getItemId();
        if (id == R.id.mnPendingTask) {
            if (Global.IS_DEV) {
                doPending();
            }
            return true;
        } else if (id == R.id.mnRecord) {
            Intent intent = new Intent(getActivity(), VoiceNotePage.class);
            Bundle extras = new Bundle();
            extras.putInt(Global.BUND_KEY_MODE_SURVEY, mode);
            extras.putSerializable(Global.BUND_KEY_SURVEY_BEAN, DynamicFormActivity.header);
            intent.putExtras(extras);
            getActivity().startActivityForResult(intent, Global.REQUEST_VOICE_NOTES);
            return true;
        } else if (id == R.id.mnGPS) {
            if (Global.LTM != null) {
                if (Global.LTM.isConnected) {
                    Global.LTM.removeLocationListener();
                    Global.LTM.connectLocationClient();
                } else {
                    CheckInManager.startGPSTracking(getActivity());
                }
                Animation a = AnimationUtils.loadAnimation(getActivity(), R.anim.icon_rotate);
                getActivity().findViewById(R.id.mnGPS).startAnimation(a);
            }
            return true;
        }
        return super.onOptionsItemSelected(item);

    }

    @SuppressLint("StaticFieldLeak")
    private void doPending() {
        new AsyncTask<Void, Void, Boolean>() {
            boolean isOK = true;
            boolean isSuccess = false;
            private ProgressDialog progressDialog;

            @Override
            protected void onPreExecute() {
                this.progressDialog = ProgressDialog.show(getActivity(), "", getActivity().getString(R.string.progressWait), true);
            }

            @Override
            public Boolean doInBackground(Void... params) {

                if (!isFinish) {
                    isOK = validateCurrentPage(true, false);
                }
                if (isOK) {
                    DynamicFormActivity.header.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);
                    isSuccess = new TaskManager().saveTask(getActivity(), mode, DynamicFormActivity.header, listOfQuestions, "1", true);
                }
                return isOK;
            }

            @Override
            protected void onPostExecute(Boolean result) {
                if (progressDialog.isShowing()) {
                    try {
                        progressDialog.dismiss();
                    } catch (Exception e) {
                        FireCrash.log(e);
                        if (Global.IS_DEV)
                            e.printStackTrace();
                    }
                }

                if (result && isSuccess) {
                    DynamicFormActivity.header = null;
                    getActivity().finish();
                }
            }
        }.execute();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_question_list, container, false);

        // Set the questionAdapter
        if (view instanceof RelativeLayout) {
            Context context = view.getContext();
            qRecyclerView = view.findViewById(R.id.questionList);
            if (mColumnCount <= 1) {
                int duration = getResources().getInteger(R.integer.scroll_duration);
                qRecyclerView.setLayoutManager(new ScrollingLinearLayoutManager(getActivity(), LinearLayoutManager.VERTICAL, false, duration));
            } else {
                qRecyclerView.setLayoutManager(new GridLayoutManager(context, mColumnCount));
            }
            questionAdapter = new QuestionViewAdapter(getActivity(), qRecyclerView, listOfQuestionGroup, listOfQuestionBean, this, this, listOfQuestions);
            qRecyclerView.setAdapter(questionAdapter);

            rRecyclerView = (ExpandableRecyclerView) view.findViewById(R.id.reviewList);
            showReviewScreen(false);
           /* new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    initByStatus();
                }
            }, 100);*/

            initByStatus();
            setHasOptionsMenu(true);
        }
        return view;
    }

    private void initByStatus() {
        try {
            if (TaskHDataAccess.STATUS_TASK_APPROVAL.equalsIgnoreCase(DynamicFormActivity.header.getStatus())
                    || TaskHDataAccess.STATUS_TASK_APPROVAL_DOWNLOAD.equalsIgnoreCase(DynamicFormActivity.header.getStatus())
                    || mode == Global.MODE_VIEW_SENT_SURVEY) {
                try {
                    DynamicFormActivity.allowImageEdit = false;
                    DynamicFormActivity.isApproval = true;
                    getActivity().findViewById(R.id.btnClose).setClickable(false);
                    isFinish = true;
                    if (mode != Global.MODE_VIEW_SENT_SURVEY) {
                        DynamicFormActivity.isVerified = true;
                    }
//                    DynamicQuestionActivity.showProgressBar(getActivity(), getActivity().getString(R.string.progressWait));
                    showFinishScreen();
                    DynamicQuestionActivity.dismissProgressBar();
                } catch (Exception e) {
                    FireCrash.log(e);
                    loadQuestionForm();
                }
            } else if (TaskHDataAccess.STATUS_TASK_VERIFICATION.equalsIgnoreCase(DynamicFormActivity.header.getStatus()) ||
                    TaskHDataAccess.STATUS_TASK_VERIFICATION_DOWNLOAD.equalsIgnoreCase(DynamicFormActivity.header.getStatus())) {
                try {
                    DynamicFormActivity.allowImageEdit = false;
                    DynamicFormActivity.isVerified = true;
                    hasLoading = true;
                    loadDraftData(true);
                    /*new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            while (true) {
                                if (!doNext(false) || needStop) {
                                    removeNextCallback();
                                    ((ImageButton) getActivity().findViewById(R.id.btnVerified)).setClickable(false);
                                    ((ImageButton) getActivity().findViewById(R.id.btnVerified)).setImageResource(R.drawable.ic_verified_off);
                                    ((ImageButton) getActivity().findViewById(R.id.btnReject)).setClickable(false);
                                    ((ImageButton) getActivity().findViewById(R.id.btnReject)).setImageResource(R.drawable.ic_reject_off);
                                    break;
                                }
                            }
                            if (isFinish)
                                removeNextCallback();
                            DynamicQuestionActivity.dismissProgressBar();
                        }
                    }, 10);*/

                } catch (Exception e) {
                    FireCrash.log(e);
                    loadQuestionForm();
                }
            } else if (TaskHDataAccess.STATUS_SEND_SAVEDRAFT.equals(DynamicFormActivity.header.getStatus())) {
                try {
                    //loadDraftData();
                    hasLoading = true;
                    getActivity().findViewById(R.id.btnBack).setClickable(false);
                    getActivity().findViewById(R.id.btnNext).setClickable(false);
                    getActivity().findViewById(R.id.btnSend).setClickable(false);
                    getActivity().findViewById(R.id.btnSave).setClickable(false);
                    getActivity().findViewById(R.id.btnSearch).setClickable(false);

                    loadDraftData(false);
//                    DynamicQuestionActivity.showProgressBar(getActivity(), getActivity().getString(R.string.progressWait));
                    /*new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            while (questionSize < DynamicFormActivity.header.getLast_saved_question()) {
                                if (!doNext(false) || needStop) {
                                    removeNextCallback();
                                }
                            }
                            removeNextCallback();
                            needStop = true;
                            DynamicQuestionActivity.dismissProgressBar();
                        }
                   }, 10);*/
                } catch (Exception e) {
                    FireCrash.log(e);
                    loadQuestionForm();
                }

            } else {
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        loadQuestionForm();
                        DynamicQuestionActivity.dismissProgressBar();
                    }
                }, 10);
            }
        } catch (Exception e) {
            FireCrash.log(e);
            Toast.makeText(getActivity(), getString(R.string.request_error), Toast.LENGTH_SHORT).show();
            getActivity().finish();
        }
    }

    @SuppressLint("StaticFieldLeak")
    public void loadReviewData() {
        new AsyncTask<Void, Void, Boolean>() {
            private ProgressDialog progressDialog;

            @Override
            protected void onPreExecute() {
                super.onPreExecute();
                progressDialog = ProgressDialog.show(getActivity(), "", getActivity().getString(R.string.please_wait), true);
            }

            @Override
            protected Boolean doInBackground(Void... params) {
                return loadQuestionForReview(listOfQuestion.size(), true);
            }

            @Override
            protected void onPostExecute(Boolean aBoolean) {
                super.onPostExecute(aBoolean);
                if (progressDialog.isShowing()) {
                    try {
                        progressDialog.dismiss();
                    } catch (Exception e) {
                        FireCrash.log(e);
                        if (Global.IS_DEV)
                            e.printStackTrace();
                    }
                }
                if (aBoolean) {
                    try {
                        int range = getCounterListBeforeGroup(reviewAdapter.getGroupItemCount(), true) + 1;
                        reviewAdapter.notifyItemRangeInserted(0, range);
                        reviewAdapter.notifyDataSetChanged();
                    } catch (Exception e) {
                        FireCrash.log(e);
                        if (Global.IS_DEV)
                            e.printStackTrace();
                    }
                }
                showReviewScreen(true);
            }

            @Override
            protected void onCancelled() {
                super.onCancelled();
                if (progressDialog.isShowing()) {
                    try {
                        progressDialog.dismiss();
                    } catch (Exception e) {
                        FireCrash.log(e);
                        if (Global.IS_DEV)
                            e.printStackTrace();
                    }
                }
            }
        }.execute();
    }

    @SuppressLint("StaticFieldLeak")
    private void loadDraftData(final boolean forVerification) {
        new AsyncTask<Void, Void, Boolean>() {
            private ProgressDialog progressDialog;

            @Override
            protected void onPreExecute() {
                super.onPreExecute();
                progressDialog = ProgressDialog.show(getActivity(), "", getActivity().getString(R.string.please_wait), true);
            }

            @Override
            protected Boolean doInBackground(Void... params) {
                if (forVerification) {
                    return loadQuestionForReview(listOfQuestion.size(), false);
                } else {
                    return loadQuestionForReview(DynamicFormActivity.header.getLast_saved_question(), true);
                }
            }

            @Override
            protected void onPostExecute(Boolean aBoolean) {
                super.onPostExecute(aBoolean);
                if (progressDialog.isShowing()) {
                    try {
                        progressDialog.dismiss();
                    } catch (Exception e) {
                        FireCrash.log(e);
                        if (Global.IS_DEV)
                            e.printStackTrace();
                    }
                }
//                if (aBoolean) {
                //Nendi: Disable Validation on Load Draft Data
//                validateAllMandatory(true);
                removeNextCallback();
                needStop = true;
                int range = getCounterListBeforeGroup(questionAdapter.getGroupItemCount(), false) + 1;
                questionAdapter.notifyItemRangeInserted(0, range);
                questionAdapter.notifyDataSetChanged();

//                }
            }
        }.execute();
    }

    private void removeNextCallback() {

        hasLoading = false;
        getActivity().findViewById(R.id.btnBack).setClickable(true);
        getActivity().findViewById(R.id.btnNext).setClickable(true);
//        getActivity().findViewById(R.id.btnSend).setClickable(true);
        getActivity().findViewById(R.id.btnSave).setClickable(true);
        getActivity().findViewById(R.id.btnSearch).setClickable(true);

        Utility.freeMemory();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Utility.freeMemory();
        GlobalData.getSharedGlobalData().setDoingTask(false);
    }

    private void showFinishScreen() {
        /*if (questionSize == 0) {
//            loadQuestionForReview(Constant.listOfQuestion.size());
            loadReviewData();
        }*/
        if (Global.NEW_FEATURE) {
            if (Global.FEATURE_REJECT_WITH_RESURVEY) {
                if (!DynamicFormActivity.isVerified) {
                    getActivity().findViewById(R.id.btnNext).setClickable(false);
                    ((ImageButton) getActivity().findViewById(R.id.btnNext)).setImageResource(R.drawable.ic_next_off);
                } else {
                    getActivity().findViewById(R.id.btnNext).setEnabled(true);
                    getActivity().findViewById(R.id.btnNext).setClickable(true);
                    ((ImageButton) getActivity().findViewById(R.id.btnNext)).setImageResource(R.drawable.ic_next);
                }
            }
        } else {
            getActivity().findViewById(R.id.btnNext).setClickable(false);
            ((ImageButton) getActivity().findViewById(R.id.btnNext)).setImageResource(R.drawable.ic_next_off);
        }
        getActivity().findViewById(R.id.btnBack).setVisibility(View.VISIBLE);
        getActivity().findViewById(R.id.btnSend).setEnabled(true);
        getActivity().findViewById(R.id.btnSend).setClickable(true);
        ((ImageButton) getActivity().findViewById(R.id.btnSend)).setImageResource(R.drawable.ic_send);
        getActivity().findViewById(R.id.btnReject).setClickable(true);
        ((ImageButton) getActivity().findViewById(R.id.btnReject)).setImageResource(R.drawable.ic_reject);
        getActivity().findViewById(R.id.btnVerified).setClickable(true);
        ((ImageButton) getActivity().findViewById(R.id.btnVerified)).setImageResource(R.drawable.ic_verified);


        getActivity().findViewById(R.id.btnClose).setClickable(true);
        if (questionSize == 0) {
//            loadQuestionForReview(Constant.listOfQuestion.size());
            loadReviewData();
        } else {
            showReviewScreen(true);
        }
//        qRecyclerView.smoothScrollToPosition(0);
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        jexlEngine = new JexlEngine(); //new JexlBuilder().create();
        if (getArguments() != null) {
            Bundle bundle = getArguments();
            mode = bundle.getInt(Global.BUND_KEY_MODE_SURVEY);
            isSimulasi = bundle.getBoolean(Global.BUND_KEY_MODE_SIMULASI);
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
    }

    @Override
    public void onSetLocationClick(QuestionBean bean, int group, int position) {
        focusBean = bean;
        focusGroup = group;
        focusPosition = position;
    }

    @Override
    public void onUpdateLocationClick(QuestionBean bean, int group, int position) {
        focusBean = bean;
        focusGroup = group;
        focusPosition = position;
    }

    @Override
    public void onEditDrawingClick(QuestionBean bean, int group, int position) {
        focusBean = bean;
        focusGroup = group;
        focusPosition = position;
    }

    @Override
    public void onCapturePhotoClick(QuestionBean bean, int group, int position) {
        focusBean = bean;
        focusGroup = group;
        focusPosition = position;
    }

    @Override
    public void onAcceptedAgreement(QuestionBean bean, int group, int position) {
        focusBean = bean;
        focusGroup = group;
        focusPosition = position;
    }

    @Override
    public void onLookupSelectedListener(QuestionBean bean, int group, int position) {
        //TODO: Set jawaban dari lookup
        focusBean = bean;
        focusGroup = group;
        focusPosition = position;
        if (bean.getQuestion_value() != null && !bean.getQuestion_value().isEmpty()) {
            try {
                CriteriaParameter criteriaParameter = GsonHelper.fromJson(bean.getQuestion_value(), CriteriaParameter.class);
                List<Parameter> parameters = criteriaParameter.getParameters();
                List<LookupAnswerBean> beanList = new ArrayList<>();
                for (Parameter parameter : parameters) {
                    String identifier = parameter.getRefId();
                    QuestionBean questionBean = listOfQuestion.get(identifier);
                    LookupAnswerBean answerBean = new LookupAnswerBean(questionBean);
                    answerBean.setCanEdit(parameter.getFlag().equals(Global.TRUE_STRING));
                    answerBean.setReadOnly(answerBean.isCanEdit());
                    beanList.add(answerBean);
                }
                bean.setLookupsAnswerBean(beanList);
            } catch (Exception e) {
                FireCrash.log(e);
                if (Global.IS_DEV)
                    e.printStackTrace();
            }
        }
        LookupFilterActivity.selectedBean = bean;
        Intent intent = new Intent(getActivity(), LookupFilterActivity.class);
        getActivity().startActivityForResult(intent, Global.REQUEST_LOOKUP_ANSWER);
    }

    @Override
    public void onReviewClickListener(QuestionBean bean, int group, int position) {
        if (TaskHDataAccess.STATUS_TASK_APPROVAL.equalsIgnoreCase(DynamicFormActivity.header.getStatus())
                || TaskHDataAccess.STATUS_TASK_APPROVAL_DOWNLOAD.equalsIgnoreCase(DynamicFormActivity.header.getStatus())
                || mode == Global.MODE_VIEW_SENT_SURVEY) {

        } else {
            isFinish = false;
            focusBean = bean;
            focusGroup = group;
            focusPosition = position;
            showReviewScreen(false);

            getActivity().findViewById(R.id.btnReject).setClickable(false);
            ((ImageButton) getActivity().findViewById(R.id.btnReject)).setImageResource(R.drawable.ic_reject_off);
            getActivity().findViewById(R.id.btnClose).setClickable(true);
            getActivity().findViewById(R.id.btnNext).setClickable(true);
            ((ImageButton) getActivity().findViewById(R.id.btnNext)).setImageResource(R.drawable.ic_next);
            getActivity().findViewById(R.id.btnSend).setClickable(false);
            ((ImageButton) getActivity().findViewById(R.id.btnSend)).setImageResource(R.drawable.ic_send_off);
            getActivity().findViewById(R.id.btnVerified).setClickable(false);
            ((ImageButton) getActivity().findViewById(R.id.btnVerified)).setImageResource(R.drawable.ic_verified_off);

            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    questionAdapter.expand(focusGroup);
                    int finalPosition = focusPosition + getCounterListBeforeGroup(focusGroup, false);
                    setFocusable(finalPosition, 800);
                }
            }, 200);
        }
    }

    @Override
    public void showOfflineMode(TextView textView, String message) {
        android.util.Log.i("Test_DSR", "Log dsr here");
        txtDsrOffline = textView;
        txtDsrOffline.setText(message);
        if (isTxtDsrOfflineShowed) {
            txtDsrOffline.setVisibility(View.VISIBLE);
        } else {
            txtDsrOffline.setVisibility(View.GONE);
        }
    }

    @Override
    public void viewTextValueDsr(EditText edtDsrValue) {
        edtValueDsr = edtDsrValue;
    }

    @Override
    public void viewButtonGetDsr(Button btnGetDsr) {
        buttonGetDsr = btnGetDsr;
    }

    private void showReviewScreen(boolean doShow) {
        if (doShow) {
            getActivity().setTitle("Question Review");
            rRecyclerView.setVisibility(View.VISIBLE);
            reviewAdapter = new QuestionReviewAdapter(getActivity(), rRecyclerView, listOfQuestionGroup, listOfQuestionBean, this);
            rRecyclerView.setAdapter(reviewAdapter);
            reviewAdapter.notifyDataSetChanged();
            qRecyclerView.setVisibility(View.GONE);
            if (Global.IS_DEV) {
                if (mainMenu != null)
                    if (mainMenu.findItem(R.id.mnPendingTask) != null && mode != Global.MODE_VIEW_SENT_SURVEY && !isSimulasi)
                        mainMenu.findItem(R.id.mnPendingTask).setVisible(true);
            }
            for (int i = 0; i <= reviewAdapter.getGroupItemCount(); i++) {
                reviewAdapter.expand(i);
            }
        } else {
            if (mainMenu != null)
                if (mainMenu.findItem(R.id.mnPendingTask) != null)
                    mainMenu.findItem(R.id.mnPendingTask).setVisible(false);
            getActivity().setTitle("Question Form");
            rRecyclerView.setVisibility(View.GONE);
            qRecyclerView.setVisibility(View.VISIBLE);
        }
        hideKeyboard();
    }

    private void setImageForDrawingQuestion(byte[] value) {
        QuestionBean mBean = focusBean;
        mBean.setImgAnswer(value);
        int position = focusPosition + getCounterListBeforeGroup(focusGroup, false);
        changeItem(position);
        Utility.freeMemory();
    }

    public void removeItem(int position) {
        QuestionBean bean = listOfQuestions.get(position);
        String newQuestionGroup = bean.getQuestion_group_name();
        List<QuestionBean> beanList = listOfQuestionBean.get(newQuestionGroup);
        if (beanList != null) {
//            if (bean.getAnswer_type().equals(Global.AT_TEXT_WITH_SUGGESTION) ||
//                    bean.getAnswer_type().equals(Global.AT_TEXT_WITH_SUGGESTION_NEW)) {
            if (bean.getAnswer_type().equals(Global.AT_TEXT_WITH_SUGGESTION)) {
                bean.setAnswer(null);
            } else if (bean.getAnswer_type().equals(Global.AT_TEXT_WITH_SUGGESTION_NEW)) {
                bean.setAnswer(null);
            }

            if (!beanList.isEmpty()) {
                beanList.remove(bean);
                listOfQuestionBean.put(newQuestionGroup, beanList);
            }
            if (beanList.isEmpty()) {
                listOfQuestionGroup.remove(newQuestionGroup);
                listOfQuestionBean.remove(newQuestionGroup);
                lastQuestionGroup = listOfQuestionGroup.get(listOfQuestionGroup.size() - 1);
            }
        } else {
            listOfQuestionGroup.remove(newQuestionGroup);
            listOfQuestionBean.remove(newQuestionGroup);
            lastQuestionGroup = listOfQuestionGroup.get(listOfQuestionGroup.size() - 1);
        }

        listOfQuestions.remove(position);
        removeQuestionLabel(position);
        position += listOfQuestionGroup.size();
        int lastPosition = questionAdapter.getItemCount() - 1;
        questionAdapter.notifyItemRemoved(lastPosition);
        questionAdapter.notifyDataSetChanged();
    }

    public void addItem(QuestionBean bean, int position, boolean fromDraft) {
        String newQuestionGroup = bean.getQuestion_group_name();
//        if(bean.getAnswer_type().equals(Global.AT_IMAGE_W_GPS_ONLY)||bean.getAnswer_type().equals(Global.AT_IMAGE_W_LOCATION)){
//            //place the feature of refreshGPS manu here for auto refresh when answerType is AT_IMAGE_W_GPS_ONLY
//            if (Global.LTM != null) {
//                if (Global.LTM.isConnected) {
//                    Global.LTM.removeLocationListener();
//                    Global.LTM.connectLocationClient();
//                } else {
//                    CheckInManager.startGPSTracking(getActivity());
//                }
//            }
//        }
        if (lastQuestionGroup == null || !newQuestionGroup.equals(lastQuestionGroup)) {
            lastQuestionGroup = newQuestionGroup;
            listOfQuestionGroup.add(lastQuestionGroup);
            List<QuestionBean> beanList = listOfQuestionBean.get(lastQuestionGroup);
            if (beanList != null) {
                beanList.add(bean);
            } else {
                beanList = new ArrayList<>();
                beanList.add(bean);
            }
            listOfQuestionBean.put(lastQuestionGroup, beanList);
        } else {
            List<QuestionBean> beanList = listOfQuestionBean.get(lastQuestionGroup);
            if (beanList != null) {
                beanList.add(bean);
            } else {
                beanList = new ArrayList<>();
                beanList.add(bean);
            }
            listOfQuestionBean.put(lastQuestionGroup, beanList);
        }
        position += listOfQuestionGroup.size();
        listOfQuestions.add(bean);
        if (!fromDraft) {
            notifyInsert(position);
            try {
                int groupIdx = listOfQuestionGroup.indexOf(lastQuestionGroup);
                focusGroup = groupIdx;
                questionAdapter.expand(groupIdx);
            } catch (Exception e) {
                FireCrash.log(e);
            }
        }
    }

    public void notifyInsert(int position) {
        questionAdapter.notifyItemInserted(position);
    }

    public void changeItem(int position) {
        questionAdapter.notifyItemChanged(position);
    }

    private void focusToView(int position) {
        ScrollingLinearLayoutManager layoutManager = (ScrollingLinearLayoutManager) qRecyclerView.getLayoutManager();
        layoutManager.scrollToPosition(position);
//        layoutManager.smoothScrollToPosition(qRecyclerView, null, position);
    }

    private void setFocusable(final int position, final int duration) {
        focusToView(position);
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                RecyclerView.ViewHolder view = qRecyclerView.findViewHolderForAdapterPosition(position);
                if (view instanceof TextQuestionViewHolder) {
                    ((TextQuestionViewHolder) view).mQuestionAnswer.requestFocus();
                    ((TextQuestionViewHolder) view).mQuestionAnswer.setFocusable(true);

                    InputMethodManager keyboard = (InputMethodManager)
                            getActivity().getSystemService(Context.INPUT_METHOD_SERVICE);
                    keyboard.showSoftInput(((TextQuestionViewHolder) view).mQuestionAnswer, 0);
                } else if (view instanceof TextWithSuggestionQuestionViewHolder) {
                    ((TextWithSuggestionQuestionViewHolder) view).mQuestionAnswer.requestFocus();
                    ((TextWithSuggestionQuestionViewHolder) view).mQuestionAnswer.setFocusable(true);

                    InputMethodManager keyboard = (InputMethodManager)
                            getActivity().getSystemService(Context.INPUT_METHOD_SERVICE);
                    keyboard.showSoftInput(((TextWithSuggestionQuestionViewHolder) view).mQuestionAnswer, 0);
                } else {
                    hideKeyboard();
                }
            }
        }, duration);
    }

    public void hideKeyboard() {
        try {
            InputMethodManager keyboard = (InputMethodManager)
                    getActivity().getSystemService(Context.INPUT_METHOD_SERVICE);
            keyboard.hideSoftInputFromWindow(getActivity().getCurrentFocus().getWindowToken(), 0);
        } catch (Exception e) {
            FireCrash.log(e);
            if (Global.IS_DEV)
                e.printStackTrace();
        }
    }

    //TODO: Update Validation by General Setting
    private boolean validateAllMandatory(boolean displayMessage) {
        boolean result = true;
        messages = "";
        mapImgQuest = new HashMap();

        for (QuestionBean bean : listOfQuestions) {
            boolean isHaveAnswer = QuestionBean.isHaveAnswer(bean);

            //Todo: Remove from production
//            if (bean.getIdentifier().equalsIgnoreCase("SVY_DP_BAYAR")) {
//                System.out.println("DP Bayar: " + bean.getAnswer());
//            }

            //Check Image Question to RuleEngine
            if (isImageQuestion(bean) || QuestionViewAdapter.IsDrawingQuestion(Integer.parseInt(bean.getAnswer_type()))) {
                mapImgQuest.put(bean.getIdentifier_name(), isHaveAnswer ? "1" : "0");
            }

            //Nendi: 12/02/2018 | Exclude Image Question
            if (bean.getIs_mandatory().equals(Global.TRUE_STRING) && bean.getIs_visible().equals(Global.TRUE_STRING)) {
                if (Constant.productOff != null && !isHaveAnswer && !(isImageQuestion(bean) || QuestionViewAdapter.IsDrawingQuestion(Integer.parseInt(bean.getAnswer_type())))) {

                    //Jowoen: 05/11/2024 | Exclude question registration check from validation.
                    if (!(isRegistrationCheckQuestion(bean) || !QuestionViewAdapter.isRegistrationCheckQuestion(Integer.parseInt(bean.getAnswer_type())))) {
                        messages = messages.concat(bean.getQuestion_label()
                                + " " + getString(R.string.msgRequired) + "\n");
                        result = false;
                    }
                } else if (!isHaveAnswer) {
                    messages = messages.concat(bean.getQuestion_label()
                            + " " + getString(R.string.msgRequired) + "\n");
                    result = false;
                }
            }
        }

        //TODO:  Move to RuleLogic
        //Validate Image Question using Rule Engine
        if (mapImgQuest.size() > 0 && Constant.productOff != null) {
//            GenericRuleJexlLogic logic  = new GenericRuleJexlLogic();
            GeneralParameter tcParam = GeneralParameterDataAccess.getOne(getContext(), GlobalData.getSharedGlobalData().getUser().getUuid_user(), Global.GS_TC_PARAM);

            if (tcParam != null && (tcParam.getGs_value() != null || !tcParam.getGs_value().equalsIgnoreCase("0"))) {
                RuleParam ruleParam = GsonHelper.fromJson(tcParam.getGs_value(), RuleParam.class);
                RuleLogic ruleEngine = new GenericRuleJexlLogic();
                List<String> listMandatoryTc = ruleEngine.validateTC(getContext(), ruleParam);

                if (listMandatoryTc != null) {
                    List<String> ids = new ArrayList<>();
                    for (String tcCode : listMandatoryTc) {
                        if ("0".equals(mapImgQuest.get(tcCode))) {
                            ids.add(tcCode);
                            messages = messages.concat(Constant.listOfQuestion.get(tcCode).getQuestion_label()
                                    + " " + getString(R.string.msgRequired) + "\n");
                            result = false;
                        }
                    }

                    QuestionBean qBean = null;
                    if (!ids.isEmpty()) {
                        for (QuestionBean q : listOfQuestions) {
                            if (q.getIdentifier_name().equalsIgnoreCase(ids.get(0))) {
                                qBean = q;
                                String groupName = qBean.getQuestion_group_name();
                                focusGroup = listOfQuestionGroup.indexOf(groupName);
                                focusPosition = listOfQuestionBean.get(groupName).indexOf(qBean);
                                focusBean = qBean;
                                break;
                            }
                        }
                    }
                }
            }
        }

        return result;
    }

    private boolean isImageQuestion(QuestionBean bean) {
        return QuestionViewAdapter.IsImageQuestion(Integer.valueOf(bean.getAnswer_type()));
    }

    private boolean isRegistrationCheckQuestion(QuestionBean bean) {
        return QuestionViewAdapter.isRegistrationCheckQuestion(Integer.valueOf(bean.getAnswer_type()));
    }

    private boolean doNext(boolean validate) {
        boolean result = false;
        Utility.freeMemory();

        if (DynamicFormActivity.isApproval) {
            //FIXME
        } else {
            if (validateCurrentPage(validate, false)) {
                while (loadQuestionForm()) {
                    isFinish = false;
                    if (listOfQuestion.size() == questionSize) {
                        DynamicFormActivity.isApproval = false;
                        isFinish = true;
                        if (Global.IS_DEV)
                            if (mainMenu != null && mode != Global.MODE_VIEW_SENT_SURVEY && !isSimulasi)
                                mainMenu.findItem(R.id.mnPendingTask).setVisible(true);
                        if (validate) {
                            showFinishScreen();
                        } else {
                            if (this.validateCurrentPage(true, false)) {
                                showFinishScreen();
                            }
                        }
                        break;
                    } else {
                        loadQuestionForm();
                        break;
                    }
                }
                result = true;
            } else {
                DynamicFormActivity.isApproval = false;
                result = false;
            }
        }
        /*int lastposition = listOfQuestions.size() - 1;
        List<String> errMessage = questionsValidator.validateGeneratedQuestionView(listOfQuestions.get(lastposition));
        if (errMessage != null && errMessage.size() > 0) {
            String[] err = errMessage.toArray(new String[errMessage
                    .size()]);
            String alert = Tool.implode(err, "\n");

            Toast.makeText(getActivity(), alert, Toast.LENGTH_SHORT).show();

        } else {
            loadQuestionForm();
        }*/
        return result;
    }

    @SuppressLint("StaticFieldLeak")
    private void doSave(final int mode) {
        new AsyncTask<Void, Void, Boolean>() {
            boolean isOK = true;
            boolean isSuccess = false;
            private ProgressDialog progressDialog;

            @Override
            protected void onPreExecute() {
                this.progressDialog = ProgressDialog.show(getActivity(), "", getString(R.string.progressWait), true);
            }

            @Override
            public Boolean doInBackground(Void... params) {
                if (!isFinish) {
                    isOK = validateCurrentPage(true, true);
                }
                if (isOK) {
                    try {
                        DynamicFormActivity.header.setStatus(TaskHDataAccess.STATUS_SEND_SAVEDRAFT);
                        String uuidLastQuestion = listOfQuestions.get(listOfQuestions.size() - 1).getUuid_question_set();
                        isSuccess = new TaskManager().saveTask(getActivity(), mode, DynamicFormActivity.header, listOfQuestions, uuidLastQuestion, false);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        isOK = false;
                        ACRA.getErrorReporter().putCustomData("errorSaveTask", "Pernah error saat save Task");
                        ACRA.getErrorReporter().putCustomData("errorSaveTaskTime", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                        ACRA.getErrorReporter().handleSilentException(e);
                    }
                }
                return isOK;
            }

            @Override
            protected void onPostExecute(Boolean result) {
                if (progressDialog.isShowing()) {
                    try {
                        progressDialog.dismiss();
                    } catch (Exception e) {
                        FireCrash.log(e);
                        if (Global.IS_DEV)
                            e.printStackTrace();
                    }
                }

                if (result && isSuccess) {
                    try {
                        //TODO: Comment kalo mau pake Save on Page, ga keluar dari Form Pertanyaan
                        if (!isSimulasi) {
                            DynamicFormActivity.header = null;
                            getActivity().finish();
                        }
                        //-------------------------------------
                        //TODO: Uncomment kalo mau pake Save on Page, ga keluar dari Form Pertanyaan
                        /**runOnUiThread(new Runnable() {
                        @Override public void run() {
                        Toast.makeText(getActivity(),getString(R.string.data_saved), Toast.LENGTH_SHORT).show();
                        }
                        });*/
                    } catch (Exception e) {
                        FireCrash.log(e);
                        getActivity().finish();
                    }
                } else {
                    try {
                        getActivity().runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                Toast.makeText(getActivity(), getString(R.string.data_saved_failed), Toast.LENGTH_SHORT).show();
                            }
                        });
                    } catch (Exception e) {
                        FireCrash.log(e);
                        getActivity().finish();
                    }
                }
            }
        }.execute();
    }

    private boolean validateCurrentPage(boolean isCekValidate, boolean isSave) {
        List<String> errMessage = new ArrayList<String>();
        boolean isSkipLastQuestValid = false;
        if (isCekValidate) {
            for (int i = 0; i < listOfQuestions.size(); i++) {
                try {
                    QuestionBean qBean = listOfQuestions.get(i);
                    String relevantMandatory = qBean.getRelevant_mandatory();
                    if (relevantMandatory == null) relevantMandatory = "";

                    qBean.setRelevantMandatory(isQuestVisibleIfRelevantMandatory(relevantMandatory, qBean));

                    if (qBean.isChange() || Global.TAG_RESULT.equals(qBean.getTag())) {
                        if (qBean.isRelevanted() || qBean.isRelevantMandatory()) {
                            qBean.setChange(false);
                            if(qBean.getAffectedQuestionBeanCopyValue()!=null){
                                if(!qBean.getAffectedQuestionBeanCopyValue().isEmpty()){
                                    for(int idx=0; idx<qBean.getAffectedQuestionBeanCopyValue().size();idx++){
                                        QuestionBean qBeanAffected = qBean.getAffectedQuestionBeanCopyValue().get(idx);
                                        copyQuestionValueAffectedBean(qBeanAffected, qBeanAffected.getQuestion_value());
                                        if(qBeanAffected.getAnswer()!=null && !"".equals(qBeanAffected.getAnswer())){
                                                qBeanAffected.setIntTextAnswer(qBeanAffected.getAnswer());
                                        }
                                    }
                                }
                            }

                            if (qBean.getAffectedQuestionBeanOptions() != null && !qBean.getAffectedQuestionBeanOptions().isEmpty()) {
                                for(int idx=0; idx<qBean.getAffectedQuestionBeanOptions().size();idx++){
                                    QuestionBean qBeanAffected = qBean.getAffectedQuestionBeanOptions().get(idx);
                                    if (Global.AT_DSR.equalsIgnoreCase(qBeanAffected.getAnswer_type())) {
                                        qBeanAffected.setAnswer("");
                                        qBeanAffected.setIntTextAnswer("");
                                    }
                                }
                            }

                            if (qBean.getAffectedQuestionBeanChoiceFilterFields() != null && !qBean.getAffectedQuestionBeanChoiceFilterFields().isEmpty()) {
                                for(int idx=0; idx<qBean.getAffectedQuestionBeanChoiceFilterFields().size();idx++){
                                    QuestionBean qBeanAffected = qBean.getAffectedQuestionBeanChoiceFilterFields().get(idx);
                                    qBeanAffected.setAnswer("");
                                    qBeanAffected.setIntTextAnswer("");
                                }
                            }

                            boolean isLastQuestion = true;
                            if (Global.AT_GET_OTR.equals(qBean.getAnswer_type())) {
                                questionAdapter.notifyDataSetChanged();
                            }
                            for (int x = (listOfQuestions.size() - 1); x > i; x--) {
                                if (!isSave) {
                                    if (QuestionViewAdapter.IsLookupQuestion(listOfQuestions.get(x).getAnswer_type())) {
                                        QuestionBean.resetAnswer(listOfQuestions.get(x));
                                        removeQuestionLabel(x);
                                    }
                                    if (!Global.AT_GET_OTR.equals(qBean.getAnswer_type())) {
                                        removeItem(x);
                                    }
                                } else {
                                    Handler handler = new Handler(Looper.getMainLooper());
                                    final int finalX = x;
                                    handler.post(new Runnable() {
                                        public void run() {
                                            removeItem(finalX);
                                        }
                                    });
                                }
                                isLastQuestion = false;
                            }
                            if (isSave && !isLastQuestion) {
                                String err2 = getString(R.string.save_on_relevant);
                                errMessage.add(err2);
                            }
                        } else {
                            qBean.setChange(false);
                        }
                    }

                    //Nendi: 23/01/2018 | Add context needed by dao for validation from blacklist
                    List<String> err = questionsValidator.validateGeneratedQuestionView(getContext(), qBean);
                    if (err != null && !err.isEmpty()) {
                        errMessage.addAll(err);
                        if (Global.TAG_VALIDATION_CHECK.equals(qBean.getTag())) {
                            if (!errMessage.isEmpty() && isCekValidate) {
                                String[] msg = errMessage.toArray(new String[errMessage.size()]);
                                final String message = Tool.implode(msg, "\n");

                                final NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(getActivity());
                                builder.withTitle(getString(R.string.info_capital))
                                        .withMessage(message)
                                        .withButton1Text(getString(R.string.btnOk))
                                        .setButton1Click(new View.OnClickListener() {
                                            @Override
                                            public void onClick(View v) {
                                                builder.dismiss();
                                            }
                                        })
                                        .isCancelable(false)
                                        .isCancelableOnTouchOutside(false)
                                        .show();

                                return false;
                            }
                        }
                    }


//                    if (qBean.isMandatory()) {
//                        qBean.setChange(false);
//                        boolean isLastQuestion = true;
//                        for (int x = (listOfQuestions.size() - 1); x > i; x--) {
//                            if (!isSave) {
//                                removeItem(x);
//                            } else {
//                                Handler handler = new Handler(Looper.getMainLooper());
//                                final int finalX = x;
//                                handler.post(new Runnable() {
//                                    public void run() {
//                                        removeItem(finalX);
//                                    }
//                                });
//                            }
//                            isLastQuestion = false;
//                        }
//                        if (isSave && !isLastQuestion) {
//                            String err2 = getString(R.string.save_on_relevant);
//                            errMessage.add(err2);
//                        }
//                    }

                    if (isSkipLastQuestValid) {
                        isSkipLastQuestValid = false;
                    } else {
//                        List<String> err = questionsValidator.validateGeneratedQuestionView(qBean);
//                        if (err != null && err.size() > 0) errMessage.addAll(err);

                    }

                } catch (Exception e) {
                    FireCrash.log(e);
                    if (Global.IS_DEV)
                        e.printStackTrace();
                }
            }
        } else {
            for (int i = 0; i < listOfQuestions.size(); i++) {
                try {
                    QuestionBean qBean = listOfQuestions.get(i);
                    boolean answer = QuestionBean.isHaveAnswer(qBean);
                    if (!answer) {        //tidak ada isi
                        if (qBean.isMandatory() || qBean.isRelevantMandatory()) {
                            if (DynamicFormActivity.isVerified || DynamicFormActivity.isApproval) {
                                if (!Tool.isImage(qBean.getAnswer_type())) {
                                    String err = qBean.getQuestion_label() + " " + getString(R.string.msgRequired);
                                    errMessage.add(err);
                                }
                            } else {
                                String err = qBean.getQuestion_label() + " " + getString(R.string.msgRequired);
                                errMessage.add(err);
                            }
                        }
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    if (Global.IS_DEV)
                        e.printStackTrace();
                }
            }
        }


        if (!errMessage.isEmpty() && isCekValidate) {
            String[] msg = errMessage.toArray(new String[errMessage
                    .size()]);
            final String alert = Tool.implode(msg, "\n");
            if (!DynamicFormActivity.isApproval) {
                getActivity().runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        Toast.makeText(getActivity(), alert, Toast.LENGTH_SHORT).show();
                    }
                });
            }
            return false;
        }
        ScrollingLinearLayoutManager layoutManager = (ScrollingLinearLayoutManager) qRecyclerView.getLayoutManager();
        layoutManager.setScrollEnable(true);
        return true;
    }
    public boolean copyQuestionValueAffectedBean(QuestionBean mBean, String valueScript) {
        String convertedExpression = valueScript.trim();
        if (convertedExpression == null || convertedExpression.length() == 0) {
            return true;
        } else {
            boolean isCopyFromLookup = false;
            boolean isCopyWithCondition = false;
            try {
                if (convertedExpression.contains("copyFromLookup("))
                    isCopyFromLookup = convertedExpression.substring(0, 15).equals("copyFromLookup(");
            } catch (Exception e) {
                FireCrash.log(e);
                isCopyFromLookup = false;
                if (Global.IS_DEV)
                    e.printStackTrace();
            }
            try {
                isCopyWithCondition = convertedExpression.substring(0, 5).equals("copy(");
            } catch (Exception e1) {
                isCopyWithCondition = false;
                if (Global.IS_DEV)
                    e1.printStackTrace();
            }

            if (isCopyFromLookup) {
                return copyValueFromLookup(mBean, convertedExpression);
            } else if(isCopyWithCondition) {
                return copyValueWithCondition(mBean, convertedExpression, false);
            } else {
                return copyValue(mBean, convertedExpression);
            }
        }
    }
    public void removeQuestionLabel(int position) {
        if (DynamicQuestionActivity.questionLabel.size() > position)
            DynamicQuestionActivity.questionLabel.remove(DynamicQuestionActivity.questionLabel.size() - 1);
        DynamicQuestionActivity.adapter.notifyDataSetChanged();
    }

    public void addQuestionLabel(String label) {
        DynamicQuestionActivity.questionLabel.add(label);
        DynamicQuestionActivity.adapter.notifyDataSetChanged();
    }

//    private boolean isSpecialCaseChangeFlag(QuestionBean bean){
//        String tempAnswer;
//        if(bean.getAnswer_type().equals(Global.AT_TEXT_MULTILINE) || bean.getAnswer_type().equals(Global.AT_TEXT)){
//            if(bean.isRelevanted()){
//                tempAnswer = bean.getAnswer();
//                copyQuestionValue(bean, bean.getQuestion_value());
//                if(!tempAnswer.equals(bean.getAnswer())){
//                    return true;
//                }
//            }
//        }else if(bean.getAnswer_type().equals(Global.AT_CURRENCY)){
//            if(bean.isRelevanted()){
//                tempAnswer = bean.getAnswer();
//                calculateQuestionBean(bean);
//                bean.setAnswer(Tool.deleteAll(bean.getAnswer(), ","));
//                if(bean.getAnswer().contains(".")){
//                    String[] intAnswer = Tool.split(bean.getAnswer(), ".");
//                    if (intAnswer.length > 1) {
//                        if (intAnswer[1].equals("00"))
//                            bean.setAnswer(intAnswer[0]);
//                        else {
//                            bean.setAnswer(tempAnswer);
//                        }
//                    } else {
//                        bean.setAnswer(tempAnswer);
//                    }
//                }
//                if(!tempAnswer.equals(bean.getAnswer())){
//                    return true;
//                }
//            }
//        }
//        return false;
//    }

    private void calculateQuestionBean(QuestionBean bean) {
        if (null != bean.getCalculate() && !"".equalsIgnoreCase(bean.getCalculate())) {
            String resultCalculate = doCalculate(bean);
            if (bean.getAnswer_type().equals(Global.AT_DECIMAL) || bean.getAnswer_type().equals(Global.AT_CURRENCY)) {
                try {
                    NumberFormat nf = NumberFormat.getInstance(Locale.US);
                    Double finalAnswer = nf.parse(resultCalculate).doubleValue();
                    resultCalculate = String.format(Locale.US, "%,.2f", finalAnswer);
                } catch (Exception e) {
                    FireCrash.log(e);
                    e.printStackTrace();
                    resultCalculate = bean.getAnswer();
                }
            }

            bean.setAnswer(resultCalculate);
        }
    }

    private void loadSingleQuestion(QuestionBean bean, int position, boolean isThreadActive) {
        bean.setVisible(true);

        //2022-04-22 RYE: penyesuaian maxRetry Untuk Submit Layer
        if (Global.AT_SUBMIT_LAYER.equalsIgnoreCase(bean.getAnswer_type()) && bean.getMaxRetry() == 0) {
            bean.setMaxRetry(this.maxRetry);
        }
        // Set Max Retry for get dsr with gsValue
        if (Global.AT_DSR.equalsIgnoreCase(bean.getAnswer_type()) && bean.getMaxRetry() == 0) {
            bean.setMaxRetry(this.maxRetryDsr);
        }
        // Set Max Retry for Invitation E-sign and Check Registration
        if (Global.AT_INVITATION_ESIGN.equalsIgnoreCase(bean.getAnswer_type()) && bean.getMaxRetry() == 0) {
            bean.setMaxRetry(this.maxRetryInvitationEsign);
        }
        if (Global.AT_REGISTRATION_CHECK.equalsIgnoreCase(bean.getAnswer_type()) && bean.getMaxRetry() == 0) {
            bean.setMaxRetry(this.maxRetryRegistrationEsign);
        }
        if (Global.AT_VALIDATION_CHECK.equalsIgnoreCase(bean.getAnswer_type()) && bean.getMaxRetry() == 0) {
            bean.setMaxRetry(this.maxRetryValidationCheck);
        }

        if (null != bean.getIntTextAnswer() && !"".equalsIgnoreCase(bean.getIntTextAnswer()) &&
                (bean.getAnswer() == null || "".equalsIgnoreCase(bean.getAnswer()))) {
            bean.setAnswer(bean.getIntTextAnswer());
        }

        if (Tool.isOptions(bean.getAnswer_type())) {
            //NENDI: 22/02/2018 | Add Default Option Answer
            List<OptionAnswerBean> options = new ArrayList<>();
            String atType = bean.getAnswer_type();
            if (atType.equalsIgnoreCase(Global.AT_DROPDOWN) || atType.equalsIgnoreCase(Global.AT_LOOKUP_TABLE)) {
                OptionAnswerBean defaultOption = new OptionAnswerBean("$$$", "Pilih Salah Satu");
                options.add(defaultOption);
            }

            // NENDI: 24/07/2018 | Add Lookup from @TABLE using Dynamic Query
            // JOWOEN: 21/05/2025 | Fix POT not autofill and empty
            try {
                List<String> prodCode = new ArrayList<>();
                Query query = finalQuery(bean.getChoice_filter());

                if (query.getTable().equalsIgnoreCase("MS_PO")) {
                    if (Global.PRE_PROD_OFF_TYPE.equals(bean.getIdentifier_name())) {
                        String str = StringUtils.EMPTY;
                        if (null != bean.getSelectedOptionAnswers() && !bean.getSelectedOptionAnswers().isEmpty()) {
                            str = bean.getSelectedOptionAnswers().get(0).getCode();
                        }

                        for (OptionAnswerBean optionAnswerBean: PODataAccess.lookup(getContext(), query)) {
                            prodCode.add(optionAnswerBean.getCode());
                        }

                        if (!prodCode.contains(str)) {
                            if (null == bean.getAnswer()) {
                                PROD_OFF_CODE = str;
                            } else if (!"Pilih Salah Satu".equals(bean.getAnswer())) {
                                if (null == bean.getLovCode()) {
                                    PROD_OFF_CODE = bean.getAnswer();
                                } else {
                                    PROD_OFF_CODE = bean.getLovCode();
                                }
                            }
                            GetTableOnDemand getTableOnDemand = new GetTableOnDemand(bean, isThreadActive, position, PROD_OFF_CODE);
                            getTableOnDemand.execute();
                        } else {
                            options.addAll(PODataAccess.lookup(getContext(), query));
                        }
                    } else {
                        options.addAll(PODataAccess.lookup(getContext(), query));
                    }
                } else if (query.getTable().equals("MS_ASSET_SCHEME")) {
                    options.addAll(AssetSchemeDataAccess.lookup(getContext(), query));
                } else {
                    options.addAll(POAssetDataAccess.lookup(getContext(), query));
                }

                bean.setOptionAnswers(options);
            } catch (Exception ex) {
                try {
                    List<OptionAnswerBean> optionAnswerBeans = new ArrayList<>();
                    if (!Global.REF_PRE_SOA.equals(bean.getIdentifier_name())) {
                        optionAnswerBeans = getOptionsForQuestion(bean, true);
                    }

                    if (optionAnswerBeans.size() > 0) {
                        options.addAll(optionAnswerBeans);
                        bean.setOptionAnswers(options);
                    } else {
                        statusSyncParamConstraints = true;
                        GetLookupOnDemand getLookupOnDemand = new GetLookupOnDemand(bean, bean.getLov_group(), filterData, filterData.size(), isThreadActive, position);
                        getLookupOnDemand.execute();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            //NENDI: 22/01/2018 | Add Lookup from @TABLE using Dynamic Query
//            if (bean.getAnswer_type().equalsIgnoreCase(Global.AT_LOOKUP_TABLE)) {
//                Query query  = finalQuery(bean.getChoice_filter());
//                if (query.getTable().equalsIgnoreCase("MS_PO")) {
//                    options.addAll(PODataAccess.lookup(getContext(), query));
//                } else {
//                    options.addAll(POAssetDataAccess.lookup(getContext(), query));
//                }
//
//                bean.setOptionAnswers(options);
//            } else {
//                try {
//                    options.addAll(getOptionsForQuestion(bean, true));
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//
//                bean.setOptionAnswers(options);
//            }
        } else {
            if(QuestionViewAdapter.IsTextQuestion(bean.getAnswer_type()) &&
                    bean.getChoice_filter()!=null && bean.getChoice_filter().contains("fields")) {
                try {
                    Query query = finalQuery(bean.getChoice_filter());
                    if (query.getTable().equalsIgnoreCase("MS_MARKET_PRICE")
                            && StringUtils.isBlank(bean.getAnswer())) {
                        MarketPrice price = MarketPriceDataAccess.findByQuery(getActivity(), query.getConstraint());
                        if (price != null) {
                            bean.setAnswer(String.format("%.0f",price.getMarket_price()));
                            marketPrice = price.getMarket_price().doubleValue();
                            tolerancePrctg = price.getTolerance_prctg().doubleValue();
                        } else {
                            Toast.makeText(getActivity(), bean.getQuestion_label() + " : " + getActivity().getString(R.string.market_price_not_found), Toast.LENGTH_SHORT).show();
                        }
                    }
                } catch (Exception e) {
                    if(Global.IS_DEV) {
                        e.printStackTrace();
                    }
                }
            }
        }

        if (bean.getAnswer_type().equals(Global.AT_LOOKUP) && (bean.getAnswer() == null || bean.getAnswer().equals(""))) {
            statusSyncParamConstraints = true;
            List<LookupAnswerBean> beanList = bean.getLookupsAnswerBean();
            List<Parameter> parameters = new ArrayList<>();
            if (beanList != null && !beanList.isEmpty()) {
                for (LookupAnswerBean answerBean : beanList) {
                    String answer = QuestionBean.getAnswer(answerBean);
                    Parameter parameter = new Parameter();
                    parameter.setRefId(answerBean.getIdentifier_name());
                    parameter.setAnswer(answer);
                    parameters.add(parameter);
                }
            } else {
                Parameter parameter = new Parameter();
                parameter.setAnswer("");
                parameters.add(parameter);
            }
            CriteriaParameter criteriaParameter = new CriteriaParameter();
            criteriaParameter.setParameters(parameters);
            GetOneLuMaskapai task = new GetOneLuMaskapai(getActivity(), bean, criteriaParameter, position, isThreadActive);
            task.execute();
        }

        if (!statusSyncParamConstraints) {
            checkHasDefaultAnswer(bean, isThreadActive); //Check has Default Answer
            calculateQuestionBean(bean);

            // TODO Add new OptionAnswerFromTable
            addQuestionLabel(bean.getQuestion_label() + " " + Global.DELIMETER_ROW + " " + bean.getQuestion_group_name());
            addItem(bean, position, isThreadActive);
            isNotThreadActive(bean, isThreadActive);
        }
    }

    private void isNotThreadActive(QuestionBean bean, boolean isThreadActive) {
        if (!isThreadActive) {
            if (!bean.isRelevanted() && !hasLoading) {
                if (!bean.isMandatory() && !needStop) {
                    if (validateCurrentPage(true, false))
                        loadQuestionForm();
                } else if (QuestionBean.isHaveAnswer(bean) && !needStop) {
                    if (!Tool.isOptions(bean.getAnswer_type()) || bean.isReadOnly()) {
                        if (validateCurrentPage(true, false))
                            loadQuestionForm();
                    }
                }
                needStop = true;
            }
        }
    }

    private void checkHasDefaultAnswer(QuestionBean bean, boolean isThreadActive) {
        if (bean.getQuestion_value() != null && !bean.getQuestion_value().isEmpty() && !Global.TAG_URL_LINK.equalsIgnoreCase(bean.getTag() /*&& !QuestionViewAdapter.IsLookupQuestion(bean.getAnswer_type())*/)) {
            try {
                //Add Default Value from Rule Engine based on Rules Parameter
                if (bean.getQuestion_value().contains("field")) { //from database query
                    Query query = finalQuery(bean.getQuestion_value());

                    if (query.getField() != null) {
                        switch (query.getField()) {
                            case "MARGIN":
                                bean.setMax_length(5);
                                double value = IndustryDataAccess.getByType(getContext(), query);
                                String resultMargin = String.format(Locale.US, "%,.2f", value);
                                if (bean.getAnswer_type().equals(Global.AT_DECIMAL)) {
                                    try {
                                        NumberFormat nf = NumberFormat.getInstance(Locale.US);
                                        Double finalAnswer = nf.parse(resultMargin).doubleValue();
                                        resultMargin = finalAnswer.toString();
                                        bean.setAnswer(resultMargin);
                                        Logger.i("INFO", "Margin : " + resultMargin);
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                }
                                break;
                        }
                    }
                } else { //from rule engine
                    RuleParam ruleParam = GsonHelper.fromJson(bean.getQuestion_value(), RuleParam.class);
                    switch (ruleParam.getFile()) {
                        case "TENOR":
                            break;
                        case "OTR":
                            getOTR(bean, ruleParam);
                            break;
                    }
                }
            } catch (Exception ex) {
                boolean isCopyFromLookup = false;
                boolean isCopyValue = false;
                boolean isCopyValueFromIdentifier = false;
                try {
                    if (bean.getQuestion_value().contains("copyFromLookup(")) {
                        isCopyFromLookup = bean.getQuestion_value().substring(0, 15).equals("copyFromLookup(");
                        isCopyValue = bean.getQuestion_value().substring(0, 4).equals("copy");
                        isCopyValueFromIdentifier = bean.getQuestion_value().substring(0, 1).equals("{");
                    } //Nendi: 24/05/2018 - Add method lov(lov_group, code)
                    else if (bean.getQuestion_value().contains("lov(") && !QuestionBean.isHaveAnswer(bean)) {
                        String strFun = bean.getQuestion_value();
                        int idxOpenAt = strFun.indexOf("(");

                        if (idxOpenAt != -1) {
                            int idxCloseAt = strFun.indexOf(")");
                            String strPrm = strFun.substring(idxOpenAt + 1, idxCloseAt);
                            String[] params = Tool.explode(strPrm, Global.DELIMETER_DATA3);

                            List<OptionAnswerBean> selectedOptionAnswers = new ArrayList<OptionAnswerBean>();
                            Lookup defaultAnswer = LookupDataAccess.getOneByCodeAndlovGroup(
                                    getContext(), params[0].trim(), params[1].trim()
                            );

                            if (defaultAnswer != null) {
                                OptionAnswerBean selectedOption = new OptionAnswerBean(defaultAnswer);
                                selectedOption.setSelected(true);
                                selectedOptionAnswers.add(selectedOption);

                                //Set default selected answers
                                bean.setSelectedOptionAnswers(selectedOptionAnswers);
                            }
                        }
                    }
                } catch (Exception e) {
                    isCopyFromLookup = false;
                    if (Global.IS_DEV)
                        e.printStackTrace();
                }

                if (bean.isReadOnly() || !QuestionBean.isHaveAnswer(bean) ||
                        (isCopyFromLookup) || (isCopyValue) || (isCopyValueFromIdentifier)) {
                    String script = bean.getQuestion_value();
                    if (!(script.contains("{\"parameters\":[") && script.contains("]}")) &&
                            !copyQuestionValue(bean, bean.getQuestion_value(), false)) {
                        if (Global.IS_DEV && !isThreadActive)
                            Toast.makeText(getActivity(), bean.getQuestion_label() + " : " + getActivity().getString(R.string.copy_value_failed), Toast.LENGTH_SHORT).show();
                    }
                }
            }
        }
    }

    public static Query finalQuery(String tmpQuery) {
        Query query = GsonHelper.fromJson(tmpQuery, Query.class);

        //Validate Constraint
        if (null != query.getConstraint()) {
            int size = query.getConstraint().size();
            if (size != 0) {
                List<Query.Constraint> constraints = new ArrayList<>();
                for (Query.Constraint constraint : query.getConstraint()) {
                    String[] params;
                    if (constraint.getValue().contains("[")) {
                        //2018-05-24 | Add Data delimiter support
                        String value = constraint.getValue();
                        String field = constraint.getColumn();
                        if (value.contains(",")) {
                            String[] params2 = Tool.split(value, Global.DELIMETER_DATA3);
                            String[] finalParams = new String[params2.length];

                            int idx = 0;
                            for (String param : params2) {
                                int idxOpenAt = param.indexOf("[");
                                if (idxOpenAt != -1) {
                                    int idxCloseAt = param.indexOf("]");
                                    String identifier = param.substring(idxOpenAt + 1, idxCloseAt);
                                    QuestionBean qBean = listOfQuestion.get(identifier);
                                    qBean.setRelevanted(true);
//                                    String finalValue = QuestionBean.getAnswer(qBean);
                                    finalParams[idx] = QuestionBean.getAnswer(qBean);
                                } else {
                                    finalParams[idx] = param;
                                }
                                idx++;
                            }

                            constraint.setValue(Tool.implode(finalParams, Global.DELIMETER_DATA3));
                            constraints.add(constraint);
                            //Add final params to constraint value
//                            constraint.append(Tool.implode(finalParams, Global.DELIMETER_DATA3));
                        } else {
                            params = new String[]{"[", "]"};
                            String identifier = getIdentifier(params, constraint.getValue());
                            QuestionBean bean = listOfQuestion.get(identifier);
                            if (bean != null) {
                                bean.setRelevanted(true);
//                                if (bean.getSelectedOptionAnswers() != null) {
                                if (Tool.isOptions(bean.getAnswer_type()) ||
                                        QuestionViewAdapter.IsTextWithSuggestionQuestion(Integer.valueOf(bean.getAnswer_type()))) {
                                    try {
                                        constraint.setValue(bean.getSelectedOptionAnswers().get(0).getCode());
                                        if (field.contains("_NAME") && bean.getAnswer() != null) {
                                            constraint.setValue(bean.getAnswer());
                                        } else if (Global.AT_LOOKUP_TABLE.equals(bean.getAnswer_type())) {
                                            String answer = bean.getSelectedOptionAnswers().get(0).getCode();
                                            if (("00000".equalsIgnoreCase(answer) || "$$$".equalsIgnoreCase(answer) || "Pilih Salah Satu".equalsIgnoreCase(answer))) {
                                                answer = bean.getAnswer();
                                            }
                                            constraint.setValue(answer);
                                        }
                                        constraints.add(constraint);
                                    } catch (Exception e) {
                                        Logger.i("INFO", bean.getQuestion_label() + ", Hidden: " + bean.isVisible());
                                        Log.e("Error In: " + bean.getQuestion_label() + "-" + bean.getIdentifier_name(), e);
                                        e.printStackTrace();

                                        constraint.setValue(bean.getAnswer());
                                        constraints.add(constraint);
                                    }
                                } else {
                                    constraint.setValue(bean.getAnswer());
                                    constraints.add(constraint);
                                }
                            }
                        }
                    } else if (constraint.getValue().contains("$")) { //NENDI: 2018-10-04 | Add Signed Variable to User Data
                        int idxOfOpenAbs = constraint.getValue().indexOf("$");
                        String finalIdentifier = constraint.getValue().substring(idxOfOpenAbs + 1);

                        switch (finalIdentifier) {
                            case Global.IDF_LOGIN_ID:
                                String loginId = GlobalData.getSharedGlobalData().getUser().getLogin_id();
                                int idxOfOpenAt = loginId.indexOf('@');
                                if (idxOfOpenAt != -1) {
                                    loginId = loginId.substring(0, idxOfOpenAt);
                                }
                                constraint.setValue(loginId);
                                break;
                            case Global.IDF_BRANCH_ID:
                                String branchId = GlobalData.getSharedGlobalData().getUser().getBranch_id();
                                constraint.setValue(branchId);
//                                kv.setValue(branchId);
                                break;
                            case Global.IDF_BRANCH_NAME:
                                String branchName = GlobalData.getSharedGlobalData().getUser().getBranch_name();
                                constraint.setValue(branchName);
//                                kv.setValue(branchName);
                                break;
                            case Global.IDF_UUID_USER:
                                String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                                constraint.setValue(uuidUser);
//                                kv.setValue(uuidUser);
                                break;
                            case Global.IDF_JOB:
                                String job = GlobalData.getSharedGlobalData().getUser().getFlag_job();
                                constraint.setValue(job);
//                                kv.setValue(job);
                                break;
                            case Global.IDF_DEALER_NAME:
                                String dealerName = GlobalData.getSharedGlobalData().getUser().getDealer_name();
                                constraint.setValue(dealerName);
//                                kv.setValue(dealerName);
                                break;
                            case Global.IDF_UUID_BRANCH:
                                String uuidBranch = GlobalData.getSharedGlobalData().getUser().getUuid_branch();
                                constraint.setValue(uuidBranch);
//                                kv.setValue(uuidBranch);
                                break;
                            case Global.IDF_DEALER_ID:
                                String dealerId = GlobalData.getSharedGlobalData().getUser().getUuid_dealer();
                                constraint.setValue(dealerId);
//                                kv.setValue(dealerId);
                                break;
                            case Global.IDF_BRANCH_TYPE:
                                String branchType = GlobalData.getSharedGlobalData().getUser().getBranch_type();
                                constraint.setValue(branchType);
                                break;
                            case Global.IDF_TASK_ID:
                                String taskId = DynamicFormActivity.header.getTask_id();
                                constraint.setValue(taskId);
                                break;
                        }

                        constraints.add(constraint);
                    } else if (constraint.getValue().contains(",")) {
                        String[] params2 = Tool.split(constraint.getValue(), Global.DELIMETER_DATA3);
                        String[] finalParams = new String[params2.length];

                        int idx = 0;
                        for (String param : params2) {
                            finalParams[idx] = param;
                            idx++;
                        }

                        constraint.setValue(Tool.implode(finalParams, Global.DELIMETER_DATA3));
                        constraints.add(constraint);
                    } else {
                        if (constraint.getOperator() != null) {
                            constraint.setValue("'".concat(constraint.getValue()).concat("'"));
                        }
                        constraints.add(constraint);
                    }
                }

                //Update Validated Constraints
                query.setConstraint(constraints);
            }
        }

        return query;
    }

    public static List<Query.Constraint> finalConstraint(List<Query.Constraint> constraints) {
        List<Query.Constraint> list = new ArrayList<>();
        if (!constraints.isEmpty()) {
            for (Query.Constraint constraint : constraints) {
                String[] params;
                if (constraint.getValue().contains("[")) {
                    //2018-05-24 | Add Data delimiter support
                    String value = constraint.getValue();
                    if (value.contains(",")) {
                        String[] params2 = Tool.split(value, Global.DELIMETER_DATA3);
                        String[] finalParams = new String[params2.length];

                        int idx = 0;
                        for (String param : params2) {
                            int idxOpenAt = param.indexOf("[");
                            if (idxOpenAt != -1) {
                                int idxCloseAt = param.indexOf("]");
                                String identifier = param.substring(idxOpenAt + 1, idxCloseAt);
                                QuestionBean qBean = Constant.listOfQuestion.get(identifier);
                                qBean.setRelevanted(true);
//                                    String finalValue = QuestionBean.getAnswer(qBean);
                                finalParams[idx] = QuestionBean.getAnswer(qBean);
                            } else {
                                finalParams[idx] = param;
                            }
                            idx++;
                        }

                        constraint.setValue(Tool.implode(finalParams, Global.DELIMETER_DATA3));
                        list.add(constraint);
                    } else {
                        params = new String[]{"[", "]"};
                        String identifier = getIdentifier(params, constraint.getValue());
                        QuestionBean bean = Constant.listOfQuestion.get(identifier);
                        if (bean != null) {
                            bean.setRelevanted(true);
//                                if (bean.getSelectedOptionAnswers() != null) {
                            if (Tool.isOptions(bean.getAnswer_type()) ||
                                    bean.getAnswer_type().equalsIgnoreCase(Global.AT_TEXT_WITH_SUGGESTION)) {
                                try {
                                    constraint.setValue(bean.getSelectedOptionAnswers().get(0).getCode());
                                    constraints.add(constraint);
                                } catch (Exception e) {
                                    Logger.i("INFO", bean.getQuestion_label() + ", Hidden: " + bean.isVisible());
                                    Log.e("Error In: " + bean.getQuestion_label() + "-" + bean.getIdentifier_name(), e);
                                    e.printStackTrace();

                                    constraint.setValue(bean.getAnswer());
                                    list.add(constraint);
                                }
                            } else {
                                constraint.setValue(bean.getAnswer());
                                list.add(constraint);
                            }
                        }
                    }
                } else if (constraint.getValue().contains("MS_PO")) {
                    String[] strings = Tool.split(constraint.getValue(), ".");
                    String idf = strings[1];

                    switch (idf) {
                        case "BRAND_CODE":
                            constraint.setValue(Constant.poAsset.getBrand_code());
                            break;
                        case "MODEL_CODE":
                            constraint.setValue(Constant.poAsset.getModel_code());
                            break;
                        case "MASTER_CODE":
                            constraint.setValue(Constant.poAsset.getMaster_code());
                            break;
                        case "SC_ID":
                            constraint.setValue(Constant.productOff.getSc_id());
                            break;
                        case "GROUP_TYPE":
                            constraint.setValue(Constant.poAsset.getGroup_type());
                            break;
                        default:
                            constraint.setValue("-");
                            break;
                    }

                    list.add(constraint);
                } else if (constraint.getValue().contains("$")) { //NENDI: 2018-10-04 | Add Signed Variable to User Data
                    int idxOfOpenAbs = constraint.getValue().indexOf("$");
                    String finalIdentifier = constraint.getValue().substring(idxOfOpenAbs + 1);

                    switch (finalIdentifier) {
                        case Global.IDF_LOGIN_ID:
                            String loginId = GlobalData.getSharedGlobalData().getUser().getLogin_id();
                            int idxOfOpenAt = loginId.indexOf('@');
                            if (idxOfOpenAt != -1) {
                                loginId = loginId.substring(0, idxOfOpenAt);
                            }
                            constraint.setValue(loginId);
                            break;
                        case Global.IDF_BRANCH_ID:
                            String branchId = GlobalData.getSharedGlobalData().getUser().getBranch_id();
                            constraint.setValue(branchId);
//                                kv.setValue(branchId);
                            break;
                        case Global.IDF_BRANCH_NAME:
                            String branchName = GlobalData.getSharedGlobalData().getUser().getBranch_name();
                            constraint.setValue(branchName);
//                                kv.setValue(branchName);
                            break;
                        case Global.IDF_UUID_USER:
                            String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                            constraint.setValue(uuidUser);
//                                kv.setValue(uuidUser);
                            break;
                        case Global.IDF_JOB:
                            String job = GlobalData.getSharedGlobalData().getUser().getFlag_job();
                            constraint.setValue(job);
//                                kv.setValue(job);
                            break;
                        case Global.IDF_DEALER_NAME:
                            String dealerName = GlobalData.getSharedGlobalData().getUser().getDealer_name();
                            constraint.setValue(dealerName);
//                                kv.setValue(dealerName);
                            break;
                        case Global.IDF_UUID_BRANCH:
                            String uuidBranch = GlobalData.getSharedGlobalData().getUser().getUuid_branch();
                            constraint.setValue(uuidBranch);
//                                kv.setValue(uuidBranch);
                            break;
                        case Global.IDF_DEALER_ID:
                            String dealerId = GlobalData.getSharedGlobalData().getUser().getUuid_dealer();
                            constraint.setValue(dealerId);
//                                kv.setValue(dealerId);
                            break;
                        case Global.IDF_BRANCH_TYPE:
                            String branchType = GlobalData.getSharedGlobalData().getUser().getBranch_type();
                            constraint.setValue(branchType);
                            break;
                        case Global.IDF_TASK_ID:
                            String taskId = DynamicFormActivity.header.getTask_id();
                            constraint.setValue(taskId);
                            break;
                    }

                    list.add(constraint);
                } else {
                    list.add(constraint);
                }
            }
        }

        return list;
    }

    public static String getIdentifier(String[] params, String fmtIdentifier) {
        int idxOpen = 0;
        int idxClose = 0;
        String identifier = null;

        if (params.length >= 2) {
            idxOpen = fmtIdentifier.indexOf(params[0]);

            if (idxOpen != -1) {
                idxClose = fmtIdentifier.indexOf(params[1]);
                identifier = fmtIdentifier.substring(idxOpen + 1, idxClose).toUpperCase();
            }
        }

        return identifier;
    }

    private boolean copyValue(QuestionBean mBean, String valueScript) {
        String script = valueScript;
        String answerType = mBean.getAnswer_type();
        if (script == null || script.length() == 0) {
            return true;
        } else {
            int idxOfOpenBrace = script.indexOf('{');
            if (idxOfOpenBrace != -1) {
                //there's {, prepare to replace what inside the {}
                int idxOfCloseBrace = script.indexOf('}');
                String identifier = script.substring(idxOfOpenBrace + 1, idxOfCloseBrace);
                int idxOfOpenAbs = identifier.indexOf("$");
                if (idxOfOpenAbs != -1) {
                    String finalIdentifier = identifier.substring(idxOfOpenAbs + 1);
                    String flatAnswer = "";
                    if (finalIdentifier.equals(Global.IDF_LOGIN_ID)) {
                        flatAnswer = GlobalData.getSharedGlobalData().getUser().getLogin_id();
                        int idxOfOpenAt = flatAnswer.indexOf('@');
                        if (idxOfOpenAt != -1) {
                            flatAnswer = flatAnswer.substring(0, idxOfOpenAt);
                        }
                    } else if (finalIdentifier.equals(Global.IDF_BRANCH_ID)) {
                        flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_id();
                    } else if (finalIdentifier.equals(Global.IDF_BRANCH_NAME)) {
                        flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_name();
                    } else if (finalIdentifier.equals(Global.IDF_UUID_USER)) {
                        flatAnswer = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                    } else if (finalIdentifier.equals(Global.IDF_JOB)) {
                        flatAnswer = GlobalData.getSharedGlobalData().getUser().getFlag_job();
                    } else if (finalIdentifier.equals(Global.IDF_DEALER_NAME)) {
                        flatAnswer = GlobalData.getSharedGlobalData().getUser().getDealer_name();
                    } else if (finalIdentifier.equals(Global.IDF_THIS_YEAR)) {
                        Calendar cal = Calendar.getInstance(TimeZone.getDefault());
                        flatAnswer = String.valueOf(cal.get(Calendar.YEAR));
                    } else if (finalIdentifier.equals(Global.IDF_NOWADAYS)) {
                        Calendar cal = Calendar.getInstance(TimeZone.getDefault());
                        String format = Global.DATE_STR_FORMAT_GSON;
                        flatAnswer = Formatter.formatDate(cal.getTime(), format);
                    } else if (finalIdentifier.equals(Global.IDF_BRANCH_TYPE)) {
                        flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_type();
                    } else if (finalIdentifier.equals(Global.IDF_TASK_ID)) {
                        String taskId = DynamicFormActivity.header.getTask_id();
                        flatAnswer = taskId;
                    }
                    mBean.setAnswer(flatAnswer);
                    return true;
                } else {
                    QuestionBean bean = listOfQuestion.get(identifier);
                    if (bean != null) {
                        if (QuestionBean.isHaveAnswer(bean)) {
                            //if(mBean.isReadOnly())
                            bean.setRelevanted(true);
                            String flatAnswer = QuestionBean.getAnswer(bean);
                            try {
                                if (Tool.isOptions(answerType) && Tool.isOptions(bean.getAnswer_type())) {
                                    mBean.setSelectedOptionAnswers(bean.getSelectedOptionAnswers());
                                    if (Tool.isOptionsWithDescription(answerType) &&
                                            Tool.isOptionsWithDescription(bean.getAnswer_type())) {
                                        mBean.setAnswer(bean.getAnswer());
                                    }
                                } else if (bean.getAnswer_type().equals(Global.AT_TEXT_WITH_SUGGESTION)) {
                                    if (answerType.equals(Global.AT_TEXT_WITH_SUGGESTION))
                                        mBean.setSelectedOptionAnswers(bean.getSelectedOptionAnswers());
                                    mBean.setAnswer(flatAnswer);
                                } else if ((Tool.isImage(bean.getAnswer_type()) || bean.getAnswer_type().equals(Global.AT_DRAWING)) &&
                                        (Tool.isImage(answerType) || answerType.equals(Global.AT_DRAWING))) {
                                    mBean.setImgAnswer(bean.getImgAnswer());
                                    if (Tool.isHaveLocation(bean.getAnswer_type()) && Tool.isHaveLocation(answerType)) {
                                        mBean.setLocationInfo(bean.getLocationInfo());
                                    }
                                    mBean.setAnswer(flatAnswer);
                                } else if (Tool.isHaveLocation(bean.getAnswer_type())) {
                                    if (Tool.isHaveLocation(answerType))
                                        mBean.setLocationInfo(bean.getLocationInfo());
                                    if(!(Global.AT_LOCATION_WITH_ADDRESS.equalsIgnoreCase(bean.getAnswer_type())
                                            && "Address not found".equalsIgnoreCase(flatAnswer))&& !"".equalsIgnoreCase(flatAnswer) && flatAnswer!=null){
                                        mBean.setAnswer(flatAnswer);
                                    }
                                } else {
                                    mBean.setAnswer(flatAnswer);
                                }
                                return true;
                            } catch (Exception e) {
                                FireCrash.log(e);
                                if (Global.IS_DEV)
                                    e.printStackTrace();
                                return false;
                            }
                        } else {
                            return true;
                        }
                    } else {
                        return false;
                    }
                }
            } else {
                return false;
            }
        }
    }

    private boolean copyQuestionValue(QuestionBean mBean, String valueScript, boolean needToChange) {
        String convertedExpression = valueScript.trim();
        if (convertedExpression == null || convertedExpression.length() == 0) {
            return true;
        } else {
            boolean isCopyFromLookup = false;
            boolean isCopyWithCondition = false;
            boolean isCopyFromDkcp = false;
            try {
                if (convertedExpression.contains("copyFromLookup(")) {
                    isCopyFromLookup = convertedExpression.substring(0, 15).equals("copyFromLookup(");
                } else if (convertedExpression.contains("copyFromDkcp(")) {
                    isCopyFromDkcp = true;
                }
            } catch (Exception e) {
                FireCrash.log(e);
                isCopyFromLookup = false;
                if (Global.IS_DEV)
                    e.printStackTrace();
            }
            try {
                isCopyWithCondition = convertedExpression.substring(0, 5).equals("copy(");
            } catch (Exception e) {
                FireCrash.log(e);
                isCopyWithCondition = false;
                if (Global.IS_DEV)
                    e.printStackTrace();
            }

            if (isCopyFromLookup) {
                return copyValueFromLookup(mBean, convertedExpression);
            } else if (isCopyWithCondition) {
                String answer = QuestionBean.getAnswer(mBean);
                if(answer != null && !"".equalsIgnoreCase(answer) && "1".equalsIgnoreCase(mBean.getIs_readonly())) {
                    return true;
                }
                return copyValueWithCondition(mBean, convertedExpression, needToChange);
            } else if (isCopyFromDkcp) {
                String answer = QuestionBean.getAnswer(mBean);
                if(StringUtils.isNotBlank(answer) && !needToChange) {
                    return true;
                }
                return copyValueFromDkcp(mBean, convertedExpression);
            } else {
                return copyValue(mBean, convertedExpression);
            }
        }
    }

    private boolean copyValueWithCondition(QuestionBean mBean, String copyScript, boolean needToChange) {
        String convertedExpression = copyScript;
        String answerType = mBean.getAnswer_type();
        String answer = "";
//        mBean.setRelevanted(true);
        String format;
        boolean needReplacing = true;
        while (needReplacing) {
            if (convertedExpression.contains("copyFromDkcp")) {
                int indexStr = convertedExpression.indexOf("copyFromDkcp");
                String scriptDKCP = convertedExpression.substring(indexStr, convertedExpression.length() -1);
                answer = getCopyDkcpValue(scriptDKCP);
                if (answer == null) {
                    answer = "";
                }
                convertedExpression = convertedExpression.replace(scriptDKCP, answer);
            }
            int idxOfOpenBrace = convertedExpression.indexOf('{');
            if (idxOfOpenBrace != -1) {
                //there's {, prepare to replace what inside the {}
                int idxOfCloseBrace = convertedExpression.indexOf('}');
                String identifier = convertedExpression.substring(idxOfOpenBrace + 1, idxOfCloseBrace);
                int idxOfOpenAbs = identifier.indexOf("$");
                if (idxOfOpenAbs != -1) {
                    String finalIdentifier = identifier.substring(idxOfOpenAbs + 1);
                    String flatAnswer = "";
                    if (finalIdentifier.equals(Global.IDF_LOGIN_ID)) {
                        flatAnswer = GlobalData.getSharedGlobalData().getUser().getLogin_id();
                        int idxOfOpenAt = flatAnswer.indexOf('@');
                        if (idxOfOpenAt != -1) {
                            flatAnswer = flatAnswer.substring(0, idxOfOpenAt);
                        }
                    } else if (finalIdentifier.equals(Global.IDF_BRANCH_ID)) {
                        flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_id();
                    } else if (finalIdentifier.equals(Global.IDF_BRANCH_NAME)) {
                        flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_name();
                    } else if (finalIdentifier.equals(Global.IDF_UUID_USER)) {
                        flatAnswer = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                    } else if (finalIdentifier.equals(Global.IDF_JOB)) {
                        flatAnswer = GlobalData.getSharedGlobalData().getUser().getFlag_job();
                    } else if (finalIdentifier.equals(Global.IDF_DEALER_NAME)) {
                        flatAnswer = GlobalData.getSharedGlobalData().getUser().getDealer_name();
                    } else if (finalIdentifier.equals(Global.IDF_THIS_YEAR)) {
                        Calendar cal = Calendar.getInstance(TimeZone.getDefault());
                        flatAnswer = String.valueOf(cal.get(Calendar.YEAR));
                    } else if (finalIdentifier.equals(Global.IDF_NOWADAYS)) {
                        Calendar cal = Calendar.getInstance(TimeZone.getDefault());
                        String format2 = Global.DATE_STR_FORMAT;
                        flatAnswer = Formatter.formatDate(cal.getTime(), format2);
                    } else if (finalIdentifier.equals(Global.IDF_BRANCH_TYPE)) {
                        flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_type();
                    } else if (finalIdentifier.equals(Global.IDF_TASK_ID)) {
                        String taskId = DynamicFormActivity.header.getTask_id();
                        flatAnswer = taskId;
                    }

                    if (flatAnswer != null && flatAnswer.length() > 0) {
                        convertedExpression = convertedExpression.replace("{" + identifier + "}", flatAnswer);
                    } else {
                        convertedExpression = convertedExpression.replace("{" + identifier + "}", "\"\"");
                    }

                } else {
                    QuestionBean bean = listOfQuestion.get(identifier);
                    if (bean != null) {
//                        if (bean.getIs_visible().equals(Global.FALSE_STRING)) return false;
                        String flatAnswer = QuestionBean.getAnswer(bean);
                        if (flatAnswer != null && flatAnswer.length() > 0) {
                            //NOTE: though it's possible to just iterate on flatAnswer substrings, we prefer to stay on method if size is 1
                            String answers[] = Tool.split(flatAnswer, Global.DELIMETER_DATA);
                            if (answers.length == 1) {
                                try {
                                    bean.setRelevanted(true);

                                    if (bean.getAnswer_type().equals(Global.AT_TIME)) {
                                        format = Global.TIME_STR_FORMAT;
                                        String formatDate = Global.TIME_STR_FORMAT2;
                                        Date date2 = Formatter.parseDate(answers[0], formatDate);
                                        Calendar now = Calendar.getInstance(TimeZone
                                                .getDefault());
                                        Calendar date = Calendar.getInstance(TimeZone
                                                .getDefault());
                                        date.setTime(date2);
                                        date.set(Calendar.YEAR, now.get(Calendar.YEAR));
                                        date.set(Calendar.MONTH, now.get(Calendar.MONTH));
                                        date.set(Calendar.DAY_OF_MONTH, now.get(Calendar.DAY_OF_MONTH));
                                        flatAnswer = Formatter.formatDate(date.getTime(), format);
                                    } else if (bean.getAnswer_type().equals(Global.AT_DATE_TIME)) {
                                        format = Global.DATE_TIME_STR_FORMAT;
                                        String formatDate = Global.DATE_STR_FORMAT_GSON;
                                        Date date2 = Formatter.parseDate(answers[0], formatDate);
                                        Calendar date = Calendar.getInstance(TimeZone
                                                .getDefault());
                                        date.setTime(date2);
                                        flatAnswer = Formatter.formatDate(date.getTime(), format);
                                    } else if (bean.getAnswer_type().equals(Global.AT_DATE)) {
                                        format = Global.DATE_STR_FORMAT;
                                        String formatDate = Global.DATE_STR_FORMAT_GSON;
                                        Date date2 = Formatter.parseDate(answers[0], formatDate);
                                        Calendar date = Calendar.getInstance(TimeZone
                                                .getDefault());
                                        date.setTime(date2);
                                        flatAnswer = Formatter.formatDate(date.getTime(), format);
                                    } else {
                                        flatAnswer = answers[0];
                                    }
                                } catch (Exception e) {
                                    FireCrash.log(e);
                                    e.printStackTrace();
                                }
                                convertedExpression = convertedExpression.replace("{" + identifier + "}", flatAnswer);
                            } else {
                                //NOTE: going into in-depth loop, won't go outside of this 'else'
                                for (int i = 0; i < answers.length; i++) {
                                    String convertedSubExpression = convertedExpression.replace("{" + identifier + "}", answers[i]);
                                    boolean isVisible = copyQuestionValue(mBean, convertedSubExpression, needToChange);
                                    if (isVisible) {
                                        return true;
                                    }
                                }
                                convertedExpression = convertedExpression.replace("{" + identifier + "}", "");
                            }
                        } else {
                            //if there's no answer, just hide the question
                            convertedExpression = convertedExpression.replace("{" + identifier + "}", "");
                        }
                    } else {
                        if (identifier.contains("#")) {
                            String exactValue = identifier.replace("{#", "").trim();
                            exactValue = exactValue.replace("}", "").trim();
                            convertedExpression = convertedExpression.replace("{" + identifier + "}", exactValue);
                        } else {
                            convertedExpression = convertedExpression.replace("{" + identifier + "}", "");
                        }
                    }
                }
            } else {
                needReplacing = false;
            }
        }
        try {
            OperatorSet opSet = OperatorSet.getStandardOperatorSet();
            opSet.addOperator("!=", NotEqualSymbol.class);
            if (QuestionViewAdapter.IsTextQuestion(answerType) && !copyScript.contains("copyFromLookup"))
                opSet.addOperator("copy", IfElseFunctionForCopyValue.class);
            else
                opSet.addOperator("copy", IfElseFunctionDummy.class);

            if (null != mBean.getTag() && Global.TAG_RESULT.equals(mBean.getTag())) {
                String first = convertedExpression.substring(0, convertedExpression.indexOf(",") + 1);
                String between = convertedExpression.substring(convertedExpression.indexOf(",") + 1, convertedExpression.lastIndexOf(",")).replace(",", ":comma:");
                String last = convertedExpression.substring(convertedExpression.lastIndexOf(","));

                convertedExpression = first + between + last;
            }

            Expression exp = new Expression(convertedExpression);
            exp.setOperatorSet(opSet);
            Object result = exp.evaluate().getObject();
            if (QuestionViewAdapter.IsTextQuestion(answerType)) {
                result = result.toString().replaceAll(":comma:", ",");
                result = result.toString().replaceAll(":enter:", "\n");
                mBean.setAnswer((String) result);
            }else if (QuestionViewAdapter.IsDropdownQuestion(answerType) && Boolean.FALSE.equals(result)) {
                List<OptionAnswerBean> optionAnswerBeans = mBean.getOptionAnswers();
                List<OptionAnswerBean> selectedAnswer = new ArrayList<>();
                for (OptionAnswerBean option : optionAnswerBeans) {
                    if (option.getValue().equalsIgnoreCase(answer)) {
                        selectedAnswer.add(option);
                        mBean.setSelectedOptionAnswers(selectedAnswer);
                        break;
                    }
                }
            } else {
                String lastScript = copyScript;
                String[] script = Tool.split(lastScript, Global.DELIMETER_DATA3);
                int i = 1;
                String trueValue = script[i].trim();
                i++;
                String falseValue = script[i].trim();
                i++;
                for (; i < script.length; i++) {
                    String tempSCript = script[i];
                    if (falseValue.contains("copy(")) {
                        falseValue += Global.DELIMETER_DATA3;
                        falseValue += tempSCript;
                    }
                }
                falseValue = removeLastChar(falseValue);

                QuestionBean bean = null;
                if ((Boolean) result) {
                    if (trueValue.isEmpty())
                        return true;
                    if (trueValue.contains("#")) {
                        trueValue = trueValue.replace("{#", "").trim();
                        trueValue = trueValue.replace("}", "").trim();
                        Date tempDate = Formatter.parseDate(trueValue, Global.DATE_STR_FORMAT);
                        trueValue = Formatter.formatDate(tempDate, Global.DATE_STR_FORMAT_GSON);
                        mBean.setAnswer(trueValue);
                        return true;
                    } else if (trueValue.contains("$")) {
                        return copyQuestionValue(mBean, trueValue, needToChange);
                    } else if (trueValue.substring(0, 1).equals("{")) {
                        trueValue = trueValue.replace("{", "").trim();
                        trueValue = trueValue.replace("}", "").trim();
                        bean = listOfQuestion.get(trueValue);

                    } else if (trueValue.startsWith("lov(") && trueValue.endsWith(")")) {
                        trueValue = StringUtils.remove(trueValue, "lov(").trim();
                        trueValue = StringUtils.remove(trueValue, ")").trim();
                        String[] dataLov = StringUtils.split(trueValue, Global.DELIMETER_DATA);
                        String lovGroup = dataLov[0];
                        String code = dataLov[1];

                        List<OptionAnswerBean> optionAnswerBeans = mBean.getOptionAnswers();
                        List<OptionAnswerBean> selectedAnswer = new ArrayList<>();

                        for (OptionAnswerBean option : optionAnswerBeans) {
                            if (!"$$$".equalsIgnoreCase(option.getCode()) && !"Pilih Salah Satu".equalsIgnoreCase(option.getValue())) {
                                if (option.getLov_group().equalsIgnoreCase(lovGroup) && option.getCode().equalsIgnoreCase(code)) {
                                    selectedAnswer.add(option);
                                    mBean.setSelectedOptionAnswers(selectedAnswer);
                                    return true;
                                }
                            }
                        }
                        return false;

                    } else {
                        return copyQuestionValue(mBean, trueValue, needToChange);
                    }

                } else {
                    if (falseValue.isEmpty())
                        return true;
                    if (falseValue.contains("#")) {
                        falseValue = falseValue.replace("{#", "").trim();
                        falseValue = falseValue.replace("}", "").trim();
                        Date tempDate = Formatter.parseDate(falseValue, Global.DATE_STR_FORMAT);
                        falseValue = Formatter.formatDate(tempDate, Global.DATE_STR_FORMAT_GSON);
                        mBean.setAnswer(falseValue);
                        return true;
                    } else if (falseValue.contains("$")) {
                        return copyQuestionValue(mBean, falseValue, needToChange);
                    } else if (falseValue.substring(0, 1).equals("{")) {
                        falseValue = falseValue.replace("{", "").trim();
                        falseValue = falseValue.replace("}", "").trim();
                        bean = listOfQuestion.get(falseValue);
                    } else {
                        return copyQuestionValue(mBean, falseValue, needToChange);
                    }

                }
                if (bean != null) {
                    if (QuestionBean.isHaveAnswer(bean)) {
                        //setRelevanted should be here
                        //alternative for isSpecialCaseChangeFlag
                        if (mBean.isReadOnly())
                            bean.setRelevanted(true);
                        String flatAnswer = QuestionBean.getAnswer(bean);
                        try {
                            if (Tool.isOptions(answerType) && Tool.isOptions(bean.getAnswer_type())) {
                                bean.setRelevanted(true);
                                mBean.setSelectedOptionAnswers(bean.getSelectedOptionAnswers());
                                if (Tool.isOptionsWithDescription(answerType) &&
                                        Tool.isOptionsWithDescription(bean.getAnswer_type())) {
                                    mBean.setAnswer(bean.getAnswer());
                                }
                            } else if (QuestionViewAdapter.IsLookupQuestion(answerType) &&
                                    QuestionViewAdapter.IsLookupQuestion(bean.getAnswer_type())) {
                                bean.setRelevanted(true);
                                mBean.setAnswer(bean.getAnswer());
                                mBean.setSelectedCriteriaBean(bean.getSelectedCriteriaBean());
                                mBean.setLookupCriteriaList(bean.getLookupCriteriaList());
                            } else if (bean.getAnswer_type().equals(Global.AT_TEXT_WITH_SUGGESTION)) {
                                bean.setRelevanted(true);
                                if (answerType.equals(Global.AT_TEXT_WITH_SUGGESTION))
                                    mBean.setSelectedOptionAnswers(bean.getSelectedOptionAnswers());
                                mBean.setAnswer(flatAnswer);
                            } else if ((Tool.isImage(bean.getAnswer_type()) || bean.getAnswer_type().equals(Global.AT_DRAWING)) &&
                                    (Tool.isImage(answerType) || answerType.equals(Global.AT_DRAWING))) {
                                mBean.setImgAnswer(bean.getImgAnswer());
                                if (Tool.isHaveLocation(bean.getAnswer_type()) && Tool.isHaveLocation(answerType)) {
                                    mBean.setLocationInfo(bean.getLocationInfo());
                                }
                                mBean.setAnswer(flatAnswer);
                            } else if (Tool.isHaveLocation(bean.getAnswer_type())) {
                                if (Tool.isHaveLocation(answerType))
                                    mBean.setLocationInfo(bean.getLocationInfo());
                                mBean.setAnswer(flatAnswer);
                            } else {
                                mBean.setAnswer(flatAnswer);
                            }
                            return true;
                        } catch (Exception e) {
                            FireCrash.log(e);
                            if (Global.IS_DEV)
                                e.printStackTrace();
                            return false;
                        }
                    } else {
                        return false;
                    }
                } else {
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            FireCrash.log(e);
            if (Global.IS_DEV)
                e.printStackTrace();
            return false;
        }
    }

    private boolean copyValueFromLookup(QuestionBean mBean, String convertedExpression) {
        String lastScript = convertedExpression;
        String[] script = Tool.split(lastScript, Global.DELIMETER_DATA3);
        String mainIdf = script[0].trim();
        mainIdf = mainIdf.replace("copyFromLookup(", "").trim();
        mainIdf = mainIdf.replace("{", "").trim();
        mainIdf = mainIdf.replace("}", "").trim();
        String valueIdf = script[1].trim();
        valueIdf = valueIdf.replace(")", "").trim();
        valueIdf = valueIdf.replace("{", "").trim();
        valueIdf = valueIdf.replace("}", "").trim();
        QuestionBean bean = listOfQuestion.get(mainIdf);
        bean.setRelevanted(true);
        String answer = null;
        LookupCriteriaBean criteriaBean = bean.getSelectedCriteriaBean();
        if (criteriaBean != null) {
            List<ParameterAnswer> parameterList = criteriaBean.getParameterAnswers();
            if (parameterList != null) {
                for (ParameterAnswer parameterAnswer : parameterList) {
                    if (parameterAnswer.getRefId().equals(valueIdf)) {
                        answer = parameterAnswer.getAnswer();
                        break;
                    }
                }
            }
        }
        if (answer != null) {
            if (Tool.isOptions(mBean.getAnswer_type())) {
                Lookup lookup = LookupDataAccess.getOne(getActivity(), answer);
                OptionAnswerBean answerBean = new OptionAnswerBean(lookup);
                List<OptionAnswerBean> selectedOptionAnswer = new ArrayList<>();
                selectedOptionAnswer.add(answerBean);
                mBean.setSelectedOptionAnswers(selectedOptionAnswer);
            } else {
                mBean.setAnswer(answer);
            }
            return true;
        }
        return false;
    }

    private boolean copyValueFromDkcp(QuestionBean mBean, String convertedExpression) {
        String lastScript = convertedExpression;
        String[] script = Tool.split(lastScript, Global.DELIMETER_DATA3);
        String mainIdf = script[0].trim();
        mainIdf = mainIdf.replace("copyFromDkcp(", "").trim();
        mainIdf = mainIdf.replace("{", "").trim();
        mainIdf = mainIdf.replace("}", "").trim();
        String valueIdf = script[1].trim();
        valueIdf = valueIdf.replace(")", "").trim();
        valueIdf = valueIdf.replace("{", "").trim();
        valueIdf = valueIdf.replace("}", "").trim();

        try {
            QuestionBean bean = Constant.getListOfQuestion().get(mainIdf);
            bean.setRelevanted(true);

            String answer = getAnswerDkcp(valueIdf, bean);
            if (mBean.getAnswer_type().equals(Global.AT_DROPDOWN)) {
                List<OptionAnswerBean> optionAnswerBeans = mBean.getOptionAnswers();
                List<OptionAnswerBean> selectedAnswer = new ArrayList<>();
                for (OptionAnswerBean option : optionAnswerBeans) {
                    if (option.getValue().equalsIgnoreCase(answer)) {
                        selectedAnswer.add(option);
                        mBean.setSelectedOptionAnswers(selectedAnswer);
                        mBean.setIsCanChange(true);
                        mBean.setChange(true);
                        break;
                    }
                }
                if (bean.getSelectedOptionAnswers() == null) {
                    Toast.makeText(getContext(), getActivity().getString(R.string.copy_value_dkcp_failed), Toast.LENGTH_SHORT).show();
                }
            } else if (mBean.getAnswer_type().equals(Global.AT_DATE)) {
                Date date = null;
                answer = answer.replace("-", "/");
                try {
                    date = Formatter.parseDate(answer, Global.DATE_STR_FORMAT);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                String finalAnswer = Formatter.formatDate(date, Global.DATE_STR_FORMAT_GSON);
                mBean.setAnswer(finalAnswer);
                mBean.setIsCanChange(true);
                mBean.setChange(true);
            } else {
                mBean.setAnswer(answer);
                mBean.setIsCanChange(true);
                mBean.setChange(true);
            }

            if (answer == null || "".equals(answer)) {
                Toast.makeText(getContext(), getActivity().getString(R.string.copy_value_dkcp_failed), Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            FireCrash.log(e);
        }
        return true;
    }
    private String getCopyDkcpValue(String convertedExpression) {
        String lastScript = convertedExpression;
        String[] script = Tool.split(lastScript, Global.DELIMETER_DATA3);
        String mainIdf = script[0].trim();
        mainIdf = mainIdf.replace("copyFromDkcp(", "").trim();
        mainIdf = mainIdf.replace("{", "").trim();
        mainIdf = mainIdf.replace("}", "").trim();
        String valueIdf = script[1].trim();
        valueIdf = valueIdf.replace(")", "").trim();
        valueIdf = valueIdf.replace("{", "").trim();
        valueIdf = valueIdf.replace("}", "").trim();
        QuestionBean bean = Constant.getListOfQuestion().get(mainIdf);
        return getAnswerDkcp(valueIdf, bean);
    }

    private String getAnswerDkcp(String valueIdf, QuestionBean bean){
        String answer = "";
        if (valueIdf.contains(Global.DELIMETER_DKCP_AT) && valueIdf.contains(Global.DELIMETER_DKCP_DOT)) {
            String[] parentKey = Tool.split(valueIdf, Global.DELIMETER_DKCP_AT);
            answer = bean.getResponseImageDkcp().getDataDkcp().getValueRead(parentKey[0]);
            String[] childKey = Tool.split(parentKey[1], Global.DELIMETER_DKCP_DOT);
            Type collectionType = new TypeToken<Map<String, Map<String, String>>>(){}.getType();
            Map<String, Map<String, String>> mapList = new Gson().fromJson(answer, collectionType);
            Map<String, String> mapParent = mapList.get(childKey[0]);
            answer = mapParent.get(childKey[1]);
        } else {
            if (bean.getResponseImageDkcp() != null && bean.getResponseImageDkcp().getDataDkcp() != null) {
                answer = bean.getResponseImageDkcp().getDataDkcp().getValueRead(valueIdf);
            }
        }
        return answer;
    }

    private boolean loadQuestionForReview(int targetLastPosition, boolean loadToFinish) {
        boolean isLastQuestion = false;
        if (!listOfQuestions.isEmpty()) {
            QuestionBean mbean = listOfQuestions.get(listOfQuestions.size() - 1);
            questionSize = DynamicFormActivity.listOfIdentifier.indexOf(mbean.getIdentifier_name()) + 1;
        }
        int lastposition = questionSize;
        for (; lastposition < targetLastPosition; lastposition++) {
            try {
                if (DynamicFormActivity.listOfIdentifier.size() > lastposition) {
                    QuestionBean bean = listOfQuestion.get(DynamicFormActivity.listOfIdentifier.get(lastposition));
                    questionSize++;
                    if (bean.isVisible()) {

                        String relevantExpression = bean.getRelevant_question();
                        if (relevantExpression == null) relevantExpression = "";
                        if (isQuestVisibleIfRelevant(relevantExpression, bean)) {
//                            bean.setVisible(true);
//
//                            if (bean.getQuestion_value() != null && !bean.getQuestion_value().isEmpty() /*&& !QuestionViewAdapter.IsLookupQuestion(bean.getAnswer_type())*/) {
//                                boolean isCopyFromLookup = false;
//                                try {
//                                    if (bean.getQuestion_value().contains("copyFromLookup("))
//                                        isCopyFromLookup = bean.getQuestion_value().substring(0, 15).equals("copyFromLookup(");
//                                } catch (Exception e) {
//                                    isCopyFromLookup = false;
//                                    if (Global.IS_DEV)
//                                        e.printStackTrace();
//                                }
//                                if (bean.isReadOnly() || !QuestionBean.isHaveAnswer(bean)
//                                        || ((QuestionBean.isHaveAnswer(bean) && !QuestionViewAdapter.IsTextQuestion(bean.getAnswer_type())))
//                                        || Tool.isOptions(bean.getAnswer_type()) ||
//                                        (isCopyFromLookup)) {
//                                    String script = bean.getQuestion_value();
//                                    if (!(script.contains("{\"parameters\":[") && script.contains("]}"))) {
//                                        copyQuestionValue(bean, bean.getQuestion_value());
//                                    }
//                                }
//                            }
//                            calculateQuestionBean(bean);
//                            if (Tool.isOptions(bean.getAnswer_type())) {
//                                List<OptionAnswerBean> options = getOptionsForQuestion(bean, true);
//                                bean.setOptionAnswers(options);
//                            }
//                            addQuestionLabel(bean.getQuestion_label() + " " + Global.DELIMETER_ROW + " " + bean.getQuestion_group_name());
//                            addItem(bean, lastposition, true);
                            loadSingleQuestion(bean, lastposition, true);
                        } else {
                            bean.setVisible(false);
                            QuestionBean.resetAnswer(bean);
                            removeQuestionLabel(lastposition);
                            calculateQuestionBean(bean);
                        }
                    } else {
                        if (lastposition == 0) {
                            doNext(false);
                        } else {
                            String relevantExpression = bean.getRelevant_question();
                            if (relevantExpression == null) relevantExpression = "";
                            if (isQuestVisibleIfRelevant(relevantExpression, bean)) {
                                QuestionSet tempQuestion = QuestionSetDataAccess.getOne(getContext(), DynamicFormActivity.header.getUuid_scheme(), bean.getQuestion_id(), bean.getQuestion_group_id());
                                if (tempQuestion != null && tempQuestion.getIs_visible().equals(Global.TRUE_STRING)) {
//                                    bean.setVisible(true);
//                                    if (bean.getQuestion_value() != null && !bean.getQuestion_value().isEmpty() /*&& !QuestionViewAdapter.IsLookupQuestion(bean.getAnswer_type())*/) {
//                                        boolean isCopyFromLookup = false;
//                                        try {
//                                            if (bean.getQuestion_value().contains("copyFromLookup("))
//                                                isCopyFromLookup = bean.getQuestion_value().substring(0, 15).equals("copyFromLookup(");
//                                        } catch (Exception e) {
//                                            isCopyFromLookup = false;
//                                            if (Global.IS_DEV)
//                                                e.printStackTrace();
//                                        }
//                                        if (bean.isReadOnly() || !QuestionBean.isHaveAnswer(bean)
//                                                || ((QuestionBean.isHaveAnswer(bean) && !QuestionViewAdapter.IsTextQuestion(bean.getAnswer_type())))
//                                                || Tool.isOptions(bean.getAnswer_type()) ||
//                                                (isCopyFromLookup)) {
//                                            String script = bean.getQuestion_value();
//                                            if (!(script.contains("{\"parameters\":[") && script.contains("]}"))) {
//                                                copyQuestionValue(bean, bean.getQuestion_value());
//                                            }
//                                        }
//                                    }
//                                    calculateQuestionBean(bean);
//                                    if (Tool.isOptions(bean.getAnswer_type())) {
//                                        List<OptionAnswerBean> options = getOptionsForQuestion(bean, true);
//                                        bean.setOptionAnswers(options);
//                                    }
//                                    addQuestionLabel(bean.getQuestion_label() + " " + Global.DELIMETER_ROW + " " + bean.getQuestion_group_name());
//                                    addItem(bean, lastposition, true);
                                    loadSingleQuestion(bean, lastposition, true);
                                } else {
                                    calculateQuestionBean(bean);
                                }
                            } else {
                                bean.setVisible(false);
                                calculateQuestionBean(bean);
                            }
                        }
                    }
                    if (!loadToFinish) {
                        if (needStop) {
                            isLastQuestion = false;
                            break;
                        }
                    }
                } else {
                    break;
                }
            } catch (IndexOutOfBoundsException e) {
                FireCrash.log(e);
                if (Global.IS_DEV)
                    e.printStackTrace();
                break;
            } catch (Exception e) {
                FireCrash.log(e);
                if (Global.IS_DEV)
                    e.printStackTrace();
            }
        }
        if (targetLastPosition == lastposition)
            isLastQuestion = true;
        return isLastQuestion;
    }


    private String getTestid(String id) {
        String result = "";
        if (id.equals("test_test")) return "halo";
        else if (id.equals("test_2")) return "hehe";
        else if (id.equals("test_3")) return "15,2";
        else if (id.equals("test_4")) return "16.1";

        return result;
    }

    public boolean isRelevantMandatory2(String relevantExpression, QuestionBean question) {
        boolean result = false;
        String convertedExpression = relevantExpression;        //make a copy of
        if (question.isMandatory()) {
            return true;
        } else if (convertedExpression == null || convertedExpression.length() == 0) {
            return false;
        } else {

            //TODO, use extractIdentifierFromString next time to simplify
            boolean needReplacing = true;
            while (needReplacing) {

                //replace application modifier
                convertedExpression = replaceModifiers(convertedExpression);

                int idxOfOpenBrace = convertedExpression.indexOf('{');
                if (idxOfOpenBrace != -1) {                    //there's {, prepare to replace what inside the {}
                    int idxOfCloseBrace = convertedExpression.indexOf('}');
                    String identifier = convertedExpression.substring(idxOfOpenBrace + 1, idxOfCloseBrace);
                    int idxOfOpenAbs = identifier.indexOf("$");
                    if (idxOfOpenAbs != -1) {
                        String finalIdentifier = identifier.substring(idxOfOpenAbs + 1);
                        String flatAnswer = "";

                        if (finalIdentifier.equals(Global.IDF_LOGIN_ID)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getLogin_id();
                            int idxOfOpenAt = flatAnswer.indexOf('@');
                            if (idxOfOpenAt != -1) {
                                flatAnswer = flatAnswer.substring(0, idxOfOpenAt);
                            }
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_ID)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_id();
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_NAME)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_name();
                        } else if (finalIdentifier.equals(Global.IDF_UUID_USER)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                        } else if (finalIdentifier.equals(Global.IDF_JOB)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getFlag_job();
                        } else if (finalIdentifier.equals(Global.IDF_DEALER_NAME)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getDealer_name();
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_TYPE)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_type();
                        } else if (finalIdentifier.equals(Global.IDF_TASK_ID)) {
                            String taskId = DynamicFormActivity.header.getTask_id();
                            flatAnswer = taskId;
                        }

                        if (flatAnswer != null && flatAnswer.length() > 0) {
                            convertedExpression = convertedExpression.replace("{" + identifier + "}", flatAnswer);
                        } else {            //if there's no answer, just hide the question
                            return false;
                        }

                    } else {
                        QuestionBean bean = listOfQuestion.get(identifier);

                        if (bean != null) {

                            // {SVY_PRODUCT}!='KSM' || {SVY_CLUSTER}=='B' || {SVY_CLUSTER}=='C'

                            //Glen 21 Oct 2014, if it relate to question which is not visible, make it not visible too
                            // if (bean.getIs_visible().equals(Global.FALSE_STRING)) return false;

                            String flatAnswer = QuestionBean.getAnswer(bean);

                            if (Tool.isOptions(bean.getAnswer_type())) {
                                try {
                                    flatAnswer = bean.getSelectedOptionAnswers().get(0).getCode();
                                } catch (Exception e) {
                                    FireCrash.log(e);

                                }
                            }

                            if (flatAnswer != null && flatAnswer.length() > 0) {
                                //Glen 22 Oct 2014, enable multi-depth checking for 'multiple' question
                                //NOTE: though it's possible to just iterate on flatAnswer substrings, we prefer to stay on method if size is 1
                                String answers[] = Tool.split(flatAnswer, Global.DELIMETER_DATA);
                                if (answers.length == 1) {
//									convertedExpression = convertedExpression.replace("{"+identifier+"}", flatAnswer);
                                    convertedExpression = convertedExpression.replace("{" + identifier + "}", answers[0]);
                                } else {
                                    //NOTE: going into in-depth loop, won't go outside of this 'else'
                                    for (int i = 0; i < answers.length; i++) {
                                        String convertedSubExpression = convertedExpression.replace("{" + identifier + "}", answers[i]);
                                        boolean isVisible = isQuestVisibleIfRelevant(convertedSubExpression, question);
                                        if (isVisible) {
                                            return true;
                                        }
                                    }
                                    return false;
                                }

                                //Glen 16 Oct 2014, added as affected bean visibility
//								bean.addToAffectedQuestionBeans(question);
                                //bean.addToAffectedQuestionBeanVisibility(question);
                            } else {            //if there's no answer, just hide the question
                                return false;
                            }
                        } else {
//							convertedExpression.replaceAll("{"+identifier+"}", "");
                            convertedExpression = convertedExpression.replace("{" + identifier + "}", "\"\"");
                        }
                    }
                }
                //moved up
//					else if (convertedExpression.indexOf('r') != -1){
//						convertedExpression = replaceModifiers(convertedExpression);
//					}

                //no more replacing needed
                else {
                    needReplacing = false;
                }

            }
            try {
                OperatorSet opSet = OperatorSet.getStandardOperatorSet();
                opSet.addOperator("!=", NotEqualSymbol.class);
                Expression exp = new Expression(convertedExpression);
                exp.setOperatorSet(opSet);
                result = exp.evaluate().toBoolean();
                return result;
            } catch (Exception e) {
                FireCrash.log(e);
                e.printStackTrace();
                return false;
            }

        }
//			return result;
    }

    public boolean isRelevantMandatory(String expression) {
        boolean flag = false, finalResult = false;
        int i, start = 0, end = 0;
        boolean tempResult = false;
        String tempString = "";
        Stack<Boolean> results = new Stack<>();
        Stack<Boolean> operators = new Stack<>();
        expression = expression.replaceAll("\\s+", "");
        if (expression.equals("")) return false;
        else if (expression.charAt(1) == '{') {
            for (i = 0; i < expression.length(); i++) {
                switch (expression.charAt(i)) {
                    case '{': {
                        if (expression.charAt(i + 1) == '{') {
                            i++;
                            start = i;
                        }
                        break;
                    }
                    case '}': {
                        if (i == expression.length() - 1) {
                            end = i;
                            tempString = expression.substring(start, end);
                            tempResult = isRelevantMandatorySubString(tempString);
                            results.push(tempResult);
                        } else {
                            if (expression.charAt(i + 1) == '&' || expression.charAt(i + 1) == '|') {
                                end = i;
                                tempString = expression.substring(start, end);
                                tempResult = isRelevantMandatorySubString(tempString);
                                results.push(tempResult);
                            }
                        }

                        break;
                    }
                    case '|': {
                        if (expression.charAt(i - 1) == '}') operators.push(false);

                        i++;
                        break;
                    }
                    case '&': {
                        if (expression.charAt(i - 1) == '}') operators.push(true);
                        i++;
                        break;
                    }
                }
            }
            flag = true;
            while (!results.isEmpty()) {
                if (flag) {
                    finalResult = results.pop();
                    if (results.isEmpty()) return finalResult;
                    else {
                        tempResult = results.pop();
                        if (operators.pop()) finalResult = finalResult && tempResult;
                        else finalResult = finalResult || tempResult;
                        flag = false;
                    }
                } else {
                    tempResult = results.pop();
                    if (operators.pop()) finalResult = finalResult && tempResult;
                    else finalResult = finalResult || tempResult;
                }
            }
            return finalResult;
        } else return isRelevantMandatorySubString(expression);
    }

    // Glen 17 Oct 2014
    protected QuestionBean getQuestionBeanForIdentifier(String identifier) {
        QuestionBean bean = listOfQuestion.get(identifier);
        return bean;
    }


    public boolean isRelevantMandatorySubString(String expression) {
        Stack<Boolean> results = new Stack<>();
        Stack<Boolean> operators = new Stack<>();
        String temp = "", ans1 = "", ans2 = "";
        QuestionBean tempbean;
        boolean flag = false, finalResult = false, tempResult1 = false;
        int i, start = 0, end = 0, operator1 = 0;
        expression = expression.replaceAll("\\s+", "");
        expression = expression.replaceAll(",", ".");
        for (i = 0; i < expression.length(); i++) {
            switch (expression.charAt(i)) {
                case '{':
                    start = i + 1;
                    break;
                case '}':
                    end = i;
                    temp = expression.substring(start, end).toUpperCase();
                    if ((getQuestionBeanForIdentifier(temp) == null)) {
                        ans2 = "";
                    } else {
                        tempbean = getQuestionBeanForIdentifier(temp);
                        if (tempbean.getAnswer() != null && !tempbean.getAnswer().equals("")) {
                            ans2 = tempbean.getAnswer();
                        } else if (tempbean.getLovCode() != null && !tempbean.getLovCode().equals("")) {
                            ans2 = tempbean.getLovCode();
                        } else ans2 = "";
                    }
                    //ans2 = getTestid(temp); //test
                    start = 0;
                    end = 0;
                    break;
                case '=':
                    i++;
                    operator1 = 0;
                    break;
                case '!':
                    i++;
                    operator1 = 1;
                    break;
                case '>':
                    if (expression.charAt(i + 1) == '=') {
                        i++;
                        operator1 = 2;
                    } else operator1 = 3;
                    break;
                case '<':
                    if (expression.charAt(i + 1) == '=') {
                        i++;
                        operator1 = 4;
                    } else operator1 = 5;
                    break;
                case '\'':
                    if (flag) {
                        flag = false;
                        end = i;
                        ans1 = expression.substring(start, end);
                        if (operator1 == 0) {
                            results.push(ans1.equals(ans2));
                        } else if (operator1 == 1) results.push(!ans1.equals(ans2));
                        else if (operator1 == 2)
                            results.push(Double.parseDouble(ans2.replaceAll(",", ".")) >= Double.parseDouble(ans1.replaceAll(",", ".")));
                        else if (operator1 == 3)
                            results.push(Double.parseDouble(ans2.replaceAll(",", ".")) > Double.parseDouble(ans1.replaceAll(",", ".")));
                        else if (operator1 == 4)
                            results.push(Double.parseDouble(ans2.replaceAll(",", ".")) <= Double.parseDouble(ans1.replaceAll(",", ".")));
                        else if (operator1 == 5)
                            results.push(Double.parseDouble(ans2.replaceAll(",", ".")) < Double.parseDouble(ans1.replaceAll(",", ".")));
                        start = 0;
                        end = 0;
                        ans1 = "";
                        ans2 = "";
                        temp = "";
                        tempbean = null;
                    } else {
                        start = i + 1;
                        flag = true;
                    }
                    break;
                case '&':
                    i++;
                    operators.push(true);
                    break;
                case '|':
                    i++;
                    operators.push(false);
                    break;
            }

        }
        flag = true;
        while (!results.isEmpty()) {
            if (flag) {
                finalResult = results.pop();
                if (results.isEmpty()) return finalResult;
                else {
                    tempResult1 = results.pop();
                    if (operators.pop()) finalResult = finalResult && tempResult1;
                    else finalResult = finalResult || tempResult1;
                    flag = false;
                }
            } else {
                tempResult1 = results.pop();
                if (operators.pop()) finalResult = finalResult && tempResult1;
                else finalResult = finalResult || tempResult1;
            }
        }
        return finalResult;
    }

    private boolean loadQuestionForm() {
        boolean isLastQuestion = true;
        if (!listOfQuestions.isEmpty()) {
            QuestionBean mbean = listOfQuestions.get(listOfQuestions.size() - 1);
            questionSize = DynamicFormActivity.listOfIdentifier.indexOf(mbean.getIdentifier_name()) + 1;
        }
        int lastposition = questionSize;
        for (; lastposition < listOfQuestion.size(); lastposition++) {
            QuestionBean bean = listOfQuestion.get(DynamicFormActivity.listOfIdentifier.get(lastposition));
            questionSize++;
            if (bean.isVisible()) {
                String relevantExpression = bean.getRelevant_question();
                String relevantMandatory = bean.getRelevant_mandatory();
                if (relevantExpression == null) relevantExpression = "";
                if (relevantMandatory == null) relevantMandatory = "";
                bean.setRelevantMandatory(isQuestVisibleIfRelevantMandatory(relevantMandatory, bean));
                if (isQuestVisibleIfRelevant(relevantExpression, bean) || bean.isRelevantMandatory()) {
                    if (isAutoHideQuestion(bean)) {
                        if (QuestionBean.getAnswer(bean) != null) {
                            loadSingleQuestion(bean, lastposition, false);
                            isLastQuestion = false;
                            break;
                        } else { // Question will be hide if the answer is null
                            bean.setVisible(false);
                            QuestionBean.resetAnswer(bean);
                            //removeQuestionLabel(lastposition);
                            calculateQuestionBean(bean);
                        }
                    } else {
                        loadSingleQuestion(bean, lastposition, false);
                        isLastQuestion = false;
                        break;
                    }
                } else {
                    bean.setVisible(false);
                    QuestionBean.resetAnswer(bean);
                    removeQuestionLabel(lastposition);
                    calculateQuestionBean(bean);
                }
            } else {
                if (lastposition == 0) {
                    doNext(false);
                } else {
                    String relevantExpression = bean.getRelevant_question();
                    String relevantMandatory = bean.getRelevant_mandatory();
                    if (relevantExpression == null) relevantExpression = "";
                    if (relevantMandatory == null) relevantMandatory = "";
                    if (isQuestVisibleIfRelevant(relevantExpression, bean)) {
                        bean.setRelevantMandatory(isQuestVisibleIfRelevantMandatory(relevantMandatory, bean));
                        QuestionSet tempQuestion = QuestionSetDataAccess.getOne(getContext(), DynamicFormActivity.header.getUuid_scheme(), bean.getQuestion_id(), bean.getQuestion_group_id());
                        if (tempQuestion != null && tempQuestion.getIs_visible().equals(Global.TRUE_STRING)) {
                            loadSingleQuestion(bean, lastposition, false);
                            isLastQuestion = false;
                            break;
                        } else {
                            calculateQuestionBean(bean);
                        }
                    } else {
                        bean.setVisible(false);
                        QuestionBean.resetAnswer(bean);
                        calculateQuestionBean(bean);
                    }
                }
            }
            if (listOfQuestion.size() == lastposition)
                isLastQuestion = true;
        }
        return isLastQuestion;
    }

    private boolean isAutoHideQuestion(QuestionBean bean) {
        boolean autoHideQuestion = false;
        if (listOfAutoHideQuestion != null) {
            for (String str : listOfAutoHideQuestion) {
                if (bean.getIdentifier_name().equals(str)) {
                    autoHideQuestion = true;
                    break;
                }
            }
        }
        return autoHideQuestion;
    }

    @SuppressLint("StaticFieldLeak")
    private void getOTR(final QuestionBean bean, final RuleParam ruleParam) {
        new AsyncTask<Void, Void, Double>() {
            ProgressDialog dialog;
            Context context = getContext();

            @Override
            protected Double doInBackground(Void... voids) {
                try {
                    QuestionBean qBean = Constant.listOfQuestion.get(ruleParam.getAssetConditionRefId());
                    if (qBean.getLovCode().toUpperCase().equalsIgnoreCase(ruleParam.getCodeIfRUle())) {
                        GenericRuleJexlLogic ruleJexlLogic = new GenericRuleJexlLogic();
                        return ruleJexlLogic.basePrice(context, ruleParam);
                    } else {
                        List<Query.Constraint> constraints = finalConstraint(ruleParam.getConstraints());
                        MarketPrice basePrice = MarketPriceDataAccess.find(context, constraints);
                        marketPrice = basePrice.getMarket_price();
                        tolerancePrctg = basePrice.getTolerance_prctg();
                        return marketPrice;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

                return 0d;
            }

            @Override
            protected void onPreExecute() {
                super.onPreExecute();

                dialog = new ProgressDialog(context);
                dialog.setMessage(context.getString(R.string.please_wait));
                dialog.setCancelable(false);
                dialog.show();
            }

            @Override
            protected void onPostExecute(Double value) {
                super.onPostExecute(value);
                dialog.dismiss();

                for (int i = 0; i < listOfQuestions.size(); i++) {
                    QuestionBean mBean = listOfQuestions.get(i);
                    if (mBean.getUuid_question_set().equals(bean.getUuid_question_set())) {
                        if (value != null) mBean.setAnswer(value.toString());
                        else mBean.setAnswer("0");

                        focusGroup = listOfQuestionGroup.indexOf(mBean.getQuestion_group_name());
                        focusPosition = listOfQuestionBean.get(listOfQuestionGroup.get(focusGroup)).indexOf(mBean);
                        final int position = focusPosition + getCounterListBeforeGroup(focusGroup, false);

                        changeItem(position);
                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                questionAdapter.expand(focusGroup);
//                                int finalPosition = focusPosition + getCounterListBeforeGroup(focusGroup,false);
                                setFocusable(position, 800);
                            }
                        }, 200);
                        break;
                    }
                }

                Utility.freeMemory();
            }
        }.execute();
    }

    /**
     * Get Options from Database and check filter from choice filters.
     * ex : {identifier1},{identifier2}
     * ex : {$LOGIN_ID} : for get login Id from active User
     * ex : {$UUID_USER} : for get uuid user from active User
     * ex : {$BRANCH_ID} : for get branch Id from active User
     * ex : {$BRANCH_NAME} : for get branch name from active User
     * ex : {$FLAG_JOB} : for get flag job from active User
     * ex : {$DEALER_NAME} : for get dealer name from active User
     *
     * @param bean : Question Bean
     * @return
     */
    protected List<OptionAnswerBean> getOptionsForQuestion(QuestionBean bean, boolean firstRequest) {
        List<String> filters = new ArrayList<String>();
        int constraintAmount = 0;
        if (bean.getChoice_filter() != null) {
            String[] tempfilters = Tool.split(bean.getChoice_filter(), Global.DELIMETER_DATA3);
            for (String newFilter : tempfilters) {
                int idxOfOpenBrace = newFilter.indexOf('{');
                if (idxOfOpenBrace != -1) {                    //there's {, prepare to replace what inside the {}
                    int idxOfCloseBrace = newFilter.indexOf('}');
                    String tempIdentifier = newFilter.substring(idxOfOpenBrace + 1, idxOfCloseBrace).toUpperCase();
                    if (tempIdentifier.contains("%")) {
                        filters.add(tempIdentifier);
                    } else {
                        int idxOfOpenAbs = tempIdentifier.indexOf("$");
                        if (idxOfOpenAbs != -1) {
                            String finalIdentifier = tempIdentifier.substring(idxOfOpenAbs + 1);
                            if (finalIdentifier.equals(Global.IDF_LOGIN_ID)) {
                                String loginId = GlobalData.getSharedGlobalData().getUser().getLogin_id();
                                int idxOfOpenAt = loginId.indexOf('@');
                                if (idxOfOpenAt != -1) {
                                    loginId = loginId.substring(0, idxOfOpenAt);
                                }
                                filters.add(loginId);
                            } else if (finalIdentifier.equals(Global.IDF_BRANCH_ID)) {
                                String branchId = GlobalData.getSharedGlobalData().getUser().getBranch_id();
                                filters.add(branchId);
                            } else if (finalIdentifier.equals(Global.IDF_BRANCH_NAME)) {
                                String branchName = GlobalData.getSharedGlobalData().getUser().getBranch_name();
                                filters.add(branchName);
                            } else if (finalIdentifier.equals(Global.IDF_UUID_USER)) {
                                String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                                filters.add(uuidUser);
                            } else if (finalIdentifier.equals(Global.IDF_JOB)) {
                                String job = GlobalData.getSharedGlobalData().getUser().getFlag_job();
                                filters.add(job);
                            } else if (finalIdentifier.equals(Global.IDF_DEALER_NAME)) {
                                String dealerName = GlobalData.getSharedGlobalData().getUser().getDealer_name();
                                filters.add(dealerName);
                            } else if (finalIdentifier.equals(Global.IDF_UUID_BRANCH)) {
                                String uuidBranch = GlobalData.getSharedGlobalData().getUser().getUuid_branch();
                                filters.add(uuidBranch);
                            } else if (finalIdentifier.equals(Global.IDF_DEALER_ID)) {
                                String dealerId = GlobalData.getSharedGlobalData().getUser().getUuid_dealer();
                                filters.add(dealerId);
                            } else if (finalIdentifier.equals(Global.IDF_BRANCH_TYPE)) {
                                String dealerId = GlobalData.getSharedGlobalData().getUser().getBranch_type();
                                filters.add(dealerId);
                            } else if (finalIdentifier.equals(Global.IDF_TASK_ID)) {
                                String taskId = DynamicFormActivity.header.getTask_id();
                                filters.add(taskId);
                            } else if (finalIdentifier.equals(Global.IDF_PRODUCT_NAME)) {
                                String productName = DynamicFormActivity.header.getProduct_name();
                                filters.add(productName);
                            } else if (finalIdentifier.equals(Global.IDF_JENIS_ASSET)) {
                                String jenisAsset = DynamicFormActivity.header.getJenis_asset();
                                filters.add(jenisAsset);
                            }
                            constraintAmount++;
                        } else {
                            QuestionBean bean2 = listOfQuestion.get(tempIdentifier);
                            if (bean2 != null) {
                                if (Global.AT_TEXT_WITH_SUGGESTION.equals(bean2.getAnswer_type())) {
                                    filters.add(bean2.getLovCode());
                                } else if (QuestionViewAdapter.IsTextQuestion(bean2.getAnswer_type())) {
                                    filters.add(bean2.getAnswer());
                                } else {
                                    for (OptionAnswerBean answerBean : bean2.getSelectedOptionAnswers()) {
                                        filters.add(answerBean.getCode());
                                    }
                                }
                                bean2.setRelevanted(true);
                                constraintAmount++;
                            }
                        }
                    }
                }
            }
        }
        List<OptionAnswerBean> optionAnswers;
        optionAnswers = GetLookupFromDB(bean, filters);
        filterData = filters;
        boolean isRVinFront = GeneralParameterDataAccess.isRvInFrontEnable(getActivity(), GlobalData.getSharedGlobalData().getUser().getUuid_user());

        if (firstRequest) {
            if (optionAnswers == null || optionAnswers.isEmpty()) {
                try {
                    needStop = true;
                    if (isRVinFront && bean.getTag() != null && bean.getTag().equals(Global.TAG_RV_NUMBER) && Global.APPLICATION_COLLECTION.equalsIgnoreCase(GlobalData.getSharedGlobalData().getApplication())) {
                        syncRvNumber(bean);
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    if (Global.IS_DEV)
                        e.printStackTrace();
                }
                optionAnswers = GetLookupFromDB(bean, filters);
            }
        }
        if (Global.APPLICATION_COLLECTION.equalsIgnoreCase(GlobalData.getSharedGlobalData().getApplication()) && bean.getTag() != null)
            if (isRVinFront && bean.getTag().equals(Global.TAG_RV_NUMBER)) {
                if (bean.getSelectedOptionAnswers() != null && !bean.getSelectedOptionAnswers().isEmpty()) {
                    List<OptionAnswerBean> opBean = bean.getSelectedOptionAnswers();
                    OptionAnswerBean answerBean = opBean.get(0);
                    Lookup lookup = LookupDataAccess.getOne(getActivity(), answerBean.getUuid_lookup());
                    if (lookup != null)
                        optionAnswers.add(answerBean);
                }
            }
        return optionAnswers;
    }

    private void syncRvNumber(final QuestionBean bean) {
        final String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        SyncRVRequest request = new SyncRVRequest();
        request.setLastDtmCrt(ReceiptVoucherDataAccess.getLastDate(getActivity(), uuidUser));

        SyncRVTask task = new SyncRVTask(getActivity(), request, new SyncRvListener() {
            ProgressDialog dialog;

            @Override
            public void onProgress() {
                dialog = ProgressDialog.show(getActivity(), "Sync RV Number", getString(R.string.please_wait), true, false);
            }

            @Override
            public void onError(SyncRVResponse response) {
                if (dialog != null && dialog.isShowing()) {
                    dialog.dismiss();
                }

                if (response.getErrorMessage() != null) {
                    String message = getString(R.string.get_lookup_failed, Global.TAG_RV_NUMBER);
                    Toast.makeText(getActivity(), message, Toast.LENGTH_LONG).show();
                    needStop = true;
                }
            }

            @Override
            public void onSuccess(SyncRVResponse response) {
                if (dialog != null && dialog.isShowing()) {
                    dialog.dismiss();
                }

                List<ReceiptVoucher> rvNumbers = response.getListReceiptVoucher();

                if (rvNumbers != null && !rvNumbers.isEmpty()) {
                    try {
                        ReceiptVoucherDataAccess.addNewReceiptVoucher(getActivity(),
                                GlobalData.getSharedGlobalData().getUser().getUuid_user(),
                                rvNumbers);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        e.printStackTrace();
                        ACRA.getErrorReporter().putCustomData("errorRV", e.getMessage());
                        ACRA.getErrorReporter().handleSilentException(new Exception("Error: Insert RV Error. " + e.getMessage()));
                        Toast.makeText(getActivity(), e.getMessage(), Toast.LENGTH_SHORT).show();
                    }
                    try {
                        boolean isRVinFront = GeneralParameterDataAccess.isRvInFrontEnable(getActivity(), uuidUser);
                        if (isRVinFront) {
                            List<Lookup> lookupRVList = new ArrayList<>();
                            for (int i = 0; i < rvNumbers.size(); i++) {
                                ReceiptVoucher rv = rvNumbers.get(i);
                                Lookup lookup = new Lookup();

                                lookup.setUuid_lookup(rv.getUuid_receipt_voucher());
                                lookup.setCode(rv.getUuid_receipt_voucher());
                                lookup.setValue(rv.getRv_number());
                                lookup.setSequence(i);
                                lookup.setDtm_upd(rv.getDtm_crt());
                                lookup.setLov_group(Global.TAG_RV_NUMBER);

                                lookup.setIs_active(Global.TRUE_STRING);
                                lookup.setIs_deleted(Global.FALSE_STRING);
                                lookupRVList.add(lookup);
                            }
                            LookupDataAccess.addOrUpdateAll(getActivity(), lookupRVList);
                        }
                    } catch (Exception e) {
                        FireCrash.log(e);
                        e.printStackTrace();
                    }
                }
                List<OptionAnswerBean> options = getOptionsForQuestion(bean, false);
                for (int i = 0; i < listOfQuestions.size(); i++) {
                    QuestionBean mBean = listOfQuestions.get(i);
                    if (mBean.getUuid_question_set().equals(bean.getUuid_question_set())) {
                        mBean.setOptionAnswers(options);
                        int groupIdx = listOfQuestionGroup.indexOf(mBean.getQuestion_group_name()) + 1;
                        int position = i + groupIdx;
                        changeItem(position);
                    }
                }
            }
        });
        task.execute();
    }

    private List<OptionAnswerBean> GetLookupFromDB(QuestionBean bean, List<String> filters) {
        List<OptionAnswerBean> optionAnswers = new ArrayList<OptionAnswerBean>();
        if (!filters.isEmpty()) {
            if (filters.size() == 1) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilter(getActivity(), bean.getLov_group(), filters.get(0));
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            } else if (filters.size() == 2) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilter(getActivity(), bean.getLov_group(), filters.get(0), filters.get(1));
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            } else if (filters.size() == 3) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilter(getActivity(), bean.getLov_group(), filters.get(0), filters.get(1), filters.get(2));
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            } else if (filters.size() == 4) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilter(getActivity(), bean.getLov_group(), filters.get(0), filters.get(1), filters.get(2), filters.get(3));
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            } else if (filters.size() == 5) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilter(getActivity(), bean.getLov_group(), filters.get(0), filters.get(1), filters.get(2), filters.get(3), filters.get(4));
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            }

        } else {
            if (bean.getChoice_filter() != null && bean.getChoice_filter().length() > 0) {
                List<Lookup> lookups = new ArrayList<Lookup>();
                optionAnswers = OptionAnswerBean.getOptionList(lookups);
            } else {
                String lovGroup = bean.getLov_group();
                if (lovGroup != null) {
                    List<Lookup> lookups = LookupDataAccess.getAllByLovGroup(getActivity(), lovGroup);
                    if (lookups != null)
                        optionAnswers = OptionAnswerBean.getOptionList(lookups);
                }
            }
        }
        return optionAnswers;
    }

    private String doCalculate(QuestionBean bean) {
//        bean.setRelevanted(true);
        String formula = bean.getCalculate();
        String expression = formula;
        double total = 0;

        try {
            if (!("var").equalsIgnoreCase(expression.substring(0, 3))) {
                return "0";
            }
        } catch (Exception e) {
            FireCrash.log(e);
            Logger.e("FragmentQuestion", e);
            return "0";
        }

        List<QuestionBean> questionList = new ArrayList<QuestionBean>();
        String resultformula2 = formula.substring(0, formula.indexOf("for"));
        resultformula2 = resultformula2.replace("_var = 0", "");
        resultformula2 = resultformula2.replace("var ", "");
        resultformula2 = resultformula2.replace(" ", "");
        resultformula2 = resultformula2.replace("\r", "");
        resultformula2 = resultformula2.replace("\n", "");
        String[] nFormula2 = resultformula2.split(";");
        for (String idf : nFormula2) {
            //setRelevanted should be here
            //alternative for isSpecialCaseChangeFlag
            QuestionBean tempBean = listOfQuestion.get(idf.toUpperCase());
            if (tempBean.getAnswer_type().equals(Global.AT_DECIMAL)) {
                try {
                    NumberFormat nf = NumberFormat.getInstance(Locale.US);
                    if (tempBean.getAnswer() != null && !"".equals(tempBean.getAnswer())) {
                        Double finalAnswer = nf.parse(tempBean.getAnswer()).doubleValue();
                        tempBean.setAnswer(finalAnswer.toString());
                    }
                } catch (ParseException e) {
                    FireCrash.log(e);
                    e.printStackTrace();
                }
            } else if (tempBean.getAnswer_type().equals(Global.AT_CURRENCY)) {
                try {
                    if (StringUtils.isNotBlank(tempBean.getAnswer())) {
                        String tempAnswer = Tool.deleteAll(tempBean.getAnswer(), ",");
                        String[] intAnswer = Tool.split(tempAnswer, ".");
                        if (intAnswer.length > 1) {
                            if (intAnswer[1].equals("00"))
                                tempBean.setAnswer(intAnswer[0]);
                            else {
                                tempBean.setAnswer(tempAnswer);
                            }
                        } else {
                            tempBean.setAnswer(tempAnswer);
                        }
                    }

                } catch (Exception e) {
                    FireCrash.log(e);
                    e.printStackTrace();
                }
            }
            questionList.add(tempBean);
        }

        JexlContext context = new MapContext();

        context.set("listOfQuestion", questionList);
        context.set("qBean", new QuestionBean(bean));
        context.set("dateformatter", new DateFormatter());
        context.set("bean", bean);
        context.set("result", total);
        Object value = null;
        try {
            value = jexlEngine.createScript(expression).execute(context);
        } catch (Exception e) {
            FireCrash.log(e);
            try {
                Toast.makeText(getActivity(), getString(R.string.calculate_failed), Toast.LENGTH_LONG).show();
            } catch (Exception e2) {
                FireCrash.log(e2);
                e.printStackTrace();
            }
        }
        if (value != null) {
            try {
                return String.format(Locale.US, "%,.2f", value);
            } catch (Exception e) {
                FireCrash.log(e);
                try {
                    int answer = (int) value;
                    return String.format(Locale.US, "%d", answer);
                } catch (Exception e2) {
                    FireCrash.log(e2);
                    return value.toString();
                }
            }
        } else {
            return "0";
        }
    }

    /**
     * Check Relevant from Question Set.
     * ex : {identifier}==value : for get value from Question Set
     * ex : {$LOGIN_ID}==value : for get value from Login ID in active User
     * ex : {$UUID_USER}==value : for get value from uuid user in active User
     * ex : {$BRANCH_ID}==value : for get value from Branch ID in active User
     * ex : {$BRANCH_NAME}==value : for get value from Branch Name in active User
     * ex : {$FLAG_JOB}==value : for get value from Branch Name in active User
     * ex : {$DEALER_NAME}==value : for get value from Dealer Name in active User
     *
     * @param relevantExpression - Relevant expression from Question Set
     * @param question           - Question Bean
     * @return boolean - True if Relevant and False if not Relevant
     */
    public boolean isQuestVisibleIfRelevant(String relevantExpression, QuestionBean question) {
        boolean result = false;
        String convertedExpression = relevantExpression;        //make a copy of
        if (convertedExpression == null || convertedExpression.length() == 0) {
            return true;
        } else {
            boolean needReplacing = true;
            while (needReplacing) {
                //replace application modifier
                convertedExpression = replaceModifiers(convertedExpression);
                int idxOfOpenBrace = convertedExpression.indexOf('{');
                if (idxOfOpenBrace != -1) {
                    //there's {, prepare to replace what inside the {}
                    int idxOfCloseBrace = convertedExpression.indexOf('}');
                    String identifier = convertedExpression.substring(idxOfOpenBrace + 1, idxOfCloseBrace);
                    int idxOfOpenAbs = identifier.indexOf("$");
                    if (idxOfOpenAbs != -1) {
                        String finalIdentifier = identifier.substring(idxOfOpenAbs + 1);
                        String flatAnswer = "";
                        if (finalIdentifier.equals(Global.IDF_LOGIN_ID)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getLogin_id();
                            int idxOfOpenAt = flatAnswer.indexOf('@');
                            if (idxOfOpenAt != -1) {
                                flatAnswer = flatAnswer.substring(0, idxOfOpenAt);
                            }
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_ID)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_id();
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_NAME)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_name();
                        } else if (finalIdentifier.equals(Global.IDF_UUID_USER)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                        } else if (finalIdentifier.equals(Global.IDF_JOB)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getFlag_job();
                        } else if (finalIdentifier.equals(Global.IDF_DEALER_NAME)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getDealer_name();
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_TYPE)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_type();
                        } else if (finalIdentifier.equals(Global.IDF_TASK_ID)) {
                            String taskId = DynamicFormActivity.header.getTask_id();
                            flatAnswer = taskId;
                        } else if (finalIdentifier.equals(Global.IDF_SOURCE_DATA)) {
                            flatAnswer = DynamicFormActivity.header.getSource_data();
                        }

                        if (flatAnswer != null && flatAnswer.length() > 0) {
                            convertedExpression = convertedExpression.replace("{" + identifier + "}", flatAnswer);
                        } else {
                            //if there's no answer, just hide the question
                            return false;
                        }

                    } else {
                        QuestionBean bean = listOfQuestion.get(identifier);
                        if (bean != null) {
                            String flatAnswer = "";
                            if (bean.getIs_visible().equals(Global.FALSE_STRING))
                                flatAnswer = "false";
                            else
                                flatAnswer = QuestionBean.getAnswer(bean);
                            if (flatAnswer != null && flatAnswer.length() > 0) {
                                //NOTE: though it's possible to just iterate on flatAnswer substrings, we prefer to stay on method if size is 1
                                String answers[] = Tool.split(flatAnswer, Global.DELIMETER_DATA);
                                if (answers.length == 1) {
                                    convertedExpression = convertedExpression.replace("{" + identifier + "}", answers[0]);
                                } else {
                                    //NOTE: going into in-depth loop, won't go outside of this 'else'
                                    for (int i = 0; i < answers.length; i++) {
                                        String convertedSubExpression = convertedExpression.replace("{" + identifier + "}", answers[i]);
                                        boolean isVisible = isQuestVisibleIfRelevant(convertedSubExpression, question);
                                        if (isVisible) {
                                            return true;
                                        }
                                    }
                                    return false;
                                }
                            } else {
                                //if there's no answer, just hide the question
                                if (Global.TAG_VALIDATION_CHECK.equals(question.getTag())) {
                                    convertedExpression = convertedExpression.replace("{" + identifier + "}", "\"\"");
                                } else {
                                    return false;
                                }
                            }
                        } else {
                            convertedExpression = convertedExpression.replace("{" + identifier + "}", "\"\"");
                        }
                    }
                } else {
                    needReplacing = false;
                }
            }
            try {
                OperatorSet opSet = OperatorSet.getStandardOperatorSet();
                opSet.addOperator("!=", NotEqualSymbol.class);
                opSet.addOperator("AND", AndSymbol.class);
                opSet.addOperator("OR", OrSymbol.class);
                Expression exp = new Expression(convertedExpression);
                exp.setOperatorSet(opSet);
                result = exp.evaluate().toBoolean();
                return result;
            } catch (Exception e) {
                FireCrash.log(e);
//                if (Global.IS_DEV)
                e.printStackTrace();
                return false;
            }
        }
    }


    /**
     * Check Relevant from Question Set.
     * ex : {identifier}==value : for get value from Question Set
     * ex : {$LOGIN_ID}==value : for get value from Login ID in active User
     * ex : {$UUID_USER}==value : for get value from uuid user in active User
     * ex : {$BRANCH_ID}==value : for get value from Branch ID in active User
     * ex : {$BRANCH_NAME}==value : for get value from Branch Name in active User
     * ex : {$FLAG_JOB}==value : for get value from Branch Name in active User
     * ex : {$DEALER_NAME}==value : for get value from Dealer Name in active User
     *
     * @param relevantExpression - Relevant expression from Question Set
     * @param question           - Question Bean
     * @return boolean - True if Relevant and False if not Relevant
     */
    public boolean isQuestVisibleIfRelevantMandatory(String relevantExpression, QuestionBean question) {
        boolean result = false;
        String convertedExpression = relevantExpression;        //make a copy of
        if (convertedExpression == null || convertedExpression.length() == 0) {
            return false;
        } else {
            boolean needReplacing = true;
            while (needReplacing) {
                //replace application modifier
                convertedExpression = replaceModifiers(convertedExpression);
                int idxOfOpenBrace = convertedExpression.indexOf('{');
                if (idxOfOpenBrace != -1) {
                    //there's {, prepare to replace what inside the {}
                    int idxOfCloseBrace = convertedExpression.indexOf('}');
                    String identifier = convertedExpression.substring(idxOfOpenBrace + 1, idxOfCloseBrace);
                    int idxOfOpenAbs = identifier.indexOf("$");
                    if (idxOfOpenAbs != -1) {
                        String finalIdentifier = identifier.substring(idxOfOpenAbs + 1);
                        String flatAnswer = "";
                        if (finalIdentifier.equals(Global.IDF_LOGIN_ID)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getLogin_id();
                            int idxOfOpenAt = flatAnswer.indexOf('@');
                            if (idxOfOpenAt != -1) {
                                flatAnswer = flatAnswer.substring(0, idxOfOpenAt);
                            }
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_ID)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_id();
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_NAME)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_name();
                        } else if (finalIdentifier.equals(Global.IDF_UUID_USER)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                        } else if (finalIdentifier.equals(Global.IDF_JOB)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getFlag_job();
                        } else if (finalIdentifier.equals(Global.IDF_DEALER_NAME)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getDealer_name();
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_TYPE)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_type();
                        } else if (finalIdentifier.equals(Global.IDF_TASK_ID)) {
                            String taskId = DynamicFormActivity.header.getTask_id();
                            flatAnswer = taskId;
                        }

                        if (flatAnswer != null && flatAnswer.length() > 0) {
                            convertedExpression = convertedExpression.replace("{" + identifier + "}", flatAnswer);
                        } else {
                            //if there's no answer, just hide the question
                            return false;
                        }

                    } else {
                        QuestionBean bean = listOfQuestion.get(identifier);
                        if (bean != null) {
                            String flatAnswer = "";
                            if (bean.getIs_visible().equals(Global.FALSE_STRING))
                                flatAnswer = "false";
                            else
                                flatAnswer = QuestionBean.getAnswer(bean);
                            if (flatAnswer != null && flatAnswer.length() > 0) {
                                //NOTE: though it's possible to just iterate on flatAnswer substrings, we prefer to stay on method if size is 1
                                String answers[] = Tool.split(flatAnswer, Global.DELIMETER_DATA);
                                if (answers.length == 1) {
                                    convertedExpression = convertedExpression.replace("{" + identifier + "}", answers[0]);
                                } else {
                                    //NOTE: going into in-depth loop, won't go outside of this 'else'
                                    for (int i = 0; i < answers.length; i++) {
                                        String convertedSubExpression = convertedExpression.replace("{" + identifier + "}", answers[i]);
                                        boolean isVisible = isQuestVisibleIfRelevantMandatory(convertedSubExpression, question);
                                        if (isVisible) {
                                            return true;
                                        }
                                    }
                                    return false;
                                }
                            } else {
                                //if there's no answer, just hide the question
                                return false;
                            }
                        } else {
                            convertedExpression = convertedExpression.replace("{" + identifier + "}", "\"\"");
                        }
                    }
                } else {
                    needReplacing = false;
                }
            }
            try {
                OperatorSet opSet = OperatorSet.getStandardOperatorSet();
                opSet.addOperator("!=", NotEqualSymbol.class);
                Expression exp = new Expression(convertedExpression);
                exp.setOperatorSet(opSet);
                result = exp.evaluate().toBoolean();
                return result;
            } catch (Exception e) {
                FireCrash.log(e);
//                if (Global.IS_DEV)
                e.printStackTrace();
                return false;
            }
        }
    }

    protected String replaceModifiers(String sourceString) {
        String newString = sourceString;
        //replace branch modifier
        String branch = GlobalData.getSharedGlobalData().getUser().getBranch_id();
        newString = newString.replace(QuestionBean.PLACEMENT_KEY_BRANCH, branch);
        //replace user modifier
        String user = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        return newString.replace(QuestionBean.PLACEMENT_KEY_USER, user);
    }

    @Override
    public void onResume() {
        super.onResume();
        if (questionAdapter != null) {
//            questionAdapter.notifyDataSetChanged();
        }
    }

    private void setLocationForLocationQuestion() {
        QuestionBean mBean = focusBean;
        LocationInfo info = LocationTagingView.locationInfo;
        LocationInfo2 infoFinal = new LocationInfo2(info);
        if (mBean.getAnswer_type().equals(Global.AT_LOCATION_WITH_ADDRESS))
            mBean.setAnswer(LocationTrackingManager.toAnswerString_short(infoFinal,
                    LocationTagingView.address));
        else
            mBean.setAnswer(LocationTrackingManager.toAnswerString_short(infoFinal));
        if(infoFinal.getAccuracy()==null){
            infoFinal.setAccuracy(0);
        }
        mBean.setLocationInfo(infoFinal);
        mBean.setIsCanChange(true);
        mBean.setChange(true);

        int position = focusPosition + getCounterListBeforeGroup(focusGroup, false);
        changeItem(position);
        Utility.freeMemory();
    }

    private void updateLocationForImageQuestion(String lat, String lng){
        if(lat.equals("0") && lng.equals("0")){
            Toast.makeText(getContext(), "Something is wrong when updating image location!", Toast.LENGTH_SHORT).show();
        }else{
            QuestionBean bean = focusBean;
            byte[] data = bean.getImgAnswer();
            Bitmap bm = Utils.byteToBitmap(data);
            int width = bm.getWidth();
            int height = bm.getHeight();
            long size = bean.getImgAnswer().length;
            String formattedSize = Formatter.formatByteSize(size);

            LocationInfo locationInfo = bean.getLocationInfo();
            locationInfo.setLatitude(lat);
            locationInfo.setLongitude(lng);
            String location = LocationTrackingManager.toAnswerString_short(locationInfo);

            String text = width + " x " + height +
                    ". Size " + formattedSize + "\n" + location;
            bean.setAnswer(text);

            int position = focusPosition + getCounterListBeforeGroup(focusGroup, false);
            changeItem(position);
            Utility.freeMemory();
        }
    }

    private void updateLocationForLocationQuestion(String lat, String lng){
        if(lat.equals("0") && lng.equals("0")){
            Toast.makeText(getContext(), "Something is wrong when updating location!", Toast.LENGTH_SHORT).show();
        }else {
            QuestionBean bean = focusBean;

            LocationInfo locationInfo = bean.getLocationInfo();
            locationInfo.setLatitude(lat);
            locationInfo.setLongitude(lng);

            String location = LocationTrackingManager.toAnswerString_short(locationInfo);
            bean.setAnswer(location);

            int position = focusPosition + getCounterListBeforeGroup(focusGroup, false);
            changeItem(position);
            Utility.freeMemory();
        }
    }

    public int getCounterListBeforeGroup(int group, boolean isReview) {
        int countListBefore = 0;
        ExpandableRecyclerView.Adapter adapter;
        for (int i = 0; i < group; i++) {
            if (isReview) {
                adapter = reviewAdapter;
            } else {
                adapter = questionAdapter;
            }
            if (adapter.isExpanded(i))
                countListBefore += adapter.getChildItemCount(i);//listOfQuestionBean.get(listOfQuestionGroup.get(i)).size();
        }
        countListBefore += (group + 1);
        return countListBefore;
    }

    private void setImageForImageQuestion(String path, boolean isFromGallery) {
        if (path != null) {
            Uri uri = Uri.parse(path);
            File file = new File(uri.getPath());
            processImageFile(file, isFromGallery);
            Utility.freeMemory();
        }
    }

    private void processImageFile(File file, boolean isFromGallery) {
        try {
            QuestionBean mBean = focusBean;
            String formattedSize;
            String indicatorGPS = "";
            boolean isGeoTagged;
            boolean isGeoTaggedGPSOnly;
            boolean isImageDkcp = false;
            int[] res;
            ExifData exifData = Utils.getDataOnExif(file);
            int rotate = exifData.getOrientation();
            float scale;
            int newSize = 0;
            int quality = Utils.picQuality;
            int thumbHeight = Utils.picHeight;
            int thumbWidht = Utils.picWidth;

            if (null != mBean.getTag()) {
                isImageDkcp = Global.AT_OCR_W_GALLERY.equals(mBean.getAnswer_type()) && mBean.getTag().contains(Global.TAG_OCR_W_GALLERY);
            }

            boolean isHQ = false;
            if (mBean.getImg_quality() != null && mBean.getImg_quality().equalsIgnoreCase(Global.IMAGE_HQ)) {
                thumbHeight = Utils.picHQHeight;
                thumbWidht = Utils.picHQWidth;
                quality = Utils.picHQQuality;
                isHQ = true;
            }

            boolean isHeightScale = thumbHeight >= thumbWidht;

            System.gc();
            byte[] data = null;
            try {
                if (isHeightScale) {
                    scale = (float) thumbHeight / exifData.getHeight();
                    newSize = Math.round(exifData.getWidth() * scale);
                    data = Utils.resizeBitmapFileWithWatermark(file, rotate, newSize, thumbHeight, quality, getActivity(), isHQ);
                } else {
                    scale = (float) thumbWidht / exifData.getWidth();
                    newSize = Math.round(exifData.getHeight() * scale);
                    data = Utils.resizeBitmapFileWithWatermark(file, rotate, thumbWidht, newSize, quality, getActivity(), isHQ);
                }
            } catch (Exception e) {
                FireCrash.log(e);
                data = null;
            }
            if (data != null) {
                mBean.setImgAnswer(data);
                deleteLatestPictureCreate();
                try {
                    if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.KITKAT && !GlobalData.getSharedGlobalData().isUseOwnCamera()) {
                        Utils.deleteLatestPicture();
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    try {
                        String manufacture = android.os.Build.MANUFACTURER;
                        if (manufacture.contains("LGE") && !GlobalData.getSharedGlobalData().isUseOwnCamera()) {
                            Utils.deleteLatestPictureLGE();
                        }
                    } catch (Exception e2) {
                        FireCrash.log(e2);
                    }
                }

                boolean getGPS = true;

                LocationInfo locBean;

                isGeoTagged = Global.AT_IMAGE_W_LOCATION.equals(mBean.getAnswer_type());
                isGeoTaggedGPSOnly = Global.AT_IMAGE_W_GPS_ONLY.equals(mBean.getAnswer_type());
                if (isGeoTagged) {
                    LocationTrackingManager pm = Global.LTM;
//                    if(!pm.isConnected()){
//                        pm.connect();
//                    }
                    if (pm != null) {
                        locBean = pm.getCurrentLocation(Global.FLAG_LOCATION_CAMERA);
                        LocationInfo2 infoFinal = new LocationInfo2(locBean);
                        if (infoFinal.getLatitude().equals("0.0") || infoFinal.getLongitude().equals("0.0")) {
                            if (infoFinal.getMcc().equals("0") || infoFinal.getMnc().equals("0")) {
                                if (mBean.isMandatory() || mBean.isRelevantMandatory()) {
                                    mBean.setLocationInfo(infoFinal);
                                    String geodataError = getString(R.string.geodata_error);
                                    String[] msg = {geodataError};
                                    String alert2 = Tool.implode(msg, "\n");
                                    Toast.makeText(getActivity(), alert2, Toast.LENGTH_LONG).show();
                                    getGPS = false;
                                }
                            } else {
                                mBean.setLocationInfo(infoFinal);
                                if (mBean.isMandatory() || mBean.isRelevantMandatory()) {
                                    String gpsError = getString(R.string.gps_gd_error);
                                    String[] msg = {gpsError};
                                    String alert2 = Tool.implode(msg, "\n");
                                    Toast.makeText(getActivity(), alert2, Toast.LENGTH_LONG).show();
                                }
                            }
                            indicatorGPS = getString(R.string.coordinat_not_available);
                        } else {
                            mBean.setAnswer(LocationTrackingManager.toAnswerString_short(infoFinal));
                            mBean.setLocationInfo(infoFinal);
                            indicatorGPS = mBean.getAnswer();
                        }
                    }
                }

                if (isGeoTaggedGPSOnly) {
//                    LocationTrackingManager pm = Global.LTM;
//                    if(!pm.isConnected()){
//                        pm.connect();
//                    }
                    if (Global.LTM != null) {
                        locBean = Global.LTM.getCurrentLocation(Global.FLAG_LOCATION_CAMERA);
                        LocationInfo2 infoFinal = new LocationInfo2(locBean);
                        if (infoFinal.getLatitude().equals("0.0") || infoFinal.getLongitude().equals("0.0")) {
                            if (mBean.isMandatory() || mBean.isRelevantMandatory()) {
                                mBean.setLocationInfo(infoFinal);
                                String gpsError = getString(R.string.gps_error);
                                String[] msg = {gpsError};
                                String alert2 = Tool.implode(msg, "\n");
                                Toast.makeText(getActivity(), alert2, Toast.LENGTH_LONG).show();
                                getGPS = false;
                            } else {
                                indicatorGPS = getString(R.string.coordinat_not_available);
                            }

                        } else {
                            mBean.setAnswer(LocationTrackingManager.toAnswerString_short(infoFinal));
                            mBean.setLocationInfo(infoFinal);
                            indicatorGPS = mBean.getAnswer();
                        }
                    }
                }

                // set thumbnail
                if (mBean.getImgAnswer() != null && getGPS) {
                    Bitmap bm = BitmapFactory.decodeByteArray(data, 0, data.length);
                    res = new int[2];
                    res[0] = bm.getWidth();
                    res[1] = bm.getHeight();

                    long size = mBean.getImgAnswer().length;
                    formattedSize = Formatter.formatByteSize(size);

                    if (isGeoTagged || isGeoTaggedGPSOnly) {
                        String text = res[0] + " x " + res[1] +
                                ". Size " + formattedSize + "\n" + indicatorGPS;
                        mBean.setAnswer(text);
                    } else {
                        String text = res[0] + " x " + res[1] +
                                ". Size " + formattedSize;
                        mBean.setAnswer(text);
                    }
                }

                if (!isFromGallery) {
                    String imageBase64 = Utils.byteToBase64(data);

                    if(isImageDkcp){
                        focusBean.setResponseImageDkcp(null);
                        new SubmitImageDkcp(getActivity(), getContext(), focusBean).execute(imageBase64);
                    }

                    if(Global.AT_IMAGE_LIVENESS.equalsIgnoreCase(mBean.getAnswer_type())) {
                        new CheckBiometricTask(focusBean, DynamicFormActivity.header.getUuid_task_h(), getContext(), getActivity()).execute(imageBase64);
                    }
                }
            }

        } catch (OutOfMemoryError e) {
            FireCrash.log(e);
            Toast.makeText(getActivity(), getActivity().getString(R.string.processing_image_error), Toast.LENGTH_SHORT).show();
        } catch (Exception e) {
            FireCrash.log(e);
            Toast.makeText(getActivity(), getActivity().getString(R.string.camera_error), Toast.LENGTH_SHORT).show();
        }
        int position = focusPosition + getCounterListBeforeGroup(focusGroup, false);
        changeItem(position);
    }

    private void deleteLatestPictureCreate() {
        try {
            getActivity().getContentResolver().delete(MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                    MediaStore.Images.Media.DATA
                            + "='"
                            + DynamicFormActivity.mCurrentPhotoPath
                            + "'", null);

            File f = Environment.getExternalStoragePublicDirectory(
                    Environment.DIRECTORY_PICTURES);
            File[] files = f.listFiles();
            Arrays.sort(files, new Comparator() {
                public int compare(Object o1, Object o2) {

                    if (((File) o1).lastModified() > ((File) o2).lastModified()) {
                        return -1;
                    } else if (((File) o1).lastModified() < ((File) o2).lastModified()) {
                        return +1;
                    } else {
                        return 0;
                    }
                }
            });
            files[0].delete();
        } catch (Exception e) {
            FireCrash.log(e);
            if (Global.IS_DEV)
                e.printStackTrace();
        }
    }

    private void doReject() {
        boolean isApprovalTask = true;
        getActivity().findViewById(R.id.btnReject).setEnabled(false);
        if (TaskHDataAccess.STATUS_TASK_VERIFICATION.equals(DynamicFormActivity.header.getStatus()) ||
                TaskHDataAccess.STATUS_TASK_VERIFICATION_DOWNLOAD.equals(DynamicFormActivity.header.getStatus())) {
            DynamicFormActivity.header.setIs_prepocessed(Global.FORM_TYPE_VERIFICATION);
            isApprovalTask = false;
        }
        if (TaskHDataAccess.STATUS_TASK_APPROVAL.equals(DynamicFormActivity.header.getStatus()) ||
                TaskHDataAccess.STATUS_TASK_APPROVAL_DOWNLOAD.equals(DynamicFormActivity.header.getStatus())) {
            DynamicFormActivity.header.setIs_prepocessed(Global.FORM_TYPE_APPROVAL);
            isApprovalTask = true;
        }
        if (DynamicFormActivity.header.getSubmit_date() == null)
            DynamicFormActivity.header.setSubmit_date(Tool.getSystemDateTime());
        new TaskManager().sendApprovalTask(getActivity(), DynamicFormActivity.header, Global.FLAG_FOR_REJECTEDTASK, isApprovalTask);
    }

    private void doApprove() {
        boolean isApprovalTask = true;
        getActivity().findViewById(R.id.btnApprove).setEnabled(false);
        if (DynamicFormActivity.header.getSubmit_date() == null)
            DynamicFormActivity.header.setSubmit_date(Tool.getSystemDateTime());
        new TaskManager().sendApprovalTask(getActivity(), DynamicFormActivity.header, Global.FLAG_FOR_APPROVALTASK, isApprovalTask);
    }

    private void doVerify() {
        getActivity().findViewById(R.id.btnVerified).setEnabled(false);
        if (validateAllMandatory(true)) {
            sendVerification();
        }
    }

    private void sendVerification() {
        DynamicFormActivity.header.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);
        DynamicFormActivity.header.setIs_prepocessed(Global.FORM_TYPE_VERIFICATION);
        if (DynamicFormActivity.header.getSubmit_date() == null)
            DynamicFormActivity.header.setSubmit_date(Tool.getSystemDateTime());
        new TaskManager().saveAndSendTask(getActivity(), mode, DynamicFormActivity.header, listOfQuestions);
    }

    private void setAnswerForLookupQuestion(Serializable selectedCriteria) {
        if (selectedCriteria instanceof LookupCriteriaBean) {
            LookupCriteriaBean bean = (LookupCriteriaBean) selectedCriteria;
            bean.setSelected(true);
            QuestionBean mBean = focusBean;
            mBean.setSelectedCriteriaBean(bean);
            listOfLookupOnlineStored.put(mBean.getIdentifier_name(), mBean.getLookupCriteriaList());
            String newAnswer = bean.getCode() + Global.DELIMETER_ROW + bean.getValue();
            mBean.setIsCanChange(true);
            mBean.setChange(true);
            mBean.setAnswer(newAnswer);

            int position = focusPosition + getCounterListBeforeGroup(focusGroup, false);
            changeItem(position);
            Utility.freeMemory();
        }
    }

    private void setImageFromEditViewer(byte[] value) {
        QuestionBean mBean = focusBean;
        mBean.setImgAnswer(value);
        boolean isGeoTagged = Global.AT_IMAGE_W_LOCATION.equals(mBean.getAnswer_type());
        boolean isGeoTaggedGPSOnly = Global.AT_IMAGE_W_GPS_ONLY.equals(mBean.getAnswer_type());
        long size = value.length;
        String formattedSize = Formatter.formatByteSize(size);
        Bitmap tempBmp = Utils.byteToBitmap(value);
        if (isGeoTagged || isGeoTaggedGPSOnly) {
            if (mBean.getLocationInfo() != null) {
                String indicatorGPS = LocationTrackingManager.toAnswerString_short(mBean.getLocationInfo());
                String answer = tempBmp.getWidth() + " x " + tempBmp.getHeight() + ". Size " + formattedSize + "\n" + indicatorGPS;
                mBean.setAnswer(answer);
            } else {
                String answer = tempBmp.getWidth() + " x " + tempBmp.getHeight() + ". Size " + formattedSize + "\n" + getActivity().getString(R.string.coordinat_not_available);
                mBean.setAnswer(answer);
            }
        } else {
            String answer = tempBmp.getWidth() + " x " + tempBmp.getHeight() + ". Size " + formattedSize;
            mBean.setAnswer(answer);
        }
        int position = focusPosition + getCounterListBeforeGroup(focusGroup, false);
        changeItem(position);
        Utility.freeMemory();
    }

    private void doSearch(String key, boolean needValidation) {
        if (this.validateCurrentPage(needValidation, false)) {
            int group = 0;
            int position = 0;
            /*for (; start < end; start++) {
                QuestionBean bean = listOfQuestions.get(start);

                if (bean.isVisible()) {
                    idx++;
                    String label = bean.getQuestion_label().toLowerCase();
                    if (label.indexOf(key) != -1) {
                        page = idx;
                        break;
                    }
                }
            }*/
            for (int i = 0; i <= questionAdapter.getGroupItemCount(); i++) {
                for (int j = 0; j < questionAdapter.getChildItemCount(i); j++) {
                    QuestionBean bean = questionAdapter.getChildItem(i, j);
                    if (bean.isVisible()) {
                        String[] txtSearch = Tool.split(key, Global.DELIMETER_ROW);
                        String txtQLabel = txtSearch[0].trim();
                        String label = bean.getQuestion_label().toLowerCase();
                        String groupName = bean.getQuestion_group_name();
                        if (label.indexOf(txtQLabel) != -1) {
                            if (txtSearch.length == 2) {
                                String txtgroupName = txtSearch[1].trim();
                                if (groupName.equalsIgnoreCase(txtgroupName)) {
                                    group = i;
                                    position = j;
                                }
                            } else {
                                group = i;
                                position = j;
                            }
                        }
                    }
                }
            }
            if (isFinish) {
                showReviewScreen(false);
                isFinish = false;
                ScaleAnimation anim = new ScaleAnimation(0, 1, 0, 1);
                anim.setDuration(500);
                anim.setFillAfter(true);
                getActivity().findViewById(R.id.btnNext).setClickable(true);
                ((ImageButton) getActivity().findViewById(R.id.btnNext)).setImageResource(R.drawable.ic_next);
                getActivity().findViewById(R.id.btnBack).setVisibility(View.VISIBLE);
                getActivity().findViewById(R.id.btnSend).setClickable(false);
                ((ImageButton) getActivity().findViewById(R.id.btnSend)).setImageResource(R.drawable.ic_send_off);
            }
            ((AutoCompleteTextView) getActivity().findViewById(R.id.autoCompleteSearch)).setText("");
            try {
//                int groupIdx = listOfQuestionGroup.indexOf(listOfQuestions.get(page).getQuestion_group_name());
//                questionAdapter.expand(groupIdx);
//                page = page + groupIdx + 1;
//                setFocusable(page, 1000);
                questionAdapter.expand(group);
                position = position + getCounterListBeforeGroup(group, false);
                setFocusable(position, 500);
            } catch (Exception e) {
                FireCrash.log(e);
            }
        }
    }

    private void doSend() {
        getActivity().findViewById(R.id.btnSend).setEnabled(false);
        if (validateAllMandatory(true)) {
            saveAndSendSurvey();
        } else if (messages != null || !messages.equalsIgnoreCase("")) {
            final NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(getContext());
            dialogBuilder.withTitle("Validation")
                    .withMessage(messages)
                    .withButton1Text("OK")
                    .setButton1Click(new View.OnClickListener() {
                        @Override
                        public void onClick(View arg0) {
                            // TODO Auto-generated method stub
                            dialogBuilder.dismiss();
                            onReviewClickListener(focusBean, focusGroup, focusPosition);
                        }
                    })
                    .isCancelable(false)
                    .show();
        }

    }

    private void saveAndSendSurvey() {
        if (!isSaveAndSending) {
            isSaveAndSending = true;
            if (TaskHDataAccess.STATUS_SEND_INIT.equals(DynamicFormActivity.header.getStatus())) {
                Constant.notifCount--;
                //TODO bangkit Constant.notifHandler.sendEmptyMessage(0);
            }

            DynamicFormActivity.header.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);
            DynamicFormActivity.header.setSubmit_date(Tool.getSystemDateTime());

            getActivity().findViewById(R.id.btnSend).setEnabled(true);
            getActivity().findViewById(R.id.btnSend).setClickable(true);

            //new TaskManager().saveAndSendTask(getActivity(), mode, DynamicFormActivity.header, listOfQuestions);  //TODO: Comment yg ini kalo pake submit on background

            isSaveAndSending = false;

            String tag = null;
            for (int i = 0; i < listOfQuestions.size(); i++) {
                QuestionBean mbean = listOfQuestions.get(i);
                tag = mbean.getTag();
                if (tag != null) {
                    if (tag.equalsIgnoreCase("Total Bayar"))
                        break;
                }
            }

            if (Global.APPLICATION_COLLECTION.equalsIgnoreCase(GlobalData.getSharedGlobalData().getApplication()) &&
                    tag != null && tag.equalsIgnoreCase("Total Bayar")) {
                new TaskManager().saveAndSendTask(getActivity(), mode, DynamicFormActivity.header, listOfQuestions);
            } else {
                new TaskManager().saveAndSendTaskOnBackground(getActivity(), mode, DynamicFormActivity.header, listOfQuestions, listOfQuestion);

                //TODO: Fix This on Performance Issue
//                Intent intent = new Intent(getContext(), MainMenuActivity.getMainMenuClass());
//                intent.setAction(Global.MAINMENU_NOTIFICATION_KEY);
//                startActivity(intent);
                Bundle bundle = new Bundle();
                bundle.putString("action", Global.MAINMENU_NOTIFICATION_KEY);
                Message message = new Message();
                message.setData(bundle);
                MainMenuActivity.mStackHandler.sendMessage(message);
                getActivity().finish();
//                getActivity().startActivity(intent);
            }

            //TODO: UnComment yang bawah kalo mau pake submit on background
             /*new TaskManager().saveAndSendTask(getActivity(), mode, DynamicFormActivity.header, listOfQuestions);
             try {
             Bundle extras = new Bundle();
             Intent intent = new Intent(getActivity(), SendResultActivity.class);
             extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
             if(DynamicFormActivity.header.getIs_prepocessed() !=null&&DynamicFormActivity.header.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)){
             extras.putString(Global.BUND_KEY_SURVEY_ERROR_MSG, "Data sedang dikirim");
             }else{
             extras.putString(Global.BUND_KEY_SURVEY_ERROR_MSG, "Data sedang dikirim");
             }
             extras.putString(Global.BUND_KEY_TASK_ID, "For Detail Task, open your Timeline or Status tab on TaskList Menu");
             intent.putExtras(extras);
             getActivity().startActivity(intent);
             getActivity().finish();
             } catch (Exception e) {
                    FireCrash.log(e);
             }*/
            //-------------------------------------------------------
        }
    }

    public class QuestionHandler extends Handler {

        @Override
        public void handleMessage(Message msg) {
            Bundle bundle = msg.getData();
            int action = bundle.getInt(BUND_KEY_ACTION);
            if (action == NEXT_QUESTION) {
                /*Penambahan validasi untuk cek task benar merupakan piloting CAE atau bukan*/
                QuestionBean beanPilotingCae = listOfQuestions.get(listOfQuestions.size() - 1);
                boolean isAllowToContinue = true;
                if (beanPilotingCae != null && "SVY_IS_PILOTING_CAE".equalsIgnoreCase(beanPilotingCae.getIdentifier_name()) && QuestionBean.isHaveAnswer(beanPilotingCae)) {
                    String pilotingCAE = "TIDAK";
                    if ("1".equalsIgnoreCase(DynamicFormActivity.header.getIs_piloting_cae())) {
                        pilotingCAE = "YA";
                    }

                    if (!pilotingCAE.equalsIgnoreCase(beanPilotingCae.getAnswer())) {
                        isAllowToContinue = false;
                        StringBuilder message = new StringBuilder("Input for Question " + beanPilotingCae.getQuestion_label() + " is Not Valid");
                        message.append("\nPlease Try to Save Draft and Delete Task to Re-download the Detail");
                        DialogManager.showAlertNotif(getActivity(), message.toString(), "INFO");
                    }
                }
                if (isAllowToContinue && isFinish && DynamicFormActivity.isVerified) {
                    if (Global.NEW_FEATURE) {
                        if (Tool.isInternetconnected(getActivity())) {
                            if (validateAllMandatory(true)) {
                                //TODO : alihkan ke halaman verification Action
                                Intent intent = new Intent(getActivity(), Global.VerificationActivityClass);
                                intent.putExtra(Global.BUND_KEY_UUID_TASKH, DynamicFormActivity.header.getUuid_task_h());
                                intent.putExtra(Global.BUND_KEY_MODE_SURVEY, mode);
                                if (DynamicFormActivity.isApproval)
                                    intent.putExtra(Global.BUND_KEY_FORM_NAME, Global.APPROVAL_FLAG);
                                else
                                    intent.putExtra(Global.BUND_KEY_FORM_NAME, Global.VERIFICATION_FLAG);
                                startActivity(intent);
                            }
                        } else {
                            Toast.makeText(getActivity(), getActivity().getString(R.string.connection_failed), Toast.LENGTH_SHORT).show();
                        }
                    }
                } else if (isAllowToContinue && doNext(true)) {
                    getActivity().findViewById(R.id.btnNext).setClickable(false);
                    if (Global.NEW_FEATURE) {
                        if (Global.FEATURE_REJECT_WITH_RESURVEY) {
                            if (!DynamicFormActivity.isVerified) {
                                getActivity().findViewById(R.id.btnNext).setClickable(false);
                                ((ImageButton) getActivity().findViewById(R.id.btnNext)).setImageResource(R.drawable.ic_next_off);
                            } else {
                                getActivity().findViewById(R.id.btnNext).setClickable(true);
                                ((ImageButton) getActivity().findViewById(R.id.btnNext)).setImageResource(R.drawable.ic_next);
                            }
                        }
                    } else {
                        getActivity().findViewById(R.id.btnNext).setClickable(false);
                        ((ImageButton) getActivity().findViewById(R.id.btnNext)).setImageResource(R.drawable.ic_next_off);
                    }
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            qRecyclerView.smoothScrollToPosition(qRecyclerView.getAdapter().getItemCount());
                            int lastPosition = qRecyclerView.getAdapter().getItemCount() - 1;
                            if (!isFinish)
                                setFocusable(lastPosition, 500);
                        }
                    }, 100);
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (!isFinish) {
                                getActivity().findViewById(R.id.btnNext).setClickable(true);
                                ((ImageButton) getActivity().findViewById(R.id.btnNext)).setImageResource(R.drawable.ic_next);
                            }
                        }
                    }, 500);
                }
            } else if (action == SAVE_QUESTION) {
                int mode = bundle.getInt(Global.BUND_KEY_MODE_SURVEY);
                doSave(mode);
                GlobalData.getSharedGlobalData().setDoingTask(false);
            } else if (action == SEND_QUESTION) {
                doSend();
                GlobalData.getSharedGlobalData().setDoingTask(false);
            } else if (action == SEARCH_QUESTION) {
                String searchKey = bundle.getString(BUND_KEY_SEARCH_ACTION);
                doSearch(searchKey, false);
            } else if (action == VERIFY_QUESTION) {
                doVerify();
                GlobalData.getSharedGlobalData().setDoingTask(false);
            } else if (action == APPROVE_QUESTION) {
                doApprove();
                GlobalData.getSharedGlobalData().setDoingTask(false);
            } else if (action == REJECT_QUESTION) {
                doReject();
                GlobalData.getSharedGlobalData().setDoingTask(false);
            } else if (action == RESULT_FROM_DRAWING_QUESTION) {
                setImageForDrawingQuestion(bundle.getByteArray(DrawingCanvasActivity.BUND_KEY_IMAGE_RESULT));
            } else if (action == RESULT_FROM_BUILT_IN_CAMERA) {
                setImageForImageQuestion(bundle.getString(CameraActivity.PICTURE_URI), false);
            } else if (action == RESULT_FROM_ANDROID_CAMERA) {
                setImageForImageQuestion(DynamicFormActivity.mCurrentPhotoPath, false);
            } else if (action == RESULT_FROM_ANDROID_GALLERY) {
                setImageForImageQuestion(DynamicFormActivity.mCurrentPhotoPath, true);
//                if (realPath != null) setImageForImageQuestion(realPath);
            } else if (action == RESULT_FROM_PICK_IMAGE) {
                setImageForImageQuestion(bundle.getString(FragmentQuestion.BUND_KEY_PICK_IMAGE), true);
                //TODO: CEK RESULT INI DARI MANA
                QuestionBean mBean = focusBean;
                if (null != mBean.getTag() && mBean.getTag().contains(Global.TAG_OCR_W_GALLERY)) {
                    Bundle extras = new Bundle();
                    extras.putByteArray(ImageViewerActivity.BUND_KEY_IMAGE, mBean.getImgAnswer());
                    extras.putInt(ImageViewerActivity.BUND_KEY_IMAGE_QUALITY, Utils.picQuality);
                    extras.putBoolean(ImageViewerActivity.BUND_KEY_IMAGE_ISVIEWER, Global.isViewer);
                    extras.putBoolean(BUND_KEY_PICK_IMAGE, true);
                    Intent intent = new Intent(getActivity(), ImageViewerActivity.class);
                    intent.putExtras(extras);
                    getActivity().startActivityForResult(intent, Global.REQUEST_EDIT_IMAGE);
                }
            } else if (action == RESULT_FROM_LOCATION_QUESTION) {
                setLocationForLocationQuestion();
            } else if (action == RESULT_FROM_EDIT_IMAGE) {
                setImageFromEditViewer(bundle.getByteArray(ImageViewerActivity.BUND_KEY_IMAGE));
                boolean isFromPickImage = bundle.getBoolean(BUND_KEY_PICK_IMAGE);

                //michael.wijaya 29/04/22: penjagaan NullPointerException kalau tag nya kosong
                if (focusBean.getTag() != null && focusBean.getTag().contains(Global.TAG_OCR_W_GALLERY) && isFromPickImage) {
                    String imageBase64 = Utils.byteToBase64(focusBean.getImgAnswer());
                    focusBean.setResponseImageDkcp(null);
                    new SubmitImageDkcp(getActivity(), getContext(), focusBean).execute(imageBase64);
                }
            } else if (action == RESULT_FROM_LOOKUP_CRITERIA) {
                setAnswerForLookupQuestion(bundle.getSerializable(KEY_SELECTED_CRITERIA));
            } else if (action == RESULT_FROM_CAMERA_ERROR) {
                //EMPTY
            } else if (action == RESULT_FROM_IMAGE_LOC_UPDATE) {
                String lat = bundle.getString("latitude", "0");
                String lng = bundle.getString("longitude", "0");
                updateLocationForImageQuestion(lat, lng);
            } else if (action == RESULT_FROM_LOCATION_UPDATE) {
                String lat = bundle.getString("latitude", "0");
                String lng = bundle.getString("longitude", "0");
                updateLocationForLocationQuestion(lat, lng);
            } else if (action == RESULT_FROM_SUBMITDKCP) {
                String responseResultDkcp = bundle.getString("identifier", "");
                if (responseResultDkcp != null && !"".equalsIgnoreCase(responseResultDkcp)) {
                    String identifier = focusBean.getIdentifier_name();
                    String questionGroupName = focusBean.getQuestion_group_name();
                    List<QuestionBean> listQuestions = listOfQuestionBean.get(questionGroupName);
                    boolean isChange = false;
                    if (listQuestions != null && !listQuestions.isEmpty()) {
                        String scriptDkcp = "copyFromDkcp({" + identifier + ",";
                        for (QuestionBean bean : listQuestions) {
                            String questionValue = bean.getQuestion_value();
                            if (questionValue != null && questionValue.toUpperCase().contains(scriptDkcp.toUpperCase()) && !"1".equalsIgnoreCase(bean.getIs_readonly())) {
                                copyQuestionValue(bean, bean.getQuestion_value(), true);
                                isChange = true;
                            }
                        }
                    }

                    if (isChange) {
                        questionAdapter.notifyDataSetChanged();
                    }
                }
            } else if (action == RESULT_FROM_SUBMITLAYER || action == RESULT_FROM_CHECK_BIOMETRIC ||
                    action == RESULT_FROM_GET_DSR || action == RESULT_FROM_CEK_REFERANTOR ||
                    action == RESULT_FROM_CHECK_VALIDATION) {
                boolean isChange = bundle.getBoolean("isChange", false);
                if (isChange) {
                    questionAdapter.notifyDataSetChanged();
                }
            } else if (action == RESULT_FROM_ACCEPTED_AGREEMENT) {
                String result = bundle.getString(BUND_KEY_RESULT);
                if (StringUtils.isNotBlank(result)) {
                    focusBean.setAnswer(result);
                    focusBean.setIsCanChange(true);
                    focusBean.setChange(true);
                    int position = focusPosition + getCounterListBeforeGroup(focusGroup, false) - 1;
                    changeItem(position);
                    Utility.freeMemory();
                }
            }
        }
    }

    @SuppressLint("StaticFieldLeak")
    private class GetOneLuMaskapai extends AsyncTask<Void, Void, String> {
        private final String defaultResultNoConnection = "{\"listField\":[{\"key\":\"-\",\"value\":\"-\"}],\"status\":{\"code\":0,\"message\":\"Success\"}}";
        private final Activity activity;
        private ProgressDialog progressDialog;
        private String errMessage = "";
        private final QuestionBean bean;
        private final CriteriaParameter criteriaParameter;
        private final boolean isThreadActive;
        private final int position;

        public GetOneLuMaskapai(Activity activity, QuestionBean bean, CriteriaParameter criteriaParameter, int position, boolean isThreadActive) {
            this.activity = activity;
            this.bean = bean;
            this.criteriaParameter = criteriaParameter;
            this.position = position;
            this.isThreadActive = isThreadActive;
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            this.progressDialog = ProgressDialog.show(activity, "", "Loading...", true);
        }

        @Override
        protected String doInBackground(Void... voids) {
            if (Tool.isInternetconnected(getActivity())) {
                String jsonRequest = getJsonRequestLuOnline(bean, criteriaParameter);
                String url = GlobalData.getSharedGlobalData().getURL_LOOKUP_ANSWER();
                boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                HttpCryptedConnection httpConn = new HttpCryptedConnection(activity, encrypt, decrypt);
                HttpConnectionResult serverResult = null;
                try {
                    serverResult = httpConn.requestToServer(url, jsonRequest, Global.DEFAULTCONNECTIONTIMEOUT);
                } catch (Exception e) {
                    FireCrash.log(e);
                    errMessage = activity.getString(R.string.msgConnectionFailed);
                    return null;
                }
                if (serverResult != null && serverResult.isOK()) {
                    String body = serverResult.getResult();
                    return body;
                } else {
                    errMessage = activity.getString(R.string.connection_failed);
                }
            } else {
                return defaultResultNoConnection;
            }
            return null;
        }

        @Override
        protected void onPostExecute(String result) {
            super.onPostExecute(result);
            progressDialog.dismiss();
            if (errMessage != null && !errMessage.isEmpty()) {
                Toast.makeText(activity, errMessage, Toast.LENGTH_SHORT).show();
            } else if (result != null) {
                processLuOnline(activity, result, bean, criteriaParameter);
            }
            checkHasDefaultAnswer(bean, isThreadActive); //Check has Default Answer
            calculateQuestionBean(bean);

            // TODO Add new OptionAnswerFromTable
            addQuestionLabel(bean.getQuestion_label() + " " + Global.DELIMETER_ROW + " " + bean.getQuestion_group_name());
            addItem(bean, position, isThreadActive);
            isNotThreadActive(bean, isThreadActive);
            statusSyncParamConstraints = false;
        }
    }

    private void processLuOnline(Activity activity, String result, QuestionBean bean, CriteriaParameter criteriaParameter) {
        ReviewResponse response = GsonHelper.fromJson(result, ReviewResponse.class);
        if (response.getStatus().getCode() == 0) {
            List<KeyValue> valueList = response.getListField();
            if (valueList != null) {
                String keyStr = valueList.get(0).getKey();
                String valueStr = valueList.get(0).getValue();
                bean.setAnswer(keyStr+" | "+valueStr);
            }
        } else {
            Toast.makeText(activity, response.getStatus().getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    public String getJsonRequestLuOnline(QuestionBean bean, CriteriaParameter criteriaParameter) {
        LookupOnlineRequest request = new LookupOnlineRequest();
        request.setAudit(GlobalData.getSharedGlobalData().getAuditData());
        request.setIdentifier(bean.getIdentifier_name());
        if (bean.getChoice_filter() != null) {
            List<String> tempFilters = getFilterAnswer(bean);
            StringBuilder stringBuilder = new StringBuilder();
            for (String filter : tempFilters) {
                if (stringBuilder.length() > 0)
                    stringBuilder.append(Global.DELIMETER_DATA_LOOKUP);
                stringBuilder.append(filter);
            }
            request.setChoiceFilter(stringBuilder.toString());
        }
        request.setUserFilter(criteriaParameter.getParameters().get(0).getAnswer());
        request.setLovGroup(bean.getLov_group());
        request.setIdentifier(bean.getIdentifier_name());
        return GsonHelper.toJson(request);
    }

    public List<String> getFilterAnswer(QuestionBean bean) {
        List<String> filters = new ArrayList<String>();
        if (bean.getChoice_filter() != null) {
            String[] tempFilters = Tool.split(bean.getChoice_filter(), Global.DELIMETER_DATA3);

            for (String newFilter : tempFilters) {
                int idxOfOpenBrace = newFilter.indexOf('{');
                if (idxOfOpenBrace != -1) {
                    int idxOfCloseBrace = newFilter.indexOf('}');
                    String tempIdentifier = newFilter.substring(idxOfOpenBrace + 1, idxOfCloseBrace).toUpperCase();
                    if (tempIdentifier.contains("%")) {
                        filters.add(tempIdentifier);
                    } else {
                        int idxOfOpenAbs = tempIdentifier.indexOf("$");
                        if (idxOfOpenAbs != -1) {
                            String finalIdentifier = tempIdentifier.substring(idxOfOpenAbs + 1);
                            if (finalIdentifier.equals(Global.IDF_LOGIN_ID)) {
                                String loginId = GlobalData.getSharedGlobalData().getUser().getLogin_id();
                                int idxOfOpenAt = loginId.indexOf('@');
                                if (idxOfOpenAt != -1) {
                                    loginId = loginId.substring(0, idxOfOpenAt);
                                }
                                filters.add(loginId);
                            } else if (finalIdentifier.equals(Global.IDF_BRANCH_ID)) {
                                String branchId = GlobalData.getSharedGlobalData().getUser().getBranch_id();
                                filters.add(branchId);
                            } else if (finalIdentifier.equals(Global.IDF_BRANCH_NAME)) {
                                String branchName = GlobalData.getSharedGlobalData().getUser().getBranch_name();
                                filters.add(branchName);
                            } else if (finalIdentifier.equals(Global.IDF_UUID_USER)) {
                                String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                                filters.add(uuidUser);
                            } else if (finalIdentifier.equals(Global.IDF_JOB)) {
                                String job = GlobalData.getSharedGlobalData().getUser().getFlag_job();
                                filters.add(job);
                            } else if (finalIdentifier.equals(Global.IDF_DEALER_NAME)) {
                                String dealerName = GlobalData.getSharedGlobalData().getUser().getDealer_name();
                                filters.add(dealerName);
                            }
                        } else {
                            QuestionBean bean2 = Constant.listOfQuestion.get(tempIdentifier);
                            if (bean2 != null) {
                                String answer = QuestionBean.getAnswer(bean2);
                                filters.add(answer);
                                bean2.setRelevanted(true);
                            }
                        }
                    }
                }
            }
        }
        return filters;
    }

    private class GetLookupOnDemand extends AsyncTask<Void, Void, Boolean> {

        String errMessage = null;
        String lov_group = null;
        int constraintAmount = 0;
        QuestionBean bean;
        List<String> filters;
        private ProgressDialog progressDialog;
        private boolean isThreadActive;
        private int position;

        public GetLookupOnDemand(
                QuestionBean bean,
                String lovGroup,
                List<String> filters,
                int constraintAmount,
                boolean isThreadActive,
                int position
        ) {
            this.bean = bean;
            this.lov_group = lovGroup;
            if (filters != null)
                this.filters = filters;
            else
                this.filters = new ArrayList<>();
            this.constraintAmount = constraintAmount;
            this.isThreadActive = isThreadActive;
            this.position = position;
        }

        @Override
        protected void onPreExecute() {
            String message = getString(R.string.lookup_progress, lov_group);
            this.progressDialog = ProgressDialog.show(getActivity(), "", message, true);
        }

        @Override
        protected Boolean doInBackground(Void... params) {
            if (Tool.isInternetconnected(getActivity())) {
                //----------------------Get Lookup Parameter----------------------
                Calendar cal = Calendar.getInstance();
                cal.setTimeInMillis(0);
                cal.set(1971, 1, 1, 1, 1, 1);
                Date date = cal.getTime();

                String url = GlobalData.getSharedGlobalData().getURL_SYNCPARAM_CONSTRAINT();
                List<HashMap<String, Object>> lookupArgs = new ArrayList<HashMap<String, Object>>();
                HashMap<String, Object> forms = new HashMap<String, Object>();
                forms.put(LookupDao.Properties.Lov_group.name, lov_group);
                forms.put(LookupDao.Properties.Dtm_upd.name, date);

                if (!Global.REF_PRE_SOA.equals(bean.getIdentifier_name())) {
                    if (!filters.isEmpty()) {
                        if (filters.size() == 1) {
                            forms.put(LookupDao.Properties.Filter1.name, filters.get(0));
                        } else if (filters.size() == 2) {
                            forms.put(LookupDao.Properties.Filter1.name, filters.get(0));
                            forms.put(LookupDao.Properties.Filter2.name, filters.get(1));
                        } else if (filters.size() == 3) {
                            forms.put(LookupDao.Properties.Filter1.name, filters.get(0));
                            forms.put(LookupDao.Properties.Filter2.name, filters.get(1));
                            forms.put(LookupDao.Properties.Filter3.name, filters.get(2));
                        } else if (filters.size() == 4) {
                            forms.put(LookupDao.Properties.Filter1.name, filters.get(0));
                            forms.put(LookupDao.Properties.Filter2.name, filters.get(1));
                            forms.put(LookupDao.Properties.Filter3.name, filters.get(2));
                            forms.put(LookupDao.Properties.Filter4.name, filters.get(3));
                        } else if (filters.size() == 5) {
                            forms.put(LookupDao.Properties.Filter1.name, filters.get(0));
                            forms.put(LookupDao.Properties.Filter2.name, filters.get(1));
                            forms.put(LookupDao.Properties.Filter3.name, filters.get(2));
                            forms.put(LookupDao.Properties.Filter4.name, filters.get(3));
                            forms.put(LookupDao.Properties.Filter5.name, filters.get(4));
                        }
                    }
                }

                forms.put("constraintAmount", constraintAmount);

                lookupArgs.add(forms);

                SynchronizeRequestModel request = new SynchronizeRequestModel();
                request.setInit(0);

                if (!lookupArgs.isEmpty())
                    request.setList(lookupArgs);
                request.setAudit(GlobalData.getSharedGlobalData().getAuditData());

                request.setTableName("MS_LOV");
                request.setDtm_upd(date);


                String jsonRequest = GsonHelper.toJson(request);
                boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                HttpCryptedConnection httpConn = new HttpCryptedConnection(getActivity(), encrypt, decrypt);
                HttpConnectionResult serverResult = null;
                try {
                    serverResult = httpConn.requestToServer(url, jsonRequest, Global.DEFAULTCONNECTIONTIMEOUT);
                } catch (Exception e) {
                    FireCrash.log(e);
                }
                if (serverResult != null && serverResult.isOK()) {
                    String body = serverResult.getResult();
                    try {
                        SynchronizeResponseLookup entityLookup = GsonHelper.fromJson(body, SynchronizeResponseLookup.class);
                        List<Lookup> entitiesLookup = entityLookup.getListSync();
                        if (entitiesLookup != null && !entitiesLookup.isEmpty())
                            LookupDataAccess.addOrUpdateAll(getActivity(), entitiesLookup);
                        else
                            errMessage = getString(R.string.lookup_not_available, lov_group);
                    } catch (JsonSyntaxException e) {
                        FireCrash.log(e);
                        ACRA.getErrorReporter().putCustomData("errorGetLookupOnDemand", e.getMessage());
                        ACRA.getErrorReporter().putCustomData("errorGetLookupOnDemandDate", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                        ACRA.getErrorReporter().handleSilentException(new Exception("Just for the stacktrace, kena exception saat Get Lookup on Demand"));
                        return false;
                    } catch (IllegalStateException e) {
                        FireCrash.log(e);
                        ACRA.getErrorReporter().putCustomData("errorGetLookupOnDemand", e.getMessage());
                        ACRA.getErrorReporter().putCustomData("errorGetLookupOnDemandDate", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                        ACRA.getErrorReporter().handleSilentException(new Exception("Just for the stacktrace, kena exception saat Get Lookup on Demand"));
                        return false;
                    }
                    return true;
                } else {
                    if (Global.IS_DEV)
                        errMessage = String.valueOf(serverResult.getStatusCode());
                    return false;
                }
            } else {
                errMessage = getString(R.string.connection_failed);
                return true;
            }
        }

        @Override
        protected void onPostExecute(Boolean result) {
            List<OptionAnswerBean> options = new ArrayList<>();
            OptionAnswerBean defaultOption = new OptionAnswerBean("$$$", "Pilih Salah Satu");
            options.add(defaultOption);

            if (progressDialog.isShowing()) {
                try {
                    progressDialog.dismiss();
                } catch (Exception e) {
                    FireCrash.log(e);
                    if (Global.IS_DEV)
                        e.printStackTrace();
                }
            }
            if (errMessage != null) {
                Toast.makeText(getActivity(), errMessage, Toast.LENGTH_LONG).show();
                needStop = true;
            } else {
                if (!result) {
                    String message = getString(R.string.get_lookup_failed, lov_group);
                    Toast.makeText(getActivity(), message, Toast.LENGTH_LONG).show();
                    needStop = true;
                } else { //Nendi: NEED CONFIRM LOAD LOOKUP
                    options.addAll(getOptionsForQuestion(bean, false));
                    for (int i = 0; i < listOfQuestions.size(); i++) {
                        QuestionBean mBean = listOfQuestions.get(i);
                        if (mBean.getUuid_question_set().equals(bean.getUuid_question_set())) {
                            mBean.setOptionAnswers(options);
                            int groupIdx = listOfQuestionGroup.indexOf(mBean.getQuestion_group_name()) + 1;
                            int position = i + groupIdx;
                            changeItem(position);
                        }
                    }
                }
            }
            bean.setOptionAnswers(options);
            checkHasDefaultAnswer(bean, isThreadActive); //Check has Default Answer
            calculateQuestionBean(bean);

            // TODO Add new OptionAnswerFromTable
            addQuestionLabel(bean.getQuestion_label() + " " + Global.DELIMETER_ROW + " " + bean.getQuestion_group_name());
            addItem(bean, position, isThreadActive);
            isNotThreadActive(bean, isThreadActive);
            statusSyncParamConstraints = false;
        }
    }

    private class GetTableOnDemand extends AsyncTask<Void, Void, Boolean> {

        QuestionBean bean;
        private String errMessage = null;
        private String productOfferingCode;
        private String branchCode;
        private ProgressDialog progressDialog;
        private List<OptionAnswerBean> optionPo = new ArrayList<>();
        private boolean isThreadActive;;
        private int position;

        public GetTableOnDemand(QuestionBean bean, boolean isThreadActive, int position, String prodCode) {
            this.bean = bean;
            this.isThreadActive = isThreadActive;
            this.position = position;
            this.productOfferingCode = prodCode;
        }

        @Override
        protected void onPreExecute() {
            String message = getString(R.string.sync_table_progress);
            this.progressDialog = ProgressDialog.show(getActivity(), "", message, true);
        }

        @Override
        protected Boolean doInBackground(Void... voids) {
            if (Tool.isInternetconnected(getActivity())) {
                String url = GlobalData.getSharedGlobalData().getURL_SYNCTABLE_CONSTRAINT();
                branchCode = GlobalData.getSharedGlobalData().getUser().getBranch_id();

                JsonRequestSyncTableConstraint request = new JsonRequestSyncTableConstraint();
                request.setProductOfferingCode(productOfferingCode);
                request.setBranchCode(branchCode);
                request.setAudit(GlobalData.getSharedGlobalData().getAuditData());

                String jsonRequest = GsonHelper.toJson(request);
                boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                HttpCryptedConnection httpConn = new HttpCryptedConnection(getActivity(), encrypt, decrypt);
                HttpConnectionResult serverResult = null;

                try {
                    serverResult = httpConn.requestToServer(url, jsonRequest, Global.DEFAULTCONNECTIONTIMEOUT);
                } catch (Exception e) {
                    FireCrash.log(e);
                }
                if (null != serverResult) {
                    try {
                        String body = serverResult.getResult();

                        SynchronizeResponseTable entityTable = GsonHelper.fromJson(body, SynchronizeResponseTable.class);
                        ProductOffering poEntity = entityTable.getProductOffering();
                        if (0 == entityTable.getStatus().getCode() && null != poEntity) {
                            ProductOffering product = new ProductOffering(
                                    poEntity.getId(),
                                    poEntity.getAsset_scheme_id(),
                                    poEntity.getDealer_scheme_id(),
                                    poEntity.getBranch_id(),
                                    poEntity.getProd_off_id(),
                                    poEntity.getProd_off_name(),
                                    poEntity.getProd_cat_code(),
                                    poEntity.getProd_cat_name(),
                                    poEntity.getJns_pembiayaan(),
                                    poEntity.getMin_tenor(),
                                    poEntity.getMax_tenor(),
                                    poEntity.getSc_id(),
                                    poEntity.getComponent(),
                                    poEntity.getRule_data_min_tdp(),
                                    poEntity.getRule_data_manf_year(),
                                    poEntity.getRule_asset_price(),
                                    poEntity.getRule_min_dp(),
                                    poEntity.getRule_app_tc(),
                                    poEntity.getRule_scoring(),
                                    poEntity.getRule_matrix(),
                                    poEntity.getIs_deleted(),
                                    poEntity.getDtm_upd(),
                                    poEntity.getShow_pot()
                            );
                            PODataAccess.addOrReplace(getActivity(), product);
                            OptionAnswerBean option = new OptionAnswerBean(product.getProd_off_id(), product.getProd_off_name());
                            optionPo.add(option);
                            return true;
                        } else {
                            return false;
                        }
                    } catch (Exception ex) {
                        FireCrash.log(ex);

                        errMessage = getActivity().getString(R.string.productOfferingNotAvailable, productOfferingCode);
                        return false;
                    }
                } else {
                    errMessage = getActivity().getString(R.string.msgErrorParsingJson);
                    return false;
                }
            } else {
                errMessage = getActivity().getString(R.string.no_internet_connection_please);
                return false;
            }
        }

        @Override
        protected void onPostExecute(Boolean result) {
            List<OptionAnswerBean> options = new ArrayList<>();
            OptionAnswerBean defaultOption = new OptionAnswerBean("$$$", "Pilih Salah Satu");
            options.add(defaultOption);

            if (progressDialog.isShowing()) {
                try {
                    progressDialog.dismiss();
                } catch (Exception e) {
                    FireCrash.log(e);
                    if (Global.IS_DEV)
                        e.printStackTrace();
                }
            }

            if (!result) {
                final NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(getActivity());
                Query query = finalQuery(bean.getChoice_filter());
                options.addAll(PODataAccess.lookup(getContext(), query));
                for (int i = 0; i < listOfQuestions.size(); i++) {
                    QuestionBean mBean = listOfQuestions.get(i);
                    if (mBean.getUuid_question_set().equals(bean.getUuid_question_set())) {
                        mBean.setOptionAnswers(options);
                        int groupIdx = listOfQuestionGroup.indexOf(mBean.getQuestion_group_name()) + 1;
                        int position = i + groupIdx;
                        changeItem(position);
                    }
                }
                isNotThreadActive(bean, isThreadActive);
                questionAdapter.notifyDataSetChanged();

                dialogBuilder.withTitle(getActivity().getString(R.string.error))
                        .withMessage(errMessage)
                        .withButton1Text(getActivity().getString(R.string.sync_table_reload))
                        .setButton1Click(new View.OnClickListener() {
                            @Override
                            public void onClick(View view) {
                                dialogBuilder.dismiss();
                                try {
                                    progressDialog.dismiss();
                                } catch (Exception ex) {
                                    FireCrash.log(ex);
                                }
                                GetTableOnDemand getTableOnDemand = new GetTableOnDemand(bean, isThreadActive, position, productOfferingCode);
                                getTableOnDemand.execute();
                            }
                        })
                        .withButton2Text("Ok")
                        .setButton2Click(new View.OnClickListener() {
                            @Override
                            public void onClick(View view) {
                                dialogBuilder.dismiss();
                            }
                        })
                        .isCancelable(false)
                        .isCancelableOnTouchOutside(false)
                        .show();
            } else if (result) {
//                options.addAll(getOptionsForQuestion(bean, false));
                Query query = finalQuery(bean.getChoice_filter());
                options.addAll(PODataAccess.lookup(getContext(), query));
                for (int i = 0; i < listOfQuestions.size(); i++) {
                    QuestionBean mBean = listOfQuestions.get(i);
                    if (mBean.getUuid_question_set().equals(bean.getUuid_question_set())) {
                        mBean.setOptionAnswers(options);
                        mBean.setSelectedOptionAnswers(optionPo);
                        int groupIdx = listOfQuestionGroup.indexOf(mBean.getQuestion_group_name()) + 1;
                        int position = i + groupIdx;
                        changeItem(position);
                    }
                }
            }
            isNotThreadActive(bean, isThreadActive);
            questionAdapter.notifyDataSetChanged();
        }
    }
}