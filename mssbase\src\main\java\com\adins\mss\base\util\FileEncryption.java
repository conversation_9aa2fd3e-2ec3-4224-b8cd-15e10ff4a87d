package com.adins.mss.base.util;

import com.adins.mss.base.commons.Callback;
import com.adins.mss.base.commons.Helper;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.security.GeneralSecurityException;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

import javax.crypto.Cipher;
import javax.crypto.CipherInputStream;
import javax.crypto.CipherOutputStream;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

/**
 * File Encryption Class
 * <AUTHOR>
 */
public class FileEncryption {

    /**
     * Path to public key file
     */
    public String publicKey;

    /**
     * Path to private key file
     */
    public String privateKey;
    public static final int AES_Key_Size = 256;

    private Cipher pkCipher, aesCipher;
    private byte[] aesKey;
    private SecretKeySpec aeskeySpec;
    private String iv = "FA4AD06616A0F2BB2F94A71F4FAC9469";

    public static FileEncryption fileEncryption = null;

    public static FileEncryption getInstance() throws GeneralSecurityException {
        if (fileEncryption == null) {
            fileEncryption = new FileEncryption();
        }

        return fileEncryption;
    }

    /**
     * Constructor: creates ciphers
     */
    public FileEncryption() throws GeneralSecurityException {
        // create RSA public key cipher
        pkCipher  = Cipher.getInstance("RSA");

        // create AES shared key cipher
        aesCipher = Cipher.getInstance("AES");
        // generate key
//        makeKey();
    }

    /**
     * Creates a new AES key
     * Generate random AES Key
     */
    public CipherFactory loadKey(String encodedKey) throws NoSuchAlgorithmException {
        aesKey     = Helper.getInstance().fromBase64(encodedKey);
        aeskeySpec = new SecretKeySpec(aesKey, "AES");
        return new CipherFactory(aesCipher, aeskeySpec, encodedKey);
    }

    public CipherFactory cipher() throws NoSuchAlgorithmException {
        String encodedKey = makeKey();
        return new CipherFactory(aesCipher, aeskeySpec, encodedKey);
    }

    /**
     * Generate IV Key for file encryption/decryption
     * @return
     * @throws NoSuchAlgorithmException
     */
    private String makeKey() throws NoSuchAlgorithmException {
        KeyGenerator kgen = KeyGenerator.getInstance("AES");
        kgen.init(AES_Key_Size);
        SecretKey key = kgen.generateKey();
        aesKey = key.getEncoded();
//        aeskeySpec = new SecretKeySpec(hexStringToByteArray(iv), "AES");
        aeskeySpec = new SecretKeySpec(aesKey, "AES");
        return Helper.getInstance().toBase64(aesKey);
    }

    /**
     * Decrypts an AES key from a file using an RSA private key
     * @param in 				Input file encrypted AES key
     * @param publicKeyFile 	Private RSA Key
     */
//	public void loadKey(File in, File privateKeyFile) throws GeneralSecurityException, IOException {
//		// read private key to be used to decrypt the AES key
//		byte[] encodedKey = new byte[(int)privateKeyFile.length()];
//		new FileInputStream(privateKeyFile).read(encodedKey);
//
//		// create private key
//		PKCS8EncodedKeySpec privateKeySpec = new PKCS8EncodedKeySpec(encodedKey);
//		KeyFactory kf = KeyFactory.getInstance("RSA");
//		PrivateKey pk = kf.generatePrivate(privateKeySpec);
//
//		// read AES key
//		pkCipher.init(Cipher.DECRYPT_MODE, pk);
//		aesKey = new byte[AES_Key_Size/8];
//		CipherInputStream is = new CipherInputStream(new FileInputStream(in), pkCipher);
//		is.read(aesKey);
//		aeskeySpec = new SecretKeySpec(aesKey, "AES");
//	}
    private void loadKey(File in, File publicKeyFile) throws GeneralSecurityException, IOException {
        // read private key to be used to decrypt the AES key
        byte[] encodedKey = new byte[(int)publicKeyFile.length()];
        new FileInputStream(publicKeyFile).read(encodedKey);

        // create private key
//        PKCS8EncodedKeySpec privateKeySpec = new PKCS8EncodedKeySpec(encodedKey);
//        KeyFactory kf = KeyFactory.getInstance("RSA");
//        PrivateKey pk = kf.generatePrivate(privateKeySpec);

        // create public key
        X509EncodedKeySpec publicKeySpec = new X509EncodedKeySpec(encodedKey);
        KeyFactory kf = KeyFactory.getInstance("RSA");
        PublicKey pk = kf.generatePublic(publicKeySpec);

        // read AES key
        pkCipher.init(Cipher.DECRYPT_MODE, pk);
        aesKey = new byte[AES_Key_Size/8];
        CipherInputStream is = new CipherInputStream(new FileInputStream(in), pkCipher);
        is.read(aesKey);
        aeskeySpec = new SecretKeySpec(aesKey, "AES");
    }

    /**
     * Encrypts the AES key to a file using an RSA public key
     * @param out 				Output encrypted AES Key file
     * @param privateKeyFile     RSA public key
     */
//	public void saveKey(File out, File publicKeyFile) throws IOException, GeneralSecurityException {
//		// read public key to be used to encrypt the AES key
//		byte[] encodedKey = new byte[(int)publicKeyFile.length()];
//		new FileInputStream(publicKeyFile).read(encodedKey);
//
//		// create public key
//		X509EncodedKeySpec publicKeySpec = new X509EncodedKeySpec(encodedKey);
//		KeyFactory kf = KeyFactory.getInstance("RSA");
//		PublicKey pk = kf.generatePublic(publicKeySpec);
//
//		// write AES key
//		pkCipher.init(Cipher.ENCRYPT_MODE, pk);
//		CipherOutputStream os = new CipherOutputStream(new FileOutputStream(out), pkCipher);
//		os.write(aesKey);
//		os.close();
//	}
    private void saveKey(File out, File privateKeyFile) throws IOException, GeneralSecurityException {
        // read public key to be used to encrypt the AES key
        byte[] encodedKey = new byte[(int)privateKeyFile.length()];
        new FileInputStream(privateKeyFile).read(encodedKey);

        // create public key
//        X509EncodedKeySpec publicKeySpec = new X509EncodedKeySpec(encodedKey);
//        KeyFactory kf = KeyFactory.getInstance("RSA");
//        PublicKey pk = kf.generatePublic(publicKeySpec);

        // create private key
        PKCS8EncodedKeySpec privateKeySpec = new PKCS8EncodedKeySpec(encodedKey);
        KeyFactory kf = KeyFactory.getInstance("RSA");
        PrivateKey pk = kf.generatePrivate(privateKeySpec);

        // write AES key
        pkCipher.init(Cipher.ENCRYPT_MODE, pk);
        CipherOutputStream os = new CipherOutputStream(new FileOutputStream(out), pkCipher);
        os.write(aesKey);
        os.close();
    }

//    /**
//     * Encrypts and then copies the contents of a given file.
//     * @param in 	Input file to encrypt (source file to encrypt)
//     * @param out 	Out encrypted file (target file encrypted)
//     */
//    public void encrypt(File in, File out) throws IOException, InvalidKeyException {
//        try {
//            makeKey();
//        } catch (NoSuchAlgorithmException e) {
//            e.printStackTrace();
//        }
//        aesCipher.init(Cipher.ENCRYPT_MODE, aeskeySpec);
//
//        FileInputStream is    = new FileInputStream(in);
//        CipherOutputStream os = new CipherOutputStream(new FileOutputStream(out), aesCipher);
//
//        copy(is, os);
//
//        os.close();
//    }
//
//    /**
//     * Decrypts and then copies the contents of a given file.
//     * @param in 	Input encrypted file (source existing encrypted file)
//     * @param out 	Output unencrypted file (target file unencrypted)
//     */
//    public void decrypt(File in, File out) throws IOException, InvalidKeyException {
//        try {
//            makeKey();
//        } catch (NoSuchAlgorithmException e) {
//            e.printStackTrace();
//        }
//        aesCipher.init(Cipher.DECRYPT_MODE, aeskeySpec);
//
//        CipherInputStream is = new CipherInputStream(new FileInputStream(in), aesCipher);
//        FileOutputStream os  = new FileOutputStream(out);
//
//        copy(is, os);
//
//        is.close();
//        os.close();
//    }

    /**
     * Copies a stream.
     */
    private void copy(InputStream is, OutputStream os) throws IOException {
        int i;
        byte[] b = new byte[1024];
        while((i=is.read(b))!=-1) {
            os.write(b, 0, i);
        }
    }

    public static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i+1), 16));
        }
        return data;
    }


    /**
     * CipherFactory for Encrypt/Decrypt File
     * Kusnendi.Muhamad
     * 02/02/2018
     */
    public class CipherFactory {
        private String signature;
        private Cipher aesCipher;
        private SecretKeySpec aeskeySpec;

        public CipherFactory(Cipher aesCipher, SecretKeySpec aeskeySpec, String signature) {
            this.aesCipher  = aesCipher;
            this.aeskeySpec = aeskeySpec;
            this.signature  = signature;
        }

        /**
         * Encrypts and then copies the contents of a given file.
         * @param in 	Input file to encrypt (source file to encrypt)
         * @param out 	Out encrypted file (target file encrypted)
         */
        public void encrypt(File in, File out, Callback callback) throws IOException, InvalidKeyException {
            try {
                makeKey();
            } catch (NoSuchAlgorithmException e) {
                e.printStackTrace();
            }
            aesCipher.init(Cipher.ENCRYPT_MODE, aeskeySpec);

            FileInputStream is    = new FileInputStream(in);
            CipherOutputStream os = new CipherOutputStream(new FileOutputStream(out), aesCipher);

            copy(is, os);
            is.close();
            os.close();

            //Notify back to main caller
            callback.onComplete(signature);
        }

        /**
         * Decrypts and then copies the contents of a given file.
         * @param in 	Input encrypted file (source existing encrypted file)
         * @param out 	Output unencrypted file (target file unencrypted)
         */
        public void decrypt(File in, File out, Callback callback) throws IOException, InvalidKeyException {
            try {
                makeKey();
            } catch (NoSuchAlgorithmException e) {
                e.printStackTrace();
            }
            aesCipher.init(Cipher.DECRYPT_MODE, aeskeySpec);

            CipherInputStream is = new CipherInputStream(new FileInputStream(in), aesCipher);
            FileOutputStream os  = new FileOutputStream(out);

            copy(is, os);

            is.close();
            os.close();

            //Notify back to main caller
            callback.onComplete(out);
        }
    }
}
