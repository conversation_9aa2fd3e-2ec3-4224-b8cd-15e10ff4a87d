package com.adins.mss.base.getreferantor;

import android.content.Context;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.getotr.JsonResponseGetOTR;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;

public class CekReferantorApi {

    private Context context;

    public CekReferantorApi(Context context) {
        this.context = context;
    }

    public JsonResponseCekReferantor request(JsonRequestCekReferantor jsonRequestCekReferantor) {
        String url = GlobalData.getSharedGlobalData().getURL_GET_CEK_REFERANTOR();
        String requestJson = GsonHelper.toJson(jsonRequestCekReferantor);

        boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
        boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
        HttpCryptedConnection httpCryptedConnection = new HttpCryptedConnection(context, encrypt, decrypt);
        HttpConnectionResult serverResult = null;
        try {
            serverResult = httpCryptedConnection.requestToServer(url, requestJson, Global.SORTCONNECTIONTIMEOUT);
        } catch (Exception e) {
            e.printStackTrace();
        }

        String responseJson;
        JsonResponseCekReferantor response = null;
        if (null != serverResult && serverResult.isOK()) {
            try {
                responseJson = serverResult.getResult();
                response = GsonHelper.fromJson(responseJson, JsonResponseCekReferantor.class);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            response = new JsonResponseCekReferantor();
            JsonResponseGetOTR.Status status = new JsonResponseGetOTR.Status();
            status.setMessage(serverResult.getResult());
            status.setCode(serverResult.getStatusCode());
            response.setStatus(status);
        }
        return response;
    }
}
