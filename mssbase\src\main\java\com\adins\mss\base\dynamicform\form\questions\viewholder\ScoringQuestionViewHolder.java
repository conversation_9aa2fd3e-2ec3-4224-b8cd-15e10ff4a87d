package com.adins.mss.base.dynamicform.form.questions.viewholder;

import android.app.ProgressDialog;
import android.content.Context;
import android.os.AsyncTask;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.adins.mss.base.R;
import com.adins.mss.base.decision.GenericRuleJexlLogic;
import com.adins.mss.base.decision.RuleLogic;
import com.adins.mss.base.decision.RuleParam;
import com.adins.mss.foundation.questiongenerator.QuestionBean;
import com.adins.mss.foundation.questiongenerator.form.QuestionView;
import com.google.gson.Gson;

/**
 * Created by developer on 10/27/17.
 */

public class ScoringQuestionViewHolder extends RecyclerView.ViewHolder {
    public QuestionView mView;
    public TextView mQuestionLabel;
    public TextView mDescription;
    public Button mButtonScoring;
    public EditText mScoringResult;
    public QuestionBean bean;
    public int tmpAnswer;

    private String description;

    public ScoringQuestionViewHolder(View itemView) {
        super(itemView);

        mView = (QuestionView) itemView.findViewById(R.id.questionScoringLayout);
        mQuestionLabel = (TextView) itemView.findViewById(R.id.questionScoringLabel);
        mDescription   = (TextView) itemView.findViewById(R.id.description);
        mButtonScoring = (Button) itemView.findViewById(R.id.btnScoring);
        mScoringResult = (EditText) itemView.findViewById(R.id.txtScoringResult);
    }

    public void bind(final QuestionBean item, int number) {
        this.bean = item;
        String questionLabel = number + ". " + bean.getQuestion_label();
        String answer = bean.getAnswer();
        description   = mView.getContext().getString(R.string.text_scoring_matrix, "N/A");

        mQuestionLabel.setText(questionLabel);
//        mDescription.setText(description);
        View.OnClickListener listener = null;

        //Read only, Answer generated by request calculate
        mScoringResult.setCursorVisible(false);
        mScoringResult.setEnabled(false);

        if(answer != null && !answer.isEmpty()) {
            mScoringResult.setText(answer);
        }

        listener = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Context context = mView.getContext(); //Nendi: 20/02/2018 | Add Scoring Calculation
                RuleParam param = new Gson().fromJson(bean.getQuestion_value(), RuleParam.class);

                ScoringTask scoringTask = new ScoringTask(context, param);
                scoringTask.execute();
            }
        };

        mButtonScoring.setOnClickListener(listener);
    }

    private class ScoringTask extends AsyncTask<Void, Void, Float> {
        private Context context;
        private RuleParam param;
        private ProgressDialog dialog;
        private boolean hasError;
        private RuleLogic logic;

        public ScoringTask(Context context, RuleParam param) {
            this.context    = context;
            this.param      = param;
        }

        @Override
        protected Float doInBackground(Void... voids) {
            logic = new GenericRuleJexlLogic();
            try {
                return logic.calculateScore(context, param);
            } catch (Exception ex) {
                ex.printStackTrace();
                hasError = true;
            }

            return 0f;
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();

            dialog = new ProgressDialog(context);
            dialog.setMessage(context.getString(R.string.please_wait));
            dialog.setCancelable(false);
            dialog.show();
        }

        @Override
        protected void onPostExecute(Float aFloat) {
            super.onPostExecute(aFloat);
            dialog.dismiss();

            String matrix = logic.scoreMatrix(context, (double) aFloat);
//            mDescription.setText(matrix.trim());

//            description   = mView.getContext().getString(R.string.text_scoring_matrix, matrix.trim());
//            if (aFloat > 0f) mDescription.setText(description);
//            if (aFloat > 0f) mDescription.setText(matrix.trim());

            if (hasError)
                Toast.makeText(context, "Failed to calculate scoring!", Toast.LENGTH_LONG).show();

            mScoringResult.setText(matrix.trim());
            bean.setAnswer(matrix.trim());
            bean.setLovId(String.valueOf(aFloat));
        }
    }
}
