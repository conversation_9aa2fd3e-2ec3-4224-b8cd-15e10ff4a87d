package com.adins.mss.foundation.db.dataaccess;

import android.content.Context;

import com.adins.mss.dao.DaoSession;
import com.adins.mss.dao.Rule;
import com.adins.mss.dao.RuleDao;
import com.adins.mss.foundation.db.DaoOpenHelper;

import java.util.List;

import de.greenrobot.dao.query.QueryBuilder;

/**
 * Created by k<PERSON><PERSON><PERSON> on 06/02/18.
 */

public class RuleDataAccess {

    /**
     * use to generate dao session that you can access modelDao
     *
     * @param context --> context from activity
     * @return
     */
    protected static DaoSession getDaoSession(Context context) {
        return DaoOpenHelper.getDaoSession(context);
    }

    /**
     * Get Rule Dao
     * @param context
     * @return
     */
    private static RuleDao getRuleDao(Context context) {
        return getDaoSession(context).getRuleDao();
    }

    public static Rule find(Context context, int id) {
        QueryBuilder<Rule> qb = getRuleDao(context).queryBuilder();
        qb.where(RuleDao.Properties.Id.eq(id));
        qb.build();

        if (!qb.list().isEmpty()) return qb.list().get(0);
        else return null;
    }

    public static Rule find(Context context, String name) {
        QueryBuilder<Rule> qb = getRuleDao(context).queryBuilder();
        qb.where(RuleDao.Properties.Rule_name.eq(name));
        qb.build();

        if (!qb.list().isEmpty()) return qb.list().get(0);
        else return null;
    }

    public static List<Rule> all(Context context) {
        List<Rule> rules = getRuleDao(context).loadAll();

        if (rules.isEmpty()) return null;
        else return rules;
    }

    public static void addOrReplace(Context context, List<Rule> rules) {
        getRuleDao(context).insertOrReplaceInTx(rules);
        getDaoSession(context).clear();
    }

    public static void addOrReplace(Context context, Rule rule) {
        getRuleDao(context).insertOrReplace(rule);
        getDaoSession(context).clear();
    }

    public static void clean(Context context) {
        getRuleDao(context).deleteAll();
        getDaoSession(context).clear();
    }

    public static void delete(Context context, Rule rule) {
        getRuleDao(context).delete(rule);
        getDaoSession(context).clear();
    }

    public static Rule last(Context context) {
        QueryBuilder<Rule> qb = getRuleDao(context).queryBuilder();
        qb.orderDesc(RuleDao.Properties.Dtm_upd);
        qb.limit(1);
        qb.build();

        if (!qb.list().isEmpty()) return qb.list().get(0);
        return null;
    }
}
