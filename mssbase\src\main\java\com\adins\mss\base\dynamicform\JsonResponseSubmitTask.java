package com.adins.mss.base.dynamicform;

import com.adins.mss.foundation.http.MssResponseType;
import com.google.gson.annotations.SerializedName;

public class JsonResponseSubmitTask extends MssResponseType {
    /**
     * Property result
     */
    @SerializedName("result")
    String result;
    @SerializedName("taskId")
    String taskId;
    @SerializedName("cash_on_hand")
    String cashOnHand;
    @SerializedName("uuid")
    String uuidTask;
    @SerializedName("isDropTask")
    boolean isDropTask;
    @SerializedName("messageTask")
    String message;

    public String getCashOnHand() {
        return cashOnHand;
    }

    public void setCashOnHand(String cashOnHand) {
        this.cashOnHand = cashOnHand;
    }

    /**
     * Gets the result
     */
    public String getResult() {
        return this.result;
    }

    /**
     * Sets the result
     */
    public void setResult(String value) {
        this.result = value;
    }

    /**
     * Gets the Task id
     */
    public String getTaskId() {
        return this.taskId;
    }

    /**
     * Sets the Task id
     */
    public void setTaskId(String value) {
        this.taskId = value;
    }

    public String getUuidTask() {
        return uuidTask;
    }

    public void setUuidTask(String uuidTask) {
        this.uuidTask = uuidTask;
    }

    public boolean isDropTask() {
        return isDropTask;
    }

    public void setDropTask(boolean dropTask) {
        isDropTask = dropTask;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
