package com.adins.mss.dao;

import java.util.List;
import com.adins.mss.dao.DaoSession;
import de.greenrobot.dao.DaoException;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "TR_TIMELINE".
 */
public class Timeline {

    /** Not-null value. */
     @SerializedName("uuid_timeline")
    private String uuid_timeline;
     @SerializedName("description")
    private String description;
     @SerializedName("latitude")
    private String latitude;
     @SerializedName("longitude")
    private String longitude;
     @SerializedName("dtm_crt_server")
    private java.util.Date dtm_crt_server;
     @SerializedName("usr_crt")
    private String usr_crt;
     @SerializedName("dtm_crt")
    private java.util.Date dtm_crt;
     @SerializedName("uuid_task_h")
    private String uuid_task_h;
     @SerializedName("uuid_user")
    private String uuid_user;
     @SerializedName("uuid_timeline_type")
    private String uuid_timeline_type;
     @SerializedName("uuid_message")
    private String uuid_message;
     @SerializedName("byte_image")
    private byte[] byte_image;

    /** Used to resolve relations */
    private transient DaoSession daoSession;

    /** Used for active entity operations. */
    private transient TimelineDao myDao;

    private User user;
    private String user__resolvedKey;

    private TimelineType timelineType;
    private String timelineType__resolvedKey;

    private Message message;
    private String message__resolvedKey;

    private TaskH taskH;
    private String taskH__resolvedKey;

    private List<Comment> commentList;

    public Timeline() {
    }

    public Timeline(String uuid_timeline) {
        this.uuid_timeline = uuid_timeline;
    }

    public Timeline(String uuid_timeline, String description, String latitude, String longitude, java.util.Date dtm_crt_server, String usr_crt, java.util.Date dtm_crt, String uuid_task_h, String uuid_user, String uuid_timeline_type, String uuid_message, byte[] byte_image) {
        this.uuid_timeline = uuid_timeline;
        this.description = description;
        this.latitude = latitude;
        this.longitude = longitude;
        this.dtm_crt_server = dtm_crt_server;
        this.usr_crt = usr_crt;
        this.dtm_crt = dtm_crt;
        this.uuid_task_h = uuid_task_h;
        this.uuid_user = uuid_user;
        this.uuid_timeline_type = uuid_timeline_type;
        this.uuid_message = uuid_message;
        this.byte_image = byte_image;
    }

    /** called by internal mechanisms, do not call yourself. */
    public void __setDaoSession(DaoSession daoSession) {
        this.daoSession = daoSession;
        myDao = daoSession != null ? daoSession.getTimelineDao() : null;
    }

    /** Not-null value. */
    public String getUuid_timeline() {
        return uuid_timeline;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_timeline(String uuid_timeline) {
        this.uuid_timeline = uuid_timeline;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public java.util.Date getDtm_crt_server() {
        return dtm_crt_server;
    }

    public void setDtm_crt_server(java.util.Date dtm_crt_server) {
        this.dtm_crt_server = dtm_crt_server;
    }

    public String getUsr_crt() {
        return usr_crt;
    }

    public void setUsr_crt(String usr_crt) {
        this.usr_crt = usr_crt;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public String getUuid_task_h() {
        return uuid_task_h;
    }

    public void setUuid_task_h(String uuid_task_h) {
        this.uuid_task_h = uuid_task_h;
    }

    public String getUuid_user() {
        return uuid_user;
    }

    public void setUuid_user(String uuid_user) {
        this.uuid_user = uuid_user;
    }

    public String getUuid_timeline_type() {
        return uuid_timeline_type;
    }

    public void setUuid_timeline_type(String uuid_timeline_type) {
        this.uuid_timeline_type = uuid_timeline_type;
    }

    public String getUuid_message() {
        return uuid_message;
    }

    public void setUuid_message(String uuid_message) {
        this.uuid_message = uuid_message;
    }

    public byte[] getByte_image() {
        return byte_image;
    }

    public void setByte_image(byte[] byte_image) {
        this.byte_image = byte_image;
    }

    /** To-one relationship, resolved on first access. */
    public User getUser() {
        String __key = this.uuid_user;
        if (user__resolvedKey == null || user__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            UserDao targetDao = daoSession.getUserDao();
            User userNew = targetDao.load(__key);
            synchronized (this) {
                user = userNew;
            	user__resolvedKey = __key;
            }
        }
        return user;
    }

    public void setUser(User user) {
        synchronized (this) {
            this.user = user;
            uuid_user = user == null ? null : user.getUuid_user();
            user__resolvedKey = uuid_user;
        }
    }

    /** To-one relationship, resolved on first access. */
    public TimelineType getTimelineType() {
        String __key = this.uuid_timeline_type;
        if (timelineType__resolvedKey == null || timelineType__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            TimelineTypeDao targetDao = daoSession.getTimelineTypeDao();
            TimelineType timelineTypeNew = targetDao.load(__key);
            synchronized (this) {
                timelineType = timelineTypeNew;
            	timelineType__resolvedKey = __key;
            }
        }
        return timelineType;
    }

    public void setTimelineType(TimelineType timelineType) {
        synchronized (this) {
            this.timelineType = timelineType;
            uuid_timeline_type = timelineType == null ? null : timelineType.getUuid_timeline_type();
            timelineType__resolvedKey = uuid_timeline_type;
        }
    }

    /** To-one relationship, resolved on first access. */
    public Message getMessage() {
        String __key = this.uuid_message;
        if (message__resolvedKey == null || message__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            MessageDao targetDao = daoSession.getMessageDao();
            Message messageNew = targetDao.load(__key);
            synchronized (this) {
                message = messageNew;
            	message__resolvedKey = __key;
            }
        }
        return message;
    }

    public void setMessage(Message message) {
        synchronized (this) {
            this.message = message;
            uuid_message = message == null ? null : message.getUuid_message();
            message__resolvedKey = uuid_message;
        }
    }

    /** To-one relationship, resolved on first access. */
    public TaskH getTaskH() {
        String __key = this.uuid_task_h;
        if (taskH__resolvedKey == null || taskH__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            TaskHDao targetDao = daoSession.getTaskHDao();
            TaskH taskHNew = targetDao.load(__key);
            synchronized (this) {
                taskH = taskHNew;
            	taskH__resolvedKey = __key;
            }
        }
        return taskH;
    }

    public void setTaskH(TaskH taskH) {
        synchronized (this) {
            this.taskH = taskH;
            uuid_task_h = taskH == null ? null : taskH.getUuid_task_h();
            taskH__resolvedKey = uuid_task_h;
        }
    }

    /** To-many relationship, resolved on first access (and after reset). Changes to to-many relations are not persisted, make changes to the target entity. */
    public List<Comment> getCommentList() {
        if (commentList == null) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            CommentDao targetDao = daoSession.getCommentDao();
            List<Comment> commentListNew = targetDao._queryTimeline_CommentList(uuid_timeline);
            synchronized (this) {
                if(commentList == null) {
                    commentList = commentListNew;
                }
            }
        }
        return commentList;
    }

    /** Resets a to-many relationship, making the next get call to query for a fresh result. */
    public synchronized void resetCommentList() {
        commentList = null;
    }

    /** Convenient call for {@link AbstractDao#delete(Object)}. Entity must attached to an entity context. */
    public void delete() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.delete(this);
    }

    /** Convenient call for {@link AbstractDao#update(Object)}. Entity must attached to an entity context. */
    public void update() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.update(this);
    }

    /** Convenient call for {@link AbstractDao#refresh(Object)}. Entity must attached to an entity context. */
    public void refresh() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.refresh(this);
    }

}
