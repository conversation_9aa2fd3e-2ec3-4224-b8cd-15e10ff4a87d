
package com.adins.mss.base.decision;

import java.util.List;

import com.adins.mss.base.commons.Query;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class RuleParam {

    @SerializedName("table")
    @Expose
    private String table;
    @SerializedName("reffIdPO")
    @Expose
    private String reffIdPO;
    @SerializedName("file")
    @Expose
    private String file;
    @SerializedName("assetConditionRefId")
    @Expose
    private String assetConditionRefId;
    @SerializedName("codeIfRUle")
    @Expose
    private String codeIfRUle;
    @SerializedName(value = "keyvalues", alternate = {"constraint"})
    @Expose
    private List<Keyvalue> keyvalues = null;

    @SerializedName("constraint2")
    @Expose
    private List<Query.Constraint> constraints = null;

    public String getTable() {
        return table;
    }

    public void setTable(String table) {
        this.table = table;
    }

    public String getReffIdPO() {
        return reffIdPO;
    }

    public void setReffIdPO(String reffIdPO) {
        this.reffIdPO = reffIdPO;
    }

    public String getFile() {
        return file;
    }

    public void setFile(String file) {
        this.file = file;
    }

    public List<Keyvalue> getKeyvalues() {
        return keyvalues;
    }

    public void setKeyvalues(List<Keyvalue> keyvalues) {
        this.keyvalues = keyvalues;
    }

    public List<Query.Constraint> getConstraints() {
        return constraints;
    }

    public void setConstraints(List<Query.Constraint> constraints) {
        this.constraints = constraints;
    }

    public String getAssetConditionRefId() {
        return assetConditionRefId;
    }

    public void setAssetConditionRefId(String assetConditionRefId) {
        this.assetConditionRefId = assetConditionRefId;
    }

    public String getCodeIfRUle() {
        return codeIfRUle;
    }

    public void setCodeIfRUle(String codeIfRUle) {
        this.codeIfRUle = codeIfRUle;
    }
}
