package com.adins.mss.base.invitationesign;

import android.content.Context;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.http.MssResponseType;

public class InvitationEsignApi {

    private Context context;

    public InvitationEsignApi(Context context) {
        this.context = context;
    }

    public MssResponseType request(JsonRequestInvitationEsign jsonRequestEsignVal) {

        String url = GlobalData.getSharedGlobalData().getURL_INVITATION_ESIGN();
        String requestJson = GsonHelper.toJson(jsonRequestEsignVal);

        boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
        boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
        HttpCryptedConnection httpCryptedConnection = new HttpCryptedConnection(context, encrypt, decrypt);
        HttpConnectionResult serverResult = null;
        try {
            serverResult = httpCryptedConnection.requestToServer(url, requestJson, Global.SORTCONNECTIONTIMEOUT);
        } catch (Exception e) {
            e.printStackTrace();
        }
        MssResponseType response = null;
        String responseJson;
        if (null != serverResult && serverResult.isOK()) {
            try {
                responseJson = serverResult.getResult();
                response = GsonHelper.fromJson(responseJson, MssResponseType.class);
            } catch (Exception e) {
                return null;
            }
        } else {
            response = new MssResponseType();
            MssResponseType.Status status = new MssResponseType.Status();
            status.setMessage(serverResult.getResult());
            status.setCode(serverResult.getStatusCode());
            response.setStatus(status);
        }
        return response;
    }
}
