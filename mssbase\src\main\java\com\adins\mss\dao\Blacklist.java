package com.adins.mss.dao;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "MS_BLACKLIST".
 */
public class Blacklist {
    public static int TYPE_NAME = 10;
    public static int TYPE_PHONE= 20;

     @SerializedName("id")
    private long id;
     @SerializedName("excludeInfo1")
    private String exclude_info1;
     @SerializedName("excludeInfo2")
    private String exclude_info2;
    /** Not-null value. */
     @SerializedName("excludeTypeCode")
    private String exclude_type_code;
    /** Not-null value. */
     @SerializedName("excludeTypeName")
    private String exclude_type_name;
     @SerializedName("isDeleted")
    private Integer is_deleted;
     @SerializedName("dtmUpd")
    private java.util.Date dtm_upd;

    public Blacklist() {
    }

    public Blacklist(long id) {
        this.id = id;
    }

    public Blacklist(long id, String exclude_info1, String exclude_info2, String exclude_type_code, String exclude_type_name, Integer is_deleted, java.util.Date dtm_upd) {
        this.id = id;
        this.exclude_info1 = exclude_info1;
        this.exclude_info2 = exclude_info2;
        this.exclude_type_code = exclude_type_code;
        this.exclude_type_name = exclude_type_name;
        this.is_deleted = is_deleted;
        this.dtm_upd = dtm_upd;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getExclude_info1() {
        return exclude_info1;
    }

    public void setExclude_info1(String exclude_info1) {
        this.exclude_info1 = exclude_info1;
    }

    public String getExclude_info2() {
        return exclude_info2;
    }

    public void setExclude_info2(String exclude_info2) {
        this.exclude_info2 = exclude_info2;
    }

    /** Not-null value. */
    public String getExclude_type_code() {
        return exclude_type_code;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setExclude_type_code(String exclude_type_code) {
        this.exclude_type_code = exclude_type_code;
    }

    /** Not-null value. */
    public String getExclude_type_name() {
        return exclude_type_name;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setExclude_type_name(String exclude_type_name) {
        this.exclude_type_name = exclude_type_name;
    }

    public Integer getIs_deleted() {
        return is_deleted;
    }

    public void setIs_deleted(Integer is_deleted) {
        this.is_deleted = is_deleted;
    }

    public java.util.Date getDtm_upd() {
        return dtm_upd;
    }

    public void setDtm_upd(java.util.Date dtm_upd) {
        this.dtm_upd = dtm_upd;
    }

}
