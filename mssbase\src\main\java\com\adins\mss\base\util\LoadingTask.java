package com.adins.mss.base.util;

import android.app.ProgressDialog;
import android.content.Context;

import com.adins.mss.base.crashlytics.FireCrash;


//public class LoadingTask extends AsyncTask<String, String, String> {

/**
 * Modification of GenericAsyncTask which will display progress dialog when executing background process
 *
 * <AUTHOR>
 */
public class LoadingTask extends GenericAsyncTask {
    protected String errMessage = null;
    private ProgressDialog progressDialog;
    //	private Activity activity;
    private Context context;
    //	private LoadingTaskInterface delegate;
    private String loadingMessage = "Loading";

    public LoadingTask(Context context, GenericTaskInterface delegate, String loadingMessage) {
        super(delegate);
        this.context = context;
        if (loadingMessage != null) {
            this.loadingMessage = loadingMessage;
        }
    }

    public LoadingTask(Context context, String loadingMessage) {
        super(null);
        this.context = context;
        if (loadingMessage != null) {
            this.loadingMessage = loadingMessage;
        }
    }

    @Override
    protected void onPreExecute() {
        progressDialog = ProgressDialog.show(context,
                "", loadingMessage, true);
        super.onPreExecute();
    }

//	@Override
//	protected String doInBackground(String... arg0) {
//	
//		//let the delegate implement try catch if needed
//	      		return delegate.doInBackground(this);
//
//	}

    @Override
    protected void onPostExecute(String result) {
        if (progressDialog != null && progressDialog.isShowing()) {
            try {
                progressDialog.dismiss();
            } catch (Exception e) {
                FireCrash.log(e);
            }
        }

//		if (errMessage != null) {
        //DialogManager.showAlert(activity, DialogManager.TYPE_ERROR, errMessage);
//			Toast.makeText(context, "this is must be and alert "+errMessage,
//					   Toast.LENGTH_LONG).show();
//		}
//		else {
//			delegate.onPostExecute(this, result, errMessage);
        super.onPostExecute(result);
//		}
    }

//	public LoadingTaskInterface getDelegate() {
//		return delegate;
//	}
//
//	public void setDelegate(LoadingTaskInterface delegate) {
//		this.delegate = delegate;
//	}
//
//	public interface LoadingTaskInterface{
//		String doInBackground(LoadingTask loadingTask);
//		void onPostExecute(LoadingTask loadingTask, String result, String errMsg);
//	}


    // Generic Task Interface
//	@Override
//	public void onPreExecute(GenericAsyncTask task) {
//		// TODO Auto-generated method stub
//		
//	}
//
//	@Override
//	public String doInBackground(GenericAsyncTask task) {
//		// TODO Auto-generated method stub
//		return null;
//	}
//
//	@Override
//	public void onPostExecute(GenericAsyncTask task, String result,
//			String errMsg) {
//		// TODO Auto-generated method stub
//		
//	}
}
