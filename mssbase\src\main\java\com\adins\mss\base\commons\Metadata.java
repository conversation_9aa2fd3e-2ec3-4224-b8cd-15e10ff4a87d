package com.adins.mss.base.commons;

import com.google.firebase.database.IgnoreExtraProperties;
import com.google.gson.annotations.SerializedName;

/**
 * Created by developer on 2/1/18.
 */
@IgnoreExtraProperties
public class Metadata {
    private String filename;
    @SerializedName("identifier")
    private String identifier;
    @SerializedName("alternateUrl")
    private String alternateUrl;
    @SerializedName("url")
    private String url;
    @SerializedName("sha1")
    private String sha1;
    @SerializedName("signature")
    private String signature;
    @SerializedName("dtmUpd")
    private String dtmUpd;
    private String path;

    public Metadata(){}

    public Metadata(String alternateUrl, String url, String sha1, String signature, String dtmUpd) {
        this.alternateUrl   = alternateUrl;
        this.url            = url;
        this.sha1           = sha1;
        this.signature      = signature;
        this.dtmUpd         = dtmUpd;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public String getIdentifier() {
        return identifier;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public String getAlternateUrl() {
        return alternateUrl;
    }

    public void setAlternateUrl(String alternateUrl) {
        this.alternateUrl = alternateUrl;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getSha1() {
        return sha1;
    }

    public void setSha1(String sha1) {
        this.sha1 = sha1;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getDtmUpd() {
        return dtmUpd;
    }

    public void setDtmUpd(String dtmUpd) {
        this.dtmUpd = dtmUpd;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    @Override
    public String toString() {
        return "Files{" +
                "alternateUrl='" + alternateUrl + '\'' +
                ", url='" + url + '\'' +
                ", sha1='" + sha1 + '\'' +
                ", signature='" + signature + '\'' +
                ", dtmUpd='" + dtmUpd + '\'' +
                '}';
    }
}
