package com.adins.mss.dao;

import java.util.List;
import java.util.ArrayList;
import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.SqlUtils;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;
import de.greenrobot.dao.query.Query;
import de.greenrobot.dao.query.QueryBuilder;

import com.adins.mss.dao.LocationInfo;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TR_LOCATION".
*/
public class LocationInfoDao extends AbstractDao<LocationInfo, String> {

    public static final String TABLENAME = "TR_LOCATION";

    /**
     * Properties of entity LocationInfo.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_location_info = new Property(0, String.class, "uuid_location_info", true, "UUID_LOCATION_INFO");
        public final static Property Latitude = new Property(1, String.class, "latitude", false, "LATITUDE");
        public final static Property Longitude = new Property(2, String.class, "longitude", false, "LONGITUDE");
        public final static Property Mcc = new Property(3, String.class, "mcc", false, "MCC");
        public final static Property Mnc = new Property(4, String.class, "mnc", false, "MNC");
        public final static Property Lac = new Property(5, String.class, "lac", false, "LAC");
        public final static Property Cid = new Property(6, String.class, "cid", false, "CID");
        public final static Property Handset_time = new Property(7, java.util.Date.class, "handset_time", false, "HANDSET_TIME");
        public final static Property Mode = new Property(8, String.class, "mode", false, "MODE");
        public final static Property Accuracy = new Property(9, Integer.class, "accuracy", false, "ACCURACY");
        public final static Property Gps_time = new Property(10, java.util.Date.class, "gps_time", false, "GPS_TIME");
        public final static Property Is_gps_time = new Property(11, String.class, "is_gps_time", false, "IS_GPS_TIME");
        public final static Property Usr_crt = new Property(12, String.class, "usr_crt", false, "USR_CRT");
        public final static Property Dtm_crt = new Property(13, java.util.Date.class, "dtm_crt", false, "DTM_CRT");
        public final static Property Location_type = new Property(14, String.class, "location_type", false, "LOCATION_TYPE");
        public final static Property Uuid_user = new Property(15, String.class, "uuid_user", false, "UUID_USER");
    };

    private DaoSession daoSession;

    private Query<LocationInfo> user_LocationInfoListQuery;

    public LocationInfoDao(DaoConfig config) {
        super(config);
    }
    
    public LocationInfoDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TR_LOCATION\" (" + //
                "\"UUID_LOCATION_INFO\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_location_info
                "\"LATITUDE\" TEXT," + // 1: latitude
                "\"LONGITUDE\" TEXT," + // 2: longitude
                "\"MCC\" TEXT," + // 3: mcc
                "\"MNC\" TEXT," + // 4: mnc
                "\"LAC\" TEXT," + // 5: lac
                "\"CID\" TEXT," + // 6: cid
                "\"HANDSET_TIME\" INTEGER," + // 7: handset_time
                "\"MODE\" TEXT," + // 8: mode
                "\"ACCURACY\" INTEGER," + // 9: accuracy
                "\"GPS_TIME\" INTEGER," + // 10: gps_time
                "\"IS_GPS_TIME\" TEXT," + // 11: is_gps_time
                "\"USR_CRT\" TEXT," + // 12: usr_crt
                "\"DTM_CRT\" INTEGER," + // 13: dtm_crt
                "\"LOCATION_TYPE\" TEXT," + // 14: location_type
                "\"UUID_USER\" TEXT);"); // 15: uuid_user
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TR_LOCATION\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, LocationInfo entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_location_info());
 
        String latitude = entity.getLatitude();
        if (latitude != null) {
            stmt.bindString(2, latitude);
        }
 
        String longitude = entity.getLongitude();
        if (longitude != null) {
            stmt.bindString(3, longitude);
        }
 
        String mcc = entity.getMcc();
        if (mcc != null) {
            stmt.bindString(4, mcc);
        }
 
        String mnc = entity.getMnc();
        if (mnc != null) {
            stmt.bindString(5, mnc);
        }
 
        String lac = entity.getLac();
        if (lac != null) {
            stmt.bindString(6, lac);
        }
 
        String cid = entity.getCid();
        if (cid != null) {
            stmt.bindString(7, cid);
        }
 
        java.util.Date handset_time = entity.getHandset_time();
        if (handset_time != null) {
            stmt.bindLong(8, handset_time.getTime());
        }
 
        String mode = entity.getMode();
        if (mode != null) {
            stmt.bindString(9, mode);
        }
 
        Integer accuracy = entity.getAccuracy();
        if (accuracy != null) {
            stmt.bindLong(10, accuracy);
        }
 
        java.util.Date gps_time = entity.getGps_time();
        if (gps_time != null) {
            stmt.bindLong(11, gps_time.getTime());
        }
 
        String is_gps_time = entity.getIs_gps_time();
        if (is_gps_time != null) {
            stmt.bindString(12, is_gps_time);
        }
 
        String usr_crt = entity.getUsr_crt();
        if (usr_crt != null) {
            stmt.bindString(13, usr_crt);
        }
 
        java.util.Date dtm_crt = entity.getDtm_crt();
        if (dtm_crt != null) {
            stmt.bindLong(14, dtm_crt.getTime());
        }
 
        String location_type = entity.getLocation_type();
        if (location_type != null) {
            stmt.bindString(15, location_type);
        }
 
        String uuid_user = entity.getUuid_user();
        if (uuid_user != null) {
            stmt.bindString(16, uuid_user);
        }
    }

    @Override
    protected void attachEntity(LocationInfo entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public LocationInfo readEntity(Cursor cursor, int offset) {
        LocationInfo entity = new LocationInfo( //
            cursor.getString(offset + 0), // uuid_location_info
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // latitude
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // longitude
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // mcc
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // mnc
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // lac
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // cid
            cursor.isNull(offset + 7) ? null : new java.util.Date(cursor.getLong(offset + 7)), // handset_time
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8), // mode
            cursor.isNull(offset + 9) ? null : cursor.getInt(offset + 9), // accuracy
            cursor.isNull(offset + 10) ? null : new java.util.Date(cursor.getLong(offset + 10)), // gps_time
            cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11), // is_gps_time
            cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12), // usr_crt
            cursor.isNull(offset + 13) ? null : new java.util.Date(cursor.getLong(offset + 13)), // dtm_crt
            cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14), // location_type
            cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15) // uuid_user
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, LocationInfo entity, int offset) {
        entity.setUuid_location_info(cursor.getString(offset + 0));
        entity.setLatitude(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setLongitude(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setMcc(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setMnc(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setLac(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setCid(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setHandset_time(cursor.isNull(offset + 7) ? null : new java.util.Date(cursor.getLong(offset + 7)));
        entity.setMode(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
        entity.setAccuracy(cursor.isNull(offset + 9) ? null : cursor.getInt(offset + 9));
        entity.setGps_time(cursor.isNull(offset + 10) ? null : new java.util.Date(cursor.getLong(offset + 10)));
        entity.setIs_gps_time(cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11));
        entity.setUsr_crt(cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12));
        entity.setDtm_crt(cursor.isNull(offset + 13) ? null : new java.util.Date(cursor.getLong(offset + 13)));
        entity.setLocation_type(cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14));
        entity.setUuid_user(cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(LocationInfo entity, long rowId) {
        return entity.getUuid_location_info();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(LocationInfo entity) {
        if(entity != null) {
            return entity.getUuid_location_info();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
    /** Internal query to resolve the "locationInfoList" to-many relationship of User. */
    public List<LocationInfo> _queryUser_LocationInfoList(String uuid_user) {
        synchronized (this) {
            if (user_LocationInfoListQuery == null) {
                QueryBuilder<LocationInfo> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_user.eq(null));
                user_LocationInfoListQuery = queryBuilder.build();
            }
        }
        Query<LocationInfo> query = user_LocationInfoListQuery.forCurrentThread();
        query.setParameter(0, uuid_user);
        return query.list();
    }

    private String selectDeep;

    protected String getSelectDeep() {
        if (selectDeep == null) {
            StringBuilder builder = new StringBuilder("SELECT ");
            SqlUtils.appendColumns(builder, "T", getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T0", daoSession.getUserDao().getAllColumns());
            builder.append(" FROM TR_LOCATION T");
            builder.append(" LEFT JOIN MS_USER T0 ON T.\"UUID_USER\"=T0.\"UUID_USER\"");
            builder.append(' ');
            selectDeep = builder.toString();
        }
        return selectDeep;
    }
    
    protected LocationInfo loadCurrentDeep(Cursor cursor, boolean lock) {
        LocationInfo entity = loadCurrent(cursor, 0, lock);
        int offset = getAllColumns().length;

        User user = loadCurrentOther(daoSession.getUserDao(), cursor, offset);
        entity.setUser(user);

        return entity;    
    }

    public LocationInfo loadDeep(Long key) {
        assertSinglePk();
        if (key == null) {
            return null;
        }

        StringBuilder builder = new StringBuilder(getSelectDeep());
        builder.append("WHERE ");
        SqlUtils.appendColumnsEqValue(builder, "T", getPkColumns());
        String sql = builder.toString();
        
        String[] keyArray = new String[] { key.toString() };
        Cursor cursor = db.rawQuery(sql, keyArray);
        
        try {
            boolean available = cursor.moveToFirst();
            if (!available) {
                return null;
            } else if (!cursor.isLast()) {
                throw new IllegalStateException("Expected unique result, but count was " + cursor.getCount());
            }
            return loadCurrentDeep(cursor, true);
        } finally {
            cursor.close();
        }
    }
    
    /** Reads all available rows from the given cursor and returns a list of new ImageTO objects. */
    public List<LocationInfo> loadAllDeepFromCursor(Cursor cursor) {
        int count = cursor.getCount();
        List<LocationInfo> list = new ArrayList<LocationInfo>(count);
        
        if (cursor.moveToFirst()) {
            if (identityScope != null) {
                identityScope.lock();
                identityScope.reserveRoom(count);
            }
            try {
                do {
                    list.add(loadCurrentDeep(cursor, false));
                } while (cursor.moveToNext());
            } finally {
                if (identityScope != null) {
                    identityScope.unlock();
                }
            }
        }
        return list;
    }
    
    protected List<LocationInfo> loadDeepAllAndCloseCursor(Cursor cursor) {
        try {
            return loadAllDeepFromCursor(cursor);
        } finally {
            cursor.close();
        }
    }
    

    /** A raw-style query where you can pass any WHERE clause and arguments. */
    public List<LocationInfo> queryDeep(String where, String... selectionArg) {
        Cursor cursor = db.rawQuery(getSelectDeep() + where, selectionArg);
        return loadDeepAllAndCloseCursor(cursor);
    }
 
}
