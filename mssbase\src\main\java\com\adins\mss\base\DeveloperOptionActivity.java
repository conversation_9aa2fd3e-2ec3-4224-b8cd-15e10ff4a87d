package com.adins.mss.base;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.Switch;
import android.widget.Toast;

import com.adins.mss.base.util.LocaleHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.foundation.config.ConfigFileReader;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.security.storepreferences.ObscuredSharedPreferences;

import java.util.Locale;
import java.util.Properties;

public class DeveloperOptionActivity extends Activity {

    private RadioButton rdEncryptYes;
    private RadioButton rdEncryptNo;
    private RadioButton rdDecryptYes;
    private RadioButton rdDecryptNo;
    private Button btnSave;
    private Switch switchEncrypt;
    private Switch switchDecrypt;
    private Switch switchAccessToken;
    private LinearLayout clientIdLayout;
    private EditText edtClientId;

    private boolean isEncrypt;
    private boolean isDecrypt;
    private boolean isAccessTokenEnable;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (Tool.isProdFlavour(Global.FLAVORS)) {
            //To can't screenshoot
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
        }

        setContentView(R.layout.activity_developer_option);
        initViews();

    }

    @Override
    protected void attachBaseContext(Context newBase) {
        Context context = newBase;
        Locale locale;
        try {
            locale = new Locale(GlobalData.getSharedGlobalData().getLocale());
            if (null == locale) {
                locale = new Locale(LocaleHelper.ENGLSIH);
            }
            context = LocaleHelper.wrap(newBase, locale);
        } catch (Exception e) {
            locale = new Locale(LocaleHelper.ENGLSIH);
            context = LocaleHelper.wrap(newBase, locale);
        } finally {
            super.attachBaseContext(context);
        }
    }
    private void initViews() {
        rdEncryptYes = (RadioButton) findViewById(R.id.rdEncryptYes);
        rdEncryptNo = (RadioButton) findViewById(R.id.rdEncryptNo);
        rdDecryptYes = (RadioButton) findViewById(R.id.rdDecryptYes);
        rdDecryptNo = (RadioButton) findViewById(R.id.rdDecryptNo);
        btnSave = (Button) findViewById(R.id.btnSave);
        switchDecrypt = (Switch) findViewById(R.id.switchDecrypt);
        switchEncrypt = (Switch) findViewById(R.id.switchEncrypt);
        switchAccessToken = (Switch) findViewById(R.id.switchAccessToken);
        clientIdLayout = (LinearLayout) findViewById(R.id.layoutClientId);
        edtClientId = (EditText) findViewById(R.id.edtClientId);
        final ObscuredSharedPreferences sharedPref = ObscuredSharedPreferences.getPrefs(getApplicationContext(),
                "GlobalData", Context.MODE_PRIVATE);
        Properties prop = ConfigFileReader.propertiesFromFile(this, GlobalData.PROPERTY_FILENAME);
        boolean encrypt = Boolean.parseBoolean(prop.getProperty(GlobalData.PROP_ENCRYPT, "false"));
        boolean decrypt = Boolean.parseBoolean(prop.getProperty(GlobalData.PROP_DECRYPT, "false"));
        boolean accessTokenEnable = Boolean.parseBoolean(prop.getProperty(GlobalData.PROP_IS_REQUIRED_ACCESS_TOKEN, "false"));
        String propClientId = prop.getProperty(GlobalData.PROP_CLIENT_ID, "android");

        boolean hasEncrypt = sharedPref.getBoolean("IS_ENCRYPT", encrypt);
        boolean hasDecrypt = sharedPref.getBoolean("IS_DECRYPT", decrypt);
        boolean isTokenEnable = sharedPref.getBoolean("IS_ACCESS_TOKEN_ENABLE", accessTokenEnable);
        final String clientId = sharedPref.getString("CLIENT_ID", propClientId);

        switchAccessToken.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    clientIdLayout.setVisibility(View.VISIBLE);
                    edtClientId.setText(clientId);
                } else {
                    clientIdLayout.setVisibility(View.GONE);
                }
            }
        });
        if (hasEncrypt) {
            rdEncryptYes.setChecked(true);
            switchEncrypt.setChecked(true);
        }
        if (hasDecrypt) {
            rdDecryptYes.setChecked(true);
            switchDecrypt.setChecked(true);
        }
        if (isTokenEnable) {
            switchAccessToken.setChecked(true);
            clientIdLayout.setVisibility(View.VISIBLE);
            edtClientId.setText(clientId);
        } else {
            clientIdLayout.setVisibility(View.GONE);
        }

        btnSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ObscuredSharedPreferences.Editor sharedPrefEditor = sharedPref.edit();
                isEncrypt = switchEncrypt.isChecked(); //rdEncryptYes.isChecked();
                isDecrypt = switchDecrypt.isChecked(); //rdDecryptYes.isChecked();
                isAccessTokenEnable = switchAccessToken.isChecked();
                if (isAccessTokenEnable && edtClientId.getText() != null && edtClientId.getText().length() > 0) {
                    String clientId = edtClientId.getText().toString();
                    sharedPrefEditor.putString("CLIENT_ID", clientId);
                    GlobalData.getSharedGlobalData().setClientId(clientId);
                }

                GlobalData.getSharedGlobalData().setEncrypt(isEncrypt);
                GlobalData.getSharedGlobalData().setDecrypt(isDecrypt);
                GlobalData.getSharedGlobalData().setRequiresAccessToken(isAccessTokenEnable);


                sharedPrefEditor.putBoolean("IS_ENCRYPT", isEncrypt);
                sharedPrefEditor.putBoolean("IS_DECRYPT", isDecrypt);
                sharedPrefEditor.putBoolean("IS_ACCESS_TOKEN_ENABLE", isAccessTokenEnable);

                sharedPrefEditor.commit();

                Toast.makeText(DeveloperOptionActivity.this, "Options Saved", Toast.LENGTH_SHORT).show();
                finish();
            }
        });

    }

}
