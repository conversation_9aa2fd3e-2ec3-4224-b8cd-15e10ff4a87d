package com.adins.mss.foundation.db.dataaccess;

import android.content.Context;

import com.adins.mss.dao.DaoSession;
import com.adins.mss.dao.EmbeddedInfo;
import com.adins.mss.dao.EmbeddedInfoDao;
import com.adins.mss.foundation.db.DaoOpenHelper;

import de.greenrobot.dao.query.QueryBuilder;

public class EmbeddedInfoDataAccess {

    /**
     * use to generate dao session that you can access modelDao
     *
     * @param context --> context from activity
     * @return
     */
    protected static DaoSession getDaoSession(Context context) {
        return DaoOpenHelper.getDaoSession(context);
    }

    /**
     * get taskH dao and you can access the DB
     *
     * @param context
     * @return
     */
    protected static EmbeddedInfoDao getEmbeddedInfoDao(Context context) {
        return getDaoSession(context).getEmbeddedInfoDao();
    }

    /**
     * Clear session, close db and set dao<PERSON>penHelper to null
     */
    public static void closeAll() {
        DaoOpenHelper.closeAll();
    }

    /**
     * This method is used to retrieve one object of embedded info by userId
     *
     * @param context
     * @return
     */
    public static EmbeddedInfo getOneTaskHeader(Context context) {
        QueryBuilder<EmbeddedInfo> qb = getEmbeddedInfoDao(context).queryBuilder();
        qb.build().forCurrentThread();

        if (qb.list().isEmpty()) {
            return null;
        }
        return qb.list().get(0);
    }

}
