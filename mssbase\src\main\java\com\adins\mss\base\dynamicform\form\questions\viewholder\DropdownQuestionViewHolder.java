package com.adins.mss.base.dynamicform.form.questions.viewholder;

import android.content.Context;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.Spinner;
import android.widget.TextView;

import com.adins.mss.base.R;
import com.adins.mss.base.commons.Query;
import com.adins.mss.base.dynamicform.Constant;
import com.adins.mss.base.dynamicform.form.FragmentQuestion;
import com.adins.mss.constant.Global;
import com.adins.mss.foundation.db.dataaccess.POAssetDataAccess;
import com.adins.mss.foundation.db.dataaccess.PODataAccess;
import com.adins.mss.foundation.questiongenerator.OptionAnswerBean;
import com.adins.mss.foundation.questiongenerator.QuestionBean;
import com.adins.mss.foundation.questiongenerator.form.QuestionView;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by gigin.ginanjar on 31/08/2016.
 */
public class DropdownQuestionViewHolder extends RecyclerView.ViewHolder implements TextWatcher, AdapterView.OnItemSelectedListener {
    public QuestionView mView;
    public TextView mQuestionLabel;
    public TextView mLookupEmpty;
    public Spinner mSpinnLookup;
    public EditText mDescription;
    public QuestionBean bean;
    public FragmentActivity mActivity;
    public ImageView img;
    protected List<OptionAnswerBean> options;
    private LookupAdapter adapter;

    @Deprecated
    public DropdownQuestionViewHolder(View itemView) {
        super(itemView);
        mView = (QuestionView) itemView.findViewById(R.id.questionDropdownLayout);
        mQuestionLabel = (TextView) itemView.findViewById(R.id.questionDropdownLabel);
        mLookupEmpty = (TextView) itemView.findViewById(R.id.questionDropdownEmpty);
        mSpinnLookup = (Spinner) itemView.findViewById(R.id.spinnerQuestionList);
        mDescription = (EditText) itemView.findViewById(R.id.questionDropdownDescription);
        img = (ImageView) itemView.findViewById(R.id.img1);
    }

    public DropdownQuestionViewHolder(View itemView, FragmentActivity activity) {
        super(itemView);
        mView = (QuestionView) itemView.findViewById(R.id.questionDropdownLayout);
        mQuestionLabel = (TextView) itemView.findViewById(R.id.questionDropdownLabel);
        mLookupEmpty = (TextView) itemView.findViewById(R.id.questionDropdownEmpty);
        mSpinnLookup = (Spinner) itemView.findViewById(R.id.spinnerQuestionList);
        mDescription = (EditText) itemView.findViewById(R.id.questionDropdownDescription);
        img = (ImageView) itemView.findViewById(R.id.img1);
        mActivity = activity;
    }

    public void bind(final QuestionBean item, int number) {
        bean = item;
        options = bean.getOptionAnswers();
        String answerType = bean.getAnswer_type();
        String qLabel = number + ". " + bean.getQuestion_label();
        mQuestionLabel.setText(qLabel);

        //TODO: If selectedOptionAnswers is null, set top 1 data optionAnswers
        if (bean.getSelectedOptionAnswers().size() == 0) {
            List<OptionAnswerBean> answerSelected = new ArrayList<>();
            answerSelected.add(0, options.get(0));
            bean.setSelectedOptionAnswers(answerSelected);
        }

        // Fix bug: Tenor tidak sesuai
        checkProductConstant(bean);

        if (bean.isReadOnly()) {
            mSpinnLookup.setClickable(false);
            mSpinnLookup.setEnabled(false);
        } else {
            mSpinnLookup.setClickable(true);
            mSpinnLookup.setEnabled(true);
        }
        int nextOptionIndex = -1;
        try {
            if (bean.getSelectedOptionAnswers() == null || bean.getSelectedOptionAnswers().isEmpty()) {
                if (bean.isReadOnly()) {
                    bean.setOptionAnswers(null);
                    options = new ArrayList<>();
                    img.setVisibility(View.GONE);
                } else {
                    img.setVisibility(View.VISIBLE);
                }
            }
            if (bean.isReadOnly())
                img.setVisibility(View.GONE);
            else
                img.setVisibility(View.VISIBLE);

            String optionSelectedIdStr = QuestionBean.getAnswer(bean);
            if (optionSelectedIdStr == null)
                optionSelectedIdStr = bean.getLovCode();
            if (options != null && !options.isEmpty()) {
                for (int i = 0; i < options.size(); i++) {
                    OptionAnswerBean option = options.get(i);
                    if (option.getCode().equalsIgnoreCase(optionSelectedIdStr)) {        //this is the same option (based on id)
                        nextOptionIndex = i;
                        break;
                    }
                }
                mLookupEmpty.setVisibility(View.GONE);
            } else {
                mLookupEmpty.setVisibility(View.VISIBLE);
                if (bean.isReadOnly()) {
                    mLookupEmpty.setText(mActivity.getString(R.string.no_answer_found));
                } else {
                    mLookupEmpty.setText(mActivity.getString(R.string.lookup_not_found));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        adapter = new LookupAdapter(mActivity, R.layout.spinner_style, R.id.text_spin, options);
        adapter.setDropDownViewResource(R.layout.spinner_style);
        mSpinnLookup.setAdapter(adapter);

        if (nextOptionIndex >= 0 && nextOptionIndex < options.size()) {
            mSpinnLookup.setSelection(nextOptionIndex);
        }

        mSpinnLookup.setOnItemSelectedListener(this);

        if (Global.AT_DROPDOWN_W_DESCRIPTION.equals(answerType)) {
            enableDescription(true);
            mDescription.addTextChangedListener(this);
        } else {
            enableDescription(false);
        }

        selectSavedOptionsFromBeans(bean.getSelectedOptionAnswers());

        if (bean.getAnswer_type().equalsIgnoreCase(Global.AT_LOOKUP_TABLE)) {
            if (options != null && !options.isEmpty()) {
                for (int i = 0; i < options.size(); i++) {
                    if(options.get(i).getCode().equalsIgnoreCase(bean.getAnswer())){
                        mSpinnLookup.setSelection(i);
                    }
                }
            }
        }

        mSpinnLookup.setFocusableInTouchMode(true);
    }

    //TODO Update Here for newAT
    private void setSelectedOptionAnswer(OptionAnswerBean option) {
        List<OptionAnswerBean> selectedOptionAnswers = new ArrayList<OptionAnswerBean>();
        if (option != null) {
            option.setSelected(true);
            selectedOptionAnswers.add(option);
            bean.setLovCode(option.getCode());
            bean.setLookupId(option.getUuid_lookup());
            //Nendi: 20/01/2018
            if (bean.getAnswer_type().equalsIgnoreCase(Global.AT_LOOKUP_TABLE)){
                bean.setAnswer(option.getValue());
                bean.setLookupId(option.getOption_id());

                //Nendi: 20/02/2018 | Add Selected Product Offering
                Query query = FragmentQuestion.finalQuery(bean.getChoice_filter()); //2018-10-19 | Add Filter Exclude Prod Cat Name
                if (query.getTable().equalsIgnoreCase("MS_PO") && !query.getFields()[0].equals("PROD_CAT_NAME") && !option.getCode().equalsIgnoreCase("$$$")) {
                    //TODO: Move to Background Process
                    Constant.productOff = PODataAccess.getOne(mActivity, Integer.parseInt(option.getOption_id()));
                    Constant.poAsset    = POAssetDataAccess.find(mActivity, query);
                    bean.setRelevanted(true);
                }
            }
        }else{
            bean.setLovCode(null);
            bean.setLookupId(null);
        }

        bean.setSelectedOptionAnswers(selectedOptionAnswers);
    }

    private void checkProductConstant(QuestionBean bean) {
        try {
            if (null != bean.getChoice_filter() && !bean.getChoice_filter().isEmpty() && null != bean.getTag()
                    && !bean.getTag().isEmpty() && bean.getTag().equalsIgnoreCase("PRODUCT")
                    && !bean.getSelectedOptionAnswers().isEmpty() && null != bean.getSelectedOptionAnswers()) {
                Query query = FragmentQuestion.finalQuery(bean.getChoice_filter());
                if (query.getTable().equalsIgnoreCase("MS_PO")) {
                    Constant.productOff = PODataAccess.getOne(mActivity, Integer.parseInt(bean.getSelectedOptionAnswers().get(1).getOption_id()));
                }
            }
        } catch (Exception e){
            Log.d("Options", e + "Options: " + options.toString());
        }
    }

    private void selectSavedOptionsFromBeans(List<OptionAnswerBean> beans) {

        if (beans != null) {
            for (OptionAnswerBean optAnsBean : beans) {
                String lovCode = optAnsBean.getCode();
                String description = null;
                if (Global.AT_DROPDOWN_W_DESCRIPTION.equals(bean.getAnswer_type()) ||
                        Global.AT_RADIO_W_DESCRIPTION.equals(bean.getAnswer_type()) ||
                        Global.AT_MULTIPLE_W_DESCRIPTION.equals(bean.getAnswer_type())) {
                    description = bean.getAnswer();
                }
                selectOption(lovCode, description);
            }
        }
    }

    public void saveSelectedOptionToBean() {
        OptionAnswerBean selected = (OptionAnswerBean) mSpinnLookup.getSelectedItem();
        if (mDescription.getVisibility() == View.VISIBLE && !mDescription.getText().toString().trim().isEmpty()) {
            bean.setAnswer(mDescription.getText().toString().trim());
        } else {
            bean.setAnswer("");
        }

        setSelectedOptionAnswer(selected);
    }

    private void selectOption(String lovCode, String desc) {
        int indexOfOption = -1;
        int i = 0;
        for (OptionAnswerBean optAnsBean : options) {
            if (lovCode.equals(optAnsBean.getCode())) {
                indexOfOption = i;
                break;
            }
            i++;
        }

        if (indexOfOption > -1) {
            if (mSpinnLookup != null) {
                mSpinnLookup.setSelection(i);
            }

            if (desc != null) {
                enableDescription(true);
                mDescription.setText(bean.getAnswer());
            } else {
                enableDescription(false);
            }
        }
    }

    public void enableDescription(boolean enable) {
        if (enable) {
            InputFilter[] inputFilters = {new InputFilter.LengthFilter(
                    Global.DEFAULT_MAX_LENGTH)};
            mDescription.setFilters(inputFilters);
            mDescription.setVisibility(View.VISIBLE);
        } else {
            mDescription.setVisibility(View.GONE);
        }
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {
        if (!bean.isReadOnly())
            saveSelectedOptionToBean();
    }

    @Override
    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
        if (parent != null && view.getId() != 0) {
            //do your code here to avoid callback twice
            if (!bean.isReadOnly() || (Global.AT_LOOKUP_TABLE.equals(bean.getAnswer_type()) && bean.isReadOnly())) {
                List<OptionAnswerBean> tempSelectedItems = bean.getSelectedOptionAnswers(); //oldValues
                OptionAnswerBean newSelectedItem = (OptionAnswerBean) mSpinnLookup.getSelectedItem(); //newValue

                if(tempSelectedItems!=null && !tempSelectedItems.isEmpty()) {
                    //TODO Add Filter here for newAT | UPDATED
                    if (tempSelectedItems.get(0).getUuid_lookup() != null) {
                        if (!tempSelectedItems.get(0).getUuid_lookup().equals(newSelectedItem.getUuid_lookup())) {
                            mView.setChanged(true);
                            bean.setIsCanChange(true);
                            bean.setChange(true);
                        } else {
                            bean.setChange(false);
                        }
                    } else {
                        //NENDI: 19 01 2017 Add for newAT
                        if (!tempSelectedItems.get(0).getCode().equals(newSelectedItem.getCode())) {
                            mView.setChanged(true);
                            bean.setIsCanChange(true);
                            bean.setChange(true);
                        } else {
                            bean.setChange(false);
                        }
                    }
                } else {
                    bean.setChange(false);
                }

                saveSelectedOptionToBean();
            } else {
                bean.setChange(false);
            }
        }
    }

    @Override
    public void onNothingSelected(AdapterView<?> parent) {

    }

    public class LookupAdapter extends ArrayAdapter<OptionAnswerBean> {
        private Context context;
        private List<OptionAnswerBean> values;

        public LookupAdapter(Context context, int resource, int textViewResourceId, List<OptionAnswerBean> objects) {
            super(context, resource, textViewResourceId, objects);
            this.context = context;
            this.values = objects;
        }

        public int getCount() {
            return values.size();
        }

        public OptionAnswerBean getItem(int position) {
            return values.get(position);
        }

        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            LayoutInflater inflater = mActivity.getLayoutInflater();
            View view = inflater.inflate(R.layout.spinner_style, parent, false);
            TextView label = (TextView) view.findViewById(R.id.text_spin);
            label.setText(values.get(position).getValue());
            return label;
        }
    }
}
