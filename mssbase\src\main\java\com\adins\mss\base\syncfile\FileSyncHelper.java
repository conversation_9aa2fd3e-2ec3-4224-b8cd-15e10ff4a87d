package com.adins.mss.base.syncfile;

import android.app.ProgressDialog;
import android.content.Context;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Message;
import android.widget.Toast;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.login.DefaultLoginModel;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.mobiledatafile;
import com.adins.mss.foundation.http.AuditDataType;
import com.adins.mss.foundation.http.AuditDataTypeGenerator;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;

import java.util.Date;
import java.util.List;

/**
 * Created by loise on 10/19/2017.
 */

/**
 * Class untuk menampung context, counter ,data , dan method untuk kese<PERSON><PERSON>han proses import external db
 * mulai dari get list file, download, decrypt, extract, dan insert.
 */
public class FileSyncHelper {
    public static List<mobiledatafile> data;
    public static List<mobiledatafile> activeData;
    public static int currentidx = 0;
    public static mobiledatafile metadata;
    public static Context instance;
    //untuk menentukan menggunakan handler yang mana
    //0= login activity, 1= main activity
    public static int senderID = -1;
    static String link, savePath, message;
    static ProgressDialog pDialog;

    /**
     * Untuk memulai proses panggil method ini
     *
     * @param context
     */
    public static void startFileSync(Context context) {
        instance = context;
        getDownloadList();
    }

    /**
     * method untuk melakukan request ke WS untuk list file yang butuh didownload
     */

    private static void getDownloadList() {

        new AsyncTask<Void, Void, Void>() {
            boolean success;

            @Override
            protected void onPreExecute() {
                super.onPreExecute();
                pDialog = new ProgressDialog(instance);
                pDialog.setMessage(instance.getResources().getString(R.string.getting_file_list));
                success = true;
                pDialog.setIndeterminate(true);
                pDialog.setProgressStyle(ProgressDialog.STYLE_SPINNER);
                pDialog.setCancelable(false);
                pDialog.show();
            }

            @Override
            protected Void doInBackground(Void... params) {
                AuditDataType audit = AuditDataTypeGenerator.generateActiveUserAuditData();
                //GlobalData.getSharedGlobalData().loadFromProperties(instance);
                GlobalData.getSharedGlobalData().setAuditData(audit);
                SyncFileRequest request = new SyncFileRequest();
                request.setAudit(audit);
                request.setImei(audit.getDeviceId());
                Date maxdtm = MobileDataFileDataAccess.getMaxTimestamp(instance);
                request.setDtm_upd(maxdtm);
                String json = GsonHelper.toJson(request);
                String url = GlobalData.getSharedGlobalData().getUrlSyncFiles();
                boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                HttpCryptedConnection httpConn = new HttpCryptedConnection(instance, encrypt, decrypt);
                HttpConnectionResult serverResult = null;
                try {
                    serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
                    JsonResponseSyncFile response = GsonHelper.fromJson(serverResult.getResult(), JsonResponseSyncFile.class);
                    data = response.getListMobileDataFile();
                } catch (Exception e) {
                    FireCrash.log(e);
                    success = false;
                    e.printStackTrace();
                }
                return null;
            }

            @Override
            protected void onPostExecute(Void aVoid) {
                super.onPostExecute(aVoid);
                try {
                    if (pDialog.isShowing()) {
                        pDialog.dismiss();
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                }
                //reset index counter
                currentidx = -1;

                //jika list tidak kosong maka masuk proses download file
                if (success) {
                    if (data == null || data.isEmpty()) {
                        Toast.makeText(instance, instance.getResources().getString(R.string.no_file_to_download), Toast.LENGTH_SHORT).show();
                        senderID = 2;
                        synchronizeCallback();
                    } else downloadFiles();
                } else {
                    Toast.makeText(instance, instance.getResources().getString(R.string.failed_to_get_list), Toast.LENGTH_LONG).show();
                }
            }
        }.execute();
    }

    /**
     * method untuk proses download file
     */
    public static void downloadFiles() {
        currentidx++;
        int i = currentidx;
        metadata = data.get(i);
        message = instance.getResources().getString(R.string.download_file_to, i + 1, data.size());
        link = data.get(i).getFile_url();
        savePath = GlobalData.getSharedGlobalData().getSavePath();
        if (link == null || link.isEmpty()) {
            link = data.get(i).getAlternate_file_url();
            if (link == null || link.isEmpty()) {
                //popup message bila link kosong
                Toast.makeText(instance, instance.getResources().getString(R.string.no_links_found, data.get(i).getId_datafile()), Toast.LENGTH_SHORT).show();
            }
        } else {
            //memanggil asynctask filedownloader
            DownloadParams parameters = new DownloadParams(savePath, instance, message, metadata);
            FileDownloader downloader = new FileDownloader();
            downloader.execute(parameters);
        }
    }

    /**
     * method untuk memanggil proses import file
     */
    public static void importFiles() {
        currentidx++;
        int i = currentidx;
        metadata = activeData.get(i);
        message = instance.getResources().getString(R.string.import_file_to, i + 1, activeData.size());
        ImportDbParams importParams = new ImportDbParams(instance, message, metadata);
        new ImportDbFromCsv(instance).execute(importParams);
    }

    public static void synchronizeCallback() {
        Message msg = new Message();
        Bundle bundle = new Bundle();
        bundle.putBoolean("importSuccess", true);
        msg.setData(bundle);
        try {
            switch (senderID) {
                case 0:
                    DefaultLoginModel.importSuccess.sendMessage(msg);
                    break;
                case 1:
                    DefaultLoginModel.importSuccess.sendMessage(msg);
                    break;
                case 2:
                    DefaultLoginModel.importSuccess.sendMessage(msg);
                    break;
                default:
                    break; //handle kalau belum dikirim idnya
            }
        } catch (Exception e) {
            //for Fatal Exception: java.lang.NullPointerException Attempt to invoke virtual method 'boolean com.adins.mss.base.login.DefaultLoginModel$onImportSuccess.sendMessage(android.os.Message)' on a null object reference
        }

    }

}
