package com.adins.mss.base.about.activity;

import android.app.ProgressDialog;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager.NameNotFoundException;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.Toast;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.about.AboutArrayAdapter;
import com.adins.mss.base.about.activity.JsonVersionResponse.ListValue;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.util.FtpHelper;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.base.util.UserSession;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.EmbeddedInfo;
import com.adins.mss.foundation.db.dataaccess.EmbeddedInfoDataAccess;
import com.adins.mss.foundation.db.dataaccess.GeneralParameterDataAccess;
import com.adins.mss.foundation.dialog.NiftyDialogBuilder;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.http.MssRequestType;
import com.adins.mss.foundation.security.storepreferences.ObscuredSharedPreferences;
import com.androidquery.AQuery;
import com.github.jjobes.slidedatetimepicker.SlidingTabLayout;
import com.google.gson.JsonSyntaxException;
import com.mikepenz.aboutlibraries.Libs;
import com.mikepenz.aboutlibraries.entity.Library;

import org.acra.ACRA;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class AboutActivity extends Fragment {
    public static final int REQ_GET_CONTENT = 100;

    public static boolean backEnabled = true;
    private static ArrayList<Library> library;
    private static int flag;
    public Libs libs;
    AQuery query;
    private SlidingTabLayout mSlidingTabLayout;
    private ViewPager mViewPager;
    private View view;
    private Context mContext;

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        mContext = AboutInfoTab.mContext;
    }

    /**
     * @param changeLog
     * @param flag
     */
    public static void setChangeLog(ArrayList<Library> changeLog, int flag) {
        library = new ArrayList<Library>();
        library = changeLog;
        AboutActivity.flag = flag;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        //To can't screenshoot
        if (Tool.isProdFlavour(Global.FLAVORS)) {
            getActivity().getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
        }



        if (view != null) {
            ViewGroup parent = (ViewGroup) view.getParent();
            if (parent != null)
                parent.removeView(view);
        }
        try {
            view = inflater.inflate(R.layout.about_layout, container, false);
        } catch (Exception e) {
            FireCrash.log(e);
        }
        return view;
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        ACRA.getErrorReporter().putCustomData("LAST_CLASS_ACCESSED", getClass().getSimpleName());
        //View view = inflater.inflate(R.layout.about_layout, container, false);
        query = new AQuery(getActivity());
        libs = new Libs(getActivity(), Libs.toStringArray(R.string.class.getFields()));
        String appDescription = "";
        String appName = "";
        Button btnUpdate = (Button) view.findViewById(R.id.btnCheckUpdate);
        Button btnImport = (Button) view.findViewById(R.id.btnImport);


        btnImport.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent chooseFile = new Intent(Intent.ACTION_GET_CONTENT);
                chooseFile.setType("file/*");
                startActivityForResult(chooseFile, REQ_GET_CONTENT);
            }
        });

        btnUpdate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                cekVersion();
            }
        });
        //for Mobile Order
        if (flag == 0) {
            appDescription = libs.getInternLibraries().get(0).getLibraryDescription();
            appName = libs.getInternLibraries().get(0).getLibraryName();
        } else if (flag == 1) {
            appDescription = libs.getInternLibraries().get(1).getLibraryDescription();
            appName = libs.getInternLibraries().get(1).getLibraryName();
        } else if (flag == 2) {
            appDescription = libs.getInternLibraries().get(2).getLibraryDescription();
            appName = libs.getInternLibraries().get(2).getLibraryName();
        }

        query.id(R.id.appDescription).text(appDescription);
        PackageInfo pInfo;
        try {
            pInfo = getActivity().getPackageManager().getPackageInfo(getActivity().getPackageName(), 0);
            appName = pInfo.versionName;
            Global.APP_VERSION = pInfo.versionName;
            Global.BUILD_VERSION = pInfo.versionCode;
        } catch (NameNotFoundException e) {
            FireCrash.log(e);
            if (Global.IS_DEV)
                e.printStackTrace();
        }
        try {
            appName = library.get(0).getLibraryName() + " v." + Global.APP_VERSION;
        } catch (Exception e) {
            FireCrash.log(e);
            try {
                pInfo = getActivity().getPackageManager().getPackageInfo(getActivity().getPackageName(), 0);
                appName = pInfo.versionName;
                Global.APP_VERSION = pInfo.versionName;
            } catch (NameNotFoundException e2) {
                FireCrash.log(e2);
                if (Global.IS_DEV)
                    e2.printStackTrace();
            }
        }
        query.id(R.id.txt_appName).text(appName);

        AboutArrayAdapter adapter = new AboutArrayAdapter(getActivity(), library);
        query.id(android.R.id.list).adapter(adapter);

        EmbeddedInfo embeddedInfo = EmbeddedInfoDataAccess.getOneTaskHeader(getActivity());
        String embedInfo = "Database Embed Info : ";
        String infoEmbed;
        if (null != embeddedInfo) {
            infoEmbed = embedInfo + embeddedInfo.getEmbedded_info();
        } else {
            infoEmbed = embedInfo + "-";
        }
        query.id(R.id.app_embedded_info).text(infoEmbed);
    }

    public void checkForUpdate(View view) {
        cekVersion();
    }

    public void cekVersion() {
        new AsyncTask<Void, Void, JsonVersionResponse>() {
            String taskId = null;
            private ProgressDialog progressDialog;
            private String errMessage = null;

            @Override
            protected void onPreExecute() {
                progressDialog = ProgressDialog.show(getActivity(), "", getString(R.string.progressWait), true);
            }

            @Override
            protected JsonVersionResponse doInBackground(Void... params) {
                // TODO Auto-generated method stub
                JsonVersionResponse result = null;
                if (Tool.isInternetconnected(getActivity())) {
                    MssRequestType request = new MssRequestType();
                    request.setAudit(GlobalData.getSharedGlobalData().getAuditData());

                    String json = GsonHelper.toJson(request);
                    String url = GlobalData.getSharedGlobalData().getURL_CHECK_UPDATE();
                    boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                    boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                    HttpCryptedConnection httpConn = new HttpCryptedConnection(getActivity(), encrypt, decrypt);
                    HttpConnectionResult serverResult = null;

                    try {
                        serverResult = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        if (Global.IS_DEV)
                            e.printStackTrace();
                        try {
                            progressDialog.dismiss();
                        } catch (Exception e1) {
                            e1.printStackTrace();
                        }
                        errMessage = e.getMessage();
                    }

                    try {
                        if (serverResult.isOK()) {
                            String resultString = serverResult.getResult();
                            JsonVersionResponse response = GsonHelper.fromJson(resultString, JsonVersionResponse.class);
                            if (response.getStatus().getCode() == 0) {
                                result = response;
                            } else {
                                errMessage = resultString;
                            }
                        } else {
                            errMessage = serverResult.getResult();
                        }
                    } catch (JsonSyntaxException e) {
                        e.printStackTrace();
                    } catch (Exception e) {
                        FireCrash.log(e);
                        // TODO: handle exception
                    }
                } else {
                    errMessage = getString(R.string.no_internet_connection);

                }
                return result;
            }

            @Override
            protected void onPostExecute(JsonVersionResponse result) {
                if (progressDialog.isShowing()) {
                    try {
                        progressDialog.dismiss();
                    } catch (Exception e) {
                        FireCrash.log(e);
                    }
                }
                if (errMessage != null && errMessage.length() > 0) {
                    Toast.makeText(getActivity(), errMessage, Toast.LENGTH_SHORT).show();
                } else {
                    if (result != null && result.getListValues() != null) {
                        boolean softUpdate = false;
                        boolean forceUpdate = false;
                        String listVersion = "";
                        String otaLink = "";
                        for (ListValue kv : result.getListValues()) {
                            if (kv.getKey().equals(Global.GS_BUILD_NUMBER)) {
                                listVersion = kv.getValue();
                            } else if (kv.getKey().equals(Global.GS_URL_DOWNLOAD)) {
                                otaLink = kv.getValue();
                            }
                        }

                        int serverVersion = 0;
                        try {
                            serverVersion = Integer.valueOf(listVersion);
                        } catch (NumberFormatException nfe) {
                            nfe.printStackTrace();
                        }

                        String[] versions = Tool.split(listVersion, Global.DELIMETER_DATA);
                        String thisVersion = Tool.split(Global.APP_VERSION, "-")[0];
                        String lastVersionFromServer = null;
                        for (int i = 0; i < versions.length; i++) {
                            lastVersionFromServer = versions[i];
                            if (thisVersion.equals(lastVersionFromServer)) {
                                softUpdate = true;
                            }
                        }

                        if (!softUpdate) {
                            forceUpdate = true;
                        } else {
                            if (thisVersion.equals(lastVersionFromServer))
                                softUpdate = false;
                        }

                        String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();

                        Boolean loginEnabled = GeneralParameterDataAccess.getOne(getActivity(), uuidUser, Global.GS_VERS_LOGIN).getGs_value().equals("1");
                        int versionLocal = 0;
                        try {
                            versionLocal = getActivity().getPackageManager().getPackageInfo(getActivity().getPackageName(), 0).versionCode;
                        } catch (NameNotFoundException e) {
                            FireCrash.log(e);
                            if (Global.IS_DEV)
                                e.printStackTrace();
                        }

                        if (forceUpdate) {
                            showAskForceUpdateDialog(otaLink);
                            return;
                        } else if (softUpdate) {
                            showAskForUpdateDialog(otaLink);
                            return;
                        } else {
                            Toast.makeText(getActivity(), getString(R.string.msgNoNewVersion), Toast.LENGTH_SHORT).show();
                        }

                        if (serverVersion > versionLocal) {
                            if (loginEnabled) {
                                showAskForUpdateDialog(otaLink);
                            } else {
                                //showAskForUpdateDialog(otaLink);
                                showAskForceUpdateDialog(otaLink);
                            }
                        } else {
                            Toast.makeText(getActivity(), getString(R.string.msgNoNewVersion), Toast.LENGTH_SHORT).show();
                        }
                    }
                }
            }
        }.execute();
    }

    private void showAskForceUpdateDialog(final String otaLink) {
        // TODO Auto-generated method stub

        final NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(getActivity());
        builder.withTitle("Server")
                .withMessage(getString(R.string.critical_update))
                .withButton1Text(getString(R.string.update))
                .setButton1Click(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
//            	builder.dismiss();
                        openUpdate(otaLink, true);
                    }
                });
        builder.isCancelable(false);
        builder.show();
        //backEnabled=false;
    }

    private void showAskForUpdateDialog(final String otaLink) {
        final NiftyDialogBuilder builder = NiftyDialogBuilder.getInstance(getActivity());
        builder.withTitle("Server")
                .withMessage(getString(R.string.update_available))
                .withButton1Text(getString(R.string.later))
                .setButton1Click(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        builder.dismiss();
                        //goToSynchronize();
                        //exit();
                        //dismissDialog(v.getId());
                    }
                })
                .withButton2Text(getString(R.string.update))
                .setButton2Click(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        builder.dismiss();
                        openUpdate(otaLink, false);

                    }
                }).show();
    }

    protected void openUpdate(final String otaLink, boolean exitApp) {
        if (Pattern.compile("^ftp").matcher(otaLink).find()) {
            new AsyncTask<Void, Integer, String>() {
                ProgressDialog dialog;

                @Override
                protected String doInBackground(Void... voids) {
                    String url = StringUtils.remove(otaLink, "ftp://");
                    String urlPath = url;
                    String user = StringUtils.EMPTY;
                    String password = StringUtils.EMPTY;

                    if (StringUtils.contains(url, "@")) {
                        String userPath[] = StringUtils.split(url, "@");
                        user = StringUtils.substringBefore(userPath[0], ":");
                        password = StringUtils.substringAfter(userPath[0], ":");
                        urlPath = userPath[1];
                    }

                    String urlHost = StringUtils.substringBefore(urlPath, "/");
                    String host = StringUtils.EMPTY;
                    if (StringUtils.contains(urlHost, ":")) {
                        host = StringUtils.substringBefore(urlHost, ":");
                    } else {
                        host = urlHost;
                    }

                    String path = StringUtils.substringAfter(urlPath, "/");
                    //**********************************/MSSSVY-*******-27-product-release.apk

                    FtpHelper.download(host, user, password, path, 21, new FtpHelper.OnProgress() {
                        @Override
                        public void onUpdate(Integer progress) {
                            publishProgress(progress);
                        }
                    });
                    return path;
                }

                @Override
                protected void onPreExecute() {
                    super.onPreExecute();

                    dialog = new ProgressDialog(getContext());
                    dialog.setMessage("Downloading Update...");
                    dialog.setCancelable(false);
                    dialog.setIndeterminate(false);
                    dialog.setProgress(0);
                    dialog.setMax(100);
                    dialog.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
                    dialog.show();
                }

                @Override
                protected void onProgressUpdate(Integer... values) {
                    super.onProgressUpdate(values);
                    dialog.setProgress(values[0]);
                }

                @Override
                protected void onPostExecute(String s) {
                    super.onPostExecute(s);

                    dialog.dismiss();
                    Toast.makeText(getContext(), "Downloading complete...", Toast.LENGTH_SHORT).show();
                }
            }.execute();

//            new Thread(new Runnable() {
//                @Override
//                public void run() {
//                    String finalUrl = otaLink.replace("ftp://","");
////                    String params[] = Tool.explode(finalUrl, "/");
////
////                    String userinfo = params[0];
////                    String[] s_data = Tool.explode(userinfo, "@");
////                    String userdata = s_data[0];
////                    String[] userpas= Tool.explode(userdata, ":");
////                    String username = userpas[0];
////                    String password = userpas[1];
////                    String hostname = s_data[1];
////                    String filename = params[1];
//                    String url = StringUtils.remove(otaLink, "ftp://");
//                    String urlPath = url;
//                    String user = StringUtils.EMPTY;
//                    String password = StringUtils.EMPTY;
//
//                    if (StringUtils.contains(url, "@")) {
//                        String userPath[] = StringUtils.split(url, "@");
//                        user = StringUtils.substringBefore(userPath[0], ":");
//                        password = StringUtils.substringAfter(userPath[0], ":");
//                        urlPath = userPath[1];
//                    }
//
//                    String urlHost = StringUtils.substringBefore(urlPath, "/");
//                    String host = StringUtils.EMPTY;
//                    String port = "21";
//                    if (StringUtils.contains(urlHost, ":")) {
//                        host = StringUtils.substringBefore(urlHost, ":");
//                        port = StringUtils.substringAfter(urlHost, ":");
//                    } else {
//                        host = urlHost;
//                    }
//
//                    String path = StringUtils.substringAfter(urlPath, "/");
//                    //**********************************/MSSSVY-*******-27-product-release.apk
//
//                    FtpHelper.download(host, user, password, path, Integer.parseInt(port));
//                }
//            }).start();
        } else {

            Intent downloadIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(otaLink));
            startActivity(downloadIntent);

            // @ Aditya Purwa
            // User may reinstall the application, closing the app may be the correct action for now.
        }

        if (exitApp) {
            exit();
        }
    }

    protected void exit() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            getActivity().finishAffinity();
        } else {
            getActivity().finish();
        }
        UserSession.clear();
        ObscuredSharedPreferences sharedPref = ObscuredSharedPreferences.getPrefs(getActivity(),
                "GlobalData", Context.MODE_PRIVATE);

        ObscuredSharedPreferences.Editor sharedPrefEditor = sharedPref.edit();
        sharedPrefEditor.remove("HAS_LOGGED");
        sharedPrefEditor.commit();
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                System.exit(0);
            }
        }, 1000);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        switch (requestCode) {
            case REQ_GET_CONTENT:
                if (data.getData() != null) {
                    String pathData = data.getData().getPath();
                    Toast.makeText(getContext(), pathData, Toast.LENGTH_LONG)
                            .show();
                }
                break;
        }
    }
}
