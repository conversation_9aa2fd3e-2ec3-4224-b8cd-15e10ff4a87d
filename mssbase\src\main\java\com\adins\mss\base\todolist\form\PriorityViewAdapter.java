package com.adins.mss.base.todolist.form;

import android.content.Context;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.Scheme;
import com.adins.mss.dao.TaskH;
import com.adins.mss.foundation.db.dataaccess.GeneralParameterDataAccess;
import com.adins.mss.foundation.db.dataaccess.SchemeDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskHDataAccess;
import com.adins.mss.foundation.formatter.Formatter;
import com.adins.mss.foundation.formatter.Tool;

import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * {@link RecyclerView.Adapter} that can display a {@link TaskH} and makes a call to the
 * specified {@link OnTaskListClickListener}.
 * TODO: Replace the implementation with code for your data type.
 */
public class PriorityViewAdapter extends RecyclerView.Adapter<PriorityViewAdapter.ViewHolder> {

    public static final String PRIORITY_HIGH = "HIGH";
    public static final String PRIORITY_REMINDER = "REMINDER";
    public static final String PRIORITY_MEDIUM = "MEDIUM";
    public static final String PRIORITY_NORMAL = "NORMAL";
    public static final String PRIORITY_LOW = "LOW";
    private final List<TaskH> mValues;
    private final OnTaskListClickListener mListener;
    private final Context mContext;
    private static String param;

    public PriorityViewAdapter(Context context, List<TaskH> items, OnTaskListClickListener listener, String param) {
        mContext=context;
        if(!items.isEmpty()){
            try {
                Collections.sort(items, new Comparator<TaskH>() {
                    @Override
                    public int compare(TaskH t1, TaskH t2) {
                        Integer priority1 = 0;
                        if(t1.getPriority().equalsIgnoreCase(PRIORITY_LOW)){
                            priority1 = 1;
                        } else if(t1.getPriority().equalsIgnoreCase(PRIORITY_NORMAL)){
                            priority1 = 2;
                        } else if(t1.getPriority().equalsIgnoreCase(PRIORITY_MEDIUM)){
                            priority1 = 3;
                        } else if(t1.getPriority().equalsIgnoreCase(PRIORITY_HIGH)){
                            priority1 = 4;
                        }
                        Integer priority2 = 0;
                        if(t2.getPriority().equalsIgnoreCase(PRIORITY_LOW)){
                            priority2 = 1;
                        } else if(t2.getPriority().equalsIgnoreCase(PRIORITY_NORMAL)){
                            priority2 = 2;
                        } else if(t2.getPriority().equalsIgnoreCase(PRIORITY_MEDIUM)){
                            priority2 = 3;
                        } else if(t2.getPriority().equalsIgnoreCase(PRIORITY_HIGH)){
                            priority2 = 4;
                        }
                        return priority2.compareTo(priority1);
                    }
                });
            }catch (NullPointerException e){
                e.printStackTrace();
            }
        }

        mValues = items;
        mListener = listener;
        this.param = param;
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = null;
        if(param.equalsIgnoreCase("3")) {
            view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.fragment_priority_item_list, parent, false);
        } else if (param.equalsIgnoreCase("1")) {
            view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.fragment_priority_item, parent, false);
        }

        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(final ViewHolder holder, int position) {
        holder.mItem = mValues.get(position);
        holder.bind(mValues.get(position));
        holder.mView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (null != mListener) {
                    // Notify the active callbacks interface (the activity, if the
                    // fragment is attached to one) that an item has been selected.
                    mListener.onItemClickListener(holder.mItem);
                    mListener.onItemLongClickListener(holder.mItem);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return mValues.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        public static final String TITLE_HIGH_PRIORITY = "High Priority";
        public final View mView;
        public final LinearLayout layout ;
        public final ImageView imgStatus ;
        public final ImageView imgThumb ;
        public final ImageView imgSLE ;
        public final TextView txtpriority ;
        public final TextView txtId ;
        public final TextView txtName ;
        public final TextView txtAppNo ;
        public final TextView txtStatus ;
        public final TextView txtScheme ;
        public final TextView slaTime;
        public final TextView txtPtsDate;
        public final TextView txtKelurahan;
        public final TextView txtStatusFollowUp;
        public final TextView txtIsRevisit;
        public final TextView txtVisitType;
        public final TextView txtPreApproval;
        public TaskH mItem;

        public ViewHolder(View view) {
            super(view);
            mView = view;
            layout = (LinearLayout) view
                    .findViewById(R.id.bgGridPriority);
            imgStatus = (ImageView) view.findViewById(R.id.imgPriority);
            imgThumb = (ImageView) view
                    .findViewById(R.id.imgStsPriority);
            imgSLE = (ImageView) view.findViewById(R.id.ImgSLE);
            txtpriority = (TextView) view.findViewById(R.id.txtPriority);
            txtId = (TextView) view.findViewById(R.id.txtTaskID);
            txtName = (TextView) view.findViewById(R.id.txtName);
            txtAppNo = (TextView) view.findViewById(R.id.txt_AppNo);
            txtStatus = (TextView) view.findViewById(R.id.txtStatusTask);
            txtScheme = (TextView) view.findViewById(R.id.txtScheme);
            slaTime = (TextView) view.findViewById(R.id.txtslatime);
            txtPtsDate = (TextView) view.findViewById(R.id.txtPtsDate);
            txtKelurahan = (TextView) view.findViewById(R.id.txtKelurahan);
            txtStatusFollowUp = (TextView) view.findViewById(R.id.txtStatusFollowUp);
            txtIsRevisit = (TextView) view.findViewById(R.id.txtIsRevisit);
            txtVisitType = (TextView) view.findViewById(R.id.txtVisitType);
            txtPreApproval = view.findViewById(R.id.txtPreApproval);
        }

        public void bind(TaskH taskH){
            mItem=taskH;
            txtId.setText(taskH.getTask_id());
            txtName.setText(taskH.getCustomer_name());
            txtAppNo.setText(taskH.getAppl_no());
            txtStatus.setText(taskH.getStatus());
            txtName.setSelected(true);
            txtAppNo.setSelected(true);

            if(mItem.getIs_revisit() == null || mItem.getIs_revisit().equals("0")){
                txtIsRevisit.setVisibility(View.GONE);
            } else if(mItem.getIs_revisit().equals("1")){
                txtIsRevisit.setVisibility(View.VISIBLE);
                txtIsRevisit.setText("Task Revisit");
            }

            if(mItem.getVisit_type() != null && !"".equalsIgnoreCase(mItem.getVisit_type())) {
                txtVisitType.setVisibility(View.VISIBLE);
                txtVisitType.setText(mItem.getVisit_type().toUpperCase());
            } else {
                txtVisitType.setVisibility(View.GONE);
            }

            //Nendi: 2017/10/20 - Add Promise to Survey
            String ptsDateStr = "-";
            if(taskH.getPms_date() != null) {
                ptsDateStr = Formatter.formatDate(taskH.getPms_date(), Global.DATE_TIME_STR_FORMAT);
            }

            txtPtsDate.setText(ptsDateStr);
            Scheme scheme = taskH.getScheme();
            if (scheme == null) {
                scheme = SchemeDataAccess.getOne(mContext,
                        taskH.getUuid_scheme());
            }
            if(scheme!=null)
                txtScheme.setText(scheme.getForm_id());

            //hide kelurahan and status if form is promise to survey/pre survey/guarantor
            if(taskH.getScheme().getScheme_description().equalsIgnoreCase(Global.FORM_NAME_PROMISE_TO_SURVEY) ||
                    taskH.getScheme().getScheme_description().equalsIgnoreCase(Global.FORM_NAME_PRE_SURVEY) ||
                    taskH.getScheme().getScheme_description().equalsIgnoreCase(Global.FORM_NAME_GUARANTOR)){
                txtKelurahan.setVisibility(View.GONE);
                txtStatusFollowUp.setVisibility(View.GONE);
            } else {
                txtKelurahan.setText(taskH.getKelurahan());
                txtStatusFollowUp.setText(taskH.getStatus_followup());
            }

            // TODO note : imgStatus itu thumbnail nya, imgThumb itu statusnya
            String priority = taskH.getPriority();
            if (priority != null) {
                if (PRIORITY_HIGH.equalsIgnoreCase(priority)) {
                    txtpriority.setText("High Priority");
                    layout.setBackgroundResource(R.drawable.highpriority_background);
                    imgStatus.setImageResource(R.drawable.icon_high);
                } else if (PRIORITY_MEDIUM.equalsIgnoreCase(priority)) {
                    txtpriority.setText("Medium Priority");
                    layout.setBackgroundResource(R.drawable.mediumpriority_background);
                    imgStatus.setImageResource(R.drawable.icon_medium);
                } else if (PRIORITY_NORMAL.equalsIgnoreCase(priority)) {
                    txtpriority.setText("Normal Priority");
                    layout.setBackgroundResource(R.drawable.mediumpriority_background);
                    imgStatus.setImageResource(R.drawable.icon_medium);
                } else if (PRIORITY_LOW.equalsIgnoreCase(priority)) {
                    txtpriority.setText("Low Priority");
                    layout.setBackgroundResource(R.drawable.lowpriority_background);
                    imgStatus.setImageResource(R.drawable.icon_low);
                } else if (PRIORITY_REMINDER.equalsIgnoreCase(priority)) {
                    txtpriority.setText("Reminder");
                    layout.setBackgroundResource(R.drawable.highpriority_background);
                    imgStatus.setImageResource(R.drawable.icon_high);
                }
            }

            // Pre Approval Conditional
            String schemeDescription = taskH.getScheme().getScheme_description();
            if ((taskH.getIs_pre_approval() != null && taskH.getIs_pre_approval() == 1) && isGroupPreApproval(schemeDescription)) {
                txtpriority.setText(TITLE_HIGH_PRIORITY);
                layout.setBackgroundResource(R.drawable.highpriority_background);
                imgStatus.setImageResource(R.drawable.icon_high);
                if (schemeDescription.equals(Global.FORM_NAME_VISIT_POLO)) {
                    txtPtsDate.setText(mContext.getString(R.string.pre_approval));
                } else {
                    txtPreApproval.setVisibility(View.VISIBLE);
                }
            }

            boolean isTaskWaiting = false;
            List<TaskH> listTaskNotDownloaded = TaskHDataAccess.getTaskDetailNotDownloaded(mContext, taskH.getAppl_no());
            if (null != listTaskNotDownloaded && !listTaskNotDownloaded.isEmpty()) {
                isTaskWaiting = true;
            }

            if (TaskHDataAccess.STATUS_SEND_DOWNLOAD.equals(taskH.getStatus()) && !isTaskWaiting) {
                imgThumb.setImageResource(R.drawable.ic_downloaded);
            } else {
                imgThumb.setImageResource(R.drawable.ic_undownload);
            }

            int SLA_time = Integer.parseInt(GeneralParameterDataAccess.getOne(
                    mContext,
                    GlobalData.getSharedGlobalData().getUser().getUuid_user(),
                    Global.GS_SLA_TIME).getGs_value());

            String application = GlobalData.getSharedGlobalData()
                    .getAuditData().getApplication();
            if (Global.APPLICATION_COLLECTION.equalsIgnoreCase(application)) {
                imgSLE.setVisibility(View.GONE);
                slaTime.setVisibility(View.GONE);
            }else{
//                imgSLE.setVisibility(View.VISIBLE);
//                slaTime.setVisibility(View.VISIBLE);
                java.util.Date assignDate = taskH.getPms_date();
                java.util.Date dSlaTime;
                if (assignDate != null) {
                    Long assDateMs = assignDate.getTime();

                    java.util.Date now = Tool.getSystemDateTime();
                    Long nowMs = now.getTime();

                    Long SLAMs = SLA_time * (long) Global.HOUR;

                    Long sla_late = assDateMs + SLAMs;

                    dSlaTime = new Date(sla_late);
                    SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
                    String slaDate = sdf.format(dSlaTime);
                    slaTime.setText(slaDate);
                    if (nowMs > sla_late) {
                        imgSLE.setImageResource(R.drawable.light_red);
                        slaTime.setVisibility(View.GONE);
                    } else {
                        imgSLE.setImageResource(R.drawable.ic_downloaded);

                        String strPTSDate = Formatter.formatDate(assignDate, Global.DATE_STR_FORMAT5);
                        String strNOWDate = Formatter.formatDate(now, Global.DATE_STR_FORMAT5);

                        if (strPTSDate.equalsIgnoreCase(strNOWDate)) {
                            slaTime.setVisibility(View.VISIBLE);
                        } else {
                            imgSLE.setImageResource(R.drawable.light_red);
                            slaTime.setVisibility(View.GONE);
                        }
                    }
                }else{
                    slaTime.setVisibility(View.GONE);
                }
            }
        }

        public boolean isGroupPreApproval(String schemeDescription) {
            switch (schemeDescription) {
                case Global.FORM_NAME_VISIT_POLO:
                case Global.FORM_NAME_PRE_SURVEY:
                case Global.FORM_NAME_PROMISE_TO_SURVEY:
                    return true;
            }
            return false;
        }

        @Override
        public String toString() {
            return super.toString() + " '" + mItem.getCustomer_name() + "'";
        }
    }
}