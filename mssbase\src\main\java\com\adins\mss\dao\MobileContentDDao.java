package com.adins.mss.dao;

import java.util.List;
import java.util.ArrayList;
import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.SqlUtils;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;
import de.greenrobot.dao.query.Query;
import de.greenrobot.dao.query.QueryBuilder;

import com.adins.mss.dao.MobileContentD;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TR_MOBILECONTENT_D".
*/
public class MobileContentDDao extends AbstractDao<MobileContentD, String> {

    public static final String TABLENAME = "TR_MOBILECONTENT_D";

    /**
     * Properties of entity MobileContentD.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_mobile_content_d = new Property(0, String.class, "uuid_mobile_content_d", true, "UUID_MOBILE_CONTENT_D");
        public final static Property Menu_id = new Property(1, String.class, "menu_id", false, "MENU_ID");
        public final static Property Content = new Property(2, byte[].class, "content", false, "CONTENT");
        public final static Property Content_type = new Property(3, String.class, "content_type", false, "CONTENT_TYPE");
        public final static Property Sequence = new Property(4, Integer.class, "sequence", false, "SEQUENCE");
        public final static Property Usr_crt = new Property(5, String.class, "usr_crt", false, "USR_CRT");
        public final static Property Dtm_crt = new Property(6, java.util.Date.class, "dtm_crt", false, "DTM_CRT");
        public final static Property Usr_upd = new Property(7, String.class, "usr_upd", false, "USR_UPD");
        public final static Property Uuid_mobile_content_h = new Property(8, String.class, "uuid_mobile_content_h", false, "UUID_MOBILE_CONTENT_H");
        public final static Property Start_date = new Property(9, java.util.Date.class, "start_date", false, "START_DATE");
        public final static Property End_date = new Property(10, java.util.Date.class, "end_date", false, "END_DATE");
    };

    private DaoSession daoSession;

    private Query<MobileContentD> mobileContentH_MobileContentDListQuery;

    public MobileContentDDao(DaoConfig config) {
        super(config);
    }
    
    public MobileContentDDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TR_MOBILECONTENT_D\" (" + //
                "\"UUID_MOBILE_CONTENT_D\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_mobile_content_d
                "\"MENU_ID\" TEXT," + // 1: menu_id
                "\"CONTENT\" BLOB," + // 2: content
                "\"CONTENT_TYPE\" TEXT," + // 3: content_type
                "\"SEQUENCE\" INTEGER," + // 4: sequence
                "\"USR_CRT\" TEXT," + // 5: usr_crt
                "\"DTM_CRT\" INTEGER," + // 6: dtm_crt
                "\"USR_UPD\" TEXT," + // 7: usr_upd
                "\"UUID_MOBILE_CONTENT_H\" TEXT," + // 8: uuid_mobile_content_h
                "\"START_DATE\" INTEGER," + // 9: start_date
                "\"END_DATE\" INTEGER);"); // 10: end_date
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TR_MOBILECONTENT_D\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, MobileContentD entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_mobile_content_d());
 
        String menu_id = entity.getMenu_id();
        if (menu_id != null) {
            stmt.bindString(2, menu_id);
        }
 
        byte[] content = entity.getContent();
        if (content != null) {
            stmt.bindBlob(3, content);
        }
 
        String content_type = entity.getContent_type();
        if (content_type != null) {
            stmt.bindString(4, content_type);
        }
 
        Integer sequence = entity.getSequence();
        if (sequence != null) {
            stmt.bindLong(5, sequence);
        }
 
        String usr_crt = entity.getUsr_crt();
        if (usr_crt != null) {
            stmt.bindString(6, usr_crt);
        }
 
        java.util.Date dtm_crt = entity.getDtm_crt();
        if (dtm_crt != null) {
            stmt.bindLong(7, dtm_crt.getTime());
        }
 
        String usr_upd = entity.getUsr_upd();
        if (usr_upd != null) {
            stmt.bindString(8, usr_upd);
        }
 
        String uuid_mobile_content_h = entity.getUuid_mobile_content_h();
        if (uuid_mobile_content_h != null) {
            stmt.bindString(9, uuid_mobile_content_h);
        }
 
        java.util.Date start_date = entity.getStart_date();
        if (start_date != null) {
            stmt.bindLong(10, start_date.getTime());
        }
 
        java.util.Date end_date = entity.getEnd_date();
        if (end_date != null) {
            stmt.bindLong(11, end_date.getTime());
        }
    }

    @Override
    protected void attachEntity(MobileContentD entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public MobileContentD readEntity(Cursor cursor, int offset) {
        MobileContentD entity = new MobileContentD( //
            cursor.getString(offset + 0), // uuid_mobile_content_d
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // menu_id
            cursor.isNull(offset + 2) ? null : cursor.getBlob(offset + 2), // content
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // content_type
            cursor.isNull(offset + 4) ? null : cursor.getInt(offset + 4), // sequence
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // usr_crt
            cursor.isNull(offset + 6) ? null : new java.util.Date(cursor.getLong(offset + 6)), // dtm_crt
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7), // usr_upd
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8), // uuid_mobile_content_h
            cursor.isNull(offset + 9) ? null : new java.util.Date(cursor.getLong(offset + 9)), // start_date
            cursor.isNull(offset + 10) ? null : new java.util.Date(cursor.getLong(offset + 10)) // end_date
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, MobileContentD entity, int offset) {
        entity.setUuid_mobile_content_d(cursor.getString(offset + 0));
        entity.setMenu_id(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setContent(cursor.isNull(offset + 2) ? null : cursor.getBlob(offset + 2));
        entity.setContent_type(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setSequence(cursor.isNull(offset + 4) ? null : cursor.getInt(offset + 4));
        entity.setUsr_crt(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setDtm_crt(cursor.isNull(offset + 6) ? null : new java.util.Date(cursor.getLong(offset + 6)));
        entity.setUsr_upd(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
        entity.setUuid_mobile_content_h(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
        entity.setStart_date(cursor.isNull(offset + 9) ? null : new java.util.Date(cursor.getLong(offset + 9)));
        entity.setEnd_date(cursor.isNull(offset + 10) ? null : new java.util.Date(cursor.getLong(offset + 10)));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(MobileContentD entity, long rowId) {
        return entity.getUuid_mobile_content_d();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(MobileContentD entity) {
        if(entity != null) {
            return entity.getUuid_mobile_content_d();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
    /** Internal query to resolve the "mobileContentDList" to-many relationship of MobileContentH. */
    public List<MobileContentD> _queryMobileContentH_MobileContentDList(String uuid_mobile_content_h) {
        synchronized (this) {
            if (mobileContentH_MobileContentDListQuery == null) {
                QueryBuilder<MobileContentD> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_mobile_content_h.eq(null));
                mobileContentH_MobileContentDListQuery = queryBuilder.build();
            }
        }
        Query<MobileContentD> query = mobileContentH_MobileContentDListQuery.forCurrentThread();
        query.setParameter(0, uuid_mobile_content_h);
        return query.list();
    }

    private String selectDeep;

    protected String getSelectDeep() {
        if (selectDeep == null) {
            StringBuilder builder = new StringBuilder("SELECT ");
            SqlUtils.appendColumns(builder, "T", getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T0", daoSession.getMobileContentHDao().getAllColumns());
            builder.append(" FROM TR_MOBILECONTENT_D T");
            builder.append(" LEFT JOIN TR_MOBILECONTENT_H T0 ON T.\"UUID_MOBILE_CONTENT_H\"=T0.\"UUID_MOBILE_CONTENT_H\"");
            builder.append(' ');
            selectDeep = builder.toString();
        }
        return selectDeep;
    }
    
    protected MobileContentD loadCurrentDeep(Cursor cursor, boolean lock) {
        MobileContentD entity = loadCurrent(cursor, 0, lock);
        int offset = getAllColumns().length;

        MobileContentH mobileContentH = loadCurrentOther(daoSession.getMobileContentHDao(), cursor, offset);
        entity.setMobileContentH(mobileContentH);

        return entity;    
    }

    public MobileContentD loadDeep(Long key) {
        assertSinglePk();
        if (key == null) {
            return null;
        }

        StringBuilder builder = new StringBuilder(getSelectDeep());
        builder.append("WHERE ");
        SqlUtils.appendColumnsEqValue(builder, "T", getPkColumns());
        String sql = builder.toString();
        
        String[] keyArray = new String[] { key.toString() };
        Cursor cursor = db.rawQuery(sql, keyArray);
        
        try {
            boolean available = cursor.moveToFirst();
            if (!available) {
                return null;
            } else if (!cursor.isLast()) {
                throw new IllegalStateException("Expected unique result, but count was " + cursor.getCount());
            }
            return loadCurrentDeep(cursor, true);
        } finally {
            cursor.close();
        }
    }
    
    /** Reads all available rows from the given cursor and returns a list of new ImageTO objects. */
    public List<MobileContentD> loadAllDeepFromCursor(Cursor cursor) {
        int count = cursor.getCount();
        List<MobileContentD> list = new ArrayList<MobileContentD>(count);
        
        if (cursor.moveToFirst()) {
            if (identityScope != null) {
                identityScope.lock();
                identityScope.reserveRoom(count);
            }
            try {
                do {
                    list.add(loadCurrentDeep(cursor, false));
                } while (cursor.moveToNext());
            } finally {
                if (identityScope != null) {
                    identityScope.unlock();
                }
            }
        }
        return list;
    }
    
    protected List<MobileContentD> loadDeepAllAndCloseCursor(Cursor cursor) {
        try {
            return loadAllDeepFromCursor(cursor);
        } finally {
            cursor.close();
        }
    }
    

    /** A raw-style query where you can pass any WHERE clause and arguments. */
    public List<MobileContentD> queryDeep(String where, String... selectionArg) {
        Cursor cursor = db.rawQuery(getSelectDeep() + where, selectionArg);
        return loadDeepAllAndCloseCursor(cursor);
    }
 
}
