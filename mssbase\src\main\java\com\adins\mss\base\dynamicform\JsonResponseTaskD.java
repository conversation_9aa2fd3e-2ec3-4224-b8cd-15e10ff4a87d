package com.adins.mss.base.dynamicform;

import com.adins.mss.dao.TaskD;
import com.adins.mss.foundation.http.MssResponseType;
import com.google.gson.annotations.SerializedName;

import java.util.List;

public class JsonResponseTaskD extends MssResponseType {
    /**
     * Property listTask
     */
    @SerializedName("listTask")
    List<TaskD> listTask;

    /**
     * Gets the listTask
     */
    public List<TaskD> getListTask() {
        return this.listTask;
    }

    /**
     * Sets the listTask
     */
    public void setListTask(List<TaskD> value) {
        this.listTask = value;
    }

}
