package com.adins.mss.dao;

import java.util.List;
import java.util.ArrayList;
import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.SqlUtils;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;
import de.greenrobot.dao.query.Query;
import de.greenrobot.dao.query.QueryBuilder;

import com.adins.mss.dao.PrintItem;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "MS_PRINTITEM".
*/
public class PrintItemDao extends AbstractDao<PrintItem, String> {

    public static final String TABLENAME = "MS_PRINTITEM";

    /**
     * Properties of entity PrintItem.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_print_item = new Property(0, String.class, "uuid_print_item", true, "UUID_PRINT_ITEM");
        public final static Property Print_type_id = new Property(1, String.class, "print_type_id", false, "PRINT_TYPE_ID");
        public final static Property Print_item_label = new Property(2, String.class, "print_item_label", false, "PRINT_ITEM_LABEL");
        public final static Property Question_group_id = new Property(3, String.class, "question_group_id", false, "QUESTION_GROUP_ID");
        public final static Property Question_id = new Property(4, String.class, "question_id", false, "QUESTION_ID");
        public final static Property Print_item_order = new Property(5, Integer.class, "print_item_order", false, "PRINT_ITEM_ORDER");
        public final static Property Usr_crt = new Property(6, String.class, "usr_crt", false, "USR_CRT");
        public final static Property Dtm_crt = new Property(7, java.util.Date.class, "dtm_crt", false, "DTM_CRT");
        public final static Property Usr_upd = new Property(8, String.class, "usr_upd", false, "USR_UPD");
        public final static Property Dtm_upd = new Property(9, java.util.Date.class, "dtm_upd", false, "DTM_UPD");
        public final static Property Uuid_scheme = new Property(10, String.class, "uuid_scheme", false, "UUID_SCHEME");
    };

    private DaoSession daoSession;

    private Query<PrintItem> scheme_PrintItemListQuery;

    public PrintItemDao(DaoConfig config) {
        super(config);
    }
    
    public PrintItemDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"MS_PRINTITEM\" (" + //
                "\"UUID_PRINT_ITEM\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_print_item
                "\"PRINT_TYPE_ID\" TEXT," + // 1: print_type_id
                "\"PRINT_ITEM_LABEL\" TEXT," + // 2: print_item_label
                "\"QUESTION_GROUP_ID\" TEXT," + // 3: question_group_id
                "\"QUESTION_ID\" TEXT," + // 4: question_id
                "\"PRINT_ITEM_ORDER\" INTEGER," + // 5: print_item_order
                "\"USR_CRT\" TEXT," + // 6: usr_crt
                "\"DTM_CRT\" INTEGER," + // 7: dtm_crt
                "\"USR_UPD\" TEXT," + // 8: usr_upd
                "\"DTM_UPD\" INTEGER," + // 9: dtm_upd
                "\"UUID_SCHEME\" TEXT);"); // 10: uuid_scheme
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"MS_PRINTITEM\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, PrintItem entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_print_item());
 
        String print_type_id = entity.getPrint_type_id();
        if (print_type_id != null) {
            stmt.bindString(2, print_type_id);
        }
 
        String print_item_label = entity.getPrint_item_label();
        if (print_item_label != null) {
            stmt.bindString(3, print_item_label);
        }
 
        String question_group_id = entity.getQuestion_group_id();
        if (question_group_id != null) {
            stmt.bindString(4, question_group_id);
        }
 
        String question_id = entity.getQuestion_id();
        if (question_id != null) {
            stmt.bindString(5, question_id);
        }
 
        Integer print_item_order = entity.getPrint_item_order();
        if (print_item_order != null) {
            stmt.bindLong(6, print_item_order);
        }
 
        String usr_crt = entity.getUsr_crt();
        if (usr_crt != null) {
            stmt.bindString(7, usr_crt);
        }
 
        java.util.Date dtm_crt = entity.getDtm_crt();
        if (dtm_crt != null) {
            stmt.bindLong(8, dtm_crt.getTime());
        }
 
        String usr_upd = entity.getUsr_upd();
        if (usr_upd != null) {
            stmt.bindString(9, usr_upd);
        }
 
        java.util.Date dtm_upd = entity.getDtm_upd();
        if (dtm_upd != null) {
            stmt.bindLong(10, dtm_upd.getTime());
        }
 
        String uuid_scheme = entity.getUuid_scheme();
        if (uuid_scheme != null) {
            stmt.bindString(11, uuid_scheme);
        }
    }

    @Override
    protected void attachEntity(PrintItem entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public PrintItem readEntity(Cursor cursor, int offset) {
        PrintItem entity = new PrintItem( //
            cursor.getString(offset + 0), // uuid_print_item
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // print_type_id
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // print_item_label
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // question_group_id
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // question_id
            cursor.isNull(offset + 5) ? null : cursor.getInt(offset + 5), // print_item_order
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // usr_crt
            cursor.isNull(offset + 7) ? null : new java.util.Date(cursor.getLong(offset + 7)), // dtm_crt
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8), // usr_upd
            cursor.isNull(offset + 9) ? null : new java.util.Date(cursor.getLong(offset + 9)), // dtm_upd
            cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10) // uuid_scheme
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, PrintItem entity, int offset) {
        entity.setUuid_print_item(cursor.getString(offset + 0));
        entity.setPrint_type_id(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setPrint_item_label(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setQuestion_group_id(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setQuestion_id(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setPrint_item_order(cursor.isNull(offset + 5) ? null : cursor.getInt(offset + 5));
        entity.setUsr_crt(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setDtm_crt(cursor.isNull(offset + 7) ? null : new java.util.Date(cursor.getLong(offset + 7)));
        entity.setUsr_upd(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
        entity.setDtm_upd(cursor.isNull(offset + 9) ? null : new java.util.Date(cursor.getLong(offset + 9)));
        entity.setUuid_scheme(cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(PrintItem entity, long rowId) {
        return entity.getUuid_print_item();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(PrintItem entity) {
        if(entity != null) {
            return entity.getUuid_print_item();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
    /** Internal query to resolve the "printItemList" to-many relationship of Scheme. */
    public List<PrintItem> _queryScheme_PrintItemList(String uuid_scheme) {
        synchronized (this) {
            if (scheme_PrintItemListQuery == null) {
                QueryBuilder<PrintItem> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_scheme.eq(null));
                scheme_PrintItemListQuery = queryBuilder.build();
            }
        }
        Query<PrintItem> query = scheme_PrintItemListQuery.forCurrentThread();
        query.setParameter(0, uuid_scheme);
        return query.list();
    }

    private String selectDeep;

    protected String getSelectDeep() {
        if (selectDeep == null) {
            StringBuilder builder = new StringBuilder("SELECT ");
            SqlUtils.appendColumns(builder, "T", getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T0", daoSession.getSchemeDao().getAllColumns());
            builder.append(" FROM MS_PRINTITEM T");
            builder.append(" LEFT JOIN MS_SCHEME T0 ON T.\"UUID_SCHEME\"=T0.\"UUID_SCHEME\"");
            builder.append(' ');
            selectDeep = builder.toString();
        }
        return selectDeep;
    }
    
    protected PrintItem loadCurrentDeep(Cursor cursor, boolean lock) {
        PrintItem entity = loadCurrent(cursor, 0, lock);
        int offset = getAllColumns().length;

        Scheme scheme = loadCurrentOther(daoSession.getSchemeDao(), cursor, offset);
        entity.setScheme(scheme);

        return entity;    
    }

    public PrintItem loadDeep(Long key) {
        assertSinglePk();
        if (key == null) {
            return null;
        }

        StringBuilder builder = new StringBuilder(getSelectDeep());
        builder.append("WHERE ");
        SqlUtils.appendColumnsEqValue(builder, "T", getPkColumns());
        String sql = builder.toString();
        
        String[] keyArray = new String[] { key.toString() };
        Cursor cursor = db.rawQuery(sql, keyArray);
        
        try {
            boolean available = cursor.moveToFirst();
            if (!available) {
                return null;
            } else if (!cursor.isLast()) {
                throw new IllegalStateException("Expected unique result, but count was " + cursor.getCount());
            }
            return loadCurrentDeep(cursor, true);
        } finally {
            cursor.close();
        }
    }
    
    /** Reads all available rows from the given cursor and returns a list of new ImageTO objects. */
    public List<PrintItem> loadAllDeepFromCursor(Cursor cursor) {
        int count = cursor.getCount();
        List<PrintItem> list = new ArrayList<PrintItem>(count);
        
        if (cursor.moveToFirst()) {
            if (identityScope != null) {
                identityScope.lock();
                identityScope.reserveRoom(count);
            }
            try {
                do {
                    list.add(loadCurrentDeep(cursor, false));
                } while (cursor.moveToNext());
            } finally {
                if (identityScope != null) {
                    identityScope.unlock();
                }
            }
        }
        return list;
    }
    
    protected List<PrintItem> loadDeepAllAndCloseCursor(Cursor cursor) {
        try {
            return loadAllDeepFromCursor(cursor);
        } finally {
            cursor.close();
        }
    }
    

    /** A raw-style query where you can pass any WHERE clause and arguments. */
    public List<PrintItem> queryDeep(String where, String... selectionArg) {
        Cursor cursor = db.rawQuery(getSelectDeep() + where, selectionArg);
        return loadDeepAllAndCloseCursor(cursor);
    }
 
}
