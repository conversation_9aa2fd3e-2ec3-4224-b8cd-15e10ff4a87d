package com.adins.mss.base.taskupdate.model.helper;

import android.content.Context;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.taskupdate.model.JsonRequestTaskUpdate;
import com.adins.mss.base.taskupdate.model.JsonResponseTaskUpdate;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.TaskUpdate;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;

import java.util.List;

public class TaskUpdateHelper {

    public static List<TaskUpdate> getTaskUpdateList(Context context) {
        JsonResponseTaskUpdate response = null;
        if (Tool.isInternetconnected(context)) {
            JsonRequestTaskUpdate request = new JsonRequestTaskUpdate();
            request.setAudit(GlobalData.getSharedGlobalData().getAuditData());
            request.addImeiAndroidIdToUnstructured();

            HttpCryptedConnection httpConn = new HttpCryptedConnection(context,
                    GlobalData.getSharedGlobalData().isEncrypt(), GlobalData.getSharedGlobalData().isDecrypt());
            String url = GlobalData.getSharedGlobalData().getURL_RETRIEVE_TASK_UPDATE();
            HttpConnectionResult serverResult;

            try {
                serverResult = httpConn.requestToServer(url, GsonHelper.toJson(request), Global.DEFAULTCONNECTIONTIMEOUT);

                if (serverResult != null && serverResult.isOK()) {
                    try {
                        response = GsonHelper.fromJson(serverResult.getResult(), JsonResponseTaskUpdate.class);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        e.printStackTrace();
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (response != null && response.getTaskUpdateResponse() != null && !response.getTaskUpdateResponse().isEmpty()) {
            return response.getTaskUpdateResponse();
        } else {
            return null;
        }
    }
}
