package com.adins.mss.base.dynamicform;

import com.adins.mss.dao.TaskD;
import com.adins.mss.dao.TaskH;
import com.adins.mss.foundation.http.MssRequestType;
import com.google.gson.annotations.SerializedName;

import java.util.List;

public class JsonRequestVerificationTask extends MssRequestType {
    /**
     * Property taskH
     */
    @SerializedName("taskH")
    TaskH taskH;

    /**
     * Property taskD
     */
    @SerializedName("taskD")
    List<TaskD> taskD;

    /**
     * Property notes
     */
    @SerializedName("notes")
    String notes;

    /**
     * Gets the notes
     */
    public String getNotes() {
        return this.notes;
    }

    /**
     * Sets the notes
     */
    public void setNotes(String value) {
        this.notes = value;
    }

    /**
     * Gets the taskH
     */
    public TaskH getTaskH() {
        return this.taskH;
    }

    /**
     * Sets the taskH
     */
    public void setTaskH(TaskH value) {
        this.taskH = value;
    }

    /**
     * Gets the taskD
     */
    public List<TaskD> getTaskD() {
        return this.taskD;
    }

    /**
     * Sets the taskD
     */
    public void setTaskD(List<TaskD> value) {
        this.taskD = value;
    }
}
