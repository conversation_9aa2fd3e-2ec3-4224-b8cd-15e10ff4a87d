package com.adins.mss.base.dynamicform;

import com.adins.mss.foundation.http.MssRequestType;
import com.google.gson.annotations.SerializedName;

public class JsonRequestTaskD extends MssRequestType {
    /**
     * Property uuid_task_h
     */
    @SerializedName("uuid_task_h")
    String uuid_task_h;
    @SerializedName("uuid_question")
    String uuid_question;

    public String getUuid_question() {
        return uuid_question;
    }

    public void setUuid_question(String uuidQuestion) {
        this.uuid_question = uuidQuestion;
    }

    /**
     * Gets the uuid_task_h
     */
    public String getuuid_task_h() {
        return this.uuid_task_h;
    }

    /**
     * Sets the uuid_task_h
     */
    public void setuuid_task_h(String value) {
        this.uuid_task_h = value;
    }
}
