package com.adins.mss.base.dynamicform.form.models;

import com.adins.mss.dao.TaskD;
import com.google.gson.annotations.SerializedName;

import java.util.List;

public class LayerBean {
    @SerializedName("uuid_question_group")
    String uuidQuestionGroup;
    @SerializedName("taskD")
    List<TaskD> taskDList;
    String seqNo;

    public String getUuidQuestionGroup() {
        return uuidQuestionGroup;
    }

    public void setUuidQuestionGroup(String uuidQuestionGroup) {
        this.uuidQuestionGroup = uuidQuestionGroup;
    }

    public List<TaskD> getTaskDList() {
        return taskDList;
    }

    public void setTaskDList(List<TaskD> taskDList) {
        this.taskDList = taskDList;
    }

    public String getSeqNo() {
        return seqNo;
    }

    public void setSeqNo(String seqNo) {
        this.seqNo = seqNo;
    }
}
