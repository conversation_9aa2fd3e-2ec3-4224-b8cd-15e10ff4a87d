package com.adins.mss.foundation.dialog;

//import com.adins.ms2mcs.sfi.mh.R;
//import com.adins.msm.constant.Global;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.location.LocationManager;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.provider.Settings;
import android.util.Log;
import android.view.View;
import android.widget.Toast;

import androidx.fragment.app.FragmentActivity;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.dynamicform.DynamicFormActivity;
import com.adins.mss.base.mainmenu.MainMenuActivity;
import com.adins.mss.base.util.UserSession;
import com.adins.mss.constant.Global;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.security.storepreferences.ObscuredSharedPreferences;
import com.github.jjobes.slidedatetimepicker.SlideDateTimeListener;
import com.github.jjobes.slidedatetimepicker.SlideDateTimePicker;
import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.GoogleApiAvailability;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class DialogManager {
    public static final int TYPE_ERROR = 0;
    public static final int TYPE_INFO = 1;
    public static final int TYPE_WARNING = 2;
    static final int REQUEST_CODE_RECOVER_PLAY_SERVICES = 1001;
    private static final String OK_LABEL = "OK";
    private static final String POSITIVE_BUTTON = "Yes";
    private static final String NEGATIVE_BUTTON = "No";
    private static final String CANCEL_BUTTON = "Cancel";
    /**
     * To check gps enabled and create an alert to force user to enable gps
     *
     * @param activity
     * <AUTHOR>
     */
    public static NiftyDialogBuilder ndb;
    public static NiftyDialogBuilder ndb2;
    private static AlertDialog alertGPS;
    private static String TAG = "DIALOG_MANAGER";
    private static SlideDateTimeListener dtmListener = new SlideDateTimeListener() {

        @Override
        public void onDateTimeSet(Date date) {
            Calendar cal = Calendar.getInstance();
            cal.setTimeInMillis(date.getTime());
            cal.set(cal.SECOND, 0);
            SimpleDateFormat mFormatter = new SimpleDateFormat(Global.DATE_TIME_STR_FORMAT);
            String result = mFormatter.format(cal);
            DynamicFormActivity.setTxtInFocusText(result);
        }

        // Optional cancel listener
        @Override
        public void onDateTimeCancel() {

        }
    };


    public static void showInfo(final Activity activity, String title, String message) {
        final NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(activity);
        dialogBuilder.withTitle(title)
                .withMessage(message)
                .withButton1Text(activity.getString(R.string.btnOk))
                .setButton1Click(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialogBuilder.dismiss();
                    }
                })
                .isCancelable(false)
                .isCancelableOnTouchOutside(true)
                .show();
    }

    public static void showInfo(final Activity activity, String title, String message, final Callback callback) {
        final NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(activity);
        dialogBuilder.withTitle(title)
                .isCancelable(false)
                .withMessage(message)
                .withButton1Text(activity.getString(R.string.btnOk))
                .withButton2Text(activity.getString(R.string.btnCancel))
                .setButton1Click(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        callback.onOK(dialogBuilder);
                    }
                })
                .setButton2Click(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        callback.onCancel(dialogBuilder);
                    }
                })
                .isCancelable(false)
                .isCancelableOnTouchOutside(false)
                .show();
    }

    public static void databaseUpdateAvailable(Activity activity, String title, String message, final Callback callback) {
        final NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(activity);
        dialogBuilder.withTitle(title)
                .withMessage(message)
                .withButton1Text(activity.getString(R.string.btnUpdate))
                .withButton2Text(activity.getString(R.string.btnContinue))
                .setButton1Click(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        callback.onOK(dialogBuilder);
                    }
                })
                .setButton2Click(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        callback.onCancel(dialogBuilder);
                    }
                })
                .isCancelable(false)
                .isCancelableOnTouchOutside(false)
                .show();
    }

    public static interface Callback {
        public void onOK(NiftyDialogBuilder niftyDialogBuilder);
        public void onCancel(NiftyDialogBuilder niftyDialogBuilder);
    }

//    public static class CallbackImpl implements Callback {
//        private Date date;
//
//        public CallbackImpl(Date date) {
//            this.date = date;
//        }
//
//        public CallbackImpl() {}
//
//        public Date getDate() {
//            return date;
//        }
//
//        public void setDate(Date date) {
//            this.date = date;
//        }
//
//        @Override
//        public void onOK(NiftyDialogBuilder niftyDialogBuilder) {
//            //
//        }
//    }

    /**
     * @param context
     * @param dialogType TYPE_ERROR, TYPE_INFO, TYPE_WARNING
     * @param message
     * @param title,     you can set empty string
     */

    public static void showAlert(Context context, int dialogType, String message, String title) {
        final AlertDialog alertDialog = new AlertDialog.Builder(context).create();

        String frontText = null;

        switch (dialogType) {
            case TYPE_ERROR:
                frontText = "ERROR";
                break;
            case TYPE_INFO:
                frontText = "INFO";
                break;
            case TYPE_WARNING:
                frontText = "WARNING";
                break;
        }

        alertDialog.setTitle(title);
        alertDialog.setMessage(frontText + ": " + message);
        alertDialog.setButton(DialogInterface.BUTTON_POSITIVE, OK_LABEL, new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int which) {
                dialog.cancel();
            }
        });

        // ** dikasih try, soalnya kadang saat lg mau nampilin,
        // orang yang megang hp nya pencet back or home,
        // so kalo dialog nya tampil dy bakal eror karena layar nya berganti.
        // 17 feb 2012
        try {
            alertDialog.show();
        } catch (Exception e) {
            FireCrash.log(e);
            if (Global.IS_DEV)
                Log.i(TAG, "Show Dialog " + e);
        }

    }

    public static void showAlertNotif(Context context, String message, String title) {

        final NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(context);
        dialogBuilder.withTitle(title)
                .withMessage(message)
                .withButton1Text(OK_LABEL)
                .setButton1Click(new View.OnClickListener() {

                    @Override
                    public void onClick(View arg0) {
                        // TODO Auto-generated method stub
                        dialogBuilder.dismiss();
                    }
                })
                .isCancelable(true)
                .isCancelableOnTouchOutside(true)
                .show();

    }

    public static void showAlert(Context context, int type, String message,
                                 DialogInterface.OnClickListener listener, String title) {
        final AlertDialog alertDialog = new AlertDialog.Builder(context).create();

        String frontText = null;

        switch (type) {
            case TYPE_ERROR:
                frontText = "ERROR";
                break;
            case TYPE_INFO:
                frontText = "INFO";
                break;
            case TYPE_WARNING:
                frontText = "WARNING";
                break;
        }

        alertDialog.setTitle(title);
        alertDialog.setMessage(frontText + ": " + message);
        alertDialog.setButton(DialogInterface.BUTTON_POSITIVE, OK_LABEL, listener);
        alertDialog.show();
    }

    public static void showImageDialog(Context context, Bitmap image) {
        NiftyDialogBuilder_PL pl = NiftyDialogBuilder_PL.getInstance(context);
        pl.withNoTitle().
                withNoMessage().
                withTransparentBackground().
                withImageView(image).show();
    }

    public static void showExitAlertQuestion(final Activity activity, String message) {
        /*if(Global.isUploading){
            //param, jika masih uoloading gak boleh exit
			final NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(activity);
			dialogBuilder.withTitle(activity.getString(R.string.btnExit))
				.withMessage(activity.getString(R.string.msgStillUploading))
				.withButton1Text(OK_LABEL)
				.setButton1Click(new View.OnClickListener() {

					@Override
					public void onClick(View arg0) {
						// TODO Auto-generated method stub
						dialogBuilder.dismiss();
					}
				})
				.isCancelable(false)
				.isCancelableOnTouchOutside(true)
				.show();
		}else{*/
        final NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(activity);
        dialogBuilder.withTitle(activity.getString(R.string.btnExit))
//				.withIcon(R.drawable.dialog_ic_exit)
                .withMessage(message)
                .withButton1Text(activity.getString(R.string.btnYes))
                .withButton2Text(activity.getString(R.string.btnNo))
                .setButton1Click(new View.OnClickListener() {

                    @Override
                    public void onClick(View arg0) {
                        dialogBuilder.dismiss();
                        activity.finish();
                        GlobalData.getSharedGlobalData().setDoingTask(false);
                    }
                })
                .setButton2Click(new View.OnClickListener() {

                    @Override
                    public void onClick(View v) {
                        dialogBuilder.dismiss();
                    }
                })
                .isCancelable(false)
                .isCancelableOnTouchOutside(true)
                .show();
//		}
    }

    public static void showExitAlert(final Activity activity, String message) {
        if (Global.isUploading) {
            //param, jika masih uoloading gak boleh exit
            final NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(activity);
            dialogBuilder.withTitle(activity.getString(R.string.btnExit))
                    .withMessage(activity.getString(R.string.msgStillUploading))
                    .withButton1Text(OK_LABEL)
                    .setButton1Click(new View.OnClickListener() {

                        @Override
                        public void onClick(View arg0) {
                            // TODO Auto-generated method stub
                            dialogBuilder.dismiss();
                        }
                    })
                    .isCancelable(false)
                    .isCancelableOnTouchOutside(true)
                    .show();
        } else {
            final NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(activity);
            dialogBuilder.withTitle(activity.getString(R.string.btnExit))
//				.withIcon(R.drawable.dialog_ic_exit)
                    .withMessage(message)
                    .withButton1Text(activity.getString(R.string.btnYes))
                    .withButton2Text(activity.getString(R.string.btnNo))
                    .setButton1Click(new View.OnClickListener() {

                        @Override
                        public void onClick(View arg0) {
                            dialogBuilder.dismiss();
                            if (MainMenuActivity.AutoSendLocationHistoryService != null)
                                activity.stopService(MainMenuActivity.AutoSendLocationHistoryService);
                            if (MainMenuActivity.RunNotificationService != null)
                                activity.stopService(MainMenuActivity.RunNotificationService);
//						if (MainMenuActivity.autoSendImageIntent != null)
//							activity.stopService(MainMenuActivity.autoSendImageIntent);
//						if (MainMenuActivity.autoSendTaskIntent != null)
//							activity.stopService(MainMenuActivity.autoSendTaskIntent);
//						if (Global.approvalNotivIntent != null)
//							activity.stopService(Global.approvalNotivIntent);
//						if (Global.verifyNotivIntent != null)
//							activity.stopService(Global.verifyNotivIntent);
//						MainMenuActivity.mainServices.stopMainServices();
                            try {
                                UserSession.clear();

                                ObscuredSharedPreferences sharedPref = ObscuredSharedPreferences.getPrefs(activity,
                                        "GlobalData", Context.MODE_PRIVATE);

                                ObscuredSharedPreferences.Editor sharedPrefEditor = sharedPref.edit();
                                sharedPrefEditor.remove("HAS_LOGGED");
                                sharedPrefEditor.commit();
                            } catch (Exception e) {
                                FireCrash.log(e);
                                // TODO: handle exception
                            }
                            activity.finish();

                            new Handler().postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    System.exit(0);
                                }
                            }, 1000);
//						android.os.Process.killProcess(android.os.Process.myPid());
                        }
                    })
                    .setButton2Click(new View.OnClickListener() {

                        @Override
                        public void onClick(View v) {
                            dialogBuilder.dismiss();
                        }
                    })
                    .isCancelable(false)
                    .isCancelableOnTouchOutside(true)
                    .show();
        }
    }
//	public static void showAbout(Activity activity, int layoutResID) {
//		Dialog dialog = new Dialog(activity);
//
//		dialog.setContentView(R.layout.about);
//		dialog.setTitle(activity.getString(R.string.aboutTitle));
//
//		String title = "Version" + " " + Global.VERSION;
//		TextView lblTitle = (TextView) dialog.findViewById(R.id.title);
//		lblTitle.setText(title);
//
//		String desc = activity.getString(R.string.aboutCopyright);
//		TextView lblDesc = (TextView) dialog.findViewById(R.id.description);
//		lblDesc.setText(desc);
//		
//		dialog.setOwnerActivity(activity);
//		dialog.show();
//	}

    public static void showForceExitAlert(final Activity activity, String message) {
        final NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(activity);
        dialogBuilder.withTitle(activity.getString(R.string.btnExit))
//				.withIcon(R.drawable.dialog_ic_exit)
                .withMessage(message)
                .withButton1Text(activity.getString(R.string.btnYes))
                .setButton1Click(new View.OnClickListener() {

                    @Override
                    public void onClick(View arg0) {
                        dialogBuilder.dismiss();
                        Global.isForceLogout=false;
                        Global.isLoggedIn=false;
                        if (MainMenuActivity.AutoSendLocationHistoryService != null)
                            activity.stopService(MainMenuActivity.AutoSendLocationHistoryService);
                        if (MainMenuActivity.RunNotificationService != null)
                            activity.stopService(MainMenuActivity.RunNotificationService);
//							if(MainMenuActivity.autoSendImageIntent!=null)
//								activity.stopService(MainMenuActivity.autoSendImageIntent);
//							if(MainMenuActivity.autoSendTaskIntent!=null)
//								activity.stopService(MainMenuActivity.autoSendTaskIntent);
//							if(Global.approvalNotivIntent!=null)
//								activity.stopService(Global.approvalNotivIntent);
//							if(Global.verifyNotivIntent!=null)
//								activity.stopService(Global.verifyNotivIntent);
//							MainMenuActivity.mainServices.stopMainServices();
                        try {
                            UserSession.clear();

                            ObscuredSharedPreferences sharedPref = ObscuredSharedPreferences.getPrefs(activity,
                                    "GlobalData", Context.MODE_PRIVATE);

                            ObscuredSharedPreferences.Editor sharedPrefEditor = sharedPref.edit();
                            sharedPrefEditor.remove("HAS_LOGGED");
                            sharedPrefEditor.commit();
                        } catch (Exception e) {
                            FireCrash.log(e);
                            // TODO: handle exception
                        }

                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                            activity.finishAffinity();
                        } else {
                            activity.finish();
                        }
                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                System.exit(0);
                            }
                        }, 1000);
                    }
                })
                .isCancelable(false)
                .isCancelableOnTouchOutside(false)
                .show();
    }

    public static void showForceSyncronize(final MainMenuActivity activity) {
        final NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(activity);
        dialogBuilder.withTitle(activity.getString(R.string.sync_dialog))
                .withMessage(R.string.sync_dialog_message)
                .withButton1Text(activity.getString(R.string.btnYes))
                .setButton1Click(new View.OnClickListener() {

                    @Override
                    public void onClick(View arg0) {
                        dialogBuilder.dismiss();
                        activity.gotoSynchronize();
                    }
                })
                .isCancelable(false)
                .isCancelableOnTouchOutside(false)
                .show();
    }

    public static void showDateTimePicker(FragmentActivity activity) {
        /*final Dialog dialog = new Dialog(activity);
        dialog.setContentView(R.layout.custom_datetime_picker);
		dialog.setTitle(activity.getString(R.string.btnDateTime));

		Calendar c = Calendar.getInstance();


		final DatePicker gDatePicker = (DatePicker)dialog.findViewById(R.id.gDatePicker);
		final TimePicker gTimePicker = (TimePicker)dialog.findViewById(R.id.gTimePicker);

		gTimePicker.setIs24HourView(true);
		Button btnOK=(Button)dialog.findViewById(R.id.btnOK);
		Button btnCancel=(Button)dialog.findViewById(R.id.btnCancel2);

		btnOK.setOnClickListener(new OnClickListener() {

			@Override
			public void onClick(View arg0) {
				// TODO Auto-generated method stub
				int mYear= gDatePicker.getYear();
				int mMonth= gDatePicker.getMonth();
				int mDayOfMonth =gDatePicker.getDayOfMonth();

				DateFormatSymbols dfs = DateFormatSymbols.getInstance();

				String year=String.valueOf(mYear);
//				String monthOfYear= dfs.getShortMonths()[mMonth]; //untuk format MMM
				String monthOfYear= Tool.appendZeroForDateTime(mMonth, true); //untuk format MM
				String dayOfMonth=String.valueOf(mDayOfMonth);

				int selectedHour=gTimePicker.getCurrentHour();
				int selectedMinute=gTimePicker.getCurrentMinute();

				String hour= (selectedHour < 10) ? "0" + selectedHour
                        : Integer.toString(selectedHour);
				String minute= (selectedMinute < 10) ? "0" + selectedMinute
                        : Integer.toString(selectedMinute);

//				String result = dayOfMonth + "-" + monthOfYear+ "-" + year+ " " + hour + ":" + minute;
				String result = dayOfMonth + "/" + monthOfYear+ "/" + year+ " " + hour + ":" + minute;
				DynamicFormActivity.setTxtInFocusText(result);
				dialog.dismiss();
			}
		});

		btnCancel.setOnClickListener(new OnClickListener() {

			@Override
			public void onClick(View arg0) {
				// TODO Auto-generated method stub
				dialog.cancel();
			}
		});

		dialog.setOwnerActivity(activity);
		dialog.show();*/

        new SlideDateTimePicker.Builder(activity.getSupportFragmentManager())
                .setListener(dtmListener)
                .setInitialDate(new Date())
//				.setMinDate(nowtime)
                .setIs24HourTime(true)
//				.setIndicatorColor(Color.parseColor("#6795fb"))
                .build()
                .show();
    }

    /**
     * generate gps alert.
     * eg : message  : "GPS provider not enabled. Would you like to enable it?"
     * button label : Enable GPS
     *
     * @param activity
     */
//	public static void showGPSAlert(final Activity activity) {
//		try {
//
//			LocationManager locationManager = (LocationManager) activity
//					.getSystemService(Context.LOCATION_SERVICE);
//			if (locationManager.getProvider(LocationManager.GPS_PROVIDER) != null) {
//				final boolean locationEnabled = locationManager
//						.isProviderEnabled(LocationManager.GPS_PROVIDER);
//
//				if (!locationEnabled) {
//					if (alertGPS == null) {
//						if (Global.IS_DEV) System.out.println("!locationEnabled");
//
//
//						final AlertDialog.Builder builder = new AlertDialog.Builder(
//								activity);
//						builder.setMessage(R.string.enable_gps_dialog);
//						builder.setPositiveButton(R.string.enable_gps,
//								new DialogInterface.OnClickListener() {
//									public void onClick(DialogInterface dialog,
//											int id) {
//										enableLocationSettings(activity);
//									}
//								});
//						alertGPS = builder.create();
//						alertGPS.setCancelable(false);
//
//					}
//
//					// show is the dialog not show
//					if (!alertGPS.isShowing()) {
//						try {
//							alertGPS.dismiss();
//						} catch (Exception e) {
//							if (Global.IS_DEV) System.out.println("showGPSAlert0 "+e);
//						}
//
//						alertGPS.show();
//
//					}else{
//						alertGPS.show();
//					}
//
//
//
//
//				}
//			}
//		} catch (Exception e) {
//			if (Global.IS_DEV) System.out.println("showGPSAlert "+e);
//		}
//	}

    // Method to launch Settings
    private static void enableLocationSettings(final Activity activity) {
        Intent settingsIntent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
        activity.startActivity(settingsIntent);
        try {
            alertGPS.dismiss();
            alertGPS = null;
        } catch (Exception e) {
            FireCrash.log(e);
            alertGPS = null;
            // TODO: handle exception
        }

    }

    public static void showGPSAlert(final Activity activity) {
        try {
            LocationManager lm = (LocationManager) activity.getSystemService(Context.LOCATION_SERVICE);
            final boolean locationEnabled = Tool.locationEnabled(lm);
            if (!locationEnabled) {
                closeGPSAlert();
                ndb = NiftyDialogBuilder.getInstance(activity);
                ndb.withTitle(activity.getString(R.string.gps_unable))
                        .withMessage(activity.getString(R.string.gps_warning))
                        .withButton1Text(activity.getString(R.string.gps_button))
                        .setButton1Click(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                ndb.dismiss();
                                //enableLocationSettings(activity);
                                Intent settingsIntent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                                activity.startActivity(settingsIntent);

                            }
                        });
                ndb.isCancelable(false);
                ndb.show();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
            try {
                LocationManager lm = (LocationManager) activity.getSystemService(Context.LOCATION_SERVICE);
                final boolean locationEnabled = Tool.locationEnabled(lm);
                boolean isMock = false;

                isMock = Settings.Secure.getString(activity.getContentResolver(), Settings.Secure.ALLOW_MOCK_LOCATION).equals("1");
                if (locationEnabled && isMock) {
                    closeGPSAlert();
                    ndb = NiftyDialogBuilder.getInstance(activity);
                    ndb.withTitle(activity.getString(R.string.mock_location))
                            .withMessage(activity.getString(R.string.mock_location_alert))

                            .withButton1Text(activity.getString(R.string.dev_option))
                            .setButton1Click(new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    ndb.dismiss();
                                    Intent settingsIntent = new Intent(Settings.ACTION_APPLICATION_DEVELOPMENT_SETTINGS);
                                    activity.startActivity(settingsIntent);

                                }
                            });
                    ndb.isCancelable(false);
                    ndb.show();
                }
            } catch (Exception e) {
                FireCrash.log(e);
                e.printStackTrace();
            }
        }
    }

    public static void showMockDialog(final Context activity) {
        try {
            closeGPSAlert();
            ndb = NiftyDialogBuilder.getInstance(activity);
            ndb.withTitle(activity.getString(R.string.mock_location))
                    .withMessage(activity.getString(R.string.mock_location_alert))

                    .withButton1Text(activity.getString(R.string.dev_option))
                    .setButton1Click(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            ndb.dismiss();
                            Intent settingsIntent = new Intent(Settings.ACTION_APPLICATION_DEVELOPMENT_SETTINGS);
                            activity.startActivity(settingsIntent);

                        }
                    });
            ndb.isCancelable(false);
            ndb.show();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void showTurnOffDevMode(final Context context) {
        try {
            ndb = NiftyDialogBuilder.getInstance(context);
            ndb.withTitle(context.getString(R.string.title_developer_mode))
                    .withMessage(context.getString(R.string.text_turn_off_dev_mode))
                    .withButton1Text(context.getString(R.string.btnSetting))
                    .setButton1Click(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            ndb.dismiss();
                            Intent setting = new Intent(Settings.ACTION_SETTINGS);
                            context.startActivity(setting);
                        }
                    });
            ndb.isCancelable(false);
            ndb.isCancelableOnTouchOutside(false);
            ndb.show();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void closeGPSAlert() {
        if (ndb != null)
            ndb.dismiss();
    }

    public static boolean checkPlayServices(Activity activity) {
        int status = GoogleApiAvailability.getInstance().isGooglePlayServicesAvailable(activity);
        if (status != ConnectionResult.SUCCESS) {
            if (GoogleApiAvailability.getInstance().isUserResolvableError(status)) {
                Dialog dialog = GoogleApiAvailability.getInstance().getErrorDialog(activity, status,
                        REQUEST_CODE_RECOVER_PLAY_SERVICES);
                //dialog.onBackPressed();
                dialog.setCancelable(false);
                dialog.show();
            } else {
                Toast.makeText(activity, activity.getString(R.string.device_not_supported),
                        Toast.LENGTH_LONG).show();
                activity.finish();
            }
            return false;
        }
        return true;
    }

    public static void uninstallAPK(Context context) {
        String packageName = context.getPackageName();
        Uri packageURI = Uri.parse("package:" + packageName);
        Intent uninstallIntent = new Intent(Intent.ACTION_DELETE, packageURI);
        uninstallIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(uninstallIntent);
    }

    public static void UninstallerHandler(final FragmentActivity activity) {
        String message = activity.getString(R.string.inactive_user, GlobalData.getSharedGlobalData().getUser().getFullname());
        final NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(activity);
        dialogBuilder.withTitle(activity.getString(R.string.warning_capital))
                .withMessage(message)
                .withButton1Text("Uninstall")
                .setButton1Click(new View.OnClickListener() {

                    @Override
                    public void onClick(View arg0) {
                        // TODO Auto-generated method stub
                        dialogBuilder.dismiss();
                        String packageName = activity.getPackageName();
                        Intent intent = new Intent(Intent.ACTION_UNINSTALL_PACKAGE);
                        intent.setData(Uri.parse("package:" + packageName));
                        intent.putExtra(Intent.EXTRA_RETURN_RESULT, true);
                        activity.startActivityForResult(intent, 1);
                    }
                })
                .isCancelable(false)
                .isCancelableOnTouchOutside(false)
                .show();
    }

    public static void UninstallerHandler(final Activity activity) {
        String message = activity.getString(R.string.inactive_user, GlobalData.getSharedGlobalData().getUser().getFullname());
        final NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(activity);
        dialogBuilder.withTitle(activity.getString(R.string.warning_capital))
                .withMessage(message)
                .withButton1Text("Uninstall")
                .setButton1Click(new View.OnClickListener() {

                    @Override
                    public void onClick(View arg0) {
                        // TODO Auto-generated method stub
                        dialogBuilder.dismiss();
                        String packageName = activity.getPackageName();
                        Intent intent = new Intent(Intent.ACTION_UNINSTALL_PACKAGE);
                        intent.setData(Uri.parse("package:" + packageName));
                        intent.putExtra(Intent.EXTRA_RETURN_RESULT, true);
                        activity.startActivityForResult(intent, 1);
                    }
                })
                .isCancelable(false)
                .isCancelableOnTouchOutside(false)
                .show();
    }

    public static void showRootAlert(final Activity activity, final Context context) {
        try {
            ndb2 = NiftyDialogBuilder.getInstance(activity);
            ndb2.withTitle(activity.getResources().getString(R.string.device_rooted))
                    .withMessage(activity.getResources().getString(R.string.device_rooted_uninstall))
                    .withButton1Text(activity.getResources().getString(R.string.uninstall_apk))
                    .setButton1Click(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            ndb2.dismiss();
                            //enableLocationSettings(activity);
                            uninstallAPK(context);
                        }
                    });
            ndb2.isCancelable(false);
            ndb2.show();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static boolean isTimeAutomatic(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            return Settings.Global.getInt(context.getContentResolver(), Settings.Global.AUTO_TIME, 0) == 1;
        } else {
            return android.provider.Settings.System.getInt(context.getContentResolver(), android.provider.Settings.System.AUTO_TIME, 0) == 1;
        }
    }

    public static void showTimeProviderAlert(final Activity activity) {
        try {
            if (!isTimeAutomatic(activity)) {
                closeGPSAlert();
                ndb = NiftyDialogBuilder.getInstance(activity);
                ndb.withTitle(activity.getString(R.string.time_unable))
                        .withMessage(activity.getString(R.string.time_warning))
                        .withButton1Text(activity.getString(R.string.time_button))
                        .setButton1Click(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                ndb.dismiss();
                                Intent settingsIntent = new Intent(Settings.ACTION_DATE_SETTINGS);
                                activity.startActivity(settingsIntent);

                            }
                        });
                ndb.isCancelable(false);
                ndb.show();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }


    }
}
