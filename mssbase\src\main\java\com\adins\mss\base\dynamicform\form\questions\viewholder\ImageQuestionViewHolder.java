package com.adins.mss.base.dynamicform.form.questions.viewholder;

import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Environment;
import android.provider.MediaStore;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;


import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.dynamicform.DynamicFormActivity;
import com.adins.mss.base.dynamicform.form.DynamicQuestionActivity;
import com.adins.mss.base.dynamicform.form.questions.ImageViewerActivity;
import com.adins.mss.base.dynamicform.form.questions.OnQuestionClickListener;
import com.adins.mss.base.timeline.MapsViewer;
import com.adins.mss.base.util.Utility;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.GeneralParameter;
import com.adins.mss.dao.LocationInfo;
import com.adins.mss.foundation.camerainapp.CameraActivity;
import com.adins.mss.foundation.db.dataaccess.GeneralParameterDataAccess;
import com.adins.mss.foundation.formatter.Formatter;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.image.Utils;
import com.adins.mss.foundation.liveness.LivenessActivity;
import com.adins.mss.foundation.location.LocationTrackingManager;
import com.adins.mss.foundation.questiongenerator.QuestionBean;
import com.adins.mss.foundation.questiongenerator.form.QuestionView;

import java.io.File;
import java.io.IOException;
import java.lang.ref.WeakReference;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * Created by gigin.ginanjar on 01/09/2016.
 */
public class ImageQuestionViewHolder extends RecyclerView.ViewHolder implements View.OnClickListener {
    public static boolean isDeviceSupportCamera2 = true;
    public QuestionView mView;
    public TextView mQuestionLabel;
    public TextView mQuestionAnswer;
    public ImageView mImageAnswer;
    public ImageView mThumbLocation;
    public QuestionBean bean;
    public FragmentActivity mActivity;
    public OnQuestionClickListener mListener;
    private int group;
    private int position;

    @Deprecated
    public ImageQuestionViewHolder(View itemView) {
        super(itemView);
        mView = (QuestionView) itemView.findViewById(R.id.questionImageLayout);
        mQuestionLabel = (TextView) itemView.findViewById(R.id.questionImageLabel);
        mQuestionAnswer = (TextView) itemView.findViewById(R.id.questionImageAnswer);
        mThumbLocation = (ImageView) itemView.findViewById(R.id.imgLocationAnswer);
        mImageAnswer = (ImageView) itemView.findViewById(R.id.imgPhotoAnswer);
    }

    public ImageQuestionViewHolder(View itemView, FragmentActivity activity, OnQuestionClickListener listener) {
        super(itemView);
        mView = (QuestionView) itemView.findViewById(R.id.questionImageLayout);
        mQuestionLabel = (TextView) itemView.findViewById(R.id.questionImageLabel);
        mQuestionAnswer = (TextView) itemView.findViewById(R.id.questionImageAnswer);
        mThumbLocation = (ImageView) itemView.findViewById(R.id.imgLocationAnswer);
        mImageAnswer = (ImageView) itemView.findViewById(R.id.imgPhotoAnswer);
        mActivity = activity;
        mListener = listener;
    }

    public static File createImageFile() throws IOException {
        // Create an image file name
        String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
        String imageFileName = "JPEG_" + timeStamp + "_";
        File storageDir = Environment.getExternalStoragePublicDirectory(
                Environment.DIRECTORY_PICTURES);
        File image = null;
        try {
            storageDir.mkdirs();
            image = File.createTempFile(
                    imageFileName,  /* prefix */
                    ".jpg",         /* suffix */
                    storageDir      /* directory */
            );
        } catch (IOException e) {
            FireCrash.log(e);
            Log.w("ExternalStorage", "Error writing ", e);
        }

        DynamicFormActivity.mCurrentPhotoPath = image.getAbsolutePath();
        return image;
    }

    public void pickImageFromGallery(FragmentActivity mActivity){
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT).setType("image/*");

        if(intent.resolveActivity(mActivity.getPackageManager())!=null){
            mActivity.startActivityForResult(intent, Utils.REQUEST_PICK);
            DynamicQuestionActivity.qBean = bean;
        }

    }
    public void bind(final QuestionBean item, int group, int number) {
        bean = item;
        this.group = group;
        position = number - 1;
        String qLabel = number + ". " + bean.getQuestion_label();


        mQuestionLabel.setText(qLabel);
        mQuestionAnswer.setText("");
        final byte[] img = bean.getImgAnswer();
        mImageAnswer.setOnClickListener(this);
        mThumbLocation.setOnClickListener(this);

        if (img != null && img.length > 0) {
            if (Tool.isHaveLocation(bean.getAnswer_type())) {
                mThumbLocation.setVisibility(View.VISIBLE);
                mThumbLocation.setImageResource(R.drawable.ic_absent);
                if (bean.getAnswer()==null){
                    byte[] data = bean.getImgAnswer();
                    Bitmap bm = Utils.byteToBitmap(data);
                    int width = bm.getWidth();
                    int height = bm.getHeight();
                    long size = bean.getImgAnswer().length;
                    String formattedSize = Formatter.formatByteSize(size);
                    LocationInfo locationInfo = new LocationInfo();
                    locationInfo = bean.getLocationInfo();
                    String location = LocationTrackingManager.toAnswerString_short(locationInfo);
                    String text = width + " x " + height +
                            ". Size " + formattedSize + "\n" + location;
                    bean.setAnswer(text);
                }
            } else {
                mThumbLocation.setVisibility(View.GONE);
                if (bean.getAnswer()==null){
                    byte[] data = bean.getImgAnswer();
                    Bitmap bm = Utils.byteToBitmap(data);
                    int width = bm.getWidth();
                    int height = bm.getHeight();
                    long size = bean.getImgAnswer().length;
                    String formattedSize = Formatter.formatByteSize(size);
                    String text = width + " x " + height +
                            ". Size " + formattedSize + "\n";
                    bean.setAnswer(text);
                }
            }
            new BitmapWorkerTask(mImageAnswer).execute(bean);
//            new ThumbnailBitmapWorkerTask(mActivity, mImageAnswer, bean.getImgAnswer()).execute();
            mQuestionAnswer.setText(bean.getAnswer());
        } else {
            if ("0".equals(bean.getIsResurvey())) {
                mImageAnswer.setImageResource(R.drawable.ic_baseline_check_24);
            } else {
                mImageAnswer.setImageResource(R.drawable.ic_camera);
            }
            if (Tool.isHaveLocation(bean.getAnswer_type())) {
                mThumbLocation.setVisibility(View.VISIBLE);
                mThumbLocation.setImageResource(R.drawable.ic_absent);
            } else {
                mThumbLocation.setVisibility(View.GONE);
            }
        }
    }
    private Boolean checkResurveyFlag(QuestionBean bean) {
        return ((null != bean.getHasDefaultImage() && !"".equals(bean.getHasDefaultImage()))
                && (null != bean.getIsResurvey() && !"".equals(bean.getIsResurvey())));
    }
    public static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i+1), 16));
        }
        return data;
    }
    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.imgPhotoAnswer) {
            mListener.onCapturePhotoClick(bean, group, position);
            GeneralParameter gp = GeneralParameterDataAccess.getOne(mActivity,
                    GlobalData.getSharedGlobalData().getUser().getUuid_user(),
                    Global.MS_IMAGE_GALLERY_REF_ID);
            List<String> gpList = new ArrayList<>();
            if (gp != null) {
                String gpStr = gp.getGs_value().replaceAll("\\s", "");
                gpList = Arrays.asList(gpStr.split(";"));
            }

            if (bean.getImgAnswer() != null) {
                if (DynamicFormActivity.isVerified || DynamicFormActivity.isApproval || bean.isReadOnly()) {
                    try {
                        Global.isViewer = true;
                        Bundle extras = new Bundle();
                        extras.putByteArray(ImageViewerActivity.BUND_KEY_IMAGE, bean.getImgAnswer());
                        extras.putInt(ImageViewerActivity.BUND_KEY_IMAGE_QUALITY, Utils.picQuality);
                        extras.putBoolean(ImageViewerActivity.BUND_KEY_IMAGE_ISVIEWER, Global.isViewer);
                        Intent intent = new Intent(mActivity, ImageViewerActivity.class);
                        intent.putExtras(extras);
                        mActivity.startActivityForResult(intent, Global.REQUEST_EDIT_IMAGE);

                    } catch (Exception e) {
                        FireCrash.log(e);
                    }
                } else {
                    final AlertDialog.Builder builder = new AlertDialog.Builder(mActivity);
                    builder.setMessage(mActivity.getString(R.string.picture_option));
                    builder.setCancelable(true);
                    builder.setPositiveButton(mActivity.getString(R.string.btnView), new DialogInterface.OnClickListener() {
                        public void onClick(DialogInterface dialog, int id) {
                            try {
                                Global.isViewer = !DynamicFormActivity.allowImageEdit;
                                Bundle extras = new Bundle();
                                extras.putByteArray(ImageViewerActivity.BUND_KEY_IMAGE, bean.getImgAnswer());
                                extras.putInt(ImageViewerActivity.BUND_KEY_IMAGE_QUALITY, Utils.picQuality);
                                extras.putBoolean(ImageViewerActivity.BUND_KEY_IMAGE_ISVIEWER, Global.isViewer);
                                Intent intent = new Intent(mActivity, ImageViewerActivity.class);
                                intent.putExtras(extras);
                                mActivity.startActivityForResult(intent, Global.REQUEST_EDIT_IMAGE);
                            } catch (Exception e) {
                                FireCrash.log(e);
                            }
                        }
                    });
                    final List<String> finalGpList = gpList;
                    builder.setNeutralButton(mActivity.getString(R.string.btnRetake), new DialogInterface.OnClickListener() {
                        public void onClick(DialogInterface dialog, int id) {
                            if (!finalGpList.isEmpty()) {

                                if(bean.getAnswer_type().equals(Global.AT_OCR_W_GALLERY)) {
                                    showImageChooser();
                                } else if(bean.getAnswer_type().equals(Global.AT_IMAGE_LIVENESS)){
                                    //at image liveness can only take picture from camera
                                    openLivenessCamera(mActivity);
                                } else if (finalGpList.contains(bean.getIdentifier_name())) {
                                    final AlertDialog.Builder albumcam = new AlertDialog.Builder(mActivity);
                                    albumcam.setMessage(mActivity.getString(R.string.picture_option));
                                    albumcam.setCancelable(true);
                                    albumcam.setPositiveButton(mActivity.getString(R.string.btn_galery), new DialogInterface.OnClickListener() {
                                        @Override
                                        public void onClick(DialogInterface dialog, int which) {
                                            pickImageFromGallery(mActivity);
                                        }
                                    });
                                    albumcam.setNegativeButton(mActivity.getString(R.string.btn_camera), new DialogInterface.OnClickListener() {

                                        @Override
                                        public void onClick(DialogInterface dialog, int which) {
                                            openCameraApp(mActivity);
                                        }
                                    });
                                    albumcam.show();
                                } else {
                                    openCameraApp(mActivity);
                                }
                            } else {
                                if(bean.getAnswer_type().equals(Global.AT_OCR_W_GALLERY)) {
                                    showImageChooser();
                                } else if(bean.getAnswer_type().equals(Global.AT_IMAGE_LIVENESS)){
                                    openLivenessCamera(mActivity);
                                } else {
                                    openCameraApp(mActivity);
                                }
                            }
                        }
                    });
                    builder.setNegativeButton(mActivity.getString(R.string.btnDelete), new DialogInterface.OnClickListener() {
                        public void onClick(DialogInterface dialog, int id) {
                            bean.setImgAnswer(null);
                            bean.setAnswer(null);
                            if (null != bean.getLocationInfo()){
                                bean.setLocationInfo(null);
                            }
                            bean.setAnswer("");
                            mImageAnswer.setImageResource(R.drawable.ic_camera);
                            mQuestionAnswer.setText("");

                            bean.setResponseImageDkcp(null);

                            dialog.cancel();
                        }
                    });
                    builder.create().show();
                }
            } else {
                if (DynamicFormActivity.isVerified || DynamicFormActivity.isApproval) {
                    try {
                        Global.isViewer = true;
                    } catch (Exception e) {
                        FireCrash.log(e);
                    }
                } else {
                    if (!gpList.isEmpty()) {
                        if(bean.getAnswer_type().equals(Global.AT_OCR_W_GALLERY)) {
                            showImageChooser();
                        } else if(bean.getAnswer_type().equals(Global.AT_IMAGE_LIVENESS)){
                            //at image liveness can only take picture from camera
                            openLivenessCamera(mActivity);
                        } else if (gpList.contains(bean.getIdentifier_name())) {
                            final AlertDialog.Builder albumcam = new AlertDialog.Builder(mActivity);
                            albumcam.setMessage(mActivity.getString(R.string.picture_option));
                            albumcam.setCancelable(true);
                            albumcam.setPositiveButton(mActivity.getString(R.string.btn_galery), new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialog, int which) {
                                    pickImageFromGallery(mActivity);
                                }
                            });
                            albumcam.setNegativeButton(mActivity.getString(R.string.btn_camera), new DialogInterface.OnClickListener() {

                                @Override
                                public void onClick(DialogInterface dialog, int which) {
                                    openCameraApp(mActivity);
                                }
                            });
                            albumcam.show();
                        } else {
                            openCameraApp(mActivity);
                        }
                    } else {
                        if(bean.getAnswer_type().equals(Global.AT_OCR_W_GALLERY)) {
                            showImageChooser();
                        } else if(bean.getAnswer_type().equals(Global.AT_IMAGE_LIVENESS)){
                            openLivenessCamera(mActivity);
                        } else {
                            openCameraApp(mActivity);
                        }
                    }
                }
            }
        } else if (id == R.id.imgLocationAnswer) {
            if (bean.getImgAnswer() != null) {
                mListener.onUpdateLocationClick(bean, group, position);
                try {
                    if (bean.getLocationInfo() != null) {
                        String lat = bean.getLocationInfo().getLatitude();
                        String lng = bean.getLocationInfo().getLongitude();
                        int acc = bean.getLocationInfo().getAccuracy();
                        Bundle extras = new Bundle();
                        extras.putString("latitude", lat);
                        extras.putString("longitude", lng);
                        extras.putInt("accuracy", acc);
                        extras.putBoolean("isViewOnly", bean.isReadOnly());
                        Intent intent = new Intent(mActivity, MapsViewer.class);
                        intent.putExtras(extras);
                        mActivity.startActivityForResult(intent, Global.REQUEST_IMAGE_GPS_LOCATION_UPDATE);
                    } else {
                        Toast.makeText(mActivity, mActivity.getString(R.string.coordinat_not_available),
                                Toast.LENGTH_LONG).show();
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    String lat = bean.getLatitude();
                    String lng = bean.getLongitude();
                    if (lat != null && lng != null) {
                        Bundle extras = new Bundle();
                        extras.putString("latitude", lat);
                        extras.putString("longitude", lng);
                        extras.putBoolean("isViewOnly", bean.isReadOnly());
                        Intent intent = new Intent(mActivity, MapsViewer.class);
                        intent.putExtras(extras);
                        mActivity.startActivityForResult(intent, Global.REQUEST_IMAGE_GPS_LOCATION_UPDATE);
                    } else {
                        Toast.makeText(mActivity, mActivity.getString(R.string.coordinat_not_available),
                                Toast.LENGTH_LONG).show();
                    }
                }
            } else {
                if (DynamicFormActivity.isVerified || DynamicFormActivity.isApproval) {

                } else {
                    Toast.makeText(mActivity, mActivity.getString(R.string.take_foto_first),
                            Toast.LENGTH_LONG).show();
                }
            }
        }
    }

    private void showImageChooser() {
        final AlertDialog.Builder builder = new AlertDialog.Builder(mActivity);
        builder.setMessage(mActivity.getString(R.string.picture_option));
        builder.setCancelable(true);
        builder.setPositiveButton("Gallery", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                openGallery(mActivity);
            }
        });
        builder.setNegativeButton("Camera", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                openCameraApp(mActivity);
            }
        });
        builder.show();
    }

    private void openGallery(FragmentActivity mActivity) {
        Intent intent = new Intent();
        intent.setType("image/*");
        intent.setAction(Intent.ACTION_GET_CONTENT);
        if (intent.resolveActivity(mActivity.getPackageManager()) != null) {
            mActivity.startActivityForResult(intent, Utils.REQUEST_PICK);
            DynamicQuestionActivity.qBean = bean;
        }
    }

    private void openLivenessCamera(FragmentActivity mActivity){
        if (GlobalData.getSharedGlobalData().isUseOwnCamera()) {
            Intent intent = new Intent(mActivity, LivenessActivity.class);
            mActivity.startActivityForResult(intent, Utils.REQUEST_IN_APP_CAMERA);
        } else {
            Toast.makeText(mActivity.getApplicationContext(), "Liveness question need access to camera", Toast.LENGTH_SHORT).show();
        }
    }

    private void openCameraApp(FragmentActivity mActivity) {
        if (GlobalData.getSharedGlobalData().isUseOwnCamera()) {
            int quality = Utils.picQuality;
            int thumbHeight = Utils.picHeight;
            int thumbWidht = Utils.picWidth;

            if (bean.getImg_quality() != null)
                if (bean.getImg_quality().equalsIgnoreCase(Global.IMAGE_HQ)) {
                    thumbHeight = Utils.picHQHeight;
                    thumbWidht = Utils.picHQWidth;
                    quality = Utils.picHQQuality;
                }

//            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP || !isDeviceSupportCamera2) {
            Intent intent = new Intent(mActivity, CameraActivity.class);
            intent.putExtra(CameraActivity.PICTURE_WIDTH, thumbWidht);
            intent.putExtra(CameraActivity.PICTURE_HEIGHT, thumbHeight);
            intent.putExtra(CameraActivity.PICTURE_QUALITY, quality);
            if (Global.AT_OCR_W_GALLERY.equals(bean.getAnswer_type()) && null != bean.getTag() && bean.getTag().contains(Global.TAG_OCR_W_GALLERY)) {
                intent.putExtra(CameraActivity.NEED_BORDER, true);
            }
            mActivity.startActivityForResult(intent, Utils.REQUEST_IN_APP_CAMERA);
//            } else {
//                Intent intent = new Intent(mActivity, Camera2BasicRealActivity.class);
//                intent.putExtra(Camera2BasicRealActivity.PICTURE_WIDTH, thumbWidht);
//                intent.putExtra(Camera2BasicRealActivity.PICTURE_HEIGHT, thumbHeight);
//                intent.putExtra(Camera2BasicRealActivity.PICTURE_QUALITY, quality);
//                mActivity.startActivityForResult(intent, Utils.REQUEST_IN_APP_CAMERA);
//            }

        } else {
            try {
                Intent intent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
                if (intent.resolveActivity(mActivity.getPackageManager()) != null) {
                    File photoFile = null;
                    try {
                        photoFile = createImageFile();
                    } catch (IOException ex) {
                        ex.printStackTrace();
                    }
                    if (photoFile != null) {
                        intent.putExtra(MediaStore.EXTRA_OUTPUT,
                                Uri.fromFile(photoFile));
                        mActivity.startActivityForResult(intent, Utils.REQUEST_CAMERA);
                    }
                }
            } catch (Exception e) {
                FireCrash.log(e);
                // TODO: handle exception for failed open camera
            }
        }
    }

    class BitmapWorkerTask extends AsyncTask<QuestionBean, Void, Bitmap> {
        private final WeakReference<ImageView> imageViewReference;
        private byte[] data;
        private int[] resolusi;

        public BitmapWorkerTask(ImageView imageView) {
            imageViewReference = new WeakReference<ImageView>(imageView);
        }

        // Decode image in background.
        @Override
        protected Bitmap doInBackground(QuestionBean... params) {
            QuestionBean bean = params[0];
            data = bean.getImgAnswer();
            if (null == data) {
                return null;
            }
            Bitmap bm = Utils.byteToBitmap(data);//BitmapFactory.decodeByteArray(data, 0, data.length);
            resolusi = new int[2];
            resolusi[0] = bm.getWidth();
            resolusi[1] = bm.getHeight();
            int[] res = Tool.getThumbnailResolution(bm.getWidth(), bm.getHeight());
            Bitmap thumbnail = Bitmap.createScaledBitmap(bm, res[0], res[1], true);
            return thumbnail;
        }

        // Once complete, see if ImageView is still around and set bitmap.
        @Override
        protected void onPostExecute(Bitmap bitmap) {
            if (imageViewReference != null && bitmap != null) {
                final ImageView imageView = imageViewReference.get();
                if (imageView != null) {
                    imageView.setImageBitmap(bitmap);
                }
            }
            Utility.freeMemory();
        }
    }
}
