package com.adins.mss.base.models;

import com.adins.mss.foundation.http.MssRequestType;
import com.google.gson.annotations.SerializedName;

/**
 * Created by adityapurwa on 30/03/15.
 */
public class ChangePasswordRequestModel extends MssRequestType {
    @SerializedName("uuid_user")
    private String uuid_user;
    @SerializedName("old_password")
    private String old_password;
    @SerializedName("new_password")
    private String new_password;

    public String getUuid_user() {

        return uuid_user;
    }

    public void setUuid_user(String uuidUser) {
        this.uuid_user = uuidUser;
    }

    public String getOld_password() {
        return old_password;
    }

    public void setOld_password(String oldPassword) {
        this.old_password = oldPassword;
    }

    public String getNew_password() {
        return new_password;
    }

    public void setNew_password(String newPassword) {
        this.new_password = newPassword;
    }
}
