package com.adins.mss.dao;

import com.adins.mss.dao.DaoSession;
import de.greenrobot.dao.DaoException;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "TR_TASK_D".
 */
public class TaskD {

    /** Not-null value. */
     @SerializedName("uuid_task_d")
    private String uuid_task_d;
     @SerializedName("question_group_id")
    private String question_group_id;
     @SerializedName("question_id")
    private String question_id;
     @SerializedName("option_answer_id")
    private String option_answer_id;
     @SerializedName("text_answer")
    private String text_answer;
     @SerializedName("image")
    private byte[] image;
     @SerializedName("is_final")
    private String is_final;
     @SerializedName("is_sent")
    private String is_sent;
     @SerializedName("lov")
    private String lov;
     @SerializedName("usr_crt")
    private String usr_crt;
     @SerializedName("dtm_crt")
    private java.util.Date dtm_crt;
     @SerializedName("uuid_task_h")
    private String uuid_task_h;
     @SerializedName("question_label")
    private String question_label;
     @SerializedName("latitude")
    private String latitude;
     @SerializedName("longitude")
    private String longitude;
     @SerializedName("mcc")
    private String mcc;
     @SerializedName("mnc")
    private String mnc;
     @SerializedName("lac")
    private String lac;
     @SerializedName("cid")
    private String cid;
     @SerializedName("gps_time")
    private java.util.Date gps_time;
     @SerializedName("accuracy")
    private Integer accuracy;
     @SerializedName("regex")
    private String regex;
     @SerializedName("is_readonly")
    private String is_readonly;
     @ExcludeFromGson 
	 @SerializedName("location_image")
    private byte[] location_image;
     @SerializedName("is_visible")
    private String is_visible;
     @SerializedName("uuid_lookup")
    private String uuid_lookup;
     @SerializedName("tag")
    private String tag;
     @SerializedName("count")
    private String count;
     @SerializedName("has_default_image")
    private String has_default_image;
     @SerializedName("is_resurvey")
    private String is_resurvey;
     @SerializedName("uuid_question_mapping")
    private String uuid_question_mapping;
     @SerializedName("is_readonly_mapping")
    private String is_readonly_mapping;

    /** Used to resolve relations */
    private transient DaoSession daoSession;

    /** Used for active entity operations. */
    private transient TaskDDao myDao;

    private TaskH taskH;
    private String taskH__resolvedKey;

    private Lookup lookup;
    private String lookup__resolvedKey;


    public TaskD() {
    }

    public TaskD(String uuid_task_d) {
        this.uuid_task_d = uuid_task_d;
    }

    public TaskD(String uuid_task_d, String question_group_id, String question_id, String option_answer_id, String text_answer, byte[] image, String is_final, String is_sent, String lov, String usr_crt, java.util.Date dtm_crt, String uuid_task_h, String question_label, String latitude, String longitude, String mcc, String mnc, String lac, String cid, java.util.Date gps_time, Integer accuracy, String regex, String is_readonly, byte[] location_image, String is_visible, String uuid_lookup, String tag, String count, String has_default_image, String is_resurvey, String uuid_question_mapping, String is_readonly_mapping) {
        this.uuid_task_d = uuid_task_d;
        this.question_group_id = question_group_id;
        this.question_id = question_id;
        this.option_answer_id = option_answer_id;
        this.text_answer = text_answer;
        this.image = image;
        this.is_final = is_final;
        this.is_sent = is_sent;
        this.lov = lov;
        this.usr_crt = usr_crt;
        this.dtm_crt = dtm_crt;
        this.uuid_task_h = uuid_task_h;
        this.question_label = question_label;
        this.latitude = latitude;
        this.longitude = longitude;
        this.mcc = mcc;
        this.mnc = mnc;
        this.lac = lac;
        this.cid = cid;
        this.gps_time = gps_time;
        this.accuracy = accuracy;
        this.regex = regex;
        this.is_readonly = is_readonly;
        this.location_image = location_image;
        this.is_visible = is_visible;
        this.uuid_lookup = uuid_lookup;
        this.tag = tag;
        this.count = count;
        this.has_default_image = has_default_image;
        this.is_resurvey = is_resurvey;
        this.uuid_question_mapping = uuid_question_mapping;
        this.is_readonly_mapping = is_readonly_mapping;
    }

    /** called by internal mechanisms, do not call yourself. */
    public void __setDaoSession(DaoSession daoSession) {
        this.daoSession = daoSession;
        myDao = daoSession != null ? daoSession.getTaskDDao() : null;
    }

    /** Not-null value. */
    public String getUuid_task_d() {
        return uuid_task_d;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_task_d(String uuid_task_d) {
        this.uuid_task_d = uuid_task_d;
    }

    public String getQuestion_group_id() {
        return question_group_id;
    }

    public void setQuestion_group_id(String question_group_id) {
        this.question_group_id = question_group_id;
    }

    public String getQuestion_id() {
        return question_id;
    }

    public void setQuestion_id(String question_id) {
        this.question_id = question_id;
    }

    public String getOption_answer_id() {
        return option_answer_id;
    }

    public void setOption_answer_id(String option_answer_id) {
        this.option_answer_id = option_answer_id;
    }

    public String getText_answer() {
        return text_answer;
    }

    public void setText_answer(String text_answer) {
        this.text_answer = text_answer;
    }

    public byte[] getImage() {
        return image;
    }

    public void setImage(byte[] image) {
        this.image = image;
    }

    public String getIs_final() {
        return is_final;
    }

    public void setIs_final(String is_final) {
        this.is_final = is_final;
    }

    public String getIs_sent() {
        return is_sent;
    }

    public void setIs_sent(String is_sent) {
        this.is_sent = is_sent;
    }

    public String getLov() {
        return lov;
    }

    public void setLov(String lov) {
        this.lov = lov;
    }

    public String getUsr_crt() {
        return usr_crt;
    }

    public void setUsr_crt(String usr_crt) {
        this.usr_crt = usr_crt;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public String getUuid_task_h() {
        return uuid_task_h;
    }

    public void setUuid_task_h(String uuid_task_h) {
        this.uuid_task_h = uuid_task_h;
    }

    public String getQuestion_label() {
        return question_label;
    }

    public void setQuestion_label(String question_label) {
        this.question_label = question_label;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getMcc() {
        return mcc;
    }

    public void setMcc(String mcc) {
        this.mcc = mcc;
    }

    public String getMnc() {
        return mnc;
    }

    public void setMnc(String mnc) {
        this.mnc = mnc;
    }

    public String getLac() {
        return lac;
    }

    public void setLac(String lac) {
        this.lac = lac;
    }

    public String getCid() {
        return cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

    public java.util.Date getGps_time() {
        return gps_time;
    }

    public void setGps_time(java.util.Date gps_time) {
        this.gps_time = gps_time;
    }

    public Integer getAccuracy() {
        return accuracy;
    }

    public void setAccuracy(Integer accuracy) {
        this.accuracy = accuracy;
    }

    public String getRegex() {
        return regex;
    }

    public void setRegex(String regex) {
        this.regex = regex;
    }

    public String getIs_readonly() {
        return is_readonly;
    }

    public void setIs_readonly(String is_readonly) {
        this.is_readonly = is_readonly;
    }

    public byte[] getLocation_image() {
        return location_image;
    }

    public void setLocation_image(byte[] location_image) {
        this.location_image = location_image;
    }

    public String getIs_visible() {
        return is_visible;
    }

    public void setIs_visible(String is_visible) {
        this.is_visible = is_visible;
    }

    public String getUuid_lookup() {
        return uuid_lookup;
    }

    public void setUuid_lookup(String uuid_lookup) {
        this.uuid_lookup = uuid_lookup;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }

    public String getHas_default_image() {
        return has_default_image;
    }

    public void setHas_default_image(String has_default_image) {
        this.has_default_image = has_default_image;
    }

    public String getIs_resurvey() {
        return is_resurvey;
    }

    public void setIs_resurvey(String is_resurvey) {
        this.is_resurvey = is_resurvey;
    }

    public String getUuid_question_mapping() {
        return uuid_question_mapping;
    }

    public void setUuid_question_mapping(String uuid_question_mapping) {
        this.uuid_question_mapping = uuid_question_mapping;
    }

    public String getIs_readonly_mapping() {
        return is_readonly_mapping;
    }

    public void setIs_readonly_mapping(String is_readonly_mapping) {
        this.is_readonly_mapping = is_readonly_mapping;
    }

    /** To-one relationship, resolved on first access. */
    public TaskH getTaskH() {
        String __key = this.uuid_task_h;
        if (taskH__resolvedKey == null || taskH__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            TaskHDao targetDao = daoSession.getTaskHDao();
            TaskH taskHNew = targetDao.load(__key);
            synchronized (this) {
                taskH = taskHNew;
            	taskH__resolvedKey = __key;
            }
        }
        return taskH;
    }

    public void setTaskH(TaskH taskH) {
        synchronized (this) {
            this.taskH = taskH;
            uuid_task_h = taskH == null ? null : taskH.getUuid_task_h();
            taskH__resolvedKey = uuid_task_h;
        }
    }

    /** To-one relationship, resolved on first access. */
    public Lookup getLookup() {
        String __key = this.uuid_lookup;
        if (lookup__resolvedKey == null || lookup__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            LookupDao targetDao = daoSession.getLookupDao();
            Lookup lookupNew = targetDao.load(__key);
            synchronized (this) {
                lookup = lookupNew;
            	lookup__resolvedKey = __key;
            }
        }
        return lookup;
    }

    public void setLookup(Lookup lookup) {
        synchronized (this) {
            this.lookup = lookup;
            uuid_lookup = lookup == null ? null : lookup.getUuid_lookup();
            lookup__resolvedKey = uuid_lookup;
        }
    }

    /** Convenient call for {@link AbstractDao#delete(Object)}. Entity must attached to an entity context. */
    public void delete() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.delete(this);
    }

    /** Convenient call for {@link AbstractDao#update(Object)}. Entity must attached to an entity context. */
    public void update() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.update(this);
    }

    /** Convenient call for {@link AbstractDao#refresh(Object)}. Entity must attached to an entity context. */
    public void refresh() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.refresh(this);
    }

}
