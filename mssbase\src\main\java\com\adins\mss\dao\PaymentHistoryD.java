package com.adins.mss.dao;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "TR_PAYMENTHISTORY_D".
 */
public class PaymentHistoryD {

    /** Not-null value. */
     @SerializedName("uuid_payment_history_d")
    private String uuid_payment_history_d;
     @SerializedName("uuid_task_h")
    private String uuid_task_h;
     @SerializedName("transactiontype")
    private String transaction_type;
     @SerializedName("receipt_no")
    private String receipt_no;
     @SerializedName("value_date")
    private java.util.Date value_date;
     @SerializedName("posting_date")
    private java.util.Date posting_date;
     @SerializedName("payment_amount")
    private String payment_amount;
     @SerializedName("installment_amount")
    private String installment_amount;
     @SerializedName("installment_number")
    private String installment_number;
     @SerializedName("wop_code")
    private String wop_code;
     @SerializedName("payment_allocation_name")
    private String payment_allocation_name;
     @SerializedName("os_amount_od")
    private String os_amount_od;
     @SerializedName("receive_amount")
    private String receive_amount;
     @SerializedName("dtm_upd")
    private java.util.Date dtm_upd;
     @SerializedName("usr_upd")
    private String usr_upd;
     @SerializedName("dtm_crt")
    private java.util.Date dtm_crt;
     @SerializedName("usr_crt")
    private String usr_crt;
     @SerializedName("uuid_payment_history_h")
    private String uuid_payment_history_h;

    public PaymentHistoryD() {
    }

    public PaymentHistoryD(String uuid_payment_history_d) {
        this.uuid_payment_history_d = uuid_payment_history_d;
    }

    public PaymentHistoryD(String uuid_payment_history_d, String uuid_task_h, String transaction_type, String receipt_no, java.util.Date value_date, java.util.Date posting_date, String payment_amount, String installment_amount, String installment_number, String wop_code, String payment_allocation_name, String os_amount_od, String receive_amount, java.util.Date dtm_upd, String usr_upd, java.util.Date dtm_crt, String usr_crt, String uuid_payment_history_h) {
        this.uuid_payment_history_d = uuid_payment_history_d;
        this.uuid_task_h = uuid_task_h;
        this.transaction_type = transaction_type;
        this.receipt_no = receipt_no;
        this.value_date = value_date;
        this.posting_date = posting_date;
        this.payment_amount = payment_amount;
        this.installment_amount = installment_amount;
        this.installment_number = installment_number;
        this.wop_code = wop_code;
        this.payment_allocation_name = payment_allocation_name;
        this.os_amount_od = os_amount_od;
        this.receive_amount = receive_amount;
        this.dtm_upd = dtm_upd;
        this.usr_upd = usr_upd;
        this.dtm_crt = dtm_crt;
        this.usr_crt = usr_crt;
        this.uuid_payment_history_h = uuid_payment_history_h;
    }

    /** Not-null value. */
    public String getUuid_payment_history_d() {
        return uuid_payment_history_d;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_payment_history_d(String uuid_payment_history_d) {
        this.uuid_payment_history_d = uuid_payment_history_d;
    }

    public String getUuid_task_h() {
        return uuid_task_h;
    }

    public void setUuid_task_h(String uuid_task_h) {
        this.uuid_task_h = uuid_task_h;
    }

    public String getTransaction_type() {
        return transaction_type;
    }

    public void setTransaction_type(String transaction_type) {
        this.transaction_type = transaction_type;
    }

    public String getReceipt_no() {
        return receipt_no;
    }

    public void setReceipt_no(String receipt_no) {
        this.receipt_no = receipt_no;
    }

    public java.util.Date getValue_date() {
        return value_date;
    }

    public void setValue_date(java.util.Date value_date) {
        this.value_date = value_date;
    }

    public java.util.Date getPosting_date() {
        return posting_date;
    }

    public void setPosting_date(java.util.Date posting_date) {
        this.posting_date = posting_date;
    }

    public String getPayment_amount() {
        return payment_amount;
    }

    public void setPayment_amount(String payment_amount) {
        this.payment_amount = payment_amount;
    }

    public String getInstallment_amount() {
        return installment_amount;
    }

    public void setInstallment_amount(String installment_amount) {
        this.installment_amount = installment_amount;
    }

    public String getInstallment_number() {
        return installment_number;
    }

    public void setInstallment_number(String installment_number) {
        this.installment_number = installment_number;
    }

    public String getWop_code() {
        return wop_code;
    }

    public void setWop_code(String wop_code) {
        this.wop_code = wop_code;
    }

    public String getPayment_allocation_name() {
        return payment_allocation_name;
    }

    public void setPayment_allocation_name(String payment_allocation_name) {
        this.payment_allocation_name = payment_allocation_name;
    }

    public String getOs_amount_od() {
        return os_amount_od;
    }

    public void setOs_amount_od(String os_amount_od) {
        this.os_amount_od = os_amount_od;
    }

    public String getReceive_amount() {
        return receive_amount;
    }

    public void setReceive_amount(String receive_amount) {
        this.receive_amount = receive_amount;
    }

    public java.util.Date getDtm_upd() {
        return dtm_upd;
    }

    public void setDtm_upd(java.util.Date dtm_upd) {
        this.dtm_upd = dtm_upd;
    }

    public String getUsr_upd() {
        return usr_upd;
    }

    public void setUsr_upd(String usr_upd) {
        this.usr_upd = usr_upd;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public String getUsr_crt() {
        return usr_crt;
    }

    public void setUsr_crt(String usr_crt) {
        this.usr_crt = usr_crt;
    }

    public String getUuid_payment_history_h() {
        return uuid_payment_history_h;
    }

    public void setUuid_payment_history_h(String uuid_payment_history_h) {
        this.uuid_payment_history_h = uuid_payment_history_h;
    }

}
