package com.adins.mss.dao;

import com.adins.mss.dao.DaoSession;
import de.greenrobot.dao.DaoException;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "MS_PRINTITEM".
 */
public class PrintItem {

    /** Not-null value. */
     @SerializedName("uuid_print_item")
    private String uuid_print_item;
     @SerializedName("print_type_id")
    private String print_type_id;
     @SerializedName("print_item_label")
    private String print_item_label;
     @SerializedName("question_group_id")
    private String question_group_id;
     @SerializedName("question_id")
    private String question_id;
     @SerializedName("print_item_order")
    private Integer print_item_order;
     @SerializedName("usr_crt")
    private String usr_crt;
     @SerializedName("dtm_crt")
    private java.util.Date dtm_crt;
     @SerializedName("usr_upd")
    private String usr_upd;
     @SerializedName("dtm_upd")
    private java.util.Date dtm_upd;
     @SerializedName("uuid_scheme")
    private String uuid_scheme;

    /** Used to resolve relations */
    private transient DaoSession daoSession;

    /** Used for active entity operations. */
    private transient PrintItemDao myDao;

    private Scheme scheme;
    private String scheme__resolvedKey;


    public PrintItem() {
    }

    public PrintItem(String uuid_print_item) {
        this.uuid_print_item = uuid_print_item;
    }

    public PrintItem(String uuid_print_item, String print_type_id, String print_item_label, String question_group_id, String question_id, Integer print_item_order, String usr_crt, java.util.Date dtm_crt, String usr_upd, java.util.Date dtm_upd, String uuid_scheme) {
        this.uuid_print_item = uuid_print_item;
        this.print_type_id = print_type_id;
        this.print_item_label = print_item_label;
        this.question_group_id = question_group_id;
        this.question_id = question_id;
        this.print_item_order = print_item_order;
        this.usr_crt = usr_crt;
        this.dtm_crt = dtm_crt;
        this.usr_upd = usr_upd;
        this.dtm_upd = dtm_upd;
        this.uuid_scheme = uuid_scheme;
    }

    /** called by internal mechanisms, do not call yourself. */
    public void __setDaoSession(DaoSession daoSession) {
        this.daoSession = daoSession;
        myDao = daoSession != null ? daoSession.getPrintItemDao() : null;
    }

    /** Not-null value. */
    public String getUuid_print_item() {
        return uuid_print_item;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_print_item(String uuid_print_item) {
        this.uuid_print_item = uuid_print_item;
    }

    public String getPrint_type_id() {
        return print_type_id;
    }

    public void setPrint_type_id(String print_type_id) {
        this.print_type_id = print_type_id;
    }

    public String getPrint_item_label() {
        return print_item_label;
    }

    public void setPrint_item_label(String print_item_label) {
        this.print_item_label = print_item_label;
    }

    public String getQuestion_group_id() {
        return question_group_id;
    }

    public void setQuestion_group_id(String question_group_id) {
        this.question_group_id = question_group_id;
    }

    public String getQuestion_id() {
        return question_id;
    }

    public void setQuestion_id(String question_id) {
        this.question_id = question_id;
    }

    public Integer getPrint_item_order() {
        return print_item_order;
    }

    public void setPrint_item_order(Integer print_item_order) {
        this.print_item_order = print_item_order;
    }

    public String getUsr_crt() {
        return usr_crt;
    }

    public void setUsr_crt(String usr_crt) {
        this.usr_crt = usr_crt;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public String getUsr_upd() {
        return usr_upd;
    }

    public void setUsr_upd(String usr_upd) {
        this.usr_upd = usr_upd;
    }

    public java.util.Date getDtm_upd() {
        return dtm_upd;
    }

    public void setDtm_upd(java.util.Date dtm_upd) {
        this.dtm_upd = dtm_upd;
    }

    public String getUuid_scheme() {
        return uuid_scheme;
    }

    public void setUuid_scheme(String uuid_scheme) {
        this.uuid_scheme = uuid_scheme;
    }

    /** To-one relationship, resolved on first access. */
    public Scheme getScheme() {
        String __key = this.uuid_scheme;
        if (scheme__resolvedKey == null || scheme__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            SchemeDao targetDao = daoSession.getSchemeDao();
            Scheme schemeNew = targetDao.load(__key);
            synchronized (this) {
                scheme = schemeNew;
            	scheme__resolvedKey = __key;
            }
        }
        return scheme;
    }

    public void setScheme(Scheme scheme) {
        synchronized (this) {
            this.scheme = scheme;
            uuid_scheme = scheme == null ? null : scheme.getUuid_scheme();
            scheme__resolvedKey = uuid_scheme;
        }
    }

    /** Convenient call for {@link AbstractDao#delete(Object)}. Entity must attached to an entity context. */
    public void delete() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.delete(this);
    }

    /** Convenient call for {@link AbstractDao#update(Object)}. Entity must attached to an entity context. */
    public void update() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.update(this);
    }

    /** Convenient call for {@link AbstractDao#refresh(Object)}. Entity must attached to an entity context. */
    public void refresh() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.refresh(this);
    }

}
