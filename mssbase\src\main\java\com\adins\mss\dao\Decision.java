package com.adins.mss.dao;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "MS_TMP_RULE".
 */
public class Decision {

     @SerializedName("id")
    private long id;
     @SerializedName("code")
    private String code;
     @SerializedName("value")
    private String value;
     @SerializedName("columnNo")
    private Integer column_no;
     @SerializedName("rowNo")
    private Integer row_no;
     @SerializedName("objName")
    private String obj_name;

    public Decision() {
    }

    public Decision(long id) {
        this.id = id;
    }

    public Decision(long id, String code, String value, Integer column_no, Integer row_no, String obj_name) {
        this.id = id;
        this.code = code;
        this.value = value;
        this.column_no = column_no;
        this.row_no = row_no;
        this.obj_name = obj_name;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Integer getColumn_no() {
        return column_no;
    }

    public void setColumn_no(Integer column_no) {
        this.column_no = column_no;
    }

    public Integer getRow_no() {
        return row_no;
    }

    public void setRow_no(Integer row_no) {
        this.row_no = row_no;
    }

    public String getObj_name() {
        return obj_name;
    }

    public void setObj_name(String obj_name) {
        this.obj_name = obj_name;
    }

}
