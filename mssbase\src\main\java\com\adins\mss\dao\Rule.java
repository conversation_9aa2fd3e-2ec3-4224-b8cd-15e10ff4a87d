package com.adins.mss.dao;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "MS_RULES".
 */
public class Rule {

     @SerializedName("id")
    private long id;
     @SerializedName("isDeleted")
    private int is_deleted;
    /** Not-null value. */
     @SerializedName("ruleName")
    private String rule_name;
    /** Not-null value. */
     @SerializedName("url")
    private String url;
     @SerializedName("dtmUpd")
    private java.util.Date dtm_upd;

    public Rule() {
    }

    public Rule(long id) {
        this.id = id;
    }

    public Rule(long id, int is_deleted, String rule_name, String url, java.util.Date dtm_upd) {
        this.id = id;
        this.is_deleted = is_deleted;
        this.rule_name = rule_name;
        this.url = url;
        this.dtm_upd = dtm_upd;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getIs_deleted() {
        return is_deleted;
    }

    public void setIs_deleted(int is_deleted) {
        this.is_deleted = is_deleted;
    }

    /** Not-null value. */
    public String getRule_name() {
        return rule_name;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setRule_name(String rule_name) {
        this.rule_name = rule_name;
    }

    /** Not-null value. */
    public String getUrl() {
        return url;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUrl(String url) {
        this.url = url;
    }

    public java.util.Date getDtm_upd() {
        return dtm_upd;
    }

    public void setDtm_upd(java.util.Date dtm_upd) {
        this.dtm_upd = dtm_upd;
    }

}
