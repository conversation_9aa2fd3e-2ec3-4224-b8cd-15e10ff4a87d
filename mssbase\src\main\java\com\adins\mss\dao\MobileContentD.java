package com.adins.mss.dao;

import com.adins.mss.dao.DaoSession;
import de.greenrobot.dao.DaoException;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "TR_MOBILECONTENT_D".
 */
public class MobileContentD {

    /** Not-null value. */
     @SerializedName("uuid_mobile_content_d")
    private String uuid_mobile_content_d;
     @SerializedName("menu_id")
    private String menu_id;
     @SerializedName("content")
    private byte[] content;
     @SerializedName("content_type")
    private String content_type;
     @SerializedName("sequence")
    private Integer sequence;
     @SerializedName("usr_crt")
    private String usr_crt;
     @SerializedName("dtm_crt")
    private java.util.Date dtm_crt;
     @SerializedName("usr_upd")
    private String usr_upd;
     @SerializedName("uuid_mobile_content_h")
    private String uuid_mobile_content_h;
     @SerializedName("start_date")
    private java.util.Date start_date;
     @SerializedName("end_date")
    private java.util.Date end_date;

    /** Used to resolve relations */
    private transient DaoSession daoSession;

    /** Used for active entity operations. */
    private transient MobileContentDDao myDao;

    private MobileContentH mobileContentH;
    private String mobileContentH__resolvedKey;


    public MobileContentD() {
    }

    public MobileContentD(String uuid_mobile_content_d) {
        this.uuid_mobile_content_d = uuid_mobile_content_d;
    }

    public MobileContentD(String uuid_mobile_content_d, String menu_id, byte[] content, String content_type, Integer sequence, String usr_crt, java.util.Date dtm_crt, String usr_upd, String uuid_mobile_content_h, java.util.Date start_date, java.util.Date end_date) {
        this.uuid_mobile_content_d = uuid_mobile_content_d;
        this.menu_id = menu_id;
        this.content = content;
        this.content_type = content_type;
        this.sequence = sequence;
        this.usr_crt = usr_crt;
        this.dtm_crt = dtm_crt;
        this.usr_upd = usr_upd;
        this.uuid_mobile_content_h = uuid_mobile_content_h;
        this.start_date = start_date;
        this.end_date = end_date;
    }

    /** called by internal mechanisms, do not call yourself. */
    public void __setDaoSession(DaoSession daoSession) {
        this.daoSession = daoSession;
        myDao = daoSession != null ? daoSession.getMobileContentDDao() : null;
    }

    /** Not-null value. */
    public String getUuid_mobile_content_d() {
        return uuid_mobile_content_d;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setUuid_mobile_content_d(String uuid_mobile_content_d) {
        this.uuid_mobile_content_d = uuid_mobile_content_d;
    }

    public String getMenu_id() {
        return menu_id;
    }

    public void setMenu_id(String menu_id) {
        this.menu_id = menu_id;
    }

    public byte[] getContent() {
        return content;
    }

    public void setContent(byte[] content) {
        this.content = content;
    }

    public String getContent_type() {
        return content_type;
    }

    public void setContent_type(String content_type) {
        this.content_type = content_type;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public String getUsr_crt() {
        return usr_crt;
    }

    public void setUsr_crt(String usr_crt) {
        this.usr_crt = usr_crt;
    }

    public java.util.Date getDtm_crt() {
        return dtm_crt;
    }

    public void setDtm_crt(java.util.Date dtm_crt) {
        this.dtm_crt = dtm_crt;
    }

    public String getUsr_upd() {
        return usr_upd;
    }

    public void setUsr_upd(String usr_upd) {
        this.usr_upd = usr_upd;
    }

    public String getUuid_mobile_content_h() {
        return uuid_mobile_content_h;
    }

    public void setUuid_mobile_content_h(String uuid_mobile_content_h) {
        this.uuid_mobile_content_h = uuid_mobile_content_h;
    }

    public java.util.Date getStart_date() {
        return start_date;
    }

    public void setStart_date(java.util.Date start_date) {
        this.start_date = start_date;
    }

    public java.util.Date getEnd_date() {
        return end_date;
    }

    public void setEnd_date(java.util.Date end_date) {
        this.end_date = end_date;
    }

    /** To-one relationship, resolved on first access. */
    public MobileContentH getMobileContentH() {
        String __key = this.uuid_mobile_content_h;
        if (mobileContentH__resolvedKey == null || mobileContentH__resolvedKey != __key) {
            if (daoSession == null) {
                throw new DaoException("Entity is detached from DAO context");
            }
            MobileContentHDao targetDao = daoSession.getMobileContentHDao();
            MobileContentH mobileContentHNew = targetDao.load(__key);
            synchronized (this) {
                mobileContentH = mobileContentHNew;
            	mobileContentH__resolvedKey = __key;
            }
        }
        return mobileContentH;
    }

    public void setMobileContentH(MobileContentH mobileContentH) {
        synchronized (this) {
            this.mobileContentH = mobileContentH;
            uuid_mobile_content_h = mobileContentH == null ? null : mobileContentH.getUuid_mobile_content_h();
            mobileContentH__resolvedKey = uuid_mobile_content_h;
        }
    }

    /** Convenient call for {@link AbstractDao#delete(Object)}. Entity must attached to an entity context. */
    public void delete() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.delete(this);
    }

    /** Convenient call for {@link AbstractDao#update(Object)}. Entity must attached to an entity context. */
    public void update() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.update(this);
    }

    /** Convenient call for {@link AbstractDao#refresh(Object)}. Entity must attached to an entity context. */
    public void refresh() {
        if (myDao == null) {
            throw new DaoException("Entity is detached from DAO context");
        }    
        myDao.refresh(this);
    }

}
