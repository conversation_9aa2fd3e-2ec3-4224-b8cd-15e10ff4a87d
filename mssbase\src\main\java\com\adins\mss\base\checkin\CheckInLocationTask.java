package com.adins.mss.base.checkin;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.os.AsyncTask;
import android.util.Log;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.timeline.TimelineManager;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.LocationInfo;
import com.adins.mss.foundation.dialog.NiftyDialogBuilder;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.image.ImageLoader;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 */
public class CheckInLocationTask extends AsyncTask<Void, Void, String> {
    private static String TAG = "CHECK IN LOCATION";
    LocationInfo locationInfo;
    String result;
    String errMessage = null;
    private ProgressDialog progressDialog;
    private Context context;
    private Activity activity;
    private String messageWait;
    private String messageUnavaiableLocation;
    private String[] address;
    private byte[] bitmapArray;

    /**
     * Inisialisasi Check In Location Task
     *
     * @param activity                  -Context
     * @param msgWait                   - Waiting Message
     * @param messageUnavaiableLocation - message if unavailable
     * @param locationInfo              - Location Info
     * @param address                   - String[] Address
     */
    public CheckInLocationTask(Activity activity, String msgWait, String messageUnavaiableLocation, LocationInfo locationInfo, String[] address) {
        this.activity = activity;
        this.context = activity;
        this.messageWait = msgWait;
        this.messageUnavaiableLocation = messageUnavaiableLocation;
        this.address = address;
        this.locationInfo = locationInfo;
    }

    public CheckInLocationTask(Context activity, String msgWait, String messageUnavaiableLocation, LocationInfo locationInfo, String[] address) {
        this.context = activity;
        this.messageWait = msgWait;
        this.messageUnavaiableLocation = messageUnavaiableLocation;
        this.address = address;
        this.locationInfo = locationInfo;
    }

//	/**
//	 * Inisialisasi Check In Location Task
//	 * @param activity - Fragment Activity
//	 * @param msgWait - Waiting Message
//	 * @param messageUnavaiableLocation - message if unavailable
//	 */
//	public CheckInLocationTask(FragmentActivity activity, String msgWait, String messageUnavaiableLocation) {
//		this.activity = activity;
//		this.messageWait = msgWait;
//		this.messageUnavaiableLocation = messageUnavaiableLocation;
//	}

    @Override
    protected void onPreExecute() {
        progressDialog = ProgressDialog.show(context, "", this.messageWait, true);
    }

    @Override
    protected String doInBackground(Void... params) {
        String lat;
        String lng;
        HttpConnectionResult resultServer = null;
        if (Tool.isInternetconnected(context)) {
            if (locationInfo != null) {
                lat = locationInfo.getLatitude();
                lng = locationInfo.getLongitude();

                List<LocationInfo> list = new ArrayList<LocationInfo>();
                list.add(locationInfo);

                String attdAddress = context.getString(R.string.address_not_found);
                if (address != null)
                    attdAddress = address[1] != null ? address[1] : context.getString(R.string.address_not_found);

                JsonRequestAbsensi requestAbsensi = new JsonRequestAbsensi();
                requestAbsensi.setAudit(GlobalData.getSharedGlobalData().getAuditData());
                requestAbsensi.addImeiAndroidIdToUnstructured();
                requestAbsensi.setLocationInfo(list);
                requestAbsensi.setAttd_address(attdAddress);

                String json = GsonHelper.toJson(requestAbsensi);

//				String data = CheckInManager.toLocationCheckInString(locationInfo);
                try {
                    boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                    boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                    HttpCryptedConnection httpConn = new HttpCryptedConnection(context, encrypt, decrypt);
                    String url = GlobalData.getSharedGlobalData().getURL_GET_ABSENSI();
                    resultServer = httpConn.requestToServer(url, json, Global.DEFAULTCONNECTIONTIMEOUT);

                    result = resultServer.getResult();

                    try {
                        String imageUrl = "https://maps.googleapis.com/maps/api/staticmap?center=" + lat + "," + lng + "&zoom=15&size=720x300&maptype=roadmap&markers=color:green%7Clabel:I%7C" + lat + "," + lng;

                        ImageLoader imgLoader = new ImageLoader(context);
                        Bitmap bitmap = imgLoader.getBitmap(imageUrl);
                        ByteArrayOutputStream stream = new ByteArrayOutputStream();
                        bitmap.compress(Bitmap.CompressFormat.JPEG, 80, stream);

                        bitmapArray = stream.toByteArray();
                    } catch (Exception e) {
                        FireCrash.log(e);

                    }

                    //** debug version
                    if (Global.IS_DEV) {
                        Log.i(TAG, "check in " + locationInfo.toString());
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    if (Global.IS_DEV)
                        e.printStackTrace();
//					System.out.println("cekinLocation "+e);
                    errMessage = context.getString(R.string.msgUnavaibleLocationCheckIn);
                }
            }
        } else {
            errMessage = context.getString(R.string.no_internet_connection);
        }


        return result;
    }

    @Override
    protected void onPostExecute(String result) {
        if (progressDialog.isShowing()) {
            try {
                progressDialog.dismiss();
            } catch (Exception e) {
                FireCrash.log(e);
            }
        }

        if (errMessage != null) {
            Log.i(TAG, "check in " + locationInfo.toString());
            if (errMessage.equals(context.getString(R.string.no_internet_connection))) {
                NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(context);
                dialogBuilder.withTitle(context.getString(R.string.info_capital))
                        .withIcon(android.R.drawable.ic_dialog_info)
                        .withMessage(errMessage)
                        .show();
            } else {
                NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(context);
                dialogBuilder.withTitle(context.getString(R.string.error_capital))
                        .withMessage(R.string.connection_failed)
                        .show();
            }
        } else {
            if (result != null) { // result : {"result":"User Collector 01 successfully check-in on 21-02-2018 18:29:35","status":{"code":0}}
                try {
                    JsonResponseAbsensi response = GsonHelper.fromJson(result, JsonResponseAbsensi.class);
                    int code = response.getStatus().getCode();
                    if (code == 0) {
                        String resultResponse = null;
                        try {
                            resultResponse = response.getResult();
                        } catch (Exception e) {
                            FireCrash.log(e);
                            // TODO: handle exception
                        }
                        if (("1").equalsIgnoreCase(response.getSuccess())) { //success
                            try {
                                String message = "";
                                message = resultResponse;
//                                DateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss");
//                                message = context.getResources().getString(R.string.successfully_check_in, GlobalData.getSharedGlobalData().getUser().getFullname(), dateFormat.format(Calendar.getInstance()));
                                locationInfo.setLocation_type(Global.LOCATION_TYPE_CHECKIN);
                                TimelineManager.insertTimeline(context, locationInfo, message, bitmapArray);

                                CheckInManager.insertLocationCheckInToDB(context, locationInfo);

                                NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(context);
                                dialogBuilder.withTitle(context.getString(R.string.success))
                                        .withMessage(message)
                                        .isCancelable(true)
                                        .show();
                            } catch (Exception e) {
                                try {
                                    CheckInManager.insertLocationCheckInToDB(context, locationInfo);
                                    String message = "";
                                    message = resultResponse;
                                    TimelineManager.insertTimeline(context, locationInfo, message, bitmapArray);
                                } catch (Exception en) {
                                    en.printStackTrace();
                                }
                            }
                        } else if (response.getSuccess().equalsIgnoreCase("0")) {//gagal karena sudah pernah login
//                                DateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss");
//                                message = context.getResources().getString(R.string.already_check_in, GlobalData.getSharedGlobalData().getUser().getFullname(), dateFormat.format(Calendar.getInstance()));
//                            {"result":"User Collector Malang 03 already checked-in, on 2018-02-23 11:08:46","success":"1","status":{"code":0}}
                            NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(context);
                            dialogBuilder.withTitle(context.getString(R.string.info_capital))
                                    .withMessage(resultResponse)
                                    .isCancelable(true)
                                    .show();
                        } else { //gagal karena empty data
                            if (resultResponse == null)
                                resultResponse = context.getString(R.string.empty_data);
                            NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(context);
                            dialogBuilder.withTitle(context.getString(R.string.info_capital))
                                    .withMessage(resultResponse)
                                    .isCancelable(true)
                                    .show();
                        }
//                        if (resultResponse != null && resultResponse.contains("berhasil") || resultResponse.contains("successfully")) {
//                            if (Pattern.compile(Pattern.quote("Tidak berhasil"), Pattern.CASE_INSENSITIVE).matcher(resultResponse).find()) {
//                              gagal
//                            } else {
//                              berhasil
//                            }
//                        } else {
//                           gagal
//                        }
                    } else {
                        NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(context);
                        dialogBuilder.withTitle(context.getString(R.string.warning_capital))
                                .withMessage(response.getStatus().getMessage())
                                .isCancelable(true)
                                .show();
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(context);
                    dialogBuilder.withTitle(context.getString(R.string.error_capital))
                            .withMessage(e.getMessage() + ", " + context.getString(R.string.contact_administrator))
                            .show();
                }
            }
        }
    }

}
