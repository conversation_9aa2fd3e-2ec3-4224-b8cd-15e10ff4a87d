package com.adins.mss.base.todolist.form;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;

import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.fragment.app.FragmentTransaction;
import androidx.viewpager.widget.ViewPager;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.mainmenu.MainMenuActivity;
import com.adins.mss.base.util.Utility;
import com.adins.mss.constant.Global;
import com.adins.mss.foundation.dialog.NiftyDialogBuilder;
import com.github.jjobes.slidedatetimepicker.SlidingTabLayout;

/**
 * Created by gigin.ginanjar on 15/08/2016.
 */
public class TaskListFragment_new extends Fragment {
    public static boolean isMenuClicked = false;
    private static Menu mainMenu;
    View view;
    private Context mContext;
    private ViewPager mViewPager;
    private ViewPagerAdapter mViewPagerAdapter;
    private SlidingTabLayout mSlidingTabLayout;
    private boolean isError = false;
    private int page;
    private String message;
    private String status = "";
    private Fragment activeFragment;

    @Override
    public void onAttach(Context activity) {
        super.onAttach(activity);

        mContext = activity;
        try {
            isError = getArguments().getBoolean(TaskList_Fragment.BUND_KEY_ISERROR, false);
            message = getArguments().getString(TaskList_Fragment.BUND_KEY_MESSAGE, "");
            status = getArguments().getString("status");
            page = getArguments().getInt(TaskList_Fragment.BUND_KEY_PAGE, 0);
        } catch (Exception e) {
            FireCrash.log(e);
        }
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setRetainInstance(true);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        view = inflater.inflate(R.layout.tasklist_fragment_new, container, false);
        setupViews(view);
        initViewPager();
        initTabs();
        setHasOptionsMenu(true);

        return view;
    }

    private void initTabs() {
        // Set intial date on date tab
        updatePriorityTab();

        // Set initial time on time tab
        updateStatusTab();

    }

    @Override
    public void onDestroyView() {
        try {
            mainMenu.findItem(R.id.menuMore).setVisible(false);
        } catch (Exception e) {
            FireCrash.log(e);
        }
        super.onDestroyView();
        Utility.freeMemory();
    }

    private void updateStatusTab() {
        mSlidingTabLayout.setTabText(1, "Status");
    }

    private void updatePriorityTab() {
        mSlidingTabLayout.setTabText(0, "Priority");
    }

    private void initViewPager() {
        mViewPagerAdapter = new ViewPagerAdapter(getChildFragmentManager());
        mViewPager.setAdapter(mViewPagerAdapter);

        try {
            if ("failed".equals(status)) {
                mViewPager.setCurrentItem(mViewPager.getCurrentItem() + 1, true);
            }
        } catch (Exception e) {
            FireCrash.log(e);
        }

        // Setting this custom layout for each tab ensures that the tabs will
        // fill all available horizontal space.

        mSlidingTabLayout.setCustomTabView(R.layout.custom_tab_tasklist, R.id.tabTextTaskList);
        mSlidingTabLayout.setSelectedIndicatorColors(ContextCompat.getColor(getActivity(), R.color.tv_white),
                ContextCompat.getColor(getActivity(), R.color.tv_white));
        mSlidingTabLayout.setDividerColors(ContextCompat.getColor(getActivity(), R.color.tv_white),
                ContextCompat.getColor(getActivity(), R.color.tv_white));
        mSlidingTabLayout.setViewPager(mViewPager);
        if (isError) {
            isError = false;
            NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(getActivity());

            if (!dialogBuilder.isShowing()) {
                dialogBuilder.withTitle("WARNING").
                        withIcon(android.R.drawable.ic_dialog_alert).
                        withMessage(message).isCancelable(true).show();
            }
        }

        if (page == 1) {
            mViewPager.setCurrentItem(1);
        }
    }

    private void setupViews(View v) {
        mViewPager = (ViewPager) v.findViewById(R.id.pager);
        mSlidingTabLayout = (SlidingTabLayout) v.findViewById(R.id.slidingTabLayout);
    }

    @Override
    public void onPrepareOptionsMenu(Menu menu) {
        super.onPrepareOptionsMenu(menu);
        mainMenu = menu;
        menu.findItem(R.id.menuMore).setVisible(true);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // Handle item selection
        if (!isMenuClicked) {
            int id = item.getItemId();
            if (id == R.id.menuMore) {
                mainMenu.findItem(R.id.mnViewAllHeader).setVisible(false);
                String application = GlobalData.getSharedGlobalData().getAuditData().getApplication();
                if (Global.APPLICATION_COLLECTION.equalsIgnoreCase(application)
                        || Global.APPLICATION_SURVEY.equalsIgnoreCase(application))
                    mainMenu.findItem(R.id.mnViewMap).setVisible(true);
                isMenuClicked = false;
            }
            if (id == R.id.mnViewMap) {
                /*Intent intent = new Intent(getActivity().getApplicationContext(),
                        ViewMapActivity.class);
				getActivity().startActivity(intent);*/
                MapsViewerFragment fragment = new MapsViewerFragment();
                FragmentTransaction transaction = MainMenuActivity.fragmentManager.beginTransaction();
                transaction.setCustomAnimations(R.anim.activity_open_translate, R.anim.activity_close_scale, R.anim.activity_open_scale, R.anim.activity_close_translate);
                transaction.replace(R.id.content_frame, fragment);
                transaction.addToBackStack(null);
                transaction.commit();
                isMenuClicked = true;
            } else if (id == R.id.mnViewAllHeader) {
                /*Intent intent = new Intent(getActivity().getApplicationContext(),
                        AllHeaderViewerActivity.class);
				intent.putExtra(AllHeaderViewerActivity.BUND_KEY_REQ, AllHeaderViewerActivity.REQ_PRIORITY_LIST);
				startActivity(intent);*/
                AllHeaderViewerFragment viewerFragment = AllHeaderViewerFragment.newInstance(AllHeaderViewerFragment.REQ_PRIORITY_LIST);
                FragmentTransaction transaction = MainMenuActivity.fragmentManager.beginTransaction();
                transaction.setCustomAnimations(R.anim.activity_open_translate, R.anim.activity_close_scale, R.anim.activity_open_scale, R.anim.activity_close_translate);
                transaction.replace(R.id.content_frame, viewerFragment);
                transaction.addToBackStack(null);
                transaction.commit();
                isMenuClicked = true;
            }

        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onResume() {
        super.onResume();
        isMenuClicked = false;
        getActivity().getActionBar().setTitle(getString(R.string.title_mn_tasklist));
        Utility.freeMemory();
        MainMenuActivity.setDrawerPosition(getString(R.string.title_mn_tasklist));
//        if(view!=null) {
//            initViewPager();
//            initTabs();
//        }
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
    }

    private class ViewPagerAdapter extends FragmentPagerAdapter {
        public ViewPagerAdapter(FragmentManager fm) {
            super(fm);
        }

        @Override
        public Fragment getItem(int position) {
            Fragment fragment = null;
            switch (position) {
                case 0:
                    fragment = new PriorityTabFragment();
                    break;
                case 1:
                    fragment = new StatusTabFragment();
                    break;
                default:
                    break;
            }
            return fragment;
        }

        @Override
        public int getCount() {
            return 2;
        }
    }
}
