package com.adins.mss.base.dynamicform;

import com.adins.mss.foundation.http.MssRequestType;
import com.google.gson.annotations.SerializedName;

public class JsonRequestRejectedWithResurvey extends MssRequestType {
    /**
     * Property uuid_task_h
     */
    @SerializedName("uuid_task_h")
    String uuid_task_h;

    /**
     * Property flag
     */
    @SerializedName("flag")
    String flag;

    /**
     * Property uuid_user
     */
    @SerializedName("uuid_user")
    String uuid_user;

    /**
     * Property uuid_ms_user
     */
    @SerializedName("uuid_ms_user")
    String uuid_ms_user;

    /**
     * Property is_suggested
     */
    @SerializedName("is_suggested")
    String is_suggested;

    /**
     * Property notes
     */
    @SerializedName("notes")
    String notes;

    /**
     * Gets the uuid_ms_user
     */
    public String getUuid_ms_user() {
        return this.uuid_ms_user;
    }

    /**
     * Sets the uuid_ms_user
     */
    public void setUuid_ms_user(String value) {
        this.uuid_ms_user = value;
    }

    /**
     * Gets the is_suggested
     */
    public String getIs_suggested() {
        return this.is_suggested;
    }

    /**
     * Sets the is_suggested
     */
    public void setIs_suggested(String value) {
        this.is_suggested = value;
    }

    /**
     * Gets the notes
     */
    public String getNotes() {
        return this.notes;
    }

    /**
     * Sets the notes
     */
    public void setNotes(String value) {
        this.notes = value;
    }

    /**
     * Gets the uuid_task_h
     */
    public String getUuid_task_h() {
        return this.uuid_task_h;
    }

    /**
     * Sets the uuid_task_h
     */
    public void setUuid_task_h(String value) {
        this.uuid_task_h = value;
    }

    /**
     * Gets the flag
     */
    public String getFlag() {
        return this.flag;
    }

    /**
     * Sets the flag
     */
    public void setFlag(String value) {
        this.flag = value;
    }

    /**
     * Gets the uuid_user
     */
    public String getUuid_user() {
        return this.uuid_user;
    }

    /**
     * Sets the uuid_user
     */
    public void setUuid_user(String value) {
        this.uuid_user = value;
    }
}