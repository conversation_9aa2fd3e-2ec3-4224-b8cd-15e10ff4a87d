package com.adins.mss.base.dynamicform.form.questions.viewholder;

import android.app.ProgressDialog;
import android.os.AsyncTask;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.dynamicform.Constant;
import com.adins.mss.base.dynamicform.form.FragmentQuestion;
import com.adins.mss.base.dynamicform.form.models.LayerBean;
import com.adins.mss.base.dynamicform.form.models.LayerRequest;
import com.adins.mss.base.dynamicform.form.models.LayerResponse;
import com.adins.mss.base.dynamicform.form.models.SubmitLayerBean;
import com.adins.mss.base.timeline.TimelineManager;
import com.adins.mss.base.util.EventBusHelper;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.LocationInfo;
import com.adins.mss.dao.Lookup;
import com.adins.mss.dao.Message;
import com.adins.mss.dao.TaskD;
import com.adins.mss.dao.TaskH;
import com.adins.mss.foundation.db.dataaccess.LookupDataAccess;
import com.adins.mss.foundation.db.dataaccess.MessageDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskHDataAccess;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.questiongenerator.OptionAnswerBean;
import com.adins.mss.foundation.questiongenerator.QuestionBean;
import com.adins.mss.logger.Logger;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class SubmitLayerQuestionViewHolder extends RecyclerView.ViewHolder{

    private final TextView mQuestionLabel;
    private final Button btnSubmitLayer;
    private final TextView mQuestionAnswer;
    private final FragmentActivity mActivity;
    private QuestionBean bean;
    private LinkedHashMap<String, List<QuestionBean>> listQuestionOfGroup;
    Map <String, QuestionBean> listSubmitLayerQuestion;
    private List<QuestionBean> listQuestionBean;
    private TaskH taskH;
    private SubmitLayerBean submitLayerBean;
    private Map<String, Map<String, String>> filters;
    private String errorMessage;


    public SubmitLayerQuestionViewHolder(View view, FragmentActivity mActivity, SubmitLayerBean bean) {
        super(view);
        mQuestionLabel = (TextView) itemView.findViewById(R.id.questionLayerLabel);
        btnSubmitLayer = (Button) itemView.findViewById(R.id.btnSubmitLayer);
        mQuestionAnswer = (TextView) itemView.findViewById(R.id.questionTvLayerAnswer);
        this.mActivity = mActivity;
        submitLayerBean = bean;
        filters = new HashMap();
    }

    public void bind(final QuestionBean item, int number) {
        bean = item;
        listSubmitLayerQuestion = new HashMap<>();
        setData(submitLayerBean);
        String qLabel = number + ". " + bean.getQuestion_label();
        mQuestionLabel.setText(qLabel);
        String qAnswer = bean.getAnswer();
        showAnswer(qAnswer);
        onClickHandler();
        for (QuestionBean bean : listQuestionBean) {
            //Create List QBean Button Send Each Layer
            if (Global.AT_SUBMIT_LAYER.equals(bean.getAnswer_type())) {
                listSubmitLayerQuestion.put(bean.getQuestion_group_id(), bean);
            }
        }
        if (bean.getCountRetry() >= bean.getMaxRetry()) {
            btnSubmitLayer.setEnabled(false);
            btnSubmitLayer.setText("Maximum retry has been reached");
        } else {
            btnSubmitLayer.setEnabled(true);
            btnSubmitLayer.setText("Submit");
        }
    }

    private void onClickHandler() {
        btnSubmitLayer.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
            bean.setCountRetry(bean.getCountRetry()+1);
            LayerRequest layerRequest = createPOJO();
            SubmitLayerTask task = new SubmitLayerTask(mActivity, layerRequest);
            task.execute();
            if (bean.getCountRetry() >= bean.getMaxRetry()) {
                btnSubmitLayer.setEnabled(false);
                btnSubmitLayer.setText("Maximum retry has been reached");
            } else {
                btnSubmitLayer.setEnabled(true);
                btnSubmitLayer.setText("Submit");
            }
            }
        });
    }

    private void showAnswer(String qAnswer) {
        if (null != qAnswer && !qAnswer.isEmpty()) {
            mQuestionAnswer.setVisibility(View.VISIBLE);
            mQuestionAnswer.setText(qAnswer);
        } else {
            mQuestionAnswer.setVisibility(View.GONE);
        }

    }
    private LayerRequest createPOJO() {
        List<LayerBean> layerBeanList = new ArrayList<>();
        int maxSeqOrder = 0;
        int currentReqSeqOrder = bean.getQuestion_group_order();
        for (String uuidQuestionGroup : listSubmitLayerQuestion.keySet()) {
            QuestionBean btnSubmit = listSubmitLayerQuestion.get(uuidQuestionGroup);
            int currentOrder = btnSubmit.getQuestion_group_order();

            if (maxSeqOrder < currentOrder) {
                maxSeqOrder = currentOrder;
            }

            List<TaskD> listTaskD = convertTaskD(listQuestionOfGroup.get(btnSubmit.getQuestion_group_name()));
            List<TaskD> excludeTaskDs = new ArrayList<>();
            for (TaskD taskD2 : listTaskD) {
                if (taskD2.getText_answer() != null &&
                        taskD2.getText_answer().equalsIgnoreCase("Pilih Salah Satu")) {
                    excludeTaskDs.add(taskD2);
                }
            }

            for (TaskD excludeTask : excludeTaskDs) {
                listTaskD.remove(excludeTask);
            }
            LayerBean layerBean = new LayerBean();
            layerBean.setUuidQuestionGroup(uuidQuestionGroup);
            layerBean.setSeqNo(String.valueOf(btnSubmit.getQuestion_group_order()));
            layerBean.setTaskDList(listTaskD);
            layerBeanList.add(layerBean);

            //create map values
            Map mapKeyValue = new HashMap();
            if(btnSubmit.getChoice_filter()!=null && btnSubmit.getChoice_filter().length()>0) {
                String[] refIdList = btnSubmit.getChoice_filter().split(",");
                if (refIdList!=null && refIdList.length>0) {
                    for(int i=0; i<refIdList.length; i++) {
                        String identifier = refIdList[i];
                        identifier = identifier.replace("{", "");
                        identifier = identifier.replace("}", "");
                        QuestionBean bean = Constant.listOfQuestion.get(identifier);
                        mapKeyValue.put(bean.getIdentifier_name(), QuestionBean.getAnswer(bean));
                    }
                }
            }

            filters.put(uuidQuestionGroup, mapKeyValue);
        }

        Collections.sort(layerBeanList, new Comparator<LayerBean>() {
            @Override
            public int compare(LayerBean o1, LayerBean o2) {
              return (Integer.parseInt(o1.getSeqNo()) - Integer.parseInt(o2.getSeqNo()));
            }
        });

        LayerRequest layerRequest = new LayerRequest();
        layerRequest.setLayerBeanList(layerBeanList);
        layerRequest.setTaskH(taskH);
        layerRequest.setFilter(filters);
        layerRequest.setCountRetry(String.valueOf(bean.getCountRetry()));

        if (currentReqSeqOrder < maxSeqOrder) {
            layerRequest.setUuidQuestionGroupProcess(bean.getQuestion_group_id());
        }
        return layerRequest;
    }



    public void setData(SubmitLayerBean bean) {
        Logger.i(this, "data : UUID TASK H = " + bean.getTaskH().getUuid_task_h());
        if (null != bean) {
            this.taskH = bean.getTaskH();
            this.listQuestionOfGroup = bean.getQuestionBeanListOfGroup();
            this.listQuestionBean = bean.getQuestionBeans();
        }
    }

    private class SubmitLayerTask extends AsyncTask<Void, Void, String> {
        final String TAG_FAILED = "FAILED";
        final String TAG_ERROR = "ERROR";
        ProgressDialog dialog;
        private final FragmentActivity context;
        private final LayerRequest requestBean;
        private String errMsg;

        public SubmitLayerTask(FragmentActivity mActivity, LayerRequest layerRequest) {
            this.context = mActivity;
            this.requestBean = layerRequest;
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            dialog = ProgressDialog.show(mActivity, "",
                    mActivity.getString(R.string.progressWait), true, false);
        }

        @Override
        protected String doInBackground(Void... voids) {
            try {
                if (Tool.isInternetconnected(context)) {
                    HttpConnectionResult serverResult;
                    try {
                        requestBean.setAudit(GlobalData.getSharedGlobalData().getAuditData());
                        serverResult = request();
                    } catch (Exception e) {
                        e.printStackTrace();
                        errMsg = e.getMessage();
                        return errMsg;
                    }
                    if (serverResult != null) {
                        if (serverResult.isOK()) {
                            try {
                                return serverResult.getResult();
                            } catch (Exception e) {
                                errMsg = e.getMessage();
                                return errMsg;
                            }
                        } else {
                            errMsg = serverResult.getResult();
                            return errMsg;
                        }
                    }
                } else {
                    errMsg = context.getString(R.string.no_internet_connection);
                    return errMsg;
                }
            } catch (Exception exception) {
                exception.printStackTrace();
            }

            return null;
        }

        @Override
        protected void onPostExecute(String results) {
            super.onPostExecute(results);
            dismissDialog(dialog);
            if (results.equals(mActivity.getString(R.string.no_internet_connection))) {
                Toast.makeText(mActivity, mActivity.getString(R.string.no_internet_connection), Toast.LENGTH_SHORT).show();
                errMsg = TAG_ERROR;
            }
            if (null == errMsg) {
                LayerResponse response = GsonHelper.fromJson(results, LayerResponse.class);
                processResponse(response);
            } else {
                mQuestionAnswer.setText(errMsg);
                bean.setAnswer(errMsg);
                bean.setIntTextAnswer(errMsg);
                mQuestionAnswer.setVisibility(View.VISIBLE);
            }

        }

        private void processResponse(LayerResponse response) {
            if (response.getStatus().getCode() != 0) {
                bean.setAnswer(response.getStatus().getMessage());
                mQuestionAnswer.setText(response.getStatus().getMessage());
            } else if (response.getStatus().getMessage().toLowerCase()
                    .contains(TAG_FAILED.toLowerCase())) {
                Message message = new Message();
                message.setUuid_message(Tool.getUUID());
                message.setDtm_crt(Tool.getSystemDate());
                message.setMessage(taskH.getTask_id() + "Telah dihapus karena \n"
                        +response.getStatus().getMessage());
                MessageDataAccess.add(mActivity, message);
                TimelineManager.insertTimeline(context, message);

            } else {
                bean.setAnswer(response.getStatus().getMessage());
                mQuestionAnswer.setText(response.getStatus().getMessage());
                if (null != response.getObjectHashMap()) {
                    processHashMap(response.getObjectHashMap());
                }
                if (null != response.getObjectHashMapReadOnly()) {
                    processHashMapReadOnly(response.getObjectHashMapReadOnly());
                }
            }
        }

        private void processHashMap(HashMap<String, Object> result) {
            StringBuilder message = new StringBuilder();
            for (Map.Entry<String, Object> mapElement : result.entrySet()) {
                String refId = mapElement.getKey();
                String answer = mapElement.getValue().toString();
                setAnswer(refId, answer);

                if (answer.toLowerCase().contains(TAG_FAILED.toLowerCase())) {
                    if (bean.getCountRetry() >= bean.getMaxRetry()) {
                        message.append("Drop Task ");
                        message.append(taskH.getAppl_no());
                        message.append(" dikarenakan oleh ");
                        QuestionBean bean = Constant.listOfQuestion.get(refId);
                        message.append(bean.getQuestion_label());
                        message.append(" - ");
                        message.append(answer);
                    } else {
                        int count = bean.getMaxRetry() - bean.getCountRetry();
                        message.append("Task ");
                        message.append(taskH.getAppl_no());
                        message.append(" - ");
                        message.append(answer);
                        message.append(". Silahkan coba lagi, anda masih mempunyai kesempatan " + count + " kali");
                    }
                }
            }

            //drop task
            if (message.toString() != null && message.toString().length()>0) {
                if (bean.getCountRetry() >= bean.getMaxRetry()) {
                    taskH.setMessage(message.toString());
                    taskH.setStatus(TaskHDataAccess.STATUS_SEND_DROP);
                    TaskHDataAccess.update(mActivity, taskH);
                    TimelineManager.insertTimeline(mActivity, taskH);
                } else {
                    android.os.Message messageIsChange = new android.os.Message();
                    Bundle bundle = new Bundle();
                    bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.RESULT_FROM_SUBMITLAYER);
                    bundle.putBoolean("isChange", true);
                    messageIsChange.setData(bundle);
                    FragmentQuestion.questionHandler.sendMessage(messageIsChange);
                }
                errorMessage = message.toString();
                EventBusHelper.post(SubmitLayerQuestionViewHolder.this);
            } else {
                android.os.Message messageIsChange = new android.os.Message();
                Bundle bundle = new Bundle();
                bundle.putInt(FragmentQuestion.BUND_KEY_ACTION, FragmentQuestion.RESULT_FROM_SUBMITLAYER);
                bundle.putBoolean("isChange", true);
                messageIsChange.setData(bundle);
                FragmentQuestion.questionHandler.sendMessage(messageIsChange);
            }
        }

        private void processHashMapReadOnly(HashMap<String, Object> resultReadOnly) {
            for (Map.Entry<String, Object> mapElement : resultReadOnly.entrySet()) {
                String refId = mapElement.getKey();
                String answer = mapElement.getValue().toString();
                setReadOnly(refId, answer);
            }
        }

        private void setReadOnly(String refId, String statusReadOnly) {
            QuestionBean beans = Constant.listOfQuestion.get(refId);
            beans.setReadOnly(!statusReadOnly.equals("0"));
        }

        private void setAnswer(String refId, String answer) {
            QuestionBean beans = Constant.listOfQuestion.get(refId);
            beans.setIntTextAnswer(answer);
            beans.setAnswer(answer);
            if (bean.getIdentifier_name().equalsIgnoreCase(beans.getIdentifier_name())) {
                mQuestionAnswer.setText(beans.getAnswer());
                mQuestionAnswer.setVisibility(View.VISIBLE);
            }
        }

        private HttpConnectionResult request() {
            String json = GsonHelper.toJson(requestBean);
            String url = GlobalData.getSharedGlobalData().getURL_SUBMIT_LAYER();
            boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
            boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
            HttpCryptedConnection httpConn = new HttpCryptedConnection(mActivity,
                    encrypt, decrypt);
            HttpConnectionResult httpConnectionResult = null;
            try {
                httpConnectionResult = httpConn.requestToServer(url, json,
                        Global.DEFAULTCONNECTIONTIMEOUT);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return httpConnectionResult;
        }
        private void dismissDialog(ProgressDialog dialog) {
            if (dialog.isShowing()) {
                dialog.dismiss();
            }
        }
    }

    //Create List TaskD
    public List<TaskD> convertTaskD(List<QuestionBean> beans) {
        String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        Date dtmCrt = new Date(System.currentTimeMillis());
        List<TaskD> listTaskD = new ArrayList<>();
        for (QuestionBean beanDetail : beans) {
            if (Tool.isOptions(beanDetail.getAnswer_type())) {
                listTaskD.addAll(listTaskDLOV(beanDetail, uuidUser, dtmCrt));
            } else {
                listTaskD.add(listTaskD(beanDetail, uuidUser,dtmCrt));
            }
        }
        return listTaskD;
    }
    private List<TaskD> listTaskDLOV(QuestionBean beanDetail, String uuidUser, Date dtmCrt) {
        List<TaskD> listTaskDs = new ArrayList<>();
        try {
            List<OptionAnswerBean> optAnsBean = beanDetail.getSelectedOptionAnswers();
            String answer = beanDetail.getAnswer();
            String[] finalAnswer = new String[1];
            if (answer != null && answer.length() > 0) {
                finalAnswer = Tool.split(answer, Global.DELIMETER_DATA);
            } else {
                finalAnswer[0] = "";
            }
            int j = 0;
            if (optAnsBean.isEmpty()) {
                OptionAnswerBean opt = new OptionAnswerBean("", "");
                optAnsBean.add(opt);
            }
            for (OptionAnswerBean selectedOption : optAnsBean) {
                TaskD taskD = new TaskD(Tool.getUUID());
                taskD.setQuestion_group_id(beanDetail.getQuestion_group_id());
                taskD.setQuestion_id(beanDetail.getQuestion_id());

                taskD.setImage(beanDetail.getImgAnswer());
                taskD.setLov(beanDetail.getLovId());
                taskD.setUsr_crt(uuidUser);
                taskD.setDtm_crt(dtmCrt);
                taskD.setUuid_task_h(taskH.getUuid_task_h());
                taskD.setQuestion_label(beanDetail.getQuestion_label());
                taskD.setIs_visible(Global.TRUE_STRING);
                taskD.setRegex(beanDetail.getRegex());
                taskD.setIs_readonly(beanDetail.getIs_readonly());

                String lookUpId = selectedOption.getUuid_lookup();
                String lovCode = selectedOption.getCode();
                String lovGroup = selectedOption.getLov_group();
                //Nendi: Add AT_028

                boolean isDbSource = false;
                if (beanDetail.getAnswer_type().equalsIgnoreCase(Global.AT_LOOKUP_TABLE) ||
                        beanDetail.getAnswer_type().equalsIgnoreCase(Global.AT_TEXT_WITH_SUGGESTION_NEW)) {
                    isDbSource = true;
                }

                if (lookUpId != null && lovCode != null && !isDbSource) {
                    Lookup lookup = LookupDataAccess.getOne(mActivity, lookUpId, lovGroup);
                    OptionAnswerBean selectedOption2 = new OptionAnswerBean(lookup);
                    selectedOption2.setSelected(true);
                    if (beanDetail.getTag() != null && beanDetail.getTag().equalsIgnoreCase("Job MH")) {
                        taskD.setOption_answer_id(selectedOption2.getCode());
                        taskD.setTag(bean.getTag());
                    } else {
                        taskD.setOption_answer_id(selectedOption2.getUuid_lookup());
                    }
                    taskD.setUuid_lookup(selectedOption2.getUuid_lookup());
                } else { //Nendi: 25/01/2018 | Add exception for AT_028
                    taskD.setText_answer(selectedOption.getValue());
                    taskD.setUuid_lookup(selectedOption.getOption_id());
                }
                taskD.setLov(lovCode);

                if (Tool.isOptionsWithDescription(bean.getAnswer_type())) {
                    taskD.setText_answer(finalAnswer[j]);
                } else if (Global.AT_MULTIPLE_ONE_DESCRIPTION.equals(bean.getAnswer_type()) ||
                        Global.AT_DROPDOWN_W_DESCRIPTION.equals(bean.getAnswer_type()) ||
                        Global.AT_TEXT_WITH_SUGGESTION.equals(bean.getAnswer_type()) ||
                        Global.AT_TEXT_WITH_SUGGESTION_NEW.equals(bean.getAnswer_type()))
                    taskD.setText_answer(bean.getAnswer());
                listTaskDs.add(taskD);
                j++;
            }
        } catch (Exception e) {
            FireCrash.log(e);
        }
        return listTaskDs;
    }
    private TaskD listTaskD(QuestionBean beanDetail, String uuidUser, Date dtmCrt) {
        TaskD taskD = new TaskD(Tool.getUUID());
        taskD.setQuestion_group_id(beanDetail.getQuestion_group_id());
        taskD.setQuestion_id(beanDetail.getQuestion_id());
        taskD.setText_answer(beanDetail.getAnswer());
        taskD.setImage(beanDetail.getImgAnswer());

        taskD.setLov(beanDetail.getLovId());
        taskD.setUsr_crt(uuidUser);
        taskD.setDtm_crt(dtmCrt);
        taskD.setUuid_task_h(taskH.getUuid_task_h());
        taskD.setQuestion_label(beanDetail.getQuestion_label());
        if (beanDetail.isVisible()) {
            taskD.setIs_visible(Global.TRUE_STRING);
        } else {
            taskD.setIs_visible(Global.FALSE_STRING);
        }
        if (beanDetail.getAnswer_type().equals(Global.AT_GPS) ||
                beanDetail.getAnswer_type().equals(Global.AT_GPS_N_LBS) ||
                beanDetail.getAnswer_type().equals(Global.AT_LOCATION) ||
                beanDetail.getAnswer_type().equals(Global.AT_IMAGE_W_GPS_ONLY) ||
                beanDetail.getAnswer_type().equals(Global.AT_IMAGE_W_LOCATION)) {
            try {
                LocationInfo info = beanDetail.getLocationInfo();
                taskD.setLatitude(info.getLatitude());
                taskD.setLongitude(info.getLongitude());
                taskD.setCid(info.getCid());
                taskD.setMcc(info.getMcc());
                taskD.setMnc(info.getMnc());
                taskD.setLac(info.getLac());
                taskD.setAccuracy(info.getAccuracy());
                taskD.setGps_time(info.getGps_time());
                taskD.setLocation_image(beanDetail.getImgLocation());
            } catch (Exception e) {
                FireCrash.log(e);

            }
        }
        taskD.setRegex(beanDetail.getRegex());
        taskD.setIs_readonly(beanDetail.getIs_readonly());
        return taskD;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
