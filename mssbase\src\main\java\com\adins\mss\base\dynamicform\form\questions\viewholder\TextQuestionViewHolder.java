package com.adins.mss.base.dynamicform.form.questions.viewholder;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.Intent;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputFilter;
import android.text.InputType;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.RequiresApi;
import androidx.recyclerview.widget.RecyclerView;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.commons.Helper;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.dynamicform.Constant;
import com.adins.mss.base.dynamicform.form.FragmentQuestion;
import com.adins.mss.base.dynamicform.form.ScrollingLinearLayoutManager;
import com.adins.mss.base.dynamicform.form.questions.OnQuestionClickListener;
import com.adins.mss.base.pdfrenderer.ViewPdfRendererFragment;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.GeneralParameter;
import com.adins.mss.foundation.db.dataaccess.GeneralParameterDataAccess;
import com.adins.mss.foundation.dialog.NiftyDialogBuilder;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.MssResponseType;
import com.adins.mss.foundation.questiongenerator.QuestionBean;
import com.adins.mss.foundation.questiongenerator.form.QuestionView;

import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.NumberFormat;
import java.util.Locale;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

/**
 * Created by gigin.ginanjar on 31/08/2016.
 */
public class TextQuestionViewHolder extends RecyclerView.ViewHolder {
    public QuestionView mView;
    public TextView mQuestionLabel;
    public EditText mQuestionAnswer;
    public QuestionBean bean;
    private String tempText = "";
    private TextView mQuestionTvAnswer;
    private RecyclerView mRecyclerView;
    private Activity mActivity;
    private Button mButtonView;
    private OnQuestionClickListener mListener;
    private String txtBefore = "";
    private String txtNow = "";

    // Variable for penjagaan tidak terpanggil kondisi 1A dan 1B. (worst case)
    private int times = 0;

    public TextQuestionViewHolder(View itemView) {
        super(itemView);
        mView = (QuestionView) itemView.findViewById(R.id.questionTextLayout);
        mQuestionLabel = (TextView) itemView.findViewById(R.id.questionTextLabel);
        mQuestionAnswer = (EditText) itemView.findViewById(R.id.questionTextAnswer);
    }

    public TextQuestionViewHolder(View itemView, RecyclerView recyclerView) {
        super(itemView);
        mRecyclerView = recyclerView;
        mView = (QuestionView) itemView.findViewById(R.id.questionTextLayout);
        mQuestionLabel = (TextView) itemView.findViewById(R.id.questionTextLabel);
        mQuestionAnswer = (EditText) itemView.findViewById(R.id.questionTextAnswer);
        mQuestionTvAnswer = (TextView) itemView.findViewById(R.id.questionTvTextAnswer);
    }

    public TextQuestionViewHolder(Activity context, View itemView, RecyclerView recyclerView, OnQuestionClickListener listener) {
        super(itemView);
        mRecyclerView = recyclerView;
        mView = (QuestionView) itemView.findViewById(R.id.questionTextLayout);
        mQuestionLabel = (TextView) itemView.findViewById(R.id.questionTextLabel);
        mQuestionAnswer = (EditText) itemView.findViewById(R.id.questionTextAnswer);
        mQuestionTvAnswer = (TextView) itemView.findViewById(R.id.questionTvTextAnswer);
        mButtonView = (Button) itemView.findViewById(R.id.questionBtnViewPdf);
        mActivity = context;
        mListener = listener;
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public void bind(final QuestionBean item, final int group, int number) {
        bean = item;
        final String answerType = bean.getAnswer_type();
        String qLabel = number + ". " + bean.getQuestion_label();
        mQuestionLabel.setText(qLabel);

        //Nendi: 23/01/2018 | Add Param UI width
        if (bean.getMax_length() <= 5) {
            int width = Tool.getDisplay(mActivity, Tool.DISPLAY_WIDTH);
            LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams((int)(width/2), LinearLayout.LayoutParams.WRAP_CONTENT);
            mQuestionAnswer.setLayoutParams(lp);
        } else {
            LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
            mQuestionAnswer.setLayoutParams(lp);
        }

        InputFilter[] inputFilters = { new InputFilter.LengthFilter(
                bean.getMax_length()) };

        mQuestionAnswer.setFilters(inputFilters);

        String qAnswer = bean.getAnswer();
        if (qAnswer != null && !qAnswer.isEmpty()) {
            qAnswer = qAnswer.replaceAll("<br/>", "\n");
            mQuestionAnswer.setText(qAnswer);
        } else {
            mQuestionAnswer.setText(null);
            bean.setAnswer("");
        }
        mQuestionAnswer.setSingleLine(true);
        if (Global.AT_DECIMAL.equals(answerType)) {
            if (qAnswer != null) {
                try {
                    NumberFormat nf = NumberFormat.getInstance(Locale.US);
                    Double finalAnswer = nf.parse(qAnswer).doubleValue();
//                    mQuestionAnswer.setText(finalAnswer.toString());
                    //20180314: Nendi - Add Two Digit Decimal
                    mQuestionAnswer.setText(String.format(Locale.US, "%.2f", finalAnswer));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            mQuestionAnswer.setInputType(InputType.TYPE_CLASS_NUMBER | InputType.TYPE_NUMBER_FLAG_DECIMAL);
        } else if (Global.AT_TEXT_MULTILINE.equals(answerType)) {
            mQuestionAnswer.setInputType(InputType.TYPE_TEXT_FLAG_MULTI_LINE);
            mQuestionAnswer.setSingleLine(false);

            if (Global.TAG_URL_LINK.equalsIgnoreCase(bean.getTag())) {
                mListener.onAcceptedAgreement(bean, group, number);
                mButtonView.setVisibility(View.VISIBLE);

                if(StringUtils.isBlank(qAnswer)){
                    mQuestionAnswer.setVisibility(View.GONE);
                    mQuestionTvAnswer.setVisibility(View.GONE);
                } else {
                    mQuestionAnswer.setText(qAnswer.split(Global.DELIMETER_DATA4)[0]);
                }
            } else {
                mButtonView.setVisibility(View.GONE);
                InputFilter[] editFilters = mQuestionAnswer.getFilters();
                InputFilter[] newFilters  = new InputFilter[editFilters.length + 1];
                System.arraycopy(editFilters, 0, newFilters, 0, editFilters.length);
                newFilters[editFilters.length] = new InputFilter.AllCaps();
                mQuestionAnswer.setFilters(newFilters);
            }
        } else if (Global.AT_CURRENCY.equals(answerType)) {
            mQuestionAnswer.setInputType(InputType.TYPE_CLASS_NUMBER);
        } else if (Global.AT_NUMERIC.equals(answerType)) {
            mQuestionAnswer.setInputType(InputType.TYPE_CLASS_NUMBER);
        } else {
            InputFilter[] editFilters = mQuestionAnswer.getFilters();
            InputFilter[] newFilters  = new InputFilter[editFilters.length + 1];
            System.arraycopy(editFilters, 0, newFilters, 0, editFilters.length);
            newFilters[editFilters.length] = new InputFilter.AllCaps();
            mQuestionAnswer.setFilters(newFilters);
            mQuestionAnswer.setInputType(InputType.TYPE_CLASS_TEXT);
        }

        tempText = bean.getAnswer() != null ? bean.getAnswer() : "";
        mQuestionAnswer.addTextChangedListener(new TextWatcher() {

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
//                bean.setRelevanted(true);
//                bean.setChange(true);
            }

            @Override
            public void beforeTextChanged(CharSequence s, int start, int count,
                                          int after) {

                if (Global.AT_CURRENCY.equals(bean.getAnswer_type())) {
//                    tempText = Tool.separateThousand(mQuestionAnswer.getText().toString().trim());
                    if (bean.getAnswer() != null)
                        tempText = Tool.separateThousand(bean.getAnswer().trim());
                    if (tempText == null) tempText = "";
                } else {
//                    tempText = mQuestionAnswer.getText().toString().trim();
                    if (bean.getAnswer() != null)
                        tempText = bean.getAnswer().trim();
                    if (tempText == null) tempText = "";
                }
                txtBefore = s.toString();
            }

            @Override
            public void afterTextChanged(Editable s) {
                if (bean.isRelevanted()) {
                    if (Global.AT_CURRENCY.equals(bean.getAnswer_type())) {
                        if (tempText.contains(".")) {
                            String newText = mQuestionAnswer.getText().toString().trim();
                            if (!newText.contains("."))
                                newText = Tool.separateThousand(newText);
                            if (!tempText.equals(newText)) {
                                if (!newText.contains(","))
                                    newText = Tool.separateThousand(newText);
                                if (!tempText.equals(newText)) {
                                    if (mRecyclerView != null) {
                                        ScrollingLinearLayoutManager layoutManager = (ScrollingLinearLayoutManager) mRecyclerView.getLayoutManager();
                                        layoutManager.setScrollEnable(false);
                                    }
                                    mView.setChanged(true);
                                    bean.setChange(true);
                                } else {
                                    mView.setChanged(false);
                                    bean.setChange(false);
                                }
                            } else {
                                boolean isSame = s.toString().equals(newText);
                                if (!isSame) {
                                    mView.setChanged(false);
                                    bean.setChange(false);
                                }
                            }
                        } else {
                            tempText = Tool.separateThousand(tempText);
                            String newText = mQuestionAnswer.getText().toString().trim();
                            if (!newText.contains("."))
                                newText = Tool.separateThousand(newText);
                            if (!tempText.equals(newText)) {
                                mView.setChanged(true);
                                bean.setIsCanChange(true);
                                bean.setChange(true);
                            } else {
                                if (!bean.isChange()) {
                                    mView.setChanged(false);
                                    bean.setChange(false);
                                }
                            }
                        }
                    } else if (Global.AT_NUMERIC.equals(answerType) || Global.AT_DECIMAL.equals(answerType)
                            || Global.AT_TEXT.equals(answerType) || Global.AT_TEXT_MULTILINE.equals(answerType)) {
                        String newText = mQuestionAnswer.getText().toString().trim();
                        if (!tempText.equals(newText)) {
                            mView.setChanged(true);

                            if (tempText.equals(bean.getAnswer())) {
                                bean.setChange(true);
                            }
                        } else {
                            mView.setChanged(false);
                            bean.setChange(false);
                        }
                    }
                }

                String newText = mQuestionAnswer.getText().toString().trim();
                if (bean.isRelevantMandatory()) {
                    if (null == bean.getAnswer())
                        bean.setAnswer("");
                    if (bean.getAnswer().equals(newText)) {
                        if (bean.getAnswer().equalsIgnoreCase("")) {
                            mQuestionAnswer.setHint(mActivity.getString(R.string.requiredField));
                        } else {
                            mQuestionAnswer.setHint("");
                        }
                    } else {
                        if (bean.getAnswer().equalsIgnoreCase("")) {
                            mQuestionAnswer.setHint(mActivity.getString(R.string.requiredField));
                        } else {
                            mQuestionAnswer.setHint("");
                        }
                        bean.setIsCanChange(true);
                        bean.setChange(true);
                        mView.setChanged(true);
                    }
                }
                else {
                    if (bean.getRelevant_mandatory() == null) {
                        setAnswerWithRelevantMandatoryConditional(bean, newText);
                    } else if (!bean.getRelevant_mandatory().equalsIgnoreCase("")) {
                        setAnswerWithRelevantMandatoryConditional(bean, newText);
                    }
                }


                bean.setAnswer(getFinalAnswer());

                txtNow = s.toString();

            }
        });

        mQuestionAnswer.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                // TODO Auto-generated method stub
                if (Global.AT_CURRENCY.equals(bean.getAnswer_type())) {
                    if (!hasFocus) {
                        if (group == 999 || FragmentQuestion.questionAdapter.isExpanded(group)) {
                            showCurrencyView();
                        }
                    } else {
                        showNormalView();
                    }
                }
            }
        });

        if (bean.isRelevanted())
            mQuestionAnswer.setImeOptions(EditorInfo.IME_ACTION_DONE);
        if (answerType.equals(Global.AT_CURRENCY)) {
            showCurrencyView();
        }
        if (bean.getIs_mandatory().equals(Global.TRUE_STRING))
            mQuestionAnswer.setHint(mActivity.getString(R.string.requiredField));
        else
            mQuestionAnswer.setHint("");

        if (bean.isRelevantMandatory()) {
            if (bean.isMandatory()) {
                mQuestionAnswer.setHint(mActivity.getString(R.string.requiredField));
            } else {
                mQuestionAnswer.setHint(mActivity.getString(R.string.requiredField));
            }
        } else {
            if (bean.isMandatory()) {
                mQuestionAnswer.setHint(mActivity.getString(R.string.requiredField));
            } else {
                mQuestionAnswer.setHint("");
            }
        }

        if (mQuestionTvAnswer.getVisibility() == View.VISIBLE) {
            mQuestionTvAnswer.setVisibility(View.GONE);
            mQuestionAnswer.setVisibility(View.VISIBLE);
        }

        if (bean.isReadOnly()) {
            mQuestionAnswer.setSingleLine(false);
            mQuestionAnswer.setCursorVisible(false);
            mQuestionAnswer.setEnabled(false);
            if (Global.AT_TEXT_MULTILINE.equals(answerType)) {
                if(!Global.TAG_URL_LINK.equalsIgnoreCase(bean.getTag())){
                    mQuestionTvAnswer.setText(mQuestionAnswer.getText());
                    mQuestionAnswer.setVisibility(View.GONE);
                    mQuestionTvAnswer.setBackgroundResource(R.color.tv_gray);
                    mQuestionTvAnswer.setVisibility(View.VISIBLE);
                } else {
                    if(StringUtils.isBlank(qAnswer)){
                        mQuestionAnswer.setVisibility(View.GONE);
                        mQuestionTvAnswer.setVisibility(View.GONE);
                    }
                }
            }
        } else {
            mQuestionAnswer.setCursorVisible(true);
            mQuestionAnswer.setEnabled(true);
        }

        mButtonView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                GeneralParameter gsUrlPdf = GeneralParameterDataAccess.getOne(mActivity, GlobalData.getSharedGlobalData().getUser().getUuid_user(), Global.GS_MS_PDF_LINK);
                if(null != gsUrlPdf && StringUtils.isNotBlank(gsUrlPdf.getGs_value())){
                    getDocumentPDF(mActivity, gsUrlPdf.getGs_value());
                }
            }
        });

        // @kelvin.ht (16/02/2024) Make Readonly off for Calculate DSR (InstallmentWOM and InstallmentOther)
        if (bean.getIdentifier_name().equalsIgnoreCase(Global.REF_INSTALLMENT_WOM)
                || bean.getIdentifier_name().equalsIgnoreCase(Global.REF_INSTALLMENT_OTHER)) {
            QuestionBean qbDSR = Constant.listOfQuestion.get(Global.REF_PRE_DSR);
            if (qbDSR.getAnswer() == null) {
                QuestionBean qb = Constant.listOfQuestion.get(bean.getIdentifier_name());
                if (qb != null) qb.setReadOnly(false);

                mQuestionAnswer.setVisibility(View.VISIBLE);
                mQuestionAnswer.setCursorVisible(true);
                mQuestionAnswer.setEnabled(true);
            }
            if (bean.getAnswer().equals("")) {
                bean.setAnswer("0");
                mQuestionAnswer.setText("0");
            }
        }
    }

    private void setAnswerWithRelevantMandatoryConditional(QuestionBean bean, String newText) {
        if (null == bean.getAnswer())
            bean.setAnswer("");
        if (null != bean && bean.getAnswer().equals(newText)) {
            if (bean.getAnswer().equalsIgnoreCase("")) {
                mQuestionAnswer.setHint("");
            } else {
                mQuestionAnswer.setHint("");
            }
        } else {
            if (null != bean && bean.getAnswer().equalsIgnoreCase("")) {
                mQuestionAnswer.setHint("");
            } else {
                mQuestionAnswer.setHint("");
            }
            bean.setIsCanChange(true);
            bean.setChange(true);
            mView.setChanged(true);
        }
    }

    public void showCurrencyView() {
        mQuestionAnswer.setInputType(InputType.TYPE_CLASS_TEXT);
        String answer = mQuestionAnswer.getText().toString().trim();
//        bean.setAnswer(answer);
        String currencyView = Tool.separateThousand(answer);
        InputFilter[] inputFilters = {new InputFilter.LengthFilter(
                50)};
        mQuestionAnswer.setFilters(inputFilters);
        if (currencyView == null) currencyView = "";
        mQuestionAnswer.setText(currencyView);
    }

    public String getFinalAnswer() {
        String answer = mQuestionAnswer.getText().toString().trim();
        String finalAnswer = "";
        if (Global.AT_CURRENCY.equals(bean.getAnswer_type())) {
            if (answer != null && answer.length() > 0) {
                String tempAnswer = Tool.deleteAll(answer, ",");
                String[] intAnswer = Tool.split(tempAnswer, ".");
                if (intAnswer.length > 1) {
                    if (intAnswer[1].equals("00"))
                        finalAnswer = intAnswer[0];
                    else {
                        finalAnswer = tempAnswer;
                    }
                } else {
                    finalAnswer = tempAnswer;
                }
            }
        } else if(Global.TAG_URL_LINK.equalsIgnoreCase(bean.getTag())){
            if (StringUtils.isNotBlank(bean.getAnswer())
                    && Global.ACCEPTED_AGREEMENT.equalsIgnoreCase(bean.getAnswer().split(Global.DELIMETER_DATA4)[0])) {
                finalAnswer = bean.getAnswer();
            }
        } else {
            finalAnswer = answer;
        }
        return finalAnswer;
    }

    public void showNormalView() {
        InputFilter[] inputFilters = {new InputFilter.LengthFilter(
                bean.getMax_length())};
        mQuestionAnswer.setFilters(inputFilters);
        String finalAnswer = getFinalAnswer();
        mQuestionAnswer.setInputType(InputType.TYPE_CLASS_NUMBER);
        mQuestionAnswer.setText(finalAnswer);
//        bean.setAnswer(finalAnswer);
    }

    @SuppressLint("StaticFieldLeak")
    private void getDocumentPDF(final Context context, final String downloadUrl) {
        new AsyncTask<Void, Void, MssResponseType>() {
            private ProgressDialog progressDialog;
            private String fileLocation;
            private File file;
            private String message;
            private final Helper helper= Helper.getInstance();

            @Override
            protected void onPreExecute() {
                progressDialog = ProgressDialog.show(context,
                        "", context.getString(R.string.progressWait), true);
            }

            @Override
            protected MssResponseType doInBackground(Void... arg0) {
                String uriFileName = context.getFilesDir().getPath() + "/pdf/" + helper
                        .getNameFromUrl(downloadUrl);
                File fileOpen = new File(uriFileName);
                if (fileOpen.exists()) {
                    fileLocation = uriFileName;
                    MssResponseType response = new MssResponseType();
                    MssResponseType.Status status = new MssResponseType.Status();
                    status.setCode(0);
                    response.setStatus(status);
                    return response;
                } else {
                    try {
                        if (Tool.isInternetconnected(context)) {
                            URL url = new URL(downloadUrl);
                            MssResponseType response = new MssResponseType();
                            // Install the all-trusting trust manager
                            if (StringUtils.isNotBlank(downloadUrl) && downloadUrl.toUpperCase().contains("HTTPS")) {
                                try {
                                    // Create a trust manager that does not validate certificate chains
                                    TrustManager[] trustAllCerts = new TrustManager[]{
                                            new X509TrustManager() {
                                                public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                                                    return null;
                                                }
                                                public void checkClientTrusted(
                                                        java.security.cert.X509Certificate[] certs, String authType) {
                                                }
                                                public void checkServerTrusted(
                                                        java.security.cert.X509Certificate[] certs, String authType) {
                                                }
                                            }
                                    };

                                    SSLContext sc = SSLContext.getInstance("SSL");
                                    sc.init(null, trustAllCerts, new java.security.SecureRandom());
                                    HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

                                    HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
                                    connection.setHostnameVerifier(new HostnameVerifier() {
                                        @Override
                                        public boolean verify(String arg0, SSLSession arg1) {
                                            return true;
                                        }
                                    });
                                    connection.setRequestMethod("GET");
                                    connection.setDoOutput(true);
                                    connection.connect();
                                    if (connection.getResponseCode() == HttpsURLConnection.HTTP_OK) {
                                        MssResponseType.Status status = new MssResponseType.Status();
                                        status.setCode(0);
                                        response.setStatus(status);

                                        file = new File(context.getFilesDir().getPath() + "/pdf");
                                        if (!file.exists()) {
                                            file.mkdirs();
                                        }
                                        String uriString = (file.getAbsolutePath() + "/" + helper.getNameFromUrl(downloadUrl));
                                        fileLocation = uriString;
                                        FileOutputStream fos = new FileOutputStream(uriString);
                                        try {
                                            InputStream is = connection.getInputStream();
                                            byte[] buffer = new byte[1024 * 1024];
                                            int len;
                                            while ((len = is.read(buffer)) > 0) {
                                                fos.write(buffer, 0, len);
                                            }
                                            fos.flush();
                                        } finally {
                                            fos.close();
                                        }
                                    }
                                } catch (Exception e) {
                                    message = "Error Download PDF: "+e.getMessage();
                                    FireCrash.log(e);
                                    if (Global.IS_DEV) {
                                        e.printStackTrace();
                                    }
                                    return null;
                                }
                            } else {
                                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                                connection.setConnectTimeout(Global.DEFAULTCONNECTIONTIMEOUT);
                                connection.setRequestMethod("GET");
                                connection.setDoOutput(true);
                                connection.connect();

                                if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                                    MssResponseType.Status status = new MssResponseType.Status();
                                    status.setCode(0);
                                    response.setStatus(status);

                                    file = new File(context.getFilesDir().getPath() + "/pdf");
                                    if (!file.exists()) {
                                        file.mkdirs();
                                    }
                                    String uriString = (file.getAbsolutePath() + "/" + helper.getNameFromUrl(downloadUrl));
                                    fileLocation = uriString;
                                    FileOutputStream fos = new FileOutputStream(uriString);
                                    try {
                                        InputStream is = connection.getInputStream();
                                        byte[] buffer = new byte[1024 * 1024];
                                        int len;
                                        while ((len = is.read(buffer)) > 0) {
                                            fos.write(buffer, 0, len);
                                        }
                                        fos.flush();
                                    } finally {
                                        fos.close();
                                    }
                                }
                            }
                            return response;
                        } else {
                            message = context.getString(R.string.no_internet_connection);
                        }
                        return null;
                    } catch (Exception e) {
                        message = "Error Download PDF: "+e.getMessage();
                        if (Global.IS_DEV) {
                            e.printStackTrace();
                        }
                        FireCrash.log(e);
                        return null;
                    }
                }
            }

            @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
            @Override
            protected void onPostExecute(MssResponseType result) {
                super.onPostExecute(result);
                if (progressDialog != null && progressDialog.isShowing()) {
                    try {
                        progressDialog.dismiss();
                    } catch (Exception e) {
                        FireCrash.log(e);
                    }
                }
                if (result != null && result.getStatus().getCode() == 0) {
                    try {
                        Intent intent = new Intent(context, ViewPdfRendererFragment.class);

                        Bundle extras = new Bundle();
                        extras.putSerializable(ViewPdfRendererFragment.KEY_QUESTION_BEAN, bean);
                        extras.putString("URL_FILE", fileLocation);
                        extras.putString("ANSWER", bean.getAnswer());
                        intent.putExtras(extras);
                        mActivity.startActivityForResult(intent, Global.REQUEST_ACCEPTED_AGREEMENT);
                    } catch (Exception ex) {
                        if (Global.IS_DEV) {
                            ex.printStackTrace();
                        }
                        Toast.makeText(context, ex.getMessage(), Toast.LENGTH_SHORT).show();
                    }
                } else if (result != null && result.getStatus().getCode() != 0) {
                    final NiftyDialogBuilder dialogBuilder = NiftyDialogBuilder.getInstance(context);
                    dialogBuilder.withTitle(context.getString(R.string.info_capital))
                            .isCancelableOnTouchOutside(false)
                            .withMessage(result.getStatus().getMessage())
                            .withButton1Text(context.getString(R.string.btnOk))
                            .setButton1Click(new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    dialogBuilder.dismiss();
                                }
                            })
                            .show();
                } else if (StringUtils.isNotBlank(message)) {
                    Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
                } else {
                    Toast.makeText(context, context.getString(R.string.msgErrorParsingJson), Toast.LENGTH_SHORT).show();
                }
            }
        }.execute();
    }
}