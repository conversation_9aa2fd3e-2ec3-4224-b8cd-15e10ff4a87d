package com.adins.mss.dao;

import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;

import com.adins.mss.dao.PaymentHistoryD;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TR_PAYMENTHISTORY_D".
*/
public class PaymentHistoryDDao extends AbstractDao<PaymentHistoryD, String> {

    public static final String TABLENAME = "TR_PAYMENTHISTORY_D";

    /**
     * Properties of entity PaymentHistoryD.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_payment_history_d = new Property(0, String.class, "uuid_payment_history_d", true, "UUID_PAYMENT_HISTORY_D");
        public final static Property Uuid_task_h = new Property(1, String.class, "uuid_task_h", false, "UUID_TASK_H");
        public final static Property Transaction_type = new Property(2, String.class, "transaction_type", false, "TRANSACTION_TYPE");
        public final static Property Receipt_no = new Property(3, String.class, "receipt_no", false, "RECEIPT_NO");
        public final static Property Value_date = new Property(4, java.util.Date.class, "value_date", false, "VALUE_DATE");
        public final static Property Posting_date = new Property(5, java.util.Date.class, "posting_date", false, "POSTING_DATE");
        public final static Property Payment_amount = new Property(6, String.class, "payment_amount", false, "PAYMENT_AMOUNT");
        public final static Property Installment_amount = new Property(7, String.class, "installment_amount", false, "INSTALLMENT_AMOUNT");
        public final static Property Installment_number = new Property(8, String.class, "installment_number", false, "INSTALLMENT_NUMBER");
        public final static Property Wop_code = new Property(9, String.class, "wop_code", false, "WOP_CODE");
        public final static Property Payment_allocation_name = new Property(10, String.class, "payment_allocation_name", false, "PAYMENT_ALLOCATION_NAME");
        public final static Property Os_amount_od = new Property(11, String.class, "os_amount_od", false, "OS_AMOUNT_OD");
        public final static Property Receive_amount = new Property(12, String.class, "receive_amount", false, "RECEIVE_AMOUNT");
        public final static Property Dtm_upd = new Property(13, java.util.Date.class, "dtm_upd", false, "DTM_UPD");
        public final static Property Usr_upd = new Property(14, String.class, "usr_upd", false, "USR_UPD");
        public final static Property Dtm_crt = new Property(15, java.util.Date.class, "dtm_crt", false, "DTM_CRT");
        public final static Property Usr_crt = new Property(16, String.class, "usr_crt", false, "USR_CRT");
        public final static Property Uuid_payment_history_h = new Property(17, String.class, "uuid_payment_history_h", false, "UUID_PAYMENT_HISTORY_H");
    };


    public PaymentHistoryDDao(DaoConfig config) {
        super(config);
    }
    
    public PaymentHistoryDDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TR_PAYMENTHISTORY_D\" (" + //
                "\"UUID_PAYMENT_HISTORY_D\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_payment_history_d
                "\"UUID_TASK_H\" TEXT," + // 1: uuid_task_h
                "\"TRANSACTION_TYPE\" TEXT," + // 2: transaction_type
                "\"RECEIPT_NO\" TEXT," + // 3: receipt_no
                "\"VALUE_DATE\" INTEGER," + // 4: value_date
                "\"POSTING_DATE\" INTEGER," + // 5: posting_date
                "\"PAYMENT_AMOUNT\" TEXT," + // 6: payment_amount
                "\"INSTALLMENT_AMOUNT\" TEXT," + // 7: installment_amount
                "\"INSTALLMENT_NUMBER\" TEXT," + // 8: installment_number
                "\"WOP_CODE\" TEXT," + // 9: wop_code
                "\"PAYMENT_ALLOCATION_NAME\" TEXT," + // 10: payment_allocation_name
                "\"OS_AMOUNT_OD\" TEXT," + // 11: os_amount_od
                "\"RECEIVE_AMOUNT\" TEXT," + // 12: receive_amount
                "\"DTM_UPD\" INTEGER," + // 13: dtm_upd
                "\"USR_UPD\" TEXT," + // 14: usr_upd
                "\"DTM_CRT\" INTEGER," + // 15: dtm_crt
                "\"USR_CRT\" TEXT," + // 16: usr_crt
                "\"UUID_PAYMENT_HISTORY_H\" TEXT);"); // 17: uuid_payment_history_h
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TR_PAYMENTHISTORY_D\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, PaymentHistoryD entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_payment_history_d());
 
        String uuid_task_h = entity.getUuid_task_h();
        if (uuid_task_h != null) {
            stmt.bindString(2, uuid_task_h);
        }
 
        String transaction_type = entity.getTransaction_type();
        if (transaction_type != null) {
            stmt.bindString(3, transaction_type);
        }
 
        String receipt_no = entity.getReceipt_no();
        if (receipt_no != null) {
            stmt.bindString(4, receipt_no);
        }
 
        java.util.Date value_date = entity.getValue_date();
        if (value_date != null) {
            stmt.bindLong(5, value_date.getTime());
        }
 
        java.util.Date posting_date = entity.getPosting_date();
        if (posting_date != null) {
            stmt.bindLong(6, posting_date.getTime());
        }
 
        String payment_amount = entity.getPayment_amount();
        if (payment_amount != null) {
            stmt.bindString(7, payment_amount);
        }
 
        String installment_amount = entity.getInstallment_amount();
        if (installment_amount != null) {
            stmt.bindString(8, installment_amount);
        }
 
        String installment_number = entity.getInstallment_number();
        if (installment_number != null) {
            stmt.bindString(9, installment_number);
        }
 
        String wop_code = entity.getWop_code();
        if (wop_code != null) {
            stmt.bindString(10, wop_code);
        }
 
        String payment_allocation_name = entity.getPayment_allocation_name();
        if (payment_allocation_name != null) {
            stmt.bindString(11, payment_allocation_name);
        }
 
        String os_amount_od = entity.getOs_amount_od();
        if (os_amount_od != null) {
            stmt.bindString(12, os_amount_od);
        }
 
        String receive_amount = entity.getReceive_amount();
        if (receive_amount != null) {
            stmt.bindString(13, receive_amount);
        }
 
        java.util.Date dtm_upd = entity.getDtm_upd();
        if (dtm_upd != null) {
            stmt.bindLong(14, dtm_upd.getTime());
        }
 
        String usr_upd = entity.getUsr_upd();
        if (usr_upd != null) {
            stmt.bindString(15, usr_upd);
        }
 
        java.util.Date dtm_crt = entity.getDtm_crt();
        if (dtm_crt != null) {
            stmt.bindLong(16, dtm_crt.getTime());
        }
 
        String usr_crt = entity.getUsr_crt();
        if (usr_crt != null) {
            stmt.bindString(17, usr_crt);
        }
 
        String uuid_payment_history_h = entity.getUuid_payment_history_h();
        if (uuid_payment_history_h != null) {
            stmt.bindString(18, uuid_payment_history_h);
        }
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public PaymentHistoryD readEntity(Cursor cursor, int offset) {
        PaymentHistoryD entity = new PaymentHistoryD( //
            cursor.getString(offset + 0), // uuid_payment_history_d
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // uuid_task_h
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // transaction_type
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // receipt_no
            cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)), // value_date
            cursor.isNull(offset + 5) ? null : new java.util.Date(cursor.getLong(offset + 5)), // posting_date
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // payment_amount
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7), // installment_amount
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8), // installment_number
            cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9), // wop_code
            cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10), // payment_allocation_name
            cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11), // os_amount_od
            cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12), // receive_amount
            cursor.isNull(offset + 13) ? null : new java.util.Date(cursor.getLong(offset + 13)), // dtm_upd
            cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14), // usr_upd
            cursor.isNull(offset + 15) ? null : new java.util.Date(cursor.getLong(offset + 15)), // dtm_crt
            cursor.isNull(offset + 16) ? null : cursor.getString(offset + 16), // usr_crt
            cursor.isNull(offset + 17) ? null : cursor.getString(offset + 17) // uuid_payment_history_h
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, PaymentHistoryD entity, int offset) {
        entity.setUuid_payment_history_d(cursor.getString(offset + 0));
        entity.setUuid_task_h(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setTransaction_type(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setReceipt_no(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setValue_date(cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)));
        entity.setPosting_date(cursor.isNull(offset + 5) ? null : new java.util.Date(cursor.getLong(offset + 5)));
        entity.setPayment_amount(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setInstallment_amount(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
        entity.setInstallment_number(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
        entity.setWop_code(cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9));
        entity.setPayment_allocation_name(cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10));
        entity.setOs_amount_od(cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11));
        entity.setReceive_amount(cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12));
        entity.setDtm_upd(cursor.isNull(offset + 13) ? null : new java.util.Date(cursor.getLong(offset + 13)));
        entity.setUsr_upd(cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14));
        entity.setDtm_crt(cursor.isNull(offset + 15) ? null : new java.util.Date(cursor.getLong(offset + 15)));
        entity.setUsr_crt(cursor.isNull(offset + 16) ? null : cursor.getString(offset + 16));
        entity.setUuid_payment_history_h(cursor.isNull(offset + 17) ? null : cursor.getString(offset + 17));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(PaymentHistoryD entity, long rowId) {
        return entity.getUuid_payment_history_d();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(PaymentHistoryD entity) {
        if(entity != null) {
            return entity.getUuid_payment_history_d();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
}
