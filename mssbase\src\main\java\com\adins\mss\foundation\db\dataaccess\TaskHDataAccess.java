package com.adins.mss.foundation.db.dataaccess;

import android.content.Context;

import com.adins.mss.base.timeline.TimelineManager;
import com.adins.mss.base.todolist.ToDoList;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.DaoSession;
import com.adins.mss.dao.DepositReportD;
import com.adins.mss.dao.Scheme;
import com.adins.mss.dao.TaskH;
import com.adins.mss.dao.TaskHDao;
import com.adins.mss.foundation.db.DaoOpenHelper;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import de.greenrobot.dao.query.QueryBuilder;
import de.greenrobot.dao.query.WhereCondition;

/**
 * <AUTHOR>
 *
 */
public class TaskHDataAccess {
	public static final String ACCESS_MODE_BRANCH = "ACCESS_MODE_BRANCH";
	public static final String ACCESS_MODE_USER = "ACCESS_MODE_USER";
	public static final String ACCESS_MODE_HYBRID = "ACCESS_MODE_HYBRID";
	public static final String STATUS_SEND_INIT = "New";
	public static final String STATUS_SEND_SENT = "Sent";
	public static final String STATUS_SEND_PENDING = "Failed";
	public static final String STATUS_SEND_SAVEDRAFT = "Draft";
	public static final String STATUS_SEND_UPLOADING = "Uploading";
	public static final String STATUS_SEND_FAILED = "Failed";
	public static final String STATUS_SEND_REMINDER = "Reminder";
	public static final String STATUS_SEND_DOWNLOAD = "Download";
	public static final String STATUS_TASK_VERIFICATION = "V"; 
	public static final String STATUS_TASK_APPROVAL = "P"; 
	public static final String STATUS_SEND_REJECTED= "Rejected";
	public static final String STATUS_SEND_DROP = "Delete";

	public static final String STATUS_TASK_WAITING = "Waiting";
	public static final String STATUS_TASK_WAITING_DOWNLOAD = "WDownload";

	public static final String STATUS_TASK_VERIFICATION_DOWNLOAD = "VDownload"; 
	public static final String STATUS_TASK_APPROVAL_DOWNLOAD = "PDownload";

	public static final String STATUS_RV_SENT = "rv_sent";
	public static final String STATUS_RV_PENDING = "rv_pending";
	
//	public static final int PRIORITY_HIGH = 1;
//	public static final int PRIORITY_REMINDER = 4;
//	public static final int PRIORITY_MEDIUM = 2;
//	public static final int PRIORITY_LOW = 3;
	
	public static final String PRIORITY_HIGH = "High";
	public static final String PRIORITY_NORMAL = "Normal";
	public static final String PRIORITY_LOW = "Low";

//	private static DaoOpenHelper daoOpenHelper;
//private static final String selectMahasiswaQuery = "SELECT * FROM " + TaskHDao.TABLENAME
//		+ " A JOIN " + TaskHSequenceDao.TABLENAME + " B ON A."+TaskHDao.Properties.Uuid_task_h + " == B."+TaskHSequenceDao.Properties.Uuid_task_h_sequence +
//		" ORDER BY B."+ TaskHSequenceDao.Properties.Sequence + " ASC";
	/**
	 * use to generate dao session that you can access modelDao
	 * 
	 * @param context --> context from activity
	 * @return 
	 */
	protected static DaoSession getDaoSession(Context context){
		/*if(daoOpenHelper==null){
//			if(daoOpenHelper.getDaoSession()==null)
				daoOpenHelper = new DaoOpenHelper(context.getApplicationContext());
		}
		DaoSession daoSeesion = daoOpenHelper.getDaoSession();
		return daoSeesion;*/
		return DaoOpenHelper.getDaoSession(context);
	}
	
	/**
	 * get taskH dao and you can access the DB
	 * 
	 * @param context
	 * @return
	 */
	protected static TaskHDao getTaskHDao(Context context){
		return getDaoSession(context).getTaskHDao();
	}
	
	/**
	 * Clear session, close db and set daoOpenHelper to null
	 *
	 */
	public static void closeAll(){
		/*if(daoOpenHelper!=null){
			daoOpenHelper.closeAll();
			daoOpenHelper = null;
		}*/
		DaoOpenHelper.closeAll();
	}
	
	/**
	 * add taskH as entity
	 * 
	 * @param context
	 * @param taskH
	 * 
	 * 
	 */
	public static void add(Context context, TaskH taskH){
		getTaskHDao(context).insert(taskH);
		getDaoSession(context).clear();
	}
	
	/**
	 * add taskH as list entity
	 * 
	 * @param context
	 * @param taskHList
	 */
	public static void add(Context context, List<TaskH> taskHList){
		getTaskHDao(context).insertInTx(taskHList);
		getDaoSession(context).clear();
	}
	
	/**
	 * add or replace data taskH
	 * @param context
	 * @param taskH
	 */
	public static void addOrReplace(Context context, TaskH taskH){
		getTaskHDao(context).insertOrReplaceInTx(taskH);
		getDaoSession(context).clear();
	}
	
	/**
	 * add or replace list data taskH
	 * 
	 * @param context
	 * @param taskHList
	 */
	public static void addOrReplace(Context context, List<TaskH> taskHList){
		/*for(TaskH taskH : taskHList){
			if(getOneHeader(context, taskH.getUuid_task_h())!=null)
				update(context, taskH);
			else add(context, taskH);
		}*/
		getTaskHDao(context).insertOrReplaceInTx(taskHList);
		getDaoSession(context).clear();
	}
	
	/**
	 * 
	 * delete all content in table.
	 * 
	 * @param context
	 */
	public static void clean(Context context){
		getTaskHDao(context).deleteAll();
		getDaoSession(context).clear();
	}

	/**
	 * @param context
	 * @param taskH
	 */
	public static void delete(Context context, TaskH taskH){
		getTaskHDao(context).delete(taskH);
		getDaoSession(context).clear();
	}
	
	/**
	 * delete List of taskH and its relational data by taskH status
	 * @param context
	 * @param uuidUser
	 * @param status
	 */
	public static void deleteTaskHByStatus(Context context, String uuidUser, String status){
		List<TaskH> taskHList = getAllTaskByStatus(context, uuidUser, status);
		if(taskHList!=null){
			if(!taskHList.isEmpty()){
				for(TaskH taskH : taskHList){
					deleteWithRelation(context, taskH);
				}
			}
		}
	}
	public static void deleteTaskHByApplNo(Context context, String uuidUser, String applNo){
		List<TaskH> taskHList = getAllTaskByApplNo(context, uuidUser, applNo);
		if(taskHList!=null){
			if(!taskHList.isEmpty()){
				for(TaskH taskH : taskHList){
					deleteWithRelation(context, taskH);
				}
			}
		}
	}
	
	/**
	 * delete row and the relation form other table
	 * @param context
	 * @param taskH
	 */
	public static void deleteWithRelation(Context context, TaskH taskH){
		TaskDDataAccess.delete(context, taskH.getUuid_task_h());
		ImageResultDataAccess.delete(context, taskH.getUuid_task_h());
		PrintResultDataAccess.delete(context, taskH.getUuid_task_h());
		getTaskHDao(context).delete(taskH);
		getDaoSession(context).clear();
	}

	/**
	 * Delete list of taskH with their relation on the other tables 
	 * @param context
	 * @param listTaskHDelete
	 */
	public static void deleteListWithRelation(Context context,
			List<TaskH> listTaskHDelete) {
		// TODO Auto-generated method stub
		for(TaskH taskHBean : listTaskHDelete)
			deleteWithRelation(context, taskHBean);
//		getDaoSession(context).clear();
	}
	
	/**
	 * delete all record by user
	 * 
	 * @param context
	 * @param uuidUser
	 */
	public static void delete(Context context, String uuidUser){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser));
		qb.build();
		getTaskHDao(context).deleteInTx(qb.list());
		getDaoSession(context).clear();
	}

	public static void deleteByUuid(Context context, String uuidUser, String uuidTask) {
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser));
		qb.where(TaskHDao.Properties.Uuid_task_h.eq(uuidTask));
		qb.build();
		getTaskHDao(context).deleteInTx(qb.list());
		getDaoSession(context).clear();
	}
	
	/**
	 * @param context
	 * @param taskH
	 */
	public static void update(Context context, TaskH taskH){
		getTaskHDao(context).update(taskH);
	}
	
	/**
	 * This method is used to retrieve one object of task header by taskId
	 * @param context
	 * @param taskId
	 * @return
	 */
	public static TaskH getOneTaskHeader(Context context, String taskId){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Task_id.eq(taskId));
		qb.build().forCurrentThread();
		if(qb.list().isEmpty())
			return null;
		return qb.list().get(0);
	}
	
	/**
	 * This method is used to retrieve one object of task header by uuidTaskH
	 * @param context
	 * @param uuid_task_h
	 * @return
	 */
	public static TaskH getOneHeader(Context context, String uuid_task_h){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_task_h.eq(uuid_task_h));
		qb.build().forCurrentThread();
		if(qb.list().isEmpty())
			return null;
		return qb.list().get(0);
	}
	public static List<TaskH> getTaskByAppl(Context context, String applNo){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Appl_no.eq(applNo)
				,TaskHDao.Properties.Status.notIn(STATUS_SEND_FAILED, STATUS_SEND_SENT, STATUS_SEND_UPLOADING));
		qb.build().forCurrentThread();
		return qb.list() ;
	}
	
//	/**
//	 * This method is used to retrieve one object of task header by taskId using user from global
//	 * @param context
//	 * @param taskId
//	 * @return
//	 */
//	public static TaskH getOneTaskHeader(Context context, String taskId, String userId){
//		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
//		qb.where(TaskHDao.Properties.Uuid_user.eq(userId),
//				TaskHDao.Properties.Task_id.eq(taskId));
//		qb.build();
//		return qb.list().get(0);
//	}

	/**
	 * This method is used to retrieve all pending sent submit pts
	 */
	public static List<TaskH> getAllPendingPTS(Context context, String isSent) {
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Is_sent_pts.eq(isSent));
		qb.build();

		return qb.list();
	}
	
	/**
	 * Get all tasks which has verified scheme
	 * 
	 * @param context
	 * @param userId
	 * @return List<TaskH>
	 */
	public static List<TaskH> getAllVerified(Context context, String userId){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(userId)
				//,TaskHDao.Properties.Uuid_scheme.in(uuidSchemeList)
				,TaskHDao.Properties.Status.in(STATUS_TASK_VERIFICATION, STATUS_TASK_VERIFICATION_DOWNLOAD));
		qb.build();
		return qb.list();
	}
	
	public static List<TaskH> getAllVerifiedForUser(Context context, String userId){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(userId)
				,TaskHDao.Properties.Access_mode.in(ACCESS_MODE_USER, ACCESS_MODE_HYBRID)
				,TaskHDao.Properties.Status.in(STATUS_TASK_VERIFICATION, STATUS_TASK_VERIFICATION_DOWNLOAD));
		qb.build();
		return qb.list();
	}
	
	public static List<TaskH> getAllVerifiedForBranch(Context context, String userId){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(userId)
				,TaskHDao.Properties.Access_mode.in(ACCESS_MODE_BRANCH, ACCESS_MODE_HYBRID)
				,TaskHDao.Properties.Status.in(STATUS_TASK_VERIFICATION, STATUS_TASK_VERIFICATION_DOWNLOAD));
		qb.build();
		return qb.list();
	}
	/**
	 * Get all tasks which has approval scheme
	 * 
	 * @param context
	 * @param userId
	 * @return List<TaskH>
	 */
	public static List<TaskH> getAllApproval(Context context, String userId){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(userId)
				//,TaskHDao.Properties.Uuid_scheme.in(uuidSchemeList)
				,TaskHDao.Properties.Status.in(STATUS_TASK_APPROVAL,STATUS_TASK_APPROVAL_DOWNLOAD));
		qb.build();
		return qb.list();
	}
	
	public static List<TaskH> getAllApprovalForUser(Context context, String userId){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(userId)
				,TaskHDao.Properties.Access_mode.in(ACCESS_MODE_USER, ACCESS_MODE_HYBRID)
				,TaskHDao.Properties.Status.in(STATUS_TASK_APPROVAL,STATUS_TASK_APPROVAL_DOWNLOAD));
		qb.build();
		return qb.list();
	}
	
	public static List<TaskH> getAllApprovalForBranch(Context context, String userId){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(userId)
				,TaskHDao.Properties.Access_mode.in(ACCESS_MODE_BRANCH, ACCESS_MODE_HYBRID)
				,TaskHDao.Properties.Status.in(STATUS_TASK_APPROVAL,STATUS_TASK_APPROVAL_DOWNLOAD));
		qb.build();
		return qb.list();
	}
	/**
	 * select * from table where uuid_user = global.userid
	 * 
	 * @param context
	 * @param uuidUser
	 * @return
	 */
	public static List<TaskH> getAll(Context context, String uuidUser){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser));
		qb.build();
		return qb.list();
	}
	
	/**
	 * select * from table where uuid_user = global.userid
	 * 
	 * @param context
	 * @param uuidUser
	 * @return
	 */
	public static List<TaskH> getAllWithAssignmentDate(Context context, String uuidUser){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Assignment_date.isNotNull());
		qb.build();
		return qb.list();
	}

	
	public static List<TaskH> getAllVerificationTaskWithAssignmentDate(Context context, String uuidUser){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Assignment_date.isNotNull(),
				TaskHDao.Properties.Status.in(STATUS_TASK_VERIFICATION, STATUS_TASK_VERIFICATION_DOWNLOAD));
		qb.build();
		return qb.list();
	}
	
	public static List<TaskH> getAllApprovalTaskWithAssignmentDate(Context context, String uuidUser){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Assignment_date.isNotNull(),
				TaskHDao.Properties.Status.in(STATUS_TASK_APPROVAL, STATUS_TASK_APPROVAL_DOWNLOAD));
		qb.build();
		return qb.list();
	}
	/**
	 * select * from table where uuid_user = param
	 * 
	 * @param context
	 * @return
	 */
//	public static List<TaskH> getAll(Context context, String userId){
//		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
//		qb.where(TaskHDao.Properties.Uuid_user.eq(userId));
//		qb.build();
//		return qb.list();
//	}
	
	/**
	 * select * from table where uuid_scheme = param 
	 * 
	 * @param context
	 * @param uuidUser
	 * @param keyScheme
	 * @return
	 */
	public static List<TaskH> getAll(Context context, String uuidUser, String keyScheme){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Uuid_scheme.eq(keyScheme));
		qb.build();
		return qb.list();
	}
	
	/**
	 * This method is used to retrieve list of all sent tasks
	 * 
	 * @param context
	 * @param uuidUser
	 * @return
	 */
	public static List<TaskH> getAllSentTask(Context context, String uuidUser){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser), 
				TaskHDao.Properties.Status.in(STATUS_SEND_SENT, STATUS_SEND_REJECTED));
		qb.orderDesc(TaskHDao.Properties.Submit_date);
		qb.build();
		return qb.list();
	}

	public static long getSentTaskCounter(Context context, String uuidUser){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser), 
				TaskHDao.Properties.Status.in(STATUS_SEND_SENT, STATUS_SEND_REJECTED));
		qb.build();
		return qb.list().size();
	}
	
	public static long getVerificationTaskCounter(Context context, String uuidUser){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Access_mode.in(ACCESS_MODE_USER, ACCESS_MODE_HYBRID),
				TaskHDao.Properties.Status.in(STATUS_TASK_VERIFICATION,STATUS_TASK_VERIFICATION_DOWNLOAD));
		qb.build();
		return qb.list().size();
	}
	
	public static long getApprovalTaskCounter(Context context, String uuidUser){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Status.in(STATUS_TASK_APPROVAL, STATUS_TASK_APPROVAL_DOWNLOAD));
		qb.build();
		return qb.list().size();
	}
	
	public static long getVerificationTaskCounterByUser(Context context, String uuidUser){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Access_mode.in(ACCESS_MODE_USER, ACCESS_MODE_HYBRID),
				TaskHDao.Properties.Status.in(STATUS_TASK_VERIFICATION,STATUS_TASK_VERIFICATION_DOWNLOAD));
		qb.build();
		return qb.list().size();
	}
	
	public static long getApprovalTaskCounterByUser(Context context, String uuidUser){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser), 
				TaskHDao.Properties.Access_mode.in(ACCESS_MODE_USER, ACCESS_MODE_HYBRID),
				TaskHDao.Properties.Status.in(STATUS_TASK_APPROVAL, STATUS_TASK_APPROVAL_DOWNLOAD));
		qb.build();
		return qb.list().size();
	}
	
	public static long getVerificationTaskCounterByBranch(Context context, String uuidUser){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Access_mode.in(ACCESS_MODE_BRANCH, ACCESS_MODE_HYBRID),
				TaskHDao.Properties.Status.in(STATUS_TASK_VERIFICATION,STATUS_TASK_VERIFICATION_DOWNLOAD));
		qb.build();
		return qb.list().size();
	}
	
	public static long getApprovalTaskCounterByBranch(Context context, String uuidUser){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser), 
				TaskHDao.Properties.Access_mode.in(ACCESS_MODE_BRANCH, ACCESS_MODE_HYBRID),
				TaskHDao.Properties.Status.in(STATUS_TASK_APPROVAL, STATUS_TASK_APPROVAL_DOWNLOAD));
		qb.build();
		return qb.list().size();
	}
	
	public static long getTaskInPriorityCounter(Context context, String uuidUser){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Status.in(STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT));
//		qb.orderAsc(TaskHDao.Properties.Priority);
		qb.build();

		List<TaskH> listTask = qb.list();

		Scheme pts = SchemeDataAccess.getOneSurveyTaskSchemeByFormName(context, Global.FORM_NAME_PROMISE_TO_SURVEY);
		Scheme preSurvey = SchemeDataAccess.getOneSurveyTaskSchemeByFormName(context, Global.FORM_NAME_PRE_SURVEY);
		Scheme ots = SchemeDataAccess.getOneSurveyTaskSchemeByFormName(context, Global.FORM_NAME_OTS);
		Scheme guarantor = SchemeDataAccess.getOneSurveyTaskSchemeByFormName(context, Global.FORM_NAME_GUARANTOR);

		//if user does not have all of these schemes (every scheme is null), this will be skipped
		if(pts != null || preSurvey != null || ots != null || guarantor != null){
			//search through the list, and remove taskH that has any of the schemes above
			for (Iterator<TaskH> iterator = listTask.iterator();iterator.hasNext();) {
				TaskH taskH = iterator.next();
				String currSchemeId = taskH.getUuid_scheme();

				if(pts != null && currSchemeId.equals(pts.getUuid_scheme())) {
					iterator.remove();
				}else if(preSurvey != null && currSchemeId.equals(preSurvey.getUuid_scheme())){
					iterator.remove();
				}else if(ots != null && currSchemeId.equals(ots.getUuid_scheme())){
					iterator.remove();
				}else if(guarantor != null && currSchemeId.equals(guarantor.getUuid_scheme())){
					iterator.remove();
				}
			}
		}

		return listTask.size();
	}
	
	public static long getTaskInStatusCounter(Context context, String uuidUser){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Status.in(STATUS_SEND_SAVEDRAFT, STATUS_SEND_PENDING, STATUS_SEND_UPLOADING));
//				TaskHDao.Properties.Status.notIn(STATUS_SEND_SENT, STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT,
//						STATUS_TASK_APPROVAL,STATUS_TASK_APPROVAL_DOWNLOAD,STATUS_TASK_VERIFICATION, STATUS_TASK_VERIFICATION_DOWNLOAD));
		qb.orderDesc(TaskHDao.Properties.Dtm_crt);
		qb.build();
		return qb.list().size();
	}

	//michael.wijaya 08 Apr 22: used to get task list by scheme description/form name
	public static List<TaskH> getAllTaskByFormName(Context context, String uuidUser, String schemeDescription){
		//get Task's Form Scheme
		Scheme formScheme = SchemeDataAccess.getOneSurveyTaskSchemeByFormName(context, schemeDescription);

		if(formScheme == null){
			return Collections.emptyList();
		}

		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Status.in(STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT),
				TaskHDao.Properties.Uuid_scheme.eq(formScheme.getUuid_scheme()));
		qb.build();
		return qb.list();
	}

	//michael.wijaya 08 Apr 22: used for types of Task List menu's counter in sidebar menu
	public static long getTaskListTypesCounter(Context context, String uuidUser, String schemeDescription){
		//get Task's Form Scheme
		Scheme formScheme = SchemeDataAccess.getOneSurveyTaskSchemeByFormName(context, schemeDescription);

		if(formScheme == null){
			return 0;
		}

		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Status.in(STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT),
				TaskHDao.Properties.Uuid_scheme.eq(formScheme.getUuid_scheme()));
		qb.build();
		return qb.list().size();
	}
	
	/**
	 * This method is sed to get all sent task which will be deleted  if lower minimum of a minimum date
	 * @param context
	 * @param uuidUser
	 * @param batasDel - minimum date
	 * @return
	 */
	public static List<TaskH> getAllDeleteTask(Context context, String uuidUser,
			String batasDel) {
		// TODO Auto-generated method stub
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser), 
				TaskHDao.Properties.Status.eq(STATUS_SEND_SENT),
				TaskHDao.Properties.Submit_date.le(batasDel));
		qb.orderDesc(TaskHDao.Properties.Dtm_crt);
		qb.build();
		return qb.list();
	}
	
	/**
	 * This method is used to retrieve list of all task in status bar
	 * @param context
	 * @param uuidUser
	 * @return
	 */
	public static List<TaskH> getAllTaskInStatus(Context context, String uuidUser){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Status.in(STATUS_SEND_SAVEDRAFT, STATUS_SEND_PENDING, STATUS_SEND_UPLOADING));
//				TaskHDao.Properties.Status.notIn(STATUS_SEND_SENT, STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT,
//						STATUS_TASK_APPROVAL,STATUS_TASK_APPROVAL_DOWNLOAD,STATUS_TASK_VERIFICATION, STATUS_TASK_VERIFICATION_DOWNLOAD));
		qb.orderDesc(TaskHDao.Properties.Dtm_crt);
		qb.build();
		return qb.list();
	}
	
	/**
	 * This method is used to retrieve list of all task in status bar by taskId
	 * @param context
	 * @param uuidUser
	 * @param taskId
	 * @return List<TaskH>
	 */
	public static List<TaskH> getAllTaskInStatusByTask(Context context, String uuidUser, String taskId){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Task_id.like(taskId),
				TaskHDao.Properties.Status.in(STATUS_SEND_SAVEDRAFT, STATUS_SEND_PENDING, STATUS_SEND_UPLOADING));
//				TaskHDao.Properties.Status.notIn(STATUS_SEND_SENT, STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT,
//						STATUS_TASK_APPROVAL,STATUS_TASK_APPROVAL_DOWNLOAD,STATUS_TASK_VERIFICATION, STATUS_TASK_VERIFICATION_DOWNLOAD));
		qb.orderDesc(TaskHDao.Properties.Dtm_crt);
		qb.build();
		return qb.list();
	}
	
	/**
	 * This method is used to retrieve list of all task in status bar by Customer Name
	 * @param context
	 * @param uuidUser
	 * @param customerName
	 * @return List<TaskH>
	 */
	public static List<TaskH> getAllTaskInStatusByCustomer(Context context, String uuidUser, String customerName){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Customer_name.like(customerName),
				TaskHDao.Properties.Status.in(STATUS_SEND_SAVEDRAFT, STATUS_SEND_PENDING, STATUS_SEND_UPLOADING));
//				TaskHDao.Properties.Status.notIn(STATUS_SEND_SENT, STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT,
//						STATUS_TASK_APPROVAL,STATUS_TASK_APPROVAL_DOWNLOAD,STATUS_TASK_VERIFICATION, STATUS_TASK_VERIFICATION_DOWNLOAD));
		qb.orderDesc(TaskHDao.Properties.Dtm_crt);
		qb.build();
		return qb.list();
	}

	/**
	 * This method is used to retrieve list of all task in status bar by Customer Name and taskId
	 * @param context
	 * @param uuidUser
	 * @param searchContent
	 * @return
	 */
	public static List<TaskH> getAllTaskInStatusByAll(Context context,
			String uuidUser, String searchContent) {
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				qb.or(TaskHDao.Properties.Customer_name.like(searchContent), TaskHDao.Properties.Task_id.like(searchContent)),
				TaskHDao.Properties.Status.in(STATUS_SEND_SAVEDRAFT, STATUS_SEND_PENDING, STATUS_SEND_UPLOADING));
//				TaskHDao.Properties.Status.notIn(STATUS_SEND_SENT, STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT,
//						STATUS_TASK_APPROVAL,STATUS_TASK_APPROVAL_DOWNLOAD,STATUS_TASK_VERIFICATION, STATUS_TASK_VERIFICATION_DOWNLOAD));
		qb.orderDesc(TaskHDao.Properties.Dtm_crt);
		qb.build();
		return qb.list();
	}
	
	/**
	 * This method is used to retrieve list of all task in priority bar
	 * @param context
	 * @param uuidUser
	 * @return
	 */
	public static List<TaskH> getAllTaskInPriority(Context context, String uuidUser){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Status.in(STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT));
		
//		qb.where(new WhereCondition.StringCondition
//				(" UUID_SCHEME IN (SELECT UUID_SCHEME FROM MS_SCHEME  WHERE FORM_TYPE NOT IN ("+Global.FORM_TYPE_APPROVAL+
//						", "+Global.FORM_TYPE_SIMULASI+
//						", "+Global.FORM_TYPE_VERIFICATION +")"));
//		qb.orderAsc(TaskHDao.Properties.Priority);
		qb.orderAsc(TaskHDao.Properties.Assignment_date);
		qb.build();
		return qb.list();
	}

	public static List<TaskH> getAllTaskInPriority(Context context, String uuidUser, String orderBy, String sortBy){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Status.in(STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT));

//		qb.where(new WhereCondition.StringCondition
//				(" UUID_SCHEME IN (SELECT UUID_SCHEME FROM MS_SCHEME  WHERE FORM_TYPE NOT IN ("+Global.FORM_TYPE_APPROVAL+
//						", "+Global.FORM_TYPE_SIMULASI+
//						", "+Global.FORM_TYPE_VERIFICATION +")"));
//		qb.orderAsc(TaskHDao.Properties.Priority);
		qb.orderRaw(orderBy + " COLLATE NOCASE " + sortBy);
//		qb.orderAsc(TaskHDao.Properties.Assignment_date);
		qb.build();
		return qb.list();
	}

	public static List<TaskH> getAllTaskInPriorityByScheme(Context context, String uuidUser, String uuidScheme){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Status.in(STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT),
				TaskHDao.Properties.Uuid_scheme.eq(uuidScheme));
//		qb.orderAsc(TaskHDao.Properties.Priority);
		qb.orderAsc(TaskHDao.Properties.Assignment_date);
		qb.build();
		return qb.list();
	}

	public static List<TaskH> getAllTaskInPriorityByScheme(Context context, String uuidUser, String uuidScheme, String sortBy, String orderBy){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Status.in(STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT),
				TaskHDao.Properties.Uuid_scheme.eq(uuidScheme));
//		qb.orderAsc(TaskHDao.Properties.Priority);
//		qb.orderAsc(TaskHDao.Properties.Assignment_date);
		qb.orderRaw(sortBy + " COLLATE NOCASE " + orderBy);
		qb.build();
		return qb.list();
	}
	
	/**
	 * This method is used to retrieve list of all task in priority bar by taskId
	 * @param context
	 * @param uuidUser
	 * @return
	 */
	public static List<TaskH> getAllTaskInPriorityByTask(Context context, String uuidUser, String taskId){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Task_id.like(taskId),
				TaskHDao.Properties.Status.in(STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT));
		
//		qb.where(new WhereCondition.StringCondition
//				(" UUID_SCHEME IN (SELECT UUID_SCHEME FROM MS_SCHEME  WHERE FORM_TYPE NOT IN ("+Global.FORM_TYPE_APPROVAL+
//						", "+Global.FORM_TYPE_SIMULASI+
//						", "+Global.FORM_TYPE_VERIFICATION +")"));
//		qb.orderAsc(TaskHDao.Properties.Priority);
		qb.orderAsc(TaskHDao.Properties.Assignment_date);
		qb.build();
		return qb.list();
	}
	public static List<TaskH> getAllTaskInPriorityByTaskAndScheme(Context context, String uuidUser, String taskId, String uuidScheme){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Task_id.like(taskId),
				TaskHDao.Properties.Status.in(STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT),
				TaskHDao.Properties.Uuid_scheme.eq(uuidScheme));
//		qb.orderAsc(TaskHDao.Properties.Priority);
		qb.orderAsc(TaskHDao.Properties.Assignment_date);
		qb.build();
		return qb.list();
	}
	/**
	 * This method is used to retrieve list of all task in priority bar by customerName
	 * @param context
	 * @param uuidUser
	 * @param customerName
	 * @return
	 */
	public static List<TaskH> getAllTaskInPriorityByCustomer(Context context, String uuidUser, String customerName){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Customer_name.like(customerName),
				TaskHDao.Properties.Status.in(STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT));
		
//		qb.where(new WhereCondition.StringCondition
//				(" UUID_SCHEME IN (SELECT UUID_SCHEME FROM MS_SCHEME  WHERE FORM_TYPE NOT IN ("+Global.FORM_TYPE_APPROVAL+
//						", "+Global.FORM_TYPE_SIMULASI+
//						", "+Global.FORM_TYPE_VERIFICATION +")"));
//		qb.orderAsc(TaskHDao.Properties.Priority);
		qb.orderAsc(TaskHDao.Properties.Assignment_date);
		qb.build();
		return qb.list();
	}

	public static List<TaskH> getAllTaskInPriorityByCustomerAndScheme(Context context, String uuidUser, String customerName, String uuidScheme){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Customer_name.like(customerName),
				TaskHDao.Properties.Status.in(STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT),
				TaskHDao.Properties.Uuid_scheme.eq(uuidScheme));
//		qb.orderAsc(TaskHDao.Properties.Priority);
		qb.orderAsc(TaskHDao.Properties.Assignment_date);
		qb.build();
		return qb.list();
	}

	public static List<TaskH> getAllTaskInPriorityByCustomerAndScheme(Context context, String uuidUser, String customerName, String uuidScheme, String sortBy, String orderBy){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Customer_name.like(customerName),
				TaskHDao.Properties.Status.in(STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT),
				TaskHDao.Properties.Uuid_scheme.eq(uuidScheme));
//		qb.orderAsc(TaskHDao.Properties.Priority);
//		qb.orderAsc(TaskHDao.Properties.Assignment_date);
		qb.orderRaw(orderBy + " COLLATE NOCASE " + sortBy);
		qb.build();
		return qb.list();
	}

	/**
	 * This method is used to retrieve list of all task in priority bar by customerName and taskId
	 * @param context
	 * @param uuidUser
	 * @param searchContent
	 * @return
	 */
	public static List<TaskH> getAllTaskInPriorityByAll(Context context,
			String uuidUser, String searchContent) {
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				qb.or(TaskHDao.Properties.Customer_name.like(searchContent), TaskHDao.Properties.Task_id.like(searchContent)),
				TaskHDao.Properties.Status.in(STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT));
//		qb.orderAsc(TaskHDao.Properties.Priority);
		qb.orderAsc(TaskHDao.Properties.Assignment_date);
		qb.build();
		return qb.list();
	}

	public static List<TaskH> getAllTaskInPriorityByAllAndScheme(Context context,String uuidUser, String searchContent, String uuidScheme, String sortBy, String orderBy) {
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				qb.or(TaskHDao.Properties.Customer_name.like(searchContent), TaskHDao.Properties.Task_id.like(searchContent)),
				TaskHDao.Properties.Status.in(STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT),
				TaskHDao.Properties.Uuid_scheme.eq(uuidScheme));
//		qb.orderAsc(TaskHDao.Properties.Priority);
		qb.orderRaw(sortBy.concat(" COLLATE NOCASE ").concat(orderBy));
		qb.build();
		return qb.list();
	}
	/**
	 * Get list of task header by status in 
	 * ("New","Sent","Pending","Draft"."Sent","Uploading","Download", "Reminder")
	 * @param context
	 * @param uuidUser
	 * @param status
	 * @return
	 */
	public static List<TaskH> getAllTaskByStatus(Context context, String uuidUser, String status){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Status.eq(status));
		qb.build();
		return qb.list();
	}
	public static List<TaskH> getAllTaskByApplNo(Context context, String uuidUser, String applNo){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Appl_no.eq(applNo),
				TaskHDao.Properties.Status.notEq(TaskHDataAccess.STATUS_SEND_SENT));
		qb.build();
		return qb.list();
	}
	
	/**
	 * Update status task header
	 * <br> Status = ("New","Sent","Pending","Draft"."Sent","Uploading","Download", "Reminder")
	 * @param context
	 * @param taskH
	 * @param status
	 */
	public static void updateStatus(Context context, TaskH taskH, String status){
		taskH.setStatus(status);
		getTaskHDao(context).update(taskH);
	}

	public static void updateStatusApp(Context context, TaskH taskH, String statusApp){
		taskH.setStatus_application(statusApp);
		getTaskHDao(context).update(taskH);
	}

	/**
	 * Update status task header by taskId
	 * <br> Status = ("New","Sent","Pending","Draft"."Sent","Uploading","Download", "Reminder")
	 * @param context
	 * @param taskId
	 * @param status
	 */
	public static void updateStatusByTaskId(Context context, String taskId, String status){
		TaskH taskH = getOneTaskHeader(context, taskId);
		taskH.setStatus(status);
		getTaskHDao(context).update(taskH);
	}

	public static void updateApplNoByTaskId(Context context, String taskId, String applNo){
		TaskH taskH = getOneTaskHeader(context, taskId);
		assert taskH != null;
		taskH.setAppl_no(applNo);
		getTaskHDao(context).update(taskH);
	}

	public static void updateStatusAppByTaskId(Context context, String taskId, String statusApp){
		TaskH taskH = getOneTaskHeader(context, taskId);
		assert taskH != null;
		taskH.setStatus_application(statusApp);
		getTaskHDao(context).update(taskH);
	}

    public static void updateIsSentConfins(Context context, String taskId, Integer isSent){
        TaskH taskH = getOneTaskHeader(context, taskId);
        assert taskH != null;
        taskH.setIs_sent_confins(isSent);
        getTaskHDao(context).update(taskH);
    }

    public static void updateIsAlreadyNotified(Context context, String taskId, int isNotified){
        TaskH taskH = getOneTaskHeader(context, taskId);
        assert taskH != null;
        taskH.setIs_already_notified(isNotified);
        getTaskHDao(context).update(taskH);
    }
	/**
	 * select TaskH where TaskH not in DepositReportD
	 * @return TaskHList
	 * 
	 */
	public static List<TaskH> getUnreportedTaskH(Context context, String uuidUser){
		List<String> uuidList=new ArrayList<String>();
		List<DepositReportD> depositReportDList = new ArrayList<DepositReportD>();
		depositReportDList = DepositReportDDataAccess.getAll(context);
		
		for(DepositReportD depositReportD : depositReportDList){
			uuidList.add(depositReportD.getUuid_task_h());
		}
		
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_task_h.notIn(uuidList));
		qb.build();
		
		return qb.list();
		
		// by aditya.purwa
//        TaskHDao dTaskH= daoOpenHelper.getDaoSession().getTaskHDao();
//        return dTaskH.queryRaw("UUID_TASK_H NOT IN (SELECT UUID_TASK_H FROM TR_DEPOSITREPORT_D");
    }



	public static List<TaskH> getTaskCollToday(Context context){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		
		qb.where( new WhereCondition.StringCondition
//				("strftime('%d',ASSIGNMENT_DATE) = strftime('%d',CURRENT_TIMESTAMP) and UUID_SCHEME IN " +
				("date(ASSIGNMENT_DATE) = CURRENT_DATE and UUID_SCHEME IN " +
						"(SELECT UUID_SCHEME FROM MS_SCHEME WHERE FORM_TYPE = '"+Global.FORM_TYPE_COLL+"')"));
			qb.build();
		return qb.list();
	}
	
	public static int getTotalTaskCollToday(Context context){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		
		qb.where( new WhereCondition.StringCondition
				("strftime('%d',ASSIGNMENT_DATE) = strftime('%d',CURRENT_TIMESTAMP, 'localtime') and UUID_SCHEME IN " +
						"(SELECT UUID_SCHEME FROM MS_SCHEME WHERE FORM_TYPE = '"+Global.FORM_TYPE_COLL+"')"));
		//qb.count();
			
		return (int) qb.count();
	}
	
	
	public static List<TaskH> getAllTaskCollection(Context context, String uuidUser){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser));
		qb.where( new WhereCondition.StringCondition
				("UUID_SCHEME IN " +
						"(SELECT UUID_SCHEME FROM MS_SCHEME WHERE FORM_TYPE = '"+Global.FORM_TYPE_COLL+"')"));
		qb.build();
		return qb.list();
	}

	public static List<TaskH> getAllTaskInHighPriority(Context context,
			String uuid_user, String sortBy, String orderBy) {
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuid_user),
				TaskHDao.Properties.Priority.eq(PRIORITY_HIGH),
				TaskHDao.Properties.Status.in(STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT));
//		qb.orderAsc(TaskHDao.Properties.Priority);
//		qb.orderAsc(TaskHDao.Properties.Assignment_date);
		qb.orderRaw(sortBy + " COLLATE NOCASE " + orderBy);
		qb.build();
		return qb.list();
	}
	
	public static List<TaskH> getAllTaskInNormalPriority(Context context,
			String uuid_user, String sortBy, String orderBy) {
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuid_user),
				TaskHDao.Properties.Priority.eq(PRIORITY_NORMAL),
				TaskHDao.Properties.Status.in(STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT));
//		qb.orderAsc(TaskHDao.Properties.Priority);
//		qb.orderAsc(TaskHDao.Properties.Assignment_date);
		qb.orderRaw(sortBy + " COLLATE NOCASE " + orderBy);
		qb.build();
		return qb.list();
	}

	public static List<TaskH> getAllTaskInLowPriority(Context context, String uuid_user,
													  String sortBy, String orderBy) {
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuid_user),
				TaskHDao.Properties.Priority.eq(PRIORITY_LOW),
				TaskHDao.Properties.Status.in(STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT));
//		qb.orderAsc(TaskHDao.Properties.Priority);
//		qb.orderAsc(TaskHDao.Properties.Assignment_date);
		qb.orderRaw(sortBy + " COLLATE NOCASE " + orderBy);
		qb.build();
		return qb.list();
	}

	public static List<TaskH> getAllTaskInHighPriorityByScheme(Context context,
													   String uuid_user, String uuid_scheme,
															   String sortBy, String orderBy) {
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuid_user),
				TaskHDao.Properties.Priority.eq(PRIORITY_HIGH),
				TaskHDao.Properties.Status.in(STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT),
				TaskHDao.Properties.Uuid_scheme.eq(uuid_scheme));
//		qb.orderAsc(TaskHDao.Properties.Priority);
//		qb.orderAsc(TaskHDao.Properties.Assignment_date);
		qb.orderRaw(sortBy + " COLLATE NOCASE " + orderBy);
		qb.build();
		return qb.list();
	}

	//Nendi: 2017/10/20 Add Sort Order on High Priority
	public static List<TaskH> getAllTaskInHighPriorityBySchemeAndSortOrder(Context context,
															   String uuid_user, String uuid_scheme, int sortBy, boolean asc) {
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuid_user),
				TaskHDao.Properties.Priority.eq(PRIORITY_HIGH),
				TaskHDao.Properties.Status.in(STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT),
				TaskHDao.Properties.Uuid_scheme.eq(uuid_scheme));
//		qb.orderAsc(TaskHDao.Properties.Priority);

		switch (sortBy) {
			case ToDoList.SEARCH_BY_TASK_ID:
				if(asc) qb.orderAsc(TaskHDao.Properties.Task_id);
				else qb.orderDesc(TaskHDao.Properties.Task_id);
				break;
			case ToDoList.SEARCH_BY_CUSTOMER_NAME:
				if(asc) qb.orderAsc(TaskHDao.Properties.Customer_name);
				else qb.orderDesc(TaskHDao.Properties.Customer_name);
				break;
			case ToDoList.SEARCH_BY_PTS:
				if(asc) qb.orderAsc(TaskHDao.Properties.Pms_date);
				else qb.orderDesc(TaskHDao.Properties.Pms_date);
				break;
		}

		qb.build();
		return qb.list();
	}

	public static List<TaskH> getAllTaskInNormalPriorityByScheme(Context context, String uuid_user, String uuid_scheme,
																 String sortBy, String orderBy) {
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuid_user),
				TaskHDao.Properties.Priority.eq(PRIORITY_NORMAL),
				TaskHDao.Properties.Status.in(STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT),
				TaskHDao.Properties.Uuid_scheme.eq(uuid_scheme));
//		qb.orderAsc(TaskHDao.Properties.Priority);
//		qb.orderAsc(TaskHDao.Properties.Assignment_date);
		qb.orderRaw(sortBy + " COLLATE NOCASE " + orderBy);
		qb.build();
		return qb.list();
	}

	public static List<TaskH> getAllTaskInLowPriorityByScheme(Context context, String uuid_user, String uuid_scheme,
															  String sortBy, String orderBy) {
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuid_user),
				TaskHDao.Properties.Priority.eq(PRIORITY_LOW),
				TaskHDao.Properties.Status.in(STATUS_SEND_DOWNLOAD, STATUS_SEND_INIT),
				TaskHDao.Properties.Uuid_scheme.eq(uuid_scheme));
//		qb.orderAsc(TaskHDao.Properties.Priority);
//		qb.orderAsc(TaskHDao.Properties.Assignment_date);
		qb.orderRaw(sortBy + " COLLATE NOCASE " + orderBy);
		qb.build();
		return qb.list();
	}


	public static TaskH getOneTaskByStatusRV(Context context, String uuidUser, String status){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Status.eq(STATUS_SEND_SENT),
				TaskHDao.Properties.Status_rv.eq(status));
		qb.build();

		if ((qb.list() == null) || qb.list().isEmpty()) return null;
		return qb.list().get(0);
	}

	public static List<String> getTaskHPrintRequired(Context context, String uuidUser) {
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
				TaskHDao.Properties.Status.eq(TaskHDataAccess.STATUS_SEND_SENT),
				TaskHDao.Properties.Print_count.isNull(),
				TaskHDao.Properties.Rv_number.isNull());
		qb.build();

		List<String> results = new ArrayList<>();
		List<TaskH> listTaskH = qb.list();
		if (listTaskH != null && !listTaskH.isEmpty()) {
			for (TaskH taskH : listTaskH) {
				results.add(taskH.getUuid_task_h());
			}
		}
		return results;
	}

	public static RvValue getRvValue(Context context, String taskId) {
		TaskH taskH = TaskHDataAccess.getOneTaskHeader(context, taskId);
		if ( taskH != null) {
			if (taskH.getRv_number() == null || taskH.getRv_number().isEmpty()) return RvValue.RV_EMPTY;
			else return RvValue.RV_NOT_EMPTY;
		} else {
			return RvValue.TASK_H_NOT_FOUND;
		}
	}

	public static List<TaskH> getTaskDetailNotDownloaded(Context context, String applNo) {
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Status.eq(STATUS_TASK_WAITING),
				TaskHDao.Properties.Appl_no.eq(applNo));
		qb.build();
		return qb.list();
	}

	public static List<TaskH> getTaskWaiting(Context context, String applNo) {
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Status.in(STATUS_TASK_WAITING, STATUS_TASK_WAITING_DOWNLOAD),
				TaskHDao.Properties.Appl_no.eq(applNo));
		qb.build();
		return qb.list();
	}

	public static List<TaskH>  getAllTaskCaeByApplNo(Context context, String applNo){
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Appl_no.eq(applNo));
		qb.build();
		return qb.list();
	}

	public static List<TaskH> getTaskByFormAndApplNo(Context context, String formName, String applNo) {
		Scheme scheme = SchemeDataAccess.getOneSurveyTaskSchemeByFormName(context, formName);
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Appl_no.eq(applNo)
		, TaskHDao.Properties.Uuid_scheme.eq(scheme.getUuid_scheme()));
		qb.build();
		return qb.list();
	}

	//to process next task to task list
	public static void processToOpenNextTask(TaskH taskCurrentProcess, Context context) {
		List<TaskH> taskWaitingList = TaskHDataAccess.getTaskWaiting(context, taskCurrentProcess.getAppl_no());
		if (taskWaitingList != null && !taskWaitingList.isEmpty()) {
			TaskH taskPreSurvey = null;
			for (int i=0; i<taskWaitingList.size(); i++) {
				if (Global.FORM_NAME_PRE_SURVEY.equalsIgnoreCase(taskWaitingList.get(i).getScheme().getScheme_description())) {
					taskPreSurvey = taskWaitingList.get(i);
					break;
				}
			}

			if (taskPreSurvey != null) {
				if(TaskHDataAccess.STATUS_TASK_WAITING.equalsIgnoreCase(taskPreSurvey.getStatus())) {
					taskPreSurvey.setStatus(TaskHDataAccess.STATUS_SEND_INIT);
                    taskPreSurvey.setAssignment_date(new Date());
					TaskHDataAccess.addOrReplace(context, taskPreSurvey);
					TimelineManager.insertTimeline(context, taskPreSurvey);
				} else if (TaskHDataAccess.STATUS_TASK_WAITING_DOWNLOAD.equalsIgnoreCase(taskPreSurvey.getStatus())) {
					taskPreSurvey.setStatus(TaskHDataAccess.STATUS_SEND_DOWNLOAD);
                    taskPreSurvey.setAssignment_date(new Date());
					TaskHDataAccess.addOrReplace(context, taskPreSurvey);
					TimelineManager.insertTimeline(context, taskPreSurvey);
				}
			} else {
				List<TaskH> checkTaskPresurvey = getTaskByFormAndApplNo(context, Global.FORM_NAME_PRE_SURVEY, taskCurrentProcess.getAppl_no());
				boolean isOutStandingPresurvey = false;
				if (checkTaskPresurvey!=null) {
					for(int i=0; i<checkTaskPresurvey.size(); i++) {
						TaskH presurvey = checkTaskPresurvey.get(i);
						if(STATUS_SEND_INIT.equalsIgnoreCase(presurvey.getStatus())
							|| STATUS_SEND_DOWNLOAD.equalsIgnoreCase(presurvey.getStatus())
							|| STATUS_SEND_SAVEDRAFT.equalsIgnoreCase(presurvey.getStatus())) {
							isOutStandingPresurvey = true;
						}
					}
				}
				if(!isOutStandingPresurvey) {
					for (int i = 0; i < taskWaitingList.size(); i++) {
						TaskH task = taskWaitingList.get(i);
						if (TaskHDataAccess.STATUS_TASK_WAITING.equalsIgnoreCase(task.getStatus())) {
							task.setStatus(TaskHDataAccess.STATUS_SEND_INIT);
                            task.setAssignment_date(new Date());
							TaskHDataAccess.addOrReplace(context, task);
							TimelineManager.insertTimeline(context, task);
						} else if (TaskHDataAccess.STATUS_TASK_WAITING_DOWNLOAD.equalsIgnoreCase(task.getStatus())) {
							task.setStatus(TaskHDataAccess.STATUS_SEND_DOWNLOAD);
							task.setAssignment_date(new Date());
							TaskHDataAccess.addOrReplace(context, task);
							TimelineManager.insertTimeline(context, task);
						}
					}
				}
			}
		}
	}

	//update flagging sendTask
	public static void updateFlaggingSendTask(TaskH taskCurrentProcess, String statusTask, Context context) {
		List<TaskH> listTaskApplNo = TaskHDataAccess.getAllTaskCaeByApplNo(context, taskCurrentProcess.getAppl_no());
		if (listTaskApplNo!=null && !listTaskApplNo.isEmpty()) {
			for (int i=0; i<listTaskApplNo.size(); i++) {
				TaskH taskH = listTaskApplNo.get(i);
				if (Global.FORM_NAME_PROMISE_TO_SURVEY.equalsIgnoreCase(taskCurrentProcess.getScheme().getScheme_description())) {
					taskH.setSend_task_promise_to_survey(statusTask);
					taskCurrentProcess.setSend_task_promise_to_survey(statusTask);
				} else if (Global.FORM_NAME_PRE_SURVEY.equalsIgnoreCase(taskCurrentProcess.getScheme().getScheme_description())) {
					taskH.setSend_task_presurvey(statusTask);
					taskCurrentProcess.setSend_task_presurvey(statusTask);
				} else {
					taskH.setSend_task_survey(statusTask);
					taskCurrentProcess.setSend_task_survey(statusTask);
				}
				TaskHDataAccess.addOrReplace(context, taskH);
			}
		}
		TaskHDataAccess.addOrReplace(context, taskCurrentProcess);
	}

	//check allow to submit or not
	public static boolean isAllowToSubmit(TaskH taskHProcess) {
		boolean isAllow = true;

		if(!Global.FORM_NAME_PROMISE_TO_SURVEY.equalsIgnoreCase(taskHProcess.getScheme().getScheme_description())) {
			if (Global.FORM_NAME_PRE_SURVEY.equalsIgnoreCase(taskHProcess.getScheme().getScheme_description())) {
				if (TaskHDataAccess.STATUS_SEND_FAILED.equalsIgnoreCase(taskHProcess.getSend_task_promise_to_survey())) {
					isAllow = false;
				} else {
					isAllow = true;
				}
			} else {
				if (TaskHDataAccess.STATUS_SEND_FAILED.equalsIgnoreCase(taskHProcess.getSend_task_promise_to_survey())
					|| TaskHDataAccess.STATUS_SEND_FAILED.equalsIgnoreCase(taskHProcess.getSend_task_presurvey())) {
					isAllow = false;
				} else {
					isAllow = true;
				}
			}
		}
		return isAllow;
	}

	//check allow to submit or not
	public static boolean isAllowToDelete(Context context, TaskH taskHProcess) {
		boolean isAllow = true;
		QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
		qb.where(TaskHDao.Properties.Appl_no.eq(taskHProcess.getAppl_no())
			, TaskHDao.Properties.Status.notIn(STATUS_TASK_WAITING, STATUS_TASK_WAITING_DOWNLOAD, STATUS_SEND_SENT)
			, TaskHDao.Properties.Uuid_task_h.notEq(taskHProcess.getUuid_task_h())
		);
		qb.build();

		List<TaskH> taskList = qb.list();
		if ((taskList!=null && !taskList.isEmpty()) &&
				(Global.FORM_NAME_PROMISE_TO_SURVEY.equalsIgnoreCase(taskHProcess.getScheme().getScheme_description()) ||
						Global.FORM_NAME_PRE_SURVEY.equalsIgnoreCase(taskHProcess.getScheme().getScheme_description())) &&
				!taskList.isEmpty()) {
			isAllow = false;
		}
		return isAllow;
	}

    public static boolean isTaskFormPromiseVisitSubmit(Context context, String uuidUser, String applNo) {
        boolean isTaskVisitSubmit = false;

        QueryBuilder<TaskH> qb = getTaskHDao(context).queryBuilder();
        qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser),
                TaskHDao.Properties.Appl_no.eq(applNo),
                TaskHDao.Properties.Status.eq(TaskHDataAccess.STATUS_SEND_SENT));
        qb.build();

        List<TaskH> taskList = qb.list();
        if (null != taskList && !taskList.isEmpty()) {
            String formName = qb.list().get(0).getScheme().getForm_id();
            if (Global.FORM_NAME_PROMISE_TO_VISIT.equalsIgnoreCase(formName)) {
                isTaskVisitSubmit = true;
            }
        }

        return isTaskVisitSubmit;
    }

	public enum RvValue {
		RV_EMPTY,
		RV_NOT_EMPTY,
		TASK_H_NOT_FOUND
	}

}
