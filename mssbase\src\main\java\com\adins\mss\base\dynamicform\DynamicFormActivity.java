package com.adins.mss.base.dynamicform;

import android.Manifest;
import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.app.DatePickerDialog;
import android.app.Dialog;
import android.app.ProgressDialog;
import android.app.TimePickerDialog;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.provider.MediaStore;
import android.text.format.DateFormat;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.view.ViewGroup.LayoutParams;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.ScaleAnimation;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.ToggleButton;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.checkin.CheckInManager;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.mainmenu.MainMenuActivity;
import com.adins.mss.base.review.QuestionReviewGenerator;
import com.adins.mss.base.todo.Task;
import com.adins.mss.base.util.CustomAnimatorLayout;
import com.adins.mss.base.util.GenericAsyncTask;
import com.adins.mss.base.util.GenericAsyncTask.GenericTaskInterface;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.base.util.Interpolator;
import com.adins.mss.base.util.LocaleHelper;
import com.adins.mss.base.util.Utility;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.LocationInfo;
import com.adins.mss.dao.Lookup;
import com.adins.mss.dao.LookupDao;
import com.adins.mss.dao.QuestionSet;
import com.adins.mss.dao.Scheme;
import com.adins.mss.foundation.camera.Camera;
import com.adins.mss.foundation.camerainapp.CameraActivity;
import com.adins.mss.foundation.camerainapp.helper.Logger;
import com.adins.mss.foundation.db.dataaccess.LookupDataAccess;
import com.adins.mss.foundation.db.dataaccess.QuestionSetDataAccess;
import com.adins.mss.foundation.db.dataaccess.SchemeDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskHDataAccess;
import com.adins.mss.foundation.dialog.DialogManager;
import com.adins.mss.foundation.formatter.Formatter;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;
import com.adins.mss.foundation.image.ExifData;
import com.adins.mss.foundation.image.Utils;
import com.adins.mss.foundation.image.ViewImageActivity;
import com.adins.mss.foundation.location.LocationTrackingManager;
import com.adins.mss.foundation.location.UpdateMenuIcon;
import com.adins.mss.foundation.questiongenerator.DateInputListener;
import com.adins.mss.foundation.questiongenerator.DynamicQuestion;
import com.adins.mss.foundation.questiongenerator.NotEqualSymbol;
import com.adins.mss.foundation.questiongenerator.OptionAnswerBean;
import com.adins.mss.foundation.questiongenerator.QuestionBean;
import com.adins.mss.foundation.questiongenerator.QuestionViewGenerator;
import com.adins.mss.foundation.questiongenerator.QuestionViewValidator;
import com.adins.mss.foundation.questiongenerator.TimeInputListener;
import com.adins.mss.foundation.questiongenerator.form.LabelFieldView;
import com.adins.mss.foundation.questiongenerator.form.LocationTagingView;
import com.adins.mss.foundation.questiongenerator.form.MultiOptionQuestionViewAbstract;
import com.adins.mss.foundation.questiongenerator.form.QuestionView;
import com.adins.mss.foundation.security.storepreferences.ObscuredSharedPreferences;
import com.adins.mss.foundation.sync.api.SynchronizeResponseLookup;
import com.adins.mss.foundation.sync.api.model.SynchronizeRequestModel;
import com.gadberry.utility.expression.Expression;
import com.gadberry.utility.expression.OperatorSet;
import com.google.gson.JsonSyntaxException;

import org.acra.ACRA;
import org.apache.commons.jexl2.JexlContext;
import org.apache.commons.jexl2.JexlEngine;
import org.apache.commons.jexl2.MapContext;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Stack;

//import org.apache.commons.jexl3.JexlBuilder;
//import org.apache.commons.jexl3.JexlContext;
//import org.apache.commons.jexl3.JexlEngine;
//import org.apache.commons.jexl3.MapContext;

//import android.content.SharedPreferences;


//Glen 6 Aug 2014, implement RequestJSONFromSErverTask
//Glen 22/12/14
public class DynamicFormActivity extends DynamicQuestion implements OnClickListener, GenericTaskInterface, LocationListener {
    public static SurveyHeaderBean header;
    public static List<String> listOfIdentifier = new ArrayList<String>();
    //    public static SpannableString deletePhoto;
    public static boolean isApproval = false;
    public static boolean isVerified = false;
    public static boolean allowImageEdit = true;
    //NENDI 20/01/2018
    public static boolean bypassImageValidation = true;
    public static String mCurrentPhotoPath;
    private static int idxPosition = 0;
    private static Menu mainMenu;
    private static int idxQuestion = 0;
    public DynamicSurveyHandler handler;
    public LinkedHashMap<String, QuestionBean> listOfQuestion;
    //GIGIN 21/1/2014
    ArrayList<QuestionBean> visibleQuestion = new ArrayList<QuestionBean>();
    ArrayList<String> questionLabel = new ArrayList<String>();
    ArrayAdapter<String> adapter;

    Handler nextHandler;
    Runnable nextRunnable;
    Handler reviewHandler;
    Runnable reviewRunnable;
    LocationManager mLocation = null;
    String currentQuestionGroup;
    String currentQuestionGroupInReview;
    private int questionSize = 0;
    private LinearLayout reviewContainer;
    private RelativeLayout searchContainer;
    private ScrollView scrollView;
    private ScrollView scrollView2;
    //use for calculation
    private JexlEngine jexlEngine;
    private int mode;
    //		private TaskH header;
    private LinearLayout questionContainer;
    private boolean isSimulasi = false;
    private boolean isFinish = false;
    private boolean isStop = false;
    private QuestionViewGenerator viewGenerator;
    //Glen 20 Oct 2014, TEMP, current page
    private List<QuestionBean> currentPageBeans = new ArrayList<QuestionBean>();
    private List<ViewGroup> currentPageViews = new ArrayList<ViewGroup>();
    // Glen 6 Aug 2014, flag, wether preview need info from server or not
    private boolean previewNeedServer = false;
    //Glen 10 Oct 2014, create array to hold preview field, to check index when clicked
    private List<QuestionView> previewFields;
    private LinkedHashMap<String, List<QuestionView>> questionFields;
    private LinkedHashMap<String, List<QuestionView>> reviewFields;
    private List<QuestionView> questionGroupFieldsHeader;
    private List<QuestionView> questionGroupFieldsHeaderPreview;
    //    private List<QuestionView> questionGroupFieldsContent;
    //Glen 15 Oct 2014, add preview mode, where user can edit one question and get back to previewScreen without changing the field
    private boolean inPreviewEditMode = false;
    private QuestionBean edittedQuestion;
    private boolean needQuickValidation = false;
    private boolean isSaveAndSending = false;
    // bong Oct 29th, 2014 - adding flag for button in screenContainer or in scrollContainer
    private boolean isButtonInScroll = false;
    private DataForDynamicQuestion dfdq = new DataForDynamicQuestion();
    private ImageButton btnSearch;
    private ImageButton btnBack;
    private ImageButton btnNext;
    // bong Oct20th, 2014 - create FormOpenHelper object for QuestionViewValidator
//		public FormOpenHelper formOpenHelper() {
//			FormOpenHelper formOpenHelper = new FormOpenHelper(getApplicationContext());
//			return formOpenHelper;
//		}
    private ImageButton btnSave;
    private ImageButton btnSend;
    private ImageButton btnVerified;
    private ImageButton btnReject;
    //		private String form_type;
    private ImageButton btnApprove;
    private ImageButton btnClose;
    private ToggleButton btnSearchBar;
    private AutoCompleteTextView txtSearch;
    //task abstract class
    private Task task;
    private QuestionReviewGenerator reviewGenerator;
    private boolean hasLoading = false;

    public static void updateMenuIcon(boolean isGPS) {
        UpdateMenuIcon uItem = new UpdateMenuIcon();
        uItem.updateGPSIcon(mainMenu);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        switch (requestCode) {
            case Utility.REQUEST_CODE_ASK_MULTIPLE_PERMISSIONS: {
                if (Utility.checkPermissionResult(DynamicFormActivity.this, permissions, grantResults))
                    bindLocationListener();
            }
            break;
            default:
                super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
    }

    private void bindLocationListener() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                Utility.checkPermissionGranted(DynamicFormActivity.this);
                return;
            } else {
                mLocation = (LocationManager) getSystemService(LOCATION_SERVICE);
                try {
                    if (mLocation.getAllProviders().contains(LocationManager.GPS_PROVIDER))
                        mLocation.requestSingleUpdate(LocationManager.GPS_PROVIDER, this, null);
                } catch (IllegalArgumentException e) {
                    FireCrash.log(e);        // TODO: handle exception
                } catch (Exception e) {
                    FireCrash.log(e);

                }
            }
        } else {
            mLocation = (LocationManager) getSystemService(LOCATION_SERVICE);
            try {
                if (mLocation.getAllProviders().contains(LocationManager.GPS_PROVIDER))
                    mLocation.requestSingleUpdate(LocationManager.GPS_PROVIDER, this, null);
            } catch (IllegalArgumentException e) {
                FireCrash.log(e);    // TODO: handle exception
            } catch (Exception e) {
                FireCrash.log(e);

            }
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (Tool.isProdFlavour(Global.FLAVORS)) {
            //To can't screenshoot
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
        }

        setContentView(R.layout.dynamic_form);
        this.getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        overridePendingTransition(R.anim.activity_open_translate, R.anim.activity_close_scale);
        getActionBar().setDisplayHomeAsUpEnabled(true);
        ACRA.getErrorReporter().putCustomData("LAST_CLASS_ACCESSED", getClass().getSimpleName());
//	        getActionBar().setHomeButtonEnabled(true);
        new AsyncTask<Void, Void, Void>() {

            @Override
            protected Void doInBackground(Void... params) {
                List<Scheme> schemes = SchemeDataAccess.getAll(getApplicationContext());
                Global.TempScheme = new HashMap<String, Date>();

                for (Scheme scheme : schemes) {
                    Global.TempScheme.put(scheme.getUuid_scheme(), scheme.getScheme_last_update());
                }

                Global.SchemeIsChange = true;
                return null;
            }
        }.execute();
        bindLocationListener();
        initialize();
    }


    @Override
    protected void attachBaseContext(Context newBase) {
        Context context = newBase;
        Locale locale;
        try {
            locale = new Locale(GlobalData.getSharedGlobalData().getLocale());
            if (null == locale) {
                locale = new Locale(LocaleHelper.ENGLSIH);
            }
            context = LocaleHelper.wrap(newBase, locale);
        } catch (Exception e) {
            locale = new Locale(LocaleHelper.ENGLSIH);
            context = LocaleHelper.wrap(newBase, locale);
        } finally {
            super.attachBaseContext(context);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Utility.freeMemory();
        Constant.listOfQuestion = null;
        CustomerFragment.header = null;
        listOfIdentifier = null;
        listOfQuestion = null;
        //DynamicFormActivity.header = null;
        DynamicFormActivity.idxQuestion = 0;
        DynamicFormActivity.idxPosition = 0;
        isApproval = false;
        isVerified = false;
        allowImageEdit = true;
        if (mLocation != null)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                    // TODO: Consider calling
                    //    Activity#requestPermissions
                    // here to request the missing permissions, and then overriding
                    //   public void onRequestPermissionsResult(int requestCode, String[] permissions,
                    //                                          int[] grantResults)
                    // to handle the case where the user grants the permission. See the documentation
                    // for Activity#requestPermissions for more details.
                    return;
                } else {
                    mLocation.removeUpdates(this);
                }
            } else {
                mLocation.removeUpdates(this);
            }
        /*header=null;
        questionContainer=null;
		reviewContainer=null;
		searchContainer=null;
		scrollView=null;
		scrollView2=null;*/
    }

    private void deleteLatestPictureCreate() {
        try {
            getContentResolver().delete(MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                    MediaStore.Images.Media.DATA
                            + "='"
                            + mCurrentPhotoPath
                            + "'", null);

            File f = Environment.getExternalStoragePublicDirectory(
                    Environment.DIRECTORY_PICTURES);
            File[] files = f.listFiles();
            Arrays.sort(files, new Comparator() {
                public int compare(Object o1, Object o2) {

                    if (((File) o1).lastModified() > ((File) o2).lastModified()) {
                        return -1;
                    } else if (((File) o1).lastModified() < ((File) o2).lastModified()) {
                        return +1;
                    } else {
                        return 0;
                    }
                }
            });
            files[0].delete();
        } catch (Exception e) {
            FireCrash.log(e);
            if (Global.IS_DEV)
                e.printStackTrace();
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent result) {
        super.onActivityResult(requestCode, resultCode, result);
        Utility.freeMemory();
        if (requestCode == Utils.REQUEST_CAMERA && resultCode == Activity.RESULT_OK) {
            try {
                Bundle extras = getIntent().getExtras();
                if (null != extras) {
                    int quality = Utils.picQuality;
                    int thumbHeight = Utils.picHeight;
                    int thumbWidht = Utils.picWidth;
                    Intent intent = new Intent(getBaseContext(), CameraActivity.class);
                    intent.putExtra(CameraActivity.PICTURE_WIDTH, thumbWidht);
                    intent.putExtra(CameraActivity.PICTURE_HEIGHT, thumbHeight);
                    intent.putExtra(CameraActivity.PICTURE_QUALITY, quality);
                    startActivityForResult(intent, Utils.REQUEST_IN_APP_CAMERA);
                }
                File file = new File(mCurrentPhotoPath);
                processImageFile(file);

//                new ProcessingBitmap().executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, file);
//					File filesave = new File(getFilesDir()+"/imgShoot");
//	    			Bitmap bmp_data = Utils.pathToBitmap(file);

                /*ExifData exifData = Utils.getDataOnExif(file);
                int rotate = exifData.getOrientation();
                float scale;
                int newSize = 0;
                int quality = Utils.picQuality;
                int thumbHeight = Utils.picHeight;
                int thumbWidht = Utils.picWidth;
                QuestionBean bean = DynamicFormActivity.getQuestionInFocus();

                if (bean.getImg_quality() != null && bean.getImg_quality().equalsIgnoreCase(Global.IMAGE_HQ)) {
                    thumbHeight = Utils.picHQHeight;
                    thumbWidht = Utils.picHQWidth;
                    quality = Utils.picHQQuality;
                }

                boolean isHeightScale = thumbHeight >= thumbWidht ? true : false;

                *//*if (isHeightScale) {
                    scale = (float) thumbHeight / exifData.getHeight();
                    newSize = Math.round(exifData.getWidth() * scale);
                    bmp_data = Utils.decodeSampledBitmapFromResource(file, newSize, thumbHeight);
                    _data = Utils.resizeBitmapFileWithWatermark(file, rotate, newSize, thumbHeight, quality, this);
                } else {
                    scale = (float) thumbWidht / exifData.getWidth();
                    newSize = Math.round(exifData.getHeight() * scale);
                    bmp_data = Utils.decodeSampledBitmapFromResource(file, thumbWidht, newSize);
                    _data = Utils.resizeBitmapFileWithWatermark(file, rotate, thumbWidht, newSize, quality, this);
                }*//*
                System.gc();
                byte[] _data = null;
                try {
                    if (isHeightScale) {
                        scale = (float) thumbHeight / exifData.getHeight();
                        newSize = Math.round(exifData.getWidth() * scale);
                        _data = Utils.resizeBitmapFileWithWatermark(file, rotate, newSize, thumbHeight, quality, this);
                    } else {
                        scale = (float) thumbWidht / exifData.getWidth();
                        newSize = Math.round(exifData.getHeight() * scale);
                        _data = Utils.resizeBitmapFileWithWatermark(file, rotate, thumbWidht, newSize, quality, this);
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    _data = null;
                }
                if (_data != null) {
                    deleteLatestPictureCreate();
                    try {
                        if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.KITKAT) {
                            Utils.deleteLatestPicture();
                        }
                    } catch (Exception e) {
                    FireCrash.log(e);
                        try {
                            String manufacture = android.os.Build.MANUFACTURER;
                            if (manufacture.contains("LGE")) {
                                Utils.deleteLatestPictureLGE();
                            }
                        }catch (Exception e2) {
                    FireCrash.log(e2);        }
                    }


                    DynamicFormActivity.saveImage(_data);

                    boolean getGPS = true;


                    LocationInfo locBean = null;
                    String indicatorGPS = "";
//                    byte[] imgLocation = null;
                    boolean isGeoTagged = Global.AT_IMAGE_W_LOCATION.equals(bean.getAnswer_type());
                    boolean isGeoTaggedGPSOnly = Global.AT_IMAGE_W_GPS_ONLY.equals(bean.getAnswer_type());
                    if (isGeoTagged) {
                        LocationTrackingManager pm = Global.LTM;
                        if (pm != null) {
                            locBean = pm.getCurrentLocation(Global.FLAG_LOCATION_CAMERA);
                            LocationInfo2 infoFinal = new LocationInfo2(locBean);

                            if (infoFinal.getLatitude().equals("0.0") || infoFinal.getLongitude().equals("0.0")) {
                                if (infoFinal.getMcc().equals("0") || infoFinal.getMnc().equals("0")) {
                                    if (bean.isMandatory()) {
                                        bean.setLocationInfo(infoFinal);
                                        String geodataError = getString(R.string.geodata_error);
                                        String[] msg = {geodataError};
                                        String alert2 = Tool.implode(msg, "\n");
                                        Toast.makeText(this, alert2, Toast.LENGTH_LONG).show();
                                        DynamicFormActivity.saveImage(null);
                                        DynamicFormActivity.saveImageLocation(null);
                                        getGPS = false;
                                    }
                                }
                                else{
                                    bean.setAnswer(getString(R.string.coordinat_not_available));
                                    bean.setLocationInfo(infoFinal);
                                    indicatorGPS =bean.getAnswer();
                                    if (bean.isMandatory()) {
                                        String gpsError = getString(R.string.gps_gd_error);
                                        String[] msg = {gpsError};
                                        String alert2 = Tool.implode(msg, "\n");
                                        Toast.makeText(this, alert2, Toast.LENGTH_LONG).show();
                                    }
                                }
                            } else {
                                bean.setAnswer(LocationTrackingManager.toAnswerString_short(infoFinal));
                                bean.setLocationInfo(infoFinal);
                                indicatorGPS =
                                        bean.getAnswer();
//									try {
//										imgLocation = new GetLocationImage(getApplicationContext(), locBean).execute().get();
//										DynamicFormActivity.saveImageLocation(imgLocation);
//									} catch (Exception e) {
                    FireCrash.log(e);
//									}
                            }
                        }
//							try {
//								imgLocation = new GetLocationImage(getApplicationContext(), locBean).execute().get();
//								DynamicFormActivity.saveImageLocation(imgLocation);
//							} catch (Exception e) {
                    FireCrash.log(e);
//							}
                    }

                    if (isGeoTaggedGPSOnly) {
                        LocationTrackingManager pm = Global.LTM;
                        if (pm != null) {
                            locBean = pm.getCurrentLocation(Global.FLAG_LOCATION_CAMERA);
                            LocationInfo2 infoFinal = new LocationInfo2(locBean);
                            if (infoFinal.getLatitude().equals("0.0") || infoFinal.getLongitude().equals("0.0")) {

                                if (bean.isMandatory()) {
                                    bean.setLocationInfo(infoFinal);
                                    String gpsError = getString(R.string.gps_error);
                                    String[] msg = {gpsError};
                                    String alert2 = Tool.implode(msg, "\n");
                                    Toast.makeText(this, alert2, Toast.LENGTH_LONG).show();
                                    DynamicFormActivity.saveImage(null);
                                    DynamicFormActivity.saveImageLocation(null);
                                    getGPS = false;
                                }

                            } else {
                                bean.setAnswer(LocationTrackingManager.toAnswerString_short(infoFinal));
                                bean.setLocationInfo(infoFinal);
                                indicatorGPS =
                                        bean.getAnswer();
//									try {
//										imgLocation = new GetLocationImage(getApplicationContext(), locBean).execute().get();
//										DynamicFormActivity.saveImageLocation(imgLocation);
//									} catch (Exception e) {
                    FireCrash.log(e);
//									}
                            }
                        }
                    }

                    // set thumbnail
                    if (DynamicFormActivity.getThumbInFocus() != null && getGPS) {
                        Bitmap bm = BitmapFactory.decodeByteArray(_data, 0, _data.length);

                        int[] res = Tool.getThumbnailResolution(bm.getWidth(), bm.getHeight());
                        Bitmap thumbnail = Bitmap.createScaledBitmap(bm, res[0], res[1], true);
                        DynamicFormActivity.setThumbInFocusImage(thumbnail);
                        long size = bean.getImgAnswer().length;
                        String formattedSize = Formatter.formatByteSize(size);
                        if (isGeoTagged || isGeoTaggedGPSOnly) {
                            try {
//									Bitmap bitmapLocation = BitmapFactory.decodeByteArray(imgLocation, 0, imgLocation.length);;
//									int[] res2 = Tool.getThumbnailResolution(bitmapLocation.getWidth(), bitmapLocation.getHeight());
                                Bitmap thumbLocation = BitmapFactory.decodeResource(getResources(), R.drawable.ic_absent);
//											Bitmap.createScaledBitmap(bitmapLocation, res[0], res[1], true);
                                try {
                                    DynamicFormActivity.setThumbLocationInfoImage(thumbLocation);
                                } catch (Exception e) {
                    FireCrash.log(e);
                                    DynamicFormActivity.setThumbLocationInfoImage(null);
                                }
                            } catch (Exception e) {
                    FireCrash.log(e);
//									System.out.println(e.getMessage());
                            }
                            DynamicFormActivity.setTxtDetailInFocus(bm.getWidth() + " x " + bm.getHeight() +
                                    ". Size " + formattedSize + "\n" + indicatorGPS);
                        } else {
                            DynamicFormActivity.setTxtDetailInFocus(bm.getWidth() + " x " + bm.getHeight() +
                                    ". Size " + formattedSize);
                        }
                        if (bm != null)
                            bm.recycle();
                    }
                    System.gc();
                } else {
                    Toast.makeText(getApplicationContext(), getString(R.string.camera_error), Toast.LENGTH_SHORT).show();
                }
                */
            } catch (OutOfMemoryError e) {
                FireCrash.log(e);

                Toast.makeText(getBaseContext(), getString(R.string.processing_image_error), Toast.LENGTH_SHORT).show();
                Utility.freeMemory();
            } catch (Exception e) {
                FireCrash.log(e);
                Toast.makeText(getBaseContext(), getString(R.string.camera_error), Toast.LENGTH_SHORT).show();
            }
        } else if (requestCode == Utils.REQUEST_IN_APP_CAMERA) {
            if (resultCode == RESULT_OK) {
                try {
                    Uri uri = Uri.parse(result.getStringExtra(CameraActivity.PICTURE_URI));
                    File file = new File(uri.getPath());
//                    new ProcessingBitmap().executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR, file);
                    processImageFile(file);
                } catch (OutOfMemoryError e) {
                    FireCrash.log(e);

                    Toast.makeText(getBaseContext(), getString(R.string.processing_image_error), Toast.LENGTH_SHORT).show();
                    Utility.freeMemory();
                } catch (Exception e) {
                    FireCrash.log(e);
                    Toast.makeText(getBaseContext(), getString(R.string.camera_error), Toast.LENGTH_SHORT).show();
                }
            }
        } else if (requestCode == Global.REQUEST_LOCATIONTAGGING && resultCode == Activity.RESULT_OK) {
            QuestionBean bean = DynamicFormActivity.getQuestionInFocus();
            LocationInfo info = LocationTagingView.locationInfo;
            LocationInfo2 infoFinal = new LocationInfo2(info);
            if (bean.getAnswer_type().equals(Global.AT_LOCATION_WITH_ADDRESS))
                bean.setAnswer(LocationTrackingManager.toAnswerString_short(infoFinal,LocationTagingView.address));
            else
                bean.setAnswer(LocationTrackingManager.toAnswerString_short(infoFinal));
            bean.setLocationInfo(infoFinal);
//		    	DynamicQuestion.setQuestionInFocus(bean.getAnswer());
//				DynamicQuestion.setTxtDetailInFocus(bean.getAnswer());
            DynamicFormActivity.setTxtInFocusText(bean.getAnswer());

        } else if (requestCode == Global.REQUEST_VOICE_NOTES && resultCode == Activity.RESULT_OK) {
            byte[] voiceNotes = result.getByteArrayExtra(Global.BUND_KEY_DETAIL_DATA);
            if (voiceNotes != null && voiceNotes.length > 0) {
                header.setVoice_note(voiceNotes);
            }
        }
        Utility.freeMemory();
    }

    private void processImageFile(File file) {
        try {
            String formattedSize;
            String indicatorGPS = "";
            boolean isGeoTagged;
            boolean isGeoTaggedGPSOnly;
            int[] res;
            ExifData exifData = Utils.getDataOnExif(file);
            int rotate = exifData.getOrientation();
            float scale;
            int newSize = 0;
            int quality = Utils.picQuality;
            int thumbHeight = Utils.picHeight;
            int thumbWidht = Utils.picWidth;
            QuestionBean bean = DynamicFormActivity.getQuestionInFocus();
            boolean isHQ = false;
            if (bean.getImg_quality() != null && bean.getImg_quality().equalsIgnoreCase(Global.IMAGE_HQ)) {
                thumbHeight = Utils.picHQHeight;
                thumbWidht = Utils.picHQWidth;
                quality = Utils.picHQQuality;
                isHQ = true;
            }

            boolean isHeightScale = thumbHeight >= thumbWidht;

            System.gc();
            byte[] data = null;
            try {
                if (isHeightScale) {
                    scale = (float) thumbHeight / exifData.getHeight();
                    newSize = Math.round(exifData.getWidth() * scale);
                    data = Utils.resizeBitmapFileWithWatermark(file, rotate, newSize, thumbHeight, quality, DynamicFormActivity.this, isHQ);
                } else {
                    scale = (float) thumbWidht / exifData.getWidth();
                    newSize = Math.round(exifData.getHeight() * scale);
                    data = Utils.resizeBitmapFileWithWatermark(file, rotate, thumbWidht, newSize, quality, DynamicFormActivity.this, isHQ);
                }
            } catch (Exception e) {
                FireCrash.log(e);
                data = null;
            }
            if (data != null) {
                deleteLatestPictureCreate();
                try {
                    if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.KITKAT && !GlobalData.getSharedGlobalData().isUseOwnCamera()) {
                        Utils.deleteLatestPicture();
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    try {
                        String manufacture = android.os.Build.MANUFACTURER;
                        if (manufacture.contains("LGE") && !GlobalData.getSharedGlobalData().isUseOwnCamera()) {
                            Utils.deleteLatestPictureLGE();
                        }
                    } catch (Exception e2) {
                        FireCrash.log(e2);
                    }
                }

                DynamicFormActivity.saveImage(data);

                boolean getGPS = true;

                LocationInfo locBean;

                isGeoTagged = Global.AT_IMAGE_W_LOCATION.equals(bean.getAnswer_type());
                isGeoTaggedGPSOnly = Global.AT_IMAGE_W_GPS_ONLY.equals(bean.getAnswer_type());
                if (isGeoTagged) {
                    LocationTrackingManager pm = Global.LTM;
                    if (pm != null) {
                        locBean = pm.getCurrentLocation(Global.FLAG_LOCATION_CAMERA);
                        LocationInfo2 infoFinal = new LocationInfo2(locBean);

                        if (infoFinal.getLatitude().equals("0.0") || infoFinal.getLongitude().equals("0.0")) {
                            if (infoFinal.getMcc().equals("0") || infoFinal.getMnc().equals("0")) {
                                if (bean.isMandatory()) {
                                    bean.setLocationInfo(infoFinal);
                                    String geodataError = getString(R.string.geodata_error);
                                    String[] msg = {geodataError};
                                    String alert2 = Tool.implode(msg, "\n");
                                    Toast.makeText(this, alert2, Toast.LENGTH_LONG).show();
                                    DynamicFormActivity.saveImage(null);
                                    DynamicFormActivity.saveImageLocation(null);
                                    getGPS = false;
                                }
                            } else {
                                bean.setAnswer(getString(R.string.coordinat_not_available));
                                bean.setLocationInfo(infoFinal);
                                indicatorGPS = bean.getAnswer();
                                if (bean.isMandatory()) {
                                    String gpsError = getString(R.string.gps_gd_error);
                                    String[] msg = {gpsError};
                                    String alert2 = Tool.implode(msg, "\n");
                                    Toast.makeText(this, alert2, Toast.LENGTH_LONG).show();
                                }
                            }
                        } else {
                            bean.setAnswer(LocationTrackingManager.toAnswerString_short(infoFinal));
                            bean.setLocationInfo(infoFinal);
                            indicatorGPS = bean.getAnswer();
                        }
                    }
                }

                if (isGeoTaggedGPSOnly) {
                    LocationTrackingManager pm = Global.LTM;
                    if (pm != null) {
                        locBean = pm.getCurrentLocation(Global.FLAG_LOCATION_CAMERA);
                        LocationInfo2 infoFinal = new LocationInfo2(locBean);
                        if (infoFinal.getLatitude().equals("0.0") || infoFinal.getLongitude().equals("0.0")) {

                            if (bean.isMandatory()) {
                                bean.setLocationInfo(infoFinal);
                                String gpsError = getString(R.string.gps_error);
                                String[] msg = {gpsError};
                                String alert2 = Tool.implode(msg, "\n");
                                Toast.makeText(this, alert2, Toast.LENGTH_LONG).show();
                                DynamicFormActivity.saveImage(null);
                                DynamicFormActivity.saveImageLocation(null);
                                getGPS = false;
                            }

                        } else {
                            bean.setAnswer(LocationTrackingManager.toAnswerString_short(infoFinal));
                            bean.setLocationInfo(infoFinal);
                            indicatorGPS = bean.getAnswer();
                        }
                    }
                }

                // set thumbnail
                if (DynamicFormActivity.getThumbInFocus() != null && getGPS) {
                    Bitmap bm = BitmapFactory.decodeByteArray(data, 0, data.length);
                    res = new int[2];
                    res[0] = bm.getWidth();
                    res[1] = bm.getHeight();
                    int[] thumbRes = Tool.getThumbnailResolution(bm.getWidth(), bm.getHeight());
                    Bitmap bitmap = Bitmap.createScaledBitmap(bm, thumbRes[0], thumbRes[1], true);

                    long size = bean.getImgAnswer().length;
                    formattedSize = Formatter.formatByteSize(size);

//                    DynamicFormActivity.getThumbInFocus().setImageBitmap(bitmap);
                    DynamicFormActivity.setThumbInFocusImage(bitmap);
                    if (isGeoTagged || isGeoTaggedGPSOnly) {
                        try {
                            Bitmap thumbLocation = BitmapFactory.decodeResource(getResources(), R.drawable.ic_absent);
//                                DynamicFormActivity.getThumbLocationInfo().setImageBitmap(thumbLocation);
                            DynamicFormActivity.setThumbLocationInfoImage(thumbLocation);

                        } catch (Exception e) {
                            FireCrash.log(e);
                            if (Global.IS_DEV)
                                e.printStackTrace();
                        }
                        String text = res[0] + " x " + res[1] +
                                ". Size " + formattedSize + "\n" + indicatorGPS;
                        DynamicFormActivity.setTxtDetailInFocus(text);

                    } else {

                        String text = res[0] + " x " + res[1] +
                                ". Size " + formattedSize;
                        DynamicFormActivity.setTxtDetailInFocus(text);

                    }
                }
            }
        } catch (OutOfMemoryError e) {
            FireCrash.log(e);

            Toast.makeText(DynamicFormActivity.this, DynamicFormActivity.this.getString(R.string.processing_image_error), Toast.LENGTH_SHORT).show();
        } catch (Exception e) {
            FireCrash.log(e);
            Toast.makeText(DynamicFormActivity.this, DynamicFormActivity.this.getString(R.string.camera_error), Toast.LENGTH_SHORT).show();
        }
    }

    private void initialize() {
        Bundle extras = getIntent().getExtras();
        mode = extras.getInt(Global.BUND_KEY_MODE_SURVEY);
//			header =(SurveyHeaderBean) extras.getSerializable(Global.BUND_KEY_SURVEY_BEAN);
        header = null;
        header = CustomerFragment.header;
//			header = TaskHDataAccess.getOneHeader(getApplicationContext(), uuid_taskH);
        task = (Task) extras.getSerializable(Global.BUND_KEY_TASK);
        listOfQuestion = new LinkedHashMap<String, QuestionBean>();

        listOfQuestion = Constant.listOfQuestion;

        viewGenerator = new QuestionViewGenerator();
        reviewGenerator = new QuestionReviewGenerator();
//			form_type = header.getScheme().getForm_type();
        jexlEngine = new JexlEngine();//new JexlBuilder().create();
        isSimulasi = extras.getBoolean(Global.BUND_KEY_MODE_SIMULASI, false);

        //----------------
        //---Gigin : save to temporary data

		/*if(listOfQuestion!=null && listOfQuestion.size()>0) {
            try {
				dfdq.setSelectedHeader(header);
				dfdq.setListOfQuestion(new ArrayList<QuestionBean>(listOfQuestion.values()));
				dfdq.setSelectedForm(Constant.selectedForm);
				dfdq.setMode(mode);
				new SavingDynamicData().execute();
			}
			catch (Exception e){
				e.printStackTrace();
			}

		}*/
        //SaveDataToTemporary(dfdq);
        //-----------------

        // Glen 7 Aug 2014, set previewNeedServer Flag
        if (Constant.selectedForm != null) {
            this.previewNeedServer = Constant.selectedForm
                    .isPreviewServer();
            // header.setPreviewNeedServer(previewNeedServer);
        } else {
            // this.previewNeedServer = header.previewNeedServer();
            try {
                this.previewNeedServer = Formatter.stringToBoolean(header.getIs_preview_server());
            } catch (Exception e) {
                FireCrash.log(e);
            }
        }

        try {
            handler = new DynamicSurveyHandler();
            if (header.getPriority() != null && header.getPriority().length() > 0) {
                if (header.getStart_date() == null) {
                    header.setStart_date(Tool.getSystemDateTime());
                    new CustomerFragment.SendOpenReadTaskH(DynamicFormActivity.this, header).execute();
                }
            }
        } catch (Exception e) {
            FireCrash.log(e);
            // ** 20 feb 2012
            // kasus pada TBG
            // start screen detail paging.setTotalRecord(listOfQuestion.size());
            // sering mendapatkan null, list questions nya tidak terisi
            // soo kalo eror dibaliin ke halaman sebelumnya dulu, suruh coba lg
            // aja

            // DialogManager.showAlert(DynamicSurveyActivity.this,
            // DialogManager.TYPE_ERROR,
            // "Failed open questions, please try again");
//            String[] msg = {"Failed open questions,\nplease try again"};
//            String alert = Tool.implode(msg, "\n");
            Toast.makeText(this, getResources().getString(R.string.failed_open_question), Toast.LENGTH_SHORT).show();
            try {
                // plan B
//					Intent intent = new Intent(this, CustomerActivity.class);
//					extras = new Bundle();
//					extras.putInt(Global.BUND_KEY_MODE_SURVEY,
//							Global.MODE_SURVEY_TASK);
//					extras.putSerializable(Global.BUND_KEY_SURVEY_BEAN,
//							MainMenuActivity.selectedSurvey);
//					intent.putExtras(extras);
//					this.startActivity(intent);
//					this.finish();
            } catch (Exception e2) {
                FireCrash.log(e2);// TODO : perlu perbaikan lagi plan C kalo plan B error,
//					Intent i = new Intent(this, SurveyListActivity.class);
//					this.startActivity(i);
//					this.finish();
            }

        }

        initScreenLayout();

        try {
            if (TaskHDataAccess.STATUS_TASK_APPROVAL.equalsIgnoreCase(header.getStatus())
                    || TaskHDataAccess.STATUS_TASK_APPROVAL_DOWNLOAD.equalsIgnoreCase(header.getStatus())
                    || mode == Global.MODE_VIEW_SENT_SURVEY) {
                try {
                    allowImageEdit = false;
                    isApproval = true;
                    btnClose.setClickable(false);
                    isFinish = true;
                    if (mode != Global.MODE_VIEW_SENT_SURVEY) {
                        isVerified = true;
                    }
                    showFinishScreen();
//						while (isApproval) {
//							try {
//								showFinishScreen();
////								doNext(true);
////								if(isFinish)
////									break;
//							} catch (Exception e) {
//								break;
//							}
//
//						}
                } catch (Exception e) {
                    FireCrash.log(e);
                    loadDynamicForm();
                }
            } else if (TaskHDataAccess.STATUS_TASK_VERIFICATION.equalsIgnoreCase(header.getStatus()) ||
                    TaskHDataAccess.STATUS_TASK_VERIFICATION_DOWNLOAD.equalsIgnoreCase(header.getStatus())) {
                try {
                    allowImageEdit = false;
                    isVerified = true;
                    hasLoading = true;
                    nextRunnable = new Runnable() {
                        @Override
                        public void run() {
                            nextHandler.postDelayed(nextRunnable, 400);
                            if (!doNext(false) || isStop) {
                                removeNextCallback();
                                btnVerified.setClickable(false);
                                btnVerified.setImageResource(R.drawable.ic_verified_off);
                                btnReject.setClickable(false);
                                btnReject.setImageResource(R.drawable.ic_reject_off);
                            } else if (isFinish)
                                removeNextCallback();
                        }
                    };
                    nextHandler = new Handler();
                    nextHandler.postDelayed(nextRunnable, 500);
                } catch (Exception e) {
                    FireCrash.log(e);
                    loadDynamicForm();
                }
            } else if (TaskHDataAccess.STATUS_SEND_SAVEDRAFT.equals(header.getStatus())) {
                try {
                    //loadDraftData();
                    hasLoading = true;
                    btnBack.setClickable(false);
                    btnNext.setClickable(false);
                    btnSend.setClickable(false);
                    btnSave.setClickable(false);
                    btnSearch.setClickable(false);

                    nextRunnable = new Runnable() {
                        @Override
                        public void run() {
                            nextHandler.postDelayed(nextRunnable, 400);
                            if (!doNext(false) || isStop) {
                                removeNextCallback();
                                synchronized (this) {
                                    this.notifyAll();
                                }
                            } else if (idxPosition >= header.getLast_saved_question()) {
                                removeNextCallback();
                                isStop = true;
                                synchronized (this) {
                                    this.notifyAll();
                                }
                            }
                        }
                    };
                    nextHandler = new Handler();
                    nextHandler.postDelayed(nextRunnable, 500);
                } catch (Exception e) {
                    FireCrash.log(e);
                    loadDynamicForm();
                }

            } else {
                loadDynamicForm();
            }
        } catch (Exception e) {
            FireCrash.log(e);
//			loadDynamicForm();
            Toast.makeText(getBaseContext(), getString(R.string.request_error), Toast.LENGTH_SHORT).show();
            this.finish();
        }

    }


    private void SaveDataToTemporary(DataForDynamicQuestion dfdq2) {

        try {
            String tempDynamicdata = GsonHelper.toJson(dfdq2);

            ObscuredSharedPreferences sharedPref = ObscuredSharedPreferences.getPrefs(this,
                    "TempDynamicData", Context.MODE_PRIVATE);
            ObscuredSharedPreferences.Editor sharedPrefEditor = sharedPref.edit();
            sharedPrefEditor.putString("SelectedDynamicData", tempDynamicdata);
            sharedPrefEditor.commit();

        } catch (Exception e) {
            FireCrash.log(e);
            if (Global.IS_DEV)
                e.printStackTrace();
        }
    }

    public void RestoreDataToTemporary(Context context) {
        try {
            if (listOfQuestion == null) {
                ObscuredSharedPreferences sharedPref = ObscuredSharedPreferences.getPrefs(context,
                        "TempDynamicData", Context.MODE_PRIVATE);
                String sTempDynamicdata = sharedPref.getString("SelectedDynamicData", "");
                if (sTempDynamicdata != null) {
                    new RestoreGlobalData().execute();
                    DataForDynamicQuestion tempDynamicdata = GsonHelper.fromJson(sTempDynamicdata, DataForDynamicQuestion.class);
                    mode = tempDynamicdata.getMode();
                    header = tempDynamicdata.getSelectedHeader();
                    CustomerFragment.header = header;
                    for (QuestionBean bean : tempDynamicdata.getListOfQuestion()) {
                        if (listOfQuestion == null)
                            listOfQuestion = new LinkedHashMap<String, QuestionBean>();
                        if (listOfIdentifier == null)
                            listOfIdentifier = new ArrayList<String>();
                        listOfQuestion.put(bean.getIdentifier_name(), bean);
                        listOfIdentifier.add(bean.getIdentifier_name());
                    }
                    if (listOfQuestion == null)
                        listOfQuestion = new LinkedHashMap<String, QuestionBean>();
                    Constant.listOfQuestion = listOfQuestion;
                    Constant.selectedForm = tempDynamicdata.getSelectedForm();
                }
            }
        } catch (Exception e) {
            FireCrash.log(e);
        }
    }

//		public static List<QuestionBean> getListOfQuestion() {
//			return listOfQuestion;
//		}

    public void loadDraftData() {
        new AsyncTask<Void, Void, Void>() {
            private ProgressDialog progressDialog;

            @Override
            protected void onPreExecute() {
                progressDialog = ProgressDialog.show(getBaseContext(),
                        "", getString(R.string.progressWait), true);

            }

            @Override
            protected Void doInBackground(Void... params) {

                try {
                    int i = 0;
                    while (i < header.getLast_saved_question()) {
                        if (!doNext(true))
                            break;
                        i++;
                    }
                    return null;
                } catch (Exception e) {
                    FireCrash.log(e);
                    if (Global.IS_DEV)
                        e.printStackTrace();
                    return null;
                }
            }

            protected void onPostExecute() {
                super.onPostExecute(null);
                if (progressDialog != null && progressDialog.isShowing()) {
                    try {
                        progressDialog.dismiss();
                    } catch (Exception e) {
                        FireCrash.log(e);
                    }
                }
            }
        }.execute();
    }

    private void initScreenLayout() {
        btnBack = (ImageButton) findViewById(R.id.btnBack);
        btnNext = (ImageButton) findViewById(R.id.btnNext);
        btnSend = (ImageButton) findViewById(R.id.btnSend);
        btnSave = (ImageButton) findViewById(R.id.btnSave);
        btnSearch = (ImageButton) findViewById(R.id.btnSearch);
        btnVerified = (ImageButton) findViewById(R.id.btnVerified);
        btnReject = (ImageButton) findViewById(R.id.btnReject);
        btnApprove = (ImageButton) findViewById(R.id.btnApprove);
        btnClose = (ImageButton) findViewById(R.id.btnClose);
        btnSearchBar = (ToggleButton) findViewById(R.id.btnSearchBar);

        adapter = new ArrayAdapter<String>(this, R.layout.autotext_list, questionLabel);
        txtSearch = (AutoCompleteTextView) findViewById(R.id.autoCompleteSearch);
        txtSearch.setAdapter(adapter);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            txtSearch.setDropDownBackgroundDrawable(getResources().getDrawable(R.drawable.actionbar_background, getTheme()));
        } else {
            txtSearch.setDropDownBackgroundDrawable(getResources().getDrawable(R.drawable.actionbar_background));
        }
        txtSearch.setOnFocusChangeListener(new View.OnFocusChangeListener() {

            @Override
            public void onFocusChange(View arg0, boolean hasFocused) {

                if (hasFocused) {
                    adapter.notifyDataSetChanged();
                    txtSearch.setAdapter(adapter);
                }
            }
        });

        btnBack.setOnClickListener(this);
        btnNext.setOnClickListener(this);
        btnVerified.setOnClickListener(this);
        btnSave.setOnClickListener(this);
        btnSearch.setOnClickListener(this);
        btnReject.setOnClickListener(this);
        btnApprove.setOnClickListener(this);
        btnSearchBar.setOnClickListener(this);
        btnClose.setOnClickListener(this);


        questionContainer = (LinearLayout) findViewById(R.id.questionContainer);
        reviewContainer = (LinearLayout) findViewById(R.id.reviewContainer);
        scrollView = (ScrollView) findViewById(R.id.scrollContainer);
        scrollView2 = (ScrollView) findViewById(R.id.scrollContainer2);
        searchContainer = (RelativeLayout) findViewById(R.id.searchLayout);
        searchContainer.setVisibility(View.GONE);

        LinearLayout sendLayout = (LinearLayout) findViewById(R.id.btnSendLayout);
        LinearLayout verifyLayout = (LinearLayout) findViewById(R.id.btnVerifiedLayout);
        LinearLayout rejectLayout = (LinearLayout) findViewById(R.id.btnRejectLayout);
        LinearLayout approveLayout = (LinearLayout) findViewById(R.id.btnApproveLayout);
        LinearLayout nextLayout = (LinearLayout) findViewById(R.id.btnNextLayout);
        LinearLayout saveLayout = (LinearLayout) findViewById(R.id.btnSaveLayout);
        LinearLayout searchLayout = (LinearLayout) findViewById(R.id.btnSearchLayout);
        LinearLayout backLayout = (LinearLayout) findViewById(R.id.btnBackLayout);
        LinearLayout closeLayout = (LinearLayout) findViewById(R.id.btnCloseLayout);

        try {
            if (TaskHDataAccess.STATUS_TASK_VERIFICATION.equalsIgnoreCase(header.getStatus()) ||
                    TaskHDataAccess.STATUS_TASK_VERIFICATION_DOWNLOAD.equalsIgnoreCase(header.getStatus())) {
                backLayout.setVisibility(View.GONE);
                sendLayout.setVisibility(View.GONE);
                saveLayout.setVisibility(View.GONE);
                approveLayout.setVisibility(View.GONE);
                //ganti ke halaman baaru
                if (!Global.NEW_FEATURE) {
                    rejectLayout.setVisibility(View.VISIBLE);
                    verifyLayout.setVisibility(View.VISIBLE);
                }

            }
            if (TaskHDataAccess.STATUS_TASK_APPROVAL.equalsIgnoreCase(header.getStatus()) ||
                    TaskHDataAccess.STATUS_TASK_APPROVAL_DOWNLOAD.equalsIgnoreCase(header.getStatus())) {
                backLayout.setVisibility(View.GONE);
                sendLayout.setVisibility(View.GONE);
                searchLayout.setVisibility(View.GONE);
                verifyLayout.setVisibility(View.GONE);
                saveLayout.setVisibility(View.GONE);
                if (!Global.NEW_FEATURE) {
                    nextLayout.setVisibility(View.GONE);
                    rejectLayout.setVisibility(View.VISIBLE);
                    approveLayout.setVisibility(View.VISIBLE);
                }
                searchContainer.setVisibility(View.GONE);
            }
        } catch (Exception e) {
            FireCrash.log(e);

        }
        if (mode == Global.MODE_VIEW_SENT_SURVEY) {
            backLayout.setVisibility(View.GONE);
            nextLayout.setVisibility(View.GONE);
            sendLayout.setVisibility(View.GONE);
            searchLayout.setVisibility(View.GONE);
            verifyLayout.setVisibility(View.GONE);
            rejectLayout.setVisibility(View.GONE);
            approveLayout.setVisibility(View.GONE);
            saveLayout.setVisibility(View.GONE);
            closeLayout.setVisibility(View.VISIBLE);
            searchContainer.setVisibility(View.GONE);
        }
        if (isSimulasi) {
            saveLayout.setVisibility(View.GONE);
            sendLayout.setVisibility(View.GONE);
        }

        if (questionGroupFieldsHeader == null) questionGroupFieldsHeader = new ArrayList<>();
        questionGroupFieldsHeader.clear();
    }

    private void loadOptionsToView(MultiOptionQuestionViewAbstract view) {
        List<OptionAnswerBean> options = getOptionsForQuestion(view.getQuestionBean());
        view.setOptions(this, options);
    }


    private String getTestid(String id) {
        String result = "";
        if (id.equals("test_test")) return "halo";
        else if (id.equals("test_2")) return "hehe";
        else if (id.equals("test_3")) return "15,2";
        else if (id.equals("test_4")) return "16.1";

        return result;
    }

    public boolean isRelevantMandatory2(String relevantExpression, QuestionBean question) {
        boolean result = false;
        String convertedExpression = relevantExpression;        //make a copy of
        if (question.isMandatory()) {
            return true;
        } else if (convertedExpression == null || convertedExpression.length() == 0) {
            return false;
        } else {

            //TODO, use extractIdentifierFromString next time to simplify
            boolean needReplacing = true;
            while (needReplacing) {

                //replace application modifier
                convertedExpression = replaceModifiers(convertedExpression);

                int idxOfOpenBrace = convertedExpression.indexOf('{');
                if (idxOfOpenBrace != -1) {                    //there's {, prepare to replace what inside the {}
                    int idxOfCloseBrace = convertedExpression.indexOf('}');
                    String identifier = convertedExpression.substring(idxOfOpenBrace + 1, idxOfCloseBrace);
                    int idxOfOpenAbs = identifier.indexOf("$");
                    if (idxOfOpenAbs != -1) {
                        String finalIdentifier = identifier.substring(idxOfOpenAbs + 1);
                        String flatAnswer = "";

                        if (finalIdentifier.equals(Global.IDF_LOGIN_ID)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getLogin_id();
                            int idxOfOpenAt = flatAnswer.indexOf('@');
                            if (idxOfOpenAt != -1) {
                                flatAnswer = flatAnswer.substring(0, idxOfOpenAt);
                            }
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_ID)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_id();
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_NAME)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_name();
                        } else if (finalIdentifier.equals(Global.IDF_UUID_USER)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                        } else if (finalIdentifier.equals(Global.IDF_JOB)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getFlag_job();
                        } else if (finalIdentifier.equals(Global.IDF_DEALER_NAME)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getDealer_name();
                        }


                        if (flatAnswer != null && flatAnswer.length() > 0) {
                            convertedExpression = convertedExpression.replace("{" + identifier + "}", flatAnswer);
                        } else {            //if there's no answer, just hide the question
                            return false;
                        }

                    } else {
                        QuestionBean bean = listOfQuestion.get(identifier);

                        if (bean != null) {

                            // {SVY_PRODUCT}!='KSM' || {SVY_CLUSTER}=='B' || {SVY_CLUSTER}=='C'

                            //Glen 21 Oct 2014, if it relate to question which is not visible, make it not visible too
                            // if (bean.getIs_visible().equals(Global.FALSE_STRING)) return false;

                            String flatAnswer = QuestionBean.getAnswer(bean);

                            if (Tool.isOptions(bean.getAnswer_type())) {
                                try {
                                    flatAnswer = bean.getSelectedOptionAnswers().get(0).getCode();
                                } catch (Exception e) {
                                    FireCrash.log(e);

                                }
                            }

                            if (flatAnswer != null && flatAnswer.length() > 0) {
                                //Glen 22 Oct 2014, enable multi-depth checking for 'multiple' question
                                //NOTE: though it's possible to just iterate on flatAnswer substrings, we prefer to stay on method if size is 1
                                String answers[] = Tool.split(flatAnswer, Global.DELIMETER_DATA);
                                if (answers.length == 1) {
//									convertedExpression = convertedExpression.replace("{"+identifier+"}", flatAnswer);
                                    convertedExpression = convertedExpression.replace("{" + identifier + "}", answers[0]);
                                } else {
                                    //NOTE: going into in-depth loop, won't go outside of this 'else'
                                    for (int i = 0; i < answers.length; i++) {
                                        String convertedSubExpression = convertedExpression.replace("{" + identifier + "}", answers[i]);
                                        boolean isVisible = isQuestVisibleIfRelevant(convertedSubExpression, question);
                                        if (isVisible) {
                                            return true;
                                        }
                                    }
                                    return false;
                                }

                                //Glen 16 Oct 2014, added as affected bean visibility
//								bean.addToAffectedQuestionBeans(question);
                                //bean.addToAffectedQuestionBeanVisibility(question);
                            } else {            //if there's no answer, just hide the question
                                return false;
                            }
                        } else {
//							convertedExpression.replaceAll("{"+identifier+"}", "");
                            convertedExpression = convertedExpression.replace("{" + identifier + "}", "\"\"");
                        }
                    }
                }
                //moved up
//					else if (convertedExpression.indexOf('r') != -1){
//						convertedExpression = replaceModifiers(convertedExpression);
//					}

                //no more replacing needed
                else {
                    needReplacing = false;
                }

            }
            try {
                OperatorSet opSet = OperatorSet.getStandardOperatorSet();
                opSet.addOperator("!=", NotEqualSymbol.class);
                Expression exp = new Expression(convertedExpression);
                exp.setOperatorSet(opSet);
                result = exp.evaluate().toBoolean();
                return result;
            } catch (Exception e) {
                FireCrash.log(e);
                e.printStackTrace();
                return false;
            }

        }
//			return result;
    }

    public boolean isRelevantMandatory(String expression) {
        boolean flag = false, finalResult = false;
        int i, start = 0, end = 0;
        boolean tempResult = false;
        String tempString = "";
        Stack<Boolean> results = new Stack<>();
        Stack<Boolean> operators = new Stack<>();
        expression = expression.replaceAll("\\s+", "");
        if (expression.equals("")) return false;
        else if (expression.charAt(1) == '{') {
            for (i = 0; i < expression.length(); i++) {
                switch (expression.charAt(i)) {
                    case '{': {
                        if (expression.charAt(i + 1) == '{') {
                            i++;
                            start = i;
                        }
                        break;
                    }
                    case '}': {
                        if (i == expression.length() - 1) {
                            end = i;
                            tempString = expression.substring(start, end);
                            tempResult = isRelevantMandatorySubString(tempString);
                            results.push(tempResult);
                        } else {
                            if (expression.charAt(i + 1) == '&' || expression.charAt(i + 1) == '|') {
                                end = i;
                                tempString = expression.substring(start, end);
                                tempResult = isRelevantMandatorySubString(tempString);
                                results.push(tempResult);
                            }
                        }

                        break;
                    }
                    case '|': {
                        if (expression.charAt(i - 1) == '}') operators.push(false);

                        i++;
                        break;
                    }
                    case '&': {
                        if (expression.charAt(i - 1) == '}') operators.push(true);
                        i++;
                        break;
                    }
                }
            }
            flag = true;
            while (!results.isEmpty()) {
                if (flag) {
                    finalResult = results.pop();
                    if (results.isEmpty()) return finalResult;
                    else {
                        tempResult = results.pop();
                        if (operators.pop()) finalResult = finalResult && tempResult;
                        else finalResult = finalResult || tempResult;
                        flag = false;
                    }
                } else {
                    tempResult = results.pop();
                    if (operators.pop()) finalResult = finalResult && tempResult;
                    else finalResult = finalResult || tempResult;
                }
            }
            return finalResult;
        } else return isRelevantMandatorySubString(expression);
    }

    public boolean isRelevantMandatorySubString(String expression) {
        Stack<Boolean> results = new Stack<>();
        Stack<Boolean> operators = new Stack<>();
        String temp = "", ans1 = "", ans2 = "";
        QuestionBean tempbean;
        boolean flag = false, finalResult = false, tempResult1 = false;
        int i, start = 0, end = 0, operator1 = 0;
        expression = expression.replaceAll("\\s+", "");
        expression = expression.replaceAll(",", ".");
        for (i = 0; i < expression.length(); i++) {
            switch (expression.charAt(i)) {
                case '{':
                    start = i + 1;
                    break;
                case '}':
                    end = i;
                    temp = expression.substring(start, end).toUpperCase();
                    if ((getQuestionBeanForIdentifier(temp) == null)) {
                        ans2 = "";
                    } else {
                        tempbean = getQuestionBeanForIdentifier(temp);
                        if (tempbean.getAnswer() != null && !tempbean.getAnswer().equals("")) {
                            ans2 = tempbean.getAnswer();
                        } else if (tempbean.getLovCode() != null && !tempbean.getLovCode().equals("")) {
                            ans2 = tempbean.getLovCode();
                        } else ans2 = "";
                    }
                    //ans2 = getTestid(temp); //test
                    start = 0;
                    end = 0;
                    break;
                case '=':
                    i++;
                    operator1 = 0;
                    break;
                case '!':
                    i++;
                    operator1 = 1;
                    break;
                case '>':
                    if (expression.charAt(i + 1) == '=') {
                        i++;
                        operator1 = 2;
                    } else operator1 = 3;
                    break;
                case '<':
                    if (expression.charAt(i + 1) == '=') {
                        i++;
                        operator1 = 4;
                    } else operator1 = 5;
                    break;
                case '\'':
                    if (flag) {
                        flag = false;
                        end = i;
                        ans1 = expression.substring(start, end);
                        if (operator1 == 0) {
                            results.push(ans1.equals(ans2));
                        } else if (operator1 == 1) results.push(!ans1.equals(ans2));
                        else if (operator1 == 2)
                            results.push(Double.parseDouble(ans2.replaceAll(",", ".")) >= Double.parseDouble(ans1.replaceAll(",", ".")));
                        else if (operator1 == 3)
                            results.push(Double.parseDouble(ans2.replaceAll(",", ".")) > Double.parseDouble(ans1.replaceAll(",", ".")));
                        else if (operator1 == 4)
                            results.push(Double.parseDouble(ans2.replaceAll(",", ".")) <= Double.parseDouble(ans1.replaceAll(",", ".")));
                        else if (operator1 == 5)
                            results.push(Double.parseDouble(ans2.replaceAll(",", ".")) < Double.parseDouble(ans1.replaceAll(",", ".")));
                        start = 0;
                        end = 0;
                        ans1 = "";
                        ans2 = "";
                        temp = "";
                        tempbean = null;
                    } else {
                        start = i + 1;
                        flag = true;
                    }
                    break;
                case '&':
                    i++;
                    operators.push(true);
                    break;
                case '|':
                    i++;
                    operators.push(false);
                    break;
            }

        }
        flag = true;
        while (!results.isEmpty()) {
            if (flag) {
                finalResult = results.pop();
                if (results.isEmpty()) return finalResult;
                else {
                    tempResult1 = results.pop();
                    if (operators.pop()) finalResult = finalResult && tempResult1;
                    else finalResult = finalResult || tempResult1;
                    flag = false;
                }
            } else {
                tempResult1 = results.pop();
                if (operators.pop()) finalResult = finalResult && tempResult1;
                else finalResult = finalResult || tempResult1;
            }
        }
        return finalResult;
    }

    private boolean loadDynamicForm() {
        boolean isLastQuestion = true;

        int start = -1;
        int x = 0;
        if (listOfQuestion == null) {
            try {
                //RestoreDataToTemporary(getApplicationContext());
            } catch (Exception e) {
                FireCrash.log(e);
            }
        }
        int end = listOfQuestion.size();

        try {
            x = questionContainer.getChildCount();
            if (x != 0) {
                QuestionView questionView = (QuestionView) questionContainer.getChildAt(x - 1);
                if (questionView.isTitleOnly())
                    start = questionView.getSequence() + 1;
                else
                    start = questionView.getSequence();
            }
        } catch (Exception e) {
            FireCrash.log(e);
            if (Global.IS_DEV)
                Logger.i("INFO", e.toString());
        }

        start++;
        if (questionFields == null) {
            questionFields = new LinkedHashMap<>();
        }
//        if(questionGroupFieldsContent ==null){
//            questionGroupFieldsContent = new ArrayList<>();
//        }
        for (; start < end; start++) {
            QuestionBean bean = listOfQuestion.get(listOfIdentifier.get(start));
            questionSize = start + 1;
//            if(questionSize==1){
//                currentQuestionGroup = bean.getQuestion_group_id();
//            }
            String newQuestionGroup = bean.getQuestion_group_id();

            idxPosition++;
            if (bean.isVisible()) {

                String relevantExpression = bean.getRelevant_question();
                String relevantMandatory = bean.getRelevant_mandatory();
                if (relevantExpression == null) relevantExpression = "";
                if (relevantMandatory == null) relevantMandatory = "";
                if (isRelevantMandatory(relevantMandatory)) {
                    bean.setMandatory(true);
                    bean.setReadOnly(false);
                }
                if (isQuestVisibleIfRelevant(relevantExpression, bean)) {
                    bean.setVisible(true);
                    if (currentQuestionGroup == null || !newQuestionGroup.equals(currentQuestionGroup)) {
                        currentQuestionGroup = newQuestionGroup;
                        QuestionGroup group = new QuestionGroup(bean);
                        QuestionView view = this.getQuestionViewWithIcon(bean, start + 1, R.drawable.ic_camera, group);
                        view.setQuestionGroup(group);
                        ScaleAnimation anim = new ScaleAnimation(0, 1, 0, 1);
                        anim.setDuration(200);
                        anim.setFillAfter(true);
                        view.startAnimation(anim);
                        view.setOnClickListener(DynamicFormActivity.this);
                        questionContainer.addView(view, LayoutParams.MATCH_PARENT,
                                LayoutParams.WRAP_CONTENT);
                        questionGroupFieldsHeader.add(view);
                    }
                    actionOnQuestionGroupClicked(questionFields, newQuestionGroup, false, false);
//						QuestionView view = this.getQuestionView(bean, start + 1);

						/*for(int j = 0 ; j<questionSize;j++){
                            QuestionBean bean2 = listOfQuestion.get(j);
							if(bean.getQuestion_id().equals(bean2.getQuestion_id()) && bean.getIdentifier_name().equals(bean2.getIdentifier_name())){
								try {
									if(!bean.getQuestion_group_id().equals(bean2.getQuestion_group_id()))
										bean.setIs_readonly(Global.TRUE_STRING);
								} catch (Exception e) {
                    FireCrash.log(e);
									
								}
								if(Tool.isHaveLocation(bean2.getAnswer_type())){
									bean.setLocationInfo(bean2.getLocationInfo());
									bean.setLatitude(bean2.getLatitude());
									bean.setLongitude(bean2.getLongitude());
								}
								if(Tool.isOptions(bean2.getAnswer_type())){
									bean.setSelectedOptionAnswers(bean2.getSelectedOptionAnswers());
									bean.setOptionAnswers(bean2.getOptionAnswers());
									bean2.setRelevanted(true);
									break;
								}else if(Tool.isImage(bean2.getAnswer_type())){
									if(bean2.getImgAnswer()!=null)
										bean.setImgAnswer(bean2.getImgAnswer());
									bean.setAnswer(bean2.getAnswer());
									bean2.setRelevanted(true);
									break;
								}
								else{
									bean.setAnswer(bean2.getAnswer());
									bean2.setRelevanted(true);
									break;
								}
							}
						}*/

                    if (null != bean.getCalculate() && !"".equalsIgnoreCase(bean.getCalculate())) {
                        String resultCalculate = doCalculate(bean);
                        bean.setAnswer(resultCalculate);
                    }

                    QuestionView view = this.getQuestionViewWithIcon(bean, start + 1, R.drawable.ic_camera, null);
                    try {
                        view.setSequence(start);
                    } catch (NullPointerException e) {
                        FireCrash.log(e);

                        ObscuredSharedPreferences sharedPref = ObscuredSharedPreferences.getPrefs(this,
                                "TempDynamicData", Context.MODE_PRIVATE);
                        int tempStartPosition = sharedPref.getInt("StartPosition", 0);
                        start = tempStartPosition;
                        view.setSequence(start);
                    }

                    view.setFocusableInTouchMode(true);
                    view.requestFocus();

                    ScaleAnimation anim = new ScaleAnimation(0, 1, 0, 1);
                    anim.setDuration(200);
                    anim.setFillAfter(true);
                    view.startAnimation(anim);
                    String answerType = bean.getAnswer_type();

                    if (Tool.isOptions(answerType)) {
                        if (bean.getOptionAnswers() == null || bean.getOptionAnswers().isEmpty() || bean.getOptionRelevances().length > 0) {

                            MultiOptionQuestionViewAbstract multiOptionView = (MultiOptionQuestionViewAbstract) view;
                            loadOptionsToView(multiOptionView);
                        }
                    } else if (Global.AT_CALCULATION.equals(answerType)) {
                        setCalculationResult(bean);
                        ((LabelFieldView) view).updateValue();
                    }


                    questionContainer.addView(view, LayoutParams.MATCH_PARENT,
                            LayoutParams.WRAP_CONTENT);
                    view.getChildAt(1).requestFocus();
                    isLastQuestion = false;

                    List<QuestionView> tempList = questionFields.get(bean.getQuestion_group_id());
                    if (tempList != null) {
                        tempList.add(view);
                    } else {
                        tempList = new ArrayList<>();
                        tempList.add(view);
                    }
                    questionFields.put(bean.getQuestion_group_id(), tempList);

                    currentPageBeans.add(bean);
                    currentPageViews.add(view);
                    visibleQuestion.add(bean);
                    questionLabel.add(bean.getQuestion_label());
                    adapter.notifyDataSetChanged();
                    if (!bean.isRelevanted() && !hasLoading) {
                        if (!bean.isMandatory() && !isStop) {
                            loadQuestionView();
                        } else if (QuestionBean.isHaveAnswer(bean) && !isStop) {
                            if (!Tool.isOptions(bean.getAnswer_type()))
                                loadQuestionView();
                        }
                        isStop = false;
                    }
                    break;
                } else {
                    bean.setVisible(false);
//						isLastQuestion = false;
                    if (questionLabel.size() > start)
                        questionLabel.remove(questionLabel.size() - 1);
                    adapter.notifyDataSetChanged();
//						break;
                }
//					questionSize++;
//					visibleQuestion.addLast(bean);

//					adapter.notifyDataSetChanged();
//					break;
            } else {
                if (start == 0) {
                    doNext(false);
                } else {
                    String relevantExpression = bean.getRelevant_question();
                    if (relevantExpression == null) relevantExpression = "";
                    else if (isQuestVisibleIfRelevant(relevantExpression, bean)) {
                        QuestionSet tempQuestion = QuestionSetDataAccess.getOne(getBaseContext(), header.getUuid_scheme(), bean.getQuestion_id(), bean.getQuestion_group_id());
                        if (tempQuestion != null) {
                            if (tempQuestion.getIs_visible().equals(Global.TRUE_STRING)) {
                                bean.setVisible(true);

                                if (currentQuestionGroup == null || !newQuestionGroup.equals(currentQuestionGroup)) {
                                    currentQuestionGroup = newQuestionGroup;
                                    QuestionGroup group = new QuestionGroup(bean);
                                    QuestionView view = this.getQuestionViewWithIcon(bean, start + 1, R.drawable.ic_camera, group);
                                    view.setQuestionGroup(group);
                                    ScaleAnimation anim = new ScaleAnimation(0, 1, 0, 1);
                                    anim.setDuration(200);
                                    anim.setFillAfter(true);
                                    view.startAnimation(anim);
                                    view.setOnClickListener(DynamicFormActivity.this);
                                    questionContainer.addView(view, LayoutParams.MATCH_PARENT,
                                            LayoutParams.WRAP_CONTENT);
                                    questionGroupFieldsHeader.add(view);
                                }
                                actionOnQuestionGroupClicked(questionFields, newQuestionGroup, false, false);
                                    /*for(int j = 0 ; j<questionSize;j++){
                                        QuestionBean bean2 = listOfQuestion.get(j);
										if(bean.getQuestion_id().equals(bean2.getQuestion_id()) && bean.getIdentifier_name().equals(bean2.getIdentifier_name())){
											try {
												if(!bean.getQuestion_group_id().equals(bean2.getQuestion_group_id()))
													bean.setIs_readonly(Global.TRUE_STRING);
											} catch (Exception e) {
                    FireCrash.log(e);
												
											}
											if(Tool.isHaveLocation(bean2.getAnswer_type())){
												bean.setLocationInfo(bean2.getLocationInfo());
												bean.setLatitude(bean2.getLatitude());
												bean.setLongitude(bean2.getLongitude());
											}
											if(Tool.isOptions(bean2.getAnswer_type())){
												bean.setSelectedOptionAnswers(bean2.getSelectedOptionAnswers());
												bean.setOptionAnswers(bean2.getOptionAnswers());
												bean2.setRelevanted(true);
												break;
											}else if(Tool.isImage(bean2.getAnswer_type())){
												if(bean2.getImgAnswer()!=null)
													bean.setImgAnswer(bean2.getImgAnswer());
												bean.setAnswer(bean2.getAnswer());
												bean2.setRelevanted(true);
												break;
											}
											else{
												bean.setAnswer(bean2.getAnswer());
												bean2.setRelevanted(true);
												break;
											}
										}
									}*/
//									QuestionView view = this.getQuestionView(bean, start + 1);
                                QuestionView view = this.getQuestionViewWithIcon(bean, start + 1, R.drawable.ic_camera, null);
                                view.setSequence(start);

                                view.setFocusableInTouchMode(true);
                                view.requestFocus();

                                ScaleAnimation anim = new ScaleAnimation(0, 1, 0, 1);
                                anim.setDuration(200);
                                anim.setFillAfter(true);
                                view.startAnimation(anim);
                                String answerType = bean.getAnswer_type();

                                if (Tool.isOptions(answerType)) {
                                    if (bean.getOptionAnswers() == null || bean.getOptionAnswers().isEmpty() || bean.getOptionRelevances().length > 0) {

                                        MultiOptionQuestionViewAbstract multiOptionView = (MultiOptionQuestionViewAbstract) view;
                                        loadOptionsToView(multiOptionView);

                                    }
                                } else if (Global.AT_CALCULATION.equals(answerType)) {
                                    setCalculationResult(bean);
                                    ((LabelFieldView) view).updateValue();
                                }

                                questionContainer.addView(view, LayoutParams.MATCH_PARENT,
                                        LayoutParams.WRAP_CONTENT);
                                view.getChildAt(1).requestFocus();
                                isLastQuestion = false;

                                List<QuestionView> tempList = questionFields.get(bean.getQuestion_group_id());
                                if (tempList != null) {
                                    tempList.add(view);
                                } else {
                                    tempList = new ArrayList<>();
                                    tempList.add(view);
                                }
                                questionFields.put(bean.getQuestion_group_id(), tempList);


                                currentPageBeans.add(bean);
                                currentPageViews.add(view);
                                visibleQuestion.add(bean);
                                questionLabel.add(bean.getQuestion_label());
                                adapter.notifyDataSetChanged();
                                if (!bean.isRelevanted() && !hasLoading) {
                                    if (!bean.isMandatory() && !isStop) {
                                        loadQuestionView();
                                    } else if (QuestionBean.isHaveAnswer(bean) && !isStop) {
                                        if (!Tool.isOptions(bean.getAnswer_type()))
                                            loadQuestionView();
                                    }
                                    isStop = false;
                                }
                                break;
                            } else {
                                bean.setVisible(false);
                            }
                        }
    /*						bean.setVisible(true);
//							QuestionView view = this.getQuestionView(bean, start + 1);
							QuestionView view = this.getQuestionViewWithIcon(bean, start+1, R.drawable.ic_camera);
							view.setSequence(start);

							view.setFocusableInTouchMode(true);
							view.requestFocus();

							ScaleAnimation anim = new ScaleAnimation(0,1,0,1);
					        anim.setDuration(200);
					        anim.setFillAfter(true);
							view.startAnimation(anim);
							String answerType = bean.getAnswer_type();

							if(Tool.isOptions(answerType)){
								if (bean.getOptionAnswers() == null || bean.getOptionAnswers().size() == 0 || bean.getOptionRelevances().length > 0){

									MultiOptionQuestionViewAbstract multiOptionView = (MultiOptionQuestionViewAbstract) view;
									loadOptionsToView(multiOptionView);

								}
							}

							else if (Global.AT_CALCULATION.equals(answerType)){
								setCalculationResult(bean);
								((LabelFieldView) view).updateValue();
							}






							questionContainer.addView(view, LayoutParams.MATCH_PARENT,
									LayoutParams.WRAP_CONTENT);
							view.getChildAt(1).requestFocus();
							isLastQuestion = false;

							currentPageBeans.add(bean);
							currentPageViews.add(view);
							visibleQuestion.add(bean);
							questionLabel.add(bean.getQuestion_label());
							adapter.notifyDataSetChanged();
							break;*/


                    }
                }


//					break;
            }
            if (end == start) {
                //last question
                isLastQuestion = true;
            }
            System.gc();
        }
        return isLastQuestion;
    }

    private void loadQuestionView() {
        final Handler handler = new Handler();

        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                loadDynamicForm();
            }
        }, 400);
    }

    private void deleteQuestionGroupView() {
        final int pos = questionContainer.getChildCount() - 1;
        final QuestionView view = (QuestionView) questionContainer.getChildAt(pos);
        ScaleAnimation anim = new ScaleAnimation(1, 0, 1, 0);
        anim.setDuration(200);
        anim.setFillAfter(true);
        view.startAnimation(anim);
        if (view.getQuestionGroup() != null) {
            questionContainer.removeViewAt(pos);
            questionGroupFieldsHeader.remove(view);
            questionFields.remove(view.getQuestionGroup().getQuestion_group_id());
        }
    }

    private boolean loadBackDynamicForm() {
        boolean isCurrentPage = true;

        final int pos = questionContainer.getChildCount() - 1;
        final QuestionView minView = (QuestionView) questionContainer.getChildAt(pos - 1);
        final QuestionView view = (QuestionView) questionContainer.getChildAt(pos);
        if (view.isTitleOnly()) {
            deleteQuestionGroupView();
            return false;
        }
        ScaleAnimation anim = new ScaleAnimation(1, 0, 1, 0);
        anim.setDuration(200);
        anim.setFillAfter(true);
        view.startAnimation(anim);
//			Handler handler = new Handler();
//			handler.postDelayed(new Runnable() {
//				public void run() {
        questionContainer.removeViewAt(pos);
        if (!visibleQuestion.isEmpty()) {
            visibleQuestion.remove(visibleQuestion.size() - 1);
        }
        if (!questionLabel.isEmpty()) {
            questionLabel.remove(questionLabel.size() - 1);
        }
        adapter.notifyDataSetChanged();
        questionSize--;
        if (minView.isTitleOnly()) {
            deleteQuestionGroupView();
            return true;
        }

        minView.setFocusableInTouchMode(true);
        minView.requestFocus();
        QuestionView questionView = (QuestionView) questionContainer.getChildAt(questionContainer.getChildCount() - 1);
        questionView.getChildAt(1).requestFocus();
//			    }
//			}, 205);
        return isCurrentPage;
    }

    // Glen 28 Aug 2014, method to get qbean of certain questionId
        /*protected QuestionBean getQuestionBeanForId(String idName) {
            for (QuestionBean bean : listOfQuestion) {
				if (bean.getIdentifier_name().equals(idName)) {
					return bean;
				}
			}
			return null;
		}*/

    private String doCalculate(QuestionBean bean) {
        String formula = bean.getCalculate();
        String expression = formula;
        String total = "0";

        List<QuestionBean> questionList = new ArrayList<QuestionBean>();
        String resultformula2 = formula.substring(0, formula.indexOf("for"));
        resultformula2 = resultformula2.replace("_var = 0", "");
        resultformula2 = resultformula2.replace("var ", "");
        resultformula2 = resultformula2.replace(" ", "");
        String[] nFormula2 = resultformula2.split(";");
        for (String idf : nFormula2) {
            questionList.add(listOfQuestion.get(idf.toUpperCase()));
        }


        JexlContext context = new MapContext();
        //Person somePerson = new Person("488572222", "Sam", new Address("jspx st", "SF", 32), 2);

        context.set("listOfQuestion", questionList);
        context.set("qBean", new QuestionBean(bean));
        context.set("bean", bean);
//			context.set("isOption", Tool.isOptions("qBean.answer_type"));
//			context.set("isOption", "com.adins.mss.foundation.formatter.Tool.isOptions(qBean.answer_type)");
        context.set("result", total);

//			if(expression.contains("ORD_TENOR_var"))
//			expression = "var ORD_TENOR_var = 0; var ORD_PAY_FREQ_var = 0; for ( qBean : listOfQuestion ) { 	if('ORD_TENOR'.equalsIgnoreCase(qBean.identifier_name)) {  		if(qBean.answer_type.equalsIgnoreCase('009') || qBean.answer_type.equalsIgnoreCase('010') || qBean.answer_type.equalsIgnoreCase('011') || qBean.answer_type.equalsIgnoreCase('012')){	ORD_TENOR_var = qBean.selectedOptionAnswers[0].code; 		}else{	ORD_TENOR_var = qBean.answer;  		} 	}  	if('ORD_PAY_FREQ'.equalsIgnoreCase(qBean.identifier_name)) {	if(qBean.answer_type.equalsIgnoreCase('009') || qBean.answer_type.equalsIgnoreCase('010') || qBean.answer_type.equalsIgnoreCase('011') || qBean.answer_type.equalsIgnoreCase('012')){	ORD_PAY_FREQ_var = qBean.selectedOptionAnswers[0].code; 				}else{ 			ORD_PAY_FREQ_var = qBean.answer;  		} 	}  }   /*start*/ result= /*$*/ ORD_TENOR_var /*$*/ / /*$*/ ORD_PAY_FREQ_var /*$*/  /*end*/ ";
//			else
//			expression = "var ORD_OTR_var = 0; for ( qBean : listOfQuestion ) {  if('ORD_OTR'.equalsIgnoreCase(qBean.identifier_name)) {  if(qBean.answer_type.equalsIgnoreCase('009') || qBean.answer_type.equalsIgnoreCase('010') || qBean.answer_type.equalsIgnoreCase('011') || qBean.answer_type.equalsIgnoreCase('012')) { ORD_OTR_var = qBean.selectedOptionAnswers[0].code; } else { ORD_OTR_var = qBean.answer; } } }  /*start*/ result= /*$*/ ORD_OTR_var /*$*/ -0.3 /*end*/ ";

        Object value = jexlEngine.createScript(expression).execute(context);

        if (value != null) {
            try {
                return String.format(Locale.US, "%.2f", value);
            } catch (Exception e) {
                FireCrash.log(e);
                return value.toString();
            }
        } else {
            return "0";
        }


    }

    public boolean isContaininUI(String qId) {
        boolean result = false;

        for (int i = 0; i < questionContainer.getChildCount(); i++) {

            QuestionView questionView = (QuestionView) questionContainer.getChildAt(i);

            if (qId.equalsIgnoreCase(questionView.getQuestionId())) {
                result = true;
                break;
            }
        }
        return result;
    }

    // Glen 17 Oct 2014
    protected QuestionBean getQuestionBeanForIdentifier(String identifier) {
        QuestionBean bean = listOfQuestion.get(identifier);
        return bean;
    }

    //Glen 15 Oct 2014, method to replace string with variable from application
    protected String getReplacementForKey(String key) {
        String stringReplacement = "";
        if (QuestionBean.PLACEMENT_KEY_BRANCH.equals(key)) {
//				filter = ;
        } else if (QuestionBean.PLACEMENT_KEY_USER.equals(key)) {
//				filter = ;
        }
        return stringReplacement;
    }

    protected String replaceModifiers(String sourceString) {
        String newString = sourceString;
        //replace branch modifier
        String branch = GlobalData.getSharedGlobalData().getUser().getBranch_id();
        newString = newString.replace(QuestionBean.PLACEMENT_KEY_BRANCH, branch);
        //replace user modifier
        String user = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        return newString.replace(QuestionBean.PLACEMENT_KEY_USER, user);
    }

    /**
     * Get Options from Database and check filter from choice filters.
     * ex : {identifier1},{identifier2}
     * ex : {$LOGIN_ID} : for get login Id from active User
     * ex : {$UUID_USER} : for get uuid user from active User
     * ex : {$BRANCH_ID} : for get branch Id from active User
     * ex : {$BRANCH_NAME} : for get branch name from active User
     * ex : {$FLAG_JOB} : for get flag job from active User
     * ex : {$DEALER_NAME} : for get dealer name from active User
     *
     * @param bean : Question Bean
     * @return
     */
    protected List<OptionAnswerBean> getOptionsForQuestion(QuestionBean bean) {

        //Gigin, validasi for Choice Filter
        List<String> filters = new ArrayList<String>();
        int constraintAmount = 0;
        if (bean.getChoice_filter() != null) {
            String[] tempfilters = Tool.split(bean.getChoice_filter(), Global.DELIMETER_DATA3);

            for (String newFilter : tempfilters) {
                int idxOfOpenBrace = newFilter.indexOf('{');
                if (idxOfOpenBrace != -1) {                    //there's {, prepare to replace what inside the {}
                    int idxOfCloseBrace = newFilter.indexOf('}');
                    String tempIdentifier = newFilter.substring(idxOfOpenBrace + 1, idxOfCloseBrace).toUpperCase();
                    if (tempIdentifier.contains("%")) {
                        filters.add(tempIdentifier);
                    } else {
                        int idxOfOpenAbs = tempIdentifier.indexOf("$");
                        if (idxOfOpenAbs != -1) {
                            String finalIdentifier = tempIdentifier.substring(idxOfOpenAbs + 1);
                            if (finalIdentifier.equals(Global.IDF_LOGIN_ID)) {
                                String loginId = GlobalData.getSharedGlobalData().getUser().getLogin_id();
                                int idxOfOpenAt = loginId.indexOf('@');
                                if (idxOfOpenAt != -1) {
                                    loginId = loginId.substring(0, idxOfOpenAt);
                                }
                                filters.add(loginId);
                            } else if (finalIdentifier.equals(Global.IDF_BRANCH_ID)) {
                                String branchId = GlobalData.getSharedGlobalData().getUser().getBranch_id();
                                filters.add(branchId);
                            } else if (finalIdentifier.equals(Global.IDF_BRANCH_NAME)) {
                                String branchName = GlobalData.getSharedGlobalData().getUser().getBranch_name();
                                filters.add(branchName);
                            } else if (finalIdentifier.equals(Global.IDF_UUID_USER)) {
                                String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                                filters.add(uuidUser);
                            } else if (finalIdentifier.equals(Global.IDF_JOB)) {
                                String job = GlobalData.getSharedGlobalData().getUser().getFlag_job();
                                filters.add(job);
                            } else if (finalIdentifier.equals(Global.IDF_DEALER_NAME)) {
                                String dealerName = GlobalData.getSharedGlobalData().getUser().getDealer_name();
                                filters.add(dealerName);
                            } else if (finalIdentifier.equals(Global.IDF_BRANCH_TYPE)) {
                                String branchType = GlobalData.getSharedGlobalData().getUser().getBranch_type();
                                filters.add(branchType);
                            } else if (finalIdentifier.equals(Global.IDF_TASK_ID)) {
                                String taskId = DynamicFormActivity.header.getTask_id();
                                filters.add(taskId);
                            }
                            constraintAmount++;
                        } else {
                            QuestionBean bean2 = listOfQuestion.get(tempIdentifier);
                            if (bean2 != null) {
                                if (Global.AT_TEXT_WITH_SUGGESTION.equals(bean2.getAnswer_type())) {
                                    filters.add(bean2.getAnswer());
                                } else {
                                    for (OptionAnswerBean answerBean : bean2.getSelectedOptionAnswers()) {
                                        filters.add(answerBean.getCode());
                                    }
                                }
                                bean2.setRelevanted(true);
                                constraintAmount++;
                            }
                        }
                    }
                }
            }
        }
        List<OptionAnswerBean> optionAnswers = new ArrayList<OptionAnswerBean>();
        optionAnswers = GetLookupFromDB(bean, filters);

        if (optionAnswers == null || optionAnswers.isEmpty()) {
            try {
                isStop = true;
                //new GetLookupOnDemand(bean.getLov_group()).execute();
                new GetLookupOnDemand(bean.getLov_group(), filters, constraintAmount).execute();

            } catch (Exception e) {
                FireCrash.log(e);
                if (Global.IS_DEV)
                    e.printStackTrace();
            }
            optionAnswers = GetLookupFromDB(bean, filters);
        }

        return optionAnswers;
    }

    protected List<OptionAnswerBean> getOptionsForQuestion2(QuestionBean bean) {

        //Gigin, validasi for Choice Filter
        List<String> filters = new ArrayList<String>();
        if (bean.getChoice_filter() != null) {
            String[] tempfilters = Tool.split(bean.getChoice_filter(), Global.DELIMETER_DATA3);

            for (String newFilter : tempfilters) {
                int idxOfOpenBrace = newFilter.indexOf('{');
                if (idxOfOpenBrace != -1) {                    //there's {, prepare to replace what inside the {}
                    int idxOfCloseBrace = newFilter.indexOf('}');
                    String tempIdentifier = newFilter.substring(idxOfOpenBrace + 1, idxOfCloseBrace).toUpperCase();
                    if (tempIdentifier.contains("%")) {
                        filters.add(tempIdentifier);
                    } else {
                        int idxOfOpenAbs = tempIdentifier.indexOf("$");
                        if (idxOfOpenAbs != -1) {
                            String finalIdentifier = tempIdentifier.substring(idxOfOpenAbs + 1);
                            if (finalIdentifier.equals(Global.IDF_LOGIN_ID)) {
                                String loginId = GlobalData.getSharedGlobalData().getUser().getLogin_id();
                                int idxOfOpenAt = loginId.indexOf('@');
                                if (idxOfOpenAt != -1) {
                                    loginId = loginId.substring(0, idxOfOpenAt);
                                }
                                filters.add(loginId);
                            } else if (finalIdentifier.equals(Global.IDF_BRANCH_ID)) {
                                String branchId = GlobalData.getSharedGlobalData().getUser().getBranch_id();
                                filters.add(branchId);
                            } else if (finalIdentifier.equals(Global.IDF_BRANCH_NAME)) {
                                String branchName = GlobalData.getSharedGlobalData().getUser().getBranch_name();
                                filters.add(branchName);
                            } else if (finalIdentifier.equals(Global.IDF_UUID_USER)) {
                                String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                                filters.add(uuidUser);
                            } else if (finalIdentifier.equals(Global.IDF_JOB)) {
                                String job = GlobalData.getSharedGlobalData().getUser().getFlag_job();
                                filters.add(job);
                            } else if (finalIdentifier.equals(Global.IDF_DEALER_NAME)) {
                                String dealerName = GlobalData.getSharedGlobalData().getUser().getDealer_name();
                                filters.add(dealerName);
                            } else if (finalIdentifier.equals(Global.IDF_BRANCH_TYPE)) {
                                String branchType = GlobalData.getSharedGlobalData().getUser().getBranch_type();
                                filters.add(branchType);
                            } else if (finalIdentifier.equals(Global.IDF_TASK_ID)) {
                                String taskId = DynamicFormActivity.header.getTask_id();
                                filters.add(taskId);
                            }
                        } else {
                            QuestionBean bean2 = listOfQuestion.get(tempIdentifier);
                            if (bean2 != null) {
                                if (Global.AT_TEXT_WITH_SUGGESTION.equals(bean2.getAnswer_type())) {
                                    filters.add(bean2.getAnswer());
                                } else {
                                    for (OptionAnswerBean answerBean : bean2.getSelectedOptionAnswers()) {
                                        filters.add(answerBean.getCode());
                                    }
                                }
                                bean2.setRelevanted(true);
                            }
                        }
                    }
                }
            }
        }
        List<OptionAnswerBean> optionAnswers = new ArrayList<OptionAnswerBean>();
        optionAnswers = GetLookupFromDB(bean, filters);
        return optionAnswers;
    }

    private List<OptionAnswerBean> GetLookupFromDB(QuestionBean bean, List<String> filters) {
        List<OptionAnswerBean> optionAnswers = new ArrayList<OptionAnswerBean>();
        if (!filters.isEmpty()) {
            if (filters.size() == 1) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilter(getBaseContext(), bean.getLov_group(), filters.get(0));
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            } else if (filters.size() == 2) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilter(getBaseContext(), bean.getLov_group(), filters.get(0), filters.get(1));
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            } else if (filters.size() == 3) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilter(getBaseContext(), bean.getLov_group(), filters.get(0), filters.get(1), filters.get(2));
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            } else if (filters.size() == 4) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilter(getBaseContext(), bean.getLov_group(), filters.get(0), filters.get(1), filters.get(2), filters.get(3));
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            } else if (filters.size() == 5) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilter(getBaseContext(), bean.getLov_group(), filters.get(0), filters.get(1), filters.get(2), filters.get(3), filters.get(4));
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            }

        } else {
            if (bean.getChoice_filter() != null && bean.getChoice_filter().length() > 0) {
                List<Lookup> lookups = new ArrayList<Lookup>();
                optionAnswers = OptionAnswerBean.getOptionList(lookups);
            } else {
                List<Lookup> lookups = LookupDataAccess.getAllByLovGroup(getBaseContext(), bean.getLov_group());
                if (lookups != null)
                    optionAnswers = OptionAnswerBean.getOptionList(lookups);
            }
        }
        return optionAnswers;
    }

    public List<String> extractIdentifierFromString(String rawString) {
        List<String> extractedIdentifiers = new ArrayList<String>();
        boolean needExtract = true;

        while (needExtract) {

            int idxOfOpenBrace = rawString.indexOf('{');
            if (idxOfOpenBrace != -1) {                    //there's {, prepare to extract what is inside the {}
                int idxOfCloseBrace = rawString.indexOf('}');
                String identifier = rawString.substring(idxOfOpenBrace + 1, idxOfCloseBrace);
                if (identifier != null) extractedIdentifiers.add(identifier);
                rawString = rawString.substring(idxOfCloseBrace + 1);            //cut extracted part
            }
            //no more extracting needed
            else {
                needExtract = false;
            }

        }
        return extractedIdentifiers;
    }

    /**
     * Check Relevant from Question Set.
     * ex : {identifier}==value : for get value from Question Set
     * ex : {$LOGIN_ID}==value : for get value from Login ID in active User
     * ex : {$UUID_USER}==value : for get value from uuid user in active User
     * ex : {$BRANCH_ID}==value : for get value from Branch ID in active User
     * ex : {$BRANCH_NAME}==value : for get value from Branch Name in active User
     * ex : {$FLAG_JOB}==value : for get value from Branch Name in active User
     * ex : {$DEALER_NAME}==value : for get value from Dealer Name in active User
     *
     * @param relevantExpression - Relevant expression from Question Set
     * @param question           - Question Bean
     * @return boolean - True if Relevant and False if not Relevant
     */
    public boolean isQuestVisibleIfRelevant(String relevantExpression, QuestionBean question) {
        boolean result = false;
        String convertedExpression = new String(relevantExpression);        //make a copy of
        if (convertedExpression == null || convertedExpression.length() == 0) {
            return true;
        } else {

            //TODO, use extractIdentifierFromString next time to simplify
            boolean needReplacing = true;
            while (needReplacing) {

                //replace application modifier
                convertedExpression = replaceModifiers(convertedExpression);

                int idxOfOpenBrace = convertedExpression.indexOf('{');
                if (idxOfOpenBrace != -1) {                    //there's {, prepare to replace what inside the {}
                    int idxOfCloseBrace = convertedExpression.indexOf('}');
                    String identifier = convertedExpression.substring(idxOfOpenBrace + 1, idxOfCloseBrace);
                    int idxOfOpenAbs = identifier.indexOf("$");
                    if (idxOfOpenAbs != -1) {
                        String finalIdentifier = identifier.substring(idxOfOpenAbs + 1);
                        String flatAnswer = "";

                        if (finalIdentifier.equals(Global.IDF_LOGIN_ID)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getLogin_id();
                            int idxOfOpenAt = flatAnswer.indexOf('@');
                            if (idxOfOpenAt != -1) {
                                flatAnswer = flatAnswer.substring(0, idxOfOpenAt);
                            }
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_ID)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_id();
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_NAME)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_name();
                        } else if (finalIdentifier.equals(Global.IDF_UUID_USER)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                        } else if (finalIdentifier.equals(Global.IDF_JOB)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getFlag_job();
                        } else if (finalIdentifier.equals(Global.IDF_DEALER_NAME)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getDealer_name();
                        } else if (finalIdentifier.equals(Global.IDF_BRANCH_TYPE)) {
                            flatAnswer = GlobalData.getSharedGlobalData().getUser().getBranch_type();
                        } else if (finalIdentifier.equals(Global.IDF_TASK_ID)) {
                            String taskId = DynamicFormActivity.header.getTask_id();
                            flatAnswer = taskId;
                        }


                        if (flatAnswer != null && flatAnswer.length() > 0) {
                            convertedExpression = convertedExpression.replace("{" + identifier + "}", flatAnswer);
                        } else {            //if there's no answer, just hide the question
                            return false;
                        }

                    } else {
                        QuestionBean bean = listOfQuestion.get(identifier);

                        if (bean != null) {

                            //Glen 21 Oct 2014, if it relate to question which is not visible, make it not visible too
                            if (bean.getIs_visible().equals(Global.FALSE_STRING)) return false;

                            String flatAnswer = QuestionBean.getAnswer(bean);

//                            if (Tool.isOptions(bean.getAnswer_type())) {
//                                try {
//                                    //flatAnswer = bean.getSelectedOptionAnswers().get(0).getCode();
//                                    flatAnswer = QuestionBean.getAnswerFromOptions(bean);
//                                } catch (Exception e) {
//                                }
//                            }

                            if (flatAnswer != null && flatAnswer.length() > 0) {
                                //Glen 22 Oct 2014, enable multi-depth checking for 'multiple' question
                                //NOTE: though it's possible to just iterate on flatAnswer substrings, we prefer to stay on method if size is 1
                                String answers[] = Tool.split(flatAnswer, Global.DELIMETER_DATA);
                                if (answers.length == 1) {
//									convertedExpression = convertedExpression.replace("{"+identifier+"}", flatAnswer);
                                    convertedExpression = convertedExpression.replace("{" + identifier + "}", answers[0]);
                                } else {
                                    //NOTE: going into in-depth loop, won't go outside of this 'else'
                                    for (int i = 0; i < answers.length; i++) {
                                        String convertedSubExpression = convertedExpression.replace("{" + identifier + "}", answers[i]);
                                        boolean isVisible = isQuestVisibleIfRelevant(convertedSubExpression, question);
                                        if (isVisible) {
                                            return true;
                                        }
                                    }
                                    return false;
                                }

                                //Glen 16 Oct 2014, added as affected bean visibility
//								bean.addToAffectedQuestionBeans(question);
                                bean.addToAffectedQuestionBeanVisibility(question);
                            } else {            //if there's no answer, just hide the question
                                return false;
                            }
                        } else {
//							convertedExpression.replaceAll("{"+identifier+"}", "");
                            convertedExpression = convertedExpression.replace("{" + identifier + "}", "\"\"");
                        }
                    }
                }
                //moved up
//					else if (convertedExpression.indexOf('r') != -1){
//						convertedExpression = replaceModifiers(convertedExpression);
//					}

                //no more replacing needed
                else {
                    needReplacing = false;
                }

            }
            try {
                OperatorSet opSet = OperatorSet.getStandardOperatorSet();
                opSet.addOperator("!=", NotEqualSymbol.class);
                Expression exp = new Expression(convertedExpression);
                exp.setOperatorSet(opSet);
                result = exp.evaluate().toBoolean();
                return result;
            } catch (Exception e) {
                FireCrash.log(e);
                if (Global.IS_DEV)
                    e.printStackTrace();
                return false;
            }

        }
//			return result;
    }

    //Glen 17 Oct 2014
    private double getCalculationResult(QuestionBean bean) {
        String expression = bean.getCalculate();
        if (expression == null || expression.length() == 0) return 0;

        String convertedExpression = new String(expression);

        List<String> identifiers = extractIdentifierFromString(expression);
        for (String identifier : identifiers) {
            QuestionBean lookupBean = getQuestionBeanForIdentifier(identifier);
            boolean haveAnswer = QuestionBean.isHaveAnswer(lookupBean);

            if (!haveAnswer) return 0;

            convertedExpression = convertedExpression.replace("{" + identifier + "}", String.valueOf(haveAnswer));

//				bean.addToAffectedQuestionBeanCalculation(lookupBean);
            lookupBean.addToAffectedQuestionBeanCalculation(bean);
        }

        try {
            double result = Expression.evaluate(convertedExpression).toDouble();
            return result;
        } catch (Exception e) {
            FireCrash.log(e);
            if (Global.IS_DEV)
                e.printStackTrace();
            return 0;
        }

    }

    //Glen 10 Oct 2014, new question relevant with jexel
    //Glen 17 Oct 2014, use isQuestVisibleIfRelevant
//		public boolean isQuestVisible2(List<String> questRelevant) {
//			boolean result =  false;
//			if(questRelevant!=null){
//			for (String stringRelevant : questRelevant) {				
////						String relevantQuestion
//					int idxOfOpenBrace = stringRelevant.indexOf('{');
//					if (idxOfOpenBrace != -1){
//						int idxOfCloseBrace = stringRelevant.indexOf('}');
//						String identifier = stringRelevant.substring(idxOfOpenBrace+1, idxOfCloseBrace-1);
//						
//						QuestionBean bean = Tool.getBeanWithIdentifier(identifier, listOfQuestion);
//						String flatAnswer = bean.getAnswer();
//						
////						List<String> answers = new ArrayList<String>();
//						
////						if (flatAnswer.indexOf(Global.DELIMETER_DATA) > -1){	//artinya pertanyaan yg jawabanya multiple
////							
////						}else{
////							answers.add(flatAnswer);
////						}
//						
////						for (String answer : answers){
////							String translatedExpression = stringRelevant.replaceFirst("{(\\S+)}", answer);
////							try {
////								result = Expression.evaluate(translatedExpression).toBoolean();
////							} catch (ArgumentCastException e) {
////								e.printStackTrace();
////							} catch (InvalidExpressionException e) {
////								e.printStackTrace();
////							}
////						}	
//					} else{
//						//TODO add logic for predefined 
//					}
//				if(result){
//					break;
//				}
//			}		
//			}else{
//				// berarti gak ada dependensi nya,jadi langsung ditampilkan
//				return true;
//			}
//			return result;
//		}

    private double setCalculationResult(QuestionBean bean) {
        double result = getCalculationResult(bean);
        bean.setAnswer(String.valueOf(result));
        return result;
    }

    private void clearListView() {
        int childCount = questionContainer.getChildCount();
        if (childCount > 0)
            questionContainer.removeViews(0, childCount);

        //Glen 20 Oct 2014
        currentPageBeans.clear();
        currentPageViews.clear();

    }

    private QuestionView getQuestionView(QuestionBean qBean, int number) {
        QuestionView linear = null;
        try {

            //TODO bangkit seharusnya ada handling nya lagi
            linear = viewGenerator.generateByAnswerType(this, qBean, number, 0, null, null, null);
        } catch (Exception ex) {
            FireCrash.log(ex);
            if (Global.IS_DEV)
                ex.printStackTrace();
        }
        return linear;
    }

    private QuestionView getQuestionViewWithIcon(QuestionBean qBean, int number, int resIdIcon, QuestionGroup group) {
        QuestionView linear = null;
        try {
            //TODO bangkit seharusnya ada handling nya lagi
            linear = viewGenerator.generateByAnswerType(this, qBean, number, resIdIcon, ViewImageActivity.class, Camera.class, group);
        } catch (Exception ex) {
            FireCrash.log(ex);
            if (Global.IS_DEV)
                ex.printStackTrace();
        }
        return linear;
    }

    @Override
    protected void onResume() {

        super.onResume();
        if (Global.isVerifiedByUser) {
            Global.isVerifiedByUser = false;
            this.finish();
        } else {
            try {
//				RestoreDataToTemporary(getApplicationContext());
                //TODO BANGKIT	DialogManager.showGPSAlert(this);
            } catch (Exception e) {
                FireCrash.log(e);
                if (listOfQuestion == null)
                    listOfQuestion = new LinkedHashMap<String, QuestionBean>();
            }
        }
        DialogManager.showGPSAlert(this);
        DialogManager.showTimeProviderAlert(this);
    }

    public void onClick(View v) {
        int id = v.getId();
        try {
            if (previewFields == null || !previewFields.contains(v)) {
                removeNextCallback();
                removeReviewCallback();
            }
        } catch (Exception e) {
            FireCrash.log(e);

        }
        if (id == R.id.btnBack) {
            this.doBack();
        } else if (id == R.id.btnNext) {
            //Glen 15 Oct 2014, added new param
//				this.doNext();
            if (isFinish && isVerified) {
                if (Global.NEW_FEATURE) {
                    if (Tool.isInternetconnected(getBaseContext())) {
                        if (needQuickValidation) {
                            if (validateAllMandatory(true)) {
                                //TODO : alihkan ke halaman verification Action
                                Intent intent = new Intent(getBaseContext(), Global.VerificationActivityClass);
                                intent.putExtra(Global.BUND_KEY_UUID_TASKH, header.getUuid_task_h());
                                intent.putExtra(Global.BUND_KEY_MODE_SURVEY, mode);
                                if (isApproval)
                                    intent.putExtra(Global.BUND_KEY_FORM_NAME, Global.APPROVAL_FLAG);
                                else
                                    intent.putExtra(Global.BUND_KEY_FORM_NAME, Global.VERIFICATION_FLAG);
                                startActivity(intent);
                            }
                        } else {
                            //TODO : alihkan ke halaman verification Action
                            Intent intent = new Intent(getBaseContext(), Global.VerificationActivityClass);
                            intent.putExtra(Global.BUND_KEY_UUID_TASKH, header.getUuid_task_h());
                            intent.putExtra(Global.BUND_KEY_MODE_SURVEY, mode);
                            if (isApproval)
                                intent.putExtra(Global.BUND_KEY_FORM_NAME, Global.APPROVAL_FLAG);
                            else
                                intent.putExtra(Global.BUND_KEY_FORM_NAME, Global.VERIFICATION_FLAG);
                            startActivity(intent);
                        }
                    } else {
                        Toast.makeText(DynamicFormActivity.this, getString(R.string.connection_failed), Toast.LENGTH_SHORT).show();
                    }
                }
            } else {
                new AsyncTask<Void, Void, Void>() {
                    @Override
                    protected void onPreExecute() {
                        super.onPreExecute();
                        btnNext.setEnabled(false);
                        btnNext.setImageResource(R.drawable.ic_next_off);
                    }

                    @Override
                    protected Void doInBackground(Void... params) {
                        Handler handler = new Handler(Looper.getMainLooper());
                        handler.post(new Runnable() {
                            public void run() {
                                // UI code goes here
                                try {
                                    doNext(true);
                                } catch (Exception e) {
                                    FireCrash.log(e);
                                    Logger.e("DynamicFormActivity", e);
                                }
                            }
                        });
                        return null;
                    }

                    @Override
                    protected void onPostExecute(Void aVoid) {
                        btnNext.setEnabled(true);
                        btnNext.setImageResource(R.drawable.ic_next);
                        if (isFinish) {
                            if (Global.NEW_FEATURE) {
                                if (Global.FEATURE_REJECT_WITH_RESURVEY) {
                                    if (!isVerified) {
                                        btnNext.setClickable(false);
                                        btnNext.setImageResource(R.drawable.ic_next_off);
                                    }
                                }
                            } else {
                                btnNext.setClickable(false);
                                btnNext.setImageResource(R.drawable.ic_next_off);
                            }
                        }
                    }
                }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR);
            }
        } else if (id == R.id.btnSend) {
            doSend();

            //TODO: Gigin : ini hanya sample (not for use)
//				Bundle extras = new Bundle();
//				Intent intent = new Intent(getApplicationContext(), SendResultActivity.class);
//
//				extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, false);
//				extras.putString(Global.BUND_KEY_SEND_RESULT, "Data berhasil dikirim");
//				extras.putString(Global.BUND_KEY_SEND_SIZE, "128");
//				extras.putString(Global.BUND_KEY_SEND_TIME, "5");
//				extras.putString(Global.BUND_KEY_TASK_ID, "t002");
//				extras.putBoolean(Global.BUND_KEY_TASK_IS_PRINTABLE, false);
//				intent.putExtras(extras);
//				startActivity(intent);
//				this.finish();

            // bong Oct 14th, 2014 - deleting tasklistview from local when sending

//				String taskId = svyMgr.saveSurvey(getApplicationContext(), mode, header,
//       					listOfQuestion);
//				String taskId = header.getUuid_task_h();
//				if (Global.IS_DEV) System.out.println("taskId = "+taskId);
//				TaskListViewOpenHelper taskListViewOpenHelper = new TaskListViewOpenHelper(getApplicationContext());
//				taskListViewOpenHelper.delete(ApplicationBean.getInstance().getUserId(), taskId);
//
//				TaskHDataAccess.delete(this, header);
        } else if (id == R.id.btnSearchBar) {
//				adapter = new ArrayAdapter<String>(this,  android.R.layout.simple_dropdown_item_1line, questionLabel);
//				txtSearch.setAdapter(adapter);
            if (btnSearchBar.isChecked()) {
//					ScaleAnimation anim = new ScaleAnimation(0,1,0,1);
//			        anim.setDuration(500);
//			        anim.setFillAfter(true);
                CustomAnimatorLayout animatorLayout = new CustomAnimatorLayout(0, 1, 0, 1, 500, searchContainer, false);
                searchContainer.setVisibility(View.VISIBLE);
                searchContainer.startAnimation(animatorLayout);
            } else {
//			        ScaleAnimation anim = new ScaleAnimation(1,0,1,0);
//			        anim.setDuration(500);
//			        anim.setFillAfter(true);
//					searchContainer.setVisibility(View.GONE);
                CustomAnimatorLayout animatorLayout = new CustomAnimatorLayout(1, 0, 1, 0, 500, searchContainer, true);
                searchContainer.startAnimation(animatorLayout);
//					searchContainer.setVisibility(View.GONE);
            }
        } else if (id == R.id.btnSearch) {
//				ScaleAnimation anim = new ScaleAnimation(1,0,1,0);
            btnSearchBar.setChecked(false);
//		        anim.setDuration(500);
//		        anim.setFillAfter(true);
            CustomAnimatorLayout animatorLayout = new CustomAnimatorLayout(1, 0, 1, 0, 500, searchContainer, true);
//				searchContainer.setVisibility(View.GONE);
            searchContainer.startAnimation(animatorLayout);
            String searchKey = "";
            if (txtSearch.getText().length() > 0)
                searchKey = txtSearch.getText().toString().toLowerCase();
            searchQuestion(searchKey, false);
        } else if (id == R.id.btnSave) {
            doSave();
        } else if (id == R.id.btnVerified) {
            doVerify();
        } else if (id == R.id.btnApprove) {
            doApprove();
        } else if (id == R.id.btnReject) {
            doReject();
        } else if (id == R.id.btnClose) {
            this.finish();
        } else if (questionGroupFieldsHeader.contains(v)) {
            onClickInQuestionGroup(v);
        } else if (previewFields.contains(v)) {
            onClickInPreview(v);
            if (TaskHDataAccess.STATUS_TASK_APPROVAL.equalsIgnoreCase(header.getStatus())
                    || TaskHDataAccess.STATUS_TASK_APPROVAL_DOWNLOAD.equalsIgnoreCase(header.getStatus())
                    || mode == Global.MODE_VIEW_SENT_SURVEY) {

            } else {
//                onClickInPreview(v);
                mainMenu.findItem(R.id.mnPendingTask).setVisible(false);
            }
        }
    }

    private ValueAnimator slideAnimator(final View view, int start, int end) {

        ValueAnimator animator = ValueAnimator.ofInt(start, end);

        animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator valueAnimator) {
                //Update Height
                int value = (Integer) valueAnimator.getAnimatedValue();
                ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
                layoutParams.height = value;
                view.setLayoutParams(layoutParams);
            }
        });
        return animator;
    }

    private void actionOnQuestionGroupClicked(LinkedHashMap<String, List<QuestionView>> fields, final String questionGroupId, boolean isClick, boolean isPreview) {
        QuestionView qv = null;
        if (isPreview) {
            if (isClick) {
                for (QuestionView view : questionGroupFieldsHeaderPreview) {
                    if (view.getQuestionGroup() != null && view.getQuestionGroup().getQuestion_group_id().equals(questionGroupId)) {
                        qv = view;
                        break;
                    }
                }
            } else {
                for (QuestionView view : questionGroupFieldsHeader) {
                    if (view.getQuestionGroup().getQuestion_group_id().equals(questionGroupId)) {
                        qv = view;
                        break;
                    }
                }
            }
        } else if (isClick) {
            for (QuestionView view : questionGroupFieldsHeader) {
                if (view.getQuestionGroup().getQuestion_group_id().equals(questionGroupId)) {
                    qv = view;
                    break;
                }
            }
        }
        if (qv != null) {
            QuestionView qview = (QuestionView) qv.getChildAt(0);
            View view2 = qview.getChildAt(1);
            if (qv.isExpanded()) {
                createRotateAnimator(view2, 180f, 0f).start();
                qv.setExpanded(false);
            } else {
                createRotateAnimator(view2, 0f, 180f).start();
                qv.setExpanded(true);
            }
        }

        List<QuestionView> questionViews = fields.get(questionGroupId);
        if (questionViews != null) {
            for (final QuestionView view : questionViews) {
                if (view.getVisibility() == View.INVISIBLE) {
                    final int widthSpec = View.MeasureSpec.makeMeasureSpec(view.getWidth(), View.MeasureSpec.EXACTLY);//View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
                    final int heightSpec = View.MeasureSpec.makeMeasureSpec(10000, View.MeasureSpec.AT_MOST);//View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
                    view.measure(widthSpec, heightSpec);
                    ValueAnimator mAnimator = slideAnimator(view, 0, view.getMeasuredHeight());
                    mAnimator.addListener(new Animator.AnimatorListener() {
                        @Override
                        public void onAnimationStart(Animator animation) {
//                            boolean doing =false;
//                            for (Map.Entry<String, List<QuestionView>> entry : questionFields.entrySet()) {
//                                if(!entry.getKey().equals(questionGroupId) && doing){
//                                    for (QuestionView view : entry.getValue()) {
//                                        view.setVisibility(View.VISIBLE);
//                                    }
//                                }else{
//                                    doing=true;
//                                }
//                            }
                        }

                        @Override
                        public void onAnimationEnd(Animator animation) {
                            view.setVisibility(View.VISIBLE);
                        }

                        @Override
                        public void onAnimationCancel(Animator animation) {

                        }

                        @Override
                        public void onAnimationRepeat(Animator animation) {

                        }
                    });
                    mAnimator.start();
                } else if (isClick) {
//                view.setVisibility(View.GONE);

                    int finalHeight = view.getHeight();

                    ValueAnimator mAnimator = slideAnimator(view, finalHeight, 0);

                    mAnimator.addListener(new Animator.AnimatorListener() {
                        @Override
                        public void onAnimationStart(Animator animation) {

                        }

                        @Override
                        public void onAnimationEnd(Animator animator) {
                            //Height=0, but it set visibility to GONE
                            view.setVisibility(View.INVISIBLE);

                        }

                        @Override
                        public void onAnimationCancel(Animator animation) {

                        }

                        @Override
                        public void onAnimationRepeat(Animator animation) {

                        }

                    });
                    mAnimator.start();
                }
            }
        }
    }

    private void onClickInQuestionGroup(View v) {
        QuestionView qv = (QuestionView) v;
        QuestionGroup group = qv.getQuestionGroup();
        actionOnQuestionGroupClicked(questionFields, group.getQuestion_group_id(), true, false);

    }

    public ObjectAnimator createRotateAnimator(final View target, final float from, final float to) {
        ObjectAnimator animator = ObjectAnimator.ofFloat(target, "rotation", from, to);
        animator.setDuration(300);
        animator.setInterpolator(Interpolator.createInterpolator(Interpolator.LINEAR_INTERPOLATOR));
        return animator;
    }

    private void doReject() {

        boolean isApprovalTask = true;
        btnReject.setEnabled(false);
        if (TaskHDataAccess.STATUS_TASK_VERIFICATION.equals(header.getStatus()) ||
                TaskHDataAccess.STATUS_TASK_VERIFICATION_DOWNLOAD.equals(header.getStatus())) {
            header.setIs_prepocessed(Global.FORM_TYPE_VERIFICATION);
            isApprovalTask = false;
        }
        if (TaskHDataAccess.STATUS_TASK_APPROVAL.equals(header.getStatus()) ||
                TaskHDataAccess.STATUS_TASK_APPROVAL_DOWNLOAD.equals(header.getStatus())) {
            header.setIs_prepocessed(Global.FORM_TYPE_APPROVAL);
            isApprovalTask = true;
        }
        if (header.getSubmit_date() == null)
            header.setSubmit_date(Tool.getSystemDateTime());
        new TaskManager().sendApprovalTask(this, header, Global.FLAG_FOR_REJECTEDTASK, isApprovalTask);
    }

    private void doApprove() {

        boolean isApprovalTask = true;
        btnApprove.setEnabled(false);
        if (header.getSubmit_date() == null)
            header.setSubmit_date(Tool.getSystemDateTime());
        new TaskManager().sendApprovalTask(this, header, Global.FLAG_FOR_APPROVALTASK, isApprovalTask);
    }

    private void doVerify() {
        btnVerified.setEnabled(false);
        if (needQuickValidation) {
            if (validateAllMandatory(true)) {
                sendVerification();
            }
        } else {
            sendVerification();
        }
    }

    private void sendVerification() {
        header.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);
        header.setIs_prepocessed(Global.FORM_TYPE_VERIFICATION);
        List<QuestionBean> questions = new ArrayList<QuestionBean>(listOfQuestion.values());
        if (header.getSubmit_date() == null)
            header.setSubmit_date(Tool.getSystemDateTime());
        new TaskManager().saveAndSendTask(this, mode, header, questions);
    }

    private void onClickInPreview(Object v) {
        try {
            QuestionView view = (QuestionView) v;
            int idx = previewFields.indexOf(view);
            if (view.isTitleOnly()) {
                actionOnQuestionGroupClicked(reviewFields, view.getQuestionGroup().getQuestion_group_id(), true, true);
            } else {
                if (TaskHDataAccess.STATUS_TASK_APPROVAL.equalsIgnoreCase(header.getStatus())
                        || TaskHDataAccess.STATUS_TASK_APPROVAL_DOWNLOAD.equalsIgnoreCase(header.getStatus())
                        || mode == Global.MODE_VIEW_SENT_SURVEY) {

                } else {
                    idxPosition = 0;
                    idxQuestion = 0;
                    btnReject.setClickable(false);
                    btnReject.setImageResource(R.drawable.ic_reject_off);
                    removeNextCallback();
                    removeReviewCallback();
                    QuestionBean bean = view.getQuestionBean();
                    doBack();
                    QuestionView questionView = (QuestionView) questionContainer.getChildAt(idx);
                    if (idx > 1) {
                        QuestionView questionView2 = previewFields.get(idx - 1);
                        if (questionContainer.getChildAt(idx - 1).getVisibility() == View.INVISIBLE) {
                            actionOnQuestionGroupClicked(questionFields, questionView2.getQuestionBean().getQuestion_group_id(), false, true);
                        }
                        questionContainer.getChildAt(idx - 1).requestFocus();
                    } else {
                        if (questionView.getVisibility() == View.INVISIBLE) {
                            actionOnQuestionGroupClicked(questionFields, bean.getQuestion_group_id(), false, true);
                        }
                        questionView.requestFocus();
                    }
                    if (Global.AT_DROPDOWN.equals(bean.getAnswer_type()) ||
                            Global.AT_DROPDOWN_W_DESCRIPTION.equals(bean.getAnswer_type())) {
                        Spinner spinner = (Spinner) questionView.getChildAt(1);
                        spinner.requestFocusFromTouch();
                    } else {
                        questionView.getChildAt(1).requestFocus();
                    }
                }
            }

        } catch (Exception e) {
            FireCrash.log(e);
            if (Global.IS_DEV)
                e.printStackTrace();
        }
    }

    protected void searchQuestion(String key, boolean needValidation) {
        if (this.validateCurrentPage(needValidation, false)) {
            int start = -1;
            int end = listOfQuestion.size();
            int idx = -1;
            int page = 0;
            start++;
            for (; start < end; start++) {
                QuestionBean bean = listOfQuestion.get(listOfIdentifier.get(start));

                if (bean.isVisible()) {
//						if (isQuestVisible(bean.getRelevant_question()) {
//							String relevantExpression = bean.getRelevant_question();
//							if (isQuestVisibleIfRelevant(relevantExpression, bean)) {
                    idx++;
                    String label = bean.getQuestion_label().toLowerCase();
                    if (label.indexOf(key) != -1) {
                        page = idx;
                        break;
                    }
//							}
//						}
                }
            }
            if (isFinish) {
                isFinish = false;
                ScaleAnimation anim = new ScaleAnimation(0, 1, 0, 1);
                anim.setDuration(500);
                anim.setFillAfter(true);
                questionContainer.startAnimation(anim);
                btnNext.setClickable(true);
                btnNext.setImageResource(R.drawable.ic_next);
                btnBack.setVisibility(View.VISIBLE);
                btnSend.setClickable(false);
                btnSend.setImageResource(R.drawable.ic_send_off);
//                if (visibleQuestion.size() == questionContainer.getChildCount()) {
                if (listOfQuestion.size() == questionSize) {
                    scrollView2.setVisibility(View.GONE);
                    scrollView.setVisibility(View.VISIBLE);
                    int childCount = reviewContainer.getChildCount();
                    if (childCount > 0)
                        reviewContainer.removeViews(0, childCount);
                }
            }
            txtSearch.setText("");
            try {
                QuestionView questionView = (QuestionView) currentPageViews.get(page);
                if (questionView.getVisibility() == View.INVISIBLE) {
                    actionOnQuestionGroupClicked(questionFields, visibleQuestion.get(page).getQuestion_group_id(), false, false);
                }
                questionView.requestFocus();
                questionView.getChildAt(1).requestFocus();
            } catch (Exception e) {
                FireCrash.log(e);
            }
        }
    }

    private void doSend() {
        //Glen 22 Oct 2014, try to avoid double tap
        btnSend.setEnabled(false);
//			btnVerified.setEnabled(false);

        //Glen 16 Oct 2014, validate all before sending, if edit preview happened before
        if (needQuickValidation) {
            if (validateAllMandatory(true)) {
//					if (Global.STATUS_SEND_INIT.equals(header.getSentStatus())) {
//						MainMenuActivity.notifCount--;
//						MainMenuActivity.notifHandler.sendEmptyMessage(0);
//					}
//
//			    	header.setSentStatus(Global.STATUS_SEND_PENDING);
//			    	new SurveySaveTask(this, mode, header, listOfQuestion, true).execute();
                saveAndSendSurvey();
                //		return true;
            }
        } else {
            saveAndSendSurvey();
        }

        //Glen 22 Oct 2014, try to avoid double tap
//			btnSend.setEnabled(true);

		/*if (TaskHDataAccess.STATUS_SEND_INIT.equals(header.getStatus())) {
            Constant.notifCount--;
			//TODO bangkit Constant.notifHandler.sendEmptyMessage(0);
		}

		header.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);*/

        //TODO : Buat Save TAsk ~GG
//			task.saveTask(this, mode, header, listOfQuestion, true);

//			new SurveySaveTask(this, mode, header, listOfQuestion, true)
//					.execute();
    }

    //Glen 17 Oct 2014 send method
    //Glen 12/12/14 constant moced from Global to TaskHDataAccess
    public synchronized void saveAndSendSurvey() {
        if (!isSaveAndSending) {
            isSaveAndSending = true;
            if (TaskHDataAccess.STATUS_SEND_INIT.equals(header.getStatus())) {
                Constant.notifCount--;
                //TODO bangkit Constant.notifHandler.sendEmptyMessage(0);
            }

            header.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);
            if (header.getSubmit_date() == null)
                header.setSubmit_date(Tool.getSystemDateTime());

            List<QuestionBean> questions = new ArrayList<QuestionBean>(listOfQuestion.values());

            btnSend.setEnabled(true);
            btnSend.setClickable(true);

            new TaskManager().saveAndSendTask(this, mode, header, questions);  //TODO: Comment yg ini kalo pake submit on background

            isSaveAndSending = false;
            //TODO: UnComment yang bawah kalo mau pake submit on background
            /**new TaskManager().saveAndSendTaskOnBackground(this, mode, header, listOfQuestion);
             try {
             Bundle extras = new Bundle();
             Intent intent = new Intent(getApplicationContext(), SendResultActivity.class);
             extras.putBoolean(Global.BUND_KEY_SURVEY_ERROR, true);
             if(header.getIs_prepocessed()!=null&&header.getIs_prepocessed().equals(Global.FORM_TYPE_VERIFICATION)){
             extras.putString(Global.BUND_KEY_SURVEY_ERROR_MSG, "Data sedang dikirim");
             }else{
             extras.putString(Global.BUND_KEY_SURVEY_ERROR_MSG, "Data sedang dikirim");
             }
             extras.putString(Global.BUND_KEY_TASK_ID, "For Detail Task, open your Timeline or Status tab on TaskList Menu");
             intent.putExtras(extras);
             startActivity(intent);
             finish();
             } catch (Exception e) {
             FireCrash.log(e);
             }*/
            //-------------------------------------------------------
        }

    }

    @Override
    protected Dialog onCreateDialog(int id) {
        Date sysdate = Tool.getSystemDateTime();
        switch (id) {
            case QuestionViewGenerator.TYPE_DATE:
                String dt = Formatter.formatDate(sysdate, Global.DATE_STR_FORMAT);
                String[] temp1 = dt.split("/");
                int dayOfMonth = Integer.parseInt(temp1[0]);
                int month = Integer.parseInt((temp1[1])) - 1;
                int year = Integer.parseInt(temp1[2]);
                DateInputListener dtListener = new DateInputListener();
                return new DatePickerDialog(this, dtListener.getmDateSetListener(),
                        year, month, dayOfMonth);
            case QuestionViewGenerator.TYPE_TIME:
                String tm = Formatter.formatDate(sysdate, Global.TIME_STR_FORMAT);
                String[] temp2 = tm.split(":");
                int hourOfDay = Integer.parseInt(temp2[0]);
                int minute = Integer.parseInt(temp2[1]);
                TimeInputListener tmListener = new TimeInputListener();
                return new TimePickerDialog(this, tmListener.getmTimeSetListener(),
                        hourOfDay, minute, true);
            case QuestionViewGenerator.TYPE_DATE_TIME:
                DialogManager.showDateTimePicker(DynamicFormActivity.this);
                break;
        }
        return null;
    }

    private void doBack() {

        //Glen 15 Oct 2014, when in previewEditMode, gotoNext without validation
        if (inPreviewEditMode) {
            //Glen 16 OCt 2014, changed to true, because we no longer use btnNext
            doNext(true);
            edittedQuestion = null;
            inPreviewEditMode = false;
            return;
        }

        if (isFinish) {
            isFinish = false;
            ScaleAnimation anim = new ScaleAnimation(0, 1, 0, 1);
            anim.setDuration(500);
            anim.setFillAfter(true);
            questionContainer.startAnimation(anim);
            btnNext.setClickable(true);
            btnNext.setImageResource(R.drawable.ic_next);
            btnBack.setVisibility(View.VISIBLE);
            btnSend.setClickable(false);
            btnSend.setImageResource(R.drawable.ic_send_off);
            btnVerified.setClickable(false);
            btnVerified.setImageResource(R.drawable.ic_verified_off);
//            if (visibleQuestion.size() == currentPageViews.size()) {
            if (listOfQuestion.size() == questionSize) {
                scrollView2.setVisibility(View.GONE);
                scrollView.setVisibility(View.VISIBLE);
                int childCount = reviewContainer.getChildCount();
                if (childCount > 0)
                    reviewContainer.removeViews(0, childCount);
            }

            QuestionView questionView = (QuestionView) questionContainer.getChildAt(questionContainer.getChildCount() - 1);

            questionView.getChildAt(1).requestFocus();
//				for(int i=0; i<listOfQuestion.size();i++){
////				for(int i=0; i<paging.getTotalPage();i++){
//					loadDynamicForm();
//				}
        } else {
            if (questionContainer.getChildCount() == 1)
                DialogManager.showExitAlertQuestion(this, getString(R.string.alertExitSurvey));
            else {
                loadBackDynamicForm();
            }
        }
    }

    //Glen 15 Oct 2014, new version for previewMode
    private void updateAffectedBeanAnswer(QuestionBean bean) {

        if (bean == null) return;

        List<QuestionBean> iteratedList;

        //empty answer for all affected bean
        //Glen 16 Oct 2014, empty bean which option may be affected by the changes
        //Glen 22 Oct 2014, copy database to prevent concurrentmodifyexception
//			for (QuestionBean affectedBean : bean.getAffectedQuestionBeans()){
//			for (QuestionBean affectedBean : bean.getAffectedQuestionBeanOptions()){
        iteratedList = new ArrayList<QuestionBean>(bean.getAffectedQuestionBeanOptions());
        for (QuestionBean affectedBean : iteratedList) {
            affectedBean.setAnswer(null);
            affectedBean.setLovCode(null);
            affectedBean.setSelectedOptionAnswers(null);
            updateAffectedBeanAnswer(affectedBean);                //loop
        }

//			for (QuestionBean affectedBean : bean.getAffectedQuestionBeanVisibility()){
        iteratedList = new ArrayList<QuestionBean>(bean.getAffectedQuestionBeanVisibility());
        for (QuestionBean affectedBean : iteratedList) {
            String relevantExpression = affectedBean.getRelevant_question();
            //Glen 29 Oct 2014, fix error
//				boolean isVisible = isQuestVisibleIfRelevant(relevantExpression, bean);
            boolean isVisible = isQuestVisibleIfRelevant(relevantExpression, affectedBean);
            affectedBean.setIs_visible(Formatter.booleanToString(isVisible));
            updateAffectedBeanAnswer(affectedBean);                //loop
        }

//			for (QuestionBean affectedBean : bean.getAffectedQuestionBeanCalculation()){
        iteratedList = new ArrayList<QuestionBean>(bean.getAffectedQuestionBeanCalculation());
        for (QuestionBean affectedBean : iteratedList) {
            setCalculationResult(affectedBean);
            updateAffectedBeanAnswer(affectedBean);
        }

    }

    // bong Oct 10th, 2014 jump to page and validate every page which get passed
//		private void doJumpToPage(int jumpPage) {
//			int current = paging.getPage();
//			AlertDialog.Builder builder = new AlertDialog.Builder(DynamicFormActivity.this)
//			.setTitle("Mandatory Question")
//	        .setMessage("Mandatory Question(s) must be filled")
//	        .setCancelable(false)
//	        .setPositiveButton("Okay",
//	                new DialogInterface.OnClickListener() {
//	            public void onClick(DialogInterface dialog,
//	                    int whichButton) {
//	            	//TASK YOU WANT TO PERFORM
//	            }
//	        });
//			
//			while (paging.getPage() != jumpPage) {
//				//Glen 16 Oct 2014, new param
//				if(paging.getPage()<paging.getTotalPage()){
//					if (this.doNext(true)){
//						current=paging.getPage();
//						current++;
//					}
//					else {
//						builder.create().show();
//						break;
//					}
//				}
//				else {
//					// bong Oct 21st, 2014 - jump to preview if hiddenQuestion is on page before preview page
//					break;
//				}
//			}
//			// print page
//			updateDisplayPage();
//		}

    // bong Oct 10th, 2014 add a jump to page feature
//		private void doJump(){
//			// pop up dialog to input page
//			AlertDialog.Builder builder = new AlertDialog.Builder(DynamicFormActivity.this);
//		    // Get the layout inflater
////		    LayoutInflater inflater = DynamicSurveyActivity.this.getLayoutInflater();
//		    // Inflate and set the layout for the dialog
//		    // Pass null as the parent view because its going in the dialog layout
////		    	builder.setView(inflater.inflate(R.layout.jump_to_page, null));
//			
//			 // Setting Dialog Title
//	        builder.setTitle("Jump to Page");
//
//	        // Setting Dialog Message
//	        builder.setMessage("Input Page");
//	        final EditText inputPage = new EditText(DynamicFormActivity.this);
//	        LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(
//	                                LinearLayout.LayoutParams.MATCH_PARENT,
//	                                LinearLayout.LayoutParams.MATCH_PARENT);
//	          inputPage.setLayoutParams(lp);
//	          // set inputType to number
//	          inputPage.setInputType(InputType.TYPE_CLASS_NUMBER);
//	          inputPage.setPadding(20, 2, 20, 2);
//	          builder.setView(inputPage);
//	          
//		    // Add action buttons
//		           builder.setPositiveButton("Go", new DialogInterface.OnClickListener() {
//		               @Override
//		               public void onClick(DialogInterface dialog, int id) {
//		            	   int jumpPage=0;
//		            	   if(inputPage.getText().toString().length()>0)
//		            		   jumpPage = Integer.parseInt(inputPage.getText().toString());   
//		            	   // Doing jumpPage
//		            	   int current = paging.getPage();
////		            	   final EditText inputPage = (EditText) findViewById(R.id.etPageNumber);
////		            	   if (Global.IS_DEV) System.out.println("inputPage = "+inputPage.getText().toString());
////		            	   int jumpPage = Integer.parseInt(inputPage.getText().toString());
//		            	   
//		            	   if(jumpPage>paging.getTotalPage()||jumpPage<1){
//		            		   AlertDialog.Builder errPage = new AlertDialog.Builder(DynamicFormActivity.this)
//		            			.setTitle("Page Not Available")
//		            	        .setMessage("Requested page is not available")
//		            	        .setCancelable(false)
//		            	        .setPositiveButton("Okay",
//		            	                new DialogInterface.OnClickListener() {
//		            	            public void onClick(DialogInterface dialog,
//		            	                    int whichButton) {
//		            	            	//TASK YOU WANT TO PERFORM
//		            	            }
//		            	        });
//		            		   errPage.create().show();
//		            	   }
//		            	   else{
//		            		   if (jumpPage < current) {
//			            		   jumpToPage(jumpPage, false);
//			            	   } else if (jumpPage > current) {
//			            		   doJumpToPage(jumpPage);
//			            	   }
//		            	   }
//		               }
//		           })
//		           // bong Oct 14th, 2014 - adding last edited page button
//		           .setNeutralButton("Last Edited Page", new DialogInterface.OnClickListener() {
//		               public void onClick(DialogInterface dialog, int id) {
//		                   // return to last edited page
//		            	   int lastEditPage=1; // must be initialize first
//		            	   int current = paging.getPage();
//		            	   
//		            	   // how to get last edited page	            	   
////		            	   FormOpenHelper formOpenHelper = new FormOpenHelper(getApplicationContext());
////		            	   List<QuestionBean> questionBeanList = formOpenHelper.getQuestionSet(header.getForm().getSchemeId());
////		            	   if (Global.IS_DEV) System.out.println("schemeId dari bean = "+questionBean.get(0).getSchemeId());
////		            	   if (Global.IS_DEV) System.out.println("schemeId = "+header.getForm().getSchemeId());
//		            	   
//		            	   List<QuestionBean> questionBeanList = new ArrayList<QuestionBean>();
//		            	   questionBeanList = listOfQuestion;
//		            	   int flag=0; // flag for relevan
//		            	   if(paging.getPageSize()==Global.ROW_PER_PAGE)
//		            		   flag=0;
//		            	   else if (paging.getPageSize()==1)
//		            		   flag=1; // having relevan
//		            	   
//		            	   int page=1;
//		            	   int idx=0;
//		            	   lastEditPage=page;
//		            	   while(idx<questionBeanList.size()){
//		            		   QuestionBean bean = questionBeanList.get(idx);
//		            		   if(QuestionBean.getAnswer(bean)==null||QuestionBean.getAnswer(bean).toString().length()==0){
//		            			   
//		            		   }
//		            		   else {
//		            			   if (Global.IS_DEV) System.out.println("answer is : " + QuestionBean.getAnswer(bean));
//		            			   lastEditPage=page;
//		            		   }
//		            		   if(idx%Global.ROW_PER_PAGE==0 && idx!=0)
//	            				   page++;
//	            			   else if(flag==1)
//	            				   page++;
//		            		   idx++;
//		            	   }
//		            	   if(lastEditPage==0)lastEditPage=1;
////		            	   lastEditPage=page;
//		            	   if (Global.IS_DEV) System.out.println("LastEditPage = "+lastEditPage);
//		            	   // then jump to page
//		            	   if (lastEditPage < current) {
//		            		   jumpToPage(lastEditPage, false);
//		            	   } else if (lastEditPage > current) {
//		            		   doJumpToPage(lastEditPage);
//		            	   }
//		               }
//		           })
//		           .setNegativeButton("Cancel", new DialogInterface.OnClickListener() {
//		               public void onClick(DialogInterface dialog, int id) {
//		                   // Cancel jumpPage
//		               }
//		           });  
//		    	builder.create().show();
//		}

    //Glen 15 Oct 2014, add new param for validation
    private boolean doNext(boolean validate) {
        boolean result = false;
        Utility.freeMemory();
        if (isApproval) {
            while (loadDynamicForm()) {
                isFinish = false;
//					if (visibleQuestion.size()==questionContainer.getChildCount()) {
                if (listOfQuestion.size() == questionSize) {
                    isApproval = false;
//						isVerified=false;
                    isFinish = true;
//						ScaleAnimation anim = new ScaleAnimation(1,0,1,0);
//				        anim.setDuration(200);
//				        anim.setFillAfter(true);
//				        questionContainer.startAnimation(anim);
                    showFinishScreen();
                    break;
                } else {
                    loadDynamicForm();
                    break;
                }
            }
            result = true;
        } else {
            if (this.validateCurrentPage(validate, false)) {

                if (inPreviewEditMode) {

                    needQuickValidation = true;            //flag as need to re-validate all

                    updateAffectedBeanAnswer(edittedQuestion);
                    edittedQuestion = null;
                    inPreviewEditMode = false;
                } else {
                    while (loadDynamicForm()) {
                        isFinish = false;
//							if (visibleQuestion.size()==questionContainer.getChildCount()) {
                        if (listOfQuestion.size() == questionSize) {
                            isApproval = false;
//								isVerified=false;
                            isFinish = true;
                            if (Global.IS_DEV)
                                mainMenu.findItem(R.id.mnPendingTask).setVisible(true);
//								ScaleAnimation anim = new ScaleAnimation(1,0,1,0);
//						        anim.setDuration(200);
//						        anim.setFillAfter(true);
//						        questionContainer.startAnimation(anim);
                            if (validate) {
                                showFinishScreen();
                            } else {
                                if (this.validateCurrentPage(true, false))
                                    showFinishScreen();
                            }
                            break;
                        } else {
                            loadDynamicForm();
                            break;
                        }
                    }
                }
                result = true;
            } else {
//					isVerified=false;
                isApproval = false;
                result = false;
            }
        }
        return result;
    }

    private void doSave() {

//			header.
//			new SurveySaveTask(this, mode, header, listOfQuestion, false)
//					.execute();
        new AsyncTask<Void, Void, Boolean>() {
            boolean isOK = true;
            boolean isSuccess = false;
            private ProgressDialog progressDialog;

            @Override
            protected void onPreExecute() {
                this.progressDialog = ProgressDialog.show(DynamicFormActivity.this, "", DynamicFormActivity.this.getString(R.string.progressWait), true);
            }

            @Override
            public Boolean doInBackground(Void... params) {

                Handler handler = new Handler(Looper.getMainLooper());
                handler.post(new Runnable() {
                    public void run() {

                        if (!isFinish) {
                            isOK = validateCurrentPage(true, true);
                        }
                    }
                });
                if (isOK) {
                    try {
                        header.setStatus(TaskHDataAccess.STATUS_SEND_SAVEDRAFT);
                        List<QuestionBean> questions = new ArrayList<QuestionBean>(listOfQuestion.values());
                        String uuidLastQuestion = visibleQuestion.get(visibleQuestion.size() - 1).getUuid_question_set();
                        isSuccess = new TaskManager().saveTask(DynamicFormActivity.this, mode, header, questions, uuidLastQuestion, false);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        isOK = false;
                        ACRA.getErrorReporter().putCustomData("errorSaveTask", "Pernah error saat save Task");
                        ACRA.getErrorReporter().putCustomData("errorSaveTaskTime", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                        ACRA.getErrorReporter().handleSilentException(e);
                    }
                }
                return isOK;
            }

            @Override
            protected void onPostExecute(Boolean result) {
                if (progressDialog.isShowing()) {
                    try {
                        progressDialog.dismiss();
                    } catch (Exception e) {
                        FireCrash.log(e);
                        if (Global.IS_DEV)
                            e.printStackTrace();
                    }
                }

                if (result && isSuccess) {
                    try {
                        //TODO: Comment kalo mau pake Save on Page, ga keluar dari Form Pertanyaan
                        header = null;
                        questionContainer = null;
                        reviewContainer = null;
                        searchContainer = null;
                        scrollView = null;
                        scrollView2 = null;
                        DynamicFormActivity.this.finish();
                        //-------------------------------------
                        //TODO: Uncomment kalo mau pake Save on Page, ga keluar dari Form Pertanyaan
                        /**runOnUiThread(new Runnable() {
                        @Override public void run() {
                        Toast.makeText(getApplicationContext(),getString(R.string.data_saved), Toast.LENGTH_SHORT).show();
                        }
                        });*/
                    } catch (Exception e) {
                        FireCrash.log(e);
                        finish();
                    }
                } else {
                    try {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                Toast.makeText(getBaseContext(), getString(R.string.data_saved_failed), Toast.LENGTH_SHORT).show();
                            }
                        });
                    } catch (Exception e) {
                        FireCrash.log(e);
                        finish();
                    }
                }
            }
        }.execute();

        // bong Oct 14th, 2014 - deleting tasklistview from local when saving
        //if (Global.IS_DEV) System.out.println("taskId = "+header.getId());
//			SurveyManager svyMgr = new SurveyManager();
//			String taskId = svyMgr.saveSurvey(getApplicationContext(), mode, header,
//   					listOfQuestion);
        //String taskId = header.getId();
        //if (Global.IS_DEV) System.out.println("taskId saat save = "+taskId);
        //TaskListViewOpenHelper taskListViewOpenHelper = new TaskListViewOpenHelper(getApplicationContext());
        //taskListViewOpenHelper.delete(ApplicationBean.getInstance().getUserId(), taskId);

    }

    //Glen 16 Oct 2014, validate all page quickly without saving
    private boolean validateAllMandatory(boolean displayMessage) {
        boolean result = true;
        for (Map.Entry<String, QuestionBean> entry : listOfQuestion.entrySet()) {
            //Glen 10 Nov 2014, fix unanswered question skipped validation, and prevent validation on invisible field
            QuestionBean bean = entry.getValue();
            if (bean.getIs_mandatory().equals(Global.TRUE_STRING) && bean.getIs_visible().equals(Global.TRUE_STRING)) {
                boolean isHaveAnswer = QuestionBean.isHaveAnswer(bean);
                if (!isHaveAnswer) {        //tidak ada isi
                    Toast.makeText(this, bean.getQuestion_label() + " " + getString(R.string.msgRequired), Toast.LENGTH_SHORT).show();
                    result = false;
                }
            }
        }
        return result;
    }

    private boolean validateCurrentPage(boolean isCekValidate, boolean isSave) {
        List<String> errMessage = new ArrayList<String>();
        final String msgRequired = getString(R.string.msgRequired);
        QuestionViewValidator validator = new QuestionViewValidator(msgRequired, getBaseContext());


        //Glen 20 Oct 2014, use list instead of index and paging
//			int idx = paging.getStart() - 1;
//			for (int i = 0; i < questionContainer.getChildCount(); i++) {
//				try {
//					QuestionBean qBean = listOfQuestion.get(idx);
        //
//					LinearLayout qContainer = (LinearLayout) questionContainer
//							.getChildAt(i);
//					List<String> err = validator.validateGeneratedQuestionView(
//							qBean, idx, qContainer);
//					if (err != null && err.size() > 0)
//						errMessage.addAll(err);
//				} finally {
//					idx++;
//				}
//			}
        if (isCekValidate) {
            for (int i = 0; i < currentPageViews.size(); i++) {
                try {
                    QuestionBean qBean = currentPageBeans.get(i);
                    QuestionView qContainer = (QuestionView) currentPageViews.get(i);
                    List<String> err = validator.validateGeneratedQuestionView(qBean, 0, qContainer);        //idx don't seem to be used, so we put 0
                    if (err != null && !err.isEmpty()) errMessage.addAll(err);

                    if (qContainer.isTitleOnly()) {
                        deleteQuestionGroupView();
                    }
                    if (qContainer.isChanged()) {
                        if (qBean.isRelevanted()) {
                            QuestionView view = (QuestionView) questionContainer.getChildAt(i);
                            if (view.isTitleOnly())
                                deleteQuestionGroupView();
                            qContainer.setChanged(false);
                            int childCount = currentPageViews.size();
                            //	QuestionView qContainer2 = (QuestionView) questionContainer.getChildAt(childCount-1);
                            //questionContainer.removeViewAt(childCount);
                            boolean isLastQuestion = true;
                            for (int x = (childCount - 1); x > i; x--) {
//								questionContainer.removeViewAt(x);
//								questionSize--;
//								visibleQuestion.removeLast();
                                currentQuestionGroup = qBean.getQuestion_group_id();
                                if (loadBackDynamicForm()) {
                                    currentPageBeans.remove(x);
                                    currentPageViews.remove(x);
                                }
                                isLastQuestion = false;
                            }
                            if (isSave && !isLastQuestion) {
                                String err2 = getString(R.string.save_on_relevant);
                                errMessage.add(err2);
                            }
                        } else {
//                            ((QuestionView) questionContainer.getChildAt(i)).setChanged(false);
                            qContainer.setChanged(false);
                        }
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    if (Global.IS_DEV)
                        e.printStackTrace();
                }
            }
        } else {
            for (int i = 0; i < currentPageViews.size(); i++) {
                try {
                    QuestionBean qBean = currentPageBeans.get(i);
                    boolean answer = QuestionBean.isHaveAnswer(qBean);
                    if (!answer) {        //tidak ada isi
                        if (qBean.isMandatory()) {
                            if (DynamicFormActivity.isVerified || DynamicFormActivity.isApproval) {
                                if (!Tool.isImage(qBean.getAnswer_type())) {
                                    String err = qBean.getQuestion_label() + " " + getString(R.string.msgRequired);
                                    errMessage.add(err);
                                }
                            } else {
                                String err = qBean.getQuestion_label() + " " + getString(R.string.msgRequired);
                                errMessage.add(err);
                            }
                        }
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    if (Global.IS_DEV)
                        e.printStackTrace();
                }
            }
        }


        if (!errMessage.isEmpty() && isCekValidate) {
            String[] msg = errMessage.toArray(new String[errMessage
                    .size()]);
            String alert = Tool.implode(msg, "\n");
            if (!isApproval) {
                Toast.makeText(this, alert, Toast.LENGTH_SHORT).show();
            }
            return false;
        }
        return true;
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        menu.clear();
        MenuInflater inflater = getMenuInflater();
        inflater.inflate(R.menu.main_menu, menu);
        mainMenu = menu;
        return true;
    }

    @Override
    public boolean onPrepareOptionsMenu(Menu menu) {
        updateMenuIcon(Global.isGPS);
        if (Global.IS_DEV) {
            mainMenu.findItem(R.id.menuMore).setVisible(true);
            mainMenu.findItem(R.id.mnPendingTask).setVisible(isFinish);
        }

        if (!isSimulasi) {
            mainMenu.findItem(R.id.menuMore).setVisible(true);
            mainMenu.findItem(R.id.mnRecord).setVisible(true);
        }

        return super.onPrepareOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // Handle item selection
        int id = item.getItemId();
        if (id == R.id.mnPendingTask) {
            if (Global.IS_DEV) {
                doPending();
            }
        } else if (id == android.R.id.home) {
            onBackPressed();
        } else if (id == R.id.mnRecord) {
            Intent intent = new Intent(this, VoiceNotePage.class);
            Bundle extras = new Bundle();
            extras.putInt(Global.BUND_KEY_MODE_SURVEY, mode);
            extras.putSerializable(Global.BUND_KEY_SURVEY_BEAN, DynamicFormActivity.header);
            intent.putExtras(extras);
            startActivityForResult(intent, Global.REQUEST_VOICE_NOTES);
        } else if (id == R.id.mnGPS) {
            if (Global.LTM != null) {
                if (Global.LTM.isConnected) {
                    Global.LTM.removeLocationListener();
                    Global.LTM.connectLocationClient();
                } else {
                    CheckInManager.startGPSTracking(getBaseContext());
                }
                Animation a = AnimationUtils.loadAnimation(this, R.anim.icon_rotate);
                findViewById(R.id.mnGPS).startAnimation(a);

            }
        }
        return true;

    }

    private void doPending() {
        new AsyncTask<Void, Void, Boolean>() {
            boolean isOK = true;
            boolean isSuccess = false;
            private ProgressDialog progressDialog;

            @Override
            protected void onPreExecute() {
                this.progressDialog = ProgressDialog.show(DynamicFormActivity.this, "", DynamicFormActivity.this.getString(R.string.progressWait), true);
            }

            @Override
            public Boolean doInBackground(Void... params) {

                Handler handler = new Handler(Looper.getMainLooper());
                handler.post(new Runnable() {
                    public void run() {

                        if (!isFinish) {
                            isOK = validateCurrentPage(true, false);
                        }
                    }
                });
                if (isOK) {
                    header.setStatus(TaskHDataAccess.STATUS_SEND_PENDING);
                    List<QuestionBean> questions = new ArrayList<QuestionBean>(listOfQuestion.values());
                    isSuccess = new TaskManager().saveTask(DynamicFormActivity.this, mode, header, questions, "1", true);
                }
                return isOK;
            }

            @Override
            protected void onPostExecute(Boolean result) {
                if (progressDialog.isShowing()) {
                    try {
                        progressDialog.dismiss();
                    } catch (Exception e) {
                        FireCrash.log(e);
                        if (Global.IS_DEV)
                            e.printStackTrace();
                    }
                }

                if (result && isSuccess) {
                    header = null;
                    questionContainer = null;
                    reviewContainer = null;
                    searchContainer = null;
                    scrollView = null;
                    scrollView2 = null;
                    DynamicFormActivity.this.finish();
                }
            }
        }.execute();
    }

    private void showFinishScreen() {
        removeNextCallback();
        showFinishScreen(null);
    }

    private void removeNextCallback() {
        if (nextHandler != null) {
            hasLoading = false;
            nextHandler.removeCallbacks(nextRunnable);
            btnBack.setClickable(true);
            btnNext.setClickable(true);
            btnSend.setClickable(btnSend.getDrawable().getConstantState() != getResources().getDrawable(R.drawable.ic_send_off).getConstantState());
//            if(btnSend.isEnabled()){
//                btnSend.setClickable(true); }else {
//                btnSend.setClickable(false);
//            }
//            btnSend.setClickable(true);
            btnSave.setClickable(true);
            btnSearch.setClickable(true);
        }
        Utility.freeMemory();
    }

    private void removeReviewCallback() {
        if (reviewHandler != null) {
            reviewHandler.removeCallbacks(reviewRunnable);
            btnClose.setClickable(true);
            if (previewFields != null) {
                for (LinearLayout view : previewFields) {
                    if (((QuestionView) view).isTitleOnly()) {
                        view.setClickable(true);
                    }
                }
            }
        }
        Utility.freeMemory();
    }

    //Glen 6 Aug 2014, show finish screen with message (add parameter, and extract an empty parameter method)
    private void showFinishScreen(String message) {
//        validateCurrentPage(false,false);
        idxQuestion = 0;
        reviewContainer.removeAllViews();
        setTitle("Review Task");
        scrollView.setVisibility(View.GONE);
        ScaleAnimation anim = new ScaleAnimation(0, 1, 0, 1);
        anim.setDuration(500);
        anim.setFillAfter(true);
        reviewContainer.startAnimation(anim);
//			btnReview.setVisibility(View.GONE);
        if (Global.NEW_FEATURE) {
            if (Global.FEATURE_REJECT_WITH_RESURVEY) {
                if (!isVerified) {
                    btnNext.setClickable(false);
                    btnNext.setImageResource(R.drawable.ic_next_off);
                }
            }
        } else {
            btnNext.setClickable(false);
            btnNext.setImageResource(R.drawable.ic_next_off);
        }

        btnBack.setVisibility(View.VISIBLE);
        btnSend.setEnabled(true);
        btnSend.setClickable(true);
        btnSend.setImageResource(R.drawable.ic_send);
        btnSend.setOnClickListener(this);
        btnReject.setClickable(true);
        btnReject.setImageResource(R.drawable.ic_reject);
        btnVerified.setClickable(true);
        btnVerified.setImageResource(R.drawable.ic_verified);
        //Glen 6 August 2014, generate preview
        if (previewFields == null) previewFields = new ArrayList<QuestionView>();
        if (questionGroupFieldsHeaderPreview == null)
            questionGroupFieldsHeaderPreview = new ArrayList<QuestionView>();
        if (reviewFields == null) reviewFields = new LinkedHashMap<>();
        previewFields.clear();
        questionGroupFieldsHeaderPreview.clear();
        scrollView2.setVisibility(View.VISIBLE);

        reviewRunnable = new Runnable() {
            @Override
            public void run() {
//                reviewHandler.postDelayed(reviewRunnable, 200);
                try {

                    if (idxQuestion < listOfQuestion.size()) {
                        QuestionBean questionBean = listOfQuestion.get(listOfIdentifier.get(idxQuestion));
//                        if(idxQuestion==0){
//                            currentQuestionGroupInReview = questionBean.getQuestion_group_id();
//                        }
                        String newQuestionGroup = questionBean.getQuestion_group_id();
                        if (questionBean.isVisible()) {
                            String relevantExpression = questionBean.getRelevant_question();
                            if (relevantExpression == null) relevantExpression = "";
                            if (isQuestVisibleIfRelevant(relevantExpression, questionBean)) {
                                if (currentQuestionGroupInReview == null || !newQuestionGroup.equals(currentQuestionGroupInReview)) {
                                    currentQuestionGroupInReview = newQuestionGroup;
                                    QuestionGroup group = new QuestionGroup(questionBean);
                                    QuestionView view = reviewGenerator.generateReviewQuestion(DynamicFormActivity.this, questionBean, ViewImageActivity.class, group);
                                    ScaleAnimation anim = new ScaleAnimation(0, 1, 0, 1);
                                    anim.setDuration(200);
                                    anim.setFillAfter(true);
                                    view.startAnimation(anim);
                                    reviewContainer.addView(view, LayoutParams.MATCH_PARENT,
                                            LayoutParams.WRAP_CONTENT);
                                    view.setOnClickListener(DynamicFormActivity.this);
                                    view.setClickable(false);
                                    previewFields.add(view);
                                    questionGroupFieldsHeaderPreview.add(view);
                                }
                                reviewHandler.postDelayed(reviewRunnable, 200);
                                QuestionView field = null;
                                try {
                                    field = reviewGenerator.generateReviewQuestion(DynamicFormActivity.this, questionBean, ViewImageActivity.class, null);
                                } catch (Exception e) {
                                    FireCrash.log(e);
                                    if (Global.IS_DEV)
                                        e.printStackTrace();
                                }
                                if (field != null) {
//                                    LinearLayout layout = new LinearLayout(DynamicFormActivity.this);
//                                    layout.setBackgroundColor(Color.TRANSPARENT);
                                    reviewContainer.addView(field, LayoutParams.MATCH_PARENT,
                                            LayoutParams.WRAP_CONTENT);
//                                    reviewContainer.addView(layout, LayoutParams.MATCH_PARENT,
//                                            10);
                                    field.setOnClickListener(DynamicFormActivity.this);
                                    //										ScaleAnimation anim = new ScaleAnimation(0,1,0,1);
                                    //								        anim.setDuration(200);
                                    //								        anim.setFillAfter(true);
                                    //								        field.startAnimation(anim);
                                    previewFields.add(field);
                                    List<QuestionView> tempList = reviewFields.get(questionBean.getQuestion_group_id());
                                    if (tempList != null) {
                                        tempList.add(field);
                                    } else {
                                        tempList = new ArrayList<>();
                                        tempList.add(field);
                                    }
                                    reviewFields.put(questionBean.getQuestion_group_id(), tempList);
                                }
                            } else {
                                reviewHandler.post(reviewRunnable);
                            }
                        } else if (!questionBean.isVisible()) {
                            //Glen 17 Oct 2014, add empty view, to make sure the order of field in previewFields is still correct
                            //				previewFields.add(null);
                            //				continue;
                            String relevantExpression = questionBean.getRelevant_question();
                            if (relevantExpression == null) {
                                relevantExpression = "";
                            }
                            if (isQuestVisibleIfRelevant(relevantExpression, questionBean)) {
                                QuestionSet tempQuestion = QuestionSetDataAccess.getOne(getBaseContext(), header.getUuid_scheme(), questionBean.getQuestion_id(), questionBean.getQuestion_group_id());
                                if (tempQuestion != null) {
                                    if (tempQuestion.getIs_visible().equals(Global.TRUE_STRING)) {
                                        if (currentQuestionGroupInReview == null || !newQuestionGroup.equals(currentQuestionGroupInReview)) {
                                            currentQuestionGroupInReview = newQuestionGroup;
                                            QuestionGroup group = new QuestionGroup(questionBean);
                                            QuestionView view = reviewGenerator.generateReviewQuestion(DynamicFormActivity.this, questionBean, ViewImageActivity.class, group);
                                            ScaleAnimation anim = new ScaleAnimation(0, 1, 0, 1);
                                            anim.setDuration(200);
                                            anim.setFillAfter(true);
                                            view.startAnimation(anim);
                                            reviewContainer.addView(view, LayoutParams.MATCH_PARENT,
                                                    LayoutParams.WRAP_CONTENT);
                                            view.setOnClickListener(DynamicFormActivity.this);
                                            view.setClickable(false);
                                            previewFields.add(view);
                                            questionGroupFieldsHeaderPreview.add(view);
                                        }
                                        reviewHandler.postDelayed(reviewRunnable, 200);
                                        questionBean.setVisible(true);
                                        QuestionView field = null;
                                        try {
                                            field = reviewGenerator.generateReviewQuestion(DynamicFormActivity.this, questionBean, ViewImageActivity.class, null);
                                        } catch (Exception e) {
                                            FireCrash.log(e);
                                            if (Global.IS_DEV)
                                                e.printStackTrace();
                                        }
                                        if (field != null) {
//                                            LinearLayout layout = new LinearLayout(DynamicFormActivity.this);
//                                            layout.setBackgroundColor(Color.TRANSPARENT);
                                            reviewContainer.addView(field, LayoutParams.MATCH_PARENT,
                                                    LayoutParams.WRAP_CONTENT);
//                                            reviewContainer.addView(layout, LayoutParams.MATCH_PARENT,
//                                                    10);
                                            field.setOnClickListener(DynamicFormActivity.this);
                                            previewFields.add(field);
                                            List<QuestionView> tempList = reviewFields.get(questionBean.getQuestion_group_id());
                                            if (tempList != null) {
                                                tempList.add(field);
                                            } else {
                                                tempList = new ArrayList<>();
                                                tempList.add(field);
                                            }
                                            reviewFields.put(questionBean.getQuestion_group_id(), tempList);
                                        }
                                    } else {
                                        reviewHandler.post(reviewRunnable);
                                        questionBean.setVisible(false);
                                    }
                                } else {
                                    reviewHandler.post(reviewRunnable);
                                }
                            } else {
                                reviewHandler.post(reviewRunnable);
                            }
                        }
                        idxQuestion++;
                    } else {
                        removeReviewCallback();
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    idxQuestion = 0;
                    if (listOfQuestion == null) {
                        listOfQuestion = Constant.listOfQuestion;
                        if (listOfQuestion == null) {
                            removeReviewCallback();
                        }
                    }
                }
            }
        };
        if (reviewHandler == null)
            reviewHandler = new Handler();
        reviewHandler.postDelayed(reviewRunnable, 200);
    }

    @Override
    public void onBackPressed() {
        try {
            removeNextCallback();
            removeReviewCallback();
        } catch (Exception e) {
            FireCrash.log(e);

        }
        if (mode == Global.MODE_VIEW_SENT_SURVEY)
            super.onBackPressed();
        else {
            DialogManager.showExitAlertQuestion(this, getString(R.string.alertExitSurvey));
        }
    }

    @Override
    public void onPreExecute(GenericAsyncTask task) {


    }

    //Glen 7 Jan 2015, updated GenericAsyncTask
    @Override
    public String doInBackground(GenericAsyncTask task, String... args) {
        String[] result = null;
        //SurveyManager svyMgr = new SurveyManager();
        try {

            header.setStatus(TaskHDataAccess.STATUS_SEND_SAVEDRAFT);
            //
            //TODO : Buat Save TAsk ~GG
//				String taskId = this.task.saveTask(this, mode, header,listOfQuestion);
//					// SurveySaveForPreSubmitTask fakeTask = new
//					// SurveySaveForPreSubmitTask(this, mode, header, listOfQuestion,
//					// false);
//					// fakeTask.do
//
//					//header.setUuid_task_h(taskId);
//					header.setTask_id(taskId);
//					mode = Global.MODE_SURVEY_TASK;
//					result = this.task.doPreSubmitNewSurveyTask(this, taskId, true);
        } catch (Exception e) {
            FireCrash.log(e);
            result = null;
            if (Global.IS_DEV)
                e.printStackTrace();
            return null;
        }

        String flattenedResult = Tool.implode(result, Global.DELIMETER_DATA2);

        return flattenedResult;
    }

		
		/*//Glen 10 Oct 2014, on preview click
//		@TargetApi(Build.VERSION_CODES.HONEYCOMB)
		public void onPreviewClicked(QuestionBean bean){		//bean of selected preview item to be editted
			idxPosition=0;
			idxQuestion=0;
			//Glen 21 Oct 2014, disable menu
			invalidateOptionsMenu();
			
			edittedQuestion = bean;
			
			inPreviewEditMode = true;
			//hack paging set to previous page, so when user press doNext they go to previewScreen again
//			paging.previous();
			
			this.clearListView();
			int idx = listOfQuestion.get(bean.getIdentifier_name());
			ViewGroup questionField = getQuestionView(bean, idx+1);
			if (Tool.isOptions(bean.getAnswer_type())){
				MultiOptionQuestionViewAbstract multipleOptionQuestionField = (MultiOptionQuestionViewAbstract) questionField;
				loadOptionsToView(multipleOptionQuestionField);
			}
			questionContainer.addView(questionField);
			
			//Glen 20 Oct 2014
			currentPageBeans.add(bean);
			currentPageViews.add(questionField);
			
			
			btnNext.setClickable(true);
			btnNext.setImageResource(R.drawable.ic_next);
			btnSend.setClickable(false);
			btnSend.setImageResource(R.drawable.ic_send_off);
		}*/

    @Override
    public void onPostExecute(GenericAsyncTask task, String result,
                              String errMsg) {
        if (result != null && result.length() > 0) {

            String[] explodedResult = Tool.explode(result, Global.DELIMETER_DATA2);
            String[] serverResponses = Tool.explode(explodedResult[0], Global.DELIMETER_ROW);
            //index0 = server message, index1 = null, index2 = status: 1=success -1=fail
            String serverMessage = serverResponses[0];
            showFinishScreen(serverMessage);
        }
        //Glen 11 Aug 2014, return toast message for failure
        else {
            Toast.makeText(this, getString(R.string.msgConnectionFailed), Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onLocationChanged(Location location) {
    }

    @Override
    public void onStatusChanged(String provider, int status, Bundle extras) {
    }

    @Override
    public void onProviderEnabled(String provider) {
        DialogManager.closeGPSAlert();
    }

    @Override
    public void onProviderDisabled(String provider) {
        DialogManager.showGPSAlert(this);
    }

    private class RestoreGlobalData extends AsyncTask<Void, Void, Void> {
        protected Void doInBackground(Void... urls) {
            MainMenuActivity.InitializeGlobalDataIfError(getBaseContext());
            return null;
        }


        protected void onPostExecute(Void result) {

        }
    }


    //=== Request JSON From Server Delegate ===//
//		@Override
//		public void onConnectionFinish(RequestJsonFromURLTask request, String result) {
//			if (result != null && result.length() > 0){
//				//Yes there is result
//				showFinishScreen(result);
//			}
//			else{
//				//throw as connection failed
//				onConnectionFailed(request, null, "Failed to connect to server\nPlease try again later");
//			}
//			
//		}
    //
//		@Override
//		public void onConnectionFailed(RequestJsonFromURLTask request,
//				String result, String errMessage) {
//			
//			Toast.makeText(this, errMessage, Toast.LENGTH_SHORT).show();
//			
//		}

    private class SavingDynamicData extends AsyncTask<Void, Void, Void> {
        protected Void doInBackground(Void... urls) {
            SaveDataToTemporary(dfdq);
            return null;
        }

        protected void onPostExecute(Void result) {

        }
    }

    private class GetLookupOnDemand extends AsyncTask<Void, Void, Boolean> {

        String errMessage = null;
        String lov_group = null;
        int constraintAmount = 0;
        List<String> filters;
        private ProgressDialog progressDialog;


        public GetLookupOnDemand(String lovGroup) {
            this.lov_group = lovGroup;
        }

        public GetLookupOnDemand(String lovGroup, List<String> filters) {
            this.lov_group = lovGroup;
            if (filters != null)
                this.filters = filters;
            else
                this.filters = new ArrayList<String>();
        }

        public GetLookupOnDemand(String lovGroup, List<String> filters, int constraintAmount) {
            this.lov_group = lovGroup;
            if (filters != null)
                this.filters = filters;
            else
                this.filters = new ArrayList<String>();
            this.constraintAmount = constraintAmount;
        }

        @Override
        protected void onPreExecute() {
            String message = getString(R.string.lookup_progress, lov_group);
            this.progressDialog = ProgressDialog.show(DynamicFormActivity.this, "", message, true);
        }

        @Override
        protected Boolean doInBackground(Void... params) {
            if (Tool.isInternetconnected(getBaseContext())) {
                //----------------------Get Lookup Parameter----------------------
                Calendar cal = Calendar.getInstance();
                cal.setTimeInMillis(0);
                cal.set(1971, 1, 1, 1, 1, 1);
                Date date = cal.getTime();

                String url = GlobalData.getSharedGlobalData().getURL_SYNCPARAM_CONSTRAINT();
                List<HashMap<String, Object>> lookupArgs = new ArrayList<HashMap<String, Object>>();
                HashMap<String, Object> forms = new HashMap<String, Object>();
                forms.put(LookupDao.Properties.Lov_group.name, lov_group);
                forms.put(LookupDao.Properties.Dtm_upd.name, date);
                if (!filters.isEmpty()) {
                    if (filters.size() == 1) {
                        forms.put(LookupDao.Properties.Filter1.name, filters.get(0));
                    } else if (filters.size() == 2) {
                        forms.put(LookupDao.Properties.Filter1.name, filters.get(0));
                        forms.put(LookupDao.Properties.Filter2.name, filters.get(1));
                    } else if (filters.size() == 3) {
                        forms.put(LookupDao.Properties.Filter1.name, filters.get(0));
                        forms.put(LookupDao.Properties.Filter2.name, filters.get(1));
                        forms.put(LookupDao.Properties.Filter3.name, filters.get(2));
                    } else if (filters.size() == 4) {
                        forms.put(LookupDao.Properties.Filter1.name, filters.get(0));
                        forms.put(LookupDao.Properties.Filter2.name, filters.get(1));
                        forms.put(LookupDao.Properties.Filter3.name, filters.get(2));
                        forms.put(LookupDao.Properties.Filter4.name, filters.get(3));
                    } else if (filters.size() == 5) {
                        forms.put(LookupDao.Properties.Filter1.name, filters.get(0));
                        forms.put(LookupDao.Properties.Filter2.name, filters.get(1));
                        forms.put(LookupDao.Properties.Filter3.name, filters.get(2));
                        forms.put(LookupDao.Properties.Filter4.name, filters.get(3));
                        forms.put(LookupDao.Properties.Filter5.name, filters.get(4));
                    }
                }
                forms.put("constraintAmount", constraintAmount);

                lookupArgs.add(forms);

                SynchronizeRequestModel request = new SynchronizeRequestModel();
                request.setInit(0);

                if (!lookupArgs.isEmpty())
                    request.setList(lookupArgs);
                request.setAudit(GlobalData.getSharedGlobalData().getAuditData());

                request.setTableName("MS_LOV");
                request.setDtm_upd(date);


                String jsonRequest = GsonHelper.toJson(request);
                boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
                boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
                HttpCryptedConnection httpConn = new HttpCryptedConnection(DynamicFormActivity.this, encrypt, decrypt);
                HttpConnectionResult serverResult = null;
                try {
                    serverResult = httpConn.requestToServer(url, jsonRequest, Global.DEFAULTCONNECTIONTIMEOUT);
                } catch (Exception e) {
                    FireCrash.log(e);
                }
                if (serverResult != null && serverResult.isOK()) {
                    String body = serverResult.getResult();
                    try {
                        SynchronizeResponseLookup entityLookup = GsonHelper.fromJson(body, SynchronizeResponseLookup.class);
                        List<Lookup> entitiesLookup = entityLookup.getListSync();
                        if (entitiesLookup != null && !entitiesLookup.isEmpty())
                            LookupDataAccess.addOrUpdateAll(getBaseContext(), entitiesLookup);
                        else
                            errMessage = getString(R.string.lookup_not_available, lov_group);
                    } catch (JsonSyntaxException e) {
                        FireCrash.log(e);
                        ACRA.getErrorReporter().putCustomData("errorGetLookupOnDemand", e.getMessage());
                        ACRA.getErrorReporter().putCustomData("errorGetLookupOnDemandDate", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                        ACRA.getErrorReporter().handleSilentException(new Exception("Just for the stacktrace, kena exception saat Get Lookup on Demand"));
                        return false;
                    } catch (IllegalStateException e) {
                        FireCrash.log(e);
                        ACRA.getErrorReporter().putCustomData("errorGetLookupOnDemand", e.getMessage());
                        ACRA.getErrorReporter().putCustomData("errorGetLookupOnDemandDate", DateFormat.format("yyyy.MM.dd G \'at\' HH:mm:ss z", Calendar.getInstance().getTime()).toString());
                        ACRA.getErrorReporter().handleSilentException(new Exception("Just for the stacktrace, kena exception saat Get Lookup on Demand"));
                        return false;
                    }


                    /*Sync newSync = newSyncResult.get(lov_group);
                    if (newSync != null) {
                        syncLov.setDtm_upd(newSync.getDtm_upd());
                        SyncDataAccess.addOrReplace(getApplicationContext(), syncLov);
                    }*/
                    return true;
                } else {
                    if (Global.IS_DEV)
                        errMessage = String.valueOf(serverResult.getStatusCode());
                    return false;
                }
            } else {
                errMessage = getString(R.string.connection_failed);
                return true;
            }
        }

        @Override
        protected void onPostExecute(Boolean result) {
            if (progressDialog.isShowing()) {
                try {
                    progressDialog.dismiss();
                } catch (Exception e) {
                    FireCrash.log(e);
                    if (Global.IS_DEV)
                        e.printStackTrace();
                }
            }
            if (errMessage != null) {
                Toast.makeText(getBaseContext(), errMessage, Toast.LENGTH_LONG).show();
                isStop = true;
            } else {
                if (!result) {
                    String message = getString(R.string.get_lookup_failed, lov_group);
                    Toast.makeText(getBaseContext(), message, Toast.LENGTH_LONG).show();
                    isStop = true;

                    if (questionContainer.getChildCount() != 1 && loadBackDynamicForm()) {
                        currentPageBeans.remove(currentPageBeans.size() - 1);
                        currentPageViews.remove(currentPageViews.size() - 1);
                    }
                } else {
                    QuestionBean qBean = currentPageBeans.get(currentPageBeans.size() - 1);
                    MultiOptionQuestionViewAbstract qContainer = (MultiOptionQuestionViewAbstract) currentPageViews.get(currentPageViews.size() - 1);
                    if (qContainer != null) {
                        String answerType = qBean.getAnswer_type();

                        if (Tool.isOptions(answerType)) {
                            if (qBean.getOptionAnswers() == null || qBean.getOptionAnswers().isEmpty() || qBean.getOptionRelevances().length > 0) {
                                List<OptionAnswerBean> options = getOptionsForQuestion2(qContainer.getQuestionBean());
                                qContainer.setOptions(getBaseContext(), options);
                            }
                        }

                    }
                }
            }
        }
    }

    final class DynamicSurveyHandler extends Handler {

        @Override
        public void handleMessage(Message msg) {
            this.setQuestionInFocusByModel(msg.what);
        }

        private void setQuestionInFocusByModel(int idx) {
            setQuestionInFocus(listOfQuestion.get(listOfIdentifier.get(idx)));

            int mod = ++idx % listOfQuestion.size();
            int position = (mod == 0) ? listOfQuestion.size() : mod;

            int start = 0;
            int end = listOfQuestion.size();

            if (idx >= start && idx <= end) {
                LinearLayout qContainer = (LinearLayout) questionContainer
                        .getChildAt(--position);
                setThumbInFocus((ImageView) qContainer.getChildAt(1));
                if (qContainer.getChildCount() > 2) {
                    setThumbLocationInfo((ImageView) qContainer.getChildAt(2));
                    setTxtDetailInFocus((TextView) qContainer.getChildAt(3));
                } else {
                    setTxtDetailInFocus((TextView) qContainer.getChildAt(2));
                }
            } else {
                setThumbInFocus(null);
            }
        }
    }

    public class ProcessingBitmap extends AsyncTask<File, Void, Bitmap> {
        private final WeakReference<ImageView> imageViewReference;
        private final WeakReference<ImageView> imageLocViewReference;
        private final WeakReference<TextView> txtViewReference;
        private ProgressDialog progressDialog;
        private String errMessage = "";
        private String formattedSize;
        private String indicatorGPS = "";
        private boolean isGeoTagged;
        private boolean isGeoTaggedGPSOnly;
        private int[] res;

        public ProcessingBitmap(ImageView imageView, ImageView imageViewLcoation) {
            this.imageLocViewReference = new WeakReference<ImageView>(imageViewLcoation);
            this.imageViewReference = new WeakReference<ImageView>(imageView);
            this.txtViewReference = new WeakReference<TextView>(DynamicFormActivity.getTxtDetailInFocus());
        }

        public ProcessingBitmap() {
            this.imageViewReference = new WeakReference<ImageView>(DynamicFormActivity.getThumbInFocus());
            this.imageLocViewReference = new WeakReference<ImageView>(DynamicFormActivity.getThumbLocationInfo());
            this.txtViewReference = new WeakReference<TextView>(DynamicFormActivity.getTxtDetailInFocus());
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            progressDialog = ProgressDialog.show(DynamicFormActivity.this, "", DynamicFormActivity.this.getString(R.string.processing_image), true);
        }

        @Override
        protected Bitmap doInBackground(File... params) {
            try {
                File file = params[0];
                ExifData exifData = Utils.getDataOnExif(file);
                int rotate = exifData.getOrientation();
                float scale;
                int newSize = 0;
                int quality = Utils.picQuality;
                int thumbHeight = Utils.picHeight;
                int thumbWidht = Utils.picWidth;
                QuestionBean bean = DynamicFormActivity.getQuestionInFocus();
                boolean isHQ = false;
                if (bean.getImg_quality() != null && bean.getImg_quality().equalsIgnoreCase(Global.IMAGE_HQ)) {
                    thumbHeight = Utils.picHQHeight;
                    thumbWidht = Utils.picHQWidth;
                    quality = Utils.picHQQuality;
                    isHQ = true;
                }

                boolean isHeightScale = thumbHeight >= thumbWidht;

                System.gc();
                byte[] data = null;
                try {
                    if (isHeightScale) {
                        scale = (float) thumbHeight / exifData.getHeight();
                        newSize = Math.round(exifData.getWidth() * scale);
                        data = Utils.resizeBitmapFileWithWatermark(file, rotate, newSize, thumbHeight, quality, DynamicFormActivity.this, isHQ);
                    } else {
                        scale = (float) thumbWidht / exifData.getWidth();
                        newSize = Math.round(exifData.getHeight() * scale);
                        data = Utils.resizeBitmapFileWithWatermark(file, rotate, thumbWidht, newSize, quality, DynamicFormActivity.this, isHQ);
                    }
                } catch (Exception e) {
                    FireCrash.log(e);
                    data = null;
                }
                if (data != null) {
                    deleteLatestPictureCreate();
                    try {
                        if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.KITKAT && !GlobalData.getSharedGlobalData().isUseOwnCamera()) {
                            Utils.deleteLatestPicture();
                        }
                    } catch (Exception e) {
                        FireCrash.log(e);
                        try {
                            String manufacture = android.os.Build.MANUFACTURER;
                            if (manufacture.contains("LGE") && !GlobalData.getSharedGlobalData().isUseOwnCamera()) {
                                Utils.deleteLatestPictureLGE();
                            }
                        } catch (Exception e2) {
                            FireCrash.log(e2);
                        }
                    }


                    DynamicFormActivity.saveImage(data);

                    boolean getGPS = true;

                    LocationInfo locBean;

                    isGeoTagged = Global.AT_IMAGE_W_LOCATION.equals(bean.getAnswer_type());
                    isGeoTaggedGPSOnly = Global.AT_IMAGE_W_GPS_ONLY.equals(bean.getAnswer_type());
                    if (isGeoTagged) {
                        LocationTrackingManager pm = Global.LTM;
                        if (pm != null) {
                            locBean = pm.getCurrentLocation(Global.FLAG_LOCATION_CAMERA);
                            LocationInfo2 infoFinal = new LocationInfo2(locBean);

                            if (infoFinal.getLatitude().equals("0.0") || infoFinal.getLongitude().equals("0.0")) {
                                if (infoFinal.getMcc().equals("0") || infoFinal.getMnc().equals("0")) {
                                    if (bean.isMandatory()) {
                                        bean.setLocationInfo(infoFinal);
                                        String geodataError = getString(R.string.geodata_error);
                                        String[] msg = {geodataError};
                                        String alert2 = Tool.implode(msg, "\n");
                                        errMessage = alert2;
//                                    Toast.makeText(this, alert2, Toast.LENGTH_LONG).show();
                                        DynamicFormActivity.saveImage(null);
                                        DynamicFormActivity.saveImageLocation(null);
                                        getGPS = false;
                                        return null;
                                    }
                                } else {
                                    bean.setAnswer(getString(R.string.coordinat_not_available));
                                    bean.setLocationInfo(infoFinal);
                                    indicatorGPS = bean.getAnswer();
                                    if (bean.isMandatory()) {
                                        String gpsError = getString(R.string.gps_gd_error);
                                        String[] msg = {gpsError};
                                        String alert2 = Tool.implode(msg, "\n");
                                        errMessage = alert2;
                                        return null;
//                                    Toast.makeText(this, alert2, Toast.LENGTH_LONG).show();
                                    }
                                }
                            } else {
                                bean.setAnswer(LocationTrackingManager.toAnswerString_short(infoFinal));
                                bean.setLocationInfo(infoFinal);
                                indicatorGPS = bean.getAnswer();
                            }
                        }
                    }

                    if (isGeoTaggedGPSOnly) {
                        LocationTrackingManager pm = Global.LTM;
                        if (pm != null) {
                            locBean = pm.getCurrentLocation(Global.FLAG_LOCATION_CAMERA);
                            LocationInfo2 infoFinal = new LocationInfo2(locBean);
                            if (infoFinal.getLatitude().equals("0.0") || infoFinal.getLongitude().equals("0.0")) {

                                if (bean.isMandatory()) {
                                    bean.setLocationInfo(infoFinal);
                                    String gpsError = getString(R.string.gps_error);
                                    String[] msg = {gpsError};
                                    String alert2 = Tool.implode(msg, "\n");
                                    errMessage = alert2;
//                                Toast.makeText(this, alert2, Toast.LENGTH_LONG).show();
                                    DynamicFormActivity.saveImage(null);
                                    DynamicFormActivity.saveImageLocation(null);
                                    getGPS = false;
                                    return null;
                                }

                            } else {
                                bean.setAnswer(LocationTrackingManager.toAnswerString_short(infoFinal));
                                bean.setLocationInfo(infoFinal);
                                indicatorGPS = bean.getAnswer();
                            }
                        }
                    }

                    // set thumbnail
                    if (DynamicFormActivity.getThumbInFocus() != null && getGPS) {
                        Bitmap bm = BitmapFactory.decodeByteArray(data, 0, data.length);
                        res = new int[2];
                        res[0] = bm.getWidth();
                        res[1] = bm.getHeight();
                        int[] thumbRes = Tool.getThumbnailResolution(bm.getWidth(), bm.getHeight());
                        Bitmap thumbnail = Bitmap.createScaledBitmap(bm, thumbRes[0], thumbRes[1], true);

                        long size = bean.getImgAnswer().length;
                        formattedSize = Formatter.formatByteSize(size);

                        return thumbnail;

                    } else {
                        return null;
                    }
                } else {
                    return null;
                }
            } catch (OutOfMemoryError e) {
                FireCrash.log(e);

                errMessage = DynamicFormActivity.this.getString(R.string.processing_image_error);
                return null;
            } catch (Exception e) {
                FireCrash.log(e);
                errMessage = DynamicFormActivity.this.getString(R.string.camera_error);
                return null;
            }
        }

        @Override
        protected void onPostExecute(final Bitmap bitmap) {
            super.onPostExecute(bitmap);
            if (progressDialog != null && progressDialog.isShowing()) {
                progressDialog.dismiss();
            }
            if (errMessage != null && !errMessage.isEmpty()) {
                Toast.makeText(DynamicFormActivity.this, errMessage, Toast.LENGTH_SHORT).show();
            } else if (imageViewReference != null && bitmap != null) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        final ImageView imageView = imageViewReference.get();
                        if (imageView != null) {
                            imageView.setImageBitmap(bitmap);
                        }
                        if (isGeoTagged || isGeoTaggedGPSOnly) {
                            try {
                                Bitmap thumbLocation = BitmapFactory.decodeResource(getResources(), R.drawable.ic_absent);
                                try {
                                    if (imageLocViewReference != null && thumbLocation != null) {
                                        final ImageView imageViewLoc = imageLocViewReference.get();
                                        if (imageViewLoc != null) {
                                            imageViewLoc.setImageBitmap(thumbLocation);
                                        }

                                    }
                                } catch (Exception e) {
                                    FireCrash.log(e);
                                    if (Global.IS_DEV)
                                        e.printStackTrace();
                                }
                            } catch (Exception e) {
                                FireCrash.log(e);
                                if (Global.IS_DEV)
                                    e.printStackTrace();
                            }
                            if (txtViewReference != null) {
                                final TextView textView = txtViewReference.get();
                                if (textView != null) {
                                    String text = res[0] + " x " + res[1] +
                                            ". Size " + formattedSize + "\n" + indicatorGPS;
                                    textView.setText(text);
                                }
                            }
                        } else {
                            if (txtViewReference != null) {
                                final TextView textView = txtViewReference.get();
                                if (textView != null) {
                                    String text = res[0] + " x " + res[1] +
                                            ". Size " + formattedSize;
                                    textView.setText(text);
                                }
                            }
                        }
//                if (bitmap != null)
//                    bitmap.recycle();
                        Utility.freeMemory();
                    }
                });

            }
        }
    }

    public static SurveyHeaderBean getHeader() {
        return header;
    }

    public static void setHeader(SurveyHeaderBean header) {
        DynamicFormActivity.header = header;
    }
}