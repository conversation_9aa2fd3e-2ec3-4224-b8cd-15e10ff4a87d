package com.adins.mss.base.dynamicform.form.questions.viewholder;

import android.content.Context;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.AdapterView;
import android.widget.AutoCompleteTextView;
import android.widget.BaseAdapter;
import android.widget.Filter;
import android.widget.Filterable;
import android.widget.TextView;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.commons.Query;
import com.adins.mss.base.dynamicform.Constant;
import com.adins.mss.base.dynamicform.DynamicFormActivity;
import com.adins.mss.base.dynamicform.form.FragmentQuestion;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.Lookup;
import com.adins.mss.foundation.db.dataaccess.LookupDataAccess;
import com.adins.mss.foundation.db.dataaccess.POAssetDataAccess;
import com.adins.mss.foundation.db.dataaccess.PODataAccess;
import com.adins.mss.foundation.db.dataaccess.PODelaerDataAccess;
import com.adins.mss.foundation.formatter.Tool;
import com.adins.mss.foundation.questiongenerator.OptionAnswerBean;
import com.adins.mss.foundation.questiongenerator.QuestionBean;
import com.adins.mss.foundation.questiongenerator.form.QuestionView;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by gigin.ginanjar on 31/08/2016.
 */
public class TextWithSuggestionQuestionViewHolder extends RecyclerView.ViewHolder {
    public QuestionView mView;
    public TextView mQuestionLabel;
    public AutoCompleteTextView mQuestionAnswer;
    public QuestionBean bean;
    private Context mContext;
    private LookupArrayAdapter arrayAdapter;
    private List<OptionAnswerBean> options;

    @Deprecated
    public TextWithSuggestionQuestionViewHolder(View itemView) {
        super(itemView);
        mView = (QuestionView) itemView.findViewById(R.id.questionTwsLayout);
        mQuestionLabel  = (TextView) itemView.findViewById(R.id.questionTwsLabel);
        mQuestionAnswer = (AutoCompleteTextView) itemView.findViewById(R.id.questionTwsAnswer);
        mQuestionAnswer.requestFocus();
    }

    public TextWithSuggestionQuestionViewHolder(View itemView, Context context) {
        super(itemView);
        mView = (QuestionView) itemView.findViewById(R.id.questionTwsLayout);
        mQuestionLabel  = (TextView) itemView.findViewById(R.id.questionTwsLabel);
        mQuestionAnswer = (AutoCompleteTextView) itemView.findViewById(R.id.questionTwsAnswer);
        mQuestionAnswer.requestFocus();
        mContext = context;
    }

    public void bind(QuestionBean item, final int number) {
        bean = item;
        if (bean.getIs_mandatory().equals(Global.TRUE_STRING))
            mQuestionAnswer.setHint(mContext.getString(R.string.requiredField));
        else
            mQuestionAnswer.setHint("");

        String questionLabel = number + ". " + bean.getQuestion_label();
        mQuestionLabel.setText(questionLabel);

        if (null != bean.getAnswer() && !"".equalsIgnoreCase(bean.getAnswer())) {
            mQuestionAnswer.setText(bean.getAnswer());
            if(bean.getSelectedOptionAnswers().isEmpty()) {
                List<OptionAnswerBean> optionAnswers = getOptionsForQuestion(bean, bean.getAnswer());
                if (optionAnswers != null && optionAnswers.size() == 1) {
                    OptionAnswerBean optionBean = optionAnswers.get(0);
                    if (optionBean.getValue().equalsIgnoreCase(bean.getAnswer())) {
                        saveSelectedOptionToBean(optionBean);
                    }
                }
            }
        } else if (bean.getSelectedOptionAnswers() != null && bean.getSelectedOptionAnswers().size()>0 && StringUtils.isNotBlank(bean.getSelectedOptionAnswers().get(0).getValue())) {
            mQuestionAnswer.setText(bean.getSelectedOptionAnswers().get(0).getValue());
        } else { //Nendi: 23/01/2017 Add default answer '%'
//            bean.setAnswer("%");
            mQuestionAnswer.setText(null);
            mQuestionAnswer.requestFocus();
            InputMethodManager imm = (InputMethodManager)   mContext.getSystemService(Context.INPUT_METHOD_SERVICE);
            if (imm != null) imm.showSoftInput(mQuestionAnswer, InputMethodManager.SHOW_IMPLICIT);
        }

        InputFilter[] inputFilters = {new InputFilter.LengthFilter(bean.getMax_length())};
        mQuestionAnswer.setFilters(inputFilters);

//        SingleArrayAdapter adapter = new SingleArrayAdapter(mContext, bean);
        arrayAdapter = new LookupArrayAdapter(mContext, bean);
        mQuestionAnswer.setAdapter(arrayAdapter);
        mQuestionAnswer.setThreshold(1);
        mQuestionAnswer.setSingleLine();
        mQuestionAnswer.setDropDownBackgroundDrawable(ContextCompat.getDrawable(mContext, R.drawable.actionbar_background));

        mQuestionAnswer.addTextChangedListener(new TextWatcher() {
            String TempText = "";

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void beforeTextChanged(CharSequence s, int start, int count,
                                          int after) {
                // save answer to bean
                TempText = mQuestionAnswer.getText().toString().trim();
                if (bean.isReadOnly()) {
                    mQuestionAnswer.setKeyListener(null);
                    mQuestionAnswer.setCursorVisible(false);
                }
            }

            @Override
            public void afterTextChanged(Editable s) {
                bean.setAnswer(s.toString().trim());
                if (bean.isRelevanted()) {
                    String newText = mQuestionAnswer.getText().toString().trim();
                    if (!TempText.equals(newText)) {
                        mView.setChanged(true);
                        saveSelectedOptionToBean(null);
                    } else
                        mView.setChanged(false);
                }
            }
        });

        mQuestionAnswer.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                if (!bean.isReadOnly()) {
                    List<OptionAnswerBean> tempSelectedItems = bean.getSelectedOptionAnswers();
                    OptionAnswerBean newSelectedItem = arrayAdapter.getLookupItem(position);
                    if (tempSelectedItems != null && !tempSelectedItems.isEmpty()) {
                        if (!tempSelectedItems.get(0).getUuid_lookup().equals(newSelectedItem.getUuid_lookup())) {
                            mView.setChanged(true);
                            bean.setIsCanChange(true);
                            bean.setChange(true);
                        } else {
                            bean.setChange(false);
                        }
                    } else {
                        bean.setChange(false);
                    }
                    saveSelectedOptionToBean(newSelectedItem);
                } else {
                    bean.setChange(false);
                }
            }
        });

        if (bean.isReadOnly()) {
            mQuestionAnswer.setKeyListener(null);
            mQuestionAnswer.setCursorVisible(false);
            mQuestionAnswer.setEnabled(false);
        } else {
            mQuestionAnswer.setCursorVisible(true);
            mQuestionAnswer.setEnabled(true);
        }
    }

    private void saveSelectedOptionToBean(OptionAnswerBean option) {
//        option.setSelected(true);
        List<OptionAnswerBean> selectedOptionAnswers = new ArrayList<OptionAnswerBean>();
        if (option != null) {
            option.setSelected(true);

            if (option.getUuid_lookup() == null)
                option.setUuid_lookup(option.getOption_id());

            selectedOptionAnswers.add(option);
            bean.setLovCode(option.getCode());
            bean.setLookupId(option.getUuid_lookup());

            //Nendi: 20/02/2018 | Add Selected Product Offering
//            if (bean.getAnswer_type().equalsIgnoreCase(Global.AT_TEXT_WITH_SUGGESTION_NEW)){
//                Query query = FragmentQuestion.finalQuery(bean.getChoice_filter());
//                if (query.getTable().equalsIgnoreCase("MS_PO") && !option.getCode().equalsIgnoreCase("-")) {
//                    //TODO: Move to Background Process
//                    Constant.productOff = PODataAccess.getOne(mContext, Integer.parseInt(option.getOption_id()));
//                    Constant.poAsset    = POAssetDataAccess.find(mContext, query);
//                    bean.setRelevanted(true);
//                }
//            }
            bean.setAnswer(option.toString());
            bean.setSelectedOptionAnswers(selectedOptionAnswers);
        } else {
            bean.setLovCode(null);
            bean.setLookupId(null);
        }
    }

    /*class SingleArrayAdapter extends BaseAdapter implements Filterable {
        private static final int MAX_RESULTS = 10;
        private Context mContext;
        private QuestionBean bean;
        private List<String> resultList = new ArrayList<String>();

        public SingleArrayAdapter(Context context, QuestionBean bean) {
            this.mContext = context;
            this.bean = bean;
        }

        @Override
        public int getCount() {
            return resultList.size();
        }

        @Override
        public String getItem(int position) {
            return resultList.get(position);
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            if (convertView == null) {
                LayoutInflater inflater = (LayoutInflater) mContext
                        .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
                convertView = inflater.inflate(R.layout.autotext_list, parent, false);
            }
            ((TextView) convertView.findViewById(R.id.textauto)).setText(getItem(position));
            return convertView;
        }

        @Override
        public Filter getFilter() {
            Filter filter = new Filter() {
                @Override
                protected FilterResults performFiltering(CharSequence constraint) {
                    FilterResults filterResults = new FilterResults();
                    if (constraint != null) {
                        List<String> books = LookupDataAccess.getAllCodeByFilter(mContext, bean.getLov_group(), constraint.toString(), MAX_RESULTS);

                        // Assign the data to the FilterResults
                        filterResults.values = books;
                        filterResults.count = books.size();
                    }
                    return filterResults;
                }

                @Override
                protected void publishResults(CharSequence constraint, FilterResults results) {
                    if (results != null && results.count > 0) {
                        resultList = (List<String>) results.values;
                        notifyDataSetChanged();
                    } else {
                        notifyDataSetInvalidated();
                    }
                }
            };
            return filter;
        }
    }*/

    private List<OptionAnswerBean> GetLookupFromDBTextWithSuggestion(Context context, QuestionBean bean, List<String> filters, String dynamicFilter) {
        List<OptionAnswerBean> optionAnswers = new ArrayList<OptionAnswerBean>();
        if (!filters.isEmpty()) {
            if (filters.size() == 1) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilterTextWithSuggestion(context, bean.getLov_group(), filters.get(0), dynamicFilter);
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            } else if (filters.size() == 2) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilterTextWithSuggestion(context, bean.getLov_group(), filters.get(0), filters.get(1), dynamicFilter);
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            } else if (filters.size() == 3) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilterTextWithSuggestion(context, bean.getLov_group(), filters.get(0), filters.get(1), filters.get(2), dynamicFilter);
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            } else if (filters.size() == 4) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilterTextWithSuggestion(context, bean.getLov_group(), filters.get(0), filters.get(1), filters.get(2), filters.get(3), dynamicFilter);
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            } else if (filters.size() == 5) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilterTextWithSuggestion(context, bean.getLov_group(), filters.get(0), filters.get(1), filters.get(2), filters.get(3), filters.get(4), dynamicFilter);
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            }

        } else {
            if (bean.getChoice_filter() != null && bean.getChoice_filter().length() > 0) {
                List<Lookup> lookups = new ArrayList<Lookup>();
                optionAnswers = OptionAnswerBean.getOptionList(lookups);
            } else {
                String lovGroup = bean.getLov_group();
                if (lovGroup != null) {
                    List<Lookup> lookups = LookupDataAccess.getAllByLovGroupTextWithSuggestion(context, lovGroup, dynamicFilter);
                    if (lookups != null)
                        optionAnswers = OptionAnswerBean.getOptionList(lookups);
                }
            }
        }
        return optionAnswers;
    }

    private List<OptionAnswerBean> GetLookupFromDB(Context context, QuestionBean bean, List<String> filters) {
        List<OptionAnswerBean> optionAnswers = new ArrayList<OptionAnswerBean>();
        if (!filters.isEmpty()) {
            if (filters.size() == 1) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilter(context, bean.getLov_group(), filters.get(0));
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            } else if (filters.size() == 2) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilter(context, bean.getLov_group(), filters.get(0), filters.get(1));
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            } else if (filters.size() == 3) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilter(context, bean.getLov_group(), filters.get(0), filters.get(1), filters.get(2));
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            } else if (filters.size() == 4) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilter(context, bean.getLov_group(), filters.get(0), filters.get(1), filters.get(2), filters.get(3));
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            } else if (filters.size() == 5) {
                List<Lookup> nLookups = LookupDataAccess.getAllByFilter(context, bean.getLov_group(), filters.get(0), filters.get(1), filters.get(2), filters.get(3), filters.get(4));
                optionAnswers = OptionAnswerBean.getOptionList(nLookups);
            }
        } else {
            if (bean.getChoice_filter() != null && bean.getChoice_filter().length() > 0) {
                if (bean.getAnswer_type().equalsIgnoreCase(Global.AT_TEXT_WITH_SUGGESTION_NEW)) {
                    Query query  = FragmentQuestion.finalQuery(bean.getChoice_filter());
                    if (query.getTable().equalsIgnoreCase("MS_PO")) {
                        optionAnswers.addAll(PODataAccess.lookup(mContext, query, bean.getLov_group()));
                    } else if (query.getTable().equalsIgnoreCase("MS_PO_DEALER")) {
                        optionAnswers.addAll(PODelaerDataAccess.lookup(mContext, query, bean.getLov_group()));
                    } else {
                        optionAnswers.addAll(POAssetDataAccess.lookup(mContext, query, bean.getLov_group()));
                    }
                } else {
                    List<Lookup> lookups = new ArrayList<Lookup>();
                    optionAnswers = OptionAnswerBean.getOptionList(lookups);
                }
            } else {
                String lovGroup = bean.getLov_group();
                if (lovGroup != null) {
                    List<Lookup> lookups = LookupDataAccess.getAllByLovGroup(context, lovGroup);
                    if (lookups != null)
                        optionAnswers = OptionAnswerBean.getOptionList(lookups);
                }
            }
        }
        return optionAnswers;
    }

    protected List<OptionAnswerBean> getOptionsForQuestion(QuestionBean bean, String dynamicFilter) {
        List<String> filters = new ArrayList<String>();
        if (bean.getChoice_filter() != null && !bean.getAnswer_type().equalsIgnoreCase(Global.AT_TEXT_WITH_SUGGESTION_NEW)) {
            String[] tempfilters = Tool.split(bean.getChoice_filter(), Global.DELIMETER_DATA3);

            for (String newFilter : tempfilters) {
                int idxOfOpenBrace = newFilter.indexOf('{');
                if (idxOfOpenBrace != -1) {                    //there's {, prepare to replace what inside the {}
                    int idxOfCloseBrace = newFilter.indexOf('}');
                    String tempIdentifier = newFilter.substring(idxOfOpenBrace + 1, idxOfCloseBrace).toUpperCase();
                    if (tempIdentifier.contains("%")) {
                        filters.add(tempIdentifier);
                    } else {
                        int idxOfOpenAbs = tempIdentifier.indexOf("$");
                        if (idxOfOpenAbs != -1) {
                            String finalIdentifier = tempIdentifier.substring(idxOfOpenAbs + 1);
                            if (finalIdentifier.equals(Global.IDF_LOGIN_ID)) {
                                String loginId = GlobalData.getSharedGlobalData().getUser().getLogin_id();
                                int idxOfOpenAt = loginId.indexOf('@');
                                if (idxOfOpenAt != -1) {
                                    loginId = loginId.substring(0, idxOfOpenAt);
                                }
                                filters.add(loginId);
                            } else if (finalIdentifier.equals(Global.IDF_BRANCH_ID)) {
                                String branchId = GlobalData.getSharedGlobalData().getUser().getBranch_id();
                                filters.add(branchId);
                            } else if (finalIdentifier.equals(Global.IDF_BRANCH_NAME)) {
                                String branchName = GlobalData.getSharedGlobalData().getUser().getBranch_name();
                                filters.add(branchName);
                            } else if (finalIdentifier.equals(Global.IDF_UUID_USER)) {
                                String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
                                filters.add(uuidUser);
                            } else if (finalIdentifier.equals(Global.IDF_JOB)) {
                                String job = GlobalData.getSharedGlobalData().getUser().getFlag_job();
                                filters.add(job);
                            } else if (finalIdentifier.equals(Global.IDF_DEALER_NAME)) {
                                String dealerName = GlobalData.getSharedGlobalData().getUser().getDealer_name();
                                filters.add(dealerName);
                            } else if (finalIdentifier.equals(Global.IDF_UUID_BRANCH)) {
                                String uuidBranch = GlobalData.getSharedGlobalData().getUser().getUuid_branch();
                                filters.add(uuidBranch);
                            } else if (finalIdentifier.equals(Global.IDF_DEALER_ID)) {
                                String dealerId = GlobalData.getSharedGlobalData().getUser().getUuid_dealer();
                                filters.add(dealerId);
                            } else if (finalIdentifier.equals(Global.IDF_BRANCH_TYPE)) {
                                String branchType = GlobalData.getSharedGlobalData().getUser().getBranch_type();
                                filters.add(branchType);
                            } else if (finalIdentifier.equals(Global.IDF_TASK_ID)) {
                                String taskId = DynamicFormActivity.header.getTask_id();
                                filters.add(taskId);
                            }
                        } else {
                            QuestionBean bean2 = Constant.listOfQuestion.get(tempIdentifier);
                            if (bean2 != null) {
                                if (Global.AT_TEXT_WITH_SUGGESTION.equals(bean2.getAnswer_type())) {
                                    filters.add(bean2.getLovCode());
                                } else {
                                    for (OptionAnswerBean answerBean : bean2.getSelectedOptionAnswers()) {
                                        filters.add(answerBean.getCode());
                                    }
                                }
                                bean2.setRelevanted(true);
                            }
                        }
                    }
                }
            }
        }

        List<OptionAnswerBean> optionAnswers = new ArrayList<OptionAnswerBean>();
        if (dynamicFilter != null) {
            if (bean.getAnswer_type().equalsIgnoreCase(Global.AT_TEXT_WITH_SUGGESTION_NEW)) {
                Query query  = FragmentQuestion.finalQuery(bean.getChoice_filter());
                if (query.getTable().equalsIgnoreCase("MS_PO")) {
                    optionAnswers.addAll(PODataAccess.lookup(mContext, query, dynamicFilter));
                } else if (query.getTable().equalsIgnoreCase("MS_PO_DEALER")) {
                    optionAnswers.addAll(PODelaerDataAccess.lookup(mContext, query, dynamicFilter));
                } else {
                    optionAnswers.addAll(POAssetDataAccess.lookup(mContext, query, dynamicFilter));
                }
            } else optionAnswers = GetLookupFromDBTextWithSuggestion(mContext, bean, filters, dynamicFilter);
        } else {
            optionAnswers = GetLookupFromDB(mContext, bean, filters);
        }
        return optionAnswers;
    }

    class LookupArrayAdapter extends BaseAdapter implements Filterable {
        private static final int MAX_RESULTS = 10;
        private Context mContext;
        private QuestionBean bean;
        private List<OptionAnswerBean> resultList = new ArrayList<>();

        public LookupArrayAdapter(Context context, QuestionBean bean) {
            this.mContext = context;
            this.bean = bean;
        }

        @Override
        public int getCount() {
            return resultList.size();
        }

        @Override
        public String getItem(int position) {
            return resultList.get(position).toString();
        }

        public OptionAnswerBean getLookupItem(int position) {
            return resultList.get(position);
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            AutoTextViewHolder holder;
            if (convertView == null) {
                LayoutInflater inflater = (LayoutInflater) mContext
                        .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
                convertView = inflater.inflate(R.layout.autotext_list, parent, false);
            }
            holder = new AutoTextViewHolder(convertView);
            holder.bind(getLookupItem(position));
            return convertView;
        }

        @Override
        public Filter getFilter() {
            Filter filter = new Filter() {
                @Override
                protected FilterResults performFiltering(CharSequence constraint) {
                    FilterResults filterResults = new FilterResults();
                    if (constraint != null) {
//                        List<OptionAnswerBean> beanList = OptionAnswerBean.getOptionList(LookupDataAccess.getAllByLovGroupWithFilter(mContext, bean.getLov_group(), constraint.toString(), MAX_RESULTS));
                        List<OptionAnswerBean> beanList = getOptionsForQuestion(bean, constraint.toString().trim());
                        // Assign the data to the FilterResults
                        filterResults.values = beanList;
                        filterResults.count  = beanList.size();
                    }
                    return filterResults;
                }

                @Override
                protected void publishResults(CharSequence constraint, FilterResults results) {
                    if (results != null && results.count > 0) {
                        resultList = (List<OptionAnswerBean>) results.values;
                        options = resultList;
                        notifyDataSetChanged();
                    } else {
                        notifyDataSetInvalidated();
                    }
                }
            };
            return filter;
        }
    }

    public class AutoTextViewHolder extends RecyclerView.ViewHolder {
        public View mView;
        public TextView textView;
        public OptionAnswerBean bean;

        public AutoTextViewHolder(View itemView) {
            super(itemView);
            mView = itemView;
            textView = (TextView) itemView.findViewById(R.id.textauto);
        }

        public void bind(OptionAnswerBean optBean) {
            bean = optBean;
            textView.setText(bean.getValue());
        }
    }

    public static void resetRelevanted(String tmpQuery) {
        Query query = GsonHelper.fromJson(tmpQuery, Query.class);
        //Validate Constraint
        if (query.getConstraint() != null) {
            int size = query.getConstraint().size();
            if (size != 0) {
                for (Query.Constraint constraint : query.getConstraint()) {
                    String[] params;
                    if (constraint.getValue().contains("[")) {
                        params  = new String[] {"[","]"};
                        String identifier = FragmentQuestion.getIdentifier(params, constraint.getValue());
                        QuestionBean bean = Constant.listOfQuestion.get(identifier);
                        bean.setChange(true);
//                        QuestionBean.resetAnswer(bean);
                    }
                }
            }
        }
    }
}
