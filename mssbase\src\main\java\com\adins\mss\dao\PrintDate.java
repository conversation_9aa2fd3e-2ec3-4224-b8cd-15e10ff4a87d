package com.adins.mss.dao;

import com.adins.mss.base.util.ExcludeFromGson;
import com.google.gson.annotations.SerializedName;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT. Enable "keep" sections if you want to edit. 
/**
 * Entity mapped to table "TR_PRINTDATE".
 */
public class PrintDate {

    /** Not-null value. */
     @SerializedName("dtm_print")
    private java.util.Date dtm_print;
     @SerializedName("uuid_task_h")
    private String uuid_task_h;

    public PrintDate() {
    }

    public PrintDate(java.util.Date dtm_print) {
        this.dtm_print = dtm_print;
    }

    public PrintDate(java.util.Date dtm_print, String uuid_task_h) {
        this.dtm_print = dtm_print;
        this.uuid_task_h = uuid_task_h;
    }

    /** Not-null value. */
    public java.util.Date getDtm_print() {
        return dtm_print;
    }

    /** Not-null value; ensure this value is available before it is saved to the database. */
    public void setDtm_print(java.util.Date dtm_print) {
        this.dtm_print = dtm_print;
    }

    public String getUuid_task_h() {
        return uuid_task_h;
    }

    public void setUuid_task_h(String uuid_task_h) {
        this.uuid_task_h = uuid_task_h;
    }

}
