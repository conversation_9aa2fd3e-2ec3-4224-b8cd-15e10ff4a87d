package com.adins.mss.dao;

import java.util.Map;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.AbstractDaoSession;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.identityscope.IdentityScopeType;
import de.greenrobot.dao.internal.DaoConfig;

import com.adins.mss.dao.CollectionActivity;
import com.adins.mss.dao.InstallmentSchedule;
import com.adins.mss.dao.GeneralParameter;
import com.adins.mss.dao.GroupUser;
import com.adins.mss.dao.Lookup;
import com.adins.mss.dao.Sync;
import com.adins.mss.dao.Migration;
import com.adins.mss.dao.Rule;
import com.adins.mss.dao.Blacklist;
import com.adins.mss.dao.ProductOffering;
import com.adins.mss.dao.PODealer;
import com.adins.mss.dao.AssetScheme;
import com.adins.mss.dao.POAsset;
import com.adins.mss.dao.Industry;
import com.adins.mss.dao.Decision;
import com.adins.mss.dao.MarketPrice;
import com.adins.mss.dao.Menu;
import com.adins.mss.dao.PrintItem;
import com.adins.mss.dao.QuestionSet;
import com.adins.mss.dao.Scheme;
import com.adins.mss.dao.TimelineType;
import com.adins.mss.dao.User;
import com.adins.mss.dao.PushSync;
import com.adins.mss.dao.Logger;
import com.adins.mss.dao.CollectionHistory;
import com.adins.mss.dao.Comment;
import com.adins.mss.dao.DepositReportD;
import com.adins.mss.dao.DepositReportH;
import com.adins.mss.dao.ImageResult;
import com.adins.mss.dao.LocationInfo;
import com.adins.mss.dao.Message;
import com.adins.mss.dao.PaymentHistoryD;
import com.adins.mss.dao.PaymentHistoryH;
import com.adins.mss.dao.PrintResult;
import com.adins.mss.dao.ReceiptVoucher;
import com.adins.mss.dao.TaskD;
import com.adins.mss.dao.TaskH;
import com.adins.mss.dao.TaskHSequence;
import com.adins.mss.dao.TaskUpdate;
import com.adins.mss.dao.Timeline;
import com.adins.mss.dao.MobileContentD;
import com.adins.mss.dao.MobileContentH;
import com.adins.mss.dao.Holiday;
import com.adins.mss.dao.PrintDate;
import com.adins.mss.dao.ErrorLog;
import com.adins.mss.dao.mobiledatafile;
import com.adins.mss.dao.ReminderPo;
import com.adins.mss.dao.EmbeddedInfo;

import com.adins.mss.dao.CollectionActivityDao;
import com.adins.mss.dao.InstallmentScheduleDao;
import com.adins.mss.dao.GeneralParameterDao;
import com.adins.mss.dao.GroupUserDao;
import com.adins.mss.dao.LookupDao;
import com.adins.mss.dao.SyncDao;
import com.adins.mss.dao.MigrationDao;
import com.adins.mss.dao.RuleDao;
import com.adins.mss.dao.BlacklistDao;
import com.adins.mss.dao.ProductOfferingDao;
import com.adins.mss.dao.PODealerDao;
import com.adins.mss.dao.AssetSchemeDao;
import com.adins.mss.dao.POAssetDao;
import com.adins.mss.dao.IndustryDao;
import com.adins.mss.dao.DecisionDao;
import com.adins.mss.dao.MarketPriceDao;
import com.adins.mss.dao.MenuDao;
import com.adins.mss.dao.PrintItemDao;
import com.adins.mss.dao.QuestionSetDao;
import com.adins.mss.dao.SchemeDao;
import com.adins.mss.dao.TimelineTypeDao;
import com.adins.mss.dao.UserDao;
import com.adins.mss.dao.PushSyncDao;
import com.adins.mss.dao.LoggerDao;
import com.adins.mss.dao.CollectionHistoryDao;
import com.adins.mss.dao.CommentDao;
import com.adins.mss.dao.DepositReportDDao;
import com.adins.mss.dao.DepositReportHDao;
import com.adins.mss.dao.ImageResultDao;
import com.adins.mss.dao.LocationInfoDao;
import com.adins.mss.dao.MessageDao;
import com.adins.mss.dao.PaymentHistoryDDao;
import com.adins.mss.dao.PaymentHistoryHDao;
import com.adins.mss.dao.PrintResultDao;
import com.adins.mss.dao.ReceiptVoucherDao;
import com.adins.mss.dao.TaskDDao;
import com.adins.mss.dao.TaskHDao;
import com.adins.mss.dao.TaskHSequenceDao;
import com.adins.mss.dao.TaskUpdateDao;
import com.adins.mss.dao.TimelineDao;
import com.adins.mss.dao.MobileContentDDao;
import com.adins.mss.dao.MobileContentHDao;
import com.adins.mss.dao.HolidayDao;
import com.adins.mss.dao.PrintDateDao;
import com.adins.mss.dao.ErrorLogDao;
import com.adins.mss.dao.mobiledatafileDao;
import com.adins.mss.dao.ReminderPoDao;
import com.adins.mss.dao.EmbeddedInfoDao;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.

/**
 * {@inheritDoc}
 * 
 * @see de.greenrobot.dao.AbstractDaoSession
 */
public class DaoSession extends AbstractDaoSession {

    private final DaoConfig collectionActivityDaoConfig;
    private final DaoConfig installmentScheduleDaoConfig;
    private final DaoConfig generalParameterDaoConfig;
    private final DaoConfig groupUserDaoConfig;
    private final DaoConfig lookupDaoConfig;
    private final DaoConfig syncDaoConfig;
    private final DaoConfig migrationDaoConfig;
    private final DaoConfig ruleDaoConfig;
    private final DaoConfig blacklistDaoConfig;
    private final DaoConfig productOfferingDaoConfig;
    private final DaoConfig pODealerDaoConfig;
    private final DaoConfig assetSchemeDaoConfig;
    private final DaoConfig pOAssetDaoConfig;
    private final DaoConfig industryDaoConfig;
    private final DaoConfig decisionDaoConfig;
    private final DaoConfig marketPriceDaoConfig;
    private final DaoConfig menuDaoConfig;
    private final DaoConfig printItemDaoConfig;
    private final DaoConfig questionSetDaoConfig;
    private final DaoConfig schemeDaoConfig;
    private final DaoConfig timelineTypeDaoConfig;
    private final DaoConfig userDaoConfig;
    private final DaoConfig pushSyncDaoConfig;
    private final DaoConfig loggerDaoConfig;
    private final DaoConfig collectionHistoryDaoConfig;
    private final DaoConfig commentDaoConfig;
    private final DaoConfig depositReportDDaoConfig;
    private final DaoConfig depositReportHDaoConfig;
    private final DaoConfig imageResultDaoConfig;
    private final DaoConfig locationInfoDaoConfig;
    private final DaoConfig messageDaoConfig;
    private final DaoConfig paymentHistoryDDaoConfig;
    private final DaoConfig paymentHistoryHDaoConfig;
    private final DaoConfig printResultDaoConfig;
    private final DaoConfig receiptVoucherDaoConfig;
    private final DaoConfig taskDDaoConfig;
    private final DaoConfig taskHDaoConfig;
    private final DaoConfig taskHSequenceDaoConfig;
    private final DaoConfig taskUpdateDaoConfig;
    private final DaoConfig timelineDaoConfig;
    private final DaoConfig mobileContentDDaoConfig;
    private final DaoConfig mobileContentHDaoConfig;
    private final DaoConfig holidayDaoConfig;
    private final DaoConfig printDateDaoConfig;
    private final DaoConfig errorLogDaoConfig;
    private final DaoConfig mobiledatafileDaoConfig;
    private final DaoConfig reminderPoDaoConfig;
    private final DaoConfig embeddedInfoDaoConfig;

    private final CollectionActivityDao collectionActivityDao;
    private final InstallmentScheduleDao installmentScheduleDao;
    private final GeneralParameterDao generalParameterDao;
    private final GroupUserDao groupUserDao;
    private final LookupDao lookupDao;
    private final SyncDao syncDao;
    private final MigrationDao migrationDao;
    private final RuleDao ruleDao;
    private final BlacklistDao blacklistDao;
    private final ProductOfferingDao productOfferingDao;
    private final PODealerDao pODealerDao;
    private final AssetSchemeDao assetSchemeDao;
    private final POAssetDao pOAssetDao;
    private final IndustryDao industryDao;
    private final DecisionDao decisionDao;
    private final MarketPriceDao marketPriceDao;
    private final MenuDao menuDao;
    private final PrintItemDao printItemDao;
    private final QuestionSetDao questionSetDao;
    private final SchemeDao schemeDao;
    private final TimelineTypeDao timelineTypeDao;
    private final UserDao userDao;
    private final PushSyncDao pushSyncDao;
    private final LoggerDao loggerDao;
    private final CollectionHistoryDao collectionHistoryDao;
    private final CommentDao commentDao;
    private final DepositReportDDao depositReportDDao;
    private final DepositReportHDao depositReportHDao;
    private final ImageResultDao imageResultDao;
    private final LocationInfoDao locationInfoDao;
    private final MessageDao messageDao;
    private final PaymentHistoryDDao paymentHistoryDDao;
    private final PaymentHistoryHDao paymentHistoryHDao;
    private final PrintResultDao printResultDao;
    private final ReceiptVoucherDao receiptVoucherDao;
    private final TaskDDao taskDDao;
    private final TaskHDao taskHDao;
    private final TaskHSequenceDao taskHSequenceDao;
    private final TaskUpdateDao taskUpdateDao;
    private final TimelineDao timelineDao;
    private final MobileContentDDao mobileContentDDao;
    private final MobileContentHDao mobileContentHDao;
    private final HolidayDao holidayDao;
    private final PrintDateDao printDateDao;
    private final ErrorLogDao errorLogDao;
    private final mobiledatafileDao mobiledatafileDao;
    private final ReminderPoDao reminderPoDao;
    private final EmbeddedInfoDao embeddedInfoDao;

    public DaoSession(Database db, IdentityScopeType type, Map<Class<? extends AbstractDao<?, ?>>, DaoConfig>
            daoConfigMap) {
        super(db);

        collectionActivityDaoConfig = daoConfigMap.get(CollectionActivityDao.class).clone();
        collectionActivityDaoConfig.initIdentityScope(type);

        installmentScheduleDaoConfig = daoConfigMap.get(InstallmentScheduleDao.class).clone();
        installmentScheduleDaoConfig.initIdentityScope(type);

        generalParameterDaoConfig = daoConfigMap.get(GeneralParameterDao.class).clone();
        generalParameterDaoConfig.initIdentityScope(type);

        groupUserDaoConfig = daoConfigMap.get(GroupUserDao.class).clone();
        groupUserDaoConfig.initIdentityScope(type);

        lookupDaoConfig = daoConfigMap.get(LookupDao.class).clone();
        lookupDaoConfig.initIdentityScope(type);

        syncDaoConfig = daoConfigMap.get(SyncDao.class).clone();
        syncDaoConfig.initIdentityScope(type);

        migrationDaoConfig = daoConfigMap.get(MigrationDao.class).clone();
        migrationDaoConfig.initIdentityScope(type);

        ruleDaoConfig = daoConfigMap.get(RuleDao.class).clone();
        ruleDaoConfig.initIdentityScope(type);

        blacklistDaoConfig = daoConfigMap.get(BlacklistDao.class).clone();
        blacklistDaoConfig.initIdentityScope(type);

        productOfferingDaoConfig = daoConfigMap.get(ProductOfferingDao.class).clone();
        productOfferingDaoConfig.initIdentityScope(type);

        pODealerDaoConfig = daoConfigMap.get(PODealerDao.class).clone();
        pODealerDaoConfig.initIdentityScope(type);

        assetSchemeDaoConfig = daoConfigMap.get(AssetSchemeDao.class).clone();
        assetSchemeDaoConfig.initIdentityScope(type);

        pOAssetDaoConfig = daoConfigMap.get(POAssetDao.class).clone();
        pOAssetDaoConfig.initIdentityScope(type);

        industryDaoConfig = daoConfigMap.get(IndustryDao.class).clone();
        industryDaoConfig.initIdentityScope(type);

        decisionDaoConfig = daoConfigMap.get(DecisionDao.class).clone();
        decisionDaoConfig.initIdentityScope(type);

        marketPriceDaoConfig = daoConfigMap.get(MarketPriceDao.class).clone();
        marketPriceDaoConfig.initIdentityScope(type);

        menuDaoConfig = daoConfigMap.get(MenuDao.class).clone();
        menuDaoConfig.initIdentityScope(type);

        printItemDaoConfig = daoConfigMap.get(PrintItemDao.class).clone();
        printItemDaoConfig.initIdentityScope(type);

        questionSetDaoConfig = daoConfigMap.get(QuestionSetDao.class).clone();
        questionSetDaoConfig.initIdentityScope(type);

        schemeDaoConfig = daoConfigMap.get(SchemeDao.class).clone();
        schemeDaoConfig.initIdentityScope(type);

        timelineTypeDaoConfig = daoConfigMap.get(TimelineTypeDao.class).clone();
        timelineTypeDaoConfig.initIdentityScope(type);

        userDaoConfig = daoConfigMap.get(UserDao.class).clone();
        userDaoConfig.initIdentityScope(type);

        pushSyncDaoConfig = daoConfigMap.get(PushSyncDao.class).clone();
        pushSyncDaoConfig.initIdentityScope(type);

        loggerDaoConfig = daoConfigMap.get(LoggerDao.class).clone();
        loggerDaoConfig.initIdentityScope(type);

        collectionHistoryDaoConfig = daoConfigMap.get(CollectionHistoryDao.class).clone();
        collectionHistoryDaoConfig.initIdentityScope(type);

        commentDaoConfig = daoConfigMap.get(CommentDao.class).clone();
        commentDaoConfig.initIdentityScope(type);

        depositReportDDaoConfig = daoConfigMap.get(DepositReportDDao.class).clone();
        depositReportDDaoConfig.initIdentityScope(type);

        depositReportHDaoConfig = daoConfigMap.get(DepositReportHDao.class).clone();
        depositReportHDaoConfig.initIdentityScope(type);

        imageResultDaoConfig = daoConfigMap.get(ImageResultDao.class).clone();
        imageResultDaoConfig.initIdentityScope(type);

        locationInfoDaoConfig = daoConfigMap.get(LocationInfoDao.class).clone();
        locationInfoDaoConfig.initIdentityScope(type);

        messageDaoConfig = daoConfigMap.get(MessageDao.class).clone();
        messageDaoConfig.initIdentityScope(type);

        paymentHistoryDDaoConfig = daoConfigMap.get(PaymentHistoryDDao.class).clone();
        paymentHistoryDDaoConfig.initIdentityScope(type);

        paymentHistoryHDaoConfig = daoConfigMap.get(PaymentHistoryHDao.class).clone();
        paymentHistoryHDaoConfig.initIdentityScope(type);

        printResultDaoConfig = daoConfigMap.get(PrintResultDao.class).clone();
        printResultDaoConfig.initIdentityScope(type);

        receiptVoucherDaoConfig = daoConfigMap.get(ReceiptVoucherDao.class).clone();
        receiptVoucherDaoConfig.initIdentityScope(type);

        taskDDaoConfig = daoConfigMap.get(TaskDDao.class).clone();
        taskDDaoConfig.initIdentityScope(type);

        taskHDaoConfig = daoConfigMap.get(TaskHDao.class).clone();
        taskHDaoConfig.initIdentityScope(type);

        taskHSequenceDaoConfig = daoConfigMap.get(TaskHSequenceDao.class).clone();
        taskHSequenceDaoConfig.initIdentityScope(type);

        taskUpdateDaoConfig = daoConfigMap.get(TaskUpdateDao.class).clone();
        taskUpdateDaoConfig.initIdentityScope(type);

        timelineDaoConfig = daoConfigMap.get(TimelineDao.class).clone();
        timelineDaoConfig.initIdentityScope(type);

        mobileContentDDaoConfig = daoConfigMap.get(MobileContentDDao.class).clone();
        mobileContentDDaoConfig.initIdentityScope(type);

        mobileContentHDaoConfig = daoConfigMap.get(MobileContentHDao.class).clone();
        mobileContentHDaoConfig.initIdentityScope(type);

        holidayDaoConfig = daoConfigMap.get(HolidayDao.class).clone();
        holidayDaoConfig.initIdentityScope(type);

        printDateDaoConfig = daoConfigMap.get(PrintDateDao.class).clone();
        printDateDaoConfig.initIdentityScope(type);

        errorLogDaoConfig = daoConfigMap.get(ErrorLogDao.class).clone();
        errorLogDaoConfig.initIdentityScope(type);

        mobiledatafileDaoConfig = daoConfigMap.get(mobiledatafileDao.class).clone();
        mobiledatafileDaoConfig.initIdentityScope(type);

        reminderPoDaoConfig = daoConfigMap.get(ReminderPoDao.class).clone();
        reminderPoDaoConfig.initIdentityScope(type);

        embeddedInfoDaoConfig = daoConfigMap.get(EmbeddedInfoDao.class).clone();
        embeddedInfoDaoConfig.initIdentityScope(type);

        collectionActivityDao = new CollectionActivityDao(collectionActivityDaoConfig, this);
        installmentScheduleDao = new InstallmentScheduleDao(installmentScheduleDaoConfig, this);
        generalParameterDao = new GeneralParameterDao(generalParameterDaoConfig, this);
        groupUserDao = new GroupUserDao(groupUserDaoConfig, this);
        lookupDao = new LookupDao(lookupDaoConfig, this);
        syncDao = new SyncDao(syncDaoConfig, this);
        migrationDao = new MigrationDao(migrationDaoConfig, this);
        ruleDao = new RuleDao(ruleDaoConfig, this);
        blacklistDao = new BlacklistDao(blacklistDaoConfig, this);
        productOfferingDao = new ProductOfferingDao(productOfferingDaoConfig, this);
        pODealerDao = new PODealerDao(pODealerDaoConfig, this);
        assetSchemeDao = new AssetSchemeDao(assetSchemeDaoConfig, this);
        pOAssetDao = new POAssetDao(pOAssetDaoConfig, this);
        industryDao = new IndustryDao(industryDaoConfig, this);
        decisionDao = new DecisionDao(decisionDaoConfig, this);
        marketPriceDao = new MarketPriceDao(marketPriceDaoConfig, this);
        menuDao = new MenuDao(menuDaoConfig, this);
        printItemDao = new PrintItemDao(printItemDaoConfig, this);
        questionSetDao = new QuestionSetDao(questionSetDaoConfig, this);
        schemeDao = new SchemeDao(schemeDaoConfig, this);
        timelineTypeDao = new TimelineTypeDao(timelineTypeDaoConfig, this);
        userDao = new UserDao(userDaoConfig, this);
        pushSyncDao = new PushSyncDao(pushSyncDaoConfig, this);
        loggerDao = new LoggerDao(loggerDaoConfig, this);
        collectionHistoryDao = new CollectionHistoryDao(collectionHistoryDaoConfig, this);
        commentDao = new CommentDao(commentDaoConfig, this);
        depositReportDDao = new DepositReportDDao(depositReportDDaoConfig, this);
        depositReportHDao = new DepositReportHDao(depositReportHDaoConfig, this);
        imageResultDao = new ImageResultDao(imageResultDaoConfig, this);
        locationInfoDao = new LocationInfoDao(locationInfoDaoConfig, this);
        messageDao = new MessageDao(messageDaoConfig, this);
        paymentHistoryDDao = new PaymentHistoryDDao(paymentHistoryDDaoConfig, this);
        paymentHistoryHDao = new PaymentHistoryHDao(paymentHistoryHDaoConfig, this);
        printResultDao = new PrintResultDao(printResultDaoConfig, this);
        receiptVoucherDao = new ReceiptVoucherDao(receiptVoucherDaoConfig, this);
        taskDDao = new TaskDDao(taskDDaoConfig, this);
        taskHDao = new TaskHDao(taskHDaoConfig, this);
        taskHSequenceDao = new TaskHSequenceDao(taskHSequenceDaoConfig, this);
        taskUpdateDao = new TaskUpdateDao(taskUpdateDaoConfig, this);
        timelineDao = new TimelineDao(timelineDaoConfig, this);
        mobileContentDDao = new MobileContentDDao(mobileContentDDaoConfig, this);
        mobileContentHDao = new MobileContentHDao(mobileContentHDaoConfig, this);
        holidayDao = new HolidayDao(holidayDaoConfig, this);
        printDateDao = new PrintDateDao(printDateDaoConfig, this);
        errorLogDao = new ErrorLogDao(errorLogDaoConfig, this);
        mobiledatafileDao = new mobiledatafileDao(mobiledatafileDaoConfig, this);
        reminderPoDao = new ReminderPoDao(reminderPoDaoConfig, this);
        embeddedInfoDao = new EmbeddedInfoDao(embeddedInfoDaoConfig, this);

        registerDao(CollectionActivity.class, collectionActivityDao);
        registerDao(InstallmentSchedule.class, installmentScheduleDao);
        registerDao(GeneralParameter.class, generalParameterDao);
        registerDao(GroupUser.class, groupUserDao);
        registerDao(Lookup.class, lookupDao);
        registerDao(Sync.class, syncDao);
        registerDao(Migration.class, migrationDao);
        registerDao(Rule.class, ruleDao);
        registerDao(Blacklist.class, blacklistDao);
        registerDao(ProductOffering.class, productOfferingDao);
        registerDao(PODealer.class, pODealerDao);
        registerDao(AssetScheme.class, assetSchemeDao);
        registerDao(POAsset.class, pOAssetDao);
        registerDao(Industry.class, industryDao);
        registerDao(Decision.class, decisionDao);
        registerDao(MarketPrice.class, marketPriceDao);
        registerDao(Menu.class, menuDao);
        registerDao(PrintItem.class, printItemDao);
        registerDao(QuestionSet.class, questionSetDao);
        registerDao(Scheme.class, schemeDao);
        registerDao(TimelineType.class, timelineTypeDao);
        registerDao(User.class, userDao);
        registerDao(PushSync.class, pushSyncDao);
        registerDao(Logger.class, loggerDao);
        registerDao(CollectionHistory.class, collectionHistoryDao);
        registerDao(Comment.class, commentDao);
        registerDao(DepositReportD.class, depositReportDDao);
        registerDao(DepositReportH.class, depositReportHDao);
        registerDao(ImageResult.class, imageResultDao);
        registerDao(LocationInfo.class, locationInfoDao);
        registerDao(Message.class, messageDao);
        registerDao(PaymentHistoryD.class, paymentHistoryDDao);
        registerDao(PaymentHistoryH.class, paymentHistoryHDao);
        registerDao(PrintResult.class, printResultDao);
        registerDao(ReceiptVoucher.class, receiptVoucherDao);
        registerDao(TaskD.class, taskDDao);
        registerDao(TaskH.class, taskHDao);
        registerDao(TaskHSequence.class, taskHSequenceDao);
        registerDao(TaskUpdate.class, taskUpdateDao);
        registerDao(Timeline.class, timelineDao);
        registerDao(MobileContentD.class, mobileContentDDao);
        registerDao(MobileContentH.class, mobileContentHDao);
        registerDao(Holiday.class, holidayDao);
        registerDao(PrintDate.class, printDateDao);
        registerDao(ErrorLog.class, errorLogDao);
        registerDao(mobiledatafile.class, mobiledatafileDao);
        registerDao(ReminderPo.class, reminderPoDao);
        registerDao(EmbeddedInfo.class, embeddedInfoDao);
    }
    
    public void clear() {
        collectionActivityDaoConfig.getIdentityScope().clear();
        installmentScheduleDaoConfig.getIdentityScope().clear();
        generalParameterDaoConfig.getIdentityScope().clear();
        groupUserDaoConfig.getIdentityScope().clear();
        lookupDaoConfig.getIdentityScope().clear();
        syncDaoConfig.getIdentityScope().clear();
        migrationDaoConfig.getIdentityScope().clear();
        ruleDaoConfig.getIdentityScope().clear();
        blacklistDaoConfig.getIdentityScope().clear();
        productOfferingDaoConfig.getIdentityScope().clear();
        pODealerDaoConfig.getIdentityScope().clear();
        assetSchemeDaoConfig.getIdentityScope().clear();
        pOAssetDaoConfig.getIdentityScope().clear();
        industryDaoConfig.getIdentityScope().clear();
        decisionDaoConfig.getIdentityScope().clear();
        marketPriceDaoConfig.getIdentityScope().clear();
        menuDaoConfig.getIdentityScope().clear();
        printItemDaoConfig.getIdentityScope().clear();
        questionSetDaoConfig.getIdentityScope().clear();
        schemeDaoConfig.getIdentityScope().clear();
        timelineTypeDaoConfig.getIdentityScope().clear();
        userDaoConfig.getIdentityScope().clear();
        pushSyncDaoConfig.getIdentityScope().clear();
        loggerDaoConfig.getIdentityScope().clear();
        collectionHistoryDaoConfig.getIdentityScope().clear();
        commentDaoConfig.getIdentityScope().clear();
        depositReportDDaoConfig.getIdentityScope().clear();
        depositReportHDaoConfig.getIdentityScope().clear();
        imageResultDaoConfig.getIdentityScope().clear();
        locationInfoDaoConfig.getIdentityScope().clear();
        messageDaoConfig.getIdentityScope().clear();
        paymentHistoryDDaoConfig.getIdentityScope().clear();
        paymentHistoryHDaoConfig.getIdentityScope().clear();
        printResultDaoConfig.getIdentityScope().clear();
        receiptVoucherDaoConfig.getIdentityScope().clear();
        taskDDaoConfig.getIdentityScope().clear();
        taskHDaoConfig.getIdentityScope().clear();
        taskHSequenceDaoConfig.getIdentityScope().clear();
        taskUpdateDaoConfig.getIdentityScope().clear();
        timelineDaoConfig.getIdentityScope().clear();
        mobileContentDDaoConfig.getIdentityScope().clear();
        mobileContentHDaoConfig.getIdentityScope().clear();
        holidayDaoConfig.getIdentityScope().clear();
        printDateDaoConfig.getIdentityScope().clear();
        errorLogDaoConfig.getIdentityScope().clear();
        mobiledatafileDaoConfig.getIdentityScope().clear();
        reminderPoDaoConfig.getIdentityScope().clear();
        embeddedInfoDaoConfig.getIdentityScope().clear();
    }

    public CollectionActivityDao getCollectionActivityDao() {
        return collectionActivityDao;
    }

    public InstallmentScheduleDao getInstallmentScheduleDao() {
        return installmentScheduleDao;
    }

    public GeneralParameterDao getGeneralParameterDao() {
        return generalParameterDao;
    }

    public GroupUserDao getGroupUserDao() {
        return groupUserDao;
    }

    public LookupDao getLookupDao() {
        return lookupDao;
    }

    public SyncDao getSyncDao() {
        return syncDao;
    }

    public MigrationDao getMigrationDao() {
        return migrationDao;
    }

    public RuleDao getRuleDao() {
        return ruleDao;
    }

    public BlacklistDao getBlacklistDao() {
        return blacklistDao;
    }

    public ProductOfferingDao getProductOfferingDao() {
        return productOfferingDao;
    }

    public PODealerDao getPODealerDao() {
        return pODealerDao;
    }

    public AssetSchemeDao getAssetSchemeDao() {
        return assetSchemeDao;
    }

    public POAssetDao getPOAssetDao() {
        return pOAssetDao;
    }

    public IndustryDao getIndustryDao() {
        return industryDao;
    }

    public DecisionDao getDecisionDao() {
        return decisionDao;
    }

    public MarketPriceDao getMarketPriceDao() {
        return marketPriceDao;
    }

    public MenuDao getMenuDao() {
        return menuDao;
    }

    public PrintItemDao getPrintItemDao() {
        return printItemDao;
    }

    public QuestionSetDao getQuestionSetDao() {
        return questionSetDao;
    }

    public SchemeDao getSchemeDao() {
        return schemeDao;
    }

    public TimelineTypeDao getTimelineTypeDao() {
        return timelineTypeDao;
    }

    public UserDao getUserDao() {
        return userDao;
    }

    public PushSyncDao getPushSyncDao() {
        return pushSyncDao;
    }

    public LoggerDao getLoggerDao() {
        return loggerDao;
    }

    public CollectionHistoryDao getCollectionHistoryDao() {
        return collectionHistoryDao;
    }

    public CommentDao getCommentDao() {
        return commentDao;
    }

    public DepositReportDDao getDepositReportDDao() {
        return depositReportDDao;
    }

    public DepositReportHDao getDepositReportHDao() {
        return depositReportHDao;
    }

    public ImageResultDao getImageResultDao() {
        return imageResultDao;
    }

    public LocationInfoDao getLocationInfoDao() {
        return locationInfoDao;
    }

    public MessageDao getMessageDao() {
        return messageDao;
    }

    public PaymentHistoryDDao getPaymentHistoryDDao() {
        return paymentHistoryDDao;
    }

    public PaymentHistoryHDao getPaymentHistoryHDao() {
        return paymentHistoryHDao;
    }

    public PrintResultDao getPrintResultDao() {
        return printResultDao;
    }

    public ReceiptVoucherDao getReceiptVoucherDao() {
        return receiptVoucherDao;
    }

    public TaskDDao getTaskDDao() {
        return taskDDao;
    }

    public TaskHDao getTaskHDao() {
        return taskHDao;
    }

    public TaskHSequenceDao getTaskHSequenceDao() {
        return taskHSequenceDao;
    }

    public TaskUpdateDao getTaskUpdateDao() {
        return taskUpdateDao;
    }

    public TimelineDao getTimelineDao() {
        return timelineDao;
    }

    public MobileContentDDao getMobileContentDDao() {
        return mobileContentDDao;
    }

    public MobileContentHDao getMobileContentHDao() {
        return mobileContentHDao;
    }

    public HolidayDao getHolidayDao() {
        return holidayDao;
    }

    public PrintDateDao getPrintDateDao() {
        return printDateDao;
    }

    public ErrorLogDao getErrorLogDao() {
        return errorLogDao;
    }

    public mobiledatafileDao getMobiledatafileDao() {
        return mobiledatafileDao;
    }

    public ReminderPoDao getReminderPoDao() {
        return reminderPoDao;
    }

    public EmbeddedInfoDao getEmbeddedInfoDao() {
        return embeddedInfoDao;
    }

}
