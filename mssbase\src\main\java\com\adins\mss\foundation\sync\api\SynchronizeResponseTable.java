package com.adins.mss.foundation.sync.api;

import com.adins.mss.dao.ProductOffering;
import com.adins.mss.foundation.http.MssResponseType;
import com.google.gson.annotations.SerializedName;

public class SynchronizeResponseTable extends MssResponseType {

    @SerializedName("productOffering")
    private ProductOffering productOffering;

    public ProductOffering getProductOffering() {
        return productOffering;
    }

    public void setProductOffering(ProductOffering productOffering) {
        this.productOffering = productOffering;
    }
}
