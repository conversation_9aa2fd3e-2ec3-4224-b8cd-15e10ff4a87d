package com.adins.mss.base.todolist;

import android.content.Context;
import android.widget.ArrayAdapter;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.dynamicform.SurveyHeaderBean;
import com.adins.mss.base.mainmenu.MainMenuActivity;
import com.adins.mss.base.todolist.form.PriorityTabFragment;
import com.adins.mss.base.util.GsonHelper;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.Scheme;
import com.adins.mss.dao.TaskH;
import com.adins.mss.foundation.db.dataaccess.SchemeDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskHDataAccess;
import com.adins.mss.foundation.db.dataaccess.TaskUpdateDataAccess;
import com.adins.mss.foundation.http.HttpConnectionResult;
import com.adins.mss.foundation.http.HttpCryptedConnection;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class ToDoList {
    private Context context;
    private static List<TaskH> listTask;
    private static int searchType;
    private static String searchContent;
    private String userId;
    public static ArrayAdapter<TaskH> statusListAdapter;
//	private RequestWrapperTaskList beanWrap;

    // Bong Dec 10th, 2014 - to determine query list
		/* SEARCH TYPE */
    public static final int SEARCH_BY_TASK_ID = 1;
    public static final int SEARCH_BY_CUSTOMER_NAME = 2;
    public static final int SEARCH_BY_PTS = 4;
    public static final int SEARCH_BY_ALL = 3;

    // Bong Dec 11th, 2014 - to determine query while refresh taskList
		/* TaskList MenuBar */
    public static final int TASK_LIST_PRIORITY = 1;
    public static final int TASK_LIST_STATUS = 2;

    public ToDoList(Context context){
        this.context = context;
        if(GlobalData.getSharedGlobalData().getUser()==null)
            MainMenuActivity.InitializeGlobalDataIfError(context);
        userId = GlobalData.getSharedGlobalData().getUser().getUuid_user();
    }

    /**
     * This method is used to get task list according to search by and search content.
     * All task in this method will be shown if they have not been submitted or
     * have not been downloaded yet from server or not new task.
     * @param searchType
     * @param searchContent
     * @return
     */
    public List<TaskH> getListTaskInStatus(int searchType, String searchContent) {
        ToDoList.searchType = searchType;
        ToDoList.searchContent = searchContent;
        if(searchContent==null || "".equals(searchContent) || searchContent.length()==0)
            return listTask = TaskHDataAccess.getAllTaskInStatus(context, userId)
                    ;
        else if(searchType==SEARCH_BY_TASK_ID)
            return listTask = TaskHDataAccess.getAllTaskInStatusByTask(context, userId, searchContent)
                    ;
        else if(searchType==SEARCH_BY_ALL)
            return listTask = TaskHDataAccess.getAllTaskInStatusByAll(context, userId, searchContent)
                    ;
        else{
            return listTask = TaskHDataAccess.getAllTaskInStatusByCustomer(context, userId, searchContent);
        }
//		return listTask;
    }

    /**
     * This method is used to get task list according to search by and search content.
     * All task in this method will be shown if they are new task or they have been downloaded from server.
     * @param searchType
     * @param searchContent
     * @return
     */
    public List<TaskH> getListTaskInPriority(int searchType, String searchContent){
        ToDoList.searchType = searchType;
        ToDoList.searchContent = searchContent;
        if(searchContent==null || "".equals(searchContent) || searchContent.length()==0)
            return listTask = TaskHDataAccess.getAllTaskInPriority(context, userId)
                    ;
        else if(searchType==SEARCH_BY_TASK_ID)
            return listTask = TaskHDataAccess.getAllTaskInPriorityByTask(context, userId, searchContent)
                    ;
        else if(searchType==SEARCH_BY_ALL)
            return listTask = TaskHDataAccess.getAllTaskInPriorityByAll(context, userId, searchContent)
                    ;
        else{
            return listTask = TaskHDataAccess.getAllTaskInPriorityByCustomer(context, userId, searchContent);
        }
//		return listTask;
    }

    //michael.wijaya 08 Apr 22: used to get task list of many types depending on the schemeDescription (ex. "Form Task Promise To Survey" or "Form Task Guarantor")
    public List<TaskH> getListTaskTypes(Context context, String schemeDescription) {
        return TaskHDataAccess.getAllTaskByFormName(context, userId, schemeDescription);
    }

    //michael.wijaya 22 Apr 22: used to filter main task list menu from tasks with form name in the other task list menu
    // ex. Task Form Guarantor or Task Promise To Survey should not be displayed in the main Task List menu
    public List<TaskH> removeTaskHWithFormFromMenu(List<TaskH> listTaskH){
        Scheme pts = SchemeDataAccess.getOneSurveyTaskSchemeByFormName(context, Global.FORM_NAME_PROMISE_TO_SURVEY);
        Scheme preSurvey = SchemeDataAccess.getOneSurveyTaskSchemeByFormName(context, Global.FORM_NAME_PRE_SURVEY);
        Scheme ots = SchemeDataAccess.getOneSurveyTaskSchemeByFormName(context, Global.FORM_NAME_OTS);
        Scheme guarantor = SchemeDataAccess.getOneSurveyTaskSchemeByFormName(context, Global.FORM_NAME_GUARANTOR);

        //if user does not have all of these schemes (every scheme is null), this will be skipped
        if(pts != null || preSurvey != null || ots != null || guarantor != null){
            //search through the list, and remove taskH that has any of the schemes above
            //used Iterator to prevent ConcurrentModificationException when using basic foreach
            for (Iterator<TaskH> iterator = listTaskH.iterator(); iterator.hasNext(); ) {
                TaskH taskH = iterator.next();
                String currSchemeId = taskH.getUuid_scheme();

                if(pts != null && currSchemeId.equals(pts.getUuid_scheme())) {
                    iterator.remove();
                }else if(preSurvey != null && currSchemeId.equals(preSurvey.getUuid_scheme())){
                    iterator.remove();
                }else if(ots != null && currSchemeId.equals(ots.getUuid_scheme())){
                    iterator.remove();
                }else if(guarantor != null && currSchemeId.equals(guarantor.getUuid_scheme())){
                    iterator.remove();
                }
            }
        }
        return listTaskH;
    }

    public List<TaskH> getListTaskInPriority(int searchType, String searchContent, String uuidScheme, String sortBy, String orderBy){
        ToDoList.searchType = searchType;
        ToDoList.searchContent = searchContent;

        if(uuidScheme.equals(PriorityTabFragment.uuidSchemeDummy)){
//            return listTask = TaskHDataAccess.getAllTaskInPriority(context, userId);
//            return listTask = TaskHDataAccess.getAllTaskInPriority(context, userId, sortBy, orderBy);
            listTask = TaskHDataAccess.getAllTaskInPriority(context, userId, sortBy, orderBy);
            return removeTaskHWithFormFromMenu(listTask);
        }
        else if(searchContent==null || "".equals(searchContent) || searchContent.length()==0)
            return listTask = TaskHDataAccess.getAllTaskInPriorityByScheme(context, userId, uuidScheme, sortBy, orderBy);
        else if(searchType==SEARCH_BY_TASK_ID)
            return listTask = TaskHDataAccess.getAllTaskInPriorityByTaskAndScheme(context, userId, searchContent, uuidScheme);
        else if(searchType==SEARCH_BY_ALL)
            return listTask = TaskHDataAccess.getAllTaskInPriorityByAllAndScheme(context, userId, searchContent, uuidScheme, sortBy, orderBy);
        else {
            return listTask = TaskHDataAccess.getAllTaskInPriorityByCustomerAndScheme(context, userId, searchContent, uuidScheme, sortBy, orderBy);
        }
    }

    public List<TaskH> getListTaskInHighPriority(String sortBy, String orderBy){
        String uuid_user = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        listTask = TaskHDataAccess.getAllTaskInHighPriority(context, uuid_user, sortBy, orderBy);
        return removeTaskHWithFormFromMenu(listTask);
    }

    public List<TaskH> getListTaskInNormalPriority(String sortBy, String orderBy){
        String uuid_user = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        listTask = TaskHDataAccess.getAllTaskInNormalPriority(context, uuid_user, sortBy, orderBy);
        return removeTaskHWithFormFromMenu(listTask);
    }

    public List<TaskH> getListTaskInLowPriority(String sortBy, String orderBy){
        String uuid_user = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        listTask = TaskHDataAccess.getAllTaskInLowPriority(context, uuid_user, sortBy, orderBy);
        return removeTaskHWithFormFromMenu(listTask);
    }

    public List<TaskH> getListTaskInHighPriority(String uuidScheme, String sortBy, String orderBy){
        if(uuidScheme.equals(PriorityTabFragment.uuidSchemeDummy)){
            return getListTaskInHighPriority(sortBy, orderBy);
        }
        String uuid_user = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        listTask = TaskHDataAccess.getAllTaskInHighPriorityByScheme(context, uuid_user, uuidScheme, sortBy, orderBy);
        return listTask;
    }

    public List<TaskH> getListTaskInNormalPriority(String uuidScheme, String sortBy, String orderBy){
        if(uuidScheme.equals(PriorityTabFragment.uuidSchemeDummy)){
            return getListTaskInNormalPriority(sortBy, orderBy);
        }
        String uuid_user = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        listTask = TaskHDataAccess.getAllTaskInNormalPriorityByScheme(context, uuid_user, uuidScheme, sortBy, orderBy);
        return listTask;
    }

    public List<TaskH> getListTaskInLowPriority(String uuidScheme, String sortBy, String orderBy){
        if(uuidScheme.equals(PriorityTabFragment.uuidSchemeDummy)){
            return getListTaskInLowPriority(sortBy, orderBy);
        }
        String uuid_user = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        return listTask = TaskHDataAccess.getAllTaskInLowPriorityByScheme(context, uuid_user, uuidScheme, sortBy, orderBy);
    }

    public static List<TaskH> getListTaskInPriority(Context context,int searchType, String searchContent){
        ToDoList.searchType = searchType;
        ToDoList.searchContent = searchContent;

        String uuid_user = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        if(uuid_user==null){
            try {
                MainMenuActivity.InitializeGlobalDataIfError(context);
            } catch (Exception e) {
                // TODO: handle exception
            }
            uuid_user = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        }
        if(searchContent==null || "".equals(searchContent) || searchContent.length()==0)
            return listTask = TaskHDataAccess.getAllTaskInPriority(context, uuid_user)
                    ;
        else if(searchType==SEARCH_BY_TASK_ID)
            return listTask = TaskHDataAccess.getAllTaskInPriorityByTask(context, uuid_user, searchContent)
                    ;
        else if(searchType==SEARCH_BY_ALL)
            return listTask = TaskHDataAccess.getAllTaskInPriorityByAll(context, uuid_user, searchContent)
                    ;
        else{
            return listTask = TaskHDataAccess.getAllTaskInPriorityByCustomer(context, uuid_user, searchContent);
        }
//		return listTask;
    }


    public static List<TaskH> getListTaskInSequence(Context context){
        ToDoList.searchType = searchType;
        ToDoList.searchContent = searchContent;

        String uuid_user = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        if(uuid_user==null){
            try {
                MainMenuActivity.InitializeGlobalDataIfError(context);
            } catch (Exception e) {
                // TODO: handle exception
            }
            uuid_user = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        }
        return listTask = TaskHDataAccess.getAllTaskInPriority(context, uuid_user);

//		return listTask;
    }
    /**
     * This method will redisplay all task list according to search by and search content.
     * @param menuBar
     * @return
     */
    public List<TaskH> doRefresh(int menuBar){
//		listTask=null;
        if(menuBar==TASK_LIST_PRIORITY)
            return getListTaskInPriority(searchType, searchContent);
        else if(menuBar==TASK_LIST_STATUS)
            return getListTaskInStatus(searchType, searchContent);
        else return listTask;
    }

    /**
     * This method will get status from a spesific taskId.
     * @param taskId
     * @return
     */
    public String getStatus(String taskId){
        String statusTask=null;
        TaskH taskH = TaskHDataAccess.getOneTaskHeader(context, taskId);
        statusTask = taskH.getStatus();
        return statusTask;
    }

//	public void sendImage(String taskId, int imagePosition){
//
//	}

	/*public void sendSurvey(Context context, String taskId, boolean isPartial) throws Exception{
		Date startTime = Tool.getSystemDateTime();
		String[] result = this.submitSurvey(context, taskId, isPartial);
		Date finishTime = Tool.getSystemDateTime();

		long time = startTime.getTime() - finishTime.getTime();
		int sec = (int) Math.ceil(time/1000); // milisecond to second

		// update survey_id

	}*/

	/*public String[] submitSurvey(Context context, String taskId, boolean isPartial) throws Exception {
		String[] result = null;
		boolean isSurveyTask = Tool.isInteger(taskId);
		if(isSurveyTask)
//			result = this.doSubmitSurveyTask(context, taskId, isPartial, Global.URL_SUBMIT)
			;
		else{
//			result = this.doSubmitNewSurveyTask(context, taskId, isPartial, Global.URL_SUBMIT);
		}
		return result;
	}*/

	/*public String[] doSubmitSurveyTask(Context context, String taskId, int withImage, String targetURL) throws Exception {
//		TaskDDataAccess taskDDataAccess = new TaskDDataAccess();
//		TaskH taskH = new TaskHDataAccess().getOneTaskHeader(context, taskId);
		TaskH taskH = TaskHDataAccess.getOneTaskHeader(context, taskId);
		List<TaskD> listOfAnswers = new ArrayList<TaskD>();
//		listOfAnswers = TaskDDataAccess.getAllByTaskId(context, userId, taskId, withImage);

		// make gson
//		beanWrap = null;
//		String json = new Gson().toJson(beanWrap);


		if (listOfAnswers==null || listOfAnswers.size()==0)
			throw new IllegalArgumentException("No data found to be sent!");
		String[] result = new String[2];
		StringBuilder sb = new StringBuilder();
		int i = 0;
		for (TaskD bean:listOfAnswers){
			if (i>0)
				sb.append(Global.DELIMETER_ROW);
			// bong Oct 27th, 2014 - Checking answer bean lov_code
			if(bean.getLov()==null)
				bean.setLov("");
			System.out.println("bean. get lov code = "+bean.getLov());

//			bean.setTaskId(taskId);
//			sb.append(bean.toSubmitString());
			i++;
		}
		//tambah user id
//		sb.append(Global.DELIMETER_INFO+ApplicationBean.getInstance().getUserId());
//		Global.setIsUploading(true);//biar saat masih upload gak bisa exit
//		result[0] = Tool.submitData(targetURL, sb.toString());
//		result[1] = String.valueOf(sb.toString().getBytes().length);
//		System.out.println("param submit "+sb.toString());
////		if (isPartial)
////			draftDb.updateStatusHeader(taskId, userId, Global.STATUS_SEND_UPLOADING);
////		else
////			draftDb.updateStatusHeader(taskId, userId, Global.STATUS_SEND_SENT);

		return result;
	}*/

	/*public String[] doSubmitNewSurveyTask(Context context, String taskId, int withImage, String targetURL) throws Exception {
//		TaskDDataAccess taskDDataAccess = new TaskDDataAccess();
//		TaskH taskH = new TaskHDataAccess().getOneTaskHeader(context, taskId);
		TaskH taskH = TaskHDataAccess.getOneTaskHeader(context, taskId);
		List<TaskD> listOfAnswers = new ArrayList<TaskD>();

		String[] result = null;
//		String header = taskH.getHeaderForSend(context, taskH.getTask_id());
//		listOfAnswer = taskDDataAccess.getAllByTaskId(context, userId, taskId, withImage);

		// make gson
//		beanWrap = null;
//		String json = new Gson().toJson(beanWrap);

		if (listOfAnswers==null || listOfAnswers.size()==0)
			throw new IllegalArgumentException("No data found to be sent!");
//		StringBuilder sb = new StringBuilder(header).append(Global.DELIMETER_INFO);
		int i = 0;
		for (TaskD bean:listOfAnswers){
			if (i > 0)
//				sb.append(Global.DELIMETER_ROW);
			if(bean.getLov()==null)
				bean.setLov("");
			System.out.println("bean. get lov code = "+bean.getLov());
//			bean.setTaskId(taskId);
//			sb.append(bean.toSubmitString());
		}
		//tambah user id
//		sb.append(Global.DELIMETER_INFO+ApplicationBean.getInstance().getUserId());
//		Global.setIsUploading(true);//biar saat masih upload gak bisa exit
//		result[0] = Tool.submitData(targetURL, sb.toString());
//		result[1] = String.valueOf(sb.toString().getBytes().length);
//		System.out.println("param submit "+sb.toString());
////		if (isPartial)
////			draftDb.updateStatusHeader(taskId, userId, Global.STATUS_SEND_UPLOADING);
////		else
////			draftDb.updateStatusHeader(taskId, userId, Global.STATUS_SEND_SENT);

		return result;
	}*/

	/*public void doAfterSubmit(Context context, String taskId, int withImage,
			int time, String size, String result){
		if(withImage!=Global.IMAGE_NULL){
			int cnt = this.countPendingImage(context, taskId);
			if(cnt>0){
				// update status survey (form and header) to uploading
			}else{
				// update status survey (form and header) to sent
			}
		}else{
			// update status survey (form and header) to sent
		}
	}*/

	/*public int countPendingImage(Context context){
		int count=0;
		String userId = "";
		//query count image pending by taskId and user
		return count;
	}*/

	/*public int countPendingImage(Context context, String taskId){
		int count=0;
		String userId = "";
		//query count image pending by taskId and user
		return count;
	}*/

    /**Return task list which has been queried
     * @return
     */
    public static List<TaskH> getListTask() {
        return listTask;
    }

    public TaskH getOneTaskFromServer(Scheme scheme){
        RequestOneTaskBean rotb = new RequestOneTaskBean();
        rotb.setSchemeId(scheme.getForm_id());
        rotb.setAudit(GlobalData.getSharedGlobalData().getAuditData());
        rotb.addImeiAndroidIdToUnstructured();

        String json = GsonHelper.toJson(rotb);
        boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
        boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
        HttpCryptedConnection httpConn = new HttpCryptedConnection(context, encrypt, decrypt);
        HttpConnectionResult result = null;
        try {
            result = httpConn.requestToServer(GlobalData.getSharedGlobalData().getURL_GET_TASKLIST(), json);
        } catch (Exception e) {
            e.printStackTrace();
        }

        JsonOneTask jot = new JsonOneTask();
        // List<TaskH> taskHList = gson.fromJson(result.getResult(),  new TypeToken<List<TaskH>>(){}.getType());
        jot = GsonHelper.fromJson(result.getResult(),  JsonOneTask.class);
        TaskHDataAccess.addOrReplace(context, jot.getOneTaskH());
        return jot.getOneTaskH();

    }

    /**
     * This method is used to retrieve list of TaskH from server
     * @return
     */
    public List<TaskH> getListTaskFromServer(){
        // List<NameValuePair> param = new ArrayList<NameValuePair>();
        // param.add(new BasicNameValuePair("task", "getList"));
        // param.add(new BasicNameValuePair("username", GlobalData.getSharedGlobalData().getUser().getLogin_id()));
        // param.add(new BasicNameValuePair("password", GlobalData.getSharedGlobalData().getUser().getPassword()));
        // param.add(new BasicNameValuePair("flagFreshInstall", Global.);

        //TODO belum tentu fix pake objek ini
        // BaseCommunicationModel bcm = new BaseCommunicationModel(true);
        RequestTaskListBean rtlb = new RequestTaskListBean();
        rtlb.setUserId(userId);
        rtlb.setAudit(GlobalData.getSharedGlobalData().getAuditData());
        rtlb.addImeiAndroidIdToUnstructured();
        // MssRequestType mrt = new MssRequestType();
        // mrt.setAudit(GlobalData.getSharedGlobalData().getAuditData());
        // mrt.addItemToUnstructured(new KeyValue("username", GlobalData.getSharedGlobalData().getUser().getLogin_id()), false);
        // mrt.addItemToUnstructured(new KeyValue("password", GlobalData.getSharedGlobalData().getUser().getPassword()), false);
        // mrt.addItemToUnstructured(new KeyValue("imei", GlobalData.getSharedGlobalData().getImei()), false);

        // to JSON
        String json = GsonHelper.toJson(rtlb);

        boolean encrypt = GlobalData.getSharedGlobalData().isEncrypt();
        boolean decrypt = GlobalData.getSharedGlobalData().isDecrypt();
        HttpCryptedConnection httpConn = new HttpCryptedConnection(context, encrypt, decrypt);
        HttpConnectionResult result = null;
        try {
            result = httpConn.requestToServer(GlobalData.getSharedGlobalData().getURL_GET_TASKLIST(), json, Global.DEFAULTCONNECTIONTIMEOUT);
        } catch (Exception e) {
            e.printStackTrace();
        }

        JsonTaskList jtl = new JsonTaskList();
        // List<TaskH> taskHList = gson.fromJson(result.getResult(),  new TypeToken<List<TaskH>>(){}.getType());
        jtl = GsonHelper.fromJson(result.getResult(),  JsonTaskList.class);
        TaskHDataAccess.addOrReplace(context, jtl.getListTaskH());
        return jtl.getListTaskH();
    }

    //michael.wijaya 08 Apr 22: used for types of Task List menu's counter in sidebar menu
    public static long getCounterTaskListTypes(Context context, String schemeDescription){
        long counterTaskListTypes = 0;
        String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        try{
            counterTaskListTypes = TaskHDataAccess.getTaskListTypesCounter(context, uuidUser, schemeDescription);
        }catch(Exception e){
            e.printStackTrace();
        }
        return counterTaskListTypes;
    }

    public static long getCounterTaskList(Context context){
        long counter=0;
        String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        try {
            long counterPriority = TaskHDataAccess.getTaskInPriorityCounter(context, uuidUser);
            long counterStatus = TaskHDataAccess.getTaskInStatusCounter(context, uuidUser);
//			counter = counterPriority + counterStatus +getCounterApprovalTask(context)+getCounterVerificationTask(context);
            counter = counterPriority + counterStatus;
        } catch (Exception e) {
            // TODO: handle exception
        }
        return counter;
    }

    public static long getAllCounter(Context context){
        long counter=0;
        String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
        try {
            long counterPriority = TaskHDataAccess.getTaskInPriorityCounter(context, uuidUser);
            long counterStatus = TaskHDataAccess.getTaskInStatusCounter(context, uuidUser);
            long counterVerify = getCounterVerificationTask(context);
            long counterApproval = getCounterApprovalTask(context);
            long counterVerifyBranch = getCounterVerificationTaskByBranch(context);
            long counterApprovalBranch = getCounterApprovalTaskByBranch(context);
            long counterAssignmentTask = getCounterAssignment(context);

            long counterPromiseToSurvey = getCounterTaskListTypes(context, Global.FORM_NAME_PROMISE_TO_SURVEY);
            long counterPreSurvey = getCounterTaskListTypes(context, Global.FORM_NAME_PRE_SURVEY);
            long counterOts = getCounterTaskListTypes(context, Global.FORM_NAME_OTS);
            long counterGuarantor = getCounterTaskListTypes(context, Global.FORM_NAME_GUARANTOR);

            counter = counterPriority + counterStatus;

            if(MainMenuActivity.mnSVYApproval!=null)
                counter += counterApproval;
            if(MainMenuActivity.mnSVYVerify!=null)
                counter += counterVerify;
            if(MainMenuActivity.mnSVYApprovalByBranch!=null)
                counter += counterApprovalBranch;
            if(MainMenuActivity.mnSVYVerifyByBranch!=null)
                counter += counterVerifyBranch;
            if(MainMenuActivity.mnSVYAssignment!=null)
                counter += counterAssignmentTask;
            if(MainMenuActivity.mnTaskPromiseToSurvey!=null)
                counter += counterPromiseToSurvey;
            if(MainMenuActivity.mnTaskPreSurvey!=null)
                counter += counterPreSurvey;
            if(MainMenuActivity.mnTaskOts!=null)
                counter += counterOts;
            if(MainMenuActivity.mnTaskGuarantor!=null)
                counter += counterGuarantor;
        } catch (Exception e) {
            // TODO: handle exception
        }
        return counter;
    }

    public static long getCounterAssignment(Context context) {
        if (MainMenuActivity.mnSVYAssignment == null) return 0;

        long counter = 0;

        try {
            counter = Long.parseLong(MainMenuActivity.mnSVYAssignment.getCounter());
        } catch (Exception e) {
            // empty
        }

        return counter;
    }

    public static long getCounterTaskUpdate(Context context) {
        long counter = 0;
        try {
            counter = TaskUpdateDataAccess.getTaskUpdateCounterByUser(context, GlobalData.getSharedGlobalData().getUser().getUuid_user());
        } catch (Exception e) {
            FireCrash.log(e);
            // TODO: handle exception
        }
        return counter;
    }

    public static long getCounterVerificationTask(Context context){
        long counter=0;
        try {
            counter = TaskHDataAccess.getVerificationTaskCounterByUser(context, GlobalData.getSharedGlobalData().getUser().getUuid_user());
        } catch (Exception e) {
            // TODO: handle exception
        }
        return counter;
    }

    public static long getCounterApprovalTask(Context context){
        long counter=0;
        try {
            counter = TaskHDataAccess.getApprovalTaskCounterByUser(context, GlobalData.getSharedGlobalData().getUser().getUuid_user());
        } catch (Exception e) {
            // TODO: handle exception
        }
        return counter;
    }

    public static long getCounterVerificationTaskByBranch(Context context){
        long counter=0;
        try {
            counter = TaskHDataAccess.getVerificationTaskCounterByBranch(context, GlobalData.getSharedGlobalData().getUser().getUuid_user());
        } catch (Exception e) {
            // TODO: handle exception
        }
        return counter;
    }

    public static long getCounterApprovalTaskByBranch(Context context){
        long counter=0;
        try {
            counter = TaskHDataAccess.getApprovalTaskCounterByBranch(context, GlobalData.getSharedGlobalData().getUser().getUuid_user());
        } catch (Exception e) {
            // TODO: handle exception
        }
        return counter;
    }

    public static long getCounterStatusTask(Context context){
        long counter=0;
        try {
            counter = TaskHDataAccess.getTaskInStatusCounter(context, GlobalData.getSharedGlobalData().getUser().getUuid_user());
        } catch (Exception e) {
            // TODO: handle exception
        }
        return counter;
    }

    public long getCounterAllPriority(){
        long counter=0;
        try {
            String uuidUser = GlobalData.getSharedGlobalData().getUser().getUuid_user();
            counter = TaskHDataAccess.getTaskInPriorityCounter(context, uuidUser);
        } catch (Exception e) {
            // TODO: handle exception
        }
        return counter;
    }

//	public long getCounterHighPriority(){
//		long counter=0;
//		try {
//			counter = getListTaskInHighPriority().size();
//		} catch (Exception e) {
//			// TODO: handle exception
//		}
//		return counter;
//	}
//
//	public long getCounterNormalPriority(){
//		long counter=0;
//		try {
//			counter = getListTaskInNormalPriority().size();
//		} catch (Exception e) {
//			// TODO: handle exception
//		}
//		return counter;
//	}

    public static List<SurveyHeaderBean> listOfSurveyStatus;
    public static void addSurveyToList(SurveyHeaderBean bean, boolean removeOld) {
        List<SurveyHeaderBean> tempList = new ArrayList<SurveyHeaderBean>();
        if (listOfSurveyStatus == null)
            listOfSurveyStatus = new ArrayList<SurveyHeaderBean>();

        if (removeOld) {
            int idx=0;
            for (SurveyHeaderBean headerBean : listOfSurveyStatus) {
                try {
                    if (bean.getTask_id().equals(headerBean.getTask_id())) {
                        listOfSurveyStatus.remove(idx);
                        break;
                    }
                } catch (Exception e) {
                    // TODO: handle exception
                }
                idx++;
            }
        }
        listOfSurveyStatus.add(listOfSurveyStatus.size(), bean);
        tempList.addAll(listOfSurveyStatus);
        listOfSurveyStatus = null;
        listOfSurveyStatus = tempList;
    }
    public static void refreshSurveyToList() {
        List<SurveyHeaderBean> tempList = new ArrayList<SurveyHeaderBean>();
        if (listOfSurveyStatus == null)
            listOfSurveyStatus = new ArrayList<SurveyHeaderBean>();
        tempList.addAll(listOfSurveyStatus);
        listOfSurveyStatus = null;
        listOfSurveyStatus = tempList;
    }
    public static void removeSurveyFromList(String taskId) {
        List<SurveyHeaderBean> tempList = new ArrayList<SurveyHeaderBean>();
        if (listOfSurveyStatus == null)
            listOfSurveyStatus = new ArrayList<SurveyHeaderBean>();

        int idx=0;
        for (SurveyHeaderBean headerBean : listOfSurveyStatus) {
            if (taskId.equals(headerBean.getTask_id())) {
                listOfSurveyStatus.remove(idx);
                break;
            }
            idx++;
        }

        tempList.addAll(listOfSurveyStatus);
        listOfSurveyStatus = null;
        listOfSurveyStatus = tempList;
    }

    public static void updateStatusSurvey(String taskId, String status, int imageLeft) {
        List<SurveyHeaderBean> tempList = new ArrayList<SurveyHeaderBean>();
        if (listOfSurveyStatus == null)
            listOfSurveyStatus = new ArrayList<SurveyHeaderBean>();

        for (SurveyHeaderBean headerBean : listOfSurveyStatus) {
            if (taskId.equals(headerBean.getTask_id())) {
                headerBean.setStatus(status);
                headerBean.setImageLeft(imageLeft);
            }
            tempList.add(headerBean);
        }
        listOfSurveyStatus = null;
        listOfSurveyStatus = tempList;
    }

    public static void updatePrioritySurvey(String taskId, String priority) {
        List<SurveyHeaderBean> tempList = new ArrayList<SurveyHeaderBean>();
        if (listOfSurveyStatus == null)
            listOfSurveyStatus = new ArrayList<SurveyHeaderBean>();

        for (SurveyHeaderBean headerBean : listOfSurveyStatus) {
            if (taskId.equals(headerBean.getTask_id())) {
                headerBean.setPriority(priority);

                break;
            }
        }

        tempList.addAll(listOfSurveyStatus);
        listOfSurveyStatus = null;
        listOfSurveyStatus = tempList;
    }

    public static void updateTaskIdStatusSurvey(String taskId, String newTaskID) {
        List<SurveyHeaderBean> tempList = new ArrayList<SurveyHeaderBean>();
        if (listOfSurveyStatus == null)
            listOfSurveyStatus = new ArrayList<SurveyHeaderBean>();

        for (SurveyHeaderBean headerBean : listOfSurveyStatus) {
            if (taskId.equals(headerBean.getTask_id())) {
                headerBean.setTask_id(newTaskID);
                break;
            }
        }

        tempList.addAll(listOfSurveyStatus);
        listOfSurveyStatus = null;
        listOfSurveyStatus = tempList;
    }

    public static boolean isOldTask(TaskH h){
        return !(h.getStatus().equals(TaskHDataAccess.STATUS_SEND_INIT) ||
                h.getStatus().equals(TaskHDataAccess.STATUS_TASK_WAITING) ||
                h.getStatus().equals(TaskHDataAccess.STATUS_TASK_APPROVAL) ||
                h.getStatus().equals(TaskHDataAccess.STATUS_TASK_VERIFICATION));
    }
}
