package com.adins.mss.base.common;

import com.google.gson.annotations.SerializedName;

public class Validation {
    @SerializedName("ref_id")
    private String refId;

    @SerializedName("value")
    private String value;

    @SerializedName("update")
    private Update update;

    public String getRefId() {
        return refId;
    }

    public void setRefId(String refId) {
        this.refId = refId;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Update getUpdate() {
        return update;
    }

    public void setUpdate(Update update) {
        this.update = update;
    }

    public class Update {
        @SerializedName("readonly")
        private boolean readonly;

        public boolean isReadonly() {
            return readonly;
        }

        public void setReadonly(boolean readonly) {
            this.readonly = readonly;
        }
    }
}
