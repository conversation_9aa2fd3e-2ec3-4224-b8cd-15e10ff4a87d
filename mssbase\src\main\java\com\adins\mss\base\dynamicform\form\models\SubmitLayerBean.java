package com.adins.mss.base.dynamicform.form.models;

import com.adins.mss.dao.TaskH;
import com.adins.mss.foundation.questiongenerator.QuestionBean;

import java.util.LinkedHashMap;
import java.util.List;

public class SubmitLayerBean {
    LinkedHashMap<String, List<QuestionBean>> questionBeanListOfGroup;
    TaskH taskH;
    List<QuestionBean> questionBeans;

    public LinkedHashMap<String, List<QuestionBean>> getQuestionBeanListOfGroup() {
        return questionBeanListOfGroup;
    }

    public void setQuestionBeanListOfGroup(LinkedHashMap<String, List<QuestionBean>> questionBeanListOfGroup) {
        this.questionBeanListOfGroup = questionBeanListOfGroup;
    }

    public TaskH getTaskH() {
        return taskH;
    }

    public void setTaskH(TaskH taskH) {
        this.taskH = taskH;
    }

    public List<QuestionBean> getQuestionBeans() {
        return questionBeans;
    }

    public void setQuestionBeans(List<QuestionBean> questionBeans) {
        this.questionBeans = questionBeans;
    }
}
