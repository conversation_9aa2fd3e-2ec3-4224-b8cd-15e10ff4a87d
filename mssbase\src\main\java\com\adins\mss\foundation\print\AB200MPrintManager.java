package com.adins.mss.foundation.print;

import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.os.AsyncTask;
import android.widget.Toast;

import com.adins.mss.base.GlobalData;
import com.adins.mss.base.R;
import com.adins.mss.base.crashlytics.FireCrash;
import com.adins.mss.base.dynamicform.Constant;
import com.adins.mss.constant.Global;
import com.adins.mss.dao.PrintResult;
import com.adins.mss.foundation.security.storepreferences.ObscuredSharedPreferences;
import com.adins.mss.logger.Logger;
import com.bixolon.printer.print.BitmapManager;
import com.bixolon.printer.utility.Command;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Vector;

/**
 * Created by angga.permadi on 3/3/2016.
 */
public class AB200MPrintManager extends AbstractPrintManager {
    public static final String PRINT_PREFERENCES = "PRINT_PREFERENCES";
    public static final String PRINTER_KEY = "PRINTER_KEY";
    public static final String PRINTER_WOOSIM = "woosim";
    public static final int TEXT_ATTRIBUTE_EMPHASIZED = 1;

    private String deviceName;
    private PrinterConnect printerConnect;
    private String lastPrinterConnected;

    public AB200MPrintManager(Context context, List<PrintResult> list) {
        super(context, list);
        ObscuredSharedPreferences preferences = ObscuredSharedPreferences.getPrefs(context, PRINT_PREFERENCES, Context.MODE_PRIVATE);
        lastPrinterConnected = preferences.getString(PRINTER_KEY, "");
    }

    @Override
    public boolean isConnected() {
        return connected;
    }

    @Override
    public boolean isPrinterConnected() {
        return BluePrintDriver.isPrinterConnected();
    }

    @Override
    public void connect() throws Exception {
        if (printerConnect != null) {
            printerConnect.cancel(true);
            printerConnect = null;
        }

        printerConnect = new PrinterConnect();
        printerConnect.execute();
    }

    @Override
    public void connect(BluetoothDevice device) throws Exception {
        if (printerConnect != null) {
            printerConnect.cancel(true);
            printerConnect = null;
        }

        printerConnect = new PrinterConnect(device);
        printerConnect.execute();
    }

    @Override
    public void disconnect() throws Exception {
        BluePrintDriver.close();
        deviceName = null;
        connected = false;

        if (listener != null) {
            listener.onDisconnect();
        }
    }

    @Override
    public void printText(String text, int alignment, int attribute, int size, boolean s) {
        BluePrintDriver.InitPrinter();

        if (BluePrintDriver.IsNoConnection()) {
            return;
        }

        switch (attribute) {
            case AbstractPrintManager.TEXT_ATTRIBUTE_FONT_A:
                break;
            case AbstractPrintManager.TEXT_ATTRIBUTE_EMPHASIZED:
                BluePrintDriver.AddBold((byte) TEXT_ATTRIBUTE_EMPHASIZED);
                break;
            default:
                break;
        }

        switch (size) {
            case AbstractPrintManager.TEXT_SIZE_HORIZONTAL1:
                break;
        }

        BluePrintDriver.AddAlignMode((byte) alignment);
        BluePrintDriver.ImportData(text);
        if (!BluePrintDriver.excute()) {
            connected = false;
            Logger.d("", "onError print ");
        }
        BluePrintDriver.ClearData();
    }

    @Override
    public void printBitmap(Bitmap bitmap, int alignment, int width, int level, boolean s) {
        if (BluePrintDriver.IsNoConnection()) {
            return;
        }

        if (deviceName != null && deviceName.toLowerCase().contains(PRINTER_WOOSIM)) {
            try {
                String bmpPath = "/data/data/" + context.getPackageName() + "/print_logo.bmp";

                BluePrintDriver.woosimBitmap(bmpPath);
                BluePrintDriver.ClearData();
            } catch (IOException e) {
                FireCrash.log(e);
                e.printStackTrace();
            }
        } else {
            if (bitmap != null) {
                int MAX_WIDTH = 384;
                if (width == -1) {
                    width = MAX_WIDTH;
                } else if (width == 0 || width < 0) {
                    width = bitmap.getWidth();
                }

                if (width > MAX_WIDTH) {
                    width = MAX_WIDTH;
                }

                byte[] printerData1;
                try {
                    printerData1 = BitmapManager.bitmap2printerData(bitmap, width, level, 0);
                } catch (RuntimeException var9) {
                    var9.printStackTrace();
                    return;
                }

                int height = BitmapManager.getBitmapHeight(bitmap, width);

                int writingCount = 1;
                if (height > 1662) {
                    writingCount += height / 1662;
                }

                int widthBytes = BitmapManager.bytesOfWidth(width);

                for (int i = 0; i < writingCount; ++i) {
                    int writingHeight = height > 1662 * (i + 1) ? 1662 : height % 1662;
                    byte[] dst = new byte[widthBytes * writingHeight];
                    System.arraycopy(printerData1, widthBytes * 1662 * i, dst, 0, dst.length);

                    int capacity = Command.ALIGNMENT_LEFT.length + Command.RASTER_BIT_IMAGE_NORMAL.length + 4 + dst.length;
                    ByteBuffer buffer = ByteBuffer.allocate(capacity);

                    switch (alignment) {
                        case 1:
                            buffer.put(Command.ALIGNMENT_CENTER);
                            break;
                        case 2:
                            buffer.put(Command.ALIGNMENT_RIGHT);
                            break;
                        default:
                            buffer.put(Command.ALIGNMENT_LEFT);
                    }

                    buffer.put(Command.RASTER_BIT_IMAGE_NORMAL);
                    buffer.put((byte) (widthBytes % 256));
                    buffer.put((byte) (widthBytes / 256));
                    buffer.put((byte) (writingHeight % 256));
                    buffer.put((byte) (writingHeight / 256));
                    buffer.put(dst);

                    BluePrintDriver.printByteData(buffer.array());
                    BluePrintDriver.ClearData();
                }
            }
        }
    }

    @Override
    public void lineFeed(int lines, boolean s) {
        for (int i = 0; i < lines; i++) {
            BluePrintDriver.printString("");
        }
    }

    public void releaseResources() {
        super.releaseResources();
        deviceName = null;

        if (printerConnect != null) {
            printerConnect.cancel(true);
            printerConnect = null;
        }
    }

    /*PRINT SATO*/
    @Override
    public boolean printSato() throws Exception {
        if (connected) {
            byte[] logo = getByteFromImage("adira_new.prn");
            BluePrintDriver.ImportData(this.getDataToPrint(), logo);
            //BluePrintDriver.ImportData(this.getDataToPrint());
            if (!BluePrintDriver.excute()) {
                connected = false;
                Logger.d("", "onError print ");
            }
            BluePrintDriver.ClearData();
//            BluePrintDriver.setmCmdBuffer(new byte[1048576]);
//
//            BluePrintDriver.ImportData(this.getDataToPrint());
//            if (!BluePrintDriver.excute()) {
//                connected = false;
//                Logger.d("", "onError print ");
//            }
//            BluePrintDriver.ClearData();

        } else {
            throw new Exception("Device is not connected to the Sato printer");
        }
        return true;
    }

    private byte[] getByteFromImage(String image) throws IOException {

        byte[] imgBytes = null;
        ByteArrayOutputStream baos = null;
        AssetManager am = context.getResources().getAssets();
        InputStream in = null;
        try {

            baos = new ByteArrayOutputStream();
            // this image is inside my mobile application
            //in = this.getClass().getResourceAsStream(imgPath);
            in = am.open(image); // <<--- image = nama file .prn yang taruh di asset
            byte[] buffer = new byte[4096];
            int n = 0;
            while ((n = in.read(buffer)) != -1) {
                baos.write(buffer, 0, n);
            }

            imgBytes = baos.toByteArray();

        } catch (Exception ex) {
            FireCrash.log(ex);
            ex.printStackTrace();
        } finally {
            // whatever happends close the streams
            if (baos != null) {
                try {
                    baos.close();
                } catch (Exception ex) {
                    FireCrash.log(ex);
                }
            }
            if (in != null) {
                try {
                    in.close();
                } catch (Exception ex) {
                    FireCrash.log(ex);
                }
            }
        }
        byte a[] = new byte[]{2, 27, 65, 27, 73, 71, 49, 27, 80, 83};
        byte b[] = new byte[]{27, 81, 48, 48, 48, 49, 27, 90, 3};

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        outputStream.write(a);
        outputStream.write(imgBytes);
        outputStream.write(b);

        byte c[] = outputStream.toByteArray();
        return c;
    }

    private String getDataToPrint() throws Exception {
        int verticalPosition = 96 - 33; // 96, sebagai posisi awal
        char start = (char) 27;
        char begin = (char) 2;
        char end = (char) 3;
       /* byte[] logo = getByteFromImage("adira_new.prn");
        String logoStrg = new String(logo);*/

        StringBuffer sbToPrint = new StringBuffer();
        sbToPrint.append(begin).append(start);
        /*sbToPrint.append("A").append(start).append("IG1").append(start)
                .append("PS");*/
        // sbToPrint.append(start).append(logoStrg);
        //for (PrintItemBean bean : Constant.listOfPrintItem) {
        for (PrintResult bean : Constant.listOfPrintItem) {

            //String type = bean.getType();
            String type = bean.getPrint_type_id();
            String vPosition = "";

            if ((verticalPosition + 33) < 100) {
                verticalPosition = verticalPosition + 33;
                vPosition = "00" + verticalPosition;
                // sbToPrint.append(start).append("L00"+horizontalPosition+"000101111000");
            } else if ((verticalPosition + 33) < 1000) {
                verticalPosition = verticalPosition + 33;
                vPosition = "0" + verticalPosition;
                // sbToPrint.append(start).append("L0"+horizontalPosition+"000101111000");
            } else {
                vPosition = "" + verticalPosition;
                verticalPosition = verticalPosition + 33;

            }
            if (Global.PRINT_NEW_LINE.equals(type)) {
                String label = bean.getLabel();
                if ("".equals(label) || " ".equals(label)) {
                    sbToPrint.append(start).append(
                            "L" + vPosition + "000101111000");
                    sbToPrint.append(start).append("D" + label);
                } else {
                    // ***untuk label gak ti wrap karena akan
                    // menghilangkan spasi,
                    // so konsekwensinya harus di pas-in dulu dari
                    // server..
                    // cth "     SIMPAN TANDA BUKTI INI"
                    sbToPrint.append(start).append(
                            "L" + vPosition + "000101111000");
                    sbToPrint.append(start).append("D" + label);
                }
            } else if (Global.PRINT_LABEL_CENTER.equals(type)) {
                String label = bean.getLabel();
                Vector vt;
                // nilai sebenarnya 384, dikurangi aj bt jaga2 kalo slah perhtungan
                vt = FontSato.wrap(374, label.trim());

                for (int j = 0; j < vt.size(); j++) {
                    SentencesSato setn = null;

                    try {
                        setn = (SentencesSato) vt.elementAt(j);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        if (Global.IS_DEV) Logger.i("INFO", "eato " + e);
                    }

                    // ------------- agar posisi bisa ditengah,
                    int iHposition = (384 - setn.getLenghtSentemce()) / 2;
                    String sHposition = "0";

                    if (iHposition < 10) {
                        sHposition = "000" + iHposition;
                    } else if (iHposition < 100) {
                        sHposition = "00" + iHposition;
                    } else if (iHposition < 1000) {
                        sHposition = "0" + iHposition;
                    }
                    // -------------

                    if (j == 0) {
                        sbToPrint.append(start).append("L"
                                + vPosition
                                + sHposition
                                + "01111000");
                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());

                    } else {
                        sbToPrint.append(start).append("L"
                                + vPrintPosition(verticalPosition)
                                + sHposition
                                + "01111000");
                        verticalPosition = verticalPosition + 33;

                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());

                    }

                }
            } else if (Global.PRINT_LABEL_CENTER_BOLD.equals(type)) {
                String label = bean.getLabel();
                Vector vt;
                // nilai sebenarnya 384, dikurangi aj bt jaga2 kalo slah perhtungan
                vt = FontSato.wrap(374, label.trim());

                for (int j = 0; j < vt.size(); j++) {
                    SentencesSato setn = null;

                    try {
                        setn = (SentencesSato) vt.elementAt(j);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        if (Global.IS_DEV) Logger.i("INFO", "eato " + e);
                    }

                    // ------------- agar posisi bisa ditengah,
                    int iHposition = (384 - setn.getLenghtSentemce()) / 2;
                    String sHposition = "0";

                    if (iHposition < 10) {
                        sHposition = "000" + iHposition;
                    } else if (iHposition < 100) {
                        sHposition = "00" + iHposition;
                    } else if (iHposition < 1000) {
                        sHposition = "0" + iHposition;
                    }
                    // -------------

                    if (j == 0) {
                        sbToPrint.append(start).append("L"
                                + vPosition
                                + sHposition
                                + "01111000");
                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());

                    } else {
                        sbToPrint.append(start).append("L"
                                + vPrintPosition(verticalPosition)
                                + sHposition
                                + "01111000");

                        verticalPosition = verticalPosition + 33;

                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());

                    }

                }
            } else if (Global.PRINT_LABEL.equals(type)) {
                String label = bean.getLabel();

                Vector vt;
                // nilai sebenarnya 384, dikurangi aj bt jaga2 kalo slah perhtungan
                vt = FontSato.wrap(374, label.trim());

                for (int j = 0; j < vt.size(); j++) {
                    SentencesSato setn = null;

                    try {
                        setn = (SentencesSato) vt.elementAt(j);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        if (Global.IS_DEV) Logger.i("INFO", "sato " + e);
                    }

                    if (j == 0) {
                        sbToPrint.append(start).append("L"
                                + vPosition
                                + "000101111000");
                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());

                    } else {
                        sbToPrint.append(start).append("L"
                                + vPrintPosition(verticalPosition)
                                + "000101111000");

                        verticalPosition = verticalPosition + 33;

                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());
                    }
                }
            } else if (Global.PRINT_LABEL_BOLD.equals(type)) {
                String label = bean.getLabel();

                Vector vt;
                // nilai sebenarnya 384, dikurangi aj bt jaga2 kalo slah perhtungan
                vt = FontSato.wrap(374, label.trim());

                for (int j = 0; j < vt.size(); j++) {
                    SentencesSato setn = null;

                    try {
                        setn = (SentencesSato) vt.elementAt(j);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        if (Global.IS_DEV) Logger.i("INFO", "sato " + e);
                    }

                    if (j == 0) {
                        sbToPrint.append(start).append("L"
                                + vPosition
                                + "000101111000");
                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());

                    } else {
                        sbToPrint.append(start).append("L"
                                + vPrintPosition(verticalPosition)
                                + "000101111000");

                        verticalPosition = verticalPosition + 33;

                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());
                    }
                }
            } else if (Global.PRINT_ANSWER.equals(type)) {
                String label = bean.getLabel();
                String answer = bean.getValue();
                if (answer != null && !answer.equals("")) {
                    if (label.equals("")
                            || label == null
                            || label.equals("null")) { // jika pertanyaan tidak ada labelnya maka posisi ditengahkan

                        Vector vt;
                        // nilai sebenarnya 384, dikurangi aj bt jaga2 kalo slah perhtungan
                        vt = FontSato.wrap(374, answer.trim());

                        for (int j = 0; j < vt.size(); j++) {

                            SentencesSato setn = null;

                            try {
                                setn = (SentencesSato) vt.elementAt(j);
                            } catch (Exception e) {
                                FireCrash.log(e);
                                if (Global.IS_DEV) Logger.i("INFO", "sato " + e);
                            }

                            // ------------- agar posisi bisa ditengah,
                            int iHposition = (384 - setn.getLenghtSentemce()) / 2;
                            String sHposition = "0";

                            if (iHposition < 10) {
                                sHposition = "000" + iHposition;
                            } else if (iHposition < 100) {
                                sHposition = "00" + iHposition;
                            } else if (iHposition < 1000) {
                                sHposition = "0" + iHposition;
                            }
                            // -------------

                            if (j == 0) {
                                sbToPrint.append(start).append("L"
                                        + vPosition
                                        + sHposition
                                        + "01111000");
                                sbToPrint.append(start).append("D"
                                        + setn.getSentence().trim());
                            } else {
                                sbToPrint.append(start).append("L"
                                        + vPrintPosition(verticalPosition)
                                        + sHposition
                                        + "01111000");
                                sbToPrint.append(start).append("D"
                                        + setn.getSentence().trim());
                                verticalPosition = verticalPosition + 33;
                            }
                        }

                    } else {

                        sbToPrint.append(start).append("L"
                                + vPosition
                                + "000101111000");
                        sbToPrint.append(start).append("D" + label);

                        sbToPrint.append(start).append("L"
                                + vPosition
                                + "020001111000");
                        sbToPrint.append(start).append("D:");

                        Vector vt;

                        vt = FontSato.wrap(166, answer.trim()); // panjang area yang akan diprint

                        for (int j = 0; j < vt.size(); j++) {

                            SentencesSato setn = null;

                            try {
                                setn = (SentencesSato) vt.elementAt(j);
                            } catch (Exception e) {
                                FireCrash.log(e);
                                if (Global.IS_DEV) Logger.i("INFO", "sato " + e);
                            }
                            if (j == 0) {
                                sbToPrint.append(start).append("L"
                                        + vPosition
                                        + "0208"
                                        + "01111000");
                                sbToPrint.append(start).append("D"
                                        + setn.getSentence().trim());
                            } else {
                                sbToPrint.append(start).append("L"
                                        + vPrintPosition(verticalPosition)
                                        + "0208"
                                        + "01111000");
                                verticalPosition = verticalPosition + 33;
                                sbToPrint.append(start).append("D"
                                        + setn.getSentence().trim());
                            }

                        }
                    }
                } else {
                    vPosition = "";
                    if ((verticalPosition - 33) < 100) {
                        verticalPosition = verticalPosition - 33;
                        vPosition = "00" + verticalPosition;
                        // sbToPrint.append(start).append("L00"+horizontalPosition+"000101111000");
                    } else if ((verticalPosition + 33) < 1000) {
                        verticalPosition = verticalPosition - 33;
                        vPosition = "0" + verticalPosition;
                        // sbToPrint.append(start).append("L0"+horizontalPosition+"000101111000");
                    } else {
                        vPosition = "" + verticalPosition;
                        verticalPosition = verticalPosition - 33;

                    }
                }
            } else if (Global.PRINT_USER_NAME.equals(type)) {
                String label = bean.getLabel();
                String answer = GlobalData.getSharedGlobalData().getUser().getFullname();

                sbToPrint.append(start).append("L"
                        + vPosition
                        + "000101111000");
                sbToPrint.append(start).append("D" + label);

                sbToPrint.append(start).append("L"
                        + vPosition
                        + "020001111000");
                sbToPrint.append(start).append("D:");

                Vector vt;

                vt = FontSato.wrap(166, answer.trim()); // panjang area yang akan diprint

                for (int j = 0; j < vt.size(); j++) {

                    SentencesSato setn = null;

                    try {
                        setn = (SentencesSato) vt.elementAt(j);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        if (Global.IS_DEV) Logger.i("INFO", "sato " + e);
                    }
                    if (j == 0) {
                        sbToPrint.append(start).append("L"
                                + vPosition
                                + "0208"
                                + "01111000");
                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());
                    } else {
                        sbToPrint.append(start).append("L"
                                + vPrintPosition(verticalPosition)
                                + "0208"
                                + "01111000");
                        verticalPosition = verticalPosition + 33;
                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());
                    }

                }
            } else if (Global.PRINT_LOGIN_ID.equals(type)) {
                String label = bean.getLabel();
                String answer = GlobalData.getSharedGlobalData().getUser().getLogin_id();

                sbToPrint.append(start).append("L"
                        + vPosition
                        + "000101111000");
                sbToPrint.append(start).append("D" + label);

                sbToPrint.append(start).append("L"
                        + vPosition
                        + "020001111000");
                sbToPrint.append(start).append("D:");

                Vector vt;

                vt = FontSato.wrap(166, answer.trim()); // panjang area yang akan diprint

                for (int j = 0; j < vt.size(); j++) {

                    SentencesSato setn = null;

                    try {
                        setn = (SentencesSato) vt.elementAt(j);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        if (Global.IS_DEV) Logger.i("INFO", "sato " + e);
                    }
                    if (j == 0) {
                        sbToPrint.append(start).append("L"
                                + vPosition
                                + "0208"
                                + "01111000");
                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());
                    } else {
                        sbToPrint.append(start).append("L"
                                + vPrintPosition(verticalPosition)
                                + "0208"
                                + "01111000");
                        verticalPosition = verticalPosition + 33;
                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());
                    }

                }
            } else if (Global.PRINT_BRANCH_NAME.equals(type)) {
                String label = bean.getLabel();
                Vector vt;
                // nilai sebenarnya 384, dikurangi aj bt jaga2 kalo slah perhtungan
                vt = FontSato.wrap(374, label.trim());

                for (int j = 0; j < vt.size(); j++) {
                    SentencesSato setn = null;

                    try {
                        setn = (SentencesSato) vt.elementAt(j);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        if (Global.IS_DEV) Logger.i("INFO", "eato " + e);
                    }

                    // ------------- agar posisi bisa ditengah,
                    int iHposition = (384 - setn.getLenghtSentemce()) / 2;
                    String sHposition = "0";

                    if (iHposition < 10) {
                        sHposition = "000" + iHposition;
                    } else if (iHposition < 100) {
                        sHposition = "00" + iHposition;
                    } else if (iHposition < 1000) {
                        sHposition = "0" + iHposition;
                    }
                    // -------------

                    if (j == 0) {
                        sbToPrint.append(start).append("L"
                                + vPosition
                                + sHposition
                                + "01111000");
                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());

                    } else {
                        sbToPrint.append(start).append("L"
                                + vPrintPosition(verticalPosition)
                                + sHposition
                                + "01111000");
                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());

                        verticalPosition = verticalPosition + 33;
                    }

                }
            } else if (Global.PRINT_BRANCH_ADDRESS.equals(type)) {
                String label = bean.getLabel();
                Vector vt;
                // nilai sebenarnya 384, dikurangi aj bt jaga2 kalo slah perhtungan
                vt = FontSato.wrap(374, label.trim());

                for (int j = 0; j < vt.size(); j++) {
                    SentencesSato setn = null;

                    try {
                        setn = (SentencesSato) vt.elementAt(j);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        if (Global.IS_DEV) Logger.i("INFO", "eato " + e);
                    }

                    // ------------- agar posisi bisa ditengah,
                    int iHposition = (384 - setn.getLenghtSentemce()) / 2;
                    String sHposition = "0";

                    if (iHposition < 10) {
                        sHposition = "000" + iHposition;
                    } else if (iHposition < 100) {
                        sHposition = "00" + iHposition;
                    } else if (iHposition < 1000) {
                        sHposition = "0" + iHposition;
                    }
                    // -------------

                    if (j == 0) {
                        sbToPrint.append(start).append("L"
                                + vPosition
                                + sHposition
                                + "01111000");
                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());

                    } else {
                        sbToPrint.append(start).append("L"
                                + vPrintPosition(verticalPosition)
                                + sHposition
                                + "01111000");
                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());

                        verticalPosition = verticalPosition + 33;
                    }

                }
            } else if (Global.PRINT_BT_ID.equals(type)) {
                String label = bean.getLabel();
                String answer = bean.getValue();
                ;

                sbToPrint.append(start).append("L"
                        + vPosition
                        + "000101111000");
                sbToPrint.append(start).append("D" + label);

                sbToPrint.append(start).append("L"
                        + vPosition
                        + "020001111000");
                sbToPrint.append(start).append("D:");

                Vector vt;

                vt = FontSato.wrap(166, answer.trim()); // panjang area yang akan diprint

                for (int j = 0; j < vt.size(); j++) {

                    SentencesSato setn = null;

                    try {
                        setn = (SentencesSato) vt.elementAt(j);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        if (Global.IS_DEV) Logger.i("INFO", "sato " + e);
                    }
                    if (j == 0) {
                        sbToPrint.append(start).append("L"
                                + vPosition
                                + "0208"
                                + "01111000");
                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());
                    } else {
                        sbToPrint.append(start).append("L"
                                + vPrintPosition(verticalPosition)
                                + "0208"
                                + "01111000");
                        verticalPosition = verticalPosition + 33;
                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());
                    }

                }
            } else if (Global.PRINT_TIMESTAMP.equals(type)) {
                SimpleDateFormat df = new SimpleDateFormat("dd-MMM-yyyy hh:mm");
                Date date = new Date();
                String answer = df.format(date);
                String label = bean.getLabel();

                sbToPrint.append(start).append("L"
                        + vPosition
                        + "000101111000");
                sbToPrint.append(start).append("D" + label);

                sbToPrint.append(start).append("L"
                        + vPosition
                        + "020001111000");
                sbToPrint.append(start).append("D:");

                Vector vt;

                vt = FontSato.wrap(166, answer.trim()); // panjang area yang akan diprint

                for (int j = 0; j < vt.size(); j++) {

                    SentencesSato setn = null;

                    try {
                        setn = (SentencesSato) vt.elementAt(j);
                    } catch (Exception e) {
                        FireCrash.log(e);
                        if (Global.IS_DEV) Logger.i("INFO", "sato " + e);
                    }
                    if (j == 0) {
                        sbToPrint.append(start).append("L"
                                + vPosition
                                + "0208"
                                + "01111000");
                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());
                    } else {
                        sbToPrint.append(start).append("L"
                                + vPrintPosition(verticalPosition)
                                + "0208"
                                + "01111000");
                        verticalPosition = verticalPosition + 33;
                        sbToPrint.append(start).append("D"
                                + setn.getSentence().trim());
                    }

                }
            }
        }
        // ENDING LINE
        verticalPosition = verticalPosition + 33;
        sbToPrint.append(start).append(
                "L" + vPrintPosition(verticalPosition) + "000101222000");
        verticalPosition = verticalPosition + 33;
        sbToPrint.append(start).append("D----------------");

        sbToPrint.append(start).append("Q0001").append(start).append("Z");
        sbToPrint.append(end);

        return sbToPrint.toString();
    }

    public String vPrintPosition(int verticalPosition) {
        String vPosition = "";
        if ((verticalPosition + 33) < 100) {
            verticalPosition = verticalPosition + 33;
            vPosition = "00" + verticalPosition;
        } else if ((verticalPosition + 33) < 1000) {
            verticalPosition = verticalPosition + 33;
            vPosition = "0" + verticalPosition;
        } else {
            vPosition = "" + verticalPosition;
            verticalPosition = verticalPosition + 33;
        }

        return vPosition;
    }

    public class PrinterConnect extends AsyncTask<Void, Void, Boolean> {
        String selectedPrinter;
        BluetoothDevice inDevice;

        public PrinterConnect() {
        }

        public PrinterConnect(BluetoothDevice inDevice) {
            this.inDevice = inDevice;
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();

            if (listener != null) {
                listener.onConnecting();
            }
        }

        @Override
        protected Boolean doInBackground(Void... params) {
            if (inDevice == null) {
                if (pairedDevices != null && !pairedDevices.isEmpty()) {
                    List<BluetoothDevice> pairedPrinters = new ArrayList<>();
                    if (lastPrinterConnected != null && !lastPrinterConnected.isEmpty()) {
                        for (BluetoothDevice device : pairedDevices) {
                            if (device.getAddress().equals(lastPrinterConnected))
                                pairedPrinters.add(0, device);
                            else
                                pairedPrinters.add(device);
                        }
                    } else {
                        pairedPrinters.addAll(pairedDevices);
                    }
                    for (BluetoothDevice device : pairedPrinters) {
                        if (device != null) {
                            Logger.d("Device Address : ", device.getAddress() + ", Device Name : " + device.getName());

                            BluePrintDriver.close();
                            if (!BluePrintDriver.OpenPrinter(device.getAddress())) {
                                BluePrintDriver.close();
                            } else {
                                deviceName = device.getName() == null ? device.getAddress() : device.getName();
                                selectedPrinter = device.getAddress();
                                return true;
                            }
                        }
                    }
                }
                return false;
            } else {
                Logger.d("Device Address : ", inDevice.getAddress() + ", Device Name : " + inDevice.getName());

                BluePrintDriver.close();
                if (!BluePrintDriver.OpenPrinter(inDevice.getAddress())) {
                    BluePrintDriver.close();
                } else {
                    deviceName = inDevice.getName() == null ? inDevice.getAddress() : inDevice.getName();
                    selectedPrinter = inDevice.getAddress();
                    return true;
                }
                return false;
            }
        }

        @Override
        protected void onPostExecute(Boolean aBoolean) {
            super.onPostExecute(aBoolean);

            if (aBoolean) {
                connected = true;
                if (listener != null) {
                    listener.onConnected(deviceName);
                }
                if (selectedPrinter != null && !selectedPrinter.isEmpty()) {
                    ObscuredSharedPreferences preferences = ObscuredSharedPreferences.getPrefs(context, PRINT_PREFERENCES, Context.MODE_PRIVATE);
                    ObscuredSharedPreferences.Editor sharedPrefEditor = preferences.edit();
                    sharedPrefEditor.putString(PRINTER_KEY, selectedPrinter);
                    sharedPrefEditor.commit();
                    lastPrinterConnected = selectedPrinter;
                }
            } else {
                Toast.makeText(context, context.getString(R.string.no_paired_device), Toast.LENGTH_SHORT).show();
                connected = false;
                if (listener != null) {
                    listener.onConnectFailed();
                }
            }
        }
    }
}

