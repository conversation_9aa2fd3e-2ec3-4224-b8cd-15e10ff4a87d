package com.adins.mss.dao;

import java.util.List;
import java.util.ArrayList;
import android.database.Cursor;

import de.greenrobot.dao.AbstractDao;
import de.greenrobot.dao.Property;
import de.greenrobot.dao.internal.SqlUtils;
import de.greenrobot.dao.internal.DaoConfig;
import de.greenrobot.dao.database.Database;
import de.greenrobot.dao.database.DatabaseStatement;
import de.greenrobot.dao.query.Query;
import de.greenrobot.dao.query.QueryBuilder;

import com.adins.mss.dao.ReceiptVoucher;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "TR_RECEIPTVOUCHER".
*/
public class ReceiptVoucherDao extends AbstractDao<ReceiptVoucher, String> {

    public static final String TABLENAME = "TR_RECEIPTVOUCHER";

    /**
     * Properties of entity ReceiptVoucher.<br/>
     * Can be used for QueryBuilder and for referencing column names.
    */
    public static class Properties {
        public final static Property Uuid_receipt_voucher = new Property(0, String.class, "uuid_receipt_voucher", true, "UUID_RECEIPT_VOUCHER");
        public final static Property Rv_status = new Property(1, String.class, "rv_status", false, "RV_STATUS");
        public final static Property Rv_number = new Property(2, String.class, "rv_number", false, "RV_NUMBER");
        public final static Property Usr_crt = new Property(3, String.class, "usr_crt", false, "USR_CRT");
        public final static Property Dtm_crt = new Property(4, java.util.Date.class, "dtm_crt", false, "DTM_CRT");
        public final static Property Dtm_use = new Property(5, java.util.Date.class, "dtm_use", false, "DTM_USE");
        public final static Property Uuid_user = new Property(6, String.class, "uuid_user", false, "UUID_USER");
        public final static Property Uuid_task_h = new Property(7, String.class, "uuid_task_h", false, "UUID_TASK_H");
    };

    private DaoSession daoSession;

    private Query<ReceiptVoucher> user_ReceiptVoucherListQuery;
    private Query<ReceiptVoucher> taskH_ReceiptVoucherListQuery;

    public ReceiptVoucherDao(DaoConfig config) {
        super(config);
    }
    
    public ReceiptVoucherDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
        this.daoSession = daoSession;
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"TR_RECEIPTVOUCHER\" (" + //
                "\"UUID_RECEIPT_VOUCHER\" TEXT PRIMARY KEY NOT NULL ," + // 0: uuid_receipt_voucher
                "\"RV_STATUS\" TEXT," + // 1: rv_status
                "\"RV_NUMBER\" TEXT," + // 2: rv_number
                "\"USR_CRT\" TEXT," + // 3: usr_crt
                "\"DTM_CRT\" INTEGER," + // 4: dtm_crt
                "\"DTM_USE\" INTEGER," + // 5: dtm_use
                "\"UUID_USER\" TEXT," + // 6: uuid_user
                "\"UUID_TASK_H\" TEXT);"); // 7: uuid_task_h
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"TR_RECEIPTVOUCHER\"";
        db.execSQL(sql);
    }

    /** @inheritdoc */
    @Override
    protected void bindValues(DatabaseStatement stmt, ReceiptVoucher entity) {
        stmt.clearBindings();
        stmt.bindString(1, entity.getUuid_receipt_voucher());
 
        String rv_status = entity.getRv_status();
        if (rv_status != null) {
            stmt.bindString(2, rv_status);
        }
 
        String rv_number = entity.getRv_number();
        if (rv_number != null) {
            stmt.bindString(3, rv_number);
        }
 
        String usr_crt = entity.getUsr_crt();
        if (usr_crt != null) {
            stmt.bindString(4, usr_crt);
        }
 
        java.util.Date dtm_crt = entity.getDtm_crt();
        if (dtm_crt != null) {
            stmt.bindLong(5, dtm_crt.getTime());
        }
 
        java.util.Date dtm_use = entity.getDtm_use();
        if (dtm_use != null) {
            stmt.bindLong(6, dtm_use.getTime());
        }
 
        String uuid_user = entity.getUuid_user();
        if (uuid_user != null) {
            stmt.bindString(7, uuid_user);
        }
 
        String uuid_task_h = entity.getUuid_task_h();
        if (uuid_task_h != null) {
            stmt.bindString(8, uuid_task_h);
        }
    }

    @Override
    protected void attachEntity(ReceiptVoucher entity) {
        super.attachEntity(entity);
        entity.__setDaoSession(daoSession);
    }

    /** @inheritdoc */
    @Override
    public String readKey(Cursor cursor, int offset) {
        return cursor.getString(offset + 0);
    }    

    /** @inheritdoc */
    @Override
    public ReceiptVoucher readEntity(Cursor cursor, int offset) {
        ReceiptVoucher entity = new ReceiptVoucher( //
            cursor.getString(offset + 0), // uuid_receipt_voucher
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // rv_status
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // rv_number
            cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3), // usr_crt
            cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)), // dtm_crt
            cursor.isNull(offset + 5) ? null : new java.util.Date(cursor.getLong(offset + 5)), // dtm_use
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // uuid_user
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7) // uuid_task_h
        );
        return entity;
    }
     
    /** @inheritdoc */
    @Override
    public void readEntity(Cursor cursor, ReceiptVoucher entity, int offset) {
        entity.setUuid_receipt_voucher(cursor.getString(offset + 0));
        entity.setRv_status(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setRv_number(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setUsr_crt(cursor.isNull(offset + 3) ? null : cursor.getString(offset + 3));
        entity.setDtm_crt(cursor.isNull(offset + 4) ? null : new java.util.Date(cursor.getLong(offset + 4)));
        entity.setDtm_use(cursor.isNull(offset + 5) ? null : new java.util.Date(cursor.getLong(offset + 5)));
        entity.setUuid_user(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setUuid_task_h(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
     }
    
    /** @inheritdoc */
    @Override
    protected String updateKeyAfterInsert(ReceiptVoucher entity, long rowId) {
        return entity.getUuid_receipt_voucher();
    }
    
    /** @inheritdoc */
    @Override
    public String getKey(ReceiptVoucher entity) {
        if(entity != null) {
            return entity.getUuid_receipt_voucher();
        } else {
            return null;
        }
    }

    /** @inheritdoc */
    @Override    
    protected boolean isEntityUpdateable() {
        return true;
    }
    
    /** Internal query to resolve the "receiptVoucherList" to-many relationship of User. */
    public List<ReceiptVoucher> _queryUser_ReceiptVoucherList(String uuid_user) {
        synchronized (this) {
            if (user_ReceiptVoucherListQuery == null) {
                QueryBuilder<ReceiptVoucher> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_user.eq(null));
                user_ReceiptVoucherListQuery = queryBuilder.build();
            }
        }
        Query<ReceiptVoucher> query = user_ReceiptVoucherListQuery.forCurrentThread();
        query.setParameter(0, uuid_user);
        return query.list();
    }

    /** Internal query to resolve the "receiptVoucherList" to-many relationship of TaskH. */
    public List<ReceiptVoucher> _queryTaskH_ReceiptVoucherList(String uuid_task_h) {
        synchronized (this) {
            if (taskH_ReceiptVoucherListQuery == null) {
                QueryBuilder<ReceiptVoucher> queryBuilder = queryBuilder();
                queryBuilder.where(Properties.Uuid_task_h.eq(null));
                taskH_ReceiptVoucherListQuery = queryBuilder.build();
            }
        }
        Query<ReceiptVoucher> query = taskH_ReceiptVoucherListQuery.forCurrentThread();
        query.setParameter(0, uuid_task_h);
        return query.list();
    }

    private String selectDeep;

    protected String getSelectDeep() {
        if (selectDeep == null) {
            StringBuilder builder = new StringBuilder("SELECT ");
            SqlUtils.appendColumns(builder, "T", getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T0", daoSession.getUserDao().getAllColumns());
            builder.append(',');
            SqlUtils.appendColumns(builder, "T1", daoSession.getTaskHDao().getAllColumns());
            builder.append(" FROM TR_RECEIPTVOUCHER T");
            builder.append(" LEFT JOIN MS_USER T0 ON T.\"UUID_USER\"=T0.\"UUID_USER\"");
            builder.append(" LEFT JOIN TR_TASK_H T1 ON T.\"UUID_TASK_H\"=T1.\"UUID_TASK_H\"");
            builder.append(' ');
            selectDeep = builder.toString();
        }
        return selectDeep;
    }
    
    protected ReceiptVoucher loadCurrentDeep(Cursor cursor, boolean lock) {
        ReceiptVoucher entity = loadCurrent(cursor, 0, lock);
        int offset = getAllColumns().length;

        User user = loadCurrentOther(daoSession.getUserDao(), cursor, offset);
        entity.setUser(user);
        offset += daoSession.getUserDao().getAllColumns().length;

        TaskH taskH = loadCurrentOther(daoSession.getTaskHDao(), cursor, offset);
        entity.setTaskH(taskH);

        return entity;    
    }

    public ReceiptVoucher loadDeep(Long key) {
        assertSinglePk();
        if (key == null) {
            return null;
        }

        StringBuilder builder = new StringBuilder(getSelectDeep());
        builder.append("WHERE ");
        SqlUtils.appendColumnsEqValue(builder, "T", getPkColumns());
        String sql = builder.toString();
        
        String[] keyArray = new String[] { key.toString() };
        Cursor cursor = db.rawQuery(sql, keyArray);
        
        try {
            boolean available = cursor.moveToFirst();
            if (!available) {
                return null;
            } else if (!cursor.isLast()) {
                throw new IllegalStateException("Expected unique result, but count was " + cursor.getCount());
            }
            return loadCurrentDeep(cursor, true);
        } finally {
            cursor.close();
        }
    }
    
    /** Reads all available rows from the given cursor and returns a list of new ImageTO objects. */
    public List<ReceiptVoucher> loadAllDeepFromCursor(Cursor cursor) {
        int count = cursor.getCount();
        List<ReceiptVoucher> list = new ArrayList<ReceiptVoucher>(count);
        
        if (cursor.moveToFirst()) {
            if (identityScope != null) {
                identityScope.lock();
                identityScope.reserveRoom(count);
            }
            try {
                do {
                    list.add(loadCurrentDeep(cursor, false));
                } while (cursor.moveToNext());
            } finally {
                if (identityScope != null) {
                    identityScope.unlock();
                }
            }
        }
        return list;
    }
    
    protected List<ReceiptVoucher> loadDeepAllAndCloseCursor(Cursor cursor) {
        try {
            return loadAllDeepFromCursor(cursor);
        } finally {
            cursor.close();
        }
    }
    

    /** A raw-style query where you can pass any WHERE clause and arguments. */
    public List<ReceiptVoucher> queryDeep(String where, String... selectionArg) {
        Cursor cursor = db.rawQuery(getSelectDeep() + where, selectionArg);
        return loadDeepAllAndCloseCursor(cursor);
    }
 
}
