package com.adins.mss.foundation.db.dataaccess;

import android.content.Context;

import com.adins.mss.dao.DaoSession;
import com.adins.mss.dao.TaskHDao;
import com.adins.mss.dao.TaskUpdate;
import com.adins.mss.dao.TaskUpdateDao;
import com.adins.mss.foundation.db.DaoOpenHelper;

import java.util.List;

import de.greenrobot.dao.query.QueryBuilder;

import static com.adins.mss.foundation.db.dataaccess.TaskHDataAccess.STATUS_SEND_FAILED;
import static com.adins.mss.foundation.db.dataaccess.TaskHDataAccess.STATUS_SEND_SENT;
import static com.adins.mss.foundation.db.dataaccess.TaskHDataAccess.STATUS_SEND_UPLOADING;

public class TaskUpdateDataAccess {

    /**
     * use to generate dao session that you can access modelDao
     *
     * @param context --> context from activity
     * @return
     */
    protected static DaoSession getDaoSession(Context context){
        return DaoOpenHelper.getDaoSession(context);
    }

    /**
     * get taskUpdate dao and you can access the DB
     *
     * @param context
     * @return
     */
    protected static TaskUpdateDao getTaskUpdateDao(Context context) {
        return getDaoSession(context).getTaskUpdateDao();
    }

    /**
     * Clear session, close db and set daoOpenHelper to null
     *
     */
    public static void closeAll(){
        DaoOpenHelper.closeAll();
    }

    /**
     * add taskUpdate as entity
     *
     * @param context
     * @param taskUpdate
     *
     *
     */
    public static void add(Context context, TaskUpdate taskUpdate){
        getTaskUpdateDao(context).insert(taskUpdate);
        getDaoSession(context).clear();
    }

    /**
     * add taskH as list entity
     *
     * @param context
     * @param taskUpdateList
     */
    public static void add(Context context, List<TaskUpdate> taskUpdateList){
        getTaskUpdateDao(context).insertInTx(taskUpdateList);
        getDaoSession(context).clear();
    }
    /**
     * add or replace data taskH
     * @param context
     * @param taskUpdate
     */
    public static void addOrReplace(Context context, TaskUpdate taskUpdate){
        getTaskUpdateDao(context).insertOrReplaceInTx(taskUpdate);
        getDaoSession(context).clear();
    }

    /**
     * add or replace list data taskH
     *
     * @param context
     * @param taskUpdateList
     */
    public static void addOrReplace(Context context, List<TaskUpdate> taskUpdateList){
        getTaskUpdateDao(context).insertOrReplaceInTx(taskUpdateList);
        getDaoSession(context).clear();
    }

    /**
     *
     * delete all content in table.
     *
     * @param context
     */
    public static void clean(Context context){
        getTaskUpdateDao(context).deleteAll();
        getDaoSession(context).clear();
    }

    /**
     * @param context
     * @param taskUpdate
     */
    public static void delete(Context context, TaskUpdate taskUpdate){
        getTaskUpdateDao(context).delete(taskUpdate);
        getDaoSession(context).clear();
    }

    /**
     * This method is used to retrieve one object of task header by uuidTaskH
     * @param context
     * @param uuid_task_update
     * @return
     */
    public static TaskUpdate getOneHeader(Context context, String uuid_task_update){
        QueryBuilder<TaskUpdate> qb = getTaskUpdateDao(context).queryBuilder();
        qb.where(TaskUpdateDao.Properties.Uuid_task_update.eq(uuid_task_update));
        qb.build().forCurrentThread();
        if(qb.list().isEmpty())
            return null;
        return qb.list().get(0);
    }

    public static void updateIsNotified(Context context, String uuid, Integer isNotified){
        TaskUpdate taskUpdate = getOneHeader(context, uuid);
        assert taskUpdate != null;
        taskUpdate.setIs_notified(isNotified);
        getTaskUpdateDao(context).update(taskUpdate);
    }

    public static List<TaskUpdate> getTaskByAppl(Context context, String applNo){
        QueryBuilder<TaskUpdate> qb = getTaskUpdateDao(context).queryBuilder();
        qb.where(TaskHDao.Properties.Appl_no.eq(applNo)
                ,TaskHDao.Properties.Status.notIn(STATUS_SEND_FAILED, STATUS_SEND_SENT, STATUS_SEND_UPLOADING));
        qb.build().forCurrentThread();
        return qb.list() ;
    }

    public static List<TaskUpdate> getAllWithAssignmentDate(Context context, String uuidUser){
        QueryBuilder<TaskUpdate> qb = getTaskUpdateDao(context).queryBuilder();
        qb.where(TaskUpdateDao.Properties.Uuid_user.eq(uuidUser),
                TaskUpdateDao.Properties.Assignment_date.isNotNull());
        qb.build();
        return qb.list();
    }

    public static long getTaskUpdateCounterByUser(Context context, String uuidUser){
        QueryBuilder<TaskUpdate> qb = getTaskUpdateDao(context).queryBuilder();
        qb.where(TaskUpdateDao.Properties.Uuid_user.eq(uuidUser));
        qb.build();
        return qb.list().size();
    }

    public static List<TaskUpdate> getAll(Context context, String uuidUser){
        QueryBuilder<TaskUpdate> qb = getTaskUpdateDao(context).queryBuilder();
        qb.where(TaskHDao.Properties.Uuid_user.eq(uuidUser));
        qb.build();
        return qb.list();
    }
}
