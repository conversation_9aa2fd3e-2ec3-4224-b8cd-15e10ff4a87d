package com.adins.mss.foundation.db.dataaccess;

import android.content.Context;

import com.adins.mss.dao.Blacklist;
import com.adins.mss.dao.BlacklistDao;
import com.adins.mss.dao.DaoSession;
import com.adins.mss.foundation.db.DaoOpenHelper;

import java.util.List;

import de.greenrobot.dao.query.QueryBuilder;
import de.greenrobot.dao.query.WhereCondition;

/**
 * Created by developer on 1/18/18.
 */

public class BlacklistDataAccess {

    /**
     * use to generate dao session that you can access modelDao
     *
     * @param context --> context from activity
     * @return
     */
    protected static DaoSession getDaoSession(Context context) {
        return DaoOpenHelper.getDaoSession(context);
    }

    /**
     * get blacklistDao and you can access DB
     */
    protected static BlacklistDao getBlacklistDao(Context context) {
        return getDaoSession(context).getBlacklistDao();
    }

    /**
     * add blacklist as entity
     */
    public static void add(Context context, Blacklist blacklist) {
        getBlacklistDao(context).insert(blacklist);
        getDaoSession(context).clear();
    }

    /**
     * add blacklist as list entity
     */
    public static void add(Context context, List<Blacklist> blacklists) {
        getBlacklistDao(context).insertInTx(blacklists);
        getDaoSession(context).clear();
    }

    /**
     * add or replace as entity
     */
    public static void addOrReplace(Context context, Blacklist blacklist) {
        getBlacklistDao(context).insertOrReplace(blacklist);
        getDaoSession(context).clear();
    }

    /**
     * add or replace as list
     */
    public static void addOrReplace(Context context, List<Blacklist> blacklists) {
        getBlacklistDao(context).insertOrReplaceInTx(blacklists);
        getDaoSession(context).clear();
    }

    /**
     * delete all blacklist
     */
    public static void clean(Context context) {
        getBlacklistDao(context).deleteAll();
        getDaoSession(context).clear();
    }

    /**
     * delete one entity
     */
    public static void delete(Context context, Blacklist blacklist) {
        getBlacklistDao(context).delete(blacklist);
        getDaoSession(context).clear();
    }

    /**
     * get all entity
     */
    public static List<Blacklist> all(Context context) {
        List<Blacklist> blacklists = getBlacklistDao(context).loadAll();
//        getDaoSession(context).clear();

        if (blacklists.isEmpty()) return null;
        else return blacklists;
    }

    /**
     * get one entity blacklist
     */
    public static boolean validatePhone(Context context, String param1, String param2) {
        QueryBuilder<Blacklist> qb = getBlacklistDao(context).queryBuilder();
        qb.where(BlacklistDao.Properties.Exclude_info1.eq(param1),
                BlacklistDao.Properties.Exclude_info2.eq(param2));
        qb.build();

        return !qb.list().isEmpty();
    }

    /**
     * get one entity blacklist
     */
    public static boolean validateName(Context context, String param1) {
        QueryBuilder<Blacklist> qb = getBlacklistDao(context).queryBuilder();
        qb.where(BlacklistDao.Properties.Exclude_info1.eq(param1));
        qb.build();

        return !qb.list().isEmpty();
    }

    /**
     * Validate Input
     */
    public static boolean validate(Context context, int type, Blacklist blacklist) {
        QueryBuilder<Blacklist> qb = getBlacklistDao(context).queryBuilder();

        if (type == Blacklist.TYPE_NAME) {
            qb.where(new WhereCondition.StringCondition("EXCLUDE_INFO2 = '" + blacklist.getExclude_info2().trim() + "' COLLATE NOCASE"));
//            qb.where(BlacklistDao.Properties.Exclude_info2.eq(blacklist.getExclude_info2()));
        }
        else if (type == Blacklist.TYPE_PHONE) {
            if (blacklist.getExclude_info1() != null) {
                qb.where(BlacklistDao.Properties.Exclude_info1.eq(blacklist.getExclude_info1().trim()));
            } else {
                qb.where(BlacklistDao.Properties.Exclude_info2.eq(blacklist.getExclude_info2().trim()));
            }
        }

        qb.where(BlacklistDao.Properties.Is_deleted.eq(0));
//        qb.where(BlacklistDao.Properties.Exclude_type_code.like("%"+ blacklist.getExclude_type_code() +"%"));
//        qb.orderRaw("ID COLLATE NOCASE ASC");
        qb.build();

        return !qb.list().isEmpty();
    }

    public static Blacklist getLast(Context context) {
        QueryBuilder<Blacklist> qb = getBlacklistDao(context).queryBuilder();
        qb.orderDesc(BlacklistDao.Properties.Dtm_upd);
        qb.limit(1);
        qb.build();

        if (!qb.list().isEmpty()) return qb.list().get(0);
        return null;
    }

    public static void addOrUpdateAll(Context context, List<Blacklist> transaction) {
            getBlacklistDao(context).insertOrReplaceInTx(transaction);
            getDaoSession(context).clear();
    }
}
