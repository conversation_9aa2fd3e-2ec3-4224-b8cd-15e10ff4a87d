package com.adins.mss.base.util;

import android.annotation.TargetApi;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;
import android.os.LocaleList;
import android.preference.PreferenceManager;

import java.util.Locale;

/**
 * Created by gigin.ginanjar on 29/03/2016.
 */
public class LocaleHelper {
    public static final String BAHASA_INDONESIA = "in";
    public static final String ENGLSIH = "en";
    private static final String SELECTED_LANGUAGE = "Locale.Helper.Selected.Language";

    public static void onCreate(Context context) {
        String lang = getPersistedData(context, Locale.getDefault().getLanguage());
        setLocale(context, lang);
    }

    public static void onCreate(Context context, String defaultLanguage) {
        String lang = getPersistedData(context, defaultLanguage);
        setLocale(context, lang);
    }

    public static String getLanguage(Context context) {
        return getPersistedData(context, ENGLSIH);
    }

    public static void setLocale(Context context, String language) {
        persist(context, language);
        updateResources(context, language);
    }

    /*public static void updateTimelineType(Context context) {

        List<TimelineType> timelineTypes = TimelineTypeDataAccess.getAll(context);

        Global.TIMELINE_TYPE_TASK= context.getString(R.string.timeline_type_task);
        Global.TIMELINE_TYPE_SUBMITTED= context.getString(R.string.timeline_type_submitted);
        Global.TIMELINE_TYPE_VERIFIED= context.getString(R.string.timeline_type_verified);
        Global.TIMELINE_TYPE_REJECTED= context.getString(R.string.timeline_type_rejected);
        Global.TIMELINE_TYPE_APPROVED= context.getString(R.string.timeline_type_approved);
        Global.TIMELINE_TYPE_VERIFICATION= context.getString(R.string.timeline_type_verification);
        Global.TIMELINE_TYPE_APPROVAL= context.getString(R.string.timeline_type_approval);
        Global.TIMELINE_TYPE_CHECKIN = context.getString(R.string.timeline_type_checkin);
        Global.TIMELINE_TYPE_CHECKOUT = context.getString(R.string.timeline_type_checkout);
        Global.TIMELINE_TYPE_MESSAGE = context.getString(R.string.timeline_type_message);

        if(timelineTypes!=null) {
            for (TimelineType type : timelineTypes) {
                if(type.getTimeline_description().equals(TimelineManager.DESC_TM_APPROVAL)){
                    type.setTimeline_type(Global.TIMELINE_TYPE_APPROVAL);
                }else if(type.getTimeline_description().equals(TimelineManager.DESC_TM_APPROVED)){
                    type.setTimeline_type(Global.TIMELINE_TYPE_APPROVED);
                }else if(type.getTimeline_description().equals(TimelineManager.DESC_TM_CHECKIN)){
                    type.setTimeline_type(Global.TIMELINE_TYPE_CHECKIN);
                }else if(type.getTimeline_description().equals(TimelineManager.DESC_TM_MESSAGE)){
                    type.setTimeline_type(Global.TIMELINE_TYPE_MESSAGE);
                }else if(type.getTimeline_description().equals(TimelineManager.DESC_TM_PRIORITY)){
                    type.setTimeline_type(Global.TIMELINE_TYPE_TASK);
                }else if(type.getTimeline_description().equals(TimelineManager.DESC_TM_REJECT)){
                    type.setTimeline_type(Global.TIMELINE_TYPE_REJECTED);
                }else if(type.getTimeline_description().equals(TimelineManager.DESC_TM_SUBMIT)){
                    type.setTimeline_type(Global.TIMELINE_TYPE_SUBMITTED);
                }else if(type.getTimeline_description().equals(TimelineManager.DESC_TM_VERIFICATION)){
                    type.setTimeline_type(Global.TIMELINE_TYPE_VERIFICATION);
                }else if(type.getTimeline_description().equals(TimelineManager.DESC_TM_VERIFIED)){
                    type.setTimeline_type(Global.TIMELINE_TYPE_VERIFIED);
                }
            }
            TimelineTypeDataAccess.updateAll(context, timelineTypes);
        }
    }*/

    private static String getPersistedData(Context context, String defaultLanguage) {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
        return preferences.getString(SELECTED_LANGUAGE, defaultLanguage);
    }

    private static void persist(Context context, String language) {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
        SharedPreferences.Editor editor = preferences.edit();

        editor.putString(SELECTED_LANGUAGE, language);
        editor.apply();
    }

    private static void updateResources(Context context, String language) {
        Locale locale = new Locale(language);
        Locale.setDefault(locale);

        Resources resources = context.getResources();

        Configuration configuration = resources.getConfiguration();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            configuration.setLocale(locale);
        } else {
            configuration.locale = locale;
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            configuration.setLocale(locale);
            LocaleList localeList = new LocaleList(locale);
            LocaleList.setDefault(localeList);
            configuration.setLocales(localeList);
            context.createConfigurationContext(configuration);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            configuration.setLocale(locale);
            context.createConfigurationContext(configuration);
        } else {
            resources.updateConfiguration(configuration, resources.getDisplayMetrics());
        }
    }

    @TargetApi(Build.VERSION_CODES.N)
    public static ContextWrapper wrap(Context context, Locale newLocale) {
        Resources res = context.getResources();
        Configuration configuration = res.getConfiguration();

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            configuration.setLocale(newLocale);
            LocaleList localeList = new LocaleList(newLocale);
            LocaleList.setDefault(localeList);
            configuration.setLocales(localeList);
            context = context.createConfigurationContext(configuration);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            configuration.setLocale(newLocale);
            context = context.createConfigurationContext(configuration);
        } else {
            configuration.locale = newLocale;
            res.updateConfiguration(configuration, res.getDisplayMetrics());
        }

        return new ContextWrapper(context);
    }
}
